﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 调班申请表
        /// </summary>
        public DbSet<AdjustScheduleRecordInfo> AdjustScheduleRecordInfos { get; set; }
        /// <summary>
        /// 排班预约申请表
        /// </summary>
        public DbSet<SchedulingRequestRecordInfo> SchedulingRequestRecordInfos { get; set; }
        /// <summary>
        /// 排班主表
        /// </summary>
        public DbSet<ShiftSchedulingRecordInfo> ShiftSchedulingRecordInfos { get; set; }
        /// <summary>
        /// 排班明细表
        /// </summary>
        public DbSet<ShiftSchedulingDetailInfo> ShiftSchedulingDetailInfos { get; set; }
        /// <summary>
        /// 排班明细标记表
        /// </summary>
        public DbSet<ShiftSchedulingDetailMarkInfo> ShiftSchedulingDetailMarkInfos { get; set; }
        /// <summary>
        /// 排班人员顺序表
        /// </summary>
        public DbSet<ShiftSchedulingEmployeeSortInfo> ShiftSchedulingEmployeeSortInfos { get; set; }

        /// <summary>
        /// 考勤表
        /// </summary>
        public DbSet<AttendanceRecordInfo> AttendanceRecordInfos { get; set; }
        /// <summary>
        /// 考勤明细表
        /// </summary>
        public DbSet<AttendanceDetailInfo> AttendanceDetailInfos { get; set; }
        /// <summary>
        /// 考勤明细统计表
        /// </summary>
        public DbSet<AttendanceDetailStatisticsInfo> AttendanceDetailStatisticsInfos { get; set; }

        /// <summary>
        /// 临时出勤记录表
        /// </summary>
        public DbSet<TemporaryAttendanceRecordInfo> TemporaryAttendanceRecordInfos { get; set; }
        /// <summary>
        /// 剩余休假天数表
        /// </summary>
        public DbSet<RemainingRestDaysInfo> RemainingRestDaysInfos { get; set; }
        /// <summary>
        /// 剩余休假天数日志表
        /// </summary>
        public DbSet<RemainingRestDaysLogInfo> RemainingRestDaysLogInfos { get; set; }
        /// <summary>
        /// 部门考勤状态表
        /// </summary>
        public DbSet<AttendanceApproveRecordInfo> AttendanceApproveRecordInfos { get; set; }
        /// <summary>
        /// 排班规则记录表
        /// </summary>
        public DbSet<ShiftSchedulingRuleInfo> ShiftSchedulingRuleInfos { get; set; }
        /// <summary>
        /// 排班模板主表
        /// </summary>
        public DbSet<SchedulingTemplateRecordInfo> SchedulingTemplateRecordInfos { get; set; }
        /// <summary>
        /// 排班模板明细表
        /// </summary>
        public DbSet<SchedulingTemplateDetailInfo> SchedulingTemplateDetailInfos { get; set; }
        /// <summary>
        /// 排班模板明细标记表
        /// </summary>
        public DbSet<SchedulingTemplateDetailMarkInfo> SchedulingTemplateDetailMarkInfos { get; set; }
    }
}
