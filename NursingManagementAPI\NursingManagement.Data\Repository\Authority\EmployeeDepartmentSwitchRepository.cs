﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;

namespace NursingManagement.Data.Repository
{
    public class EmployeeDepartmentSwitchRepository : IEmployeeDepartmentSwitchRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public EmployeeDepartmentSwitchRepository(
            NursingManagementDbContext dbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService
            )
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var data = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _dbContext.EmployeeDepartmentSwitchInfos.
                    Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return data;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeDepartmentSwitch.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 根据工号获取拥有的部门权限
        /// </summary>
        /// <param name="employeeID">工号:为空，获取全部的数据</param>
        /// <param name="cacheFlag">是否从缓存中获取</param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentSwitchInfo>> GetDepartmentSwitchByEmployeeIDAsync(string employeeID, bool cacheFlag)
        {
            if (cacheFlag)
            {
                var cacheList = await GetCacheAsync() as List<EmployeeDepartmentSwitchInfo>;
                if (string.IsNullOrEmpty(employeeID))
                {
                    return cacheList;
                }
                return cacheList.Where(m => m.EmployeeID == employeeID).ToList();
            }
            string key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _dbContext.EmployeeDepartmentSwitchInfos.Where(m => m.HospitalID == hospitalID && 
                string.IsNullOrEmpty(employeeID) ? true : m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据工号和组织类型获取拥有的部门权限
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="organizationType">组织类别</param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentSwitchInfo>> GetDepartmentListByEmployeeID(string employeeID, string organizationType)
        {
            var cacheList = await GetCacheAsync() as List<EmployeeDepartmentSwitchInfo>;
            return cacheList.Where(m => m.EmployeeID == employeeID && m.OrganizationType == organizationType).ToList();
        }
        /// <summary>
        /// 获取拥有权限的科室ID集合
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        public async Task<List<int>> GetSwitchDepartmentIDsAsync(string employeeID)
        {
            var cacheList = await GetCacheAsync() as List<EmployeeDepartmentSwitchInfo>;

            return cacheList.Where(m => m.EmployeeID == employeeID).
                Select(m => m.DepartmentID).ToList();
        }
    }
}
