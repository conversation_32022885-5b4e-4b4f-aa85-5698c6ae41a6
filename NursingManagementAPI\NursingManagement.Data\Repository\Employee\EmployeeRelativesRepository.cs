﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository
{
    public class EmployeeRelativesRepository : IEmployeeRelativesRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        /// <summary>
        /// GB/T 4761 家庭关系
        /// </summary>
        private const string RELATIONSHIPCODE_SELF = "0";
        public EmployeeRelativesRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeRelativesInfo>> GetListByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeRelativesInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取本人关联信息（手机号、住址、学校等）
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<T> GetHerSelfFieldValueByEmployeeIDAsync<T>(string employeeID, [DisallowNull] Expression<Func<EmployeeRelativesInfo, T>> predicate)
        {
            return await _dbContext.EmployeeRelativesInfos
                .Where(m => m.EmployeeID == employeeID && m.RelationshipCode == RELATIONSHIPCODE_SELF && m.DeleteFlag != "*")
                .Select(predicate).FirstOrDefaultAsync();

        }
    }
}