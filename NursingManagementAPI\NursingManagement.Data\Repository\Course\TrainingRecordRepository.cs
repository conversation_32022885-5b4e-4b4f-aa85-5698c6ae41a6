﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class TrainingRecordRepository : ITrainingRecordRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        public TrainingRecordRepository(
            NursingManagementDbContext nursingManagementDbContext
            )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }
        /// <summary>
        /// 根据部门ID和医院序号获取数据
        /// </summary>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        public async Task<List<TrainingRecordInfo>> GetListByDepartMentID(List<int> departmentIDs, string hospitalID)
        {
            if (departmentIDs == null)
            {
                return await _nursingManagementDbContext.TrainingRecordInfos.Where(m =>  m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
            }
            return await _nursingManagementDbContext.TrainingRecordInfos.Where(m =>  departmentIDs.Any(n => n == m.DepartmentID) && m.HospitalID == hospitalID && m.DeleteFlag != "*").OrderByDescending(m=>m.AddDateTime).ToListAsync();
        }
        /// <summary>
        /// 根据主键ID获取数据
        /// </summary>
        /// <param name="trainingRecordID">培训记录ID</param>
        /// <returns></returns>
        public async Task<TrainingRecordInfo> GetDataByID(string trainingRecordID)
        {
            return await _nursingManagementDbContext.TrainingRecordInfos.FirstOrDefaultAsync(m => m.TrainingRecordID == trainingRecordID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据员工编号和医院序号获取培训讲师和培训主持人的数据
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        public async Task<List<string>> GetTrainingRecordIDsByEmployeeID(string employeeID, string hospitalID)
        {
            return await _nursingManagementDbContext.TrainingRecordInfos.Where(m => (m.TrainingLecturer == employeeID || m.TrainingHost == employeeID) && m.HospitalID == hospitalID && m.DeleteFlag != "*").Select(m => m.TrainingRecordID).ToListAsync();
        }
    }
}
