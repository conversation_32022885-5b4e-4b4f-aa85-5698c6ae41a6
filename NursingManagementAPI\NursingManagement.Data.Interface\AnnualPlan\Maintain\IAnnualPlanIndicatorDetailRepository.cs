﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划指标明细
    /// </summary>
    public interface IAnnualPlanIndicatorDetailRepository : IBaseAPDetailRepository
    {
        /// <summary>
        /// 根据主键获取指标明细
        /// </summary>
        /// <param name="indicatorDetailID">主键ID</param>
        /// <returns></returns>
        Task<AnnualPlanIndicatorDetailInfo> GetDetailByID(string indicatorDetailID);
        /// <summary>
        /// 根据主表ID获取指标明细集合，不跟踪
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanIndicatorDetailInfo>> GetInfosByPlanMainIDAsNoTracking(string annualPlanMainID);
        /// <summary>
        /// 获取简略的指标明细集合
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <param name="mainGoalIDs">目标ID集合</param>
        /// <returns></returns>
        Task<APIndicatorDetail[]> GetIndicatorDetailsByPlanMainID(string planMainID, string[] mainGoalIDs = null);
        /// <summary>
        /// 获取已分组的指标ID
        /// </summary>
        /// <param name="mainID">年度计划业务主表ID</param>
        /// <returns></returns>
        Task<int[]> GetRefIndicatorIDs(string mainID);
        /// <summary>
        /// 获取分组对指标明细集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">计划分组ID</param>
        /// <returns></returns>
        Task<List<APIndicatorDetail>> GetViewsByGroupID(string mainID, string groupID);
        /// <summary>
        /// 获取当前分组的明细实体
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanIndicatorDetailInfo>> GetIndicatorInfosByGroupID(string groupID);
        /// <summary>
        /// 获取分组中序号之后的明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="sort">序号</param>
        /// <returns></returns>
        Task<List<AnnualPlanIndicatorDetailInfo>> GetAfterSortDetail(string mainID, int sort);
        /// <summary>
        /// 根据主表ID获取明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalIDs">目标业务IDs</param>
        /// <returns></returns>
        Task<List<APIndicatorDetail>> GetViewsByMainIDAndMainGoalIDs(string mainID, string[] mainGoalIDs = null);
        /// <summary>
        /// 根据主表ID获取指标明细集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanIndicatorDetailInfo>> GetInfosByMainID(string annualPlanMainID);
        /// <summary>
        /// 分组序号
        /// </summary>
        /// <param name="groupID">分组序号</param>
        /// <param name="groupSort">当前分组序号</param>
        /// <returns></returns>
        Task<int> GetMaxSortByGroupID(string groupID, int groupSort);
        /// <summary>
        /// 根据主表ID获取指标明细集合
        /// </summary>
        /// <param name="mainGoalID">目标表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanIndicatorDetailInfo>> GetInfosByMainGoalID(string mainGoalID);
    }
}
