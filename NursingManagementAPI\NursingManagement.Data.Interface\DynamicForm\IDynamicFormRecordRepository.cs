﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDynamicFormRecordRepository : ICacheRepository
    {
        /// <summary>
        /// 根据表单ID获取表单
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns></returns>
        Task<DynamicFormRecordInfo> GetFormByFormRecordID(string dynamicFormRecordID);
        /// <summary>
        /// 根据动态表单ID获取数据（列表）
        /// </summary>
        /// <param name="dynamicFormRecordIDs">动态表单ID集合</param>
        /// <returns></returns>
        Task<List<DynamicFormRecordInfo>> GetFormByFormRecordIDs(List<string> dynamicFormRecordIDs);
    }
}
