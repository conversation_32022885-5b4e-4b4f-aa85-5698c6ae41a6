﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models.Examine
{
    /// <summary>
    /// 考核明细表
    /// </summary>
    [Table("ExaminationDetail")]
    public class ExaminationDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考核明细表ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar32")]
        public string ExaminationDetailID { get; set; }
        /// <summary>
        /// 考核主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationMainID { get; set; }
        /// <summary>
        /// 题目明细ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationQuestionDetailID { get; set; }
        /// <summary>
        /// 题目ID
        /// </summary>
        public int ExaminationQuestionID { get; set; }
        /// <summary>
        /// 作答内容(简答题)
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string Value { get; set; }
        /// <summary>
        /// 选择答案状态(未作答，正确，错误)(配置在SettingDictionary)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "nvarchar(500)")]
        public string Remark { get; set; }
        
    }
}
