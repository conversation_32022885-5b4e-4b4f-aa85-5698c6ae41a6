﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AuthorityRoleListRepository : IAuthorityRoleListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public AuthorityRoleListRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AuthorityRoleListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AuthorityRoleList.GetKey(_sessionCommonServer);
        }

        public async Task<List<int>> GetAuthorityListByRoleID(List<int> roleIDs)
        {
            var authorityRoleList = await GetCacheAsync() as List<AuthorityRoleListInfo>;
            if (authorityRoleList.Count <= 0)
            {
                return new List<int>();
            }
            return authorityRoleList.Where(m => roleIDs.Contains(m.AuthorityRoleID)).Select(m => m.AuthorityID).ToList();
        }
    }
}
