﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IMenuListRepository : ICacheRepository
    {

        /// <summary>
        /// 获取所有菜单
        /// </summary>
        /// <returns></returns>
        Task<List<MenuListInfo>> GetAllMenuList();
        /// <summary>
        /// 根据菜单ID获取菜单路由ID
        /// </summary>
        /// <param name="menuListID"></param>
        /// <returns></returns>
        Task<int?> GetRouterListIDByID(int menuListID);
    }
}
