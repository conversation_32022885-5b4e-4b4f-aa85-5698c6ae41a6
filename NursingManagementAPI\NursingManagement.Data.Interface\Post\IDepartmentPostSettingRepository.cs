﻿
using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDepartmentPostSettingRepository : ICacheRepository
    {
        /// <summary>
        /// 从缓存中获取没有被删除的科室岗位设定
        /// </summary>
        /// <returns></returns>
        Task<List<DepartmentPostSettingInfo>> GetAllCacheAsync();
        /// <summary>
        /// 从数据库获取数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="type">类型</param>
        /// <returns></returns>
        Task<DepartmentPostSettingInfo> GetRecordByKeysAsync(int postID,int departmentID,string type);
        /// <summary>
        /// 获取数据，包含已删除的
        /// </summary>
        /// <param name="postID"></param>
        /// <param name="departmentID"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<DepartmentPostSettingInfo> GetRecordIncludeDelete(int postID, int departmentID, string type);
    }
}
