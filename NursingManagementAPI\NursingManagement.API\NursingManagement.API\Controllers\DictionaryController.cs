﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 字典控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/dictionary")]
    [EnableCors("any")]
    public class DictionaryController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IDictionaryService _dictionaryService;

        /// <summary>
        /// 字典
        /// </summary>
        public DictionaryController(
            ISessionService session
            , IDictionaryService dictionaryService
        )
        {
            _session = session;
            _dictionaryService = dictionaryService;
        }

        /// <summary>
        /// 获取医院列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetHospitalList")]
        public async Task<IActionResult> GetHospitalList()
        {
            var result = new ResponseResult();

            result.Data = await _dictionaryService.GetHospitalList();
            return result.ToJson();
        }
        /// <summary>
        /// 获取医院列表
        /// </summary>
        /// <param name="hospitalID">医院类别</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetHospitalByHospitalID")]
        public async Task<IActionResult> GetHospitalByHospitalID(string hospitalID)
        {
            var result = new ResponseResult();

            result.Data = await _dictionaryService.GetHospitalByHospitalIDAsync(hospitalID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取人事部门字典
        /// </summary>
        [HttpGet]
        [Route("GetHrmDepartmentDict")]
        public async Task<IActionResult> GetHrmDepartmentDict()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetHrmDepartmentDict();
            return result.ToJson();
        }

        /// <summary>
        /// 获取国标字典
        /// </summary>
        /// <param name="administrationParams"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAdministrationDict")]
        public async Task<IActionResult> GetAdministrationDict([FromQuery] AdministrationParams administrationParams)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetAdministrationDict(administrationParams);
            return result.ToJson();
        }

        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <param name="postTypeID"></param>
        /// <returns ></returns>
        [HttpGet]
        [Route("GetPostDict")]
        public async Task<IActionResult> GetPostDict(string postTypeID = null)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetPostDict(postTypeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取部门岗位数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="showAll"></param>
        /// <param name="postTypeID"></param>
        /// <param name="currentDate">传入日期，取时间对应的岗位工作时间</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentPostDict")]
        public async Task<IActionResult> GetDepartmentPostDict(int departmentID, bool showAll, string postTypeID = null, DateTime? currentDate = null)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if (departmentID == 0)
            {
                departmentID = session.DepartmentID;
            }
            result.Data = await _dictionaryService.GetDepartmentPostDict(departmentID, showAll, postTypeID, currentDate);
            return result.ToJson();
        }

        /// <summary>
        /// 获取能级数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCapabilityLevelDict")]
        public async Task<IActionResult> GetCapabilityLevelDict(string type)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetCapabilityLevelDict(type);
            return result.ToJson();
        }

        /// <summary>
        /// 获取人员数据
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="showAll">是否获取全部人员</param>
        /// <returns ></returns>
        [HttpGet]
        [Route("GetEmployeeDict")]
        public async Task<IActionResult> GetEmployeeDict(int? departmentID, bool showAll)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if (departmentID == 0)
            {
                departmentID = session.DepartmentID;
            }
            result.Data = await _dictionaryService.GetEmployeeDict(departmentID, showAll);
            return result.ToJson();
        }
        /// <summary>
        /// 获取部门级联选择器数据
        /// </summary>
        /// <param name="queryView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentCascaderList")]
        public async Task<IActionResult> GetDepartmentCascaderList([FromQuery] DepartmentCascaderQueryView queryView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetDepartmentCascaderList(queryView.OrganizationType, ListToJson.ToList<int[]>(queryView.DisableDepartmentIDs));
            return result.ToJson();
        }
        /// <summary>
        /// 获取注记图示
        /// </summary>
        /// <param name="moduleType">标识类型</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetIconsByModuleType")]
        public async Task<IActionResult> GetIconsByModuleType(string moduleType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetIconsByModuleType(moduleType);
            return result.ToJson();
        }
        /// <summary>
        /// 获取职务级联选择器数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentToJobs")]
        public async Task<IActionResult> GetDepartmentToJobs(int? departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetDepartmentToJobs(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 模糊查询人员信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeDataByName")]
        public async Task<IActionResult> GetEmployeeDataByName(string employeeName)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetEmployeeDataByName(employeeName);
            return result.ToJson();
        }
        /// <summary>
        /// 查询人员信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeDataByIDs")]
        public async Task<IActionResult> GetEmployeeDataByIDs(string employeeIDs)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetEmployeeDataByIDs(ListToJson.ToList<string[]>(employeeIDs));

            return result.ToJson();
        }
        /// <summary>
        /// 获取部门字典
        /// </summary>
        [HttpGet]
        [Route("GetDepartmentName")]
        public async Task<IActionResult> GetDepartmentName(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetDepartmentName(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取所有部门
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentViewsByOrganizationType")]
        public async Task<IActionResult> GetDepartmentViewsByOrganizationType(string organizationType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetDepartmentViewsByOrganizationType(organizationType);
            return result.ToJson();
        }
        /// <summary>
        /// 获取角色权限清单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAuthorityRoles")]
        public async Task<IActionResult> GetAuthorityRoles()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetAuthorityRoles();
            return result.ToJson();
        }
        /// <summary>
        /// 根据工号获取所拥有权限的科室
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="organizationType">组织类型</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeDepartment")]
        public async Task<IActionResult> GetEmployeeDepartmentAsync(string employeeID, string organizationType = "1")
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetEmployeeDepartmentAsync(employeeID, organizationType);
            return result.ToJson();
        }
        /// <summary>
        /// 根据部门ID 获取部门岗位
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPostDictByDepartmentID")]
        public async Task<IActionResult> GetPostDictByDepartmentID(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if (departmentID == 0)
            {
                departmentID = session.DepartmentID;
            }
            result.Data = await _dictionaryService.GetPostDictByDepartmentID(departmentID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取AppConfigSetting配置
        /// </summary>
        /// <param name="settingType"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAppConfigsAsync")]
        public async Task<IActionResult> GetAppConfigsAsync(string settingType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetAppConfigsAsync(settingType);
            return result.ToJson();
        }
        /// <summary>
        /// 根据组件类型获取组件数据
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetComponentListByType")]
        public async Task<IActionResult> GetComponentListByType(string componentType)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetComponentListByType(componentType);
            return result.ToJson();
        }

        /// <summary>
        /// 获取上级片区选项
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUpperDeptOptions")]
        public async Task<IActionResult> GetUpperDeptOptions()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetUpperDeptOptions();
            return result.ToJson();
        }
        /// <summary>
        /// 获取上级片区选项
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetVisitsDeptOptions")]
        public async Task<IActionResult> GetVisitsDeptOptions(string employeeID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dictionaryService.GetVisitsDeptOptionsAsync(employeeID);
            return result.ToJson();
        }
    }
}
