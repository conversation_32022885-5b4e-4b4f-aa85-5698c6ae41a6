﻿
using Arch.EntityFrameworkCore.UnitOfWork;
using Newtonsoft.Json;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Data;


namespace NursingManagement.Services
{
    public partial class DynamicFormService : IDynamicFormService
    {
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IComponentListRepository _componentListRepository;
        private readonly IComponentAttributeRepository _componentAttributeRepository;
        private readonly IDynamicFormRecordRepository _dynamicFormRecordRepository;
        private readonly IDynamicFormDetailRepository _dynamicFormDetailRepository;
        private readonly IDynamicFormDetailAttributeRepository _dynamicFormDetailAttributeRepository;
        private readonly IDynamicFormDetailConditionRepository _dynamicFormDetailConditionRepository;
        private readonly FormExtendFactory _formExtendFactory;
        private readonly NursingManagementDbContext _dbContext = null;

        #region 常量定义
        /// <summary>
        /// 标签组件ID
        /// </summary>
        private const int COMPONENT_LIST_ID_LABEL = 100;
        /// 单选组件ID
        /// </summary>
        private const int COMPONENT_LIST_ID_RADIO = 103;
        /// <summary>
        /// 考评分数组件ID
        /// </summary>
        private const int COMPONENT_LIST_ID_GRADE = 104;
        /// <summary>
        /// 分组组件ID
        /// </summary>
        private const int COMPONENT_LIST_ID_GROUP = 109;
        /// <summary>
        /// 标签页组件ID
        /// </summary>
        private const int COMPONENT_LIST_ID_TABS = 113;
        /// <summary>
        /// 包含options属性的组件ID
        /// </summary>
        private readonly List<int> HAS_OPTIONS_COMPONENT_LIST_IDS = [102, 103];
        /// <summary>
        /// 业务组件 人员和岗位
        /// </summary>
        private readonly List<int> APPLICATION_OPTIONS_COMPONENT_LIST_IDS = [105, 106];
        /// <summary>
        /// 布局组件
        /// </summary>
        private readonly List<int> LAYOUT_COMPONENT_LIST_IDS = [COMPONENT_LIST_ID_GROUP, COMPONENT_LIST_ID_TABS];
        /// <summary>
        /// 组件的Label属性ID
        /// </summary>
        private const int COMPONENT_ATTRIBUTE_ID_LABEL = 3;
        /// <summary>
        /// 组件的Label属性ID
        /// </summary>
        private const int COMPONENT_ATTRIBUTE_ID_INPUT_ENABLE = 45;
        /// <summary>
        /// 组件的defaultValue属性
        /// </summary>
        private const string COMPONENT_ATTRIBUTE_DEFAULT = "defaultValue";
        /// <summary>
        /// 组件的options属性
        /// </summary>
        private const string COMPONENT_ATTRIBUTE_OPTIONS = "options";
        /// <summary>
        /// 组件的pages属性
        /// </summary>
        private const string COMPONENT_ATTRIBUTE_PAGES = "pages";
        /// <summary>
        /// 组件所属tabs组件的pageID属性
        /// </summary>
        private const string COMPONENT_ATTRIBUTE_PAGEID = "pageID";
        /// <summary>
        /// 组件选项属性标记对应属性ID
        /// </summary>
        private static readonly Dictionary<string, int> COMPONENT_OPTIONS_ATTRIBUTE_TO_ID = new() { { "options", 70 }, { "pages", 71 } };
        /// <summary>
        /// 转换json的组件属性类型
        /// </summary>
        private static readonly List<string> CONVERT_JSON_COMPONENT_ATTRIBUTE_TYPES = ["list", "object", "json"];
        #endregion

        public DynamicFormService(
            IUnitOfWork unitOfWork
            , SessionCommonServer sessionCommonServer
            , IComponentListRepository componentListRepository
            , IComponentAttributeRepository componentAttributeRepository
            , IDynamicFormRecordRepository dynamicFormRecordRepository
            , IDynamicFormDetailRepository dynamicFormDetailRepository
            , IDynamicFormDetailAttributeRepository dynamicFormDetailAttributeRepository
            , IDynamicFormDetailConditionRepository dynamicFormDetailConditionRepository
            , FormExtendFactory formExtendFactory
            , NursingManagementDbContext nursingManagementDbContext
        )
        {
            _unitOfWork = unitOfWork;
            _sessionCommonServer = sessionCommonServer;
            _componentListRepository = componentListRepository;
            _componentAttributeRepository = componentAttributeRepository;
            _dynamicFormRecordRepository = dynamicFormRecordRepository;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
            _dynamicFormDetailAttributeRepository = dynamicFormDetailAttributeRepository;
            _dynamicFormDetailConditionRepository = dynamicFormDetailConditionRepository;
            _formExtendFactory = formExtendFactory;
            _dbContext = nursingManagementDbContext;
        }

        #region 查询动态表单模板    
        /// <summary>
        /// 获取动态表单模板
        /// </summary>
        /// <param name="dynamicFormRecordID">动态表单主记录ID</param>
        /// <param name="datas">初始数据</param>
        /// <param name="itemDisabled">禁用项</param>
        /// <param name="itemSourceType">来源</param>
        /// <param name="computeGroupScore">是否计算分组总分</param>
        /// <returns></returns>
        public async Task<FormTemplateView> GetFormTemplateByRecordID(string dynamicFormRecordID, List<FormValueView> datas, Dictionary<string, string> itemDisabled, string itemSourceType, bool computeGroupScore = true)
        {
            var formTemplateView = new FormTemplateView();
            var dynamicFormRecord = await _dynamicFormRecordRepository.GetFormByFormRecordID(dynamicFormRecordID);
            if (dynamicFormRecord == null)
            {
                return formTemplateView;
            }
            formTemplateView.Props = new FormRecordView()
            {
                FormID = dynamicFormRecordID,
                FormName = dynamicFormRecord.FormName,
                FormType = dynamicFormRecord.FormType,
                Size = dynamicFormRecord.Size,
                Column = dynamicFormRecord.Column,
                LabelWidth = dynamicFormRecord.LabelWidth,
                LabelPosition = dynamicFormRecord.LabelPosition
            };
            var formDetails = await _dynamicFormDetailRepository.GetFormDetailListByFormRecordID(dynamicFormRecordID);
            if (formDetails == null || formDetails.Count <= 0)
            {
                return formTemplateView;
            }
            var dynamicFormDetailIDs = formDetails.Select(m => m.DynamicFormDetailID).ToList();
            var getComponentProps = await CreateGetPropsFunc(formDetails, itemDisabled, dynamicFormDetailIDs, itemSourceType, dynamicFormRecordID);

            (formTemplateView.Components, formTemplateView.Datas) = await GetComponentsAndData(dynamicFormRecord.FormType, formDetails, datas, getComponentProps, computeGroupScore);
            return formTemplateView;
        }

        /// <summary>
        /// 获取属性方法
        /// </summary>
        /// <param name="sources">动态表单明细集合</param>
        /// <param name="itemDisabled">禁用项</param>
        /// <param name="dynamicFormDetailIDs">动态表单明细ID集合</param>
        /// <param name="itemSourceType">来源</param>
        /// <returns></returns>
        private async Task<Func<List<DynamicFormDetailInfo>, DynamicFormDetailInfo, List<ComponentAttributeInfo>
            , Dictionary<string, object>>> CreateGetPropsFunc(List<DynamicFormDetailInfo> sources
            , Dictionary<string, string> itemDisabled, List<string> dynamicFormDetailIDs, string itemSourceType, string dynamicFormRecordID)
        {
            var gradeRemarks = new List<KeyValueString>();
            // 如果来源不为空，依据来源从工厂里获取评分组件的备注选项集合
            if (!string.IsNullOrWhiteSpace(itemSourceType))
            {
                var formExtendFactory = _formExtendFactory.SwitchCondition(itemSourceType);
                if (formExtendFactory != null)
                {
                    gradeRemarks = await formExtendFactory.GetGradeRemarkOptions(sources.Select(m => m.ItemID).ToList());
                }
            }
            var detailAttributeList = await _dynamicFormDetailAttributeRepository.GetDetailAttributeListByRecordID(dynamicFormRecordID);
            var detailConditionList = (await _dynamicFormDetailConditionRepository.GetAll<DynamicFormDetailConditionInfo>())
                                        .Where(m => dynamicFormDetailIDs.Contains(m.DynamicFormDetailID)).ToList();
            return (List<DynamicFormDetailInfo> formDetailList, DynamicFormDetailInfo formDetail, List<ComponentAttributeInfo> componentAttributes) =>
            {
                var props = new Dictionary<string, object>();
                var currentAttrList = detailAttributeList.Where(m => m.DynamicFormDetailID == formDetail.DynamicFormDetailID).ToList();
                if (currentAttrList.Count <= 0)
                {
                    return props;
                }
                var itemDisabledExpression = itemDisabled?.Count > 0 && itemDisabled.TryGetValue(formDetail.ItemID, out string value) ? value : "";
                foreach (var detailAttribute in currentAttrList)
                {
                    var componentAttribute = componentAttributes.Find(m => m.ComponentAttributeID == detailAttribute.ComponentAttributeID);
                    if (componentAttribute == null)
                    {
                        continue;
                    }
                    object attributeValue;
                    if (CONVERT_JSON_COMPONENT_ATTRIBUTE_TYPES.Contains(componentAttribute.DataType))
                    {
                        attributeValue = ListToJson.ToList<object>(detailAttribute.AttributeValue);
                    }
                    else
                    {
                        // 根据组件属性的数据类型将组件属性值进行类型转换
                        var dataType = ObjectUtil.GetTypeByString(componentAttribute.DataType);
                        attributeValue = Convert.ChangeType(detailAttribute.AttributeValue, dataType);
                    }
                    props.Add(componentAttribute.Attribute, attributeValue);
                    if (!string.IsNullOrWhiteSpace(itemDisabledExpression))
                    {
                        itemDisabledExpression = itemDisabledExpression.Replace($"{{{componentAttribute.Attribute}}}", attributeValue.ToString());
                    }
                }
                // 动态添加禁用属性
                props = AddDisabledProp(props, itemDisabledExpression);
                // 添加备注选项属性
                if (formDetail.ComponentListID == COMPONENT_LIST_ID_GRADE)
                {
                    var itemRemarkOptions = gradeRemarks.Where(m => m.ID.ToString() == formDetail.ItemID).ToList();
                    props.Add("remarkOptions", itemRemarkOptions);
                }
                // 单选、多选、人员、岗位添加options属性
                if (HAS_OPTIONS_COMPONENT_LIST_IDS.Contains(formDetail.ComponentListID) || APPLICATION_OPTIONS_COMPONENT_LIST_IDS.Contains(formDetail.ComponentListID))
                {
                    props = AddComponentOptionsProp(props, formDetailList, formDetail.ItemID, detailAttributeList, COMPONENT_ATTRIBUTE_OPTIONS);
                }
                // 标签页添加pages属性
                if (COMPONENT_LIST_ID_TABS == formDetail.ComponentListID)
                {
                    props = AddComponentOptionsProp(props, formDetailList, formDetail.ItemID, detailAttributeList, COMPONENT_ATTRIBUTE_PAGES);
                }
                // 添加组件的动态关联条件属性
                props = AddConditionProp(props, detailConditionList, componentAttributes, formDetail.DynamicFormDetailID);
                return props;
            };
        }

        /// <summary>
        /// 获取组件模板和值
        /// </summary>
        /// <param name="formType">表单类型</param>
        /// <param name="formDetails">动态表单模板明细数据集合</param>
        /// <param name="datas">预设数据</param>
        /// <param name="getComponentProps">获取组件属性的函数</param>
        /// <param name="computeGroupScore">计算分组总分</param>
        /// <returns></returns>
        private async Task<(List<FormComponentView>, Dictionary<string, object>)> GetComponentsAndData(string formType, List<DynamicFormDetailInfo> formDetails, List<FormValueView> datas,
            Func<List<DynamicFormDetailInfo>, DynamicFormDetailInfo, List<ComponentAttributeInfo>, Dictionary<string, object>> getComponentProps = null, bool computeGroupScore = true)
        {
            var parentDetails = formDetails.Where(m => string.IsNullOrWhiteSpace(m.ParentID)).OrderBy(m => m.Sort).ToList();
            var componentAttributeList = await _componentAttributeRepository.GetAttributeList();
            var componentList = await _componentListRepository.GetComponentListByType("DynamicForm");
            var componentTypes = componentList.ToDictionary(m => m.ComponentListID, m => m.ControlerType);

            var (components, formData) = ConvertComponentsAndDatas(formType, componentAttributeList, formDetails, parentDetails, [], componentTypes, datas, getComponentProps, computeGroupScore);
            return (components, formData);
        }
        /// <summary>
        /// 转换组件和值
        /// </summary>
        /// <param name="formType">表单类型</param>
        /// <param name="componentAttributeList">组件属性字典</param>
        /// <param name="sources">动态表单明细集合</param>
        /// <param name="parentSources">父级动态表单明细集合</param>
        /// <param name="formData"></param>
        /// <param name="componentTypes">组件类型集合</param>
        /// <param name="datas"></param>
        /// <param name="getComponentProps">获取组件属性函数</param>
        /// <param name="computeGroupScore">是否计算分组总分</param>
        /// <returns></returns>
        private (List<FormComponentView>, Dictionary<string, object>) ConvertComponentsAndDatas(string formType, List<ComponentAttributeInfo> componentAttributeList,
            List<DynamicFormDetailInfo> formDetails, List<DynamicFormDetailInfo> parentDetails, Dictionary<string, object> formData, Dictionary<int, string> componentTypes, List<FormValueView> datas,
            Func<List<DynamicFormDetailInfo>, DynamicFormDetailInfo, List<ComponentAttributeInfo>, Dictionary<string, object>> getComponentProps, bool computeGroupScore)
        {
            var components = new List<FormComponentView>();
            foreach (var parentSource in parentDetails)
            {
                if (!componentTypes.ContainsKey(parentSource.ComponentListID))
                {
                    continue;
                }
                var props = getComponentProps(formDetails, parentSource, componentAttributeList);
                // 如果时tabs组件的页签，跳过
                if (props.TryGetValue("isPage", out var isPage) && isPage.ToString() == "True")
                {
                    continue;
                }

                var formComponent = new FormComponentView()
                {
                    Type = componentTypes[parentSource.ComponentListID],
                    ID = parentSource.ItemID.ToString(),
                    ItemID = parentSource.ItemID,
                    ItemSourceType = parentSource.ItemSourceType
                };
                // 布局组件组装子组件集合
                if (LAYOUT_COMPONENT_LIST_IDS.Contains(parentSource.ComponentListID))
                {
                    var childrenDetails = formDetails.Where(m => m.ParentID == parentSource.ItemID).OrderBy(m => m.Sort).ToList();
                    (var children, formData) = ConvertComponentsAndDatas(formType, componentAttributeList, formDetails, childrenDetails, formData, componentTypes, datas, getComponentProps, computeGroupScore);
                    formComponent.Children = children;
                    formComponent.IsLayout = true;
                }
                else
                {
                    // 试卷模式下，添加状态属性，
                    if (formType == ((int)FormTypeEnum.Examination).ToString())
                    {
                        props["examinationStatus"] = 1;
                        var value = datas?.Find(m => m.ParentID == parentSource.ItemID);
                        if (value != null && value.ExaminationStatus.HasValue)
                        {
                            props["examinationStatus"] = (int)value.ExaminationStatus.Value;
                            // 非正确答案标题颜色标红
                            if (value.ExaminationStatus.Value == ExaminationStatusEnum.Error)
                            {
                                props["color"] = "#ff0000";
                            }
                        }
                    }
                    // 设置值
                    formData = SetFormDatas(formData, parentSource, datas, props);
                }
                formComponent.Props = props;
                components.Add(formComponent);
                if (parentSource.ComponentListID == COMPONENT_LIST_ID_GROUP && !computeGroupScore)
                {
                    continue;
                }
            }
            return (components, formData);
        }

        /// <summary>
        /// 添加禁用属性
        /// </summary>
        /// <param name="props"></param>
        /// <param name="itemDisabledExpression"></param>
        /// <returns></returns>
        private Dictionary<string, object> AddDisabledProp(Dictionary<string, object> props, string itemDisabledExpression)
        {
            if (string.IsNullOrWhiteSpace(itemDisabledExpression))
            {
                return props;
            }
            // 防止传入错误：DataTable的Compute计算表达式只需一个=号，多了会报错
            itemDisabledExpression = itemDisabledExpression.Replace("===", "=").Replace("==", "=");
            bool disabled = false;
            try
            {
                disabled = (Boolean)new DataTable().Compute(itemDisabledExpression, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.Error($"条件计算错误，itemDisabledExpression：{itemDisabledExpression},{ex}");
            }
            if (!disabled)
            {
                return props;
            }
            if (props.ContainsKey("disabled"))
            {
                props["disabled"] = disabled;
            }
            else
            {
                props.Add("disabled", disabled);
            }
            return props;
        }
        /// <summary>
        /// 获取组件的动态关联条件属性
        /// </summary>
        /// <param name="props"></param>
        /// <param name="conditionList"></param>
        /// <param name="componentAttributeList"></param>
        /// <param name="dynamicFormDetailID"></param>
        /// <returns></returns>
        private static Dictionary<string, object> AddConditionProp(Dictionary<string, object> props, List<DynamicFormDetailConditionInfo> conditionList, List<ComponentAttributeInfo> componentAttributeList, string dynamicFormDetailID)
        {
            var detailConditionList = conditionList.Where(m => m.DynamicFormDetailID == dynamicFormDetailID).ToList();
            // 将动态关联条件转换为组件属性
            if (detailConditionList.Count <= 0)
            {
                return props;
            }
            var componentAttributeIDs = detailConditionList.Select(m => m.ComponentAttributeID).Distinct().ToList();
            foreach (var componentAttributeID in componentAttributeIDs)
            {
                var conditionComponentAttribute = componentAttributeList.Find(m => m.ComponentAttributeID == componentAttributeID);
                if (conditionComponentAttribute == null)
                {
                    continue;
                }
                var conditions = detailConditionList.Where(m => m.ComponentAttributeID == componentAttributeID)
                                   .OrderBy(m => m.Sort)
                                   .Select(m => new FormDetailConditionView()
                                   {
                                       ItemID = m.ItemID,
                                       Condition = m.Condition,
                                       Value = m.ConditionValue,
                                       ConditionType = m.ConditionType,
                                   }).ToList();
                props.Add(conditionComponentAttribute.Attribute, conditions);
            }
            return props;
        }
        /// <summary>
        /// 添加组件的选项属性
        /// </summary>
        /// <param name="props"></param>
        /// <param name="formDetailList"></param>
        /// <param name="itemID"></param>
        /// <param name="attributeList"></param>
        /// <param name="optionAttributeName">选项属性名称</param>
        /// <returns></returns>
        private static Dictionary<string, object> AddComponentOptionsProp(Dictionary<string, object> props, List<DynamicFormDetailInfo> formDetailList, string itemID, List<DynamicFormDetailAttributeInfo> attributeList, string optionAttributeName)
        {
            var options = new List<ComponentOptionView>();
            var detailList = formDetailList.Where(m => m.ParentID == itemID).OrderBy(m => m.Sort).ToList();
            if (detailList.Count <= 0)
            {
                return props;
            }
            foreach (var detail in detailList)
            {
                var detailAttributeList = attributeList.Where(m => m.DynamicFormDetailID == detail.DynamicFormDetailID).ToList();
                var flag = detailAttributeList.Find(m => m.ComponentAttributeID == COMPONENT_OPTIONS_ATTRIBUTE_TO_ID[optionAttributeName]);
                if (flag == null)
                {
                    continue;
                }
                var inputEnableProp = detailAttributeList.Find(m => m.ComponentAttributeID == COMPONENT_ATTRIBUTE_ID_INPUT_ENABLE);
                bool inputEnable = inputEnableProp != null && bool.Parse(inputEnableProp.AttributeValue);
                options.Add(new ComponentOptionView()
                {
                    Value = detail.ItemID.ToString(),
                    Label = detailAttributeList.Find(m => m.ComponentAttributeID == COMPONENT_ATTRIBUTE_ID_LABEL)?.AttributeValue ?? "",
                    ItemSourceType = detail.ItemSourceType,
                    InputEnable = inputEnable,
                });
            }
            props.Add(optionAttributeName, options);
            return props;
        }

        /// <summary>
        /// 设置项目的回显值
        /// </summary>
        /// <param name="formDatas"></param>
        /// <param name="formDetail"></param>
        /// <param name="datas"></param>
        /// <param name="props"></param>
        /// <returns></returns>
        private Dictionary<string, object> SetFormDatas(Dictionary<string, object> formDatas, DynamicFormDetailInfo formDetail, List<FormValueView> datas, Dictionary<string, object> props)
        {
            if (datas == null)
            {
                return formDatas;
            }
            var data = datas.Find(m => m.ID == formDetail.ItemID && (m.ParentID == m.ID || (m.ParentID != m.ID && m.ParentID == formDetail.ParentID)));
            // 如果有值 赋值后返回
            if (data != null)
            {
                formDatas.TryAdd(formDetail.ItemID.ToString(), data.Value);
                return formDatas;
            }
            // 单选、多选 值特殊处理
            if (HAS_OPTIONS_COMPONENT_LIST_IDS.Contains(formDetail.ComponentListID))
            {
                var optionDatas = datas.Where(m => m.ParentID == formDetail.ItemID).ToList();
                if (optionDatas.Count > 0)
                {
                    var optionData = new List<string>();
                    optionDatas.ForEach(option =>
                    {
                        optionData.Add(option.ID.ToString());
                        // 选项如果有值，则是允许填空的选项,赋值让前端可以正常回显
                        var key = $"{formDetail.ItemID}||{option.ID}";
                        if (option.Value != null)
                        {
                            formDatas.TryAdd(key, option.Value);
                        }
                    });
                    object value = formDetail.ComponentListID == COMPONENT_LIST_ID_RADIO ? optionData[0] : optionData.ToArray();
                    formDatas.TryAdd(formDetail.ItemID.ToString(), value);
                    return formDatas;
                }
            }
            // 默认值
            if (props.TryGetValue(COMPONENT_ATTRIBUTE_DEFAULT, out object defaultValue) && defaultValue != null)
            {
                if (formDetail.ComponentListID == COMPONENT_LIST_ID_GRADE)
                {
                    var value = new Dictionary<string, string>() { { "score", defaultValue.ToString() } };
                    formDatas.TryAdd(formDetail.ItemID.ToString(), value);
                }
                else
                {
                    formDatas.TryAdd(formDetail.ItemID.ToString(), defaultValue);
                }
            }
            return formDatas;
        }
        #endregion

        #region 保存动态表单模板
        public async Task<string> SaveFormTemplate(FormTemplateView formTemplateView, string employeeID)
        {
            if (formTemplateView.Props == null || formTemplateView.Components?.Count <= 0)
            {
                _logger.Error("SaveFormTemplate方法缺失参数！");
                return null;
            }
            var now = DateTime.Now;
            var componentList = await _componentListRepository.GetComponentListByType("DynamicForm");
            var componentTypes = componentList.ToDictionary(m => m.ControlerType, m => m.ComponentListID);
            var componentAttributeList = await _componentAttributeRepository.GetAttributeList();
            // 保存表单主表
            var dynamicFormRecord = await SaveDynamicFormRecord(formTemplateView.Props, employeeID, now);
            var UpdateCacheList = GetUpdateCacheList(dynamicFormRecord);

            // 存储ID与ItemID的对应关系，处理关联条件保存时需要替换
            var idToItemIDDict = new Dictionary<string, string>();
            // 将前端的临时ID转换为各字典表的ID,并返回ID与ItemID的对应关系字典，若有新增字典数据，返回字典表清缓存方法
            (var components, idToItemIDDict, UpdateCacheList) = await ConvertFormDetailItemID(formTemplateView.Components, idToItemIDDict, UpdateCacheList, formTemplateView.AllowCreateDictionary, componentTypes);
            // 保存明细表
            await SaveFormDetails(components, dynamicFormRecord.DynamicFormRecordID, employeeID, now, componentTypes, componentAttributeList, idToItemIDDict);
            var result = false;
            try
            {
                result = await _unitOfWork.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
            }
            // 保存成功，更新缓存
            if (result)
            {
                foreach (var updateCacheFunc in UpdateCacheList)
                {
                    await updateCacheFunc.Value();
                }
            }
            return result ? dynamicFormRecord.DynamicFormRecordID : "";
        }

        /// <summary>
        /// 获取待更新缓存清单
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns></returns>
        private Dictionary<string, Func<Task>> GetUpdateCacheList(DynamicFormRecordInfo dynamicFormRecordInfo)
        {
            var cacheQuery = new Dictionary<string, object>
            {
                { "CacheName", dynamicFormRecordInfo.DynamicFormRecordID },
            };
            // 存放更新缓存方法
            var updateCacheFuncMap = new Dictionary<string, Func<Task>>() {
                { "DynamicFormRecord", async () =>await _dynamicFormRecordRepository.UpdateCache() },
                { "DynamicFormDetail", async () =>await _dynamicFormDetailRepository.UpdateCacheByQuery(cacheQuery) },
                { "DynamicFormDetailAttribute", async () =>await _dynamicFormDetailAttributeRepository.UpdateCacheByQuery(cacheQuery) },
                { "DynamicFormDetailCondition", async () =>await _dynamicFormDetailConditionRepository.UpdateCache() },
            };
            return updateCacheFuncMap;
        }
        #region 保存表单主表
        /// <summary>
        /// 创建动态表单主表
        /// </summary>
        /// <param name="props"></param>
        /// <param name="employeeID"></param>
        /// <param name="now"></param>
        /// <returns></returns>
        private async Task<DynamicFormRecordInfo> SaveDynamicFormRecord(FormRecordView props, string employeeID, DateTime now)
        {
            DynamicFormRecordInfo dynamicFormRecord;
            var session = _sessionCommonServer.GetSessionByCache();
            if (string.IsNullOrWhiteSpace(props.FormID))
            {
                dynamicFormRecord = new DynamicFormRecordInfo()
                {
                    HospitalID = session.HospitalID,
                    AddEmployeeID = employeeID,
                    AddDateTime = now,
                    DeleteFlag = ""
                };
                dynamicFormRecord.DynamicFormRecordID = dynamicFormRecord.GetId();
                await _unitOfWork.GetRepository<DynamicFormRecordInfo>().InsertAsync(dynamicFormRecord);
            }
            else
            {
                dynamicFormRecord = await _dynamicFormRecordRepository.GetFormByFormRecordID(props.FormID);
                // 修改 删除旧数据
                await DeleteOldDetailDatas(props.FormID, employeeID);
            }
            dynamicFormRecord.FormName = props.FormName;
            dynamicFormRecord.FormType = props.FormType;
            dynamicFormRecord.Size = props.Size;
            dynamicFormRecord.Column = props.Column;
            dynamicFormRecord.LabelWidth = props.LabelWidth;
            dynamicFormRecord.LabelPosition = props.LabelPosition;
            dynamicFormRecord.ModifyEmployeeID = employeeID;
            dynamicFormRecord.ModifyDateTime = now;
            return dynamicFormRecord;
        }
        /// <summary>
        /// 删除旧的明细表、明细属性表、明细联动条件表
        /// </summary>
        /// <returns></returns>
        private async Task DeleteOldDetailDatas(string dynamicFormRecordID, string employeeID)
        {
            var oldFormDetailList = await _dynamicFormDetailRepository.GetFormDetailListByFormRecordID(dynamicFormRecordID);
            if (oldFormDetailList.Count <= 0)
            {
                return;
            }
            var formDetailAttributeList = await _dynamicFormDetailAttributeRepository.GetDetailAttributeListByRecordID(dynamicFormRecordID);
            foreach (var detail in oldFormDetailList)
            {
                detail.Delete(employeeID);
                var oldFormDetailAttributeList = formDetailAttributeList.Where(m => m.DynamicFormDetailID == detail.DynamicFormDetailID).ToList();
                if (oldFormDetailAttributeList.Count > 0)
                {
                    oldFormDetailAttributeList.ForEach(m => m.Delete(employeeID));
                    _dbContext.UpdateRange(oldFormDetailAttributeList, e => e.DeleteFlag, e => e.ModifyDateTime, e => e.ModifyEmployeeID);
                }
                var oldFormDetailConditionList = await _dynamicFormDetailConditionRepository.GetDetailConditionsByDetailID(detail.DynamicFormDetailID);
                if (oldFormDetailConditionList.Count > 0)
                {
                    oldFormDetailConditionList.ForEach(m => m.Delete(employeeID));
                    _dbContext.UpdateRange(oldFormDetailAttributeList, e => e.DeleteFlag, e => e.ModifyDateTime, e => e.ModifyEmployeeID);
                }
            };
            _dbContext.UpdateRange(oldFormDetailList, e => e.DeleteFlag, e => e.ModifyDateTime, e => e.ModifyEmployeeID);
        }
        #endregion

        #region 转换ItemID
        /// <summary>
        /// 转换表单明细ID
        /// </summary>
        /// <param name="components"></param>
        /// <param name="idToItemIDDict"></param>
        /// <param name="updateCacheFuncMap"></param>
        /// <param name="allowCreateDictionary"></param>
        /// <param name="componentTypes"></param>
        /// <param name="sequenceIndex">序列，如果component的SourceType为空，启用此序列，防止组件ID重复</param>
        /// <returns></returns>
        private async Task<Tuple<List<FormComponentView>, Dictionary<string, string>, Dictionary<string, Func<Task>>>> ConvertFormDetailItemID(List<FormComponentView> components, Dictionary<string, string> idToItemIDDict, Dictionary<string, Func<Task>> updateCacheFuncMap, bool? allowCreateDictionary, Dictionary<string, int> componentTypes, int? sequenceIndex = null)
        {
            int sequence = sequenceIndex ?? 1;
            for (var i = 0; i < components.Count; i++)
            {
                var component = components[i];
                // 优先去固定ID
                if (!string.IsNullOrWhiteSpace(component.FixedItemID))
                {
                    component.ItemID = component.FixedItemID;
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(component.Props["label"].ToString()))
                    {
                        component.ItemID = "";
                    }
                    else if (!allowCreateDictionary.HasValue || allowCreateDictionary.Value)
                    {
                        (component.ItemID, updateCacheFuncMap) = await GetItemIDBySourceType(component.ItemSourceType, component.Props["label"].ToString(), updateCacheFuncMap);
                    }
                    else
                    {
                        component.ItemID = component.ID.StartsWith("temp-") ? "" : component.ID;
                    }
                }
                component.ItemID = string.IsNullOrWhiteSpace(component.ItemID) ? (sequence++).ToString() : component.ItemID;
                idToItemIDDict.TryAdd(component.ID, component.ItemID);
                // 人员和岗位直接跳过
                if (APPLICATION_OPTIONS_COMPONENT_LIST_IDS.Contains(componentTypes[component.Type]))
                {
                    continue;
                }
                // 处理tabs的pages属性或者单选多选的options属性
                if (component.Props.ContainsKey(COMPONENT_ATTRIBUTE_OPTIONS) || component.Props.ContainsKey(COMPONENT_ATTRIBUTE_PAGES))
                {
                    var propName = "";
                    object optionsValue = null;
                    if (component.Props.TryGetValue(COMPONENT_ATTRIBUTE_PAGES, out object pageValue))
                    {
                        optionsValue = pageValue;
                        propName = COMPONENT_ATTRIBUTE_PAGES;
                    }
                    if (component.Props.TryGetValue(COMPONENT_ATTRIBUTE_OPTIONS, out object value))
                    {
                        optionsValue = value;
                        propName = COMPONENT_ATTRIBUTE_OPTIONS;
                    }
                    if (optionsValue == null)
                    {
                        continue;
                    }
                    string optionsValueJsonString = JsonConvert.SerializeObject(optionsValue);
                    var options = ListToJson.ToList<List<ComponentOptionView>>(optionsValueJsonString);
                    if (options == null || options.Count <= 0)
                    {
                        continue;
                    }
                    for (var j = 0; j < options.Count; j++)
                    {
                        var option = options[j];
                        var optionItemID = "";
                        if (!allowCreateDictionary.HasValue || allowCreateDictionary.Value)
                        {
                            (optionItemID, updateCacheFuncMap) = await GetItemIDBySourceType(component.ItemSourceType, option.Label, updateCacheFuncMap);
                        }
                        else
                        {
                            optionItemID = option.Value.StartsWith("temp-") ? "" : option.Value;
                        }
                        optionItemID = string.IsNullOrWhiteSpace(optionItemID) ? (sequence++).ToString() : optionItemID;
                        idToItemIDDict.TryAdd(option.Value, optionItemID);
                        option.Value = optionItemID.ToString();
                    }
                    component.Props[propName] = ListToJson.ToJson(options);
                }
                // 如果是布局组件且子项不为空
                if (component.IsLayout && component.Children?.Count > 0)
                {
                    (component.Children, idToItemIDDict, updateCacheFuncMap) = await ConvertFormDetailItemID(component.Children, idToItemIDDict, updateCacheFuncMap, allowCreateDictionary, componentTypes, sequence);
                }
            }
            return Tuple.Create(components, idToItemIDDict, updateCacheFuncMap);
        }
        /// <summary>
        /// 根据SourceType获取ItemID
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="label"></param>
        /// <param name="updateCacheFuncMap"></param>
        /// <returns></returns>
        private async Task<Tuple<string, Dictionary<string, Func<Task>>>> GetItemIDBySourceType(string sourceType, string label, Dictionary<string, Func<Task>> updateCacheFuncMap)
        {
            var newItemID = "";
            if (string.IsNullOrWhiteSpace(sourceType) || string.IsNullOrWhiteSpace(label))
            {
                return Tuple.Create(newItemID, updateCacheFuncMap);
            }
            var formExtendFactory = _formExtendFactory.SwitchCondition(sourceType);
            if (formExtendFactory == null)
            {
                _logger.Error($"ConvertFormDetailItemID方法中获取工厂失败，ItemSourceType={sourceType}");
                return Tuple.Create(newItemID, updateCacheFuncMap);
            }
            (newItemID, var updateCache) = await formExtendFactory.GetItemIDByLabel(label);
            // 需要清空缓存
            if (updateCache != null && !updateCacheFuncMap.ContainsKey(sourceType))
            {
                updateCacheFuncMap.Add(sourceType, updateCache);
            }
            return Tuple.Create(newItemID, updateCacheFuncMap);
        }
        #endregion

        #region 保存表单明细、明细属性、明细关联关系

        /// <summary>
        /// 保存表单明细
        /// </summary>
        /// <param name="components"></param>
        /// <param name="dynamicFormRecordID"></param>
        /// <param name="employeeID"></param>
        /// <param name="now"></param>
        /// <param name="componentTypes"></param>
        /// <param name="componentAttributeList"></param>
        /// <param name="idToItemIDDict"></param>
        /// <param name="parentID"></param>
        /// <returns></returns>
        private async Task SaveFormDetails(List<FormComponentView> components, string dynamicFormRecordID, string employeeID, DateTime now, Dictionary<string, int> componentTypes, List<ComponentAttributeInfo> componentAttributeList, Dictionary<string, string> idToItemIDDict, string parentID = null)
        {
            var dynamicFormDetailList = new List<DynamicFormDetailInfo>();
            for (var i = 0; i < components.Count; i++)
            {
                var component = components[i];
                var dynamicFormDetail = new DynamicFormDetailInfo()
                {
                    DynamicFormRecordID = dynamicFormRecordID,
                    ParentID = parentID,
                    ItemID = component.ItemID,
                    ItemSourceType = component.ItemSourceType,
                    ComponentListID = componentTypes[component.Type],
                    Sort = i + 1,
                    AddEmployeeID = employeeID,
                    AddDateTime = now,
                    ModifyEmployeeID = employeeID,
                    ModifyDateTime = now,
                    DeleteFlag = ""
                };
                dynamicFormDetail.DynamicFormDetailID = dynamicFormDetail.GetId();
                dynamicFormDetailList.Add(dynamicFormDetail);
                await SaveFormDetailAttribute(dynamicFormRecordID, dynamicFormDetail.DynamicFormDetailID, dynamicFormDetail.ItemID, component.Props, componentAttributeList, employeeID, now, idToItemIDDict);
                if (component.IsLayout && component.Children?.Count > 0)
                {
                    await SaveFormDetails(component.Children, dynamicFormRecordID, employeeID, now, componentTypes, componentAttributeList, idToItemIDDict, component.ItemID);
                }
            }
            await _unitOfWork.GetRepository<DynamicFormDetailInfo>().InsertAsync(dynamicFormDetailList);
        }

        /// <summary>
        /// 保存表单明细属性
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <param name="dynamicFormDetailID"></param>
        /// <param name="itemID"></param>
        /// <param name="props"></param>
        /// <param name="componentAttributeList"></param>
        /// <param name="employeeID"></param>
        /// <param name="now"></param>
        /// <param name="idToItemIDDict"></param>
        /// <returns></returns>
        private async Task SaveFormDetailAttribute(string dynamicFormRecordID, string dynamicFormDetailID, string itemID, Dictionary<string, object> props, List<ComponentAttributeInfo> componentAttributeList, string employeeID, DateTime now, Dictionary<string, string> idToItemIDDict)
        {
            if (props == null)
            {
                return;
            }
            var detailAttributeList = new List<DynamicFormDetailAttributeInfo>();
            foreach (var prop in props)
            {
                var componentAttribute = componentAttributeList.Find(m => m.Attribute == prop.Key);
                if (componentAttribute == null)
                {
                    continue;
                }
                // 特殊处理tabs的pages属性或者单选多选的options属性
                if (prop.Key == COMPONENT_ATTRIBUTE_PAGES || prop.Key == COMPONENT_ATTRIBUTE_OPTIONS)
                {
                    await AddFormDetailByOptions(dynamicFormRecordID, itemID, prop.Value, employeeID, now, prop.Key);
                }
                else if (prop.Key.EndsWith("Conditions"))
                {
                    await SaveFormDetailCondition(dynamicFormDetailID, componentAttribute.ComponentAttributeID, prop.Value, employeeID, now, idToItemIDDict);
                }
                else
                {
                    var attributeValue = (prop.Value ?? "").ToString();
                    if (string.IsNullOrEmpty(attributeValue))
                    {
                        continue;
                    }
                    if (CONVERT_JSON_COMPONENT_ATTRIBUTE_TYPES.Contains(componentAttribute.DataType))
                    {
                        attributeValue = ListToJson.ToJson(prop.Value);
                    }
                    // 条件表达式或默认值或pageID中的临时ID 转换
                    if (prop.Key.EndsWith("ConditionExpression") || prop.Key == COMPONENT_ATTRIBUTE_DEFAULT || prop.Key == COMPONENT_ATTRIBUTE_PAGEID)
                    {
                        foreach (var idToItemID in idToItemIDDict)
                        {
                            attributeValue = attributeValue.Replace(idToItemID.Key, idToItemID.Value.ToString());
                        }
                    }
                    if (prop.Key.EndsWith("ConditionContent") && !string.IsNullOrWhiteSpace(attributeValue) && attributeValue.Length > 1000)
                    {
                        attributeValue = "点击查看详情";
                    }
                    var detailAttribute = GetFormDetailAttribute(dynamicFormDetailID, componentAttribute.ComponentAttributeID, attributeValue, now, employeeID);
                    detailAttributeList.Add(detailAttribute);
                }
            }
            await _unitOfWork.GetRepository<DynamicFormDetailAttributeInfo>().InsertAsync(detailAttributeList);
        }

        /// <summary>
        /// 将表单明细的选项属性转换为表单明细
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <param name="parentID"></param>
        /// <param name="optionsProp"></param>
        /// <param name="employeeID"></param>
        /// <param name="now"></param>
        /// <param name="optionAttributeName"></param>
        /// <returns></returns>
        private async Task AddFormDetailByOptions(string dynamicFormRecordID, string parentID, object optionsProp, string employeeID, DateTime now, string optionAttributeName)
        {
            // 判断optionsProp是否为空集合，防止转换时报错
            if (optionsProp is List<PaperQuestionOptionsView> temp && temp.Count <= 0)
            {
                return;
            }
            var test = optionsProp.ToString();
            var options = ListToJson.ToList<List<ComponentOptionView>>(optionsProp.ToString());
            var dynamicFormDetailList = new List<DynamicFormDetailInfo>();
            if (options == null || options.Count <= 0)
            {
                return;
            }
            for (var i = 0; i < options.Count; i++)
            {
                var option = options[i];
                var dynamicFormDetail = new DynamicFormDetailInfo()
                {
                    DynamicFormRecordID = dynamicFormRecordID,
                    ParentID = parentID,
                    ItemID = option.Value,
                    ItemSourceType = option.ItemSourceType,
                    ComponentListID = COMPONENT_LIST_ID_LABEL,
                    Sort = i + 1,
                    AddEmployeeID = employeeID,
                    AddDateTime = now,
                    ModifyEmployeeID = employeeID,
                    ModifyDateTime = now,
                    DeleteFlag = ""
                };
                dynamicFormDetail.DynamicFormDetailID = dynamicFormDetail.GetId();
                dynamicFormDetailList.Add(dynamicFormDetail);
                var detailAttributes = new List<DynamicFormDetailAttributeInfo>()
                {
                    GetFormDetailAttribute(dynamicFormDetail.DynamicFormDetailID, COMPONENT_OPTIONS_ATTRIBUTE_TO_ID[optionAttributeName], "true",now,employeeID),
                    GetFormDetailAttribute(dynamicFormDetail.DynamicFormDetailID, COMPONENT_ATTRIBUTE_ID_LABEL, option.Label,now,employeeID),
                    GetFormDetailAttribute(dynamicFormDetail.DynamicFormDetailID, COMPONENT_ATTRIBUTE_ID_INPUT_ENABLE, option.InputEnable==null?"false":option.InputEnable.ToString().ToLower(),now,employeeID)
                };
                await _unitOfWork.GetRepository<DynamicFormDetailAttributeInfo>().InsertAsync(detailAttributes);
            }
            await _unitOfWork.GetRepository<DynamicFormDetailInfo>().InsertAsync(dynamicFormDetailList);
        }
        /// <summary>
        /// 组装项目明细属性
        /// </summary>
        /// <param name="dynamicFormDetailID"></param>
        /// <param name="componentAttributeID"></param>
        /// <param name="attributeValue"></param>
        /// <param name="now"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private DynamicFormDetailAttributeInfo GetFormDetailAttribute(string dynamicFormDetailID, int componentAttributeID, string attributeValue, DateTime now, string employeeID)
        {
            return new DynamicFormDetailAttributeInfo()
            {
                DynamicFormDetailID = dynamicFormDetailID,
                ComponentAttributeID = componentAttributeID,
                AttributeValue = attributeValue,
                AddEmployeeID = employeeID,
                AddDateTime = now,
                ModifyEmployeeID = employeeID,
                ModifyDateTime = now,
                DeleteFlag = ""
            };
        }
        /// <summary>
        /// 保存表单明细联动条件
        /// </summary>
        /// <param name="dynamicFormDetailID"></param>
        /// <param name="componentAttributeID"></param>
        /// <param name="conditions"></param>
        /// <param name="employeeID"></param>
        /// <param name="now"></param>
        /// <param name="idToItemIDDict"></param>
        /// <returns></returns>
        private async Task SaveFormDetailCondition(string dynamicFormDetailID, int componentAttributeID, object conditions, string employeeID, DateTime now, Dictionary<string, string> idToItemIDDict)
        {
            var detailConditions = ListToJson.ToList<List<FormDetailConditionView>>(conditions.ToString());
            if (detailConditions == null || detailConditions.Count <= 0)
            {
                return;
            }
            var dynamicFormDetailConditionList = new List<DynamicFormDetailConditionInfo>();
            for (var i = 0; i < detailConditions.Count; i++)
            {
                var detailCondition = detailConditions[i];
                // 如果idToItemIDDict包含key
                if (idToItemIDDict.TryGetValue(detailCondition.ItemID, out string itemID))
                {
                    detailCondition.ItemID = itemID;
                }
                var conditionValue = detailCondition.Value;
                if (idToItemIDDict.ContainsKey(conditionValue))
                {
                    conditionValue = idToItemIDDict[conditionValue].ToString();
                }
                var dynamicFormDetailCondition = new DynamicFormDetailConditionInfo()
                {
                    DynamicFormDetailID = dynamicFormDetailID,
                    ComponentAttributeID = componentAttributeID,
                    ItemID = detailCondition.ItemID,
                    Condition = detailCondition.Condition,
                    ConditionValue = conditionValue,
                    ConditionType = detailCondition.ConditionType ?? "",
                    Sort = i + 1,
                    AddEmployeeID = employeeID,
                    AddDateTime = now,
                    ModifyEmployeeID = employeeID,
                    ModifyDateTime = now,
                    DeleteFlag = ""
                };
                dynamicFormDetailConditionList.Add(dynamicFormDetailCondition);
            }
            await _unitOfWork.GetRepository<DynamicFormDetailConditionInfo>().InsertAsync(dynamicFormDetailConditionList);
        }

        #endregion
        #endregion

        /// <summary>
        /// 删除动态表单模板
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task DeleteFormTemplate(string dynamicFormRecordID, string employeeID)
        {
            var dynamicFormRecord = await _dynamicFormRecordRepository.GetFormByFormRecordID(dynamicFormRecordID);
            if (dynamicFormRecord == null)
            {
                _logger.Error($"DeleteFormTemplate方法中获取动态模板失败，DeleteFormTemplate={DeleteFormTemplate}");
                return;
            }
            dynamicFormRecord.Delete(employeeID);
            var formDetailList = await _dynamicFormDetailRepository.GetFormDetailListByFormRecordID(dynamicFormRecordID);
            if (formDetailList.Count > 0)
            {
                formDetailList.ForEach(m => m.Delete(employeeID));
                _unitOfWork.GetRepository<DynamicFormDetailInfo>().Update(formDetailList);
                var detailIDs = formDetailList.Select(m => m.DynamicFormDetailID).ToList();
                var detailAttributeList = await _dynamicFormDetailAttributeRepository.GetDetailAttributeListByRecordID(dynamicFormRecordID);
                if (detailAttributeList.Count > 0)
                {
                    detailAttributeList.ForEach(m => m.Delete(employeeID));
                    _unitOfWork.GetRepository<DynamicFormDetailAttributeInfo>().Update(detailAttributeList);
                }
                var detailConditionList = await _dynamicFormDetailConditionRepository.GetDetailConditionsByDetailIDList(detailIDs);
                if (detailConditionList.Count > 0)
                {
                    detailConditionList.ForEach(m => m.Delete(employeeID));
                    _unitOfWork.GetRepository<DynamicFormDetailConditionInfo>().Update(detailConditionList);
                }
            }
        }

        public async Task UpdateFormCache()
        {
            await _dynamicFormRecordRepository.UpdateCache();
            await _dynamicFormDetailRepository.UpdateCache();
            await _dynamicFormDetailAttributeRepository.UpdateCache();
            await _dynamicFormDetailConditionRepository.UpdateCache();
        }

        /// <summary>
        /// 根据给出的表单明细数据，获取其直接与间接子级数据
        /// </summary>
        /// <typeparam name="T">要返回的数据类型</typeparam>
        /// <param name="formDetails">动态表单明细集合</param>
        /// <param name="formDetail">给出的明细数据</param>
        /// <param name="predicate">过滤子孙数据，默认不过滤</param>
        /// <param name="selector">要返回子孙数据的属性，默认为表单明细对象</param>
        /// <returns></returns>
        public List<T> GetRecursiveChildren<T>(List<DynamicFormDetailInfo> formDetails, DynamicFormDetailInfo formDetail, Func<DynamicFormDetailInfo, bool> predicate = null, Func<DynamicFormDetailInfo, T> selector = null)
        {
            List<T> children = [];
            predicate ??= m => true;
            selector ??= typeof(T) == typeof(DynamicFormDetailInfo) ? m => (T)(object)m : throw new ArgumentException("不允许转换类型");
            var nestChildCollection = formDetails.Where(m => m.ParentID == formDetail.ItemID)
                .Where(predicate).ToArray();
            if (nestChildCollection.Length == 0)
            {
                return children;
            }
            children.AddRange(nestChildCollection.Select(selector));
            foreach (var child in nestChildCollection)
            {
                children.AddRange(GetRecursiveChildren(formDetails, child, predicate, selector));
            }
            return children;
        }
    }
}
