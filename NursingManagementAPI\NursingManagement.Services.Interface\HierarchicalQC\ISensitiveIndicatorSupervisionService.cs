﻿using NursingManagement.ViewModel;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public interface ISensitiveIndicatorSupervisionService
    {
        /// <summary>
        /// 获取督导记录
        /// </summary>
        /// <param name="supervisionQuery"></param>
        /// <returns></returns>
        Task<VisitsTableView> GetSensitiveIndicatorSupervisionRecord(SupervisionQueryView supervisionQuery);
        /// <summary>
        /// 获取督导记录详情
        /// </summary>
        /// <param name="profileID"></param>
        /// <param name="qcMainID"></param>
        /// <returns></returns>
        Task<FormTemplateView> GetSensitiveQcAssessViewAsync(string profileID, string qcMainID);
        /// <summary>
        /// 保存危重患者访视记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveSensitiveRecordAsync(SupervisionRecordSaveView view);
        /// <summary>
        /// 获取访视记录
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <param name="supervisionType"></param>
        /// <param name="templateCode"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<VisitsTableView> GetSensitiveQcRecordAsync(string sourceID, string sourceType, string supervisionType, string templateCode, string hospitalID);
        /// <summary>
        /// 删除敏感记录
        /// </summary>
        /// <param name="patientProfileRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteSensitiveRecordAsync(string patientProfileRecordID, string employeeID);
        /// <summary>
        /// 删除所有督导记录
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceID"></param>
        /// <param name="relatedTableName"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteAllSupervisionRecordAsync(string sourceType, string sourceID, string relatedTableName, string employeeID);
    }
}
