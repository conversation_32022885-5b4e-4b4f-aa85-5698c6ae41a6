﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DynamicTableListRepository : IDynamicTableListRepository
    {
        private NursingManagementDbContext _dbContext;
        public DynamicTableListRepository(
             NursingManagementDbContext dbContext
            )
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据表格分类和表格子类获取数据
        /// </summary>
        /// <param name="tableType"></param>
        /// <param name="tableSubType"></param>
        /// <returns></returns>
        public async Task<DynamicTableListInfo> GetDataByType(string tableType, string tableSubType)
        {
            return await _dbContext.DynamicTableListInfos.FirstOrDefaultAsync(m => m.TableType == tableType && m.TableSubType == tableSubType && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据表格分类和表格子类获取DynamicTableListID
        /// </summary>
        /// <param name="tableType"></param>
        /// <param name="tableSubType"></param>
        /// <returns></returns>
        public async Task<int> GetIDByType(string tableType, string tableSubType)
        {
            return await _dbContext.DynamicTableListInfos.Where(m => m.TableType == tableType && m.TableSubType == tableSubType && m.DeleteFlag != "*").Select(m=>m.DynamicTableListID).FirstOrDefaultAsync();
        }
    }
}
