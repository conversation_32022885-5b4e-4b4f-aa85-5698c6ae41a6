﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  考勤审核表   
    /// </summary>
    [Table("AttendanceApproveRecord")]
    public class AttendanceApproveRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考勤主表记录号，主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string AttendanceApproveRecordID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门编码
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 考勤年份
        /// </summary>
        public int AttendanceYear { get; set; }
        /// <summary>
        /// 考勤月份
        /// </summary>
        public int AttendanceMonth { get; set; }
        /// <summary>
        /// 审核人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ApproveEmployeeID { get; set; }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? ApproveDateTime { get; set; }
        /// <summary>
        /// 审核状态（1,未审核,2审核通过,3审核驳回，4审核取消）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }
    }
}