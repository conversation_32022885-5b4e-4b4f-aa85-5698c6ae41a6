﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModel;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 岗位控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/PatientProfileRecord")]
    [EnableCors("any")]
    public class PatientProfileRecordController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IPatientProfileRecordService _patientProfileRecordService;

        /// <summary>
        /// 岗位控制器
        /// </summary>
        public PatientProfileRecordController(
              ISessionService session
            , IPatientProfileRecordService patientProfileRecordService
            )
        {
            _session = session;
            _patientProfileRecordService = patientProfileRecordService;
        }
        /// <summary>
        /// 获取CCC某种状况患者信息（病危、病重……）
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("GetCCCPatientProfile")]
        public async Task<IActionResult> GetCCCPatientProfile([FromBody] GetCCCPatientProfileDataView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _patientProfileRecordService.GetCCCPatientProfileRecord(view, session.HospitalID);
            return result.ToJson();
        }

    }
}