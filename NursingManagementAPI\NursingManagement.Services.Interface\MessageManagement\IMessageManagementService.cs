﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IMessageManagementService
    {
        /// <summary>
        /// 获取消息列表
        /// </summary>
        /// <param name="messageType"></param>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        Task<List<MessageRecordView>> GetMessageListAsync(string messageType, List<int> departmentIDs);

        /// <summary>
        /// 获取消息详情
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <returns>消息详情</returns>
        Task<string> GetMessageDetailAsync(string messageRecordID);

        /// <summary>
        /// 添加新消息
        /// </summary>
        /// <param name="messageView">消息信息</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>添加结果</returns>
        Task<bool> AddMessageAsync(MessageRecordView messageView, string employeeId);

        /// <summary>
        /// 更新消息
        /// </summary>
        /// <param name="messageView">更新的消息信息</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateMessageAsync(MessageRecordView messageView, string employeeId);

        /// <summary>
        /// 删除消息
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteMessageAsync(string messageRecordID, string employeeId);

        /// <summary>
        /// 发布消息
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <param name="messageStatus"></param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>发布结果</returns>
        Task<bool> PublishMessageAsync(string messageRecordID, string messageStatus, string employeeId);
        /// <summary>
        /// 发送系统预更新通知
        /// </summary>
        /// <param name="messageRecordID"></param>
        /// <param name="customMessage"></param>
        /// <returns></returns>
        Task<bool> SendSystemPreUpdateMessage(string messageRecordID, string customMessage);
        /// <summary>
        /// 获取最后一条系统更新记录
        /// </summary>
        /// <returns></returns>
        Task<MessageRecordView> GetLastSystemUpdateRecord();
    }
}