﻿namespace NursingManagement.ViewModels
{
    public class ShiftSchedulingPost
    {
        /// <summary>
        /// 部门岗位序号
        /// </summary>
        public int DepartmentPostID { get; set; }
        /// <summary>
        /// 岗位类别
        /// </summary>
        public string PostType { get; set; }
        /// <summary>
        /// 部门岗位名称
        /// </summary>
        public string DepartmentPostName { get; set; }
        /// <summary>
        /// 部门岗位名称简称
        /// </summary>
        public string DepartmentPostShortName { get; set; }
        /// <summary>
        /// 调班记录号
        /// </summary>
        public string AdjustScheduleRecordID { get; set; }
        /// <summary>
        /// 岗位对应考勤天数
        /// </summary>
        public decimal AttendanceDays { get; set; }
        /// <summary>
        /// 排版预约标记
        /// </summary>
        public bool SchedulingRequestFlag { get; set; }

        /// <summary>
        /// 前景色
        /// </summary>
        public string Color { get; set; }
        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackGroundColor { get; set; }

    }
}
