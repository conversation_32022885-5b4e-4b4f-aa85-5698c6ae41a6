﻿using Microsoft.CSharp.RuntimeBinder;

namespace NursingManagement.Common
{
    /// <summary>
    /// 设置前端请求View公共属性
    /// </summary>
    public static class ViewCommonProp
    {
        /// <summary>
        /// 设置公共属性
        /// </summary>
        /// <param name="view">拥有Session中共有属性的View</param>
        /// <param name="session"></param>
        public static T Set<T>(this T view, Session session)
        {
            if (view is null)
            {
                return view;
            }
            SetProperty(view, "HospitalID", session);
            SetProperty(view, "Language", session);
            SetProperty(view, "EmployeeID", session);
            SetProperty(view, "ClientType", session);
            SetProperty(view, "DepartmentID", session);
            return view;
        }

        /// <summary>
        /// 设置属性
        /// </summary>
        /// <param name="view">传递的View</param>
        /// <param name="propName">属性名</param>
        /// <param name="session">session</param>
        private static void SetProperty(dynamic view, string propName, Session session)
        {
            try
            {
                switch (propName)
                {
                    case "HospitalID":
                        view.HospitalID ??= session.HospitalID;
                        break;
                    case "Language":
                        view.Language = view.Language == 0 ? session.Language : view.Language;
                        break;
                    case "EmployeeID":
                        view.EmployeeID ??= session.EmployeeID;
                        break;
                    case "ClientType":
                        view.ClientType ??= session.ClientType;
                        break;
                    case "DepartmentID":
                        view.DepartmentID = view.DepartmentID == 0 ? session.DepartmentID : view.DepartmentID;
                        break;
                }
            }
            catch (RuntimeBinderException)
            {
                return;
            }
        }
    }
}
