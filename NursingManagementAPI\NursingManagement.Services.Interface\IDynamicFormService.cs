﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public interface IDynamicFormService
    {
        /// <summary>
        /// 根据表单ID获取表单模板
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <param name="datas">项目值集合</param>
        /// <param name="itemDisabled">要禁用的项目</param>
        /// <param name="itemSourceType">项目来源类型，一般是业务字典表名</param>
        /// <param name="computeGroupScore">是否要返回组明细</param>
        /// <returns></returns>
        Task<FormTemplateView> GetFormTemplateByRecordID(string dynamicFormRecordID, List<FormValueView> datas, Dictionary<string, string> itemDisabled, string itemSourceType, bool computeGroupScore = true);

        /// <summary>
        /// 保存表单模板
        /// </summary>
        /// <param name="formTemplateView"></param>
        /// <param name="employeeID"></param>
        /// <returns>保存成功返回</returns>
        Task<string> SaveFormTemplate(FormTemplateView formTemplateView, string employeeID);
        /// <summary>
        /// 删除表单模板
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task DeleteFormTemplate(string dynamicFormRecordID, string employeeID);
        /// <summary>
        /// 更新表单相关缓存
        /// </summary>
        /// <returns></returns>
        Task UpdateFormCache();
        /// <summary>
        /// 根据给出的表单明细数据，获取其直接与间接子级数据
        /// </summary>
        /// <typeparam name="T">要返回的数据类型</typeparam>
        /// <param name="formDetails">动态表单明细集合</param>
        /// <param name="formDetail">给出的明细数据</param>
        /// <param name="predicate">过滤子孙数据，默认不过滤</param>
        /// <param name="selector">要返回子孙数据的属性，默认为表单明细对象</param>
        /// <returns></returns>
        List<T> GetRecursiveChildren<T>(List<DynamicFormDetailInfo> formDetails, DynamicFormDetailInfo formDetail, Func<DynamicFormDetailInfo, bool> predicate = null, Func<DynamicFormDetailInfo, T> selector = null);
    }
}