﻿namespace NursingManagement.ViewModels.HierarchicalQC
{
    public class HierarchicalQCMainTableView
    {
        /// <summary>
        /// 主题ID
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }

        /// <summary>
        /// 质控维护记录ID
        /// </summary>
        public string HierarchicalQCMainID { get; set; }

        /// <summary>
        /// 质控主记录ID
        /// </summary>
        public string HierarchicalQCRecordID { get; set; }

        /// <summary>
        /// 考核次数
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 考核日期
        /// </summary>
        public DateTime ExamineDate { get; set; }

        /// <summary>
        /// 考核人
        /// </summary>
        public string ExamineEmployee { get; set; }

        /// <summary>
        /// 考核人ID
        /// </summary>
        public List<string> ExamineEmployeeID { get; set; }

        /// <summary>
        /// 考核对象
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 质控分数
        /// </summary>
        public decimal? Point { get; set; }

        /// <summary>
        /// 是否已阅读
        /// </summary>
        public string Reader { get; set; }

        /// <summary>
        /// 是否已阅读
        /// </summary>
        public bool? IsReadFlag { get; set; }

        /// <summary>
        /// 提交状态
        /// </summary>
        public string SubmitStatus { get; set; }

        /// <summary>
        /// 被质控病区
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 质控指导意见
        /// </summary>
        public string Guidance { get; set; }

        /// <summary>
        /// 科室改进内容
        /// </summary>
        public string Improvement { get; set; }

        /// <summary>
        /// 未满分项
        /// </summary>
        public List<TrackDetail> TrackDetails { get; set; }

        /// <summary>
        /// 考核主题名称
        /// </summary>
        public string SubjectName { get; set; }

        /// <summary>
        /// 考核模板Code
        /// </summary>
        public string TemplateCode { get; set; }

        /// <summary>
        /// 考核单条内容的最大分数
        /// </summary>
        public int? ScoreThreshold { get; set; }

        /// <summary>
        /// 当天考核记录的考核人ID，现在在使用新增人员充当
        /// </summary>
        public string CurrExamineEmployeeID { get; set; }

        /// <summary>
        /// 审核状态：0、待提交||审批未通过（当AuditDateTime为空的时候），1、待审批，2、审批通过
        /// </summary>
        public string AuditStatus { get; set; }

        /// <summary>
        /// 质控开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 达标分数
        /// </summary>
        public int? MinPassingScore { get; set; }

        /// <summary>
        /// 质控结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    public class TrackDetail
    {
        /// <summary>
        /// 维护记录主键
        /// </summary>
        public string HierarchicalQCMainID { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public int HierarchyID { get; set; }

        /// <summary>
        /// 得分
        /// </summary>
        public int? Score { get; set; }

        /// <summary>
        /// 满分
        /// </summary>
        public int? FullMark { get; set; }
    }
}
