﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class AdministrationDictionaryRepository : IAdministrationDictionaryRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        public AdministrationDictionaryRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer,
            ISettingDictionaryRepository settingDictionaryRepository
        )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
            _settingDictionaryRepository = settingDictionaryRepository;
        }

 
        public async Task<List<AdministrationDictionaryInfo>> GetDictionary(AdministrationParams administrationParams)
        {
            var onlyLocal = false;
            // 取配置，决定是否只取当前医院的配置
            var onlyLocalSettingParams = new SettingDictionaryParams
            {
                SettingType = "Common",
                SettingTypeCode = "SystemSwitch",
                SettingTypeValue = "AdministrationDictionaryLocal",
            };
            var onlyLocalSetting = await _settingDictionaryRepository.GetSettingSwitch(onlyLocalSettingParams);
            if (onlyLocalSetting)
            {
                onlyLocal = true;
            }
            var datas = await GetAll();
            if (onlyLocal)
            {
                datas = datas.Where(m => !string.IsNullOrEmpty(m.LocalCode)).ToList();
            }
            if (administrationParams == null)
            {
                return datas;
            }
            if (!string.IsNullOrWhiteSpace(administrationParams.SettingTypeCode))
            {
                datas = datas.Where(m => m.SettingTypeCode == administrationParams.SettingTypeCode).ToList();
            }
            if (!string.IsNullOrWhiteSpace(administrationParams.SettingValue))
            {
                datas = datas.Where(m => m.SettingValue == administrationParams.SettingValue).ToList();
            }
            if (!string.IsNullOrWhiteSpace(administrationParams.ReferenceTypeCode))
            {
                datas = datas.Where(m => m.ReferenceTypeCode == administrationParams.ReferenceTypeCode).ToList();
            }
            if (!string.IsNullOrWhiteSpace(administrationParams.ReferenceValue))
            {
                datas = datas.Where(m => m.ReferenceValue == administrationParams.ReferenceValue).ToList();
            }
            if (!string.IsNullOrWhiteSpace(administrationParams.TypeCode))
            {
                datas = datas.Where(m => m.TypeCode == administrationParams.TypeCode).ToList();
            }
            return datas.OrderBy(m => m.Sort).ToList();
        }

        public async Task<List<AdministrationDictionaryInfo>> GetAll()
        {
            return (List<AdministrationDictionaryInfo>)await GetCacheAsync();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AdministrationDictionaryInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }
        public string GetCacheType()
        {
            return CacheType.AdministrationDictionary.GetKey(_sessionCommonServer);
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }
    }
}
