﻿namespace NursingManagement.Data.Interface
{
    public interface ICacheFactory
    {
        /// <summary>
        /// 增加CacheRepository
        /// </summary>
        /// <param name="_cacheRepository"></param>
        void Add(ICacheRepository _cacheRepository);
        /// <summary>
        /// 创建CacheRepository
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        ICacheRepository Create(string type);
        /// <summary>
        /// 获取Repository
        /// </summary>
        /// <returns></returns>
        string[] Get();
    }
}