﻿namespace NursingManagement.ViewModels
{
    public class AnnualPlanPreview
    {
        /// <summary>
        /// 主表ID
        /// </summary>
        public string AnnualPlanMainId { get; set; }
        /// <summary>
        /// 分类集合
        /// </summary>
        public List<PlanType> PlanTypes { get; set; }

        /// <summary>
        /// 分类分组
        /// </summary>
        public class PlanType
        {
            public int TypeId { get; set; }
            public List<PlanGoal> PlanGoals { get; set; }

            /// <summary>
            /// 目标分组
            /// </summary>
            public class PlanGoal
            {
                /// <summary>
                /// 目标ID
                /// </summary>
                public int GoalId { get; set; }
                /// <summary>
                /// 分类Id
                /// </summary>
                public int TypeId { get; set; }
                /// <summary>
                /// 序号
                /// </summary>
                public int Sort { get; set; }
                /// <summary>
                /// 计划分组
                /// </summary>
                public List<PlanGroup> PlanGroups { get; set; }

                public class PlanGroup
                {
                    /// <summary>
                    /// 负责部门
                    /// </summary>
                    public string[] ResponsibleDepartments { get; set; }
                    /// <summary>
                    /// 计划指标
                    /// </summary>
                    public List<PlanIndicator> PlanIndicators { get; set; }
                    /// <summary>
                    /// 计划项目
                    /// </summary>
                    public List<PlanProject> PlanProjects { get; set; }

                    public class PlanIndicator
                    {

                        /// <summary>
                        /// 显示名称
                        /// </summary>
                        public string LocalShowName { get; set; }
                        /// <summary>
                        /// 运算符
                        /// </summary>
                        public string Operator { get; set; }
                        /// <summary>
                        /// 目标值
                        /// </summary>
                        public decimal? ReferenceValue { get; set; }
                        /// <summary>
                        /// 单位
                        /// </summary>
                        public string Unit { get; set; }
                        /// <summary>
                        /// 标记ID
                        /// </summary>
                        public string MarkId { get; set; }
                        /// <summary>
                        /// 备注
                        /// </summary>
                        public string Remark { get; set; }
                        /// <summary>
                        /// 序号
                        /// </summary>
                        public int Sort { get; set; }
                    }

                    public class PlanProject
                    {
                        /// <summary>
                        /// 显示名称
                        /// </summary>
                        public string LocalShowName { get; set; }
                        /// <summary>
                        /// 标记ID
                        /// </summary>
                        public string MarkId { get; set; }
                        /// <summary>
                        /// 序号
                        /// </summary>
                        public int Sort { get; set; }
                    }
                }
            }
        }
    }
}
