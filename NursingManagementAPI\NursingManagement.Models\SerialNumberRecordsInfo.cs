﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 业务流水号记录表
    /// </summary>
    [Serializable]
    [Table("SerialNumberRecords")]
    public class SerialNumberRecordsInfo : MutiModifyInfo
    {
        /// <summary>
        /// 业务分类细类码，可查找到唯一流水号
        /// </summary>
        [Key]
        public string BizCategoryCode { get; set; }
        /// <summary>
        /// 系统目前最大使用号码
        /// </summary>
        public string SerialNumber { get; set; }
    }
}
