﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models.Examine;

namespace NursingManagement.Data.Repository
{
    public class ExaminationDetailRepository : IExaminationDetailRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContex;
        public ExaminationDetailRepository(
              NursingManagementDbContext nursingManagementDbContex
            )
        {
            _nursingManagementDbContex = nursingManagementDbContex;
        }
        /// <summary>
        /// 根据MainID获取明细记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<List<ExaminationDetailInfo>> GetListByMainIDAsNoTracking(string mainID)
        {
            return await _nursingManagementDbContex.ExaminationDetailInfos.AsNoTracking().Where(m => m.ExaminationMainID == mainID && m.StatusCode != "1" && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据MainID获取明细记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<List<ExaminationDetailInfo>> GetListByMainID(string mainID)
        {
            return await _nursingManagementDbContex.ExaminationDetailInfos.Where(m => m.ExaminationMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据MainID集合获取所有状态明细记录数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        public async Task<List<ExaminationDetailInfo>> GetAllListByMainIDsAsNoTracking(List<string> mainIDs)
        {
            return await _nursingManagementDbContex.ExaminationDetailInfos.AsNoTracking().Where(m => mainIDs.Any(n => n == m.ExaminationMainID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
