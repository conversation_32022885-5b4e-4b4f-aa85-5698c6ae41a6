﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API
{
    /// <summary>
    /// 培训学员管理 控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/TrainingLearner")]
    [EnableCors("any")]
    public class TrainingLearnerController : Controller
    {

        private readonly ISessionService _session;
        private readonly ITrainingLearnerService _trainingLearnerService;
        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="session"></param>
        /// <param name="trainingLearnerService"></param>
        public TrainingLearnerController(
            ISessionService session,
            ITrainingLearnerService trainingLearnerService)
        {
            _session = session;
            _trainingLearnerService = trainingLearnerService;
        }

        /// <summary>
        /// 获取学员信息列表
        /// </summary>
        /// <param name="trainingRecordID">查询条件</param>
        /// <returns>学员信息列表</returns>
        [HttpGet]
        [Route("GetTraineeList")]
        public async Task<IActionResult> GetTraineeList(string trainingRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            Request.HttpContext.GetRouteData();
            result.Data = await _trainingLearnerService.GetTraineeList(trainingRecordID, session);
            result.Sucess();
            return result.ToJson();
        }

        /// <summary>
        /// 更新学员信息
        /// </summary>
        /// <param name="trainingLearnerInfo">更新的人员培训信息</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        [Route("UpdateTraineeRecord")]
        public async Task<IActionResult> UpdateTraineeRecord([FromBody] TrainingLearnerInfo trainingLearnerInfo)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _trainingLearnerService.UpdateTraineeRecordAsync(trainingLearnerInfo, session.EmployeeID);
            result.Sucess();
            return result.ToJson();
        }
        /// <summary>
        /// 删除学员记录
        /// </summary>
        /// <param name="trainingLearnerID">人员培训记ID</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        [Route("DeleteTrainee")]
        public async Task<IActionResult> DeleteTrainee([FromForm] string trainingLearnerID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _trainingLearnerService.DeleteTraineeAsync(trainingLearnerID, session.EmployeeID);
            result.Sucess();
            return result.ToJson();
        }

        /// <summary>
        /// 设置班长
        /// </summary>
        /// <param name="setMonitorParamsView">是否设置人员为班长参数</param>
        /// <returns>设置结果</returns>
        [HttpPost]
        [Route("SetTraineeMonitor")]
        public async Task<IActionResult> SetTraineeMonitor([FromBody] TrainingLearnerParamView setMonitorParamsView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _trainingLearnerService.SetTraineeMonitorAsync(setMonitorParamsView, session.EmployeeID);
            result.Sucess();
            return result.ToJson();
        }

        /// <summary>
        /// 获取本人培训时间线
        /// </summary>
        /// <param name="timelineSearchParamsView">时间线查询参数</param>
        /// <returns>培训时间线</returns>
        [HttpPost]
        [Route("GetTrainTimeline")]
        public async Task<IActionResult> GetTrainTimeline([FromBody] TrainingLearnerParamView timelineSearchParamsView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _trainingLearnerService.GetTrainTimelineAsync(timelineSearchParamsView);
            result.Sucess();
            return result.ToJson();
        }
        /// <summary>
        /// 保存课程评价及课程建议
        /// </summary>
        /// <param name="recommendationsView">培训记录建议及满意度信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveCourseRecommendations")]
        public async Task<IActionResult> SaveCourseRecommendations([FromBody] TrainingLearnerRecommendationsParamsView recommendationsView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingLearnerService.SaveCourseRecommendations(recommendationsView, session.EmployeeID);
            result.Sucess();
            return result.ToJson();
        }
        /// <summary>
        /// 培训二维码扫描签到
        /// </summary>
        /// <param name="trainingRecordID">培训主记录ID</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <returns></returns>
        [HttpGet]
        [Route("TrainingLearnerSignIn")]
        public async Task<IActionResult> TrainingLearnerSignIn(string trainingRecordID, long timeStamp)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingLearnerService.TrainingLearnerSignIn(trainingRecordID, timeStamp, session.EmployeeID);
            return result.ToJson();
        }
    }
}
