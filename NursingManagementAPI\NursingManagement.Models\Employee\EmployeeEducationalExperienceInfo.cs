﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员教育经历表
    /// </summary>
    [Serializable]
    [Table("EmployeeEducationalExperience")]
    public class EmployeeEducationalExperienceInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeEducationalExperienceID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 学历(GB/T 4658-2006)
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string EducationCode { get; set; }

        /// <summary>
        /// 毕业院校
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string GraduateSchool { get; set; }

        /// <summary>
        /// 毕业专业
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string GraduationMajor { get; set; }

        /// <summary>
        /// 入学日期
        /// </summary>
        public DateTime? EntryDate { get; set; }

        /// <summary>
        /// 毕业日期
        /// </summary>
        public DateTime? GraduationDate { get; set; }

        /// <summary>
        /// 状态(10:毕业、20、在读)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string EducationStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string Remark { get; set; }

        /// <summary>
        /// 特殊标记：第一学历First(F)、最高学历Highest(H)
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string SpecialFlag { get; set; }
    }
}