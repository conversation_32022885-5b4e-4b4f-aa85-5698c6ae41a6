﻿namespace NursingManagement.ViewModels
{
    public class EmployeeResignationView
    {
        /// <summary>
        /// 员工编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 部门编号
        /// </summary>
        public string DepartmentID { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 员工性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 职务
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 申请离职日期
        /// </summary>
        public DateTime? ResignationApplyDate { get; set; }
        /// <summary>
        /// 离职日期
        /// </summary>
        public DateTime? ResignationDate { get; set; }
    }
}
