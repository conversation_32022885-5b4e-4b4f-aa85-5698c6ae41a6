﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DepartmentPostSettingRepository : IDepartmentPostSettingRepository
    {

        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public DepartmentPostSettingRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer
            )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 从数据库获取数据
        /// </summary>
        /// <param name="postID"></param>
        /// <param name="departmentID"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<DepartmentPostSettingInfo> GetRecordByKeysAsync(int postID, int departmentID,string type)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _nursingManagementDbContext.DepartmentPostSettingInfos.Where(m => m.PostID == postID && m.DepartmentID == departmentID && m.HospitalID == hospitalID && m.DeleteFlag != "*" && m.Type == type).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取数据，包含已删除的
        /// </summary>
        /// <param name="postID"></param>
        /// <param name="departmentID"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<DepartmentPostSettingInfo> GetRecordIncludeDelete(int postID, int departmentID, string type)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _nursingManagementDbContext.DepartmentPostSettingInfos.Where(m => m.PostID == postID && m.DepartmentID == departmentID && m.HospitalID == hospitalID && m.Type == type).FirstOrDefaultAsync();
        }

        public async Task<List<DepartmentPostSettingInfo>> GetAllCacheAsync()
        {
            var list = await GetCacheAsync() as List<DepartmentPostSettingInfo>;
            return list;
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DepartmentPostSettingInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });

            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType) => GetCacheType() == cacheType;

        public string GetCacheType() => CacheType.DepartmentPostSetting.GetKey(_sessionCommonServer);
    }
}
