﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 试卷筛选题目条件规则view
    /// </summary>
    public class PaperFilterQuestionConditionRuleView
    {
        /// <summary>
        /// 组卷分数
        /// </summary>
        public decimal Score { get; set; }

        /// <summary>
        /// 每个题库对应的组卷规则
        /// </summary>
        public List<QuestionFilterConditionRule> QuestionFilterConditionRules { get; set; }

        /// <summary>
        /// 必选题目ID集合
        /// </summary>
        public List<int> RequireQuestion { get; set; }

        /// <summary>
        /// 难易程度规则
        /// </summary>
        public List<RuleKeyValue> DifficultyRule { get; set; }

        #region 组卷相关Setting

        /// <summary>
        /// 组卷题型集合
        /// </summary>
        public List<string> QustionTypes { get; set; }

        /// <summary>
        /// 试卷题目数量
        /// </summary>
        public int PaperQuestionCount { get; set; }

        /// <summary>
        /// 难易程度数量
        /// </summary>
        public Dictionary<string, decimal> DifficultyPercentage { get; set; }

        #endregion
    }

    public class QuestionFilterConditionRule
    {
        /// <summary>
        /// 题库ID
        /// </summary>
        public string QuestionBankID { get; set; }

        /// <summary>
        /// 題目过滤条件
        /// </summary>
        public List<QuestionFilter> QuestionFilters { get; set; }

        /// <summary>
        /// 选择的题库中的题目数量 暂未使用********
        /// </summary>
        public int SelectBankQuestionCount { get; set; }
    }

    public class QuestionFilter
    {
        /// <summary>
        /// 题型
        /// </summary>
        public string QuestionType { get; set; }

        /// <summary>
        /// 每个题型的规则
        /// </summary>
        public List<RuleKeyValue> RuleKeyValues { get; set; }

        #region 存储动态组卷时的计算数据

        /// <summary>
        /// 总题目数量
        /// </summary>
        public int QuestionCount { get; set; }

        /// <summary>
        /// 动态题目数量 从题库中排除必选题后需要筛选的题型数量
        /// </summary>
        public int DynamicFilterCount { get; set; }

        /// <summary>
        /// 此类题型题目分数
        /// </summary>
        public Decimal Score { get; set; }

        /// <summary>
        /// 题目难易程度数量 暂未使用********
        /// </summary>
        public Dictionary<string, int> QuestionDifficultyCounts { get; set; } = [];

        /// <summary>
        /// 当前题库当前题型中包含的必选题 TODO 动态组卷排除必选题
        /// </summary>
        public Dictionary<string, List<int>> QuestionFilterRequireQuestions { get; set; } = [];

        #endregion
    }

    public class RuleKeyValue
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public int RuleListID { get; set; }

        /// <summary>
        /// 具体的规则编码
        /// </summary>
        public string RuleCode { get; set; }

        /// <summary>
        /// 规则的值
        /// </summary>
        public int Value { get; set; }
    }
}
