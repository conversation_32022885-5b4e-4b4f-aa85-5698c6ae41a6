﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 考勤表相关逻辑
    /// </summary>
    public interface IAttendanceService
    {
        /// <summary>
        /// 检核考勤是否被编辑过
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns>true:已编辑过；false:未被编辑过</returns>
        Task<bool> CheckAttendanceEdit(int departmentID, int attendanceYear, int attendanceMonth);

        /// <summary>
        /// 生成考勤表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<bool> CreateAttendanceRecord(int departmentID, int attendanceYear, int attendanceMonth, string employeeID, string hospitalID);

        /// <summary>
        /// 获取部门指定月份的考勤表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <param name="employeeID"></param>
        /// <param name="sortFlag"></param>
        /// <returns></returns>
        Task<AttendanceView> GetAttendanceDatas(int departmentID, int attendanceYear, int attendanceMonth, string employeeID,bool sortFlag);
        /// <summary>
        /// 保存手动调整的考勤数据
        /// </summary>
        /// <param name="attendanceView"></param>
        /// <returns></returns>
        Task<bool> SaveAttendance(AttendanceView attendanceView);
        /// <summary>
        /// 同步考勤信息到OA
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        Task<bool> SyncAttendanceData(int departmentID, int attendanceYear, int attendanceMonth, string employeeID);
        /// <summary>
        /// 获取部门考勤状态
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        Task<string> GetAttendanceStatus(int departmentID, int attendanceYear, int attendanceMonth);
        /// <summary>
        /// 提交审核
        /// </summary>
        /// <param name="departmentID">病区主键</param>
        /// <param name="attendanceYear">排班年份</param>
        /// <param name="attendanceMonth">排班月份</param>
        /// <param name="proveCategory">审批类型</param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        Task<SaveReponseView> AttendanceApproval(int departmentID, int attendanceYear, int attendanceMonth,  string proveCategory, string employeeID, string hospitalID);
        /// <summary>
        /// 取消考勤审核
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        Task<bool> CancelAttendanceApproval(int departmentID, int attendanceYear, int attendanceMonth, string employeeID);
        /// <summary>
        /// 创建审批流程实体参数
        /// </summary>
        /// <param name="data"></param>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        Task<bool> CreateAttendanceApproval(AttendanceApproveRecordInfo data, string proveCategory);
    }
}
