﻿using NursingManagement.Common;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 剩余休假天数服务层
    /// </summary>
    public interface IRemainingRestDaysService
    {
        /// <summary>
        /// 获取剩余休假天数集合
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<TableView> GetRemainingRestDaysData(int year, int departmentID);
        /// <summary>
        /// 保存剩余休息日数据
        /// </summary>
        /// <param name="saveViews">保存数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> SaveRemainingRestDaysData(List<RemainingRestDaysView> saveViews, Session session); 
    }
}
