﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AdjustScheduleRecordRepository : IAdjustScheduleRecordRepository
    {
        private readonly NursingManagementDbContext _context;

        public AdjustScheduleRecordRepository(NursingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<AdjustScheduleRecordInfo> GetRecordByIDAsync(string id)
        {
            return await _context.AdjustScheduleRecordInfos.FirstOrDefaultAsync(x => x.AdjustScheduleRecordID == id && x.DeleteFlag != "*");
        }

        public async Task<List<AdjustScheduleRecordInfo>> GetListByDepartmentIDAsync(int departmentID, bool asNoTrack = false)
        {
            var datas = _context.AdjustScheduleRecordInfos.Where(x => x.DepartmentID == departmentID && x.DeleteFlag != "*");
            if (asNoTrack)
            {
                return await datas.AsNoTracking().ToListAsync();
            }
            return await datas.ToListAsync();
        }

        public async Task<List<AdjustScheduleRecordInfo>> GetListByEmployeeIDAsync(string employeeID, bool asNoTrack)
        {
            var datas = _context.AdjustScheduleRecordInfos.Where(x => x.AddEmployeeID == employeeID && x.DeleteFlag != "*");
            if (asNoTrack)
            {
                return await datas.AsNoTracking().ToListAsync();
            }
            return await datas.ToListAsync();
        }

        public async Task<List<AdjustScheduleRecordInfo>> GetRecordsByIDsAsNoTrackAsync(List<string> recordIDs)
        {
            return await  _context.AdjustScheduleRecordInfos.AsNoTracking().Where(x => 
            recordIDs.Contains(x.AdjustScheduleRecordID) && x.DeleteFlag != "*").ToListAsync();
        }
    }
}
