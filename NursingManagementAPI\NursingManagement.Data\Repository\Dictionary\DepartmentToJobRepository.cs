﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DepartmentToJobRepository : IDepartmentToJobRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public DepartmentToJobRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 根据职务编号获取科室+职务的拼接名称
        /// </summary>
        /// <param name="jobCode">职务编号</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<string> GetJobAndDepartmentNameByJobCode(string jobCode, int? departmentID)
        {
            var data = await this.GetAll<DepartmentToJobInfo>();
            var result = data.Where(m => m.JobCode == jobCode)
                .IfWhere(departmentID.HasValue, m => m.DepartmentID == departmentID.Value)
                .Select(m => $"{m.ShowJobName}（{m.HRDepartmentName}）").FirstOrDefault();
            return result;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var data = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DepartmentToJobInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return data;
        }

        public string GetCacheType()
        {
            return CacheType.DepartmentToJob.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
    }
}
