﻿using Microsoft.AspNetCore.Http;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IFileService
    {
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="formFile"></param>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<FileUploadReturnView> UpLoadFile(IFormFile formFile, DocumentView view);
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="formFile"></param>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<(string fileID, string errMessage)> UpLoadFileAsync(IFormFile formFile, FileUploadView view);
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="fileID">文件唯一标识</param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteFileAsync(string fileID, string employeeID);
        /// <summary>
        /// 根据文件分类与文件来源获取文件集合
        /// </summary>
        /// <param name="fileClass">文件分类；1：公共文件，2：消息附件</param>
        /// <param name="sourceID">文件来源ID</param>
        /// <returns></returns>
        Task<List<FileView>> GetFileListByClassAndSourceAsync(string fileClass, string sourceID);

        /// <summary>
        /// 根据文件唯一ID获取远端文件系统重的文件访问信息
        /// </summary>
        /// <param name="fileIDs"></param>
        /// <returns></returns>
        Task<List<DocumentMainView>> GetFileAccessInfosFromFileSystemAsync(List<string> fileIDs);
        /// <summary>
        /// 删除远端文件服务器上的文件
        /// </summary>
        /// <param name="documentID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        Task<bool> DeleteFileOfRemoteServer(string documentID, string userID);
        /// <summary>
        /// 上传文件并返回上传结果信息
        /// </summary>
        /// <param name="file"></param>
        /// <param name="employeeID">操作人工号</param>
        /// <param name="employeeName">操作人名称</param>
        /// <returns></returns>
        Task<FileUploadReturnView> UploadRichTextFile(IFormFile file, string employeeID, string employeeName);
    }
}
