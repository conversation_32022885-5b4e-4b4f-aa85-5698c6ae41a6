﻿using System.Reflection;
using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModel;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class SensitiveIndicatorSupervisionService(
              IUnitOfWork unitOfWork
            , IDynamicTableSettingService dynamicTableSettingService
            , ISettingDictionaryRepository settingDictionaryRepository
            , IHierarchicalQCService hierarchicalQCService
            , IHierarchicalQCFormRepository hierarchicalQCFormRepository
            , IPatientProfileDetailRepository profileDetailRepository
            , IPatientProfileRecordRepository profileRecordRepository
            , IHierarchicalQCSubjectRepository hierarchicalQCSubjectRepository
            , IDepartmentListRepository departmentListRepository
            , IRequestApiService requestApiService
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IHierarchicalQCAssessListRepository hierarchicalQCAssessListRepository
            ) : ISensitiveIndicatorSupervisionService
    {
        #region
        /// <summary>
        /// 日志
        /// </summary>
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 3科室类型
        /// </summary>
        private const string ORGANIZATIONTYPE_3 = "3";
        /// <summary>
        /// 1科室类型
        /// </summary>
        private const string ORGANIZATIONTYPE_1 = "1";
        /// <summary>
        /// 督导类型导管
        /// </summary>
        private const string SUPERVISIONTYPE_1 = "1";
        /// <summary>
        /// 督导类型导管
        /// </summary>
        private const string TABLESUBTYPEKEY_TUBE = "tube";
        /// <summary>
        /// 督导类型风险
        /// </summary>
        private const string TABLESUBTYPEKEY_RISK = "risk";
        /// <summary>
        /// 表格类型
        /// </summary>
        private const string TABLETYPE = "PatientCondition";
        /// <summary>
        /// 指标督导
        /// </summary>
        private const string SOURCETYPE_INDICATORSUPERVISION = "IndicatorSupervision";
        /// <summary>
        /// 关联表名
        /// </summary>
        private const string RELATEDTABLENAME = "HierarchicalQCRecord";
        /// <summary>
        /// 极端督导率的合格ID
        /// </summary>
        private const int HierarchicalQCAssessListID_100229 = 100229;
        #endregion


        /// <summary>
        /// 获取督导记录
        /// </summary>
        /// <param name="supervisionQuery">查询督导记录的view</param>
        /// <returns></returns>
        public async Task<VisitsTableView> GetSensitiveIndicatorSupervisionRecord(SupervisionQueryView supervisionQuery)
        {
            var result = new VisitsTableView 
            {
                TableData = [],
                TableHeader = [] 
            };
            try
            {
                var (headerSuccess, headers) = await TryGetTableHeaders(supervisionQuery);
                if (!headerSuccess) 
                {
                    return result;
                }
                result.TableHeader = headers;
                var departmentMapping = await departmentListRepository.GetNMDepartmentByHisDepartment(ORGANIZATIONTYPE_3,ORGANIZATIONTYPE_1,supervisionQuery.HospitalID);
                var cccData = await GetCCCPatientSupervisionRecord(supervisionQuery, departmentMapping);
                if (cccData.Count == 0) 
                {
                    return result;
                } 
                var (templateCode, qcFormID) = await GetQcTemplateInfo(supervisionQuery.SupervisionType);
                var sourceIDs = cccData.Where(x => x.SourceType == SOURCETYPE_INDICATORSUPERVISION).Select(x => x.SourceID).ToList();
                var recordIDs = await profileRecordRepository.GetRelatedTableRecordIDList(SOURCETYPE_INDICATORSUPERVISION,sourceIDs,RELATEDTABLENAME);
                result.TableData = await ConvertToDictionaryList(cccData,templateCode,qcFormID,recordIDs,departmentMapping,GetDenominator(supervisionQuery.SupervisionType)
                );
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "敏感指标督导记录获取失败");
            }

            return result;
        }
        /// <summary>
        /// 获取表格头配置
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        private async Task<(bool Success, List<DynamicTableHeaderView> Headers)> TryGetTableHeaders(SupervisionQueryView query)
        {
            var tableSubType = (query.SupervisionType == SUPERVISIONTYPE_1 ? TABLESUBTYPEKEY_TUBE : TABLESUBTYPEKEY_RISK) + "-Record";
            var headerQuery = new DynamicTableHeaderQueryView
            {
                TableType = TABLETYPE,
                TableSubType = tableSubType,
                HospitalID = query.HospitalID
            };

            var headers = await dynamicTableSettingService.GetDynamicTableHeader(headerQuery);
            if (headers?.Count > 0) return (true, headers);

            _logger.Error("动态表格配置缺失 {@HeaderQuery}", headerQuery);
            return (false, new List<DynamicTableHeaderView>());
        }

        /// <summary>
        /// 获取分母值
        /// </summary>
        /// <param name="supervisionType">督导类型</param>
        /// <returns></returns>
        private static int GetDenominator(string supervisionType) => supervisionType == SUPERVISIONTYPE_1 ? 14 : 15;

        /// <summary>
        /// 获取督导模板信息
        /// </summary>
        /// <param name="supervisionType"></param>
        /// <returns></returns>
        private async Task<(string TemplateCode, int QcFormID)> GetQcTemplateInfo(string supervisionType)
        {
            var qcFormID = await settingDictionaryRepository.GetSettingValue(new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "SupervisionType",
                FilterSettingTypeValue = supervisionType
            });

            if (string.IsNullOrEmpty(qcFormID) || !int.TryParse(qcFormID, out var formId))
                return (string.Empty, 0);

            var formInfo = await hierarchicalQCFormRepository.GetFormByID(formId);
            return (formInfo?.TemplateCode ?? string.Empty, formId);
        }
        /// <summary>
        /// 转换为字典列表
        /// </summary>
        /// <param name="supervisionDatas">ccc获取到的数据</param>
        /// <param name="templateCode">模板编码</param>
        /// <param name="qcFormID">质控表单ID</param>
        /// <param name="isSaveQcRecordIDList">存在督导记录的数据</param>
        /// <param name="departmentMapping">病区关系映射字典</param>
        /// <param name="denominator">极端督导率的分母</param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> ConvertToDictionaryList(List<SupervisionDataView> supervisionDatas, string templateCode, int qcFormID
            , List<Dictionary<string, string>> isSaveQcRecordIDList, List<Dictionary<string, object>> departmentMapping, int denominator)
        {
            var dictList = new List<Dictionary<string, object>>();
            var profileViews = await profileRecordRepository.GetRecordDataBySourceIDAndType(isSaveQcRecordIDList.Select(x => x["sourceID"]).ToList(), SOURCETYPE_INDICATORSUPERVISION, RELATEDTABLENAME);
            foreach (var supervisionData in supervisionDatas)
            {
                var dict = new Dictionary<string, object>();
                foreach (PropertyInfo prop in supervisionData.GetType().GetProperties())
                {
                    dict[StringExtension.FirstLetterToLowerCase(prop.Name)] = prop.GetValue(supervisionData, null);
                }
                var nmDepartments = departmentMapping.FirstOrDefault(m => m["stationID"].ToString() == supervisionData.StationID.ToString());
                foreach (var nmDepartmet in nmDepartments)
                {
                    dict[nmDepartmet.Key] = nmDepartmet.Value;
                }
                dict["templateCode"] = templateCode;
                dict["superVisionFormID"] = qcFormID;
                dict["admissionDateTime"] = supervisionData.AdmissionDateTime.ToString("yyyy-MM-dd HH:mm");
                dict["tubeStartDateTime"] = supervisionData.TubeStartDateTime.ToString("yyyy-MM-dd HH:mm");
                (dict["hierarchicalQCSubjectID"], dict["hierarchicalQCFormID"], dict["isSensitiveFlag"], dict["lastPassRate"]) = GetAboutSuccessRecord(isSaveQcRecordIDList, supervisionData.SourceID, profileViews, denominator);
                dict["supervisionDetails"] = CreatePatientProfileDetailViews(dict);
                dictList.Add(dict);
            }
            return dictList;
        }
        /// <summary>
        /// 获取相关成功记录
        /// </summary>
        /// <param name="isSaveQcRecordIDList">成功记录集合</param>
        /// <param name="sourceID">来源ID</param>
        /// <param name="profileViews">元数据</param>
        /// <param name="denominator">分母</param>
        /// <returns></returns>
        private static (string, string,bool,string) GetAboutSuccessRecord(List<Dictionary<string, string>> isSaveQcRecordIDList,string sourceID, List<SensitiveRecordView> profileViews, int denominator)
        {
            var successRecord = isSaveQcRecordIDList.LastOrDefault(m => m["sourceID"] == sourceID);
            return successRecord == null 
                ? (string.Empty, string.Empty, false, string.Empty) 
                : (successRecord?["hierarchicalQCSubjectID"] ?? "", successRecord?["hierarchicalQCFormID"] ?? "", successRecord != null, ComputePassRate(profileViews, successRecord["sourceID"], denominator));
        }
        /// <summary>
        /// 计算通过率
        /// </summary>
        /// <param name="profileViews"></param>
        /// <param name="sourceID"></param>
        /// <param name="denominator"></param>
        /// <returns></returns>
        private static string ComputePassRate(List<SensitiveRecordView> profileViews,string sourceID, int denominator)
        {
            var singleProfileViews = profileViews.Where(m => m.SourceID == sourceID).ToList();
            if (singleProfileViews.Count > 0)
            {
                var group = singleProfileViews.GroupBy(m => m.PatientProfileRecordID).ToList().Find(m => m.Key == singleProfileViews.OrderBy(m => m.SupervisionRecordTime).LastOrDefault().PatientProfileRecordID).ToList();
                var passRate = (decimal)group.Count(m => m.HierarchicalQCAssessListID == HierarchicalQCAssessListID_100229);
                return passRate.ToString("0.##") + "%";
            }
            return string.Empty;
        }
        /// <summary>
        /// 创建患者状况明细视图
        /// </summary>
        /// <param name="dict">明细字典</param>
        /// <returns></returns>
        private static List<PatientProfileDetailView> CreatePatientProfileDetailViews(Dictionary<string, object> dict)
        {
            return [
                    new()
                    {
                        DataID = "riskContent",
                        DataValue = dict["riskContent"]== null ? "" : dict["riskContent"].ToString(),
                        OccurDateTime = dict["riskAssessTime"]== null ? DateTime.Now : (DateTime)dict["riskAssessTime"]
                    },
                    new()
                    {
                        DataID = "riskColor",
                        DataValue = dict["riskColor"]== null ? "" : dict["riskColor"].ToString(),
                        OccurDateTime = dict["riskAssessTime"]== null ? DateTime.Now : (DateTime)dict["riskAssessTime"]
                    },
                    new ()
                    {
                        DataID = "riskAssessTime",
                        DataValue = dict["riskAssessTime"]== null ? "" : dict["riskAssessTime"].ToString(),
                        OccurDateTime = dict["riskAssessTime"]== null ? DateTime.Now : (DateTime)dict["riskAssessTime"]
                    },
                    new ()
                    {
                        DataID = "nurseName",
                        DataValue = dict["nurseName"]== null ? "" : dict["nurseName"].ToString(),
                        OccurDateTime = dict["riskAssessTime"]== null ? DateTime.Now : (DateTime)dict["riskAssessTime"]
                    },
                ];
        }
        /// <summary>
        /// 获取CCC患者数据
        /// </summary>
        /// <param name="supervisionQuery"></param>
        /// <param name="deparmentMapping"></param>
        /// <returns></returns>
        private async Task<List<SupervisionDataView>> GetCCCPatientSupervisionRecord(SupervisionQueryView supervisionQuery, List<Dictionary<string, object>> deparmentMapping)
        {
            var returnView = new List<SupervisionDataView>();
            supervisionQuery.StationIDs ??= [];
            var stationIDs = deparmentMapping.Where(m => supervisionQuery.StationIDs.Contains(Convert.ToInt32(m["nmDepartmentID"]))).Select(m => m["stationID"].ToString()).ToList();
            if (stationIDs.Count > 0)
            {
                foreach (var stationID in stationIDs)
                {
                    if (int.TryParse(stationID, out var stationIDInt))
                    {
                        supervisionQuery.StationIDs.Add(stationIDInt);
                    }
                }
            }
            object result;
            try
            {
                result = await requestApiService.RequestAPI("GetSupervisionList", ListToJson.ToJson(supervisionQuery));
            }
            catch (Exception ex)
            {
                _logger.Error(ex + "获取CCC患者数据失败 参数||" + ListToJson.ToJson(supervisionQuery));
                return returnView;
            }
            string stringResult = result == null ? "" : result.ToString();
            if (string.IsNullOrEmpty(stringResult))
            {
                _logger.Error("获取CCC患者数据失败 参数||" + ListToJson.ToJson(supervisionQuery));
                return returnView;
            }
            var response = ListToJson.ToList<ResponseResult>(stringResult);
            if (response.Data != null)
            {
                returnView = ListToJson.ToList<List<SupervisionDataView>>(response.Data.ToString());
            }
            return returnView;
        }
        /// <summary>
        /// 获取督导记录详情
        /// </summary>
        /// <param name="profileID"></param>
        /// <param name="qcMainID"></param>
        /// <returns></returns>
        public async Task<FormTemplateView> GetSensitiveQcAssessViewAsync(string profileID, string qcMainID)
        {
            var returnView = new FormTemplateView();
            if (string.IsNullOrEmpty(profileID))
            {
                return returnView;
            };
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "SupervisionType",
                SettingTypeValue = profileID
            };
            var superVisionFormID = await settingDictionaryRepository.GetSettingValue(settingParams);
            if (string.IsNullOrEmpty(superVisionFormID))
            {
                _logger.Error("未找到访视settingDictionary配置 profileID||" + profileID);
                return returnView;
            }
            var qcFormInfo = await hierarchicalQCFormRepository.GetFormByID(Convert.ToInt32(superVisionFormID));
            if (qcFormInfo == null)
            {
                _logger.Error("未找到访视hierarchicalQCForm配置 superVisionFormID||" + superVisionFormID);
                return returnView;
            }
            return await hierarchicalQCService.GetAssessContentView(qcMainID, qcFormInfo.TemplateCode, false, false);
        }
        /// <summary>
        /// 保存督导记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> SaveSensitiveRecordAsync(SupervisionRecordSaveView view)
        {
            if (view == null || view.SupervisionRecord == null || view.QCMainAndDetail == null)
            {
                _logger.Error("危重患者访视保存失败！ 保存参数有误！");
                return false;
            }
            var sensitiveFormID = view.SupervisionFormID;
            var relatedTableRecordID = view.SupervisionRecord.RelatedTableRecordID;
            //每月首次新增自动添加本片区主题
            if (string.IsNullOrEmpty(relatedTableRecordID))
            {
                view.QCMainAndDetail.QcSubjectID = await SaveSensitiveSubject(view, sensitiveFormID);
            }
            // 质控记录保存
            relatedTableRecordID = await hierarchicalQCService.SaveHierarchicalQCMainAndDetailsAsync(view.QCMainAndDetail, view.HospitalID, view.EmployID, view.NurseEmployeeID);
            // 患者状况主记录及明细记录保存
            return await SaveSensitiveRecordAndDetail(view, relatedTableRecordID);
        }
        /// <summary>
        /// 保存督导主题
        /// </summary>
        /// <param name="view"></param>
        /// <param name="sensitiveFormID"></param>
        /// <returns></returns>
        public async Task<string> SaveSensitiveSubject(SupervisionRecordSaveView view, int sensitiveFormID)
        {
            var qcDate = view.QCMainAndDetail.QcMain.ExamineDate;
            var yearMonth = qcDate.ToString("yyyy-MM");
            var qcSubject = await hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectData("2", sensitiveFormID, yearMonth, view.UpNMDepartmetID);
            if (qcSubject.Count > 0)
            {
                return qcSubject.FirstOrDefault().HierarchicalQCSubjectID;
            }
            var qcForm = await hierarchicalQCFormRepository.GetFormByID(sensitiveFormID);
            if (qcForm == null)
            {
                _logger.Error("危重患者访视保存失败！未找到对应hierarchicalQCForm  hierarchicalQCFormID||" + sensitiveFormID);
                return "";
            }
            var subjectSaveView = new SubjectView()
            {
                HospitalID = view.HospitalID,
                Language = view.Language,
                HierarchicalQCFormID = sensitiveFormID,
                FormName = qcForm.FormName,
                HierarchicalQCFormLevel = qcForm.HierarchicalQCFormLevel,
                StartDate = DateHelper.GetFirstDayOfMonth(qcDate),
                EndDate = DateHelper.GetLastDayOfMonth(qcDate),
                DepartmentID = view.UpNMDepartmetID,
                EmployeeID = view.EmployID,
                FormType = qcForm.FormType,
                TemplateCode = qcForm.TemplateCode,
                YearMonth = yearMonth,
            };
            return await hierarchicalQCService.AddSubjectPlan(subjectSaveView);
        }
        /// <summary>
        /// 患者状况主记录及明细记录保存
        /// </summary>
        /// <param name="view"></param>
        /// <param name="relatedTableRecordID"></param>
        /// <returns></returns>
        public async Task<bool> SaveSensitiveRecordAndDetail(SupervisionRecordSaveView view, string relatedTableRecordID)
        {
            var profileRecord = await profileRecordRepository.GetRecordByRelatedTableID(RELATEDTABLENAME, view.SupervisionRecord.RelatedTableRecordID);
            var addFlag = profileRecord == null;
            if (addFlag)
            {
                profileRecord = view.SupervisionRecord;
                profileRecord.PatientProfileRecordID = profileRecord.GetId();
                profileRecord.RelatedTableName = RELATEDTABLENAME;
                profileRecord.RelatedTableRecordID = relatedTableRecordID;
                profileRecord.HospitalID = view.HospitalID;
                profileRecord.Add(view.EmployID);
                profileRecord.Modify(view.EmployID);
                profileRecord.DeleteFlag = "";
                await unitOfWork.GetRepository<PatientProfileRecordInfo>().InsertAsync(profileRecord);
            }
            else
            {
                profileRecord.Modify(view.EmployID);
                var profileDetails = await profileDetailRepository.GetDataByPatientProfileRecordID(profileRecord.PatientProfileRecordID);
                if (profileDetails.Count > 0)
                {
                    profileDetails.ForEach(x => x.Modify(view.EmployID));
                }
            }
            if ((view.SupervisionDetails?.Count ?? 0) > 0)
            {
                view.SupervisionDetails.ForEach(m =>
                {
                    m.PatientProfileRecordID = profileRecord.PatientProfileRecordID;
                    m.Add(view.EmployID);
                    m.Modify(view.EmployID);
                    m.PatientProfileDetailID = m.GetId();
                });
                await unitOfWork.GetRepository<PatientProfileDetailInfo>().InsertAsync(view.SupervisionDetails);
            };
            return await unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 获取访视记录
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <param name="supervisionType"></param>
        /// <param name="templateCode"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<VisitsTableView> GetSensitiveQcRecordAsync(string sourceID, string sourceType, string supervisionType, string templateCode, string hospitalID)
        {
            var returnList = new VisitsTableView()
            {
                TableData = [],
                TableHeader = []
            };
            if (string.IsNullOrEmpty(sourceID) || string.IsNullOrEmpty(sourceType) || string.IsNullOrEmpty(supervisionType))
            {
                _logger.Error("获取访视质控记录失败！入参有误！");
                return returnList;
            }
            var dynamicTableHeaderQueryView = new DynamicTableHeaderQueryView()
            {
                TableType = TABLETYPE,
                TableSubType = (supervisionType == SUPERVISIONTYPE_1 ? TABLESUBTYPEKEY_TUBE : TABLESUBTYPEKEY_RISK) + "-Main",
                HospitalID = hospitalID
            };
            returnList.TableHeader = await dynamicTableSettingService.GetDynamicTableHeader(dynamicTableHeaderQueryView);
            if (returnList.TableHeader.Count == 0)
            {
                _logger.Error("动态表格配置未找到！dynamicTableHeaderQueryView||" + ListToJson.ToJson(dynamicTableHeaderQueryView));
                return returnList;
            }
            var profileViews = await profileRecordRepository.GetRecordDataBySourceIDAndType([sourceID], sourceType, RELATEDTABLENAME);
            if (profileViews.Count == 0)
            {
                return returnList;
            }
            var departments = await departmentListRepository.GetAllDictAsync();
            var users = await employeePersonalDataRepository.GetIDAndNameData();
            var settings = await hierarchicalQCAssessListRepository.GetAssessDictionaries();

            var settingParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "SupervisionDetail",
            };
            var dynamicMappings = await settingDictionaryRepository.GetSettingDictionary(settingParams);
            var supervisionDetails = await profileDetailRepository.GetDataByPatientProfileRecordIDList(profileViews.Select(m => m.PatientProfileRecordID).Distinct().ToList());
            foreach (var profileView in profileViews.GroupBy(m => m.PatientProfileRecordID))
            {
                var tableDataItem = await CreateTableDataItem(profileView.Key, [.. profileView], GetDenominator(supervisionType));
                var sucPatientProfileDetail = supervisionDetails.Where(m => m.PatientProfileRecordID == profileView.Key).ToList();
                if (sucPatientProfileDetail.Count > 0)
                {
                    sucPatientProfileDetail.ForEach(m =>
                    {
                        tableDataItem.Add(StringExtension.FirstLetterToLowerCase(m.DataID), m.DataValue);
                    });
                }
                var groups = profileView.GroupBy(m => m.GroupID).ToList();
                foreach (var dynamicMapping in dynamicMappings)
                {
                    var group = groups.FirstOrDefault(m => m.Key.ToString() == dynamicMapping.SettingValue);
                    if (group == null)
                    {
                        continue;
                    }
                    var contents = new List<string>();
                    foreach (var detail in group.ToList())
                    {
                        if (settings.TryGetValue(detail.ParentID, out string title) && settings.TryGetValue(detail.HierarchicalQCAssessListID, out string content))
                        {
                            contents.Add($"{title}:{content}");
                        }
                    }
                    tableDataItem[dynamicMapping.SettingTypeValue] = string.Join(",", contents);
                }
                returnList.TableData.Add(tableDataItem);
            }
            return returnList;
        }
        /// <summary>
        /// 创建表格数据项
        /// </summary>
        /// <param name="key"></param>
        /// <param name="profileView"></param>
        /// <param name="denominator"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, object>> CreateTableDataItem(string key, List<SensitiveRecordView> profileView, int denominator)
        {
            var departments = await departmentListRepository.GetAllDictAsync();
            var users = await employeePersonalDataRepository.GetIDAndNameData();
            return new Dictionary<string, object>()
                {
                    { "patientProfileRecordID", key },
                    { "hierarchicalQCRecordID", profileView.FirstOrDefault().HierarchicalQCRecordID },
                    { "hierarchicalQCMainID", profileView.FirstOrDefault().HierarchicalQCMainID },
                    { "nmdeparmentID", profileView.FirstOrDefault().DepartmentID },
                    { "nmDepartmentName", departments.FirstOrDefault(m=>m.DepartmentID == profileView.FirstOrDefault().DepartmentID)?.LocalShowName},
                    { "supervisionDateTime", profileView.FirstOrDefault().AssessDate.ToString("yyyy-MM-dd")},
                    { "assessEmployName", users.FirstOrDefault(m=>m.EmployeeID == profileView.FirstOrDefault().AssessEmployID)?.EmployeeName},
                    { "remark", profileView.FirstOrDefault().GuiDance },
                    { "patientName", profileView.FirstOrDefault().PatientName },
                    { "bedNumber", profileView.FirstOrDefault().BedNumber },
                    { "passRate",((decimal)profileView.Count(m => m.HierarchicalQCAssessListID == HierarchicalQCAssessListID_100229) / denominator).ToString("0.##") + "%"}
                };
        }
        /// <summary>
        /// 删除敏感记录
        /// </summary>
        /// <param name="patientProfileRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteSensitiveRecordAsync(string patientProfileRecordID, string employeeID)
        {
            if (string.IsNullOrWhiteSpace(patientProfileRecordID))
            {
                _logger.Error("访视入参有误 删除失败！");
                return false;
            }
            var record = await profileRecordRepository.GetRecordDataByRecordIDAsync(patientProfileRecordID);
            if (record == null)
            {
                _logger.Error("未找到访视记录 删除失败！");
                return false;
            }
            record.Delete(employeeID);
            var details = await profileDetailRepository.GetDataByPatientProfileRecordIDList(patientProfileRecordID);
            if (details.Count > 0)
            {
                details.ForEach(m => m.Delete(employeeID));
            }
            await hierarchicalQCService.DeleteHierarchicalQCRecord(record.RelatedTableRecordID, employeeID);
            return await unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 删除所有督导记录
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceID"></param>
        /// <param name="relatedTableName"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteAllSupervisionRecordAsync(string sourceType, string sourceID, string relatedTableName, string employeeID)
        {
            var recordIDList = await profileRecordRepository.GetRecordIDBySourceID(sourceID, sourceType, relatedTableName);
            if (recordIDList.Count == 0)
            {
                _logger.Error("未找到访视记录 删除失败！");
                return false;
            }
            var patientVisitsRecordList = await profileRecordRepository.GetRecordDataByRecordIDList(recordIDList);
            if (patientVisitsRecordList.Count == 0)
            {
                _logger.Error("未找到访视记录 删除失败！");
                return false;
            }
            var patientVisitsDetailList = await profileDetailRepository.GetDataByPatientProfileRecordIDList(recordIDList);
            if (patientVisitsRecordList.Count > 0)
            {
                patientVisitsRecordList.ForEach(m => m.Delete(employeeID));
            }
            if (patientVisitsDetailList.Count == 0)
            {
                patientVisitsDetailList.ForEach(m => m.Delete(employeeID));
            }
            //删除质控记录
            var qcRecordIDList = patientVisitsRecordList.Select(m => m.RelatedTableRecordID).ToList();
            foreach (var qcRecordID in qcRecordIDList)
            {
                await hierarchicalQCService.DeleteHierarchicalQCRecord(qcRecordID, employeeID);
            }
            return await unitOfWork.SaveChangesAsync() >= 0;
        }
    }
}
