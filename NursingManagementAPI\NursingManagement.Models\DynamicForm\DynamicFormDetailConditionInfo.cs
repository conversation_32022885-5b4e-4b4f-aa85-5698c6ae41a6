﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 动态表单明细联动条件表
    /// </summary>
    [Serializable]
    [Table("DynamicFormDetailCondition")]
    public class DynamicFormDetailConditionInfo : MutiModifyInfo
    {
        /// <summary>
        /// 动态表单明细条件ID
        /// </summary>
        public int DynamicFormDetailConditionID { get; set; }
        /// <summary>
        /// 动态表单明细ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string DynamicFormDetailID { get; set; }
        /// <summary>
        /// 动态表单明细属性ID，ComponentAttribute表主键
        /// </summary>
        public int ComponentAttributeID { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ItemID { get; set; }        
        /// <summary>
        /// 条件：EQUALS:等于; NOT_EQUALS:不等于; GREATER_THAN:大于; LESS_THAN:小于;GREATER_THAN_OR_EQUALS:
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Condition { get; set; }
        /// <summary>
        /// 条件表达式
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string ConditionValue { get; set; }
        /// <summary>
        /// 条件类型：and、or
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ConditionType { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
