﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ITrainingRecordRepository
    {
        /// <summary>
        /// 根据部门ID和医院序号获取数据
        /// </summary>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        Task<List<TrainingRecordInfo>> GetListByDepartMentID(List<int> departmentIDs, string hospitalID);
        /// <summary>
        ///  根据主键ID获取数据
        /// </summary>
        /// <param name="trainingRecordID">培训记录ID</param>
        /// <returns></returns>
        Task<TrainingRecordInfo> GetDataByID(string trainingRecordID);
        /// <summary>
        /// 根据员工编号和医院序号获取数据
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        Task<List<string>> GetTrainingRecordIDsByEmployeeID(string employeeID, string hospitalID);
    }
}
