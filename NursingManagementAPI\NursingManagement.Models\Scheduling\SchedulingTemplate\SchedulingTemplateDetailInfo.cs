﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  排班模板明细表   
    /// </summary>
    [Table("SchedulingTemplateDetail")]
    public class SchedulingTemplateDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 排班模板明细记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SchedulingTemplateDetailID { get; set; }
        /// <summary>
        /// 排班模板主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SchedulingTemplateRecordID { get; set; }
        /// <summary>
        /// 部门岗位编号，DepartmentPost表的主键
        /// </summary>
        public int DepartmentPostID { get; set; }
        /// <summary>
        /// 行序号
        /// </summary>
        public int RowIndex { get; set; }
        /// <summary>
        /// 列序号
        /// </summary>
        public int ColumnIndex { get; set; }
        /// <summary>
        /// 午别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string NoonType { get; set; }
    }
}