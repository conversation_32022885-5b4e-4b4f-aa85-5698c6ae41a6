﻿using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划分组
    /// </summary>
    public class AnnualPlanGoalGroupRepository : IAnnualPlanGoalGroupRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public AnnualPlanGoalGroupRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 获取计划分组集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalID">计划目标ID</param>
        /// <returns></returns>
        public async Task<List<APGroup>> GetAnnualPlanGroups(string mainID, string mainGoalID = null)
        {
            var separator = new[] { "\r\n", "\r", "\n", "、" };
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.Where(m => m.AnnualPlanMainID == mainID
            && m.DeleteFlag != "*").IfWhere(!string.IsNullOrEmpty(mainGoalID), m => m.AnnualPlanMainGoalID == mainGoalID)
            .Select(m => new APGroup
            {
                MainID = m.AnnualPlanMainID,
                GroupID = m.AnnualPlanGoalGroupID,
                MainGoalID = m.AnnualPlanMainGoalID,
                Sort = m.Sort,
                ResponsibleDepartments = m.ResponsibleDepartments == null ? new string[0] : m.ResponsibleDepartments.Trim().Split(separator, StringSplitOptions.RemoveEmptyEntries),
            }).OrderBy(m => m.Sort).ToListAsync();
        }
        /// <summary>
        /// 获取不跟踪的年度计划目标分组集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanGoalGroupInfo>> GetInfosByPlanMainIDAsNoTracking(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.AsNoTracking()
                .Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取年度计划目标分组集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanGoalGroupInfo>> GetInfosByMainID(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos
                .OrderBy(m => m.Sort)
                .Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 获取年度计划目标分组集合
        /// </summary>
        /// <param name="mainGoalID">目标主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanGoalGroupInfo>> GetInfosByMainGoalID(string mainGoalID)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos
                .OrderBy(m => m.Sort)
                .Where(m => m.AnnualPlanMainGoalID == mainGoalID && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 获取目标对应分组集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<string[]> GetAPGroupDepartmentsByMainID(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .Select(m => m.ResponsibleDepartments).ToArrayAsync();
        }
        /// <summary>
        /// 获取年度计划目标分组集合
        /// </summary>
        /// <param name="groupIDs">分组ID集合</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanGoalGroupInfo>> GetGoalGroups(IEnumerable<string> groupIDs)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.Where(m => groupIDs.Contains(m.AnnualPlanGoalGroupID) && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 获取目标分组最大序号
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<int> GetMaxSort(string mainID)
        {
            var queryData = await _nursingManagementDbContext.AnnualPlanGoalGroupInfos
               .Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*").ToListAsync();
            if (queryData.Count<=0)
            {
                return 1;
            }
            return queryData.Max(m => m.Sort) +1;
        }
        /// <summary>
        /// 获取年度计划分组数据
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanGoalGroupInfo> GetGoalGroup(string groupID)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.FirstOrDefaultAsync(m => m.AnnualPlanGoalGroupID == groupID);
        }
        /// <summary>
        /// 获取年度计划分组对应的序号
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<string[]> GetAPGroupIDsBySort(string planMainID)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.Where(m => m.AnnualPlanMainID == planMainID && m.DeleteFlag != "*")
                .OrderBy(m => m.Sort).Select(m => m.AnnualPlanGoalGroupID).ToArrayAsync();
        }
        /// <summary>
        /// 获取其后的分组
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="sort">序号</param>
        /// <returns></returns>
        public async Task<AnnualPlanGoalGroupInfo[]> GetAfterSortGroup(string mainID, int sort)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.Where(m => m.AnnualPlanMainID == mainID && m.Sort >= sort && m.DeleteFlag != "*")
                .OrderBy(m => m.Sort).ToArrayAsync();
        }
        /// <summary>
        /// 获取排序后的分组ID集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<string[]> GetGroupIDsByMainID(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanGoalGroupInfos.Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .OrderBy(m => m.Sort).Select(m => m.AnnualPlanGoalGroupID).ToArrayAsync();
        }
    }
}
