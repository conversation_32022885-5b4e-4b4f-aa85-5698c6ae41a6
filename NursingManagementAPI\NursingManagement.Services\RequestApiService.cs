﻿using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;

namespace NursingManagement.Services
{
    public class RequestApiService : IRequestApiService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        public readonly IAPISettingService _apiSettingService;
        public RequestApiService(IAPISettingService apiSettingService)
        {
            _apiSettingService = apiSettingService;
        }

        public async Task<object> RequestAPI(string settingCode, string param, string token = null,string contentType = "application/json")
        {
            if (string.IsNullOrEmpty(settingCode))
            {
                return null;
            }
            //获取API
            var apiStr = await _apiSettingService.GetAPIAddressByCode(settingCode);
            //apiStr.ApiUrl = "http://localhost:5295/api/sync/SendShiftSchedulingToCSSD";
            if (apiStr == null || string.IsNullOrEmpty(apiStr.ApiUrl))
            {
                _logger.Error("获取地址失败！code：" + settingCode);
                return null;
            }
            Dictionary<string, string> httpHeader = null;
            if (!string.IsNullOrEmpty(token))
            {
                httpHeader = new Dictionary<string, string>
                {
                    { "medical-token", token}
                };
            }
            var url = apiStr.ApiUrl.Trim();
            _logger.Info("APiUrl||" + url);
            string result;
            // 根据配置的访问方式访问API-----1 Post,2 Get
            if (apiStr.CallType == 1)
            {
                // Post方式请求数据
                result = await HttpHelper.HttpPostAsync(url, param, contentType, 30, httpHeader);
            }
            else
            {
                // Get方式请求数据
                if (!string.IsNullOrEmpty(param))
                {
                    url += param;
                }
                //呼叫api
                result = HttpHelper.HttpGet(url, "application/json", httpHeader);
            }
            return result;
        }

    }
}
