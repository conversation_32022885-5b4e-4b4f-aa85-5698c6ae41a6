﻿using NursingManagement.Data.Interface;
using NursingManagement.Data.Interface.Employee;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class EmployeeSecondmentDynamicColumn
    {
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeeSecondmentRecordRepository _employeeSecondmentRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IAdministrationDictionaryRepository _administrationDictionaryRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;

        public EmployeeSecondmentDynamicColumn(
            IDepartmentListRepository departmentListRepository,
            IEmployeeSecondmentRecordRepository employeeSecondmentRecordRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IAdministrationDictionaryRepository administrationDictionaryRepository,
            ISettingDictionaryRepository settingDictionaryRepository)
        {
            _departmentListRepository = departmentListRepository;
            _employeeSecondmentRecordRepository = employeeSecondmentRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _administrationDictionaryRepository = administrationDictionaryRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
        }


        /// <summary>
        /// 根据主键来源表动态列
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetDynamicColumnListByRecordIDAsync(List<string> recordIDs, string proveCategory)
        {
            List<Dictionary<string, object>> resultList = new();
            var departmentList = await _departmentListRepository.GetByCacheAsync();
            var recordLists = await _employeeSecondmentRecordRepository.GetRecordsByIDsAsNoTrackAsync(recordIDs);
            if (recordLists.Count <= 0)
            {
                return null;
            }
            var administrationParams = new AdministrationParams()
            {
                //国标字典
                SettingTypeCode = "EmployeeProfileList",
            };
            var dictionarySettingList = await _administrationDictionaryRepository.GetDictionary(administrationParams);
            var typeSettingList = dictionarySettingList.Where(m => m.ReferenceValue == "SecondmentType");
            var purposeList = dictionarySettingList.Where(m => m.ReferenceValue == "SecondmentPurpose");
            foreach (var item in recordLists)
            {               //添加审批流程
                var view = await CreateEmployeeSecondmentApproveViewAsync(item, departmentList, proveCategory, purposeList, typeSettingList);
                resultList.Add(view);
            }
            return resultList;


        }

        /// <summary>
        /// 创建人员借调审批需要的参数
        /// </summary>
        /// <param name="employeeSecondmentRecordInfo"></param>
        /// <param name="departmentList"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, object>> CreateEmployeeSecondmentApproveViewAsync(EmployeeSecondmentRecordInfo employeeSecondmentRecordInfo, List<DepartmentListInfo> departmentList, string proveCategory, IEnumerable<AdministrationDictionaryInfo> purposeList, IEnumerable<AdministrationDictionaryInfo> typeSettingList)
        {
            var approveEmployeeIDs = new List<string>();
            var noonParam = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var employeeDatas = await _employeePersonalDataRepository.GetDataByEmployeeID(employeeSecondmentRecordInfo.EmployeeID);
            var noonSetting = await _settingDictionaryRepository.GetSettingDictionary(noonParam);
            var noonPairs = noonSetting.Select(m => new KeyValueString { Key = m.SettingValue.ToString(), Value = m.Description }).ToList();
            return new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase)
            {
                { "SourceID", employeeSecondmentRecordInfo.EmployeeSecondmentRecordID },
                { "ApproveRecordID", employeeSecondmentRecordInfo.ApproveRecordID },
                { "ProveCategory", proveCategory },
                { "DepartmentID", employeeSecondmentRecordInfo.SecondmentDepartmentID },
                { "AddEmployeeID", employeeSecondmentRecordInfo.AddEmployeeID },
                { "AdjustEmployeeName", employeeDatas?.EmployeeName },
                { "DepartmentName", departmentList.Find(m => m.DepartmentID == employeeSecondmentRecordInfo.SecondmentDepartmentID)?.DepartmentContent },
                { "OriginalDeptName", departmentList.Find(m => m.DepartmentID == employeeSecondmentRecordInfo.DepartmentID)?.DepartmentContent },
                { "StartDate", employeeSecondmentRecordInfo.StartDate.ToString("yyyy-MM-dd") },
                { "EndDate", employeeSecondmentRecordInfo.EndDate.ToString("yyyy-MM-dd") },
                { "StartNoon", noonPairs.Find(m => m.Key == employeeSecondmentRecordInfo.StartNoon)?.Value },
                { "EndNoon", noonPairs.Find(m => m.Key == employeeSecondmentRecordInfo.EndNoon)?.Value },
                { "SecondmentDays", employeeSecondmentRecordInfo.SecondmentDays },
                { "purpose",purposeList.FirstOrDefault(m => m.TypeValue == employeeSecondmentRecordInfo.SecondmentPurpose)?.LocalShowName ?? ""},
                { "adjustType",typeSettingList.FirstOrDefault(m => m.TypeValue == employeeSecondmentRecordInfo.SecondmentType)?.LocalShowName ?? ""}
            };
        }
    }
}
