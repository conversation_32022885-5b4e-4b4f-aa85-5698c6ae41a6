﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDepartmentPostWorkingTimeRepository : ICacheRepository
    {
        /// <summary>
        /// 根据部门岗位ID和季节获取数据
        /// </summary>
        /// <param name="departmenPostID"></param>
        /// <param name="season"></param>
        /// <returns></returns>
        Task<List<DepartmentPostWorkingTimeInfo>> GetByDepartmenPostID(int departmentPostID, string season);
        /// <summary>
        /// 根据季节获取数据
        /// </summary>
        /// <param name="season"></param>
        /// <returns></returns>
        Task<List<DepartmentPostWorkingTimeInfo>> GetBySeason(string season);
        /// <summary>
        /// 依据岗位集合获取数据
        /// </summary>
        /// <param name="departmenPostIDs"></param>
        /// <param name="season"></param>
        /// <returns></returns>
        Task<List<DepartmentPostWorkingTimeInfo>> GetByDepartmenPostIDs(List<int> departmenPostIDs, string season = null);        
    }
}
