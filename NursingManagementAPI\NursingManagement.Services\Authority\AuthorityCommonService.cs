﻿using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;

namespace NursingManagement.Services
{
    public class AuthorityCommonService : IAuthorityCommonService
    {
        private readonly IAuthorityListRepository _authorityListRepository;
        private readonly IAuthorityRoleListRepository _authorityRoleListRepository;
        private readonly IMenuListRepository _menuListRepository;
        private readonly IRouterListRepository _routerListRepository;

        public AuthorityCommonService(
            IAuthorityListRepository authorityListRepository
            , IAuthorityRoleListRepository authorityRoleListRepository
            , IMenuListRepository menuListRepository
            , IRouterListRepository routerListRepository
        )
        {
            _authorityListRepository = authorityListRepository;
            _authorityRoleListRepository = authorityRoleListRepository;
            _menuListRepository = menuListRepository;
            _routerListRepository = routerListRepository;
        }

        public async Task<Tuple<List<MenuListInfo>, List<RouterListInfo>>> GetMenuAndRouterList(List<int> roles, int clientType)
        {
            if (roles == null || roles.Count <= 0)
            {
                return null;
            }
            // 获取权限菜单列表
            var authorityList = await _authorityListRepository.GetAuthorityList();
            if (authorityList.Count <= 0)
            {
                return null;
            }
            // 获取角色权限列表
            var authorityIDs = await _authorityRoleListRepository.GetAuthorityListByRoleID(roles);
            if (authorityIDs.Count <= 0)
            {
                return null;
            }
            // 去掉没有权限的功能
            authorityList = authorityList.Where(m => authorityIDs.Contains(m.AuthorityID)).ToList();
            if (authorityList.Count <= 0)
            {
                return null;
            }
            var menuList = await _menuListRepository.GetAllMenuList();
            // 移动端菜单
            if (clientType == 2)
            {
                menuList = menuList.Where(m=>m.MobileFlag.HasValue && m.MobileFlag.Value).ToList();
            }
            menuList = menuList.Where(t => authorityList.Any(n => n.MenuListID == t.MenuListID)).ToList();
            if (menuList.Count <= 0)
            {
                return null;
            }
            var routerList = await _routerListRepository.GetRouterListByClientType(clientType);
            // 获取有权限的或不需要权限的路由清单
            routerList = routerList.Where(m => (m.Auth != null && m.Auth.Value && menuList.Any(n => n.RouterListID == m.RouterListID)) || (m.Auth == null || !m.Auth.Value)).ToList();
            if (routerList.Count <= 0)
            {
                return null;
            }
            return new Tuple<List<MenuListInfo>, List<RouterListInfo>>(menuList, routerList);
        }

        public string AssemblePath(string routerPath, bool isRouter)
        {
            if (string.IsNullOrWhiteSpace(routerPath) || !routerPath.Contains("?"))
            {
                return routerPath;
            }
            var router = routerPath.Split("?");
            var newPath = router[0];
            var propList = router[1].Split("&");
            foreach (var prop in propList)
            {
                if (!prop.Contains("="))
                {
                    continue;
                }
                var props = prop.Split("=");
                if (isRouter)
                {
                    newPath = $"{newPath}/:{props[0]}";
                }
                else
                {
                    newPath = $"{newPath}/{props[1]}";
                }
            }
            return newPath;
        }
    }
}
