﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划-计划制定明细表
    /// </summary>
    [Serializable]
    [Table("AnnualInterventionDetail")]
    public class AnnualInterventionDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 制定明细序号
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanInterventionDetailID { get; set; }
        /// <summary>
        /// 制定主表序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanInterventionMainID { get; set; }
        /// <summary>
        /// 年度计划主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 计划月份
        /// </summary>
        public int PlanMonth { get; set; }
        /// <summary>
        /// 计划日期
        /// </summary>
        public DateTime? PlanDate { get; set; }
    }
}
