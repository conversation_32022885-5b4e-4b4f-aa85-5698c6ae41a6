﻿﻿namespace NursingManagement.ViewModels.NormalWorkingReminder
{
    /// <summary>
    /// 提醒请求参数视图
    /// </summary>
    public class ReminderRequestView
    {
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 提醒类型：3-3天未整改提醒护士长，6-6天未整改提醒片区主任，0-全部提醒
        /// </summary>
        public int ReminderType { get; set; }

        /// <summary>
        /// 指定部门ID（可选，为空则检查所有部门）
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 开始日期（可选，用于限制检查范围）
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期（可选，用于限制检查范围）
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 是否只返回需要提醒的问题列表，不实际发送提醒
        /// </summary>
        public bool OnlyQuery { get; set; }

        /// <summary>
        /// 强制发送提醒（即使今天已经发送过）
        /// </summary>
        public bool ForceRemind { get; set; }

        public ReminderRequestView()
        {
            ReminderType = 0; // 默认全部提醒
            OnlyQuery = false;
            ForceRemind = false;
        }
    }

    /// <summary>
    /// 查询未整改问题请求视图
    /// </summary>
    public class QueryUnrectifiedProblemsRequestView
    {
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 部门ID（可选）
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 最小未整改天数
        /// </summary>
        public int MinUnrectifiedDays { get; set; }

        /// <summary>
        /// 最大未整改天数（可选）
        /// </summary>
        public int? MaxUnrectifiedDays { get; set; }

        /// <summary>
        /// 开始日期（问题发现日期范围）
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期（问题发现日期范围）
        /// </summary>
        public DateTime? EndDate { get; set; }

        public QueryUnrectifiedProblemsRequestView()
        {
            MinUnrectifiedDays = 3; // 默认查询3天以上未整改的问题
        }
    }
}
