﻿using NursingManagement.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NursingManagement.ViewModels.CacheView
{
    public class DynamicFormDetailAttributeCacheView
    {
        /// <summary>
        /// 动态表单明细属性ID
        /// </summary>
        public int DynamicFormDetailAttributeID { get; set; }
        /// <summary>
        /// 动态表单明细ID
        /// </summary> 
        public string DynamicFormDetailID { get; set; }
        /// <summary>
        /// 来源字典ComponentAttribute
        /// </summary>
        public int ComponentAttributeID { get; set; }
        /// <summary>
        /// 属性值
        /// </summary>  
        public string AttributeValue { get; set; }

    }
}
