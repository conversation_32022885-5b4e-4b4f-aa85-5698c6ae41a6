﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class SchedulingTemplateDetailMarkRepository : ISchedulingTemplateDetailMarkRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        public SchedulingTemplateDetailMarkRepository(
            NursingManagementDbContext db
        )
        {
            _nursingManagementDbContext = db;
        }

        public async Task<List<SchedulingTemplateDetailMarkInfo>> GetMarkByRecordID(string schedulingTemplateRecordID)
        {
            return await _nursingManagementDbContext.SchedulingTemplateDetailMarkInfos
                   .Where(m => m.SchedulingTemplateRecordID == schedulingTemplateRecordID && m.DeleteFlag != "*")
                   .ToListAsync();
        }
    }
}
