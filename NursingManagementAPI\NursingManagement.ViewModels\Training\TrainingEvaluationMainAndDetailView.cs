﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 保存培训评价视图
    /// </summary>
    public class TrainingEvaluationMainAndDetailView
    {
        /// <summary>
        /// 培训人员记录ID
        /// </summary>
        public string TrainingLearnerID { get; set; }

        /// <summary>
        /// 异动人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 培训记录ID
        /// </summary>
        public string TrainingRecordID { get; set; }

        /// <summary>
        /// 被评价人员ID
        /// </summary>
        public string BeEvaluationEmployeeID { get; set; }

        /// <summary>
        /// 评价类型(评价学生、评价老师)
        /// </summary>
        public string EvaluationType { get; set; }

        /// <summary>
        /// 明细内容
        /// </summary>
        public List<TrainingEvaluationDetailView> DetailList { get; set; }
    }
}
