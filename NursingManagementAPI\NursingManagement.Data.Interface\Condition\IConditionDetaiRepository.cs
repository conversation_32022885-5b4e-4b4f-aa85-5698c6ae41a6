﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IConditionDetaiRepository
    {
        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        Task<List<ConditionDetailInfo>> GetListByMainIDs(List<string> mainIDs);

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<List<ConditionDetailInfo>> GetListByMainID(string mainID);
    }
}
