﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Table("HospitalList")]
    public class HospitalListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 医院所代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HospitalCode { get; set; }
        /// <summary>
        /// 地区
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string Prefecture { get; set; }
        /// <summary>
        /// 城市ID
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string CityID { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string Address { get; set; }
        /// <summary>
        /// 医院名称
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string HospitalName { get; set; }
        /// <summary>
        /// 系统版本
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SystemVersion { get; set; }
        /// <summary>
        /// 默许语言
        /// </summary>
        public int DefaultLanguage { get; set; }
        /// <summary>
        /// 主题色
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string ThemeColor { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        /// 是否单点登入
        /// </summary>
        public bool SingleSignOn { get; set; }
    }
}
