﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ICommonFileRepository
    {
        /// <summary>
        /// 根据分类和来源获取文件集合
        /// </summary>
        /// <param name="fileClass">文件分类；1：公共文件，2：消息附件</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        Task<List<CommonFileInfo>> GetFileListByClassAndSourceAsync(string fileClass, string sourceID);
        /// <summary>
        /// 获取文件记录(根据文件唯一ID，现在先使用CommonFileID主键)
        /// </summary>
        /// <param name="fileID"></param>
        /// <returns></returns>
        Task<CommonFileInfo> GetFileByFileIDAsync(string fileID);
        /// <summary>
        /// 根据主键获取link
        /// </summary>
        /// <param name="fileID"></param>
        /// <returns></returns>
        Task<string> GetFileUrlByIDAsync(string fileID);
        /// <summary>
        /// 根据文件IDs获取对应的Source
        /// </summary>
        /// <param name="fileIDs"></param>
        /// <returns></returns>
        Task<List<CommonFileInfo>> GetPartFileInfosByIDsAsync(List<string> fileIDs);
    }
}
