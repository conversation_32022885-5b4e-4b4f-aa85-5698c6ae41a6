﻿using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  DepartmentPostSetting    
    /// </summary>
    [Table("DepartmentPostSetting")]
    public class DepartmentPostSettingInfo:MutiModifyInfo
    {
        /// <summary>
        /// 岗位序号，主键，PostDescription表的主键    
        /// </summary>
        public int PostID { get; set; }

        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID 
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 医院序号，主键    
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 类型，1:常规，2:高峰 
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 期望岗位数量    
        /// </summary>
        public int ExpectedCount { get; set; }

        /// <summary>
        /// 发布日期，用于查询历年晋升标准记录    
        /// </summary>
        public DateTime PublishDate { get; set; }

    }
}