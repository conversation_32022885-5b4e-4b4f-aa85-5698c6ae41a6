﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class DepartmentVSDepartmentService : IDepartmentVSDepartmentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IDepartmentVSDepartmentRepository _departmentVSDepartmentRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IDictionaryService _dictionaryService;

        /// <summary>
        /// 护理组织架构，6：HR系统组织
        /// </summary>
        private const string ORGANIZATION_TYPE_6 = "6";
        /// <summary>
        /// 护理组织架构，1：护理管理组织
        /// </summary>
        private const string ORGANIZATION_TYPE_1 = "1";

        public DepartmentVSDepartmentService(
            IUnitOfWork unitOfWork
            , IDepartmentVSDepartmentRepository departmentVSDepartmentRepository
            , IDepartmentListRepository departmentListRepository
            , IDictionaryService dictionaryService
        )
        {
            _unitOfWork = unitOfWork;
            _departmentVSDepartmentRepository = departmentVSDepartmentRepository;
            _departmentListRepository = departmentListRepository;
            _dictionaryService = dictionaryService;
        }
        /// <summary>
        /// 获取部门关系视图
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        public async Task<List<DepartmentMaintenanceView>> GetDepartmentVSDepartmentView(string organizationType)
        {
            var departmentList = await _departmentListRepository.GetDepartmentListNoCache();
            //获取护理管理部门名称
            var managementDepartment = departmentList.Where(m => m.OrganizationType == organizationType).ToList();
            //获取人事管理部门名称
            var hrDepartment = await _dictionaryService.GetHrmDepartmentDict();
            //获取部门之间对应关系名称
            var hrDeptToManageDept = await _departmentVSDepartmentRepository.GetByCacheAsync();
            hrDeptToManageDept = hrDeptToManageDept.Where(m => m.OrganizationType1 == "6" && m.OrganizationType2 == organizationType).ToList();
            var returnView = GenerateDepartmentView(managementDepartment, hrDepartment, hrDeptToManageDept, 1, null);
            return returnView;
        }

        /// <summary>
        /// 递归获取不同层级部门及对照关系
        /// </summary>
        /// <param name="managementDepartment">护理管理系统部门组织</param>
        /// <param name="hrDepartment">HR对应部门</param>
        /// <param name="hrDeptToManageDept">部门对照关系</param>
        /// <param name="level">层级</param>
        /// <param name="upperLevelDepartmentID">父级部门组织ID</param>
        /// <returns></returns>
        private List<DepartmentMaintenanceView> GenerateDepartmentView(List<DepartmentListInfo> managementDepartment, List<DictItem> hrDepartment, List<DepartmentVSDepartmentInfo> hrDeptToManageDept, int level, int? upperLevelDepartmentID)
        {
            var cascaderViews = new List<DepartmentMaintenanceView>();
            var currentLevelList = managementDepartment.Where(m => m.Level == level)
                .IfWhere(upperLevelDepartmentID != null, m => m.UpperLevelDepartmentID == upperLevelDepartmentID).ToList();

            foreach (var department in currentLevelList)
            {
                var departmentView = new DepartmentMaintenanceView();
                var tempDeptIDArr = hrDeptToManageDept.Where(m => m.DepartmentID2 == department.DepartmentID).Select(m => m.DepartmentID1).ToArray();
                if (tempDeptIDArr.Count() <= 0)
                {
                    departmentView = new DepartmentMaintenanceView
                    {
                        ManagementDepartment = department.LocalShowName,
                        ManagementDepartmentID = department.DepartmentID,
                        OrganizationType = department.OrganizationType,
                        IsActived = string.IsNullOrEmpty(department.DeleteFlag),
                        Level = department.Level,
                        UpperLevelDepartmentID = department.UpperLevelDepartmentID,
                        Children = GenerateDepartmentView(managementDepartment, hrDepartment, hrDeptToManageDept, level + 1, department.DepartmentID)
                    };
                }
                else
                {
                    departmentView = new DepartmentMaintenanceView
                    {
                        ManagementDepartment = department.LocalShowName,
                        ManagementDepartmentID = department.DepartmentID,
                        OrganizationType = department.OrganizationType,
                        IsActived = string.IsNullOrEmpty(department.DeleteFlag),
                        Level = department.Level,
                        HRDepartment = GetHRDepartmentName(hrDepartment, tempDeptIDArr),
                        HRDepartmentID = tempDeptIDArr,
                        UpperLevelDepartmentID = department.UpperLevelDepartmentID,
                        Children = GenerateDepartmentView(managementDepartment, hrDepartment, hrDeptToManageDept, level + 1, department.DepartmentID)
                    };
                }
                cascaderViews.Add(departmentView);
            }
            return cascaderViews;
        }

        /// <summary>
        /// 获取HR部门名称
        /// </summary>
        /// <param name="hrDepartment">HR部门集合</param>
        /// <param name="departmentIDArr">HR部门ID</param>
        /// <returns></returns>
        private String GetHRDepartmentName(List<DictItem> hrDepartment, int[] departmentIDArr)
        {
            var str = "";
            var localShowNameDepartment = hrDepartment.Where(m => departmentIDArr.Contains(m.Key)).Select(m => m.Value).ToList();
            if (localShowNameDepartment.Count <= 0)
            {
                return str;
            }
            for (int i = 0; i < localShowNameDepartment.Count; i++)
            {
                if (i == 0)
                {
                    str += localShowNameDepartment[i];
                }
                else
                {
                    str += "<br />" + localShowNameDepartment[i];
                }
            }
            return str;
        }

        /// <summary>
        /// 保存单笔数据
        /// </summary>
        /// <param name="saveView">保存数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> SaveDepartmentVSDepartmentView(DepartmentMaintenanceView saveView, Session session)
        {
            if (saveView == null)
            {
                return false;
            }
            var departmentList = await _departmentListRepository.GetDepartmentListNoCache();
            if (saveView.ManagementDepartmentID == 0)
            {
                //插入新数据
                return await InsertDepartmentView(saveView, departmentList, session);
            }
            var departmentInfo = departmentList.Where(m => m.OrganizationType == saveView.OrganizationType && m.DepartmentID == saveView.ManagementDepartmentID).FirstOrDefault();
            departmentInfo.DepartmentContent = departmentInfo.LocalShowName = saveView.ManagementDepartment;
            departmentInfo.Modify(session.EmployeeID);
            if (saveView.OrganizationType == ORGANIZATION_TYPE_1)
            {
                await InsertDepartmentVSDepartmentView(saveView, departmentInfo, session);
            }
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                await _departmentVSDepartmentRepository.UpdateCache();
                await _departmentListRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 插入部门数据
        /// </summary>
        /// <param name="saveView">保存数据</param>
        /// <param name="departmentList">部门列表</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        private async Task<bool> InsertDepartmentView(DepartmentMaintenanceView saveView, List<DepartmentListInfo> departmentList, Session session)
        {
            //在部门表中插入一条新数据
            var maxDepartmentID = await _departmentListRepository.GetMaxDepartmentID(session.HospitalID, session.Language);
            var departmentInfo = new DepartmentListInfo
            {
                DepartmentID = maxDepartmentID + 1,
                HospitalID = session.HospitalID,
                Language = session.Language,
                DepartmentContent = saveView.ManagementDepartment,
                LocalShowName = saveView.ManagementDepartment,
                DepartmentCode = "",
                OrganizationType = saveView.OrganizationType,
                DepartmentType = saveView.OrganizationType == "1" ? "3" : "1",
                SourceType = "3",
                Level = saveView.Level,
                UpperLevelDepartmentID = saveView.UpperLevelDepartmentID,
                DeleteFlag = saveView.IsActived ? "" : "*",
                UpperLevelDepartmentCode = ""
            };
            var sort = departmentList.Where(m => m.OrganizationType == saveView.OrganizationType && m.Level == saveView.Level && m.UpperLevelDepartmentID == departmentInfo.UpperLevelDepartmentID).Select(m => m.Sort);
            if (sort.Count() > 0 && sort != null)
            {
                departmentInfo.Sort = sort.Max() + 1;
            }
            else
            {
                var upperDepartmentInfo = departmentList.FirstOrDefault(m => m.DepartmentID == saveView.UpperLevelDepartmentID);
                if (upperDepartmentInfo != null)
                {
                    departmentInfo.Sort = (upperDepartmentInfo.Sort * 10) + 1;
                }
            }
            departmentInfo.Add(session.EmployeeID);
            departmentInfo.Modify(session.EmployeeID);
            await _unitOfWork.GetRepository<DepartmentListInfo>().InsertAsync(departmentInfo);

            if (saveView.OrganizationType == ORGANIZATION_TYPE_1)
            {
                await InsertDepartmentVSDepartmentView(saveView, departmentInfo, session);
            }
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await _departmentVSDepartmentRepository.UpdateCache();
                await _departmentListRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 插入部门对照关系
        /// </summary>
        /// <param name="saveView">保存数据</param>
        /// <param name="departmentInfo">部门信息</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        private async Task InsertDepartmentVSDepartmentView(DepartmentMaintenanceView saveView, DepartmentListInfo departmentInfo, Session session)
        {
            if (saveView == null)
            {
                return;
            }
            var deptVSDeptList = new List<DepartmentVSDepartmentInfo>();
            var deptVSDeptListInfo = await _departmentVSDepartmentRepository.GetByCacheAsync();
            var deptVSDeptByDepartmentID = await _departmentVSDepartmentRepository.GetByOrganizationType(ORGANIZATION_TYPE_1, saveView.ManagementDepartmentID);
            //不在本次保存的部门进行删除
            var deleteDeptVSDeptIDList = deptVSDeptByDepartmentID.Where(m => !saveView.HRDepartmentID.Contains(m.DepartmentID1) && m.OrganizationType1 == ORGANIZATION_TYPE_6).ToList();
            foreach (var item in deleteDeptVSDeptIDList)
            {
                item.Delete(session.EmployeeID);
                item.Modify(session.EmployeeID);
                _unitOfWork.GetRepository<DepartmentVSDepartmentInfo>().Update(item);
            }
            if (saveView.HRDepartmentID == null || saveView.HRDepartmentID.Count() <= 0)
            {
                return;
            }
            foreach (var hrDepartmentID in saveView.HRDepartmentID)
            {
                //若当前人事部门已有对应的护理管理部门，进行更新
                var deptVSDept = deptVSDeptListInfo.FirstOrDefault(m => m.DepartmentID1 == hrDepartmentID
                && m.OrganizationType1 == ORGANIZATION_TYPE_6 && m.OrganizationType2 == ORGANIZATION_TYPE_1);
                if (deptVSDept != null)
                {
                    deptVSDept.DepartmentID2 = departmentInfo.DepartmentID;
                    deptVSDept.Modify(session.EmployeeID);
                    _unitOfWork.GetRepository<DepartmentVSDepartmentInfo>().Update(deptVSDept);
                    continue;
                }
                //添加部门对照关系
                var deptVSDeptInfo = new DepartmentVSDepartmentInfo
                {
                    HospitalID = session.HospitalID,
                    DepartmentID1 = hrDepartmentID,
                    OrganizationType1 = ORGANIZATION_TYPE_6,
                    DepartmentID2 = departmentInfo.DepartmentID,
                    OrganizationType2 = ORGANIZATION_TYPE_1,
                };
                deptVSDeptInfo.Add(session.EmployeeID);
                deptVSDeptInfo.Modify(session.EmployeeID);
                deptVSDeptList.Add(deptVSDeptInfo);
            }
            await _unitOfWork.GetRepository<DepartmentVSDepartmentInfo>().InsertAsync(deptVSDeptList);
        }

        /// <summary>
        /// 批量保存数据
        /// </summary>
        /// <param name="saveViews">保存数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> SaveDepartmentVSDepartmentViews(List<DepartmentMaintenanceView> saveViews, Session session)
        {
            if (saveViews == null || saveViews.Count() <= 0)
            {
                return false;
            }
            try
            {
                foreach (var saveView in saveViews)
                {
                    await SaveDepartmentVSDepartmentView(saveView, session);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("保存部门信息失败，失败原因" + ex.ToString());
                return false;
            }
        }
        /// <summary>
        /// 启用或停用部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="employeeID">护士工号</param>
        /// <param name="isActived">是否启用</param>
        /// <returns></returns>
        public async Task<bool> EnableOrDisableDepartment(int departmentID, string employeeID, bool isActived)
        {
            if (departmentID == 0)
            {
                return false;
            }
            var departmentList = await _departmentListRepository.GetDepartmentListNoCache();
            var departmentInfo = departmentList.Where(m => m.OrganizationType == ORGANIZATION_TYPE_1 && (m.DepartmentID == departmentID || m.UpperLevelDepartmentID == departmentID)).ToList();
            if (departmentInfo == null || departmentInfo.Count <= 0)
            {
                _logger.Warn("未找到该部门及其子集部门信息，departmentID=" + departmentID);
                return false;
            }
            foreach (var item in departmentInfo)
            {
                if (!isActived)
                {
                    item.Delete(employeeID);
                    continue;
                }
                item.DeleteFlag = "";
                item.Modify(employeeID);
            }
            var flag = await _unitOfWork.SaveChangesAsync() > 0;
            if (flag)
            {
                await _departmentListRepository.UpdateCache();
            };
            return flag;
        }
    }
}
