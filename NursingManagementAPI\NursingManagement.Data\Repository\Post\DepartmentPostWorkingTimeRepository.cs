﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DepartmentPostWorkingTimeRepository : IDepartmentPostWorkingTimeRepository
    {

        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public DepartmentPostWorkingTimeRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer
            )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        /// <summary>
        /// 根据部门岗位ID和季节获取数据
        /// </summary>
        /// <param name="departmenPostID"></param>
        /// <param name="season"></param>
        /// <returns></returns>
        public async Task<List<DepartmentPostWorkingTimeInfo>> GetByDepartmenPostID(int departmentPostID, string season)
        {
            var datas = await GetBySeason(season);
            return datas.Where(m => m.DepartmentPostID == departmentPostID).ToList();
        }
        /// <summary>
        /// 根据季节获取数据
        /// </summary>
        /// <param name="season"></param>
        /// <returns></returns>
        public async Task<List<DepartmentPostWorkingTimeInfo>> GetBySeason(string season)
        {
            var datas = await GetCacheAsync() as List<DepartmentPostWorkingTimeInfo>;
            return datas.Where(m => m.Season == season).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DepartmentPostWorkingTimeInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });

            return datas;
        }
        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType) => GetCacheType() == cacheType;

        public string GetCacheType() => CacheType.DepartmentPostWorkingTime.GetKey(_sessionCommonServer);

        public async Task<List<DepartmentPostWorkingTimeInfo>> GetByDepartmenPostIDs(List<int> departmenPostIDs, string season = null)
        {
            var datas = await GetCacheAsync() as List<DepartmentPostWorkingTimeInfo>;
            if (!string.IsNullOrWhiteSpace(season))
            {
                datas = datas.Where(m => m.Season == season).ToList();
            }
            return datas.Where(m => departmenPostIDs.Contains(m.DepartmentPostID)).OrderBy(m => m.Sort).ToList();
        }
    }
}
