﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IExamineService
    {
        /// <summary>
        ///根据试卷id集合删除考核相关记录
        /// </summary>
        /// <param name="paperMainIDList"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<string> DeleteExamineByPaperMainID(List<string> paperMainIDList, string employeeID);
        /// <summary>
        /// 获取考核主记录数据
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="type"></param>
        /// <param name="employeeID">会话人工号</param>
        /// <returns></returns>
        Task<List<ExaminationRecordView>> GetExaminationRecordList(DateTime startDate, DateTime endDate, string type, string employeeID);
        /// <summary>
        /// 删除考核主记录数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteExaminationRecordData(string recordID, string employeeID);
        /// <summary>
        /// 保存考核主记录数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveExaminationRecordData(ExaminationRecordView view);
        /// <summary>
        ///  发布考核
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> PublishExamine(ExaminationRecordView view);
        /// <summary>
        /// 获取考核主表数据
        /// </summary>
        /// <param name="examineEmployeeID">主考人工号</param>
        /// <param name="examineRecordID">考核记录工号</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="examinationType">考核类型</param>
        /// <returns></returns>
        Task<List<ExaminationMainView>> GetExamineMainList(string examineEmployeeID, string examineRecordID, DateTime startDate, DateTime endDate, string examinationType);
        /// <summary>
        /// 更改考核主表状态
        /// </summary>
        /// <param name="examinationMainID"></param>
        /// <param name="statusCode"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> ChangeExamineMainStatus(string examinationMainID, string statusCode, string employeeID);
        /// <summary>
        /// 补考
        /// </summary>
        /// <param name="examinationMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SupplementaryExamineMain(string examinationMainID, string employeeID);
        /// <summary>
        /// 获取考核记录(个人理论考核以及主考的实操考核)
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <param name="statusCodes">作答状态</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="filterExaminerFlag">根据主考人筛选数据开关</param>
        /// <param name="examinationType">根据主考人筛选数据开关</param>
        /// <returns></returns>
        Task<List<ExaminationMainView>> GetEmployeeExamineMainList(string employeeID, string statusCodes, bool filterExaminerFlag, string examinationType, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 保存考核明细记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveExamineDetail(SaveExaminationDetailView view);
        /// <summary>
        /// 考核扫码签到
        /// </summary>
        /// <param name="examinationRecordID">考核主表记录</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        Task<(bool, string)> ExaminationSignIn(string examinationRecordID, long timeStamp, string employeeID);
        /// <summary>
        /// 获取刷题练习数据
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="statusCode">考核状态</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<ExaminationMainView>> GetPracticeExamineMainList(string employeeID, string statusCode, DateTime? startDate, DateTime? endDate);

        /// <summary>
        /// 停止发布考核
        /// </summary>
        /// <param name="examinationRecordID">考核主记录ID</param>
        /// <param name="employeeID">会话人员</param>
        /// <returns></returns>
        Task<bool> StopPublishExamine(string examinationRecordID, string employeeID);

        /// <summary>
        /// 获取未完成的的实操类考核计划记录
        /// </summary>
        /// <returns></returns>
        Task<List<ExaminationRecordData>> GetNotFinishedPracticalRecords();
        /// <summary>
        /// 检查考核是否已经考评完成 未完成考评，调整最后一次修改人信息
        /// </summary>
        /// <param name="examinationMainID">考核记录ID</param>
        /// <param name="sessionEmployeeID">当前会话用户ID</param>
        /// <returns></returns>
        Task<ExaminationStatusView> CheckExamineCompleteAssessOrNot(string examinationMainID, string sessionEmployeeID);
        /// <summary>
        /// 保存刷题练习考核记录
        /// </summary>
        /// <param name="questionBankID">题库序号</param>
        /// <param name="questionBankName">题库名称</param>
        /// <param name="departmentID">部门序号</param>
        /// <param name="employeeID">人员序号</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns></returns>
        Task<bool> SavePracticeExaminationRecord(string questionBankID, string questionBankName, int departmentID, string employeeID, string hospitalID);
    }
}
