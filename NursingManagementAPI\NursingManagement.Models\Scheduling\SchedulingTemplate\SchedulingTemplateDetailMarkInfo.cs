﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  排班模板主表   
    /// </summary>
    [Table("SchedulingTemplateDetailMark")]
    public class SchedulingTemplateDetailMarkInfo : MutiModifyInfo
    {
        /// <summary>
        /// 排班模板明细标记记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SchedulingTemplateDetailMarkID { get; set; }
        /// <summary>
        /// 排班模板主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SchedulingTemplateRecordID { get; set; }
        /// <summary>
        /// 行序号
        /// </summary>
        public int RowIndex { get; set; }
        /// <summary>
        /// 列序号
        /// </summary>
        public int ColumnIndex { get; set; }
        /// <summary>
        /// 标记编码，对应AdministrationIcon表中的唯一码
        /// </summary>
        public int MarkID { get; set; }
        /// <summary>
        /// 标记的值
        /// </summary>
        [Column(TypeName = "nvarchar(500)")]
        public string MarkValue { get; set; }
        
    }
}