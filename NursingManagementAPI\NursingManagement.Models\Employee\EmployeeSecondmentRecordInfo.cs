﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("EmployeeSecondmentRecord")]
    public class EmployeeSecondmentRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 人员借调记录号
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string EmployeeSecondmentRecordID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// HR员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 借调部门编号，护理管理组织架构的DepartmentID
        /// </summary>
        public int SecondmentDepartmentID { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 到期日期
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 借调目的
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string SecondmentPurpose { get; set; }
        /// <summary>
        /// 借调类型
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string SecondmentType { get; set; }
        /// <summary>
        /// 开始午别
        /// </summary>
        [Column(TypeName ="varchar(1)")]
        public string StartNoon { get; set; }
        /// <summary>
        /// 结束午别
        /// </summary>
        [Column(TypeName ="varchar(1)")]
        public string EndNoon { get; set; }
        /// <summary>
        /// 借调天数
        /// </summary>
        [Column(TypeName = "decimal(5,1)")]
        public decimal? SecondmentDays { get; set; }
        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 提交审批记录序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveRecordID { get; set; }
        /// <summary>
        /// 实际结束日期
        /// </summary>
        public DateTime? ActualEndDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string ActualEndNoon { get; set; }

    }
}
