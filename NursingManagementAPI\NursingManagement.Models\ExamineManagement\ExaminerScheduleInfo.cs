﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 监考安排表
    /// </summary>
    [Table("ExaminerSchedule")]
    public class ExaminerScheduleInfo : MutiModifyInfo
    {
        /// <summary>
        /// 监考安排ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminerScheduleID { get; set; }
        /// <summary>
        /// 计划日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }
        /// <summary>
        /// 计划开始时间
        /// </summary>
        public TimeSpan ScheduleStartTime { get; set; }
        /// <summary>
        /// 计划结束时间
        /// </summary>
        public TimeSpan ScheduleEndTime { get; set; }
        /// <summary>
        /// 状态:0未被预约，1已被预约
        /// </summary>
        [Column(TypeName = "varchar(50")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 考核地点
        /// </summary>
        [Column(TypeName = "nvarchar(200")]
        public string Location { get; set; }
    }
}