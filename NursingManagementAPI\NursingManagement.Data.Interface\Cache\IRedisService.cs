﻿namespace NursingManagement.Data.Interface
{
    public interface IRedisService
    {
        /// <summary>
        /// 取得缓存数据
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<T> GetAsync<T>(string key);


        /// <summary>
        /// 获取或者写入缓存，传参Language
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSeconds">多少秒后到期</param>
        /// <param name="language"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, int language, Func<int, Task<T>> datas);

        /// <summary>
        /// 获取或者写入缓存，传参hospitalID
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSeconds">多少秒后到期/param>
        /// <param name="hospitalID"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, string hospitalID, Func<string, Task<T>> datas);
        /// <summary>
        /// 获取或者新增缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSeconds">多少秒后到期</param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, Func<Task<T>> datas);
        /// <summary>
        ///  获取或者写入缓存，传参Language,传参hospitalID
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSeconds">多少秒后到期</param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, string hospitalID, int language, Func<string, int, Task<T>> datas);
        /// <summary>
        /// 增加緩存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSeconds">多少秒后到期</param>
        /// <param name="value"></param>
        /// <returns></returns>
        Task<bool> Add<T>(string key, int expirationSeconds, T value);

        /// <summary>
        /// 移除緩存
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<bool> Remove(string key);

        /// <summary>
        /// 更新緩存
        /// </summary>
        /// <param name="key"></param>
        /// <param name="expirationSeconds">多少秒后到期</param>
        /// <param name="value"></param>
        /// <returns></returns>
        Task<bool> Update<T>(string key, int expirationSeconds, T value);

        /// <summary>
        /// 验证是否存在
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<bool> Exists(string key);
        /// <summary>
        /// 清除系统所有字典缓存
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<(bool, string)> RemoveCacheByNameAsync(string cacheName, string hospitalID, int language);

        /// <summary>
        /// 获取所有缓存清单
        /// </summary>
        /// <returns></returns>
        List<string> GetDateBaseKeys();
 
        /// <summary>
        /// 预加载所有缓存
        /// </summary>
        /// <returns></returns>
        Task<bool> PreloadingAllCache(string hospitalID);
    }
}
