﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 动态表单主表
    /// </summary>
    [Serializable]
    [Table("DynamicFormRecord")]
    public class DynamicFormRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 动态表单ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string DynamicFormRecordID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 表单名称
        /// </summary>
        [Column(TypeName = "nvarchar(300)")]
        public string FormName { get; set; }
        /// <summary>
        /// 表单类型 1:问卷表单；2:试卷表单
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string FormType { get; set; }
        /// <summary>
        /// 表单尺寸：small、default、large
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Size { get; set; }
        /// <summary>
        /// 表单栅格布局列数，用于计算内部组件宽度做参考
        /// </summary>
        public int Column { get; set; }
        /// <summary>
        /// 标题宽度
        /// </summary>
        public int LabelWidth { get; set; }
        /// <summary>
        /// 标题显示位置：top、left、right
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string LabelPosition { get; set; }
    }
}
