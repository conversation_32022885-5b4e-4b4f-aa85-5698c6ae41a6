﻿namespace NursingManagement.ViewModels.HierarchicalQC
{
    public class QCSubjectSearchView
    {
        /// <summary>
        /// 查询数据日期(年，月)
        /// </summary>
        public string YearMonth { get; set; }

        /// <summary>
        /// 考核科室
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 考核科室
        /// </summary>
        public int? QcDepartmentID { get; set; }

        /// <summary>
        /// 质控主题主键
        /// </summary>
        public string HierarchicalQCFormLevel { get; set; }

        /// <summary>
        /// 质控主题类型
        /// </summary>
        public string FormType { get; set; }

        /// <summary>
        /// 主题ID
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }

        /// <summary>
        /// 质控分数
        /// </summary>
        public int? Score { get; set; }

        /// <summary>
        /// 质控人员
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 是否为人员质控
        /// </summary>
        public bool? IsQcEmployeeFlag { get; set; }

        /// <summary>
        /// 质控科室主题类型
        /// </summary>
        public static List<string> QCDepartmentFormType = new List<string> { "1", "2", "3", "4", "5" };

        /// <summary>
        /// 质控人员主题类型
        /// </summary>
        public static List<string> QCEmployeeFormType = new List<string> { "6" };

        /// <summary>
        /// 开始月份
        /// </summary>
        public string YearMonthrangeStart { get; set; }

        /// <summary>
        /// 结束月份
        /// </summary>
        public string YearMonthrangeEnd { get; set; }

        /// <summary>
        /// 组织类型
        /// </summary>
        public string OrganizationType { get; set; }
    }
}
