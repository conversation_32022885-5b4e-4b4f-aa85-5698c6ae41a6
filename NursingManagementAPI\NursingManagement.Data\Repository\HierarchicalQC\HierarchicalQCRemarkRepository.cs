﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCRemarkRepository : IHierarchicalQCRemarkRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;

        public HierarchicalQCRemarkRepository(
            NursingManagementDbContext dbContext
            , SessionCommonServer sessionCommonServer
        )
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<HierarchicalQCRemarkView>> GetQCRemarkViewByAssessListIDAsync(int assessListID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _dbContext.HierarchicalQCRemarkInfos.Where(m => m.HierarchicalQCAssessListID == assessListID
                                && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCRemarkView
                {
                    HQCAssessListID = m.HierarchicalQCAssessListID,
                    HQCRemarkID = m.HierarchicalQCRemarkID,
                    Remark = m.Remark
                }).ToListAsync();
        }

        public async Task<List<HierarchicalQCRemarkView>> GetQCRemarkViewByAssessListIDsAsync(List<int> assessListIDs)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _dbContext.HierarchicalQCRemarkInfos.Where(m => assessListIDs.Contains(m.HierarchicalQCAssessListID) && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCRemarkView
                {
                    HQCAssessListID = m.HierarchicalQCAssessListID,
                    HQCRemarkID = m.HierarchicalQCRemarkID,
                    Remark = m.Remark
                }).ToListAsync();
        }

        public async Task<HierarchicalQCRemarkInfo> GetQCRemarkViewAsync(string remarkID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _dbContext.HierarchicalQCRemarkInfos.Where(m => m.HierarchicalQCRemarkID == remarkID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

    }
}