﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/QuarterPlanMaintain")]
    [ApiController]
    [EnableCors("any")]
    public class QuarterPlanMaintainController : ControllerBase
    {
        private readonly ISessionService _session;
        private readonly IQuarterPlanMaintainService _quarterPlanMaintainService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="session"></param>
        /// <param name="quarterPlanMaintainService"></param>
        public QuarterPlanMaintainController(ISessionService session, IQuarterPlanMaintainService quarterPlanMaintainService)
        {
            _session = session;
            _quarterPlanMaintainService = quarterPlanMaintainService;
        }

        #region 季度计划表增删改查
        /// <summary>
        /// 保存季度计划工作内容
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveQuarterWorks")]
        public async Task<IActionResult> SaveQuarterWorks(QpWorksSaveView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.Set(session);
            result.Data = await _quarterPlanMaintainService.SaveQuarterWorks(view);
            return result.ToJson();
        }

        /// <summary>
        /// 删除工作
        /// </summary>
        /// <param name="quarterPlanDetailID">主键</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteQuarterWork")]
        public async Task<IActionResult> DeleteQuarterWork(string quarterPlanDetailID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.DeleteQuarterWork(quarterPlanDetailID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 更新季度计划工作内容
        /// </summary>
        /// <param name="workView">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateQuarterWork")]
        public async Task<IActionResult> UpdateQuarterWork(TieredPlanWork workView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.UpdateQuarterWork(workView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 发布季度计划
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("PublishQuarterPlan")]
        public async Task<IActionResult> PublishQuarterPlan(string quarterPlanMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.PublishQuarterPlan(quarterPlanMainID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取季度计划状态
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuarterPlanStatus")]
        public async Task<IActionResult> GetQuarterPlanStatus(string quarterPlanMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.GetQuarterPlanStatus(quarterPlanMainID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取季度计划状态
        /// </summary>
        /// <param name="annualPlanMainID">季度计划主表ID</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuarterPlanMainID")]
        public async Task<IActionResult> GetQuarterPlanMainID(string annualPlanMainID, int quarter)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.GetQuarterPlanMainID(annualPlanMainID, quarter);
            return result.ToJson();
        }

        /// <summary>
        /// 查询某科室的某季度计划
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuarterWorks")]
        public async Task<IActionResult> GetQuarterWorks(string annualPlanMainID, string quarterPlanMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.GetQuarterWorks(annualPlanMainID, quarterPlanMainID);
            return result.ToJson();
        }
        #endregion

        #region 参考、导入逻辑
        /// <summary>
        /// 获取可导入的工作
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="annual">年度</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCanImportQpWorksGroupByPlanThenType")]
        public async Task<IActionResult> GetCanImportQpWorksGroupByPlanThenType(string annualPlanMainID, string quarterPlanMainID, int departmentID, int annual, int quarter)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.GetCanImportQpWorksGroupByPlanThenType(annualPlanMainID, quarterPlanMainID, departmentID, annual, quarter);
            return result.ToJson();
        }
        /// <summary>
        /// 获取上级部门季度计划可选工作
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="annual">年度</param>
        /// <param name="apInterventionID">指定执行项目字典ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUpperDeptQpWorksByPlanThenTypeList")]
        public async Task<IActionResult> GetUpperDeptQpWorksByPlanThenTypeList(int annual, int departmentID, int? apInterventionID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.GetUpperDeptQpWorksByPlanThenTypeList(annual, departmentID, apInterventionID);
            return result.ToJson();
        }
        /// <summary>
        /// 批量导入保存
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveImportWorks")]
        public async Task<IActionResult> SaveImportWorks(QpWorksSaveView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.Set(session);
            result.Data = await _quarterPlanMaintainService.SaveImportWorks(view);
            return result.ToJson();
        }
        #endregion
        #region 预览
        /// <summary>
        /// 查询本人及上下级已制定的季度计划
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBrowseQPViews")]
        public async Task<IActionResult> GetBrowseQPViews(int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.GetBrowseQPViews(year, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 预览季度计划
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuarterPlanPreview")]
        public async Task<IActionResult> GetQuarterPlanPreview(string quarterPlanMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _quarterPlanMaintainService.GetQuarterPlanPreview(quarterPlanMainID);
            return result.ToJson();
        }
        #endregion
    }
}
