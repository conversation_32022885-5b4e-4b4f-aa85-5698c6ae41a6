﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDynamicFormDetailAttributeRepository : ICacheRepository
    {
        /// <summary>
        /// 根据表单明细ID获取明细属性集合
        /// </summary>
        /// <param name="dynamicFormDetailID"></param>
        /// <returns></returns>
        Task<List<DynamicFormDetailAttributeInfo>> GetDetailAttributeListByDetailID(string dynamicFormRecordID,string dynamicFormDetailID); 

        /// <summary>
        /// 根据dynamicFormRecordID，获取表单属性清单
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns></returns>
        Task<List<DynamicFormDetailAttributeInfo>> GetDetailAttributeListByRecordID(string dynamicFormRecordID);

        /// <summary>
        /// 根据条件更新缓存
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task UpdateCacheByQuery(Dictionary<string, object> query);
    }
}
