﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class RemainingRestDaysRepository : IRemainingRestDaysRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        public RemainingRestDaysRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        public async Task<List<RemainingRestDaysInfo>> GetAllRemainingRestDaysByDepartmentID(int departmentID)
        {
            return await _nursingManagementDbContext.RemainingRestDaysInfos.Where(m => m.DepartmentID == departmentID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<RemainingRestDaysView>> GetRemainingRestDaysByDepartmentID(int departmentID, int year)
        {
            var datas = await GetAllRemainingRestDaysByDepartmentID(departmentID);
            return datas.Where(m => m.Year == year).Select(m => new RemainingRestDaysView
            {
                RemainingRestDaysID = m.RemainingRestDaysID,
                HospitalID = m.HospitalID,
                DepartmentID = m.DepartmentID,
                EmployeeID = m.EmployeeID,
                Year = m.Year,
                Month = m.Month,
                Days = m.Days,
            }).ToList();
        }
       
        public async Task<Dictionary<string, decimal?>> GetEmployeeRestDaysDict(int departmentID, int year, int month)
        {
            var datas = await GetRemainingRestDaysByDepartmentID(departmentID, year);
            return datas.Where(m => m.Month == month).ToDictionary(m => m.EmployeeID, m => m.Days);
        }
    }
}
