using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 质控主记录
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCRecord")]
    public class HierarchicalQCRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主记录主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCRecordID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 质控主题主表主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCSubjectID { get; set; }
        /// <summary>
        /// 质控表单序号
        /// </summary>
        public int HierarchicalQCFormID { get; set; }
        /// <summary>
        /// 质控人（多位||）
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string HierarchicalQCEmployID { get; set; }
        /// <summary>
        /// 质控部门，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 审核人
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string VerifierEmployeeID { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 首次质控分数
        /// </summary>
        public decimal? FirstResult { get; set; }
        /// <summary>
        /// 末次质控分数
        /// </summary>
        public decimal? LastResult { get; set; }
        /// <summary>
        /// 质控对象标识ID 比如部门、个人
        /// </summary>
        public int QCObjectID { get; set; }
    }
}