﻿namespace NursingManagement.ViewModels
{
    public class AnnualScheduleParamsView
    {
        /// <summary>
        /// 年度计划排程主键ID
        /// </summary>
        public string ScheduleMainID { get; set; }
        /// <summary>
        /// 执行内容
        /// </summary>
        public string PerformComment { get; set; }
        /// <summary>
        /// 排程执行项目明细
        /// </summary>
        public List<AnnualScheduleDetailView> ScheduleDetails { get; set; }

        /// <summary>
        /// 排程的实际执行时间.
        /// </summary>
        /// <value>
        /// Nullable 包含日期和时间
        /// </value>
        public DateTime PerformDateTime { get; set; }
        /// <summary>
        /// 延迟执行原因分类
        /// </summary>
        public string Reason { get; set; }
        /// <summary>
        /// 延迟执行备注
        /// </summary>
        public string DelayContent { get; set; }
    }
}
