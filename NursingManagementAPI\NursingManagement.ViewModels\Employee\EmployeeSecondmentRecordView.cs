﻿namespace NursingManagement.ViewModels
{
    public class EmployeeSecondmentRecordView
    {
        /// <summary>
        /// 人员借调记录号，主键，自增
        /// </summary>
        public string EmployeeSecondmentRecordID { get; set; }
        /// <summary>
        /// HR员工编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 部门编号
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 借调部门名称
        /// </summary>
        public string SecondmentDepartmentName { get; set; }
        /// <summary>
        /// 借调部门编号
        /// </summary>
        public int SecondmentDepartmentID { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 用户工号
        /// </summary>
        public string UserID { get; set; }
        /// <summary>
        /// 借调目的
        /// </summary
        public string SecondmentPurpose { get; set; }
        /// <summary>
        /// 借调目的名称
        /// </summary
        public string SecondmentPurposeName { get; set; }
        /// <summary>
        /// 借调类型
        /// </summary>
        public string SecondmentType { get; set; }
        /// <summary>
        /// 借调类型
        /// </summary>
        public string SecondmentTypeName { get; set; }
        /// <summary>
        /// 开始午别
        /// </summary>
        public string StartNoon { get; set; }
        /// <summary>
        /// 开始午别
        /// </summary>
        public string StartNoonName { get; set; }
        /// <summary>
        /// 结束午别
        /// </summary
        public string EndNoon { get; set; }
        /// <summary>
        /// 结束午别
        /// </summary
        public string EndNoonName { get; set; }
        /// <summary>
        /// 姓名拼音
        /// </summary
        public string NamePinyin { get; set; }
        /// <summary>
        /// 借调天数
        /// </summary>
        public decimal? SecondmentDays { get; set; }
        /// <summary>
        /// 状态 ：申请提交、审核中、审批通过、审批未通过
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 审批人ID
        /// </summary>
        public string ApproveEmployeeID{ get; set; }
        /// <summary>
        /// 审批人
        /// </summary>
        public string ApproveEmployeeName { get; set; }
        /// <summary>
        /// 实际结束日期
        /// </summary>
        public DateTime? ActualEndDate { get; set; }
        /// <summary>
        /// 实际结束午别
        /// </summary>
        public string ActualEndNoon { get; set; }
        /// <summary>
        /// 实际结束午别
        /// </summary>
        public string ActualEndNoonName { get; set; }
        /// <summary>
        /// 提前结束标志
        /// </summary>
        public bool EarlyClosureFlag { get; set; }

        /// <summary>
        /// 审批标记
        /// </summary>
        public bool ApproveFlag { get; set; }
    }
}
