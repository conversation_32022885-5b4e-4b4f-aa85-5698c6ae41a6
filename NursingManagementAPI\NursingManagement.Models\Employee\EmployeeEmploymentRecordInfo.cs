﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员任职记录（本公司）
    /// </summary>
    [Serializable]
    [Table("EmployeeEmploymentRecord")]
    public class EmployeeEmploymentRecordInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeEmploymentRecordID { get; set; }
        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 任职开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }
        /// <summary>
        /// 任职结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        /// 任职部门
        /// </summary>
        public int? DepartmentID { get; set; }
        /// <summary>
        /// 任职岗位
        /// </summary>
        public int? Post { get; set; }
        /// <summary>
        /// 任职职务
        /// </summary>
        public int? Title { get; set; }
        /// <summary>
        /// 任职类型 1.正式任职 2.兼职任职 3.社会任职
        /// </summary>
        public byte PostType { get; set; }
        /// <summary>
        /// 标题名
        /// </summary>
        public string TitleName { get; set; }
    }
}