using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 质控明细记录
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCDetail")]
    public class HierarchicalQCDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 质控维护明细主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCDetailID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 质控主记录主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCRecordID { get; set; }
        /// <summary>
        /// 质控维护记录主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCMainID { get; set; }
        /// <summary>
        /// 质控表单序号
        /// </summary>
        public int HierarchicalQCFormID { get; set; }
        /// <summary>
        /// 质控结果
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string Result { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 配置项 对应HierarchicalQCAssessList表主键
        /// </summary>
        public int HierarchicalQCAssessListID { get;set; }
        /// <summary>
        /// 考核日期
        /// </summary>
        public DateTime? AssessDate { get; set; }
        /// <summary>
        /// 问题
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string Problem { get; set; }
        /// <summary>
        /// 亮点
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string BrightSpot { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string Remark { get; set; }
        /// <summary>
        /// 分组ID
        /// </summary>
        public int? GroupID { get; set; }
        /// <summary>
        /// 父级ID
        /// </summary>
        public int? ParentID { get; set; }
        /// <summary>
        /// 满分
        /// </summary>
        public int? FullMark { get; set; }
    }
}
