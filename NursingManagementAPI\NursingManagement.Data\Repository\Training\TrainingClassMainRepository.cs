﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class TrainingClassMainRepository : ITrainingClassMainRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public TrainingClassMainRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 获取培训群组列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<TrainingClassMainInfo>> GetTrainingClassViewList()
        {
            return await _dbContext.TrainingClassMainInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据trainingClassMainID获取培训群组数据
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        public async Task<TrainingClassMainInfo> GetListByMainID(string trainingClassMainID)
        {
            return await _dbContext.TrainingClassMainInfos.Where(m => m.DeleteFlag != "*" && m.TrainingClassMainID == trainingClassMainID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取培训群组下拉选项集合
        /// </summary>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetTrainClassOptions()
        {
            return await _dbContext.TrainingClassMainInfos.Where(m => m.DeleteFlag != "*" ).
                Select(m=>new SelectOptionsView { 
                    Label = m.TrainingClassName,
                    Value = m.TrainingClassMainID
                }).ToListAsync();
        }
    }
}