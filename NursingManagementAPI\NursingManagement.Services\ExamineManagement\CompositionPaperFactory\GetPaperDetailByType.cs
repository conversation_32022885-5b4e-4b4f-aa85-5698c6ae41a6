﻿using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services.Examine
{
    /// <summary>
    /// 根据题目类型规则和题目难度等级规则筛选题目数据
    /// </summary>
    public class GetPaperDetailByType : CompositionPaper
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IExaminationQuestionDetailRepository _examinationQuestionDetailRepository;
        public GetPaperDetailByType(
            IExaminationQuestionDetailRepository examinationQuestionDetailRepository
            )
        {
            _examinationQuestionDetailRepository = examinationQuestionDetailRepository;
        }

        /// <summary>
        /// 根据题目类型规则和题目难度等级规则筛选题目数据
        /// </summary>
        /// <param name="questionType">题型</param>
        /// <param name="examinationQuestionList"></param>
        /// <param name="paperCompositionRuleView"></param>
        /// <param name="choicePaperQuestionViewList"></param>
        /// <param name="examinationPaperMainData"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        public override async Task<List<PaperQuestionView>> FilterExaminationQuestion(string questionType, List<ExaminationQuestionInfo> examinationQuestionList, PaperCompositionRuleView paperCompositionRuleView, List<PaperQuestionView> choicePaperQuestionViewList, ExaminationPaperMainInfo examinationPaperMainData, string modifyEmployeeID)
        {
            var difficultyRatio = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.DifficultyPercentage;
            var questionBankConditionRules = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.QuestionFilterConditionRules;
            // 选择的题目
            var selectedQuestions = new List<PaperQuestionView>();
            // 筛选题型
            var currBankQuestions = examinationQuestionList.Where(m => m.ExaminationQuestionType == questionType && !choicePaperQuestionViewList.Any(choice => choice.QuestionID == m.ExaminationQuestionID.ToString())).ToList();
            if (currBankQuestions.Count == 0)
            {
                return [];
            }
            foreach (var questionBankConditionRule in questionBankConditionRules)
            {
                var questionFilter = questionBankConditionRule.QuestionFilters.Find(m => m.QuestionType == questionType);
                if (questionFilter == null)
                {
                    continue;
                }
                var questionCount = questionFilter.DynamicFilterCount;
                if (questionCount <= 0)
                {
                    continue;
                }
                // 按照题库分组
                var groupQuestions = currBankQuestions.Where(m => m.QuestionBankID == questionBankConditionRule.QuestionBankID)
                    .GroupBy(m => m.DifficultyLevel).ToDictionary(g => g.Key, g => g.ToList());
                // 按照难易程度选题
                var tempSelected = SelectQuestionByCountAndDifficultLevel(groupQuestions, questionCount, difficultyRatio, currBankQuestions, questionBankConditionRule.QuestionBankID);
                if (tempSelected.Count <= 0)
                {
                    continue;
                }
                var questionIDList = tempSelected.Select(m => m.ExaminationQuestionID).ToList();
                var questionDetailList = await _examinationQuestionDetailRepository.GetListByQuestionIDs(questionIDList, true);
                if (questionDetailList.Count <= 0)
                {
                    continue;
                }
                // 记录分数
                var transferQuestions = tempSelected.Select(m => new PaperQuestionView
                {
                    QuestionBankID = m.QuestionBankID,
                    QuestionID = m.ExaminationQuestionID.ToString(),
                    QuestionTitle = m.QuestionContent,
                    Analysis = m.Analysis,
                    QuestionType = m.ExaminationQuestionType,
                    ComponentListID = m.ComponentListID,
                    Score = questionFilter.Score,
                    FixedItemID = m.ExaminationQuestionID.ToString(),
                    ItemSourceType = "ExaminationQuestion",
                    Options = SortQuestionOption(m, questionDetailList).Select(detail => new PaperQuestionOptionsView
                    {
                        Value = detail.ExaminationQuestionDetailID,
                        Label = detail.Content,
                        ItemSourceType = "ExaminationQuestionDetail",
                    }).ToList()
                }).ToList();
                selectedQuestions.AddRange(transferQuestions);
                currBankQuestions.RemoveAll(m => tempSelected.Exists(n => n.ExaminationQuestionID == m.ExaminationQuestionID));
            }
            if (selectedQuestions.Count <= 0)
            {
                return [];
            }
            return selectedQuestions;
        }

        ///// <summary>
        ///// 添加序号
        ///// </summary>
        ///// <param name="sort"></param>
        ///// <param name="selectedQuestions"></param>
        ///// <returns></returns>
        //private List<PaperQuestionView> InsertDymaicFormData(int sort, List<PaperQuestionView> selectedQuestions)
        //{
        //    var newSort = sort;
        //    foreach (var question in selectedQuestions)
        //    {
        //        question.Sort = newSort;
        //        newSort += 1;
        //    }
        //    return selectedQuestions;
        //}

        /// <summary>
        /// 根据难易程度、题目数量 从指定的（题库、题型）中 筛选题目
        /// </summary>
        /// <param name="groupQuestions">按照难度分组的题目（指定题库、题型）</param>
        /// <param name="requireCount">当前题库中要求的题型数量</param>
        /// <param name="ratios">题目难度比率</param>
        /// <param name="currBankQuestions">所有选择的题库中 -特定题型的数量（在方法外已经按照题型筛选）</param>
        /// <param name="questionBankID">题库ID</param>
        /// <returns></returns>
        private List<ExaminationQuestionInfo> SelectQuestionByCountAndDifficultLevel(Dictionary<string, List<ExaminationQuestionInfo>> groupQuestions, int requireCount, Dictionary<string, decimal> ratios, List<ExaminationQuestionInfo> currBankQuestions, string questionBankID)
        {
            var selectedQuestions = new List<ExaminationQuestionInfo>();
            foreach (var ratio in ratios.OrderBy(r => r.Key))
            {
                // 如果该难易程度没有题目，跳过
                if (!groupQuestions.ContainsKey(ratio.Key))
                {
                    continue;
                }
                // 可以筛选的题目数量
                var availableQuestion = groupQuestions[ratio.Key];
                int toSelect = (int)Math.Round(requireCount * ratio.Value);
                if (toSelect > availableQuestion.Count)
                {
                    _logger.Warn($"有{availableQuestion.Count} 道 {ratio.Key} 难度的题目，数量不足,需要{toSelect}道");
                    toSelect = availableQuestion.Count;
                }
                // 随机取题
                var selectedQuestion = availableQuestion.OrderBy(q => Guid.NewGuid()).Take(toSelect).ToList();
                selectedQuestions.AddRange(selectedQuestion);
            }
            //题目数量不足
            var diffCount = requireCount - selectedQuestions.Count;
            if (diffCount > 0)
            {
                // 当题库》题型》难易程度 不能满足条件，从题库中筛选题目，满足要求的题目数量（优先从当前题库查找，没有的话随机在其他选择的题库中查找）
                var diffQuestions = currBankQuestions.Where(m =>
                !selectedQuestions.Exists(q => q.ExaminationQuestionID == m.ExaminationQuestionID))
                    .OrderBy(m => m.QuestionBankID == questionBankID ? 0 : 1).ThenBy(m => Guid.NewGuid()).Take(diffCount).ToList();

                selectedQuestions.AddRange(diffQuestions);
            }
            // 随机移除多余题目
            if (diffCount < 0)
            {
                for (var i = 0; i < -diffCount; i++)
                {
                    Random rand = new();
                    var removeIndex = rand.Next(0, selectedQuestions.Count);
                    selectedQuestions.RemoveAt(removeIndex);
                }
            }
            return selectedQuestions;
        }
    }
}
