﻿namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCAssessListRepository : ICacheRepository
    {
        /// <summary>
        /// 根据内容获取ID
        /// </summary>
        /// <param name="contentName"></param>
        /// <returns></returns>
        Task<int?> GetIDByContentName(string contentName);
        /// <summary>
        /// 获取一个新的ID
        /// </summary>
        /// <returns></returns>
        Task<int> GetNewID();
        /// <summary>
        /// 获取评估字典键值对
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<int, string>> GetAssessDictionaries();
    }
}
