﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 培训群组控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/TrainingClass")]
    [EnableCors("any")]
    public class TrainingClassController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly ITrainingClassService _trainingClassService;

        /// <summary>
        /// 培训群组构造器
        /// </summary>
        /// <param name="session"></param>
        /// <param name="trainingClassService"></param>
        public TrainingClassController(ISessionService session
            , ITrainingClassService trainingClassService)
        {
            _session = session;
            _trainingClassService = trainingClassService;
        }

        /// <summary>
        /// 获取培训群组列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTrainingClassList")]
        public async Task<IActionResult> GetTrainingClassList()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingClassService.GetTrainingClassList(session);
            return result.ToJson();
        }

        /// <summary>
        /// 保存培训群组
        /// </summary>
        /// <param name="trainingClassView">培训群组信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveTrainingClass")]
        public async Task<IActionResult> SaveTrainingClass([FromBody] TrainingClassView trainingClassView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingClassService.SaveTrainingClass(trainingClassView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 删除培训群组
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteTrainingClassByID")]
        public async Task<IActionResult> DeleteTrainingClassByID(string trainingClassMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingClassService.DeleteTrainingClassByID(trainingClassMainID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 根据培训群组ID获取培训群组课程列表
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTrainingClassCourseListByMainID")]
        public async Task<IActionResult> GetTrainingClassCourseListByMainID(string trainingClassMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingClassService.GetTrainingClassCourseListByMainID(trainingClassMainID);
            return result.ToJson();
        }


        /// <summary>
        /// 获取培训群组选择器options数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTrainClassOptions")]
        public async Task<IActionResult> GetTrainClassOptions()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingClassService.GetTrainClassOptions();
            return result.ToJson();
        }
    }
}