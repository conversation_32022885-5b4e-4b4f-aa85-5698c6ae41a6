﻿using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;

namespace NursingManagement.Services.ApproveManagement
{
    public class ProcessingAfterApprovalFactory
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IServiceProvider _serviceProvider;
        private readonly IApproveProcessService _approveProcessService;

        public ProcessingAfterApprovalFactory(IServiceProvider serviceProvider,
            IApproveProcessService approveProcessService
            )
        {
            _serviceProvider = serviceProvider;
            _approveProcessService = approveProcessService;
        }
        /// <summary>
        /// 切换工厂方法
        /// </summary>
        /// <param name="approveProcessID"></param>
        /// <returns></returns>
        /// <example>
        /// 添加审批流程对应后续处理工厂，请参考 <see cref="ScheduleRequestAfterApproval"/> 类.
        /// </example>
        public async Task<ICommonProcessingAfterApproval> SwitchProcessingAsync(string approveProcessID)
        {
            var api = await _approveProcessService.GetApiByProcessID(approveProcessID);
            if (string.IsNullOrEmpty(api))
            {
                _logger.Info("无api配置，工厂结束");
                return null;
            }
            var type = Type.GetType("NursingManagement.Services." + api);
            if (type == null)
            {
                _logger.Error($"配置的api工厂类无法获取,API=[{api}]");
                return null;
            }
            var service = _serviceProvider.GetService(type);
            if (service == null)
            {
                _logger.Error($"获取service失败【{type.FullName}】");
            }
            return (ICommonProcessingAfterApproval)service;
        }

    }
}
