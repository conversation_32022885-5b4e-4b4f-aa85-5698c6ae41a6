﻿using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 培训记录视图
    /// </summary>
    public class TrainingRecordView
    {
        /// <summary>
        /// 培训记录ID(主键ID)
        /// </summary>
        public string TrainingRecordID { get; set; }
        /// <summary>
        /// 课程ID集合
        /// </summary>
        public List<string> CourseSettingIDArr { get; set; }
        /// <summary>
        /// 课程名称
        /// </summary>
        public string CourseSettingName { get; set; }
        /// <summary>
        /// 培训地点
        /// </summary>
        public string TrainingLocation { get; set; }
        /// <summary>
        /// 培训地点名称
        /// </summary>
        public string TrainingLocationName { get; set; }
        /// <summary>
        /// 培训方式
        /// </summary>
        public string TrainingMethod { get; set; }
        /// <summary>
        /// 培训方式名称
        /// </summary>
        public string TrainingMethodName { get; set; }
        /// <summary>
        /// 培训内容
        /// </summary>
        public string TrainingContent { get; set; }
        /// <summary>
        /// 培训目标
        /// </summary>
        public string TrainingTarget { get; set; }
        /// <summary>
        /// 培训讲师
        /// </summary>
        public string TrainingLecturer { get; set; }
        /// <summary>
        /// 培训讲师名字
        /// </summary>
        public string TrainingLecturerName { get; set; }
        /// <summary>
        /// 培训主持人
        /// </summary>
        public string TrainingHost { get; set; }
        /// <summary>
        /// 培训主持人名字
        /// </summary>
        public string TrainingHostName { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartDateTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDateTime { get; set; }
        /// <summary>
        /// 培训状态
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }
        /// <summary>
        /// 考核主记录ID
        /// </summary>
        public string ExaminationRecordID { get; set; }
        /// <summary>
        /// 培训评价问卷ID，关联评价问卷ID（DynamicFormRecord表）
        /// </summary>
        public string EvaluationID { get; set; }
        /// <summary>
        /// 护士长评价问卷ID，关联评价问卷ID（DynamicFormRecord表）
        /// </summary>
        public string HeadNurseEvaluationID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 异动人
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 条件内容
        /// </summary>
        public string SignUpConditionContent { get; set; }
        /// <summary>
        ///  条件表达式
        /// </summary>
        public string SignUpConditionExpression { get; set; }
        /// <summary>
        ///  报名条件选择内容
        /// </summary>
        public List<FormDetailConditionView> SignUpConditions { get; set; }
        /// <summary>
        ///  部门ID集合
        /// </summary>
        public List<int> DepartmentIDs { get; set; }
        /// <summary>
        /// 文件集合
        /// </summary>
        public List<IFormFile> Files { get; set; }
        /// <summary>
        /// 已上传文件信息
        /// </summary>
        public List<FileUploadReturnView> FileInfoList { get; set; }
        /// <summary>
        /// 培训群组主表ID
        /// </summary>
        public string TrainingClassMainID { get; set; }
        /// <summary>
        /// 是否需要签到标记
        /// </summary>
        public bool SignInFlag { get; set; }
        /// <summary>
        /// 二维码刷新时间（单位：秒），0不刷新
        /// </summary>
        public int QRCodeRefreshTime { get; set; }
    }
}
