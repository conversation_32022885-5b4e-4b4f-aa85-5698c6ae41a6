﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员护士层级
    /// </summary>
    [Serializable]
    [Table("EmployeeCapabilityLevel")]
    public class EmployeeCapabilityLevelInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeCapabilityLevelID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 层级ID
        /// </summary>
        public int? CapabilityLevelID { get; set; }

        /// <summary>
        /// 晋级日期
        /// </summary>
        public DateTime? PromotionDate { get; set; }

        /// <summary>
        /// 申请编号
        /// </summary>
        public string ApplicationNo { get; set; }
    }
}