﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeDepartmentChangeRequestRepository
    {
        /// <summary>
        /// 获取人员部门申请信息（取未审核的）
        /// </summary>
        /// <param name="employeeIDs">人员ID集合</param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentChangeRequestInfo>> GetListByEmployeeIDs(List<string> employeeIDs,string hospitalID);
        /// <summary>
        /// 根据部门ID获取人员部门变更记录
        /// </summary>
        /// <param name="hospitalID">医院编码</param>
        /// <param name="requestStartDate">申请开始时间</param>
        /// <param name="requestEndDate">申请结束时间</param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentChangeView>> GetEmployeeDepartmentChangeRequestViews(string hospitalID, DateTime requestStartDate, DateTime requestEndDate);
        /// <summary>
        /// 根据主键ID获取申请记录
        /// </summary>
        /// <param name="dataID">主键ID</param>
        /// <returns></returns>
        Task<EmployeeDepartmentChangeRequestInfo> GetEmployeeDepartmentChangeRequestByID(string dataID);
        /// <summary>
        /// 根据主键获取调岗申请记录
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentChangeRequestInfo>> GetRecordsByIDsAsNoTrackAsync(List<string> recordIDs);
    }
}
