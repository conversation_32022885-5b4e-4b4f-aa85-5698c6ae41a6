﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IAnnualScheduleDetailRepository
    {
        /// <summary>
        /// 获取年度计划排程明细
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <param name="asNoTrack"></param>
        /// <returns>List<AnnualScheduleDetailInfo></returns>
        Task<List<AnnualScheduleDetailInfo>> GetAnnualScheduleDetailsAsync(string scheduleMainID, bool asNoTrack);
        /// <summary>
        /// 获取年度计划排程明细
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        Task<List<AnnualScheduleDetailView>> GetAnnualScheduleDetailViewsAsync(string scheduleMainID);

    }
}
