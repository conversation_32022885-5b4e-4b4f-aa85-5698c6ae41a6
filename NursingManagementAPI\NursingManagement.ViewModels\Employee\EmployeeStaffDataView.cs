﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class EmployeeStaffDataView : EmployeeStaffDataInfo
    {
        /// <summary>
        /// 工作性质名称(10长期聘用;20正式;30享正;40临时聘用)
        /// </summary>
        public string JobCategory { get; set; }

        /// <summary>
        /// 员工状态名称(1、在职、2、离职)
        /// </summary>
        public string StaffStatusName { get; set; }

        /// <summary>
        /// 当前部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 人员级别
        /// </summary>
        public string EmployeeLevelName { get; set; }
    }
}
