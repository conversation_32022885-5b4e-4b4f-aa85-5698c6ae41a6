﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("RuleDetailList")]
    public class RuleDetailListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 规则属性明细表ID（非自增)
        /// </summary>
        public int RuleDetailListID { get; set; }
        /// <summary>
        /// 规则字典主表ID
        /// </summary>
        public int RuleListID { get; set; }
        /// <summary>
        /// 内容
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Value { get; set; }
        /// <summary>
        /// 语言序号
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
    }
}
