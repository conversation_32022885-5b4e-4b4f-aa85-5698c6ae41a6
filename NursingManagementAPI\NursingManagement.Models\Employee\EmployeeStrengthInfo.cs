﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 个人特长
    /// </summary>
    [Serializable]
    [Table("EmployeeStrength")]
    public class EmployeeStrengthInfo : MutiModifyInfo
    {
        /// <summary>
        /// 表主键
        /// </summary>
        [Key]
        public string EmployeeStrengthID { get; set; }
        /// <summary>
        /// 员工工号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 特长
        /// </summary>
        public string StrengthID { get; set; }
        /// <summary>
        /// 荣誉
        /// </summary>
        public string Honors { get; set; }
    }
}
