using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 角色表
    /// </summary>
    [Serializable]
    [Table("AuthorityRole")]
    public class AuthorityRoleInfo : MutiModifyInfo
    {

        /// <summary>
        /// 角色权限序号
        /// </summary>
        public int AuthorityRoleID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "verchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言序号
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        [Column(TypeName = "nverchar(50)")]
        public string RoleName { get; set; }
    }
}
