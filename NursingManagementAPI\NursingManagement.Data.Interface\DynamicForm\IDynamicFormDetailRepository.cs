﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IDynamicFormDetailRepository : ICacheRepository
    {
        /// <summary>
        /// 根据表单ID获取表单明细集合
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns></returns>
        Task<List<DynamicFormDetailInfo>> GetFormDetailListByFormRecordID(string dynamicFormRecordID);
        /// <summary>
        /// 根据FormRecordID获取FormDetailID集合
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns></returns>
        Task<List<string>> GetFormDetailIDByFormRecordID(string dynamicFormRecordID);
        /// <summary>
        /// 根据主记录ID和组件类型ID获取数据
        /// </summary>
        /// <param name="dynamicFormRecordID">动态表单ID</param>
        /// <param name="componentListID">组件ID</param>
        /// <param name="orderBy">排序规则</param>
        /// <returns></returns>
        Task<List<string>> GetFormItemIDByFormRecordIDAndComponentListID(string dynamicFormRecordID, int componentListID, Func<IQueryable<DynamicFormDetailInfo>, IOrderedQueryable<DynamicFormDetailInfo>> orderBy = null);
        /// <summary>
        /// 根据FormRecordID获取FormDetailID集合
        /// </summary>
        /// <param name="dynamicFormRecordID">动态表单记录主键</param>
        /// <param name="attributeID">属性字典</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetFormSettingsByFormRecordID(string dynamicFormRecordID, int attributeID);
        /// <summary>
        /// 根据主记录ID集合模板明细数据
        /// </summary>
        /// <param name="dynamicFormRecordID">模板主记录ID</param>
        /// <returns></returns>
        Task<List<DynamicFormView>> GetDynamicFormDetailViewByFormRecordID(string dynamicFormRecordID);
        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="dynamicFormRecordIDs"></param>
        /// <returns></returns>
        Task<List<DynamicFormDetailInfo>> GetFormDetailListByRecordIDList(List<string> dynamicFormRecordIDs);

        /// <summary>
        /// 缓存测试使用
        /// </summary>
        /// <returns></returns>
        Task<List<DynamicFormDetailInfo>> GetAllDynamicFormDetailByTest();
        /// <summary>
        /// 根据itemID获取数据
        /// </summary>
        /// <param name="itemIDs"></param>
        /// <param name="itemSourceType"></param>
        /// <returns></returns>
        Task<List<DynamicFormDetailInfo>> GetFormDetailListByItemIDList(List<string> itemIDs, string itemSourceType);

        /// <summary>
        /// 根据主记录ID查询ItemIDs
        /// </summary>
        /// <param name="recordIDs">动态表单主记录</param>
        /// <returns></returns>
        Task<List<Tuple<string, string>>> GetItemIDsByFormRecordID(List<string> recordIDs);

        /// <summary>
        /// 根据条件更新缓存
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task UpdateCacheByQuery(Dictionary<string, object> query);
    }
}
