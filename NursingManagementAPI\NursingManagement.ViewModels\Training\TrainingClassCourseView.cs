﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 培训群组课程ViewModel
    /// </summary>
    public class TrainingClassCourseView
    {
        /// <summary>
        /// 课程ID
        /// </summary>
        public string CourseSettingID { get; set; }

        /// <summary>
        /// 课程名称
        /// </summary>
        public string CourseName { get; set; }

        /// <summary>
        /// 课程简介
        /// </summary>
        public string CourseIntroduction { get; set; }

        /// <summary>
        /// 课程制定年份
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 课程分类ID
        /// </summary>
        public string CourseTypeID { get; set; }

        /// <summary>
        /// 课程分类
        /// </summary>
        public string CourseTypeName { get; set; }

        /// <summary>
        /// 级别
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        public string ParentID { get; set; }

        /// <summary>
        /// 子课程
        /// </summary>
        public List<TrainingClassCourseView> ChildCourses { get; set; }
    }
}
