﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeContactRepository : IEmployeeContactRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        public EmployeeContactRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeContactInfo>> GetDataByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeContactInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取手机号
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<string> GetPhoneNumberByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeContactInfos.AsNoTracking()
                .Where(m => m.EmployeeID == employeeID && m.ContactWayCode=="10" && m.DeleteFlag != "*")
                .Select(m=>m.ContactContent).FirstOrDefaultAsync();
        }
    }
}
