﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划目标字典
    /// </summary>
    public interface IAnnualGoalListRepository : ICacheRepository
    {
        /// <summary>
        /// 获取年度计划目标（无缓存）
        /// </summary>
        /// <param name="goalID">目标ID</param>
        /// <returns></returns>
        Task<AnnualGoalListInfo> GetGoalListInfoNoCache(int goalID);

        /// <summary>
        /// 获取目标类型最大值
        /// </summary>
        /// <returns></returns>
        Task<int> GetMaxGoalID();
    }
}
