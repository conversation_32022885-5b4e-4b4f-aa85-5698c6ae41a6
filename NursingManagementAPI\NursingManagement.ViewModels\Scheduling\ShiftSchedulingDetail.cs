﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班明细
    /// </summary>
    public class ShiftSchedulingDetail
    {
        /// <summary>
        /// 排班明细记录ID
        /// </summary>
        public string ShiftSchedulingDetailID { get; set; }

        /// <summary>
        /// 排班主记录ID
        /// </summary>
        public string ShiftSchedulingRecordID { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 部门岗位编号，DepartmentPost表的主键
        /// </summary>
        public int DepartmentPostID { get; set; }

        /// <summary>
        /// 部门岗位，DepartmentPost表的主键
        /// </summary>
        public string DepartmentPostName { get; set; }

        /// <summary>
        /// 排班人
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime SchedulingDate { get; set; }

        /// <summary>
        /// 午别
        /// </summary>
        public string NoonType { get; set; }

        /// <summary>
        /// 调班记录ID，AdjustScheduleRecord表的主键
        /// </summary>
        public string AdjustScheduleRecordID { get; set; }
    }
}
