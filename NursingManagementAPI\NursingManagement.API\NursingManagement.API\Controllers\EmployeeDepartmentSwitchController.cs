﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 人员多部门权限api控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/EmployeeDepartmentSwitch")]
    [EnableCors("any")]
    public class EmployeeDepartmentSwitchController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IEmployeeDepartmentSwitchService _employeeDepartmentSwitchService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="session"></param>
        /// <param name="employeeDepartmentSwitchService"></param>
        public EmployeeDepartmentSwitchController(
            ISessionService session,
            IEmployeeDepartmentSwitchService employeeDepartmentSwitchService)
        {
            _session = session;
            _employeeDepartmentSwitchService = employeeDepartmentSwitchService;
        }

        /// <summary>
        /// 根据科室获取对应人员的多部门权限信息
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeDepartmentSwitchList")]
        public async Task<IActionResult> GetEmployeeDepartmentSwitchListAsync(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var success = await _employeeDepartmentSwitchService.GetEmployeeDepartmentSwitchListAsync(departmentID);
            result.Data = success;
            return result.ToJson();
        }
        /// <summary>
        /// 保存人员多部门权限记录
        /// </summary>
        /// <param name="employeeDepartmentSwitchParamView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEmployeeDepartmentSwitch")]
        public async Task<IActionResult> SaveEmployeeDepartmentSwitchAsync([FromBody] EmployeeDepartmentSwitchParamView employeeDepartmentSwitchParamView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if (string.IsNullOrEmpty(employeeDepartmentSwitchParamView.HospitalID))
            {
                employeeDepartmentSwitchParamView.HospitalID = session.HospitalID;
            }
            var success = await _employeeDepartmentSwitchService.SaveEmployeeDepartmentSwitchAsync(employeeDepartmentSwitchParamView, session.EmployeeID);
            result.Data = success;
            if (success)
            {
                result.Code = 1;
            }
            return result.ToJson();
        }

        /// <summary>
        /// 删除多部门权限记录
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteEmployeeDepartmentSwitch")]
        public async Task<IActionResult> DeleteEmployeeDepartmentSwitchAsync(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var success = await _employeeDepartmentSwitchService.DeleteEmployeeDepartmentSwitchAsync(employeeID, session.EmployeeID);
            result.Data = success;
            if (success)
            {
                result.Code = 1;
            }
            return result.ToJson();
        }
        /// <summary>
        /// 获取用户拥有权限部门的级联Option
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeSwitchCascader")]
        public async Task<IActionResult> GetEmployeeSwitchCascader(string employeeID = null)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            employeeID ??= session.EmployeeID;
            var success = await _employeeDepartmentSwitchService.GetEmployeeSwitchCascader(employeeID);
            result.Data = success;
            return result.ToJson();
        }
        
    }
}
