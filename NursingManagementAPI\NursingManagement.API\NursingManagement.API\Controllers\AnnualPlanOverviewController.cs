﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.Services.Interface.AnnualPlan;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// </summary>
    [Produces("application/json")]
    [Route("api/AnnualPlanOverview")]
    [EnableCors("any")]
    public class AnnualPlanOverviewController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IAnnualPlanOverViewService _annualPlanOverViewService;

        /// <summary>
        /// 计划总览
        /// </summary>
        /// <param name="annualPlanOverViewService"></param>
        /// <param name="session"></param>
        public AnnualPlanOverviewController(
              ISessionService session
            , IAnnualPlanOverViewService annualPlanOverViewService

        )
        {
            _session = session;
            _annualPlanOverViewService = annualPlanOverViewService;
        }

        /// <summary>
        /// 获取计划总览视图
        /// </summary>
        /// <param name="annualPlanMainId">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanPreview")]
        public async Task<IActionResult> GetAnnualPlanPreview(string annualPlanMainId)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanOverViewService.GetAnnualPlanPreview(annualPlanMainId);
            return result.ToJson();
        }

        /// <summary>
        /// 获取年度Word文档
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("ExportAnnualPlan")]
        public async Task<IActionResult> ExportAnnualPlan(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanOverViewService.CombineWordDoc(mainID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取季度Word文档
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <param name="showRoutineWorks">是否显示常规工作</param>
        /// <returns></returns>
        [HttpGet]
        [Route("ExportQuarterPlan")]
        public async Task<IActionResult> ExportQuarterPlan(string quarterPlanMainID, bool showRoutineWorks)
        {
            var result = new ResponseResult
            {
                Data = await _annualPlanOverViewService.GenerateQuarterPlanWordDocAsync(quarterPlanMainID, showRoutineWorks)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 获取月度Word文档
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <param name="showRoutineWorks">是否显示常规工作</param>
        /// <returns></returns>
        [HttpGet]
        [Route("ExportMonthlyPlan")]
        public async Task<IActionResult> ExportMonthPlan(string monthlyPlanMainID, bool showRoutineWorks)
        {
            var result = new ResponseResult
            {
                Data = await _annualPlanOverViewService.GenerateMonthlyPlanWordDocAsync(monthlyPlanMainID, showRoutineWorks)
            };
            return result.ToJson();
        }
    }
}
