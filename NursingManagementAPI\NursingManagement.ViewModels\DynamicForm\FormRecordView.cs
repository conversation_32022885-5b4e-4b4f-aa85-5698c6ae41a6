﻿namespace NursingManagement.ViewModels
{
    public class FormRecordView
    {
        public FormRecordView() { }
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="formName"></param>
        /// <param name="formType"></param>
        public FormRecordView(string formName, string formType)
        {
            FormName = formName;
            FormType = formType;
            Size = "small";
            Column = 12;
            LabelWidth = 90;
            LabelPosition = "top";
        }
        /// <summary>
        /// 表单ID
        /// </summary>
        public string FormID { get; set; }
        /// <summary>
        /// 表单名称
        /// </summary>
        public string FormName { get; set; }
        /// <summary>
        /// 表单类型 1:问卷表单；2:试卷表单
        /// </summary>
        public string FormType { get; set; }
        /// <summary>
        /// 表单尺寸：small、default、large
        /// </summary>
        public string Size { get; set; }
        /// <summary>
        /// 表单栅格布局列数，用于计算内部组件宽度做参考
        /// </summary>
        public int Column { get; set; }
        /// <summary>
        /// 标题宽度
        /// </summary>
        public int LabelWidth { get; set; }
        /// <summary>
        /// 标题显示位置：top、left、right
        /// </summary>
        public string LabelPosition { get; set; }
    }
}
