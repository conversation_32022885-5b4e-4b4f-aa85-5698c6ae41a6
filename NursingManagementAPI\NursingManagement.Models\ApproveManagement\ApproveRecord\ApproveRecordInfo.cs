﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 审批主记录
    /// </summary>
    [Serializable]
    [Table("ApproveRecord")]
    public class ApproveRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 审批记录唯一码，Guid
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ApproveRecordID { get; set; }
        /// <summary>
        /// 来源流程表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }
        /// <summary>
        /// 审批流程状态（1：审批中、2：审批完成、3：审批驳回、4.审批撤销）
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 审批流程完成/终止时间
        /// </summary>
        public DateTime? CompleteDateTime { get; set; }
        /// <summary>
        /// 审批流程配置ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveProcessID { get; set; }
        /// <summary>
        /// 审批业务进行到的明细记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveMainID { get; set; }
        /// <summary>
        /// 发起审批的业务内容
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string Content { get; set; }
        /// <summary>
        /// 流水号
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SerialNumber { get; set; }
        /// <summary>
        /// 撤销原因
        /// </summary>
        public string Reason { get; set; }
    }
}
