﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeEducationalExperienceRepository : IEmployeeEducationalExperienceRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeEducationalExperienceRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeEducationalExperienceInfo>> GetListByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeEducationalExperienceInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<EmployeeEducationalExperienceInfo>> GetFirstAndHighestEducationalView(string hospitalID, string[] employeeIDs, string[] eucationalIDs)
        {
            var result = _dbContext.EmployeeEducationalExperienceInfos.Where(m => m.HospitalID == hospitalID  && m.DeleteFlag != "*" && employeeIDs.Contains(m.EmployeeID)
            &&  (m.SpecialFlag=="F" || m.SpecialFlag == "H"));
            if (eucationalIDs != null && eucationalIDs.Length > 0)
            {
                result = result.Where(m => eucationalIDs.Contains(m.EducationCode));
            }
            return await result.Select(m => new EmployeeEducationalExperienceInfo
            {
                EmployeeID = m.EmployeeID,
                EducationCode = m.EducationCode,                
                SpecialFlag=m.SpecialFlag
            }
            ).ToListAsync();
        }
    }
}