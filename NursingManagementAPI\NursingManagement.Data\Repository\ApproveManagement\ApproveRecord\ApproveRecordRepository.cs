﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 审批主表仓储接口
    /// </summary>
    public class ApproveRecordRepository : IApproveRecordRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ApproveRecordRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        public async Task<ApproveRecordInfo> GetApproveRecordByRecordIDAsync(string approveRecordID)
        {
            return await _nursingManagementDbContext.ApproveRecordInfos.Where(m => m.DeleteFlag != "*" && m.ApproveRecordID == approveRecordID).FirstOrDefaultAsync();

        }
        /// <summary>
        /// 获取还没有完成审批的审批申请记录
        /// </summary>
        ///  <param name="processID"></param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<ApproveRecordInfo>> GetUnCompletedApproveRecordsAsNoTrackAsync(string processID, DateTime? startDate, DateTime? endDate)
        {
            return await _nursingManagementDbContext.ApproveRecordInfos.AsNoTracking()
                .Where(m => m.DeleteFlag != "*" && m.CompleteDateTime == null && m.StatusCode != ApprovalStatus.Revoke.ToString("d"))
                .IfWhere(!string.IsNullOrEmpty(processID), m => m.ApproveProcessID == processID)
                .IfWhere(startDate.HasValue, m => m.AddDateTime >= startDate.Value.Date)
                .IfWhere(endDate.HasValue, m => m.AddDateTime < endDate.Value.Date.AddDays(1)).ToListAsync();
        }
        /// <summary>
        /// 获取完成审批的审批申请记录
        /// </summary>
        /// <param name="employeeIDs">审批人员的工号集合</param>
        /// <param name="processID">审批流程主记录ID,null表示部分流程类别</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<ApproveRecordInfo>> GetCompletedApproveRecordsAsync(List<string> employeeIDs, string processID, DateTime? startDate, DateTime? endDate)
        {
            var approveRecordInfos = _nursingManagementDbContext.ApproveRecordInfos
                .Where(record => record.DeleteFlag != "*" &&
                           record.StatusCode != ApprovalStatus.Waiting.ToString("d") &&
                           (string.IsNullOrEmpty(processID) || record.ApproveProcessID == processID) &&
                           (!startDate.HasValue || record.CompleteDateTime >= startDate.Value.Date) &&
                           (!endDate.HasValue || record.CompleteDateTime < endDate.Value.Date.AddDays(1)));

            var approveMainInfos = _nursingManagementDbContext.ApproveMainInfos
                .Where(m => m.DeleteFlag != "*");

            var approveDetailInfos = _nursingManagementDbContext.ApproveDetailInfos
                .Where(d => d.DeleteFlag != "*" && employeeIDs.Contains(d.ApproveEmployeeID));

            var query = from record in approveRecordInfos
                        join main in approveMainInfos
                            on record.ApproveRecordID equals main.ApproveRecordID
                        join detail in approveDetailInfos
                            on main.ApproveMainID equals detail.ApproveMainID
                        select record;
            return await query.ToListAsync();


        }
        /// <summary>
        /// 根据来源ID获取审批记录
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        public async Task<ApproveRecordInfo> GetApproveRecordBySourceIDAsync(string sourceID)
        {
            return await _nursingManagementDbContext.ApproveRecordInfos.Where(m => m.DeleteFlag != "*" && m.SourceID == sourceID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取审批记录
        /// </summary>
        /// <param name="sourceIDs">来源主表ID</param>
        /// <returns></returns>
        public async Task<ApproveRecordInfo[]> GetRecordsBySourceIDs(IEnumerable<string> sourceIDs)
        {
            return await _nursingManagementDbContext.ApproveRecordInfos.Where(m => m.DeleteFlag != "*" && sourceIDs.Contains(m.SourceID)).ToArrayAsync();
        }
        /// <summary>
        /// 获取审批记录MainID集合
        /// </summary>
        /// <param name="sourceIDs"></param>
        /// <returns></returns>
        public async Task<string[]> GetMianIDArrayBySourceIDs(IEnumerable<string> sourceIDs)
        {
            return await _nursingManagementDbContext.ApproveRecordInfos.Where(m => m.DeleteFlag != "*" && sourceIDs.Contains(m.SourceID)).Select(m => m.ApproveMainID).ToArrayAsync();
        }
        /// <summary>
        /// 获取审批原因
        /// </summary>
        /// <param name="recordIDs">审批记录ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetRevokeReasonsByRecordIDsAsync(List<string> recordIDs)
        {
            return await _nursingManagementDbContext.ApproveRecordInfos
                .Where(m => m.DeleteFlag != "*" && recordIDs.Contains(m.ApproveRecordID))
                .ToDictionaryAsync(m=>m.ApproveRecordID,n=>n.Reason);
        }
    }
}
