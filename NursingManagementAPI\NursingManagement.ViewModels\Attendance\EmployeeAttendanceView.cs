﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班明细
    /// </summary>
    public class EmployeeAttendanceView
    {
        /// <summary>
        /// 考勤主记录序号
        /// </summary>
        public string AttendanceRecordID { get; set; }
        /// <summary>
        /// 考勤人ID
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 应该出勤天数
        /// </summary>
        public decimal RequiredAttendanceDays { get; set; }
        /// <summary>
        /// 实际出勤天数
        /// </summary>
        public decimal ActualAttendanceDays { get; set; }
        /// <summary>
        /// 休假天数
        /// </summary>
        public decimal RestDays { get; set; }
        /// <summary>
        /// 小夜天数
        /// </summary>
        public decimal? EveningShiftDays { get; set; }
        /// <summary>
        /// 大夜天数
        /// </summary>
        public decimal? NightShiftDays { get; set; }
        /// <summary>
        /// 通夜天数
        /// </summary>
        public decimal? WholeNightShiftDays { get; set; }
        /// <summary>
        /// 考勤备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 考勤休假 统计
        /// </summary>
        public List<RestStatisticsView> RestStatistics { get; set; }
        /// <summary>
        /// 考勤明细
        /// </summary>
        public List<AttendanceDetailView> AttendanceDetails { get; set; }

    }
    public class RestStatisticsView
    {
        /// <summary>
        /// 休假岗 ID
        /// </summary>
        public int RestPostID { get; set; }
        /// <summary>
        /// 统计天数
        /// </summary>
        public decimal StatisticsDays { get; set; }
    }
    public class AttendanceDetailView
    {
        /// <summary>
        /// 考勤午别
        /// </summary>
        public string NoonTypeID { get; set; }
        /// <summary>
        /// 考勤日期
        /// </summary>
        public DateTime AttendanceDate { get; set; }
        /// <summary>
        /// 考勤内容
        /// </summary>
        public string AttendanceContent { get; set; }
    }
}
