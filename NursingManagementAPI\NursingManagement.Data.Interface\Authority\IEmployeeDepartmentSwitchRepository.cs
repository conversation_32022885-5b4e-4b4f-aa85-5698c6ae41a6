﻿namespace NursingManagement.Data.Interface
{
    public interface IEmployeeDepartmentSwitchRepository : ICacheRepository
    {
        /// <summary>
        /// 根据工号获取拥有的部门权限
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="cacheFlag">是否从缓存中获取</param>
        /// <returns></returns>

        Task<List<EmployeeDepartmentSwitchInfo>> GetDepartmentSwitchByEmployeeIDAsync(string employeeID, Boolean cacheFlag);
        /// <summary>
        /// 根据工号和组织类型获取拥有的部门权限
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="organizationType">组织类别</param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentSwitchInfo>> GetDepartmentListByEmployeeID(string employeeID, string organizationType);
        /// <summary>
        /// 获取拥有权限的科室ID集合
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        Task<List<int>> GetSwitchDepartmentIDsAsync(string employeeID);

    }
}