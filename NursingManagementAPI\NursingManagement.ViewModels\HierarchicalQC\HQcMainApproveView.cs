﻿namespace NursingManagement.ViewModels.HierarchicalQC
{
    public class HQcMainApproveView
    {
        /// <summary>
        /// 考核记录主ID
        /// </summary>
        public string HQcMainId { get; set; }

        /// <summary>
        /// 考核排序
        /// </summary>
        public int HQcSort { get; set; }

        /// <summary>
        /// 考核日期
        /// </summary>
        public DateTime? HQcDate { get; set; }

        /// <summary>
        /// 考核分数
        /// </summary>
        public decimal? HQcScore { get; set; }

        /// <summary>
        /// 达标分数
        /// </summary>
        public int? MinPassingScore { get; set; }
        /// <summary>
        /// 审核状态：0、待提交||审批未通过（当AuditDateTime为空的时候），1、待审批，2、审批通过
        /// </summary>
        public string AuditStatus { get; set; }
    }
}
