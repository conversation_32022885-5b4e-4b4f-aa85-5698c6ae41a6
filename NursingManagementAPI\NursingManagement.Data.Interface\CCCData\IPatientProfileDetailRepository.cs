﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IPatientProfileDetailRepository
    {
        /// <summary>
        /// 根据主记录ID 获取明细记录
        /// </summary>
        /// <param name="patientProfileRecordID"></param>
        /// <returns></returns>
        Task<List<PatientProfileDetailInfo>> GetDataByPatientProfileRecordID(string patientProfileRecordID);
        /// <summary>
        /// 根据主记录ID集合 获取明细记录
        /// </summary>
        /// <param name="patientProfileRecordIDs"></param>
        /// <returns></returns>
        Task<List<PatientProfileDetailInfo>> GetDataByPatientProfileRecordIDList(List<string> patientProfileRecordIDs);
        /// <summary>
        /// 根据主记录ID获取明细记录
        /// </summary>
        /// <param name="patientProfileRecordID"></param>
        /// <returns></returns>
        Task<List<PatientProfileDetailInfo>> GetDataByPatientProfileRecordIDList(string patientProfileRecordID);
    }
}
