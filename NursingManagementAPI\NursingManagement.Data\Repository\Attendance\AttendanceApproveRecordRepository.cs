﻿using DocumentFormat.OpenXml.Bibliography;
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AttendanceApproveRecordRepository : IAttendanceApproveRecordRepository
    {
        private readonly NursingManagementDbContext _context;
        private readonly SessionCommonServer _sessionCommonServer;

        public AttendanceApproveRecordRepository(
            NursingManagementDbContext context
            , SessionCommonServer sessionCommonServer
        )
        {
            _context = context;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 获取部门考核状态表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        public async Task<List<AttendanceApproveRecordInfo>> GetRecordByDepartmentID(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.AttendanceApproveRecordInfos.Where(m =>
                                                m.DepartmentID == departmentID
                                                && m.AttendanceYear == attendanceYear
                                                && m.AttendanceMonth == attendanceMonth
                                                && m.HospitalID == session.HospitalID
                                                && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据ID获取部门审核状态表
        /// </summary>
        /// <param name="attendanceApproveRecordID"></param>
        /// <returns></returns>
        public async Task<AttendanceApproveRecordInfo> GetRecordByRecordID(string attendanceApproveRecordID)
        {
            return await _context.AttendanceApproveRecordInfos.Where(m => m.AttendanceApproveRecordID == attendanceApproveRecordID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取审核最新状态
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        public async Task<AttendanceApproveRecordInfo> GetRecord(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.AttendanceApproveRecordInfos.Where(m =>
                                                m.DepartmentID == departmentID
                                                && m.AttendanceYear == attendanceYear
                                                && m.AttendanceMonth == attendanceMonth
                                                && m.HospitalID == session.HospitalID
                                                && m.DeleteFlag != "*").OrderByDescending(m => m.AddDateTime).FirstOrDefaultAsync();

        }
        /// <summary>
        /// 根据主键获取记录
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<AttendanceApproveRecordInfo>> GetRecordsByIDAsNoTrackAsync(List<string> recordIDs)
        {
            return await _context.AttendanceApproveRecordInfos.Where(m =>
            recordIDs.Contains(m.AttendanceApproveRecordID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
