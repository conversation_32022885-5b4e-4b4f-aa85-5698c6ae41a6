﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class ShiftSchedulingParams
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 排班主记录序号，周排班会存在跨月情况，多条记录ID
        /// </summary>
        public List<string> ShiftSchedulingRecordIDs { get; set; }
        /// <summary>
        /// 标志新增，只有新增时才取预约申请的信息
        /// </summary>
        public bool IsAdd { get; set; }
        /// <summary>
        /// 如果是查询，不取预约申请的信息
        /// </summary>
        public bool IsQuery { get; set; }
        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime SchedulingDate { get; set; }        
        /// <summary>
        /// 排班人员
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 排班明细
        /// </summary>
        public List<ShiftSchedulingDetailInfo> ShiftSchedulingDetails { get; set; }
        /// <summary>
        /// 排班明细标记
        /// </summary>
        public List<ShiftSchedulingDetailMarkInfo> ShiftSchedulingDetailMarks { get; set; }
        /// <summary>
        /// 标记字典表
        /// </summary>
        public List<AdministrationIconInfo> AdministrationIconList { get; set; }
        /// <summary>
        /// 排班预约
        /// </summary>
        public List<SchedulingRequestRecordInfo> SchedulingRequestList { get; set; }
        /// <summary>
        /// 岗位字典
        /// </summary>
        public List<PostSelectOptionsView> DepartmentPostList { get; set; }
        /// <summary>
        /// 午别字典
        /// </summary>
        public List<SelectOptionsView> NoonTypeList { get; set; }
        /// <summary>
        /// 人员信息
        /// </summary>
        public EmployeeForSchedulingView EmployeeInfo { get; set; }
        /// <summary>
        /// 排班主表
        /// </summary>
        public ShiftSchedulingRecordInfo ShiftSchedulingRecord { get; set; }
        /// <summary>
        /// 人员清单
        /// </summary>
        public List<EmployeeForSchedulingView> EmployeeList { get; set; }
        /// <summary>
        /// 人员休产假天数字典
        /// </summary>
        public Dictionary<string, int> EmployeeMaternityLeaveDays { get; set; }
        /// <summary>
        /// 人员休年假天数字典
        /// </summary>
        public Dictionary<string, int> EmployeeAnnualLeaveDays { get; set; }
    }
}
