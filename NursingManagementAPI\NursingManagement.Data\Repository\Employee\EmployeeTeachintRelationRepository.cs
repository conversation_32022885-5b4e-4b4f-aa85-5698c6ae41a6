﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeTeachintRelationRepository : IEmployeeTeachintRelationRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeTeachintRelationRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 获取带教关系信息
        /// </summary>
        /// <param name="employeeIDs">护士工号集合</param>
        /// <returns></returns>
        public async Task<List<EmployeeTeachingRelationInfo>> GetEmployeeTeachingRelation(List<string> employeeIDs)
        {
            return await _dbContext.EmployeeTeachingRelationInfos.Where(m => employeeIDs.Contains(m.EmployeeID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 通过employeeID获取带教关系
        /// </summary>
        /// <param name="employeeID">护士工号</param>
        /// <returns></returns>
        public async Task<List<EmployeeTeachingRelationInfo>> GetTeachRelationByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeTeachingRelationInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 通过主键ID获取带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationID">主键ID</param>
        /// <returns></returns>
        public async Task<EmployeeTeachingRelationInfo> GetTeachRelationByID(int employeeTeachingRelationID)
        {
            return await _dbContext.EmployeeTeachingRelationInfos.FirstOrDefaultAsync(m => m.EmployeeTeachingRelationID == employeeTeachingRelationID && m.DeleteFlag != "*");
        }
    }
}