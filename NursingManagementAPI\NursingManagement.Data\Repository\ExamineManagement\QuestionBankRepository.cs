﻿using DocumentFormat.OpenXml.InkML;
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class QuestionBankRepository : IQuestionBankRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContex;
        private readonly SessionCommonServer _sessionCommonServer;

        public QuestionBankRepository(
            NursingManagementDbContext nursingManagementDbContex
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContex = nursingManagementDbContex;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<QuestionBankInfo>> GetQuestionBankList(List<int> departmentIDs = null, bool? isPractical = null)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContex.QuestionBankInfos.Where(m => m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                        .IfWhere(departmentIDs?.Count >= 0, m => m.DepartmentID == null || departmentIDs.Contains(m.DepartmentID.Value))
                        .IfWhere(isPractical != null, m => m.IsPractical == isPractical)
                        .ToListAsync();
        }

        public async Task<QuestionBankInfo> GetDataByID(string questionBankID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContex.QuestionBankInfos.FirstOrDefaultAsync(m => m.QuestionBankID == questionBankID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*");
        }

        public async Task<List<SelectOptionsView>> GetQuestionBankDictAsync()
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContex.QuestionBankInfos.Where(m => m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                .Select(m => new SelectOptionsView
                {
                    ID = m.QuestionBankID,
                    Label = m.Content,
                    Value = m.QuestionBankID,
                    Type = m.OrganizationalDepartmentCode
                }).ToListAsync();
        }

        public async Task<Dictionary<string, string>> GetPracticalQuestionBank()
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContex.QuestionBankInfos.Where(m => m.IsPractical && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
              .ToDictionaryAsync(m => m.QuestionBankID, m => m.Content);
        }

        public async Task<List<QuestionBankInfo>> GetListByBankID(string questionBankID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContex.QuestionBankInfos.Where(m => m.QuestionBankID == questionBankID || m.ParentID == questionBankID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据题库ID获取数据，包含子级
        /// </summary>
        /// <param name="questionBankIDs"><题库集合/param>
        /// <returns></returns>
        public async Task<List<QuestionBankInfo>> GetListByBankIDs(List<string> questionBankIDs)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContex.QuestionBankInfos.Where(m => questionBankIDs.Contains(m.QuestionBankID) && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取题库父子关系映射表
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, List<string>>> GetQuestionBankParentChildMapping()
        {
            return await _nursingManagementDbContex.QuestionBankInfos.Where(m => m.DeleteFlag != "*" && m.ParentID != null)
              .GroupBy(kvp => kvp.ParentID)
                .ToDictionaryAsync(
                    g => g.Key,
                    g => g.Select(kvp => kvp.QuestionBankID).ToList()
                );
        }

    }
}
