﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    ///  能力层级字典
    /// </summary>
    [Table("CapabilityLevel")]
    public class CapabilityLevelInfo : MutiModifyInfo
    {
        ///<summary>
        ///能级编码，主键，非自增
        ///</summary>
        public int CapabilityLevelID { get; set; }
        ///<summary>
        ///医院序号，主键
        ///</summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        ///<summary>
        ///语言序号，主键
        ///</summary>
        public int Language { get; set; }
        ///<summary>
        /// 类型
        ///</summary>
        [Column(TypeName = "varchar(20)")]
        public string Type { get; set; }
        ///<summary>
        ///能级名称
        ///</summary>
        [Column(TypeName = "nvarchar(50)")]
        public string CapabilityLevelName { get; set; }
        ///<summary>
        ///层级，用于岗位-能级对照
        ///</summary>
        public int Level { get; set; }
        ///<summary>
        ///排序
        ///</summary>
        public int Sort { get; set; }
    }
}
