﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{

    /// <summary>
    /// 排班预约申请控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/attendance")]
    [EnableCors("any")]
    public class AttendanceController : Controller
    {
        /// <summary>
        /// 引用
        /// </summary>
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IAttendanceService _attendanceService;
        private readonly ITemporaryAttendanceRecordService _temporaryAttendanceRecordService;

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="session"></param>
        /// <param name="attendanceService"></param>
        /// <param name="temporaryAttendanceRecordService"></param>
        public AttendanceController(
            ISessionService session
            , IAttendanceService attendanceService
            , ITemporaryAttendanceRecordService temporaryAttendanceRecordService

        )
        {
            _session = session;
            _attendanceService = attendanceService;
            _temporaryAttendanceRecordService = temporaryAttendanceRecordService;
        }

        /// <summary>
        /// 检核考勤是否被编辑过
        /// </summary>
        /// <param name="departmentID">部门编码</param>
        /// <param name="attendanceYear">考勤年份</param>
        /// <param name="attendanceMonth">考勤月份</param>
        /// <returns>true:已编辑过；false:未被编辑过</returns>
        [HttpPost]
        [Route("CheckAttendanceEdit")]
        public async Task<IActionResult> CheckAttendanceEdit(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if (departmentID <= 0 || attendanceYear <= 0 || attendanceMonth <= 0)
            {
                _logger.Error("CreateAttendanceRecord缺失必要参数！");
                result.Error("CreateAttendanceRecord缺失必要参数！");
                return result.ToJson();
            }
            result.Data = await _attendanceService.CheckAttendanceEdit(departmentID, attendanceYear, attendanceMonth);
            return result.ToJson();
        }

        /// <summary>
        /// 生成考勤表
        /// </summary>
        /// <param name="departmentID">部门编码</param>
        /// <param name="attendanceYear">考勤年份</param>
        /// <param name="attendanceMonth">考勤月份</param>
        /// <returns></returns>
        [HttpPost]
        [Route("CreateAttendanceRecord")]
        public async Task<IActionResult> CreateAttendanceRecord(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if (departmentID <= 0 || attendanceYear <= 0 || attendanceMonth <= 0)
            {
                _logger.Error("CreateAttendanceRecord缺失必要参数！");
                result.Error("CreateAttendanceRecord缺失必要参数！");
                return result.ToJson();
            }
            result.Data = await _attendanceService.CreateAttendanceRecord(departmentID, attendanceYear, attendanceMonth, session.EmployeeID, session.HospitalID);
            return result.ToJson();
        }

        /// <summary>
        /// 查询部门指定月份的考勤表
        /// </summary>
        /// <param name="departmentID">部门编码</param>
        /// <param name="attendanceYear">考勤年份</param>
        /// <param name="attendanceMonth">考勤月份</param>
        /// <param name="employeeID"></param>
        /// <param name="sortFlag">是否按照排班排序否则按照考勤</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAttendanceDatas")]
        public async Task<IActionResult> GetAttendanceDatas(int departmentID, int attendanceYear, int attendanceMonth, string employeeID,bool sortFlag)
        {

            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if (departmentID <= 0 || attendanceYear <= 0 || attendanceMonth <= 0)
            {
                _logger.Error("CreateAttendanceRecord缺失必要参数！");
                result.Error("CreateAttendanceRecord缺失必要参数！");
                return result.ToJson();
            }
            result.Data = await _attendanceService.GetAttendanceDatas(departmentID, attendanceYear, attendanceMonth, employeeID, sortFlag);
            return result.ToJson();
        }


        /// <summary>
        /// 保存手动调整的考勤数据
        /// </summary>
        /// <param name="attendanceView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAttendance")]
        public async Task<IActionResult> SaveAttendance([FromBody] AttendanceView attendanceView)
        {

            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            attendanceView.HospitalID = session.HospitalID;
            attendanceView.EmployeeID = session.EmployeeID;
            result.Data = await _attendanceService.SaveAttendance(attendanceView);
            return result.ToJson();
        }
        /// <summary>
        /// 获取临时出勤岗位标识数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTemporaryAttendancePostSetting")]
        public async Task<IActionResult> GetTemporaryAttendancePostSetting(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _temporaryAttendanceRecordService.GetTemporaryAttendancePostSetting(departmentID == 0 ? session.DepartmentID : departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除临时出勤记录
        /// </summary>
        /// <param name="recordID">临时出勤记录ID(表主键ID)</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteTemporaryAttendanceData")]
        public async Task<IActionResult> DeleteTemporaryAttendanceData(int recordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _temporaryAttendanceRecordService.DeleteTemporaryAttendanceData(recordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取临时出勤记录数据
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="showAllFlag">是否查看全科开关</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTemporaryAttendanceList")]
        public async Task<IActionResult> GetTemporaryAttendanceList(string employeeID, bool showAllFlag, int departmentID, DateTime month)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _temporaryAttendanceRecordService.GetTemporaryAttendanceList(employeeID, showAllFlag, departmentID == 0 ? session.DepartmentID : departmentID, month);
            return result.ToJson();
        }
        /// <summary>
        /// 保存临时出勤记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveTemporaryAttendanceData")]
        public async Task<IActionResult> SaveTemporaryAttendanceData([FromBody] TemporaryAttendanceRecordView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _temporaryAttendanceRecordService.SaveTemporaryAttendanceData(view, session);
            return result.ToJson();
        }
        /// <summary>
        /// 获取考勤状态（未审批：申请审批：取消审批：审批通过）
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAttendanceStatus")]
        public async Task<IActionResult> GetAttendanceStatus(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _attendanceService.GetAttendanceStatus(departmentID, attendanceYear, attendanceMonth);
            return result.ToJson();
        }
        /// <summary>
        /// 考勤审核
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("AttendanceApproval")]
        public async Task<IActionResult> AttendanceApproval(int departmentID, int attendanceYear, int attendanceMonth, string proveCategory)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _attendanceService.AttendanceApproval(departmentID, attendanceYear, attendanceMonth, proveCategory, session.EmployeeID, session.HospitalID);
            return result.ToJson();
        }
        /// <summary>
        /// 取消考勤审批
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("CancelAttendanceApproval")]
        public async Task<IActionResult> CancelAttendanceApproval(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _attendanceService.CancelAttendanceApproval(departmentID, attendanceYear, attendanceMonth, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 考勤回传OA
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("SyncAttendanceDataToOA")]
        public async Task<IActionResult> SyncAttendanceDataToOA(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _attendanceService.SyncAttendanceData(departmentID, attendanceYear, attendanceMonth, session.EmployeeID);
            return result.ToJson();
        }
    }
}
