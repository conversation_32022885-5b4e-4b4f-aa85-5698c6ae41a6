﻿using static NursingManagement.Models.AnnualPlanEnums;
namespace NursingManagement.Models
{
    /// <summary>
    ///  年度计划排程主表    
    /// </summary>
    public class AnnualScheduleMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 排程ID    
        /// </summary>
        public string AnnualScheduleMainID { get; set; }
        /// <summary>
        ///  医院     
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 执行项目ID，来源执行项目字典表    
        /// </summary>
        public int? InterventionID { get; set; }

        /// <summary>
        /// 年    
        /// </summary>
        public int ScheduleYear { get; set; }

        /// <summary>
        /// 月份    
        /// </summary>
        public byte ScheduleMonth { get; set; }

        /// <summary>
        /// 预计执行时间    
        /// </summary>
        public DateTime ScheduleDateTime { get; set; }

        /// <summary>
        /// 预计执行人    
        /// </summary>
        public string SchedulePerformer { get; set; }

        /// <summary>
        /// 执行时间    
        /// </summary>
        public DateTime? PerformDateTime { get; set; }

        /// <summary>
        /// 执行人    
        /// </summary>
        public string Performer { get; set; }

        /// <summary>
        /// 执行备注    
        /// </summary>
        public string PerformComment { get; set; }

        /// <summary>
        /// 排程状态    
        /// </summary>
        public APScheduleStatus Status { get; set; }

        /// <summary>
        /// 不执行、延迟执行原因    
        /// </summary>
        public string Reason { get; set; }
        /// <summary>
        /// 延迟执行备注 
        /// </summary>
        public string DelayContent { get; set; }
        /// <summary>
        /// 措施内容，无措施字典时使用此字段
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 关联的计划工作信息
        /// </summary>
        public ICollection<MonthlyWorkToTaskInfo> MonthlyWorkToTasks { get; set; }
    }
}