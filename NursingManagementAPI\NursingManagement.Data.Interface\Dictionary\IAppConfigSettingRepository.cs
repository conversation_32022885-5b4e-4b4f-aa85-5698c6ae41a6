﻿
using NursingManagement.Models;
namespace NursingManagement.Data.Interface
{
    public interface IAppConfigSettingRepository : ICacheRepository
    {

        /// <summary>
        /// 根据settingType获取配置
        /// </summary>
        /// <param name="settingType"></param>
        /// <returns></returns>
        Task<List<AppConfigSettingInfo>> GetBySettingType(string settingType);

        /// <summary>
        /// 根据settingType，settingCode获取配置
        /// </summary>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        Task<AppConfigSettingInfo> GetConfigSetting(string settingType, string settingCode);

        /// <summary>
        /// 根据settingType，settingCode获取配置的值
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        Task<string> GetConfigSettingValue( string settingType, string settingCode);

       



    }
}
