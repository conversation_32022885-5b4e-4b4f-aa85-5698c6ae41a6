﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划主表
    /// </summary>
    [Serializable]
    [Table("AnnualPlanMain")]
    public class AnnualPlanMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 年度计划主键Guid
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 年度计划制定者，如：部门ID、病区ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Planner { get; set; }
        /// <summary>
        /// 提交状态（0 未提交，1、已提交） 
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }
    }
}
