﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.file;
using NursingManagement.ViewModels.HierarchicalQC;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    public class HierarchicalQCService : IHierarchicalQCService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHierarchicalQCRecordRepository _hierarchicalQCRecordRepository;
        private readonly IHierarchicalQCSubjectRepository _hierarchicalQCSubjectRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IHierarchicalQCFormRepository _hierarchicalQCFormRepository;
        private readonly IHierarchicalQCMainRepository _hierarchicalQCMainRepository;
        private readonly IHierarchicalQCDetailRepository _hierarchicalQCDetailRepository;
        private readonly IHierarchicalQCRemarkRepository _hierarchicalQCRemarkRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IServiceProvider _serviceProvider;
        private readonly IHierarchicalQCAssessListRepository _hierarchicalQCAssessListRepository;
        private readonly IDynamicFormService _dynamicFormService;
        private readonly IDynamicFormDetailRepository _dynamicFormDetailRepository;
        private readonly IDynamicFormDetailAttributeRepository _dynamicFormDetailAttributeRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly IHierarchicalQCObjectRepository _hierarchicalQCObjectRepository;
        private readonly IFileService _fileService;
        private readonly ICommonFileRepository _commonFileRepository;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IProblemRectificationRepository _problemRectificationRepository;
        private readonly IComponentListRepository _componentListRepository;
        private readonly IEmployeeRoleRepository _employeeRoleRepository;
        private readonly IAPISettingRepository _apiSettingRepository;
        private readonly IApproveRecordRepository _approveRecordRepository;
        private readonly IMessageService _messageService;
        private readonly IEmployeeToJobRepository _employeeToJobRepository;

        /// <summary>
        /// 公共质量分类
        /// </summary>
        private const int QCASSESS_LIST_974 = 974;

        /// <summary>
        /// 专科质量分类
        /// </summary>
        private const int QCASSESS_LIST_975 = 975;

        /// <summary>
        /// 最大分支的属性ID
        /// </summary>
        private const int MAXSCORE_ATTRIBUTEID_36 = 36;

        /// <summary>
        /// 组件类型属性ID
        /// </summary>
        private const int TYPE_ATTRIBUTEID_28 = 28;

        /// <summary>
        /// 评分组件ID
        /// </summary>
        private const int COMPONENTID_GRADE = 104;

        /// <summary>
        /// 图片组件ID
        /// </summary>
        private const int COMPONENTID_IMG = 108;

        /// <summary>
        /// 最大分支的属性ID
        /// </summary>
        private const int TEXT_ATTRIBUTEID_3 = 3;
        /// <summary>
        /// 常态工作过程控制移动端路由地址
        /// </summary>
        private const string NORMAL_WORKING_CONTRPL_MOBILE_PATH = "/pages/qcManagement/hierarchicalQC/normalWorkingProcessControl";
        /// <summary>
        /// 护士长职务编码
        /// </summary>
        private const string HEAD_NURSE_JOB_CODE = "975";
        /// <summary>
        ///  常态工作过程控制-已整改状态
        /// </summary>
        private const string AUDIT_STATUS_5 = "5";
        /// <summary>
        /// 常态工作过程控制-护士长已确认状态
        /// </summary>
        private const string AUDIT_STATUS_6 = "6";

        public HierarchicalQCService(
              IUnitOfWork unitOfWork
            , IHierarchicalQCRecordRepository hierarchicalQCRecordRepository
            , IHierarchicalQCSubjectRepository hierarchicalQCSubjectRepository
            , IDepartmentListRepository departmentListRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IHierarchicalQCFormRepository hierarchicalQCFormRepository
            , IHierarchicalQCMainRepository hierarchicalQCMainRepository
            , IHierarchicalQCDetailRepository hierarchicalQCDetailRepository
            , IHierarchicalQCRemarkRepository hierarchicalQCRemarkRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , IServiceProvider serviceProvider
            , IHierarchicalQCAssessListRepository hierarchicalQCAssessListRepository
            , IDynamicFormService dynamicFormService
            , IDynamicFormDetailRepository dynamicFormDetailRepository
            , IDynamicFormDetailAttributeRepository dynamicFormDetailAttributeRepository
            , ICapabilityLevelRepository capabilityLevelRepository
            , IHierarchicalQCObjectRepository hierarchicalQCObjectRepository
            , IFileService fileService
            , ICommonFileRepository commonFileRepository
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IProblemRectificationRepository problemRectificationRepository
            , IComponentListRepository componentListRepository
            , IEmployeeRoleRepository employeeRoleRepository
            , IAPISettingRepository apiSettingRepository
            , IApproveRecordRepository approveRecordRepository
            , IMessageService messageService
            , IEmployeeToJobRepository employeeToJobRepository
        )
        {
            _unitOfWork = unitOfWork;
            _hierarchicalQCSubjectRepository = hierarchicalQCSubjectRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentListRepository = departmentListRepository;
            _hierarchicalQCRecordRepository = hierarchicalQCRecordRepository;
            _hierarchicalQCMainRepository = hierarchicalQCMainRepository;
            _hierarchicalQCDetailRepository = hierarchicalQCDetailRepository;
            _hierarchicalQCFormRepository = hierarchicalQCFormRepository;
            _hierarchicalQCRemarkRepository = hierarchicalQCRemarkRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _serviceProvider = serviceProvider;
            _hierarchicalQCAssessListRepository = hierarchicalQCAssessListRepository;
            _dynamicFormService = dynamicFormService;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
            _dynamicFormDetailAttributeRepository = dynamicFormDetailAttributeRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
            _hierarchicalQCObjectRepository = hierarchicalQCObjectRepository;
            _fileService = fileService;
            _commonFileRepository = commonFileRepository;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _problemRectificationRepository = problemRectificationRepository;
            _componentListRepository = componentListRepository;
            _employeeRoleRepository = employeeRoleRepository;
            _apiSettingRepository = apiSettingRepository;
            _approveRecordRepository = approveRecordRepository;
            _messageService = messageService;
            _employeeToJobRepository = employeeToJobRepository;
        }

        /// <summary>
        /// 新增修改质控主题
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        public async Task<bool> SubjectPlanSave(SubjectView saveView)
        {
            if (saveView == null)
            {
                _logger.Error("质控主题保存参数有误");
                return false;
            }
            var addFlag = string.IsNullOrEmpty(saveView.HierarchicalQCSubjectID);
            if (addFlag)
            {
                return !string.IsNullOrEmpty(await AddSubjectPlan(saveView));
            }
            return await UpdateSubjectPlan(saveView);
        }

        /// <summary>
        ///主题复制
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        public async Task<bool> CopySubjectPlan(SubjectView saveView)
        {
            if (saveView == null)
            {
                _logger.Error("质控主题复制参数有误");
                return false;
            };
            var hierarchicalQCSubjectID = await AddSubjectPlan(saveView);
            if (string.IsNullOrEmpty(hierarchicalQCSubjectID))
            {
                return false;
            }
            var subjectAssignTableView = await GetSubjectAssignView(saveView.HierarchicalQCSubjectID);
            if (subjectAssignTableView != null)
            {
                subjectAssignTableView.HierarchicalQCSubjectID = hierarchicalQCSubjectID;
                await SaveSubjectAssign(subjectAssignTableView, saveView.EmployeeID, saveView.HospitalID);
            }
            return true;
        }

        /// <summary>
        /// 质控主题修改
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        public async Task<bool> UpdateSubjectPlan(SubjectView saveView)
        {
            var oldSubject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(saveView.HierarchicalQCSubjectID);
            if (oldSubject == null)
            {
                _logger.Error("主题修改失败！无法找到相关主题数据 主键ID：" + saveView.HierarchicalQCSubjectID);
                return false;
            }
            var updateFlag = false;
            if (saveView.FormName != oldSubject.FormName)
            {
                oldSubject.FormName = saveView.FormName;
                updateFlag = true;
            };
            if (saveView.FormType != oldSubject.FormType)
            {
                oldSubject.FormType = saveView.FormType;
                updateFlag = true;
            };
            if (saveView.ImplementationStartDate.HasValue && (!oldSubject.ImplementationStartDate.HasValue || oldSubject.ImplementationStartDate.Value != saveView.ImplementationStartDate))
            {
                oldSubject.ImplementationStartDate = saveView.ImplementationStartDate;
                updateFlag = true;
            }
            if (saveView.MinPassingScore != oldSubject.MinPassingScore)
            {
                oldSubject.MinPassingScore = saveView.MinPassingScore; updateFlag = true;
            }
            if (updateFlag)
            {
                oldSubject.Modify(saveView.EmployeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 质控主题新增
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        public async Task<string> AddSubjectPlan(SubjectView saveView)
        {
            var newSubjectInfo = new HierarchicalQCSubjectInfo()
            {
                HospitalID = saveView.HospitalID,
                Language = saveView.Language,
                HierarchicalQCFormID = saveView.HierarchicalQCFormID,
                FormName = saveView.FormName,
                HierarchicalQCFormLevel = saveView.HierarchicalQCFormLevel,
                StartDate = saveView.StartDate,
                EndDate = saveView.EndDate,
                StatusCode = 1,
                DeleteFlag = "",
                ReportFileID = "",
                AddDepartmentID = saveView.DepartmentID,
                AddEmployeeID = saveView.EmployeeID,
                AddDateTime = DateTime.Now,
                FormType = saveView.FormType,
                ImplementationStartDate = saveView.ImplementationStartDate,
                MinPassingScore = saveView.MinPassingScore,
            };
            newSubjectInfo.HierarchicalQCSubjectID = newSubjectInfo.GetId();
            newSubjectInfo.TemplateCode = saveView.TemplateCode;
            //补充节点式督导最大分数
            //var dynamicFormDetailIDs = await _dynamicFormDetailRepository.GetFormDetailIDByFormRecordID(saveView.TemplateCode);
            //newSubjectInfo.ScoreThreshold = await _dynamicFormDetailAttributeRepository.GetDynamicFormMaxScoreByDynamicFormDetailIDs(dynamicFormDetailIDs);
            newSubjectInfo.Modify(saveView.EmployeeID);
            if (!string.IsNullOrEmpty(saveView.YearMonth))
            {
                var stringArr = saveView.YearMonth.Trim().Split("-");
                if (stringArr.Length > 1)
                {
                    newSubjectInfo.Year = Convert.ToInt32(stringArr[0]);
                    newSubjectInfo.Month = Convert.ToInt32(stringArr[1]);
                }
            }
            await _unitOfWork.GetRepository<HierarchicalQCSubjectInfo>().InsertAsync(newSubjectInfo);
            return await _unitOfWork.SaveChangesAsync() >= 0 ? newSubjectInfo.HierarchicalQCSubjectID : "";
        }

        /// <summary>
        /// 获取质控主题表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="employeeID">当前登录操作用户ID</param>
        /// <returns></returns>
        public async Task<List<SubjectView>> GetSubjectTableView(GetQCSubjectView searchView, string employeeID)
        {
            List<SubjectView> tableView = new List<SubjectView>();
            var qcSubjectInfos = await _hierarchicalQCSubjectRepository.GetSubject(searchView);
            //未传维护部门ID时
            if (!searchView.DepartmentID.HasValue)
            {
                //依据规则是否只获取权限部门维护数据
                qcSubjectInfos = await GetSwitchData(qcSubjectInfos, searchView.QCLevel, employeeID);
            }
            if (qcSubjectInfos.Count == 0)
            {
                return tableView;
            }
            var depatmentList = await _departmentListRepository.GetByCacheAsync();
            var employeeIDs = qcSubjectInfos.Where(m => !string.IsNullOrEmpty(m.ModifyEmployeeID)).Select(m => m.ModifyEmployeeID).Distinct().ToList();
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            var settingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "HierarchicalQCForm",
                SettingTypeValue = "HierarchicalQCFormLevel"
            };
            var levelSetting = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            var formTypeSettingParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "HierarchicalQCFormType",
                SettingTypeValue = "HierarchicalQCFormType"
            };
            var formTypeSetting = await _settingDictionaryRepository.GetSettingDictionary(formTypeSettingParams);
            foreach (var subjectItem in qcSubjectInfos)
            {
                tableView.Add(await CreateSubjectTableItem(subjectItem, depatmentList, employeeList, levelSetting, formTypeSetting));
            }
            return tableView;
        }

        /// <summary>
        /// 二级三级节点式督导根据配置来决定 当前端未串维护部门ID时 是否只获取患者有权限部门数据 一级节点式督导和常态工作控制只获取患者有权限部门数据
        /// </summary>
        /// <param name="qcSubjectInfos"></param>
        /// <param name="qcLevel"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCSubjectInfo>> GetSwitchData(List<HierarchicalQCSubjectInfo> qcSubjectInfos, string qcLevel, string employeeID)
        {
            var switchDeptIDs = await _employeeDepartmentSwitchRepository.GetSwitchDepartmentIDsAsync(employeeID);
            if (qcLevel == "1")
            {
                return qcSubjectInfos.Where(m => switchDeptIDs.Contains(m.AddDepartmentID)).ToList();
            }
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "SystemSwitch",
                SettingTypeValue = "defaultShowSwitchDeparemtData"
            };
            var isShowSwitchDataFlag = await _settingDictionaryRepository.GetSettingSwitch(settingDictionaryParams);
            if (!isShowSwitchDataFlag)
            {
                return qcSubjectInfos;
            }
            return qcSubjectInfos.Where(m => switchDeptIDs.Contains(m.AddDepartmentID)).ToList();
        }

        /// <summary>
        /// 年月转化为年 月
        /// </summary>
        /// <param name="yearMonth"></param>
        /// <returns></returns>
        public Tuple<int?, int?> GetYearAndMonth(string yearMonth)
        {
            if (string.IsNullOrEmpty(yearMonth))
            {
                return new Tuple<int?, int?>(null, null);
            }
            var stringArr = yearMonth.Trim().Split("-");
            if (stringArr.Length > 1)
            {
                return new Tuple<int?, int?>(Convert.ToInt32(stringArr[0]), Convert.ToInt32(stringArr[1]));
            }
            return new Tuple<int?, int?>(null, null);
        }

        /// <summary>
        /// 创建表格实例
        /// </summary>
        /// <param name="subjectItem"></param>
        /// <param name="depatmentList"></param>
        /// <param name="employeeList"></param>
        /// <returns></returns>
        public async Task<SubjectView> CreateSubjectTableItem(HierarchicalQCSubjectInfo subjectItem, List<DepartmentListInfo> depatmentList, List<EmployeePersonalDataListView> employeeList, List<SettingDictionaryInfo> levelSetting, List<SettingDictionaryInfo> formTypeSetting)
        {
            SubjectView item = new()
            {
                HierarchicalQCSubjectID = subjectItem.HierarchicalQCSubjectID,
                HierarchicalQCFormID = subjectItem.HierarchicalQCFormID,
                FormName = subjectItem.FormName,
                HierarchicalQCFormLevel = subjectItem.HierarchicalQCFormLevel,
                HierarchicalQCFormName = levelSetting.Find(m => m.SettingValue == subjectItem.HierarchicalQCFormLevel)?.Description ?? "",
                StartDate = subjectItem.StartDate,
                EndDate = subjectItem.EndDate,
                ReportFileID = subjectItem.ReportFileID,
                DepartmentID = subjectItem.AddDepartmentID,
                YearMonth = subjectItem.Year + "-" + (subjectItem.Month < 10 ? "0" : "") + subjectItem.Month,
                AddDepartmentName = depatmentList.Find(m => m.DepartmentID == subjectItem.AddDepartmentID)?.LocalShowName ?? "",
                EmployeeName = employeeList.Find(m => m.EmployeeID == subjectItem.ModifyEmployeeID)?.EmployeeName ?? "",
                EmployeeID = subjectItem.ModifyEmployeeID,
                ModifyDate = subjectItem.ModifyDateTime,
                AssignFlag = await _hierarchicalQCRecordRepository.GetExistsReocrdBySubjectID(subjectItem.HierarchicalQCSubjectID),
                TemplateCode = subjectItem.TemplateCode,
                FormType = formTypeSetting.Find(m => m.SettingValue == subjectItem.FormType)?.Description,
                FormTypeCode = subjectItem.FormType,
                qcDepartmentCount = await _hierarchicalQCRecordRepository.GetQCReocrdDepartmentCountBySubjectID(subjectItem.HierarchicalQCSubjectID),
                ImplementationStartDate = subjectItem.ImplementationStartDate,
                MinPassingScore = subjectItem.MinPassingScore,
            };
            var commonFile = await _commonFileRepository.GetFileByFileIDAsync(item.ReportFileID);
            var settings = await _apiSettingRepository.GetCacheAsync() as List<APISettingInfo>; ;
            var setting = settings.Find(m => m.SettingCode == "DocumentManagementServer");
            if (commonFile != null && setting != null)
            {
                var split = commonFile.Content.Split('.');
                item.ReportFileName = split[0];
                item.ReportFileType = split[1];
                item.ReportFileUrl = $"{setting.SettingValue}uploadDocuments/{commonFile.SourceID}.{split[1]}";
                item.VerifierEmployeeIDs = [subjectItem.AddEmployeeID];
                var recordInfos = await _hierarchicalQCRecordRepository.GetQCReocrdBySubjectID(subjectItem.HierarchicalQCSubjectID);
                if (recordInfos.Count > 0)
                {
                    item.VerifierEmployeeIDs.AddRange(recordInfos.Select(m => m.VerifierEmployeeID).Distinct().ToList());
                }
            }
            return item;
        }

        public async Task<List<HierachicalQCFormView>> GetHierarchicalQCFormList()
        {
            var list = new List<HierachicalQCFormView>();
            var formList = await _hierarchicalQCFormRepository.GetAll();
            if (formList.Count <= 0)
            {
                return list;
            }
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "HierarchicalQC"
            };
            var hierarchicalQCSettings = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            var levelSetting = hierarchicalQCSettings.Where(m => m.SettingTypeCode == "HierarchicalQCForm" && m.SettingTypeValue == "HierarchicalQCFormLevel").ToList();
            var formTypeSetting = hierarchicalQCSettings.Where(m => m.SettingTypeCode == "HierarchicalQCFormType" && m.SettingTypeValue == "HierarchicalQCFormType").ToList();
            var statusCodeSetting = hierarchicalQCSettings.Where(m => m.SettingTypeCode == "HierarchicalQCForm" && m.SettingTypeValue == "StatusCode").ToList();
            var depatmentList = await _departmentListRepository.GetByCacheAsync();
            var employeeIDs = formList.Select(m => m.ModifyEmployeeID).Distinct().ToList();
            var employeeOptions = await _employeePersonalDataRepository.GetEmployOptionViewByEmployeeIDs(employeeIDs);
            foreach (var form in formList)
            {
                list.Add(new HierachicalQCFormView()
                {
                    HierarchicalQCFormID = form.HierarchicalQCFormID,
                    TemplateCode = form.TemplateCode,
                    HierarchicalQCFormLevel = form.HierarchicalQCFormLevel,
                    HierarchicalQCFormLevelName = levelSetting.Find(m => m.SettingValue == form.HierarchicalQCFormLevel)?.Description ?? "",
                    FormType = form.FormType,
                    FormTypeName = formTypeSetting.Find(m => m.SettingValue == form.FormType)?.Description,
                    FormName = form.FormName,
                    StatusCode = form.StatusCode,
                    StatusCodeName = statusCodeSetting.FirstOrDefault(m => m.SettingValue == form.StatusCode.ToString())?.Description ?? "",
                    AddDepartmentID = form.AddDepartmentID,
                    AddDepartmentName = depatmentList.Find(m => m.DepartmentID == form.AddDepartmentID)?.LocalShowName ?? "",
                    ModifyEmployee = employeeOptions.Find(m => m.Value.ToString() == form.ModifyEmployeeID)?.Label ?? form.ModifyEmployeeID,
                    ModifyDateTime = form.ModifyDateTime,
                });
            }
            return list;
        }

        /// <summary>
        /// 获取质控考核结果主记录
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="employeeID">操作人工号</param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCRecordTableView>> GetHierarchicalQCRecordList(GetQCRecordView searchView, string employeeID)
        {
            var qcSubjectList = new List<HierarchicalQCSubjectInfo>();
            var onlyGetSelfRecordSettingParams = new SettingDictionaryParams()
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "SystemSwitch",
                SettingTypeValue = "OnlyGetSelfRecord"
            };
            var onlyGetSelfRecordFlag = await _settingDictionaryRepository.GetSettingSwitch(onlyGetSelfRecordSettingParams);
            var employeeRoleList = await _employeeRoleRepository.GetRolesByEmployeeID(employeeID);
            qcSubjectList = await _hierarchicalQCSubjectRepository.GetSubject(searchView);
            //未传维护部门ID时
            if (!searchView.DepartmentID.HasValue)
            {
                //依据规则是否只获取权限部门维护数据
                qcSubjectList = await GetSwitchData(qcSubjectList, searchView.QCLevel, employeeID);
            }
            var settingParams = new SettingDictionaryParams()
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "SystemSwitch",
                SettingTypeValue = "GetRecordByImplementationStartDate"
            };
            var filterImplementationDateFlag = await _settingDictionaryRepository.GetSettingSwitch(settingParams);
            if (filterImplementationDateFlag)
            {
                qcSubjectList = qcSubjectList.Where(m => m.AddEmployeeID == employeeID || m.ImplementationStartDate <= DateTime.Now).ToList();
            }
            if (qcSubjectList.Count == 0)
            {
                return new List<HierarchicalQCRecordTableView>();
            }
            return await GetQcRecord(searchView, qcSubjectList, employeeID);
            //return view.IfWhere(onlyGetSelfRecordFlag && searchView.HierarchicalQCFormLevel == "3", m => m.VerifierEmployeeID == operateEmployeeID).ToList();
        }

        /// <summary>
        /// 获取质控数据
        /// </summary>
        /// <param name="view"></param>
        /// <param name="qcSubjectList"></param>
        /// <param name="searchView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<List<HierarchicalQCRecordTableView>> GetQcRecord(GetQCRecordView searchView, List<HierarchicalQCSubjectInfo> qcSubjectList, string employeeID)
        {
            var view = new List<HierarchicalQCRecordTableView>();
            var qcRecordList = new List<HierarchicalQCRecordInfo>();
            searchView.QCSubjectIDs = qcSubjectList.Select(m => m.HierarchicalQCSubjectID).ToList();
            if (searchView.QCSubjectIDs.Count == 0)
            {
                return view;
            }
            qcRecordList = await _hierarchicalQCRecordRepository.GetQCRecordInfos(searchView);
            if (!searchView.QCDepartmentID.HasValue)
            {
                var switchDeptIDs = await _employeeDepartmentSwitchRepository.GetSwitchDepartmentIDsAsync(employeeID);
                qcRecordList = qcRecordList.Where(m => switchDeptIDs.Contains(m.DepartmentID)).ToList();
            }
            if (qcRecordList.Count == 0)
            {
                return view;
            }
            //提取EmployID，查询Employee数据
            var employeeIDList = new List<string>();
            foreach (var recod in qcRecordList)
            {
                var splitIDList = recod.HierarchicalQCEmployID.Split("||").ToList();
                employeeIDList.AddRange(splitIDList);
                if (!string.IsNullOrEmpty(recod.VerifierEmployeeID))
                {
                    employeeIDList.Add(recod.VerifierEmployeeID);
                }
            }
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDList);
            var mainList = await _hierarchicalQCMainRepository.GetDataByRecordIDs(qcRecordList.Select(m => m.HierarchicalQCRecordID).ToList());
            foreach (var item in qcRecordList)
            {
                var subjectData = qcSubjectList.Find(m => m.HierarchicalQCSubjectID == item.HierarchicalQCSubjectID);
                var recordTomainList = mainList.Where(m => m.HierarchicalQCRecordID == item.HierarchicalQCRecordID).ToList();
                var lastMainData = recordTomainList.OrderByDescending(m => m.AddDateTime).FirstOrDefault();
                var firstMainData = recordTomainList.OrderByDescending(m => m.AddDateTime).LastOrDefault();
                var recordEmployeeList = item.HierarchicalQCEmployID.Split("||").ToList();
                var recordEmployeeNames = employeeList.Where(m => recordEmployeeList.Contains(m.EmployeeID)).Select(m => m.EmployeeName);
                var names = recordEmployeeNames.Aggregate("", (current, s) => current + (s + "、"));
                var tableView = new HierarchicalQCRecordTableView()
                {
                    HierarchicalQCSubjectID = item.HierarchicalQCSubjectID,
                    HierarchicalQCRecordID = item.HierarchicalQCRecordID,
                    FormName = subjectData?.FormName,
                    ExamineDate = lastMainData != null && lastMainData.AuditDateTime.HasValue ? lastMainData.AuditDateTime.Value : null,
                    ExamineEmployee = !string.IsNullOrEmpty(names) ? names.Remove(names.Length - 1, 1) : "",
                    ExamineEmployeeIDList = recordEmployeeList,
                    LastPoint = (lastMainData == null || !lastMainData.Result.HasValue) ? null : lastMainData.Result.Value.ToString("0.##"),
                    LastQCDateTime = lastMainData?.AssessDate,
                    FirstPoint = (firstMainData == null || !firstMainData.Result.HasValue) ? null : firstMainData.Result.Value.ToString("0.##"),
                    FirstQCDateTime = firstMainData?.AssessDate,
                    ExamineNumbers = recordTomainList.Count <= 0 ? "0" : recordTomainList.Count.ToString(),
                    AuditNurse = !string.IsNullOrEmpty(item.VerifierEmployeeID) ? employeeList.Find(m => m.EmployeeID == item.VerifierEmployeeID)?.EmployeeName : "",
                    VerifierEmployeeID = item.VerifierEmployeeID,
                    Status = lastMainData != null ? GetStatus(lastMainData.AuditStatus) : "",
                    ExamineObject = (await _departmentListRepository.GetByIDAsync(item.QCObjectID))?.LocalShowName,
                    ExamineDepartmentID = item.DepartmentID,
                    TemplateCode = subjectData.TemplateCode,
                    FormType = subjectData.FormType,
                    QcSubjectID = subjectData.HierarchicalQCSubjectID,
                    StartDate = subjectData.StartDate,
                    EndDate = subjectData.EndDate,
                    ScoreThreshold = subjectData.ScoreThreshold,
                    MinPassingScore = subjectData.MinPassingScore,
                };
                view.Add(tableView);
            }
            return view;
        }

        /// <summary>
        /// 获取质控审核状态
        /// </summary>
        /// <param name="auditStatus"></param>
        /// <returns></returns>
        private static string GetStatus(string auditStatus)
        {
            switch (auditStatus)
            {
                case "0":
                    return "待提交";
                case "1":
                    return "待审核";
                case "2":
                    return "已审核";
                case "3":
                    return "审批未通过";
                case "4":
                    return "审批撤销";
            }
            return null;
        }

        /// <summary>
        /// 获取申诉状态-常态工作过程控制
        /// </summary>
        /// <param name="auditStatus"></param>
        /// <returns></returns>
        private static string GetAppealStatus(string auditStatus)
        {
            return auditStatus switch
            {
                "0" => "未申诉",
                "1" => "申诉中",
                "2" => "申诉通过",
                "3" => "申诉失败",
                "4" => "申诉撤销",
                "5" => "已整改",
                "6" => "护士长确认",
                _ => "",
            };
        }

        /// <summary>
        /// 删除质控考核结果主记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteHierarchicalQCRecord(string recordID, string employeeID)
        {
            if (string.IsNullOrEmpty(recordID))
            {
                return false;
            }
            var record = await _hierarchicalQCRecordRepository.GetDataByRecordID(recordID);
            var mainList = await _hierarchicalQCMainRepository.GetDataByRecordID(recordID);
            var detailList = await _hierarchicalQCDetailRepository.GetQCDetailByQCRecordID(recordID);
            if (mainList.Count > 0)
            {
                foreach (var main in mainList)
                {
                    main.Delete(employeeID);
                }
            }
            if (detailList.Count > 0)
            {
                foreach (var detail in detailList)
                {
                    detail.Delete(employeeID);
                }
            }
            if (record == null)
            {
                return false;
            }
            record.DeleteFlag = "*";
            record.Delete(employeeID);
            //质控对象删除
            await DeleteQCObject(record.HierarchicalQCRecordID, 1, employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 根据主记录ID获取质控考核结果维护记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCMainTableView>> GetHierarchicalQCMainList(string recordID, string hospitalID,string employeeID)
        {
            var returnView = new List<HierarchicalQCMainTableView>();
            var mainList = await _hierarchicalQCMainRepository.GetDataByRecordIDs(new List<string> { recordID });
            if (mainList.Count == 0)
            {
                return returnView;
            }
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(mainList.Select(m => m.AddEmployeeID).ToList());
            if (employeeList.Count <= 0)
            {
                return returnView;
            }
            var qcMainIDs = mainList.Select(m => m.HierarchicalQCMainID).ToList();
            var qcDetailList = await GetTrackDetailsAsync(qcMainIDs, null);
            var deparmentList = await _departmentListRepository.GetByCacheAsync();
            for (int i = 0; i < mainList.Count; i++)
            {
                var departmentData = deparmentList.Find(m => m.DepartmentID == mainList[i].DepartmentID);
                var view = new HierarchicalQCMainTableView
                {
                    HierarchicalQCMainID = mainList[i].HierarchicalQCMainID,
                    Number = (i + 1).ToString(),
                    // TODO 确定考核时间和审核时间分别使用什么字段，这个返回前端的时间是否可空
                    ExamineDate = mainList[i].AssessDate ?? mainList[i].AddDateTime,
                    ExamineEmployee = employeeList.Find(m => m.EmployeeID == mainList[i].AddEmployeeID)?.EmployeeName,
                    Department = departmentData?.LocalShowName ?? "",
                    DepartmentID = mainList[i].DepartmentID,
                    Point = mainList[i].Result,
                    Reader = mainList[i].IsReadFlag.HasValue && mainList[i].IsReadFlag.Value ? "是" : "否",
                    SubmitStatus = GetStatus(mainList[i].AuditStatus),
                    Guidance = mainList[i].Guidance,
                    IsReadFlag = mainList[i].IsReadFlag ?? false,
                    Improvement = mainList[i].Improvement,
                    CurrExamineEmployeeID = mainList[i].AddEmployeeID,
                    AuditStatus = mainList[i].AuditStatus,
                    TrackDetails = qcDetailList.Where(m => m.HierarchicalQCMainID == mainList[i].HierarchicalQCMainID).OrderBy(m => m.Score).ToList(),
                };
                returnView.Add(view);
            }
            return (await GetQcResultShowReadAsync(recordID, employeeID)) ?[..returnView.Where(m => m.AuditStatus == "2")] : returnView;
        }

        /// <summary>
        /// 删除质控维护考核记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteHierarchicalQCMain(string mainID)
        {
            if (string.IsNullOrEmpty(mainID))
            {
                return false;
            }
            var mainData = await _hierarchicalQCMainRepository.GetDataByMainID(mainID);
            if (mainData == null)
            {
                return false;
            }
            mainData.DeleteFlag = "*";
            var detailList = await _hierarchicalQCDetailRepository.GetQCDetailInfoByQCMainID(mainData.HierarchicalQCMainID);
            if (detailList.Count > 0)
            {
                foreach (var detail in detailList)
                {
                    detail.DeleteFlag = "*";
                }
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 获取 质控科室
        /// </summary>
        /// <param name="subjectID"></param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="formLevel">质控等级</param>
        /// <param name="organizationType">组织类别</param>
        /// <returns></returns>
        public async Task<List<AssignDepartmentView>> GetQCAssignDepartmentList(string subjectID, string employeeID, string formLevel, string organizationType = "1")
        {
            var list = await _departmentListRepository.GetByOrganizationType(organizationType);
            var switchFlag = await GetSwitchOfShowOwnDeptAsync(formLevel);
            // 过滤掉四个片区，安装阶层和sort排序
            var returnData = list.Where(m => m.Level != 1 || (m.Level == 1 && m.UpperLevelDepartmentID == 0))
                                .OrderBy(m => m.Level).ThenBy(m => m.Sort).Select(m => new AssignDepartmentView()
                                {
                                    DepartmentID = m.DepartmentID,
                                    DepartmentCode = m.DepartmentCode,
                                    Department = m.LocalShowName,
                                    CheckFlag = false,
                                    UpperLevelDepartmentID = m.UpperLevelDepartmentID
                                }).ToList();
            // 获取部门类别 -根据人员权限筛选出人员当前拥有权限的部门 | 现有一级质控筛选（根据配置）
            if (organizationType == "1" && switchFlag)
            {
                var deptSwitchList = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeID, true);
                returnData = returnData.Where(m => deptSwitchList.Exists(n => n.DepartmentID == m.DepartmentID)).ToList();
            }
            if (!string.IsNullOrEmpty(subjectID))
            {
                var subjectInfo = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(subjectID);
                if (subjectInfo == null)
                {
                    _logger.Error("找不到对应主题 主题ID：" + subjectID);
                    return returnData;
                }
                if (subjectInfo.FormType != "6")
                {
                    _logger.Error("非部门质控 主题ID：" + subjectID);
                    return returnData;
                }
                var assignDepartment = await _hierarchicalQCRecordRepository.GetQCReocrdDepartmentBySubjectID(subjectID);
                list = list.Where(m => !assignDepartment.Contains(m.DepartmentID)).ToList();
            }
            return returnData;
        }

        /// <summary>
        /// 获取质控质控及审核人员
        /// </summary>
        /// <param name="formLevel">质控等级</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        public async Task<List<AssignEmployeeView>> GetQCAssignEmployeeList(string formLevel, string employeeID)
        {
            bool switchFlag = await GetSwitchOfShowOwnDeptAsync(formLevel);
            List<EmployeeDepartmentSwitchInfo> deptSwitchList = null;
            if (switchFlag)
            {
                deptSwitchList = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeID, true);
            }
            var staffs = await _employeeStaffDataRepository.GetStaffsByDepartmentID(null, new string[] { "HL", "XZ" });
            if (deptSwitchList != null && deptSwitchList.Count >= 0)
            {
                staffs = staffs.Where(m => deptSwitchList.Exists(n => n.DepartmentID == m.DepartmentID)).ToList();
            }
            var capacities = await _capabilityLevelRepository.GetAll<CapabilityLevelInfo>();
            var personalData = await _employeePersonalDataRepository.GetListByEmployeeIDs(staffs.Select(m => m.EmployeeID).ToList());
            var departments = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var data = from a in personalData
                       join b in staffs on a.EmployeeID equals b.EmployeeID
                       join c in capacities on b.CapabilityLevelID equals c.CapabilityLevelID into bc
                       from c in bc.DefaultIfEmpty()
                       join d in departments on b.DepartmentID equals d.DepartmentID
                       // 管理层的员工排在前面
                       orderby c?.Level descending
                       select new AssignEmployeeView
                       {
                           EmployeeID = a.EmployeeID,
                           EmployeeName = a.EmployeeName,
                           NamePinyin = a.NamePinyin,
                           DepartmentName = d.LocalShowName,
                           CapabilityLevel = c?.CapabilityLevelName,
                           DepartmentID = b.DepartmentID
                       };
            return data.ToList();
        }

        /// <summary>
        /// 根据质控等级和配置判断主题指派的时候显示的科室和人员是所有部门的或自己拥有权限的部门的
        /// </summary>
        /// <param name="formLevel">质控等级</param>
        /// <returns></returns>
        private async Task<bool> GetSwitchOfShowOwnDeptAsync(string formLevel)
        {
            var settingParams = new SettingDictionaryParams
            {
                DataType = "2",
                SettingType = "HierarchicalQC",
                SettingTypeCode = "OnlyShowOwnDept",
                SettingTypeValue = formLevel
            };
            var switchFlag = await _settingDictionaryRepository.GetSettingSwitch(settingParams);
            return switchFlag;
        }

        /// <summary>
        /// 质控指派保存
        /// </summary>
        /// <param name="view"></param>
        /// <param name="employee"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SaveSubjectAssign(SubjectAssignView view, string employee, string hospitalID)
        {
            if (view == null || string.IsNullOrEmpty(view.HierarchicalQCSubjectID))
            {
                _logger.Error("保存参数有误");
                return false;
            }
            var subjectInfo = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(view.HierarchicalQCSubjectID);
            if (subjectInfo == null)
            {
                _logger.Error("找不到对应主题 主题ID：" + view.HierarchicalQCSubjectID);
                return false;
            }
            if (subjectInfo.FormType == "6")
            {
                _logger.Error("按部门质控才需要指派" + view.HierarchicalQCSubjectID);
                return false;
            }
            var oldQCRecordList = await _hierarchicalQCRecordRepository.GetQCReocrdBySubjectIDAndDepart(view.HierarchicalQCSubjectID);
            if (oldQCRecordList.Count > 0 && view.AssignTableData.Count == 0)
            {
                foreach (var item in oldQCRecordList)
                {
                    await DeleteAllQCDataByQCRecordID(item.HierarchicalQCRecordID, employee);
                }
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            var addList = new List<HierarchicalQCRecordInfo>();
            var departmentListIDs = new List<int>();
            foreach (var item in view.AssignTableData)
            {
                foreach (var department in item.DepartmentViews)
                {
                    departmentListIDs.Add(department.DepartmentID);
                    var oldQCRecord = oldQCRecordList.Find(m => m.QCObjectID == department.DepartmentID);
                    if (oldQCRecord == null)
                    {
                        addList.Add(AddQCRecord(item, department.DepartmentID, subjectInfo, employee, hospitalID));
                        continue;
                    }
                    UpdateQCRecord(item, oldQCRecord, employee);
                }
            }
            //删除移除的科室数据
            foreach (var oldRecord in oldQCRecordList)
            {
                if (subjectInfo.FormType != "6" && !departmentListIDs.Contains(oldRecord.QCObjectID))
                {
                    await DeleteAllQCDataByQCRecordID(oldRecord.HierarchicalQCRecordID, employee);
                };
            }
            if (addList.Count > 0)
            {
                await _unitOfWork.GetRepository<HierarchicalQCRecordInfo>().InsertAsync(addList);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 新增质控主记录
        /// </summary>
        /// <param name="item"></param>
        /// <param name="departmentID"></param>
        /// <param name="subjectInfo"></param>
        /// <param name="employee"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public static HierarchicalQCRecordInfo AddQCRecord(AssignTableItem item, int departmentID, HierarchicalQCSubjectInfo subjectInfo, string employee, string hospitalID)
        {
            HierarchicalQCRecordInfo record = new()
            {
                HospitalID = hospitalID,
                HierarchicalQCSubjectID = subjectInfo.HierarchicalQCSubjectID,
                HierarchicalQCFormID = subjectInfo.HierarchicalQCFormID,
                DepartmentID = departmentID,
                HierarchicalQCFormLevel = subjectInfo.HierarchicalQCFormLevel,
                FirstResult = null,
                LastResult = null,
                AddDateTime = DateTime.Now,
                AddEmployeeID = employee,
                ModifyDateTime = DateTime.Now,
                ModifyEmployeeID = employee,
                DeleteFlag = "",
                QCObjectID = departmentID
            };
            record.HierarchicalQCRecordID = record.GetId();
            record.HierarchicalQCEmployID = string.Join("||", item.HierarchicalQCEmploy.Select(m => m.EmployeeID));
            record.VerifierEmployeeID = item.VerifierEmployee.FirstOrDefault()?.EmployeeID ?? "";
            return record;
        }

        /// <summary>
        /// 主记录修改
        /// </summary>
        /// <param name="item"></param>
        /// <param name="oldQCRecord"></param>
        /// <param name="employee"></param>
        private static void UpdateQCRecord(AssignTableItem item, HierarchicalQCRecordInfo oldQCRecord, string employee)
        {
            var updataFlag = false;
            if (oldQCRecord.VerifierEmployeeID != (item.VerifierEmployee.FirstOrDefault()?.EmployeeID ?? ""))
            {
                oldQCRecord.VerifierEmployeeID = item.VerifierEmployee.FirstOrDefault()?.EmployeeID ?? "";
                updataFlag = true;
            }
            if (oldQCRecord.HierarchicalQCEmployID != string.Join("||", item.HierarchicalQCEmploy.Select(m => m.EmployeeID)))
            {
                oldQCRecord.HierarchicalQCEmployID = string.Join("||", item.HierarchicalQCEmploy.Select(m => m.EmployeeID));
                updataFlag = true;
            }
            if (updataFlag)
            {
                oldQCRecord.Modify(employee);
            }
        }

        /// <summary>
        /// 删除质控记录
        /// </summary>
        /// <param name="qcRecordID"></param>
        /// <param name="employee"></param>
        /// <returns></returns>
        public async Task<bool> DeleteAllQCDataByQCRecordID(string qcRecordID, string employee)
        {
            var qcRecord = await _hierarchicalQCRecordRepository.GetDataByRecordID(qcRecordID);
            if (qcRecord == null)
            {
                _logger.Error("找不到主记录数据 删除质控主记录失败  记录ID：" + qcRecordID);
                return false;
            }
            qcRecord.Delete(employee);
            var qcMain = await _hierarchicalQCMainRepository.GetDataByRecordID(qcRecord.HierarchicalQCRecordID);
            if (qcMain.Count > 0)
            {
                qcMain.ForEach(m => m.Delete(employee));
            }
            var qcDetail = await _hierarchicalQCDetailRepository.GetQCDetailByQCRecordID(qcRecord.HierarchicalQCRecordID);
            if (qcDetail.Count > 0)
            {
                qcDetail.ForEach(m => m.Delete(employee));
            }
            return true;
        }

        /// <summary>
        /// 主题指派页面回显
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        public async Task<SubjectAssignView> GetSubjectAssignView(string subjectID)
        {
            SubjectAssignView view = new SubjectAssignView()
            {
                HierarchicalQCSubjectID = subjectID,
                AssignTableData = new List<AssignTableItem>()
            };
            if (string.IsNullOrEmpty(subjectID))
            {
                return view;
            }
            var qcRecord = await _hierarchicalQCRecordRepository.GetQCReocrdBySubjectIDAndDepart(subjectID);
            if (qcRecord.Count == 0)
            {
                return view;
            }
            var departmentList = await _departmentListRepository.GetAllDictAsync();
            var employeeList = await _employeePersonalDataRepository.GetIDAndNameData();
            var hierarchicalQCEmployIDs = qcRecord.Select(m => m.HierarchicalQCEmployID).Distinct().ToList();

            foreach (var record in qcRecord)
            {
                var depart = new AssignDepartmentView()
                {
                    DepartmentID = record.QCObjectID,
                    DepartmentCode = departmentList.Find(m => m.DepartmentID == record.QCObjectID)?.DepartmentCode ?? "",
                    Department = departmentList.Find(m => m.DepartmentID == record.QCObjectID)?.LocalShowName ?? "",
                    CheckFlag = record.FirstResult.HasValue
                };
                var isHaveData = view.AssignTableData.Find(m => string.Join("||", m.HierarchicalQCEmploy.Select(m => m.EmployeeID).ToList()) == record.HierarchicalQCEmployID && m.VerifierEmployee.Select(m => m.EmployeeID).ToList().Contains(record.VerifierEmployeeID));
                if (isHaveData == null)
                {
                    var viewItem = new AssignTableItem()
                    {
                        DepartmentViews = new List<AssignDepartmentView>(),
                        HierarchicalQCEmploy = new List<AssignEmployeeView>(),
                        VerifierEmployee = new List<AssignEmployeeView>(),
                    };
                    var hierarchicalQCEmployIDList = record.HierarchicalQCEmployID.Split("||").ToList();
                    hierarchicalQCEmployIDList.ForEach(m =>
                    {
                        viewItem.HierarchicalQCEmploy.Add(new AssignEmployeeView()
                        {
                            EmployeeID = m,
                            EmployeeName = employeeList.Find(n => n.EmployeeID == m)?.EmployeeName ?? ""
                        });
                    });
                    viewItem.VerifierEmployee.Add(new AssignEmployeeView()
                    {
                        EmployeeID = record.VerifierEmployeeID,
                        EmployeeName = employeeList.Find(n => n.EmployeeID == record.VerifierEmployeeID)?.EmployeeName ?? ""
                    });
                    viewItem.DepartmentViews.Add(depart);
                    view.AssignTableData.Add(viewItem);
                }
                else
                {
                    isHaveData.DepartmentViews.Add(depart);
                }
            }
            return view;
        }

        /// <summary>
        /// 删除质控主题
        /// </summary>
        /// <param name="hierarchicalQCSubjectID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteSubject(string hierarchicalQCSubjectID, string employeeID)
        {
            if (string.IsNullOrEmpty(hierarchicalQCSubjectID))
            {
                _logger.Error("删除失败 入参无效");
                return false;
            }
            var qcSubject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(hierarchicalQCSubjectID);
            if (qcSubject == null)
            {
                _logger.Error("删除失败 数据库中找不到数据hierarchicalQCSubjectID： " + hierarchicalQCSubjectID);
                return false;
            }
            qcSubject.Delete(employeeID);
            var qcRecordList = await _hierarchicalQCRecordRepository.GetQCReocrdBySubjectID(hierarchicalQCSubjectID);
            if (qcRecordList == null)
            {
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            foreach (var record in qcRecordList)
            {
                await DeleteAllQCDataByQCRecordID(record.HierarchicalQCRecordID, employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 根据AssessListID获取对应的备注内容
        /// </summary>
        /// <param name="assessListID"></param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        public async Task<List<KeyValueString>> GeHierarchicalQCRemarkAsync(int assessListID)
        {
            var returnData = new List<KeyValueString>();
            if (assessListID == 0)
            {
                _logger.Warn($"获取质控备注时，参数为空assessListID={assessListID}");
                return returnData;
            }
            var list = await _hierarchicalQCRemarkRepository.GetQCRemarkViewByAssessListIDAsync(assessListID);
            return list.Select(m => new KeyValueString()
            {
                Key = m.HQCRemarkID,
                Value = m.Remark
            }).ToList();
        }

        /// <summary>
        /// 保存新增的质控备注内容
        /// </summary>
        /// <param name="remarkView">保存的质控备注参数</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">执行人工号</param>
        /// <returns>1.返回布尔值 2.新增成功时返回对象</returns>
        public async Task<object> SaveHierarchicalQCRemarkAsync(HierarchicalQCRemarkView remarkView, string hospitalID, string employeeID)
        {
            if (remarkView.HQCRemarkID != null)
            {
                var recordInfo = await _hierarchicalQCRemarkRepository.GetQCRemarkViewAsync(remarkView.HQCRemarkID);
                if (recordInfo != null)
                {
                    var saveFlag = UpdateHierarchicalQCRemark(remarkView, recordInfo, employeeID);
                    if (!saveFlag)
                    {
                        return false;
                    }
                    return await _unitOfWork.SaveChangesAsync() > 0;
                }
            }
            var addRecord = await AddHierarchicalQCRemark(remarkView, hospitalID, employeeID);
            var success = await _unitOfWork.SaveChangesAsync() > 0;
            if (!success)
            {
                return false;
            }
            return new HierarchicalQCRemarkView
            {
                HQCRemarkID = addRecord.HierarchicalQCRemarkID,
                HQCAssessListID = addRecord.HierarchicalQCAssessListID,
                Remark = addRecord.Remark
            };
        }

        /// <summary>
        /// 新增记录
        /// </summary>
        /// <param name="remarkView"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<HierarchicalQCRemarkInfo> AddHierarchicalQCRemark(HierarchicalQCRemarkView remarkView, string hospitalID, string employeeID)
        {
            var remarkInfo = new HierarchicalQCRemarkInfo()
            {
                HierarchicalQCAssessListID = remarkView.HQCAssessListID,
                Remark = remarkView.Remark,
                HospitalID = hospitalID,
            };
            remarkInfo.HierarchicalQCRemarkID = remarkInfo.GetId();
            remarkInfo.Add(employeeID);
            remarkInfo.Modify(employeeID);
            await _unitOfWork.GetRepository<HierarchicalQCRemarkInfo>().InsertAsync(remarkInfo);

            return remarkInfo;
        }

        /// <summary>
        /// 更新质控备注记录
        /// </summary>
        /// <param name="remarkView"></param>
        /// <param name="recordInfo"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private bool UpdateHierarchicalQCRemark(HierarchicalQCRemarkView remarkView, HierarchicalQCRemarkInfo recordInfo, string employeeID)
        {
            var update = false;
            if (recordInfo.HierarchicalQCAssessListID != remarkView.HQCAssessListID)
            {
                return update;
            }
            if (recordInfo.Remark != remarkView.Remark)
            {
                recordInfo.Remark = remarkView.Remark;
                update = true;
            }
            if (update)
            {
                recordInfo.Modify(employeeID);
            }
            return update;
        }

        /// <summary>
        /// 获取质控评估明细|根据维护记录ID
        /// </summary>
        /// <param name="careMainID"></param>
        /// <param name="trackFlag">跟踪标记为true 满分禁止勾选</param>
        /// <returns></returns>
        public async Task<object> GetHierarchicalQCDetailsAsync(string careMainID, bool? trackFlag)
        {
            List<HierarchicalQCDetailView> details = await _hierarchicalQCDetailRepository.GetQCDetailByQCMainID(careMainID);

            if (trackFlag.HasValue && trackFlag.Value && details.Count > 0)
            {
                // TODO：写死内容
                details.ForEach(m => m.ReadOnly = m.Value == "5" ? "1" : "0");
                return details;
            }
            return details;
        }

        /// <summary>
        /// 保存质控评估维护记录和明细内容
        /// </summary>
        /// <param name="mainAndDetailView">前端参数</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<string> SaveHierarchicalQCMainAndDetailsAsync(HierarchicalQCMainAndDetailView mainAndDetailView, string hospitalID, string employeeID, string nurseEmployeeID)
        {
            if (mainAndDetailView == null || mainAndDetailView.QcMain == null)
            {
                _logger.Error("保存失败！入参有误！");
                return "";
            }
            var record = await _hierarchicalQCRecordRepository.GetDataByRecordID(mainAndDetailView.QcMain.HierarchicalQCRecordID);
            var subject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(mainAndDetailView.QcSubjectID);
            if (subject != null && record == null)
            {
                record ??= await CreateQCRecord(mainAndDetailView, subject, employeeID, hospitalID, nurseEmployeeID);
            }
            if (record == null)
            {
                _logger.Error($"无法获取质控主记录信息={mainAndDetailView.QcMain.HierarchicalQCRecordID}");
                return "";
            }
            // 保存维护记录
            await SaveHierachicalQCMain(mainAndDetailView.QcMain, hospitalID, employeeID, record);
            // 保存质控评估明细数据并返回问题发生人
            var problemOccurEmployeeIDs = await SaveHierarchicalQCDetail(mainAndDetailView, hospitalID, employeeID, record);
            await SaveRemark(mainAndDetailView.QcDetails, hospitalID, employeeID);
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            if (result)
            {
                if (problemOccurEmployeeIDs.Count > 0)
                {
                    await SendNotice(problemOccurEmployeeIDs, "您有新的常态工作过程控制问题", NORMAL_WORKING_CONTRPL_MOBILE_PATH);
                }
                // 给问题发生人发送消息
                return record.HierarchicalQCRecordID;
            }
            return "";
        }

        /// <summary>
        /// 保存质控评估维护记录
        /// </summary>
        /// <param name="mainAndDetailView"></param>
        /// <param name="subject"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="nurseEmployeeID"></param>
        /// <returns></returns>
        private async Task<HierarchicalQCRecordInfo> CreateQCRecord(HierarchicalQCMainAndDetailView mainAndDetailView, HierarchicalQCSubjectInfo subject, string employeeID
            , string hospitalID, string nurseEmployeeID)
        {
            HierarchicalQCRecordInfo record = new()
            {
                HospitalID = hospitalID,
                HierarchicalQCSubjectID = subject.HierarchicalQCSubjectID,
                HierarchicalQCFormID = subject.HierarchicalQCFormID,
                DepartmentID = mainAndDetailView.QcMain.DepartmentID == 0 ? subject.AddDepartmentID : mainAndDetailView.QcMain.DepartmentID,
                HierarchicalQCFormLevel = subject.HierarchicalQCFormLevel,
                FirstResult = null,
                LastResult = null,
                AddDateTime = DateTime.Now,
                AddEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID,
                DeleteFlag = "",
                HierarchicalQCEmployID = employeeID
            };
            record.HierarchicalQCRecordID = record.GetId();
            record.VerifierEmployeeID = string.IsNullOrWhiteSpace(nurseEmployeeID) ? string.Empty : nurseEmployeeID;
            await _unitOfWork.GetRepository<HierarchicalQCRecordInfo>().InsertAsync(record);
            return record;
        }

        /// <summary>
        /// 保存备注信息记录
        /// </summary>
        /// <param name="qcDetails">质控明细</param>
        /// <param name="hospitalID">医院累呗</param>
        /// <param name="employeeID">员工工号</param>
        /// <returns></returns>
        private async Task SaveRemark(List<HierarchicalQCDetailView> qcDetails, string hospitalID, string employeeID)
        {
            if (qcDetails == null || qcDetails.Count <= 0)
            {
                return;
            }
            var itemIDs = qcDetails.Select(m => m.ItemID).Distinct().ToList();
            var remarks = await _hierarchicalQCRemarkRepository.GetQCRemarkViewByAssessListIDsAsync(itemIDs);
            foreach (var detail in qcDetails)
            {
                if (string.IsNullOrEmpty(detail.Problem))
                {
                    continue;
                }

                var existedRemarks = remarks.Where(m => m.HQCAssessListID == detail.ItemID && (m.Remark == detail.Problem || m.Remark == detail.BrightSpot || m.Remark == detail.Remark)).ToList();
                // 已存在 跳过
                if (existedRemarks.Count > 0)
                {
                    continue;
                }
                if (!string.IsNullOrWhiteSpace(detail.Problem))
                {
                    await AddRemark(detail.ItemID, detail.Problem, employeeID, hospitalID);
                }
                if (!string.IsNullOrWhiteSpace(detail.BrightSpot))
                {
                    await AddRemark(detail.ItemID, detail.BrightSpot, employeeID, hospitalID);
                }
                if (!string.IsNullOrWhiteSpace(detail.Remark))
                {
                    await AddRemark(detail.ItemID, detail.Remark, employeeID, hospitalID);
                }
            }
            return;
        }

        /// <summary>
        /// 添加备注选项
        /// </summary>
        /// <param name="itemID"></param>
        /// <param name="remark"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task AddRemark(int itemID, string remark, string employeeID, string hospitalID)
        {
            var remarkInfo = new HierarchicalQCRemarkInfo
            {
                HierarchicalQCAssessListID = itemID,
                HospitalID = hospitalID,
                Remark = remark,
                DeleteFlag = ""
            };
            remarkInfo.HierarchicalQCRemarkID = remarkInfo.GetId();
            remarkInfo.Add(employeeID);
            remarkInfo.Modify(employeeID);
            await _unitOfWork.GetRepository<HierarchicalQCRemarkInfo>().InsertAsync(remarkInfo);
        }

        /// <summary>
        /// 保存质控评估明细数据并返回问题发生人
        /// </summary>
        /// <param name="mainAndDetailView">前端保存的参数</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工ID</param>
        /// <param name="recordInfo"></param>
        /// <returns></returns>
        private async Task<List<string>> SaveHierarchicalQCDetail(HierarchicalQCMainAndDetailView mainAndDetailView, string hospitalID, string employeeID, HierarchicalQCRecordInfo recordInfo)
        {
            var nowDateTime = DateTime.Now;
            var qcDetailViews = mainAndDetailView.QcDetails;
            var qcMainView = mainAndDetailView.QcMain;
            var employeeSettingParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "NormalWorkingComponentListID",
                SettingTypeValue = "Employee"
            };
            var employeeSettingList = await _settingDictionaryRepository.GetSettingDictionary(employeeSettingParams);
            var employeeSetting = employeeSettingList.FirstOrDefault();
            var hierarchicalQCSubject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(recordInfo.HierarchicalQCSubjectID);
            var settings = await _dynamicFormDetailRepository.GetFormSettingsByFormRecordID(hierarchicalQCSubject.TemplateCode, MAXSCORE_ATTRIBUTEID_36);
            // 删除旧明细
            if (!string.IsNullOrEmpty(qcMainView.HierarchicalQCMainID))
            {
                var existedDetails = await _hierarchicalQCDetailRepository.GetQCDetailInfoByQCMainID(qcMainView.HierarchicalQCMainID);
                existedDetails.ForEach(m => m.Delete(employeeID));
            }
            var settingDictionaryParams = new SettingDictionaryParams
            {
                DataType = "1",
                SettingType = "HierarchicalQC"
            };
            var tagMappingList = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            var groupAssessListIDs = new List<int>();
            var tagViews = new List<DocumentTagView>();
            //新增明细
            List<HierarchicalQCDetailInfo> details = new List<HierarchicalQCDetailInfo>();
            if (qcDetailViews != null)
            {
                foreach (var item in qcDetailViews)
                {
                    var detail = new HierarchicalQCDetailInfo
                    {
                        HierarchicalQCAssessListID = item.ItemID,
                        GroupID = item.GroupID,
                        Problem = item.Problem,
                        BrightSpot = item.BrightSpot,
                        Remark = item.Remark,
                        Result = string.IsNullOrWhiteSpace(item.Value) ? null : item.Value,
                        HospitalID = hospitalID,
                        HierarchicalQCFormLevel = recordInfo.HierarchicalQCFormLevel,
                        HierarchicalQCFormID = recordInfo.HierarchicalQCFormID,
                        HierarchicalQCMainID = qcMainView.HierarchicalQCMainID,
                        HierarchicalQCRecordID = recordInfo.HierarchicalQCRecordID,
                        AssessDate = mainAndDetailView.QcMain.ExamineDate,
                        ParentID = item.ParentID,
                    };
                    if (settings.TryGetValue(item.ItemID.ToString(), out var setting) && int.TryParse(setting, out var maxScore))
                    {
                        detail.FullMark = maxScore;
                    }
                    detail.HierarchicalQCDetailID = detail.GetId();
                    detail.AddDateTime = nowDateTime;
                    detail.AddEmployeeID = employeeID;
                    detail.ModifyDateTime = nowDateTime;
                    detail.ModifyEmployeeID = employeeID;
                    details.Add(detail);
                    if (item.ParentID != null && (QCASSESS_LIST_974 == item.ParentID || QCASSESS_LIST_975 == item.ParentID))
                    {
                        groupAssessListIDs.Add(item.ParentID.Value);
                    }
                }
                // 获取质控明细中对应的标签ID、质控等级对应的标签ID
                tagViews = GetTagListAsync(groupAssessListIDs.Select(m => m.ToString()).ToList(), recordInfo.HierarchicalQCFormLevel, null, tagMappingList);
            }
            // 将文件转换为明细
            var fileDetails = await GetDetailByFiles(mainAndDetailView, tagViews, nowDateTime, employeeID, hospitalID, recordInfo, qcMainView.HierarchicalQCMainID, tagMappingList);
            if (fileDetails != null)
            {
                details.AddRange(fileDetails);
            }

            //质控对象处理
            var problemOccurEmployeeIDs = new List<string>();
            if (employeeSetting != null && !string.IsNullOrEmpty(employeeSetting.SettingValue))
            {
                var employeeIDDetail = qcDetailViews?.Find(m => m.ItemID.ToString() == employeeSetting.SettingValue && !string.IsNullOrEmpty(m.Value));
                problemOccurEmployeeIDs = await QcObjectMaintain(recordInfo.HierarchicalQCRecordID, employeeIDDetail?.Value ?? "", 1, employeeID);
            }
            await _unitOfWork.GetRepository<HierarchicalQCDetailInfo>().InsertAsync(details);
            return problemOccurEmployeeIDs;
        }

        /// <summary>
        /// 根据assessListID集合/质控等级/质控类型 获取对应的文档标签ID
        /// </summary>
        /// <param name="assessListIDs"></param>
        /// <param name="formLevel"></param>
        /// <param name="formType"></param>
        /// <param name="tagMappingList"></param>
        /// <returns></returns>
        public List<DocumentTagView> GetTagListAsync(List<string> assessListIDs, string formLevel, string formType, List<SettingDictionaryInfo> tagMappingList)
        {
            if (tagMappingList.Count <= 0)
            {
                return [];
            }
            var views = new List<DocumentTagView>();
            if (assessListIDs != null && assessListIDs.Count > 0)
            {
                views.AddRange(tagMappingList.Where(m => m.SettingTypeCode == "TagMappingAssessListID" && assessListIDs.Contains(m.SettingTypeValue))
                .Select(m => new DocumentTagView
                {
                    DocumentTagListID = m.SettingValue
                }));
            }
            //formLevel和FormType 使用相同配置
            if (!string.IsNullOrEmpty(formLevel))
            {
                views.AddRange(tagMappingList.Where(m => m.SettingTypeCode == "TagMappingFormType" && m.SettingTypeValue == formLevel)
                .Select(m => new DocumentTagView
                {
                    DocumentTagListID = m.SettingValue
                }));
            }
            if (!string.IsNullOrEmpty(formType))
            {
                views.AddRange(tagMappingList.Where(m => m.SettingTypeCode == "TagMappingFormType" && m.SettingTypeValue == formType)
                .Select(m => new DocumentTagView
                {
                    DocumentTagListID = m.SettingValue
                }));
            }
            return views;
        }

        /// <summary>
        /// 获取文件质控明细
        /// </summary>
        /// <param name="mainAndDetailView"></param>
        /// <param name="tagViews"></param>
        /// <param name="nowDateTime"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="recordInfo"></param>
        /// <param name="hierarchicalQCMainID"></param>
        /// <param name="tagMappingList"></param>
        /// <returns></returns>
        private async Task<List<HierarchicalQCDetailInfo>> GetDetailByFiles(HierarchicalQCMainAndDetailView mainAndDetailView, List<DocumentTagView> tagViews, DateTime nowDateTime, string employeeID, string hospitalID, HierarchicalQCRecordInfo recordInfo, string hierarchicalQCMainID, List<SettingDictionaryInfo> tagMappingList)
        {
            if (mainAndDetailView.FileList == null || mainAndDetailView.FileList.Count <= 0)
            {
                return null;
            }
            var qcSubject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(mainAndDetailView.QcSubjectID);
            if (qcSubject == null)
            {
                return null;
            }
            var userName = await _employeePersonalDataRepository.GetEmployeeNameByID(employeeID);
            // 组装文档明细属性集合 TODO：等文档管理的文件属性字典表整理好，这里替换为正常的
            var documentDetails = new List<DocumentDetailView>()
            {
                new (){ GroupID = 1, ItemID = 1, Value = "测试" },
            };
            var details = new List<HierarchicalQCDetailInfo>();
            var fileList = new List<string>();
            // 循环文件 组装文件明细
            foreach (var files in mainAndDetailView.FileList)
            {
                var currTagViews = tagViews;
                if (files.GroupID != null)
                {
                    var groupTags = GetTagListAsync([files.GroupID.ToString(), files.ID.ToString()], null, qcSubject.FormType, tagMappingList);
                    currTagViews.AddRange(groupTags);
                }
                // 原本已经存储的文件ID + 本地新增的文件的文件ID
                var uploadFileList = await UploadFileListAsync(employeeID, userName, documentDetails, files, currTagViews);
                fileList = files.FileIDs ?? [];
                fileList.AddRange(uploadFileList);
                // 文件记录对应的质控明细
                var detail = SetHierarchicalQCDetailInfo(mainAndDetailView, nowDateTime, employeeID, hospitalID, recordInfo, hierarchicalQCMainID, fileList, files);
                details.Add(detail);
            }
            return details;
        }

        /// <summary>
        /// 上传质控模板中的多个文件、图片到文件管理系统，并保存CommonFile记录
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="userName"></param>
        /// <param name="documentDetails"></param>
        /// <param name="files"></param>
        /// <param name="currTagViews"></param>
        /// <returns></returns>
        private async Task<List<string>> UploadFileListAsync(string employeeID, string userName, List<DocumentDetailView> documentDetails, FileListView files, List<DocumentTagView> currTagViews)
        {
            var fileList = new List<string>();
            foreach (var file in files.Files ?? [])
            {
                // 组装保存文档接口参数
                var document = new DocumentView()
                {
                    DocumentMainView = new DocumentMainView()
                    {
                        SourceSystem = "NursingManagement",
                        DocumentTypeID = 1,
                        UserID = employeeID,
                        UserName = userName,
                        DocumentTitle = file.FileName,
                        DocumentType = file.FileName.Substring(file.FileName.LastIndexOf(".") + 1),
                        DocumentAbstract = "",
                        DocumentStatus = 20
                    },
                    DocumentDetailViews = documentDetails,
                    DocumentTagViews = currTagViews
                };
                // 调用文档管理系统的接口保存文件
                var view = await _fileService.UpLoadFile(file, document);
                // 将文件记录表的主键写入 文件明细结果
                if (view != null && !string.IsNullOrEmpty(view.FileID))
                {
                    fileList.Add(view.FileID);
                }
            }
            return fileList;
        }

        /// <summary>
        /// 设置一条质控明细（包含文件信息的）
        /// </summary>
        /// <param name="mainAndDetailView"></param>
        /// <param name="nowDateTime"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="recordInfo"></param>
        /// <param name="hierarchicalQCMainID"></param>
        /// <param name="fileList"></param>
        /// <param name="files"></param>
        /// <returns></returns>
        private HierarchicalQCDetailInfo SetHierarchicalQCDetailInfo(HierarchicalQCMainAndDetailView mainAndDetailView, DateTime nowDateTime, string employeeID, string hospitalID, HierarchicalQCRecordInfo recordInfo, string hierarchicalQCMainID, List<string> fileList, FileListView files)
        {
            // 组装文件的质控明细项
            var detail = new HierarchicalQCDetailInfo
            {
                HierarchicalQCAssessListID = files.ID,
                GroupID = files.GroupID,
                ParentID = files.ParentID,
                Result = ListToJson.ToJson(new Dictionary<string, List<string>>
                    {
                        { "fileIDs",fileList }
                    }),
                HospitalID = hospitalID,
                HierarchicalQCFormLevel = recordInfo.HierarchicalQCFormLevel,
                HierarchicalQCFormID = recordInfo.HierarchicalQCFormID,
                HierarchicalQCMainID = hierarchicalQCMainID,
                HierarchicalQCRecordID = recordInfo.HierarchicalQCRecordID,
                AssessDate = mainAndDetailView.QcMain.ExamineDate
            };
            detail.HierarchicalQCDetailID = detail.GetId();
            detail.AddDateTime = nowDateTime;
            detail.AddEmployeeID = employeeID;
            detail.ModifyDateTime = nowDateTime;
            detail.ModifyEmployeeID = employeeID;

            return detail;
        }

        /// <summary>
        /// 质控对象维护
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="stringValues"></param>
        /// <param name="objectType"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<List<string>> QcObjectMaintain(string recordID, string stringValues, int objectType, string employeeID)
        {
            var objectValues = new List<string>();
            if (string.IsNullOrEmpty(recordID) || string.IsNullOrEmpty(stringValues))
            {
                _logger.Error("质控对象入参有误！");
                return objectValues;
            }
            try
            {
                //人员多选
                if (stringValues.Contains('['))
                {
                    objectValues = ListToJson.ToList<List<string>>(stringValues);
                }
                else//人员单选
                {
                    var objectValue = ListToJson.ToList<string>(stringValues);
                    objectValues.Add(objectValue);
                }
            }
            catch (Exception ex)
            {
                _logger.Error("质控对象JSON转换失败！" + ex);
            }
            if ((objectValues?.Count ?? 0) == 0)
            {
                _logger.Error("质控对象入参有误！");
                return objectValues;
            }
            var qcObjectInfos = await _hierarchicalQCObjectRepository.GetHierarchicalQCObjectInfoByRecordID(recordID, objectType);
            if (qcObjectInfos.Count > 0)
            {
                qcObjectInfos.ForEach(m => m.Delete(employeeID));
            }
            var addQCObjectInfos = new List<HierarchicalQCObjectInfo>();
            objectValues.ForEach(m =>
            {
                var qcObject = new HierarchicalQCObjectInfo()
                {
                    HierarchicalQCRecordID = recordID,
                    HierarchicalQCDetailID = "",
                    ObjectType = 1,
                    ObjectValue = m,
                    DeleteFlag = "",
                };
                qcObject.Add(employeeID);
                qcObject.Modify(employeeID);
                addQCObjectInfos.Add(qcObject);
            });
            await _unitOfWork.GetRepository<HierarchicalQCObjectInfo>().InsertAsync(addQCObjectInfos);
            return objectValues;
        }

        /// <summary>
        /// 删除质控对象
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="objectType"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<bool> DeleteQCObject(string recordID, int objectType, string employeeID)
        {
            var qcObjectInfos = await _hierarchicalQCObjectRepository.GetHierarchicalQCObjectInfoByRecordID(recordID, objectType);
            if (qcObjectInfos.Count == 0)
            {
                return true;
            }
            qcObjectInfos.ForEach(m => m.Delete(employeeID));
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 保存质控评估主记录
        /// </summary>
        /// <param name="hierarchicalQMainTableView"></param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工ID</param>
        /// <param name="recordInfo">质控计划记录信息</param>
        /// <returns></returns>
        public async Task<string> SaveHierachicalQCMain(HierarchicalQCMainTableView hierarchicalQMainTableView, string hospitalID, string employeeID, HierarchicalQCRecordInfo recordInfo)
        {
            if (!string.IsNullOrEmpty(hierarchicalQMainTableView.HierarchicalQCMainID))
            {
                var existedMain = await _hierarchicalQCMainRepository.GetDataByMainID(hierarchicalQMainTableView.HierarchicalQCMainID);
                // 更新回写主记录分数
                UpdateRecord(recordInfo, hierarchicalQMainTableView.Point);
                UpdateQCMain(existedMain, hierarchicalQMainTableView, recordInfo, employeeID);
                return hierarchicalQMainTableView.HierarchicalQCMainID;
            }
            return await AddQCMain(hierarchicalQMainTableView, hospitalID, employeeID, recordInfo);
        }

        /// <summary>
        /// 获取最后指控分数及质控时间
        /// </summary>
        /// <param name="qcRecoedID"></param>
        /// <returns></returns>
        public async Task<Tuple<decimal?, DateTime>> GetLastQCMain(string qcRecoedID)
        {
            var lastQCMain = await _hierarchicalQCMainRepository.GetLastDataByRecordID(qcRecoedID);
            if (lastQCMain != null)
            {
                return new Tuple<decimal?, DateTime>(lastQCMain.Result, lastQCMain.AddDateTime);
            }
            return null;
        }

        /// <summary>
        /// 更新质控评估主记录
        /// </summary>
        /// <param name="existedMain">数据库中已经存在的维护主记录</param>
        /// <param name="hierarchicalQMainTableView">前端传过来的关于主记录的保存信息</param>
        /// <param name="recordInfo"></param>
        /// <param name="employeeID">被考核人工号</param>
        /// <returns></returns>
        private static bool UpdateQCMain(HierarchicalQCMainInfo existedMain, HierarchicalQCMainTableView hierarchicalQMainTableView, HierarchicalQCRecordInfo recordInfo, string employeeID)
        {
            bool update = false;
            if (existedMain == null)
            {
                return update;
            }
            if (existedMain.Result != hierarchicalQMainTableView.Point)
            {
                update = true;
                existedMain.Result = hierarchicalQMainTableView.Point;
            }
            if (existedMain.Improvement != hierarchicalQMainTableView.Improvement)
            {
                update = true;
                existedMain.Improvement = hierarchicalQMainTableView.Improvement ?? "";
            }
            if (existedMain.Guidance != hierarchicalQMainTableView.Guidance)
            {
                update = true;
                existedMain.Guidance = hierarchicalQMainTableView.Guidance;
            }
            if (existedMain.AssessDate != hierarchicalQMainTableView.ExamineDate)
            {
                update = true;
                existedMain.AssessDate = hierarchicalQMainTableView.ExamineDate;
            }

            if (update)
            {
                existedMain.Modify(employeeID);
            }
            return update;
        }

        /// <summary>
        /// 新增质控评估主记录
        /// </summary>
        /// <param name="hierarchicalQMainTableView">前端传过来的关于主记录的保存信息</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        private async Task<string> AddQCMain(HierarchicalQCMainTableView hierarchicalQMainTableView, string hospitalID, string employeeID, HierarchicalQCRecordInfo recordInfo)
        {
            var hierachicalQCMain = new HierarchicalQCMainInfo
            {
                HospitalID = hospitalID,
                HierarchicalQCFormLevel = recordInfo.HierarchicalQCFormLevel,
                HierarchicalQCRecordID = recordInfo.HierarchicalQCRecordID,
                HierarchicalQCFormID = recordInfo.HierarchicalQCFormID,
                DepartmentID = hierarchicalQMainTableView.DepartmentID == 0 ? recordInfo.DepartmentID : hierarchicalQMainTableView.DepartmentID,
                Guidance = hierarchicalQMainTableView.Guidance,
                // 因为后端不允许为空，所以设置默认值，如果字段非空属性改变，此处可一同改变
                Improvement = hierarchicalQMainTableView.Improvement ?? "",
                IsReadFlag = false,
                AuditStatus = "0",
                DeleteFlag = "",
                Result = hierarchicalQMainTableView.Point,
                VerifierEmployeeID = recordInfo.VerifierEmployeeID,
                AssessDate = hierarchicalQMainTableView.ExamineDate
            };
            hierachicalQCMain.HierarchicalQCMainID = hierachicalQCMain.GetId();
            hierachicalQCMain.Add(employeeID);
            hierachicalQCMain.Modify(employeeID);
            hierarchicalQMainTableView.HierarchicalQCMainID = hierachicalQCMain.HierarchicalQCMainID;

            await _unitOfWork.GetRepository<HierarchicalQCMainInfo>().InsertAsync(hierachicalQCMain);
            // 更新回写主记录分数
            UpdateRecord(recordInfo, hierarchicalQMainTableView.Point);

            return hierachicalQCMain.HierarchicalQCMainID;
        }

        /// <summary>
        /// 更新回写主记录分数
        /// </summary>
        /// <param name="recordInfo">考核主记录</param>
        /// GetTrackTableData
        /// <param name="lastPoint">最后一次评估的分数</param>
        private void UpdateRecord(HierarchicalQCRecordInfo recordInfo, decimal? lastPoint)
        {
            if (recordInfo != null)
            {
                recordInfo.LastResult = lastPoint;
            }
        }

        /// <summary>
        /// 获取追踪考核表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCMainTableView>> GetTrackTableView(GetQCRecordView searchView, string employeeID)
        {
            var view = new List<HierarchicalQCMainTableView>();
            var qcRecordList = await GetHierarchicalQCRecordList(searchView, employeeID);
            if (qcRecordList.Count == 0)
            {
                return view;
            }
            var qcRecords = qcRecordList.Select(m => m.HierarchicalQCRecordID).ToList();
            var qcMainList = await _hierarchicalQCMainRepository.GetLastDataByRecordIDs(qcRecords);
            if (qcMainList.Count == 0)
            {
                return view;
            }
            var qcMainIDs = qcMainList.Select(m => m.HierarchicalQCMainID).ToList();
            var qcDetailList = await GetTrackDetailsAsync(qcMainIDs, searchView.Score);
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(qcMainList.Select(m => m.AddEmployeeID).ToList());
            var deparmentList = await _departmentListRepository.GetByCacheAsync();
            foreach (var item in qcMainList)
            {
                var qcRecord = qcRecordList.Find(m => m.HierarchicalQCRecordID == item.HierarchicalQCRecordID);
                if (qcRecord == null)
                {
                    continue;
                }
                var viewItem = new HierarchicalQCMainTableView()
                {
                    HierarchicalQCSubjectID = qcRecord.HierarchicalQCSubjectID,
                    HierarchicalQCMainID = item.HierarchicalQCMainID,
                    HierarchicalQCRecordID = qcRecord.HierarchicalQCRecordID,
                    // TODO 确定考核时间和审核时间分别使用什么字段，这个返回前端的时间是否可空
                    ExamineDate = item.AssessDate ?? item.AddDateTime,
                    ExamineEmployee = employeeList.Find(m => m.EmployeeID == item.AddEmployeeID)?.EmployeeName,
                    ExamineEmployeeID = new List<string> { item.AddEmployeeID },
                    Department = deparmentList.Find(m => m.DepartmentID == item.DepartmentID)?.LocalShowName,
                    DepartmentID = item.DepartmentID,
                    Point = item.Result,
                    Reader = item.IsReadFlag.HasValue && item.IsReadFlag.Value ? "是" : "否",
                    SubmitStatus = GetStatus(item.AuditStatus),
                    Guidance = item.Guidance,
                    IsReadFlag = item.IsReadFlag ?? false,
                    Improvement = item.Improvement,
                    SubjectName = qcRecord?.FormName,
                    TrackDetails = qcDetailList.Where(m => m.HierarchicalQCMainID == item.HierarchicalQCMainID).OrderBy(m => m.Score).ToList(),
                    TemplateCode = qcRecord?.TemplateCode ?? "",
                    ScoreThreshold = qcRecord?.ScoreThreshold,
                    StartDate = qcRecord?.StartDate,
                    MinPassingScore = qcRecord?.MinPassingScore,
                    EndDate = qcRecord?.EndDate,
                };
                view.Add(viewItem);
            }
            return view;
        }

        /// <summary>
        /// 获取质控数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="loginEmployeeID"></param>
        /// <returns></returns>
        private async Task<Tuple<List<HierarchicalQCMainInfo>, List<HierarchicalQCSubjectInfo>, List<HierarchicalQCRecordInfo>>> GetQCMainList(QCSubjectSearchView searchView, string loginEmployeeID)
        {
            var qcSubjectList = new List<HierarchicalQCSubjectInfo>();
            if (string.IsNullOrEmpty(searchView.HierarchicalQCSubjectID))
            {
                qcSubjectList = await _hierarchicalQCSubjectRepository.GetQCSubjectOptions(searchView);
            }
            else
            {
                qcSubjectList.Add(await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(searchView.HierarchicalQCSubjectID));
            }
            if (qcSubjectList.Count == 0)
            {
                return null;
            }
            var qcSubjectIDs = qcSubjectList.Where(m => m.FormType != "6").Select(m => m.HierarchicalQCSubjectID).ToList();
            List<int> switchDeptIDs = null;
            if (searchView.QcDepartmentID.HasValue)
            {
                switchDeptIDs = [searchView.QcDepartmentID.Value];
            }
            else
            {
                switchDeptIDs = await _employeeDepartmentSwitchRepository.GetSwitchDepartmentIDsAsync(loginEmployeeID);
            }
            var qcRecordList = await _hierarchicalQCRecordRepository.GetRecordData(qcSubjectIDs, searchView.EmployeeID, switchDeptIDs);
            if (qcRecordList.Count == 0)
            {
                return null;
            }
            var onlyGetSelfRecordSettingParams = new SettingDictionaryParams()
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "SystemSwitch",
                SettingTypeValue = "OnlyGetSelfRecord"
            };
            var onlyGetSelfRecordFlag = await _settingDictionaryRepository.GetSettingSwitch(onlyGetSelfRecordSettingParams);
            if (onlyGetSelfRecordFlag && searchView.HierarchicalQCFormLevel == "3")
            {
                qcRecordList = qcRecordList.Where(m => m.VerifierEmployeeID == searchView.EmployeeID).ToList();
            }
            var qcRecords = qcRecordList.Select(m => m.HierarchicalQCRecordID).ToList();
            return new Tuple<List<HierarchicalQCMainInfo>, List<HierarchicalQCSubjectInfo>, List<HierarchicalQCRecordInfo>>(await _hierarchicalQCMainRepository.GetLastDataByRecordIDs(qcRecords), qcSubjectList, qcRecordList);
        }

        /// <summary>
        /// 根据条件获取主题下拉框数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCSubjectInfo>> GetSubjectOptions(QCSubjectSearchView searchView)
        {
            var options = new List<HierarchicalQCSubjectInfo>();
            if (searchView == null || string.IsNullOrEmpty(searchView.YearMonth) || string.IsNullOrEmpty(searchView.HierarchicalQCFormLevel))
            {
                _logger.Error("获取数据参数有误");
                return options;
            }
            var qcSubjectList = await _hierarchicalQCSubjectRepository.GetQCSubjectOptions(searchView);
            if (qcSubjectList.Count == 0)
            {
                _logger.Warn("获取数据为空");
                return options;
            }
            return qcSubjectList;
        }

        /// <summary>
        /// 获取人员在质控表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        public async Task<List<NormalWorkingProcessControlTableView>> GetNormalWorkingTableData(GetQCSubjectView searchView)
        {
            var view = new List<NormalWorkingProcessControlTableView>();
            var qcSubjectList = await _hierarchicalQCSubjectRepository.GetSubject(searchView);
            if (qcSubjectList.Count == 0)
            {
                return view;
            }
            var qcSubjectIDs = qcSubjectList.Select(m => m.HierarchicalQCSubjectID).ToList();
            var qcRecordList = await _hierarchicalQCRecordRepository.GetQCReocrdBySubjectIDs(qcSubjectIDs);
            if (qcRecordList.Count == 0)
            {
                return view;
            }
            var qcRecords = qcRecordList.Select(m => m.HierarchicalQCRecordID).ToList();
            var qcObjectList = await _hierarchicalQCObjectRepository.GetHierarchicalQCObjectViewByRecordID(qcRecords, 1);
            var qcMainList = await _hierarchicalQCMainRepository.GetLastDataByRecordIDs(qcRecords);
            if (qcMainList.Count == 0)
            {
                return view;
            }
            var qcMainIDs = qcMainList.Select(m => m.HierarchicalQCMainID).ToList();
            var problemRectificationList = await _problemRectificationRepository.GetProblemRectificationByHierarchicalQCMainID(qcMainIDs);
            var employeeList = await _employeePersonalDataRepository.GetIDAndNameData();
            // 添加特殊人员
            var param = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "HierarchicalQCSpecialListID",
                SettingTypeValue = "973"
            };
            var specialEmployeeList = await _settingDictionaryRepository.GetSettingDictionary(param);
            employeeList.AddRange(specialEmployeeList.Select(m => new EmployeePersonalDataListView() { EmployeeID = m.SettingValue, EmployeeName = m.Description }));
            return await CreateNormalWorkingProcessControlTableView(qcSubjectList, qcRecordList, qcObjectList, qcMainList, problemRectificationList, employeeList);
        }

        /// <summary>
        /// 创建常态工作控制返回视图
        /// </summary>
        /// <param name="qcSubjectList"></param>
        /// <param name="qcRecordList"></param>
        /// <param name="qcObjectList"></param>
        /// <param name="qcMainList"></param>
        /// <param name="problemRectificationList"></param>
        /// <param name="employeeList"></param>
        /// <returns></returns>
        private async Task<List<NormalWorkingProcessControlTableView>> CreateNormalWorkingProcessControlTableView(List<HierarchicalQCSubjectInfo> qcSubjectList
            , List<HierarchicalQCRecordInfo> qcRecordList
            , List<HierarchicalQCObjectInfo> qcObjectList, List<HierarchicalQCMainInfo> qcMainList
            , List<ProblemRectificationView> problemRectificationList, List<EmployeePersonalDataListView> employeeList)
        {
            var view = new List<NormalWorkingProcessControlTableView>();
            var qcAssessList = await _hierarchicalQCAssessListRepository.GetAll<HierarchicalQCAssessListInfo>();
            var formDetails = await _dynamicFormDetailRepository.GetFormDetailListByRecordIDList(qcSubjectList.Select(m => m.TemplateCode).Distinct().ToList());
            var qcDetails = await _hierarchicalQCDetailRepository.GetQCDetailInfosByQCMainIDs(qcMainList.Select(m => m.HierarchicalQCMainID).ToList());
            var approveRecords = await _approveRecordRepository.GetRecordsBySourceIDs(qcMainList.Select(m => m.HierarchicalQCMainID).Distinct().ToList());
            foreach (var item in qcMainList)
            {
                var qcRecord = qcRecordList.Find(m => m.HierarchicalQCRecordID == item.HierarchicalQCRecordID);
                if (qcRecord == null)
                {
                    continue;
                }
                var viewItem = SetNormalWorkProcessTableViewItem(qcSubjectList, qcObjectList, employeeList, item, qcRecord, problemRectificationList);
                var parentDetails = formDetails.Where(m => m.DynamicFormRecordID == viewItem.TemplateCode && m.ParentID == null).OrderBy(m => m.Sort).ToList();
                var formDetailAttributes = await _dynamicFormDetailAttributeRepository.GetDetailAttributeListByRecordID(viewItem.TemplateCode);
                var allDetails = qcDetails.Where(m => m.HierarchicalQCMainID == viewItem.HierarchicalQCMainID).ToList();
                var result = new List<string>();
                //处理数据
                foreach (var parentDetail in parentDetails)
                {
                    result.AddRange(ProcessDetail(parentDetail, qcAssessList, allDetails, formDetails, formDetailAttributes));
                }
                viewItem.Contents = string.Join("<br>", result);
                viewItem.ApproveFlag = approveRecords.Any(m => m.SourceID == item.HierarchicalQCMainID);
                view.Add(viewItem);
            }
            return [.. view.OrderByDescending(m => m.ExamineDate)];
        }

        /// <summary>
        /// 设置前端呈现的View对象
        /// </summary>
        /// <param name="qcSubjectList">质控主题集合</param>
        /// <param name="qcObjectList">内容明细字典表</param>
        /// <param name="employeeList">人员信息集合</param>
        /// <param name="item">数据库中维护记录信息</param>
        /// <param name="qcRecord">质控主记录ID</param>
        /// <param name="problemRectificationList">问题整改记录</param>
        /// <returns></returns>
        private NormalWorkingProcessControlTableView SetNormalWorkProcessTableViewItem(List<HierarchicalQCSubjectInfo> qcSubjectList, List<HierarchicalQCObjectInfo> qcObjectList, List<EmployeePersonalDataListView> employeeList, HierarchicalQCMainInfo item, HierarchicalQCRecordInfo qcRecord, List<ProblemRectificationView> problemRectificationList)
        {
            var problemRectificationView = problemRectificationList.FirstOrDefault(m => m.HierarchicalQCMainID == item.HierarchicalQCMainID);
            if (problemRectificationView != null)
            {
                problemRectificationView.EmployeeName = employeeList.FirstOrDefault(m => m.EmployeeID == problemRectificationView.EmployeeID)?.EmployeeName ?? "";
            }
            var viewItem = new NormalWorkingProcessControlTableView()
            {
                HierarchicalQCSubjectID = qcRecord.HierarchicalQCSubjectID,
                HierarchicalQCMainID = item.HierarchicalQCMainID,
                HierarchicalQCRecordID = qcRecord.HierarchicalQCRecordID,
                // TODO 确定考核时间和审核时间分别使用什么字段，这个返回前端的时间是否可空
                ExamineDate = item.AssessDate ?? item.ModifyDateTime,
                ExamineEmployeeID = item.ModifyEmployeeID,
                ExamineEmployee = employeeList.Find(m => m.EmployeeID == item.ModifyEmployeeID)?.EmployeeName,
                QcEmployeeID = qcObjectList.Where(m => m.HierarchicalQCRecordID == qcRecord.HierarchicalQCRecordID).Select(m => m.ObjectValue).ToList(),
                Point = item.Result,
                Reader = item.IsReadFlag.HasValue && item.IsReadFlag.Value ? "是" : "否",
                SubmitStatus = GetAppealStatus(item.AuditStatus),
                AuditStatus = item.AuditStatus,
                Guidance = item.Guidance,
                IsReadFlag = item.IsReadFlag ?? false,
                Improvement = item.Improvement,
                SubjectName = qcSubjectList.Find(m => m.HierarchicalQCSubjectID == qcRecord.HierarchicalQCSubjectID)?.FormName,
                TemplateCode = qcSubjectList.Find(m => m.HierarchicalQCSubjectID == qcRecord.HierarchicalQCSubjectID)?.TemplateCode ?? "",
                DepartmentID = item.DepartmentID,
                ProblemRectificationView = problemRectificationView
            };
            var qcEmployeeName = employeeList.Where(m => viewItem.QcEmployeeID.Contains(m.EmployeeID)).Select(m => m.EmployeeName).ToList();
            if (qcEmployeeName.Count > 0)
            {
                viewItem.QcEmployeeName = string.Join("、", qcEmployeeName);
            }
            return viewItem;
        }

        /// <summary>
        /// 停止审批
        /// </summary>
        /// <param name="hierarchicalMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> StopHierarchicalQCApprovalAsync(string hierarchicalMainID, string employeeID)
        {
            var apparoveRecordService = _serviceProvider.GetService<IApproveRecordService>();

            if (string.IsNullOrEmpty(hierarchicalMainID))
            {
                _logger.Error("质控考核维护记录ID为空");
                return false;
            }
            var qcMainInfo = await _hierarchicalQCMainRepository.GetDataByMainID(hierarchicalMainID);

            if (qcMainInfo == null)
            {
                _logger.Error($"获取质控维护记录失败，主键ID ={hierarchicalMainID}");
                return false;
            }

            await apparoveRecordService.StopApprovalAsync(hierarchicalMainID, employeeID);

            qcMainInfo.AuditStatus = "0";
            qcMainInfo.Reason = null;
            qcMainInfo.AuditDateTime = DateTime.Now;
            qcMainInfo.Modify(employeeID);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 提交审批
        /// </summary>
        /// <param name="view">审批主记录与明细记录视图</param>
        /// <returns></returns>
        public async Task<SaveReponseView> SubmitForApprovalAsync(ApproveMainAndDetailParamView view)
        {
            var result = new SaveReponseView();
            if (view == null || string.IsNullOrEmpty(view.SourceID))
            {
                _logger.Error("记录ID为空");
                return result;
            }
            var qcMainInfo = await _hierarchicalQCMainRepository.GetDataByMainID(view.SourceID);

            if (qcMainInfo == null)
            {
                _logger.Error($"获取维护记录失败，参数为 ={ListToJson.ToJson(view)}");
                return result;
            }
            //提交申诉的时候，添加申诉原因
            if (!string.IsNullOrEmpty(view.Reason))
            {
                qcMainInfo.Reason = view.Reason;
            }
            result.RecordSaveFlag = true;
            result.ApproveSaveFlag = await CreateApproveMainAndDetail(view, qcMainInfo);
            await _unitOfWork.SaveChangesAsync();
            return result;
        }

        /// <summary>
        /// 创建审批主记录和明细
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        private async Task<bool> CreateApproveMainAndDetail(ApproveMainAndDetailParamView view, HierarchicalQCMainInfo mainInfo)
        {
            var approveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            var approveProcessService = _serviceProvider.GetService<IApproveProcessService>();
            var (approveProcessID, _) = await approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(view.ProveCategory, view.DepartmentID);
            bool approveFlag = await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID);
            if (!approveFlag)
            {
                var approveRecordInfo = await _approveRecordRepository.GetApproveRecordBySourceIDAsync(mainInfo.HierarchicalQCMainID);
                approveRecordInfo?.Delete(view.AddEmployeeID);
                return false;
            };
            mainInfo.AuditStatus = "1";
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        public async Task<List<ComponentOptionView>> GetQCAssessList()
        {
            var list = await _hierarchicalQCAssessListRepository.GetAll<HierarchicalQCAssessListInfo>();
            return list.Select(m => new ComponentOptionView()
            {
                Value = m.HierarchicalQCAssessListID.ToString(),
                Label = m.ContentName,
                ItemSourceType = "HierarchicalQCAssessList"
            }).ToList();
        }

        public async Task<bool> SaveQCForm(HierachicalQCFormView hierachicalQCFormView, string employeeID, string hospitalID, int language)
        {
            if (hierachicalQCFormView == null)
            {
                _logger.Error("SaveQCForm方法hierachicalQCFormView参数为空");
                return false;
            }
            _logger.Info($"SaveQCForm方法hierachicalQCFormView参数值{ListToJson.ToJson(hierachicalQCFormView)}");
            if (hierachicalQCFormView.FormTemplateView == null)
            {
                _logger.Error("SaveQCForm方法参数hierachicalQCFormView缺少FormTemplateView参数");
                return false;
            }
            // 先保存表单模板，然后将表单模板ID写入Form表中
            var dynamicFormID = await _dynamicFormService.SaveFormTemplate(hierachicalQCFormView.FormTemplateView, employeeID);
            if (string.IsNullOrWhiteSpace(dynamicFormID))
            {
                _logger.Error("SaveQCForm方法中调用_dynamicFormService.SaveFormTemplate保存表单失败");
                return false;
            }
            HierarchicalQCFormInfo qcForm;
            if (hierachicalQCFormView.HierarchicalQCFormID != null)
            {
                qcForm = await _hierarchicalQCFormRepository.GetFormByID(hierachicalQCFormView.HierarchicalQCFormID.Value);
                _unitOfWork.GetRepository<HierarchicalQCFormInfo>().Update(qcForm);
            }
            else
            {
                var id = await _hierarchicalQCFormRepository.GetNewID();
                qcForm = new HierarchicalQCFormInfo()
                {
                    HierarchicalQCFormID = id,
                    HospitalID = hospitalID,
                    Language = language,
                    AddEmployeeID = employeeID,
                    AddDateTime = DateTime.Now,
                    DeleteFlag = ""
                };
                await _unitOfWork.GetRepository<HierarchicalQCFormInfo>().InsertAsync(qcForm);
            }
            qcForm.HierarchicalQCFormLevel = hierachicalQCFormView.HierarchicalQCFormLevel;
            qcForm.TemplateCode = dynamicFormID;
            qcForm.FormType = hierachicalQCFormView.FormType;
            qcForm.FormName = hierachicalQCFormView.FormName;
            qcForm.AddDepartmentID = hierachicalQCFormView.AddDepartmentID;
            qcForm.StatusCode = "1";
            qcForm.ModifyEmployeeID = employeeID;
            qcForm.ModifyDateTime = DateTime.Now;
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            if (result)
            {
                await _hierarchicalQCFormRepository.UpdateCache();
            }
            return result;
        }

        public async Task<bool> DeleteQCForm(int hierarchicalQCFormID, string employeeID)
        {
            var qcForm = await _hierarchicalQCFormRepository.GetFormByID(hierarchicalQCFormID);
            if (qcForm == null)
            {
                _logger.Error("DeleteQCForm方法中根据hierarchicalQCFormID查不到数据, hierarchicalQCFormID=" + hierarchicalQCFormID);
                return false;
            }
            qcForm.Delete(employeeID);
            _unitOfWork.GetRepository<HierarchicalQCFormInfo>().Update(qcForm);
            await _dynamicFormService.DeleteFormTemplate(qcForm.TemplateCode, employeeID);
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            if (result)
            {
                await _hierarchicalQCFormRepository.UpdateCache();
                await _dynamicFormService.UpdateFormCache();
            }
            return result;
        }

        public async Task<bool> SaveQCSubjectForm(SubjectFormView subjectFormView, string employeeID)
        {
            if (string.IsNullOrWhiteSpace(subjectFormView.HierarchicalQCSubjectID))
            {
                _logger.Error("SaveQCSubjectForm方法缺少HierarchicalQCSubjectID参数");
                return false;
            }
            if (subjectFormView.FormTemplateView == null)
            {
                _logger.Error("SaveQCSubjectForm方法缺少FormTemplateView参数");
                return false;
            }
            var subject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(subjectFormView.HierarchicalQCSubjectID);
            if (subject == null)
            {
                _logger.Error("SaveQCSubjectForm方法中根据HierarchicalQCSubjectID查不到数据, HierarchicalQCSubjectID=" + subjectFormView.HierarchicalQCSubjectID);
                return false;
            }
            // 主题模板的每次修改都是新增
            subjectFormView.FormTemplateView.Props.FormID = null;
            // 先保存表单模板，然后将表单模板ID写入HierarchicalQCSubject表中
            var dynamicFormID = await _dynamicFormService.SaveFormTemplate(subjectFormView.FormTemplateView, employeeID);
            if (string.IsNullOrWhiteSpace(dynamicFormID))
            {
                _logger.Error("SaveQCForm方法中调用_dynamicFormService.SaveFormTemplate保存表单失败");
                return false;
            }
            // 判断旧模板是否为母版，若非母版则删除
            var deleteFormTemplateFlag = false;
            var qcForm = await _hierarchicalQCFormRepository.GetFormByID(subject.HierarchicalQCFormID);
            if (qcForm != null && qcForm.TemplateCode != subject.TemplateCode)
            {
                await _dynamicFormService.DeleteFormTemplate(subject.TemplateCode, employeeID);
                deleteFormTemplateFlag = true;
            }
            subject.TemplateCode = dynamicFormID;
            subject.Modify(employeeID);
            _unitOfWork.GetRepository<HierarchicalQCSubjectInfo>().Update(subject);
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            if (result && deleteFormTemplateFlag)
            {
                await _dynamicFormService.UpdateFormCache();
            }
            return result;
        }

        /// <summary>
        /// 获取质控评估模板
        /// </summary>
        /// <param name="careMainID">质控维护记录ID</param>
        /// <param name="templateCode">模板ID</param>
        /// <param name="trackFlag">追踪考核标记</param>
        /// <param name="computeGroupScore">是否计算分组明细分数</param>
        /// <returns></returns>
        public async Task<FormTemplateView> GetAssessContentView(string careMainID, string templateCode, bool? trackFlag, bool computeGroupScore = true)
        {
            var view = new FormTemplateView();
            if (!string.IsNullOrEmpty(careMainID) && string.IsNullOrEmpty(templateCode))
            {
                templateCode = await _hierarchicalQCMainRepository.GetTemplateCodeByMainIDAsync(careMainID);
            }
            if (string.IsNullOrEmpty(templateCode))
            {
                return view;
            }
            var details = string.IsNullOrEmpty(careMainID) ? new List<HierarchicalQCDetailView>() : (await _hierarchicalQCDetailRepository.GetQCDetailByQCMainID(careMainID));
            var datas = new List<FormValueView>();
            var itemDisabled = new Dictionary<string, string>();
            var formDetailList = await _dynamicFormDetailRepository.GetDynamicFormDetailViewByFormRecordID(templateCode);
            var componentList = await _componentListRepository.GetComponentListByType("DynamicForm");
            List<(int, int?, IEnumerable<string>)> fileStore = [];
            foreach (var item in details)
            {
                try
                {
                    var valueDict = ListToJson.ToList<Dictionary<string, List<string>>>(item.Value);
                    if (valueDict.TryGetValue("fileIDs", out var valueJson) && valueJson is IEnumerable<string> jsonList)
                    {
                        fileStore.Add((item.ItemID, item.GroupID, jsonList));
                        continue;
                    }
                }
                catch (Exception ex) { }
                var data = CreateFormValueView(item, formDetailList, componentList);
                //分数组件特殊处理
                datas.Add(data);
                if (!trackFlag.HasValue || !trackFlag.Value)
                {
                    continue;
                }
                //追踪考核 满分无法进行修改
                itemDisabled[item.ItemID.ToString()] = item.Value + "={maxScore}";
            }
            //获取文件相关明细
            datas = await AssemblyFileDatas(details, datas, fileStore);
            view = await _dynamicFormService.GetFormTemplateByRecordID(templateCode, datas, itemDisabled, "HierarchicalQCAssessList", computeGroupScore);
            return view;
        }

        /// <summary>
        /// 根据组件类型创建view
        /// </summary>
        /// <param name="qcDetailView">质控明细</param>
        /// <param name="formDetailList">明细字典数据</param>
        /// <param name="componentList">质控组件集合</param>
        /// <returns></returns>
        private FormValueView CreateFormValueView(HierarchicalQCDetailView qcDetailView, List<DynamicFormView> formDetailList, List<ComponentListInfo> componentList)
        {
            var isGradeFlag = false;
            var temp = formDetailList.FirstOrDefault(m => m.ItemID == qcDetailView.ItemID.ToString())?.ComponentListID;
            if (temp.HasValue)
            {
                isGradeFlag = componentList.Exists(m => m.ComponentListID == temp.Value && m.ControlerType == "grade");
            }
            return new FormValueView()
            {
                ID = qcDetailView.ItemID.ToString(),
                GroupID = qcDetailView.GroupID.ToString(),
                ParentID = qcDetailView.ParentID.ToString(),
                Value = !isGradeFlag ? ListToJson.ToList<object>(qcDetailView.Value ?? "") : new Dictionary<string, object>()
                                                                                            {
                                                                                                { "problem", qcDetailView.Problem },
                                                                                                { "brightSpot", qcDetailView.BrightSpot },
                                                                                                { "score", qcDetailView.Value },
                                                                                                {"remark",qcDetailView.Remark },
                                                                                            }
            };
        }

        /// <summary>
        /// 获取文件相关明细
        /// </summary>
        /// <param name="details"></param>
        /// <param name="datas"></param>
        /// <param name="fileStore"></param>
        /// <returns></returns>
        private async Task<List<FormValueView>> AssemblyFileDatas(List<HierarchicalQCDetailView> details, List<FormValueView> datas, List<(int, int?, IEnumerable<string>)> fileStore)
        {
            if (fileStore.Count <= 0)
            {
                return datas;
            }
            var commonFileIDs = fileStore.SelectMany(m => m.Item3).ToList();
            var commonFileInfos = await _commonFileRepository.GetPartFileInfosByIDsAsync(commonFileIDs);
            var remoteFileIDs = commonFileInfos.Select(m => m.SourceID).ToList();
            var documentMainViews = await _fileService.GetFileAccessInfosFromFileSystemAsync(remoteFileIDs);
            if (documentMainViews == null || documentMainViews.Count < 0)
            {
                return datas;
            }
            var commonFiles = new List<CommonFileInfo>();
            foreach ((int, int, IEnumerable<string>) item in fileStore)
            {
                var detailItem = details.Find(m => m.ItemID == item.Item1 && m.GroupID == item.Item2);
                if (detailItem == null)
                {
                    continue;
                }
                commonFiles = commonFileInfos.Where(m => item.Item3.Contains(m.CommonFileID)).ToList();
                foreach (var comonFile in commonFiles)
                {
                    var documentView = documentMainViews.Where(n => n.DocumentMainID == comonFile.SourceID).FirstOrDefault();
                    datas.Add(new FormValueView
                    {
                        ID = detailItem.ItemID.ToString(),
                        GroupID = detailItem.GroupID.ToString(),
                        Value = new List<Dictionary<string, object>> {
                            new ()
                            {
                                { "url", documentView?.DocumentUrl ?? "" },
                                { "name",documentView?.DocumentTitle ?? "" },
                                { "fileID",comonFile.CommonFileID },
                                { "extname",documentView?.DocumentType }
                            }
                        }
                    });
                }
            }
            return datas;
        }

        /// <summary>
        /// 处理分组明细
        /// </summary>
        /// <param name="parentDetails">分组明细</param>
        /// <param name="settings">内容明细字典</param>
        /// <param name="posts">岗位字典</param>
        /// <returns></returns>
        private static List<string> ProcessGroupDetails(List<IGrouping<int?, HierarchicalQCDetailInfo>> parentDetails, List<HierarchicalQCAssessListInfo> settings, List<PostInfo> posts)
        {
            var strings = new List<string>();
            foreach (var parentDetail in parentDetails)
            {
                //if (groupDetail.Key == 972)//972为岗位
                //{
                //    strings.Add(ProcessPostGroup(groupDetail, posts));
                //}
                //else
                //{
                strings.Add(ProcessRegularGroup(parentDetail, settings));
                //}
            }
            return strings;
        }

        /// <summary>
        /// 处理岗位分组
        /// </summary>
        /// <param name="parentDetail">分组明细</param>
        /// <param name="posts">岗位字典</param>
        /// <returns></returns>
        private static string ProcessPostGroup(IGrouping<int?, HierarchicalQCDetailInfo> parentDetail, List<PostInfo> posts)
        {
            var postNames = new List<string>();
            foreach (var item in parentDetail)
            {
                if (int.TryParse(item.Result.Replace("[", "").Replace("]", "").Replace("\"", "").Trim(), out int postID))
                {
                    var postName = posts.Find(m => m.PostID == postID)?.PostName;
                    if (postName != null)
                    {
                        postNames.Add(postName);
                    }
                }
            }
            return "岗位：" + string.Join("、", postNames);
        }

        /// <summary>
        /// 处理常规分组
        /// </summary>
        /// <param name="parentDetail">分组明细</param>
        /// <param name="settings">内容明细字典</param>
        /// <returns></returns>
        private static string ProcessRegularGroup(IGrouping<int?, HierarchicalQCDetailInfo> parentDetail, List<HierarchicalQCAssessListInfo> settings)
        {
            var values = new List<string>();
            foreach (var item in parentDetail)
            {
                var setting = settings.Find(m => m.HierarchicalQCAssessListID == item.HierarchicalQCAssessListID);
                if (setting != null)
                {
                    values.Add(!string.IsNullOrEmpty(item.Result)
                                ? $"{setting.ContentName}({item.Result.Replace("\"", "").Trim()})"
                                : setting.ContentName);
                }
            }
            var title = settings.Find(m => m.HierarchicalQCAssessListID == parentDetail.Key)?.ContentName;
            if (string.IsNullOrEmpty(title) && values.Count == 0)
            {
                return string.Empty;
            }
            return $"{title}：{string.Join("、", values)}";
        }

        /// <summary>
        /// 节点式督导获取质控人员
        /// </summary>
        /// <param name="qcLevel"></param>
        /// <param name="yearMonth"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetQCEmployeeOptions(string qcLevel, string yearMonth, string hospitalID, string employeeID)
        {
            var qcEmployeeOptions = new List<SelectOptionsView>();
            if (string.IsNullOrEmpty(qcLevel) || string.IsNullOrEmpty(yearMonth))
            {
                _logger.Error("获取质控人 入参 有误！ ");
                return qcEmployeeOptions;
            }
            var (year, month) = TimeHelper.GetYearAndMonth(yearMonth);
            if (!year.HasValue || !month.HasValue)
            {
                _logger.Error("获取质控人 入参 yearMonth转换失败！|| " + yearMonth);
                return qcEmployeeOptions;
            }
            // 获取拥有权限的部门ID
            var switchDeptIDs = await _employeeDepartmentSwitchRepository.GetSwitchDepartmentIDsAsync(employeeID);
            //节点式督导formType
            var qcFormTypes = new List<string>() { "1", "2", "3", "4", "5" };
            var qcEmployeeIDs = await _hierarchicalQCRecordRepository.GetQCReocrdHierarchicalQCEmployIDList(qcLevel, year.Value, month.Value, qcFormTypes, hospitalID, switchDeptIDs);
            if (qcEmployeeIDs.Count == 0)
            {
                return qcEmployeeOptions;
            }
            List<string> newQcEmployeeIDs = qcEmployeeIDs.SelectMany(s => s.Split(new[] { "||" }, StringSplitOptions.None)).ToList();
            return await _employeePersonalDataRepository.GetEmployOptionViewByEmployeeIDs(newQcEmployeeIDs);
        }

        /// <summary>
        /// 获取明细数据
        /// </summary>
        /// <param name="qcMainIDs"></param>
        /// <param name="score">分数，可空,只获取小于制定分数的项，或者所有项</param>
        /// <returns></returns>
        private async Task<List<TrackDetail>> GetTrackDetailsAsync(List<string> qcMainIDs, int? score)
        {
            var result = new List<TrackDetail>();
            var qCDetais = await _hierarchicalQCDetailRepository.GetQCDetailByQCMainIDs(qcMainIDs);
            var settings = await _hierarchicalQCAssessListRepository.GetCacheAsync() as List<HierarchicalQCAssessListInfo>;
            var assessListSettingsDictionary = settings.ToDictionary(s => s.HierarchicalQCAssessListID);
            foreach (var item in qCDetais)
            {
                if (!int.TryParse(item.Result.Trim(), out int itemValue))
                {
                    continue;
                }
                if (itemValue == item.FullMark)
                {
                    continue;
                }
                if (GetShoudShowSwitch(score, itemValue, item.FullMark))
                {
                    var name = assessListSettingsDictionary.TryGetValue(item.HierarchicalQCAssessListID, out var setting)
                                ? setting.ContentName
                                : null;

                    result.Add(new TrackDetail
                    {
                        HierarchicalQCMainID = item.HierarchicalQCMainID,
                        HierarchyID = item.HierarchicalQCAssessListID,
                        Name = name,
                        Score = itemValue,
                        FullMark = item.FullMark
                    });
                }
            }

            return result;
        }

        /// <summary>
        /// 获取是否添加开关
        /// </summary>
        /// <param name="score"></param>
        /// <param name="itemValue"></param>
        /// <param name="fullMark"></param>
        /// <returns></returns>
        private static bool GetShoudShowSwitch(int? score, int itemValue, int? fullMark)
        {
            bool shouldAdd;
            if (!score.HasValue)
            {
                shouldAdd = itemValue != fullMark;
            }
            else
            {
                shouldAdd = score.Value switch
                {
                    0 => itemValue != fullMark,
                    -1 => itemValue == -1,
                    _ => itemValue != fullMark && itemValue < score.Value
                };
            }
            return shouldAdd;
        }
        public async Task<bool> SaveProblemRectificationData(ProblemRectificationView saveView, Session session)
        {
            if (saveView == null)
            {
                _logger.Error("保存问题整改记录失败，前端传递参数为空");
                return false;
            }
            var hierarchicalQCMainData = await _hierarchicalQCMainRepository.GetDataByMainID(saveView.HierarchicalQCMainID);
            if (hierarchicalQCMainData == null)
            {
                _logger.Error($"保存问题整改记录失败，未找到对应质控记录，HierarchicalQCMainID：{saveView.HierarchicalQCMainID}");
                return false;
            }
            hierarchicalQCMainData.AuditStatus = AUDIT_STATUS_5;
            var formData = await _hierarchicalQCFormRepository.GetFormByID(hierarchicalQCMainData.HierarchicalQCFormID);
            if (formData == null)
            {
                _logger.Error($"保存问题整改记录失败，未找到对应质控模板，HierarchicalQCFormID：{hierarchicalQCMainData.HierarchicalQCFormID}");
                return false;
            }
            var problemRectificationData = await _problemRectificationRepository.GetRecordByHierarchicalQCMainIDAndEmployeeID(saveView.HierarchicalQCMainID, saveView.EmployeeID);
            if (problemRectificationData == null)
            {
                //新增
                var problemRectificationInfo = new ProblemRectificationInfo()
                {
                    EmployeeID = saveView.EmployeeID,
                    RectificationDateTime = saveView.RectificationDateTime,
                    RectificationRemarks = saveView.RectificationRemarks,
                    HierarchicalQCMainID = saveView.HierarchicalQCMainID,
                    FormType = formData.FormType,
                    HierarchicalQCFormLevel = hierarchicalQCMainData.HierarchicalQCFormLevel,
                    SourceType = "HierarchicalQC",
                    SourceID = saveView.HierarchicalQCMainID
                };
                problemRectificationInfo.ProblemRectificationID = problemRectificationInfo.GetId();
                problemRectificationInfo.Add(session.EmployeeID).Modify(session.EmployeeID);
                await _unitOfWork.GetRepository<ProblemRectificationInfo>().InsertAsync(problemRectificationInfo);
            }
            else
            {
                //修改
                problemRectificationData.RectificationDateTime = saveView.RectificationDateTime;
                problemRectificationData.RectificationRemarks = saveView.RectificationRemarks;
                problemRectificationData.Modify(session.EmployeeID);
            }
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            if (result)
            {
                // 问题整改后 通知护士长
                var employeeName = await _employeePersonalDataRepository.GetEmployeeNameByID(saveView.EmployeeID);
                var HeadNurseEmployeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(HEAD_NURSE_JOB_CODE, session.DepartmentID);
                await SendNotice(HeadNurseEmployeeIDs, $"{employeeName}的常态工作过程控制问题已整改！", NORMAL_WORKING_CONTRPL_MOBILE_PATH);
            }
            return result;
        }
        public async Task<bool> ConfirmRectification(string hierarchicalQCMainID, string employeeID)
        {
            var hierarchicalQCMainData = await _hierarchicalQCMainRepository.GetDataByMainID(hierarchicalQCMainID);
            if (hierarchicalQCMainData == null)
            {
                _logger.Error($"确认问题整改失败，未找到对应质控记录，HierarchicalQCMainID：{hierarchicalQCMainID}");
                return false;
            }
            hierarchicalQCMainData.AuditStatus = AUDIT_STATUS_6;
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        public async Task<bool> UploadHierarchicalReportAsync(IFormFile formFile, HierarchicalQCFileView qCFileView)
        {
            if (qCFileView == null || string.IsNullOrEmpty(qCFileView.SourceID))
            {
                _logger.Error("获取质控主题失败，主键ID=" + qCFileView.SourceID);
                return false;
            }
            var subject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(qCFileView.SourceID);
            var document = new DocumentView();
            var documentMain = new DocumentMainView()
            {
                SourceSystem = "NursingManagement",
                DocumentTypeID = 1,
                UserID = qCFileView.UserID,
                UserName = await _employeePersonalDataRepository.GetEmployeeNameByID(qCFileView.UserID),
                DocumentTitle = formFile.FileName,
                DocumentAbstract = "",
                DocumentStatus = 20,
                DocumentType = formFile.FileName[(formFile.FileName.LastIndexOf(".") + 1)..],
            };
            document.DocumentMainView = documentMain;
            var documentDetails = new List<DocumentDetailView>()
            {
                new (){ GroupID = 1, ItemID = 1, Value = "质控管理" },
            };
            document.DocumentDetailViews = documentDetails;
            //标签数据
            var settingDictionaryParams = new SettingDictionaryParams
            {
                DataType = "1",
                SettingType = "HierarchicalQC"
            };
            var tagMappingList = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            // 获取质控明细中对应的标签ID、质控等级对应的标签ID
            var parentIds = await _hierarchicalQCDetailRepository.GetParentIdBySubjectId(qCFileView.SourceID);
            var documentTags = GetTagListAsync(parentIds.Select(m => m.ToString()).ToList(), subject.HierarchicalQCFormLevel, null, tagMappingList);
            document.DocumentTagViews = documentTags;
            // 调用文档管理系统的接口保存文件
            var view = await _fileService.UpLoadFile(formFile, document);
            if (view != null && !string.IsNullOrEmpty(view.FileID))
            {
                subject.ReportFileID = view.FileID;
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            _logger.Error("上传文件失败");
            return false;
        }
        public async Task<bool> DeleteQCFileAsync(string subjectID, string userID)
        {
            var subject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(subjectID);
            var commonFile = await _commonFileRepository.GetFileByFileIDAsync(subject.ReportFileID);
            if (commonFile == null || string.IsNullOrEmpty(commonFile.SourceID))
            {
                return false;
            }
            var deleteSuccessFlag = await _fileService.DeleteFileOfRemoteServer(commonFile.SourceID, userID);
            if (!deleteSuccessFlag)
            {
                return false;
            }
            subject.ReportFileID = "";
            commonFile.DeleteFlag = "*";
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 明细处理
        /// </summary>
        /// <param name="detail"></param>
        /// <param name="qcAssessList"></param>
        /// <param name="allDetails"></param>
        /// <param name="formDetails"></param>
        /// <param name="formDetailAttributes"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private List<string> ProcessDetail(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList,
            List<HierarchicalQCDetailInfo> allDetails, List<DynamicFormDetailInfo> formDetails,
            List<DynamicFormDetailAttributeInfo> formDetailAttributes)
        {
            var result = new List<string>();
            //根据组件的ID创建Dictionary
            var componentHandlers = new Dictionary<int, Func<List<string>>>
            {
                { 100, () => ProcessLabel(detail, qcAssessList) },//标签
                { 101, () => ProcessTextBox(detail, qcAssessList, allDetails) },//单行文本框
                { 102, () => ProcessCheckBox(detail, qcAssessList, allDetails, formDetails, formDetailAttributes) },//复选框
                { 103, () => ProcessRadioButton(detail, qcAssessList, allDetails, formDetails, formDetailAttributes) },//单选框
                { 104, () => ProcessScore(detail, qcAssessList, allDetails) },//考核评分
                //{ 105, () => ProcessPersonnelOrPosition(detail, qcAssessList, allDetails, formDetails, formDetailAttributes) },//人员
                { 106, () => ProcessPersonnelOrPosition(detail, qcAssessList, allDetails, formDetails, formDetailAttributes) },//岗位
                { 109, () => ProcessGroupPanel(detail, qcAssessList, allDetails, formDetails, formDetailAttributes) }//分组面板
            };

            if (componentHandlers.TryGetValue(detail.ComponentListID, out var handler))
            {
                result.AddRange(handler());
            }
            return result;
        }

        /// <summary>
        /// 标签组件处理
        /// </summary>
        /// <param name="detail">表单明细</param>
        /// <param name="qcAssessList">评估字典</param>
        /// <param name="result">结果</param>
        /// <returns></returns>
        private static List<string> ProcessLabel(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList)
        {
            var result = new List<string>();
            var label = qcAssessList.Find(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            if (label != null && !string.IsNullOrEmpty(label.ContentName))
            {
                result.Add(label.ContentName);
            }
            return result;
        }

        /// <summary>
        /// 文本框处理
        /// </summary>
        /// <param name="detail">表单明细</param>
        /// <param name="qcAssessList">评估字典</param>
        /// <param name="allDetails">所有明细</param>
        /// <param name="result">结果</param>
        /// <returns></returns>
        private static List<string> ProcessTextBox(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList, List<HierarchicalQCDetailInfo> allDetails)
        {
            var result = new List<string>();
            var title = qcAssessList.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            var value = allDetails.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            if (title != null && value != null)
            {
                result.Add($"{title.ContentName}：{value.Result.Replace("\"", "")}");
            }
            return result;
        }

        /// <summary>
        /// 处理复选框相关逻辑并返回结果字符串列表
        /// </summary>
        /// <param name="detail">动态表单详情信息</param>
        /// <param name="qcAssessList">层级质量评估列表信息</param>
        /// <param name="allDetails">所有层级质量评估详情信息</param>
        /// <param name="formDetails">动态表单详情列表</param>
        /// <param name="formDetailAttributes">动态表单详情属性列表</param>
        /// <returns>结果字符串列表</returns>
        private static List<string> ProcessCheckBox(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList,
                List<HierarchicalQCDetailInfo> allDetails, List<DynamicFormDetailInfo> formDetails, List<DynamicFormDetailAttributeInfo> formDetailAttributes)
        {
            var result = new List<string>();
            var title = qcAssessList.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            if (title == null)
            {
                return result;
            }
            var valueIds = allDetails.Where(m => m.ParentID.HasValue && m.ParentID.Value.ToString() == detail.ItemID)
                                        .Select(m => m.HierarchicalQCAssessListID)
                                        .Distinct()
                                        .ToList();

            if (valueIds.Count == 0)
            {
                return result;
            }
            var formOptions = GetFormOptions(detail, formDetails, formDetailAttributes);
            var filterOptions = formOptions.Where(fo => valueIds.Contains(fo.key)).ToList();

            var selectedResults = allDetails.Where(m => valueIds.Contains(m.HierarchicalQCAssessListID) && !string.IsNullOrEmpty(m.Result))
                                               .Select(m => m.Result.Replace("\"", ""))
                                               .Distinct()
                                               .ToList();

            var resultString = $"{title.ContentName}：{string.Join("，", filterOptions.Select(m => m.value))}：{string.Join("，", selectedResults)}";
            result.Add(resultString);
            return result;
        }

        /// <summary>
        /// 单选组件处理
        /// </summary>
        /// <param name="detail">表单明细</param>
        /// <param name="qcAssessList">评估字典</param>
        /// <param name="allDetails">所有明细</param>
        /// <param name="formDetails">表单明细</param>
        /// <param name="formDetailAttributes">表单明细属性</param>
        /// <param name="result">结果</param>
        /// <returns></returns>
        private static List<string> ProcessRadioButton(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList,
            List<HierarchicalQCDetailInfo> allDetails, List<DynamicFormDetailInfo> formDetails, List<DynamicFormDetailAttributeInfo> formDetailAttributes)
        {
            var result = new List<string>();
            var title = qcAssessList.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            if (title == null)
            {
                return result;
            }
            var valueIds = allDetails.Where(m => m.ParentID.HasValue && m.ParentID.Value.ToString() == detail.ItemID)
                                         .Select(m => m.HierarchicalQCAssessListID)
                                         .Distinct()
                                         .ToList();

            if (valueIds.Count == 0)
            {
                return result;
            }
            var formOptions = GetFormOptions(detail, formDetails, formDetailAttributes);
            var filterOptions = formOptions.Where(fo => valueIds.Contains(fo.key)).ToList();

            var selectedResults = allDetails.Where(m => valueIds.Contains(m.HierarchicalQCAssessListID) && !string.IsNullOrEmpty(m.Result))
                                               .Select(m => m.Result.Replace("\"", ""))
                                               .Distinct()
                                               .ToList();

            var resultString = $"{title.ContentName}：{string.Join("，", filterOptions.Select(m => m.value))}：{string.Join("，", selectedResults)}";
            result.Add(resultString);

            return result;
        }

        /// <summary>
        /// 考评分数组件处理
        /// </summary>
        /// <param name="detail">表单明细</param>
        /// <param name="qcAssessList">评估字典</param>
        /// <param name="allDetails">所有明细</param>
        /// <param name="result">结果</param>
        /// <returns></returns>
        private static List<string> ProcessScore(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList, List<HierarchicalQCDetailInfo> allDetails)
        {
            var result = new List<string>();
            var title = qcAssessList.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            var value = allDetails.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            if (title != null && value != null)
            {
                result.Add($"{title.ContentName}：{value.Result}分"
                    + (string.IsNullOrEmpty(value.Problem) ? "" : $"，问题：{value.Problem}")
                    + (string.IsNullOrEmpty(value.BrightSpot) ? "" : $"，亮点：{value.BrightSpot}"));
            }
            return result;
        }

        /// <summary>
        /// 人员岗位组件处理
        /// </summary>
        /// <param name="detail">表单明细</param>
        /// <param name="qcAssessList">评估字典</param>
        /// <param name="allDetails">所有明细</param>
        /// <param name="formDetails">表单明细</param>
        /// <param name="formDetailAttributes">表单明细属性</param>
        /// <param name="result">结果</param>
        /// <returns></returns>
        private static List<string> ProcessPersonnelOrPosition(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList,
            List<HierarchicalQCDetailInfo> allDetails, List<DynamicFormDetailInfo> formDetails, List<DynamicFormDetailAttributeInfo> formDetailAttributes)
        {
            var result = new List<string>();
            var title = qcAssessList.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            var value = allDetails.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            if (title != null && value != null)
            {
                var attribute = formDetailAttributes.FirstOrDefault(m => m.DynamicFormDetailID == detail.DynamicFormDetailID && m.ComponentAttributeID == TYPE_ATTRIBUTEID_28);
                var employeeIDs = new List<string>();
                if (value.Result.Contains("[") || attribute?.AttributeValue == "checkbox")
                {
                    employeeIDs = JsonConvert.DeserializeObject<List<string>>(value.Result);
                }
                if (!value.Result.Contains("[") || attribute?.AttributeValue == "radio")
                {
                    employeeIDs = [JsonConvert.DeserializeObject<string>(value.Result)];
                }
                var formOptions = GetFormOptions(detail, formDetails, formDetailAttributes);
                var filterOptions = formOptions.Where(fo => employeeIDs.Contains(fo.key.ToString())).ToList();
                result.Add($"{title.ContentName}：{string.Join("，", filterOptions.Select(m => m.value))}");
            }
            return result;
        }

        /// <summary>
        /// 分组面板处理
        /// </summary>
        /// <param name="detail">表单明细</param>
        /// <param name="qcAssessList">评估字典</param>
        /// <param name="allDetails">所有明细</param>
        /// <param name="formDetails">表单明细</param>
        /// <param name="formDetailAttributes">表单明细属性</param>
        /// <param name="result">结果</param>
        /// <returns></returns>
        private List<string> ProcessGroupPanel(DynamicFormDetailInfo detail, List<HierarchicalQCAssessListInfo> qcAssessList,
            List<HierarchicalQCDetailInfo> allDetails, List<DynamicFormDetailInfo> formDetails,
            List<DynamicFormDetailAttributeInfo> formDetailAttributes)
        {
            var result = new List<string>();
            var title = qcAssessList.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.ItemID);
            var groupResult = new List<string>();

            var childDetails = formDetails.Where(m => m.ParentID == detail.ItemID).ToList();
            foreach (var childDetail in childDetails)
            {
                result.AddRange(ProcessDetail(childDetail, qcAssessList, allDetails, formDetails, formDetailAttributes));
            }
            if (title != null && groupResult.Count > 0)
            {
                result.Add($"{title.ContentName}：{string.Join("，", groupResult)}");
            }
            return result;
        }

        /// <summary>
        /// 获取选项主键与值
        /// </summary>
        /// <param name="detail">明细</param>
        /// <param name="formDetails">表单明细</param>
        /// <param name="formDetailAttributes">表单明细字典</param>
        /// <returns></returns>
        private static List<(int key, string value)> GetFormOptions(DynamicFormDetailInfo detail, List<DynamicFormDetailInfo> formDetails,
            List<DynamicFormDetailAttributeInfo> formDetailAttributes)
        {
            return (from fa in formDetailAttributes
                    join pd in formDetails
                    on fa.DynamicFormDetailID equals pd.DynamicFormDetailID
                    where pd.ParentID == detail.ItemID && fa.ComponentAttributeID == 3
                    orderby pd.Sort
                    select (key: int.Parse(pd.ItemID), value: fa.AttributeValue)).ToList();
        }

        /// <summary>
        /// 获取评价和指导内容
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns>(guidance,improvement)</returns>
        public async Task<(string guidance, string improvement)> GetGuidanceAndImprovement(string careMainID)
        {
            var (guidance, improvement) = await _hierarchicalQCMainRepository.GetGuidanceAndImprovement(careMainID);
            return (guidance, improvement);
        }

        /// <summary>
        /// 获取主题下拉框options
        /// </summary>
        /// <param name="view"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, string>>> GetSubjectSelectOptions(GetQCSubjectView view, string employeeID)
        {
            var tableView = new List<Dictionary<string, string>>();
            var qcSubjectInfos = await _hierarchicalQCSubjectRepository.GetSubject(view);
            //未传维护部门ID时
            if (!view.DepartmentID.HasValue)
            {
                //依据规则是否只获取权限部门维护数据
                qcSubjectInfos = await GetSwitchData(qcSubjectInfos, view.QCLevel, employeeID);
            }
            if (qcSubjectInfos.Count == 0)
            {
                return tableView;
            }
            tableView = qcSubjectInfos.Select(m => new Dictionary<string, string>
            {
                {"label",m.FormName },
                {"value",m.HierarchicalQCSubjectID },
                {"templateCode",m.TemplateCode },
                {"startDate",m.StartDate.ToString("yyyy-MM-dd")},
                {"endDate",m.EndDate.ToString("yyyy-MM-dd")}
            }).ToList();
            return tableView;
        }

        /// <summary>
        /// 获取评价标准
        /// </summary>
        /// <param name="templateCode">模板ID</param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, string>>> GetQuestionTitles(string templateCode)
        {
            var qcAssessList = await _hierarchicalQCAssessListRepository.GetAll<HierarchicalQCAssessListInfo>();
            var details = await _hierarchicalQCDetailRepository.GetQCExportDatas(templateCode);
            var attributeList = details.Where(m => int.TryParse(m.AttributeValue, out int a)).Select(m => m.AttributeValue).Distinct().OrderByDescending(m => m).ToList();
            var baseDict = new Dictionary<string, string>();
            foreach (var attribute in attributeList)
            {
                baseDict.Add(attribute, "");
            }
            var result = new List<Dictionary<string, string>>();
            foreach (var detail in details.OrderBy(m=>m.Sort))
            {
                var child = baseDict.CloneObj();
                if (child.ContainsKey(detail.AttributeValue))
                {
                    var assess = qcAssessList.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == detail.AssessListID);
                    if (assess != null && !string.IsNullOrEmpty(assess.ContentName))
                    {
                        if (detail.ComponentListID == 104)
                        {
                            child[child.LastOrDefault().Key] = assess.ContentName;
                        }
                        else
                        {
                            child[detail.AttributeValue] = assess.ContentName;
                        }
                    }
                }
                
                result.Add(child);
            }
            Dictionary<string, object> lastNonNullValues = [];
            object lastFirstKeyValue = null;
            string lastColumnKey = "2";
            foreach (var dict in result)
            {
                object currentFirstKeyValue = dict.FirstOrDefault().Value;
                foreach (var key in dict.Keys.ToList())
                {
                    if (dict[key] == null || dict[key].ToString() == "")
                    {
                        if (lastNonNullValues.ContainsKey(key))
                        {
                            if (key == lastColumnKey)
                            {
                                if (currentFirstKeyValue.Equals(lastFirstKeyValue))
                                {
                                    dict[key] = (string)lastNonNullValues[key];
                                }
                            }
                            else
                            {
                                dict[key] = (string)lastNonNullValues[key];
                            }
                        }
                    }
                    else
                    {
                        lastNonNullValues[key] = dict[key];
                    }
                }
                lastFirstKeyValue = currentFirstKeyValue;
            }
            return result;
        }

        /// <summary>
        /// 获取主记录下的考核记录
        /// </summary>
        /// <param name="mainID">主ID</param>
        /// <returns></returns>
        public async Task<List<HQcMainApproveView>> GetQcMainViews(string mainID)
        {
            var recordID = await _hierarchicalQCMainRepository.GetQcRecordIDByMainID(mainID);
            var hierarchicalQCRecord = await _hierarchicalQCRecordRepository.GetDataByRecordID(recordID);
            var hierarchicalQCSubject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectByID(hierarchicalQCRecord?.HierarchicalQCSubjectID);
            // 根据主记录ID获取所有考核记录
            var qcMainViews = await _hierarchicalQCMainRepository.GetQcMainViewsByRecordID(recordID);
            qcMainViews = qcMainViews.Where(m => m.AuditStatus is not "2").ToList();
            var sort = 0;
            foreach (var view in qcMainViews)
            {
                view.HQcSort = ++sort;
                view.MinPassingScore = hierarchicalQCSubject?.MinPassingScore;
            }
            return qcMainViews;
        }

        /// <summary>
        /// 获取质控预览图片
        /// </summary>
        /// <param name="careMainID">主键</param>
        /// <param name="templateCode">模板编号</param>
        /// <returns></returns>
        public async Task<ImgPreviewList> GetPreviewImageAsync(string careMainID, string templateCode)
        {
            var result = new ImgPreviewList
            {
                ImageList = []
            };
            var details = await _hierarchicalQCDetailRepository.GetQCDetailByQCMainID(careMainID);
            List<(int?, List<string>)> fileStores = [];
            foreach (var item in details)
            {
                if (string.IsNullOrWhiteSpace(item.Value) || !item.Value.Contains("fileIDs"))
                {
                    continue;
                }
                var valueDict = ListToJson.ToList<Dictionary<string, List<string>>>(item.Value);
                if (valueDict.TryGetValue("fileIDs", out var valueJson) && valueJson is List<string> jsonList)
                {
                    fileStores.Add((item.GroupID, jsonList));
                }
            }
            var formDetails = await _dynamicFormDetailRepository.GetFormDetailListByFormRecordID(templateCode);

            var commonFileIDs = fileStores.SelectMany(m => m.Item2).ToList();
            var commonFileInfos = await _commonFileRepository.GetPartFileInfosByIDsAsync(commonFileIDs);
            var remoteFileIDs = commonFileInfos.Select(m => m.SourceID).ToList();
            var documentMainViews = await _fileService.GetFileAccessInfosFromFileSystemAsync(remoteFileIDs);
            if (documentMainViews == null || documentMainViews.Count < 0)
            {
                return result;
            }
            var attributes = await _dynamicFormDetailAttributeRepository.GetDetailAttributeListByRecordID(templateCode);
            foreach (var fileStore in fileStores)
            {
                var Groups = commonFileInfos.Where(m => fileStore.Item2.Contains(m.CommonFileID)).Select(m => m.SourceID).ToList();
                var Uris = documentMainViews.Where(m => Groups.Contains(m.DocumentMainID)).Select(m => m.DocumentUrl).ToList();
                var imgList = new ImgPreviewImageList
                {
                    ImageTitle = string.Empty,
                    ImageSrcList = Uris
                };

                if (!fileStore.Item1.HasValue || fileStore.Item1 == 0)
                {
                    result.ImageList.Add(imgList);
                    continue;
                }
                var urlDetail = formDetails.Find(m => m.ComponentListID == COMPONENTID_IMG && m.ParentID == fileStore.Item1.ToString());
                if (urlDetail == null)
                {
                    result.ImageList.Add(imgList);
                    continue;
                }
                var groupDetail = formDetails.Find(m => m.ItemID == urlDetail.ParentID);
                if (groupDetail == null)
                {
                    result.ImageList.Add(imgList);
                    continue;
                }
                var title = attributes.Find(m => m.DynamicFormDetailID == groupDetail.DynamicFormDetailID && m.ComponentAttributeID == TEXT_ATTRIBUTEID_3).AttributeValue;
                imgList.ImageTitle = title;
                result.ImageList.Add(imgList);
            }
            return result;
        }

        /// <summary>
        /// 发送通知消息
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="message"></param>
        /// <param name="path"></param>
        /// <returns></returns>
        private async Task SendNotice(List<string> employeeIDs, string message, string path)
        {
            foreach (var employeeID in employeeIDs)
            {
                var messageView = new MessageView
                {
                    MessageTools = [MessageTool.Wechat],
                    EmployeeID = employeeID,
                    ClientType = (int)ClientType.Mobile,
                    MessageCondition = new MessageConditionView
                    {
                        // 移动端与PC端使用不同的交换机
                        MQExchangeName = "MQNotification",
                        MQRoutingKey = employeeID,
                        Message = message,
                        ClientType = (int)ClientType.Mobile,
                        Url = path
                    }
                };
                await _messageService.SendMessage(messageView);
            }
        }

        /// 获取阅读按钮
        /// </summary>
        /// <param name="recordID">记录ID</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns>返回布尔值表示是否显示重新评估按钮</returns>
        public async Task<bool> GetQcResultShowReadAsync(string recordID, string employeeID)
        {
            // 检查参数是否为空
            if (string.IsNullOrEmpty(recordID) || string.IsNullOrEmpty(employeeID))
                return false;

            // 根据recordID获取质控记录
            var record = await _hierarchicalQCRecordRepository.GetDataByRecordID(recordID);
            // 检查记录是否存在，并且当前员工是质控人员或验证人员
            return !(record is not null &&
                   (record.HierarchicalQCEmployID?.Contains(employeeID) == true ||
                    record.VerifierEmployeeID == employeeID));
        }

        /// <summary>
        /// 更新阅读状态
        /// </summary>
        /// <param name="mainID">主记录ID</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateReadStatusAsync(string mainID, string employeeID)
        {
            // 检查参数是否为空
            if (string.IsNullOrEmpty(mainID) || string.IsNullOrEmpty(employeeID))
                return false;
            var mainInfo = await _hierarchicalQCMainRepository.GetDataByMainID(mainID);
            if (mainInfo is null)
                return false;
            mainInfo.IsReadFlag = true;
            mainInfo.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
    }
}
