﻿namespace NursingManagement.ViewModels
{
    public class SaveExaminerScheduleView
    {
        /// <summary>
        /// 监考安排ID
        /// </summary>
        public string ExaminerScheduleID { get; set; }

        /// <summary>
        /// 考试记录ID集合
        /// </summary>
        public List<string> ExaminationRecordIDs { get; set; }
        /// <summary>
        /// 监考人工号集合
        /// </summary>
        public List<string> Examiners { get; set; }

        /// <summary>
        /// 计划日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }
        /// <summary>
        /// 计划时间段
        /// </summary>
        public List<TimeSpan> ScheduleTimeRange { get; set; }
        /// <summary>
        /// 是否批量生成
        /// </summary>
        public bool? BatchFlag { get; set; }
        /// <summary>
        /// 批量生成间隔天数
        /// </summary>
        public int? BatchInterval { get; set; }
        /// <summary>
        /// 批量生成结束日期
        /// </summary>
        public DateTime? BatchEndDate { get; set; }
        /// <summary>
        /// 考核地点
        /// </summary>
        public string Location { get; set; }
    }
}
