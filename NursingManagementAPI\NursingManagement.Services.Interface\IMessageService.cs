﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IMessageService
    {
        /// <summary>
        /// 获取消息分类和类型
        /// </summary>
        /// <param name="messageTool"></param>
        /// <param name="messagetype"></param>
        /// <returns></returns>
        Tuple<MessageTool, MessageType> GetMessageToolAndMessageType(int messageTool, int messagetype);
        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="messageView"></param>
        /// <returns></returns>
        Task<bool> SendMessage(MessageView messageView);
    }
}