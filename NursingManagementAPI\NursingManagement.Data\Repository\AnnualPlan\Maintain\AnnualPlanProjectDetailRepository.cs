﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Linq;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划项目明细
    /// </summary>
    public class AnnualPlanProjectDetailRepository : IAnnualPlanProjectDetailRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public AnnualPlanProjectDetailRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }
        /// <summary>
        /// 获取项目明细
        /// </summary>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanProjectDetailInfo> GetDetailByID(string projectDetailID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.FirstOrDefaultAsync(m => m.DetailID == projectDetailID);
        }
        /// <summary>
        /// 获取当前分组的明细实体
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanProjectDetailInfo>> GetProjectInfosByGroupID(string groupID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.Where(m => m.AnnualPlanGoalGroupID == groupID && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 获取序号之后的明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="sort">序号</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanProjectDetailInfo>> GetAfterSortDetail(string mainID, int sort)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos
                .Where(m => m.AnnualPlanMainID == mainID && m.Sort >= sort && m.DeleteFlag != "*")
                .OrderBy(m => m.Sort).ToListAsync();
        }
        /// <summary>
        /// 根据分组ID获取明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        public async Task<List<APProjectDetail>> GetViewsByGroupID(string mainID, string groupID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos
                .Where(m => m.AnnualPlanMainID == mainID && m.AnnualPlanGoalGroupID == groupID && m.DeleteFlag != "*")
                .Select(m => new APProjectDetail
                {
                    MainID = m.AnnualPlanMainID,
                    DetailID = m.DetailID,
                    MainGoalID = m.AnnualPlanMainGoalID,
                    GroupID = m.AnnualPlanGoalGroupID,
                    Content = m.Content,
                    Sort = m.Sort,
                    MarkID = m.MarkID
                }).ToListAsync();
        }
        /// <summary>
        /// 根据分组ID获取项目明细ID集合
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        public async Task<string[]> GetProjectDetailIDsByGroupID(string groupID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.Where(m => m.AnnualPlanGoalGroupID == groupID && m.DeleteFlag != "*")
                .Select(m => m.DetailID).ToArrayAsync();
        }
        /// <summary>
        /// 根据主表ID获取工作项目
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalIDs">计划目标表ID</param>
        /// <returns></returns>
        public async Task<List<APProjectDetail>> GetViewsByPlanMainID(string mainID, string[] mainGoalIDs = null)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .IfWhere(mainGoalIDs?.Length > 0, m => mainGoalIDs.Contains(m.AnnualPlanMainGoalID))
                .Select(m => new APProjectDetail
                {
                    MainID = m.AnnualPlanMainID,
                    DetailID = m.DetailID,
                    MainGoalID = m.AnnualPlanMainGoalID,
                    GroupID = m.AnnualPlanGoalGroupID,
                    Content = m.Content,
                    Sort = m.Sort,
                    MarkID = m.MarkID
                }).OrderBy(m => m.Sort).ToListAsync();
        }
        /// <summary>
        /// 根据主表ID获取工作项目集合，不跟踪
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanProjectDetailInfo>> GetInfosByPlanMainIDAsNoTracking(string annualPlanMainID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.AsNoTracking().Where(m => m.AnnualPlanMainID == annualPlanMainID && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 根据主表ID获取工作项目集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanProjectDetailInfo>> GetInfosByMainID(string annualPlanMainID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos
                .OrderBy(m => m.Sort)
                .Where(m => m.AnnualPlanMainID == annualPlanMainID && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 获取一定范围内的明细
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <param name="beginSort">开始序号</param>
        /// <param name="endSort">结束序号</param>
        /// <returns></returns>
        public async Task<IBaseAPDetail[]> GetRangeInfosByMainID(string annualPlanMainID, int beginSort, int endSort)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos
                .Where(m => m.AnnualPlanMainID == annualPlanMainID && m.Sort >= beginSort && m.Sort <= endSort && m.DeleteFlag != "*")
                .ToArrayAsync();
        }
        /// <summary>
        /// 获取简略的指标明细集合
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <param name="mainGoalIDs">目标ID集合</param>
        /// <returns></returns>
        public async Task<APProjectDetail[]> GetProjectDetailsByPlanMainID(string planMainID, string[] mainGoalIDs = null)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.Where(m => planMainID == m.AnnualPlanMainID && m.DeleteFlag != "*")
                .IfWhere(mainGoalIDs?.Length > 0, m => mainGoalIDs.Contains(m.AnnualPlanMainGoalID))
                .Select(m => new APProjectDetail
                {
                    MainID = m.AnnualPlanMainID,
                    MainGoalID = m.AnnualPlanMainGoalID,
                    GroupID = m.AnnualPlanGoalGroupID,
                    DetailID = m.DetailID,
                    Sort = m.Sort,
                }).OrderBy(m => m.Sort).ToArrayAsync();
        }
        /// <summary>
        /// 根据主表ID和MarkID获取工作项目内容集合，不跟踪
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <param name="markIDs">标识ID集合</param>
        /// <param name="mainGoalID">主记录目标内容主键ID</param>
        /// <returns></returns>
        public async Task<List<string>> GetProjectDetailContentAsNoTracking(string annualPlanMainID, List<string> markIDs, string mainGoalID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.Where(
                m => m.AnnualPlanMainID == annualPlanMainID && markIDs.Contains( m.MarkID)  && m.AnnualPlanMainGoalID == mainGoalID
                && m.DeleteFlag != "*" && m.Content != null).Select(m=>m.Content).ToListAsync();
        }
        /// <summary>
        /// 分组序号
        /// </summary>
        /// <param name="groupID">分组序号</param>
        /// <param name="groupSort">分组排序号</param>
        /// <returns></returns>
        public async Task<int> GetMaxSortByGroupID(string groupID, int groupSort)
        {
            var currentGroupSorts = await _nursingManagementDbContext.AnnualPlanProjectDetailInfos.Where(m => m.AnnualPlanGoalGroupID == groupID && m.DeleteFlag != "*").Select(m => m.Sort).ToListAsync();
            // 如果当前分组无明细，则查询此分组之前的分组最大序号
            if (currentGroupSorts.Count == 0)
            {
                var sorts = await (from a in _nursingManagementDbContext.AnnualPlanProjectDetailInfos
                                   from b in _nursingManagementDbContext.AnnualPlanGoalGroupInfos
                                   where a.AnnualPlanGoalGroupID == b.AnnualPlanGoalGroupID && b.Sort < groupSort
                                   select a.Sort).ToListAsync();
                // 如果此前的分组也没有明细，则返回最大序号为0
                if (sorts.Count == 0)
                {
                    return 0;
                }
                // 否则返回此前分组最大序号
                return sorts.Max();
            }
            var maxSort = currentGroupSorts.Max();
            return maxSort;
        }
        /// <summary>
        /// 根据主表ID获取工作项目集合
        /// </summary>
        /// <param name="mainGoalID">目标表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanProjectDetailInfo>> GetInfosByMainGoalID(string mainGoalID)
        {
            return await _nursingManagementDbContext.AnnualPlanProjectDetailInfos
                .OrderBy(m => m.Sort)
                .Where(m => m.AnnualPlanMainGoalID == mainGoalID && m.DeleteFlag != "*")
                .ToListAsync();
        }
    }
}
