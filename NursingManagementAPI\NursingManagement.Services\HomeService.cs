﻿using Microsoft.Extensions.DependencyInjection;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.HomeService;

namespace NursingManagement.Services
{
    public class HomeService : IHomeService
    {
        private readonly IServiceProvider _serviceProvider;
        private IAnnualScheduleService _annualScheduleService;
        private readonly IApproveRecordRepository _approveRecordRepository;
        private readonly IApproveDetailRepository _approveDetailRepository;
        private readonly IRouterListRepository _routingListRepository;
        private readonly IApproveProcessRepository _approveProcessRepository;
        private readonly IMessageRecordRepository _messageRecordRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IMessageToDepartmentRepository _messageToDepartmentRepository;
        public HomeService(
            IServiceProvider serviceProvider,
            IApproveRecordRepository approveRecordRepository,
            IApproveDetailRepository approveDetailRepository,
            IRouterListRepository routerListRepository,
            IApproveProcessRepository approveProcessRepository,
            IMessageRecordRepository messageRecordRepository,
            ISettingDictionaryRepository settingDictionaryRepository,
            IMessageToDepartmentRepository messageToDepartmentRepository
        )
        {
            _serviceProvider = serviceProvider;
            _approveRecordRepository = approveRecordRepository;
            _approveDetailRepository = approveDetailRepository;
            _routingListRepository = routerListRepository;
            _approveProcessRepository = approveProcessRepository;
            _messageRecordRepository = messageRecordRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _messageToDepartmentRepository = messageToDepartmentRepository;
        }
        /// <summary>
        /// 工作重点
        /// </summary>
        private const string MESSAGE_TYPE_1 = "1";
        /// <summary>
        /// 全院部门序号
        /// </summary>
        private const int DEPARTMENTM_ID_999999 = 999999;
        /// <summary>
        /// 获取待办列表
        /// </summary>
        /// <param name="employeeID">当前用户ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="todoType">待办类别</param>
        /// <param name="clientType">客户端类型</param>
        /// <returns>待办的事项数据</returns>
        public async Task<object> GetToDoListAsync(string employeeID, int departmentID, string todoType, int clientType)
        {
            var routerListByClientTypes = await _routingListRepository.GetRouterListByClientType(clientType);
            return todoType switch
            {
                "37" => await ReturnAppoveRecord(),
                // TODO：年度计划还未完成，数据依赖关系不完整，暂不上线，待上线后打开
                //"46" => await ReturnAnnualSchedule(),
                _ => await ReturnAll()
            };
            // 返回全部
            async Task<List<ToDoView>> ReturnAll()
            {
                return [
                    await ReturnAppoveRecord(),
                    //await ReturnAnnualSchedule()
                ];
            }
            //返回年度计划待办
            //async Task<ToDoView> ReturnAnnualSchedule()
            //{
            //    var annualScheduleToDoView = await GetUnExecAnnualScheduleAsync(employeeID, departmentID, DateTime.Now.Month, true, clientType);
            //    return new()
            //    {
            //        ToDoContent = $"未执行年度计划",
            //        RouterListID = 46,
            //        Counts = annualScheduleToDoView.Counts,
            //        ToDoList = annualScheduleToDoView.ToDoList,
            //        RouterPath = annualScheduleToDoView.RouterPath
            //    };
            //}
            // 返回审批待办
            async Task<ToDoView> ReturnAppoveRecord()
            {
                var unApproveRecord = await GetUnApproveRecordAsync(employeeID);
                return new()
                {
                    ToDoContent = $"待审批",
                    RouterListID = 37,
                    Counts = unApproveRecord.Count,
                    // pc端审批详情不在首页显示
                    ToDoList = unApproveRecord,
                    RouterPath = routerListByClientTypes.Find(m => m.RouterListID == 37)?.Path
                };
            }
        }
        /// <summary>
        /// 获取需要审批的项目明细
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        private async Task<List<ToDoDetailView>> GetUnApproveRecordAsync(string employeeID)
        {
            var approveRecordInfos = await _approveRecordRepository.GetUnCompletedApproveRecordsAsNoTrackAsync(null, null, null);
            if (approveRecordInfos.Count <= 0)
            {
                return [];
            }
            // 筛选出 （未审批的 && 当前账号需要审批的） 节点;
            var mainIDArrays = approveRecordInfos.Select(m => m.ApproveMainID).ToArray();
            var approveDetailInfos = await _approveDetailRepository.GetPartDetailsByMainIDAsync(mainIDArrays);
            var unApproveDetails = approveDetailInfos.Where(m => string.IsNullOrEmpty(m.StatusCode) && m.PreApproveEmployeeID.Contains(employeeID)).ToList();
            if (unApproveDetails.Count <= 0)
            {
                return [];
            }
            var processDict = await _approveProcessRepository.GetAllCategoryAndProcessID();
            // 过滤出当前节点需要登录人员审批的记录
            return approveRecordInfos.Where(m => unApproveDetails.Exists(n => n.ApproveMainID == m.ApproveMainID))
                .Select(m => new ToDoDetailView
                {
                    Title = m.Content,
                    ScheduleDateTime = m.AddDateTime,
                    ProveCategory = processDict.TryGetValue(m.ApproveProcessID, out var category) ? category : ""
                }).OrderBy(m => m.ScheduleDateTime).ToList();
        }
        /// <summary>
        /// 优先获取当前部门计划对应的未执行排程数据
        /// </summary>
        /// <param name="employeeID">计划执行人</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="preOrNextFlag">获取计划月份之前OR之后，True：当月和当月之前；False：当月</param>
        /// <param name="clientType">客户端类型</param>
        /// <returns></returns>
        //public async Task<ToDoView> GetUnExecAnnualScheduleAsync(string employeeID, int departmentID, int? scheduleMonth, bool preOrNextFlag, int clientType)
        //{
            //_annualScheduleService ??= _serviceProvider.GetService<IAnnualScheduleService>();

            //var scheduleViews = await _annualScheduleService.GetUnExecTasks(employeeID, departmentID, scheduleMonth, preOrNextFlag);
            //var routerListByClientTypes = await _routingListRepository.GetRouterListByClientType(clientType);
            //return new ToDoView
            //{
            //    RouterListID = 46,
            //    Counts = scheduleViews.Length,
            //    ToDoList = scheduleViews.Select(m => new ToDoDetailView
            //    {
            //        Title = m.InterventionName,
            //        ScheduleDateTime = m.ScheduleDateTime,
            //        InterventionID = m.InterventionID,
            //    }).ToList(),
            //    RouterPath = routerListByClientTypes.Find(m => m.RouterListID == 46)?.Path
            //};
        //}
        /// <summary>
        /// 获取主页呈现的消息列表
        /// </summary>
        /// <param name="departmentID">部门序号</param>
        /// <returns></returns>
        public async Task<List<HomeMessageView>> GetHomeMessageList(int departmentID)
        {
            var startDate = DateTime.Now.AddDays(-7);
            var messageSettings = await _settingDictionaryRepository.GetSettingDictionary(new SettingDictionaryParams
            {
                SettingType = "Common",
                SettingTypeCode = "MessageManagement",
                SettingTypeValue = "MessageType",
            });
            var messageRecordList = await _messageRecordRepository.GetListByStartDateAsNoTrack(startDate);
            // 取全院和本部门的消息
            var messageToDepartmentIDs = await _messageToDepartmentRepository.GetMessageRecordIDsByDepartmentIDs([departmentID, DEPARTMENTM_ID_999999]);
            messageRecordList = messageRecordList.Where(m => messageToDepartmentIDs.Contains(m.MessageRecordID)).ToList();
            var viewList = messageRecordList.Where(m => m.TopDays == 0 || m.PublishTime.Value.Date.AddDays(m.TopDays) < DateTime.Now.Date).OrderByDescending(m => m.PublishTime.Value)
            .Select(m => new HomeMessageView
            {
                PublishTime = m.PublishTime.Value,
                MessageTitle = m.MessageTitle,
                MessageRecordID = m.MessageRecordID,
                MessageType = m.MessageType,
                MessageTypeName = messageSettings.Find(n => n.SettingValue == m.MessageType)?.Description,
                MessageContent = m.MessageType == MESSAGE_TYPE_1 ? m.MessageContent : ""
            }).ToList();
            // 置顶消息
            var topMessageList = messageRecordList.Where(m => m.TopDays > 0 && m.PublishTime.Value.Date.AddDays(m.TopDays) >= DateTime.Now.Date).OrderByDescending(m => m.PublishTime.Value).
                Select(m => new HomeMessageView
                {
                    PublishTime = m.PublishTime.Value,
                    MessageTitle = m.MessageTitle,
                    MessageRecordID = m.MessageRecordID,
                    MessageType = m.MessageType,
                    IsNew = m.PublishTime.Value.Date == DateTime.Now.Date,
                    IsTop = true,
                    MessageTypeName = messageSettings.Find(n => n.SettingValue == m.MessageType)?.Description,
                    MessageContent = m.MessageType == MESSAGE_TYPE_1 ? m.MessageContent : ""
                }).ToList();
            topMessageList.AddRange(viewList);
            return topMessageList;
        }
    }
}
