﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.DependencyInjection;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Interface.Employee;
using NursingManagement.Data.Repository;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class ApproveProcessService : IApproveProcessService
    {
        private readonly IApproveProcessRepository _approveProcessRepository;
        private readonly IDepartmentToApproveProcessRepository _departmentToApproveProcessRepository;
        private readonly IApproveProcessNodeRepository _approveProcessNodeRepository;
        private readonly IApproveProcessNodeDetailRepository _approveProcessNodeDetailRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IDepartmentToJobRepository _departmentToJobRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly TransactionTool _transactionTool;
        private readonly IServiceProvider _serviceProvider;
        private readonly ISchedulingRequestRepository _schedulingRequestRepository;
        private readonly IAdjustScheduleRecordRepository _adjustScheduleRecordRepository;
        private readonly IEmployeeSecondmentRecordRepository _employeeSecondmentRecordRepository;
        private readonly IAttendanceApproveRecordRepository _attendanceApproveRecordRepository;
        private readonly IHierarchicalQCMainRepository _hierarchicalQCMainRepository;
        /// <summary>
        /// 管理员角色ID
        /// </summary>
        private const int  MANAGER_ROLE = 99;

        public ApproveProcessService(
            IApproveProcessRepository approveProcessRepository,
            IApproveProcessNodeRepository approveProcessNodeRepository,
            IApproveProcessNodeDetailRepository approveProcessNodeDetailRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IDepartmentToApproveProcessRepository departmentToApproveProcessRepository,
            IDepartmentListRepository departmentListRepository,
            IDepartmentToJobRepository departmentToJobRepository,
            ISettingDictionaryRepository settingDictionaryRepository,
            IUnitOfWork unitOfWork,
            TransactionTool transactionTool,
            IServiceProvider serviceProvider,
            ISchedulingRequestRepository schedulingRequestRepository,
            IAdjustScheduleRecordRepository adjustScheduleRecordRepository,
            IEmployeeSecondmentRecordRepository employeeSecondmentRecordRepository,
            IAttendanceApproveRecordRepository attendanceApproveRecordRepository,
            IHierarchicalQCMainRepository hierarchicalQCMainRepository
            )
        {
            _approveProcessRepository = approveProcessRepository;
            _approveProcessNodeRepository = approveProcessNodeRepository;
            _approveProcessNodeDetailRepository = approveProcessNodeDetailRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentToApproveProcessRepository = departmentToApproveProcessRepository;
            _departmentListRepository = departmentListRepository;
            _departmentToJobRepository = departmentToJobRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _unitOfWork = unitOfWork;
            _transactionTool = transactionTool;
            _serviceProvider = serviceProvider;
            _schedulingRequestRepository = schedulingRequestRepository;
            _adjustScheduleRecordRepository = adjustScheduleRecordRepository;
            _employeeSecondmentRecordRepository = employeeSecondmentRecordRepository;
            _attendanceApproveRecordRepository = attendanceApproveRecordRepository;
            _hierarchicalQCMainRepository = hierarchicalQCMainRepository;
        }

        #region 查询
        /// <summary>
        /// 获取审批流程列表
        /// </summary>
        /// <param name="employeeID">HR工号</param>
        /// <param name="roles">角色集合</param>
        /// <returns></returns>
        public async Task<List<ApproveProcess>> GetApproveProcesses(string hospitalID)
        {
            // 管理员角色，可以查看所有科室的审批流程记录
            var approveProcesses = await _approveProcessRepository.GetApproveProcessViews(hospitalID);
            var processIDs = approveProcesses.Select(m => m.ApproveProcessID).ToArray();
            var processToDepartmentDict = await _departmentToApproveProcessRepository.GetDepartmentIDsByProcessIDs(false, processIDs);
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            foreach (var approveProcess in approveProcesses)
            {
                // 回显适用科室
                processToDepartmentDict.TryGetValue(approveProcess.ApproveProcessID, out var departmentIDs);
                approveProcess.DepartmentIDs = departmentIDs;
                var departmentNames = departmentList.Where(m => departmentIDs?.Contains(m.DepartmentID) ?? false).Select(m => m.LocalShowName);
                if (departmentIDs?.Contains(999999) ?? false)
                {
                    departmentNames = departmentNames.Prepend("全院");
                }
                approveProcess.DepartmentNames = string.Join("，", departmentNames);
                // 回显制定者
                approveProcess.AddEmployeeName = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(approveProcess.AddEmployeeID, m => m.EmployeeName);
            }

            return approveProcesses;
        }
        /// <summary>
        /// 查询审批流程节点列表
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        public async Task<List<ApproveProcessNode>> GetApproveProcessNodes(string approveProcessID)
        {
            var approveProcessNodes = await _approveProcessNodeRepository.GetApproveProcessNodeViews(approveProcessID);
            var nodeDetails = await _approveProcessNodeDetailRepository.GetViewsByProcessID(approveProcessID);
            foreach (var approveNode in approveProcessNodes)
            {
                approveNode.NodeDetails = nodeDetails.FindAll(m => m.ApproveNodeID == approveNode.ApproveNodeID);
                foreach (var nodeDetail in approveNode.NodeDetails)
                {
                    // 回显节点明细内容
                    nodeDetail.Name = nodeDetail.NodeDetailType switch
                    {
                        ApproveProcessNodeDetailType.Employee => await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(nodeDetail.DataValue, m => m.EmployeeName),
                        ApproveProcessNodeDetailType.Position => await GetPositionNameByProcessNodeDetail(nodeDetail),
                        ApproveProcessNodeDetailType.SelfSelected => "业务审批人",//跟前端显示内容保持一致
                        _ => null
                    };
                }
            }
            return SortNodes(approveProcessNodes);
        }
        /// <summary>
        /// 对节点进行排序
        /// </summary>
        /// <param name="nodes"></param>
        /// <returns></returns>
        private static List<ApproveProcessNode> SortNodes(List<ApproveProcessNode> nodes)
        {
            // 将每个节点放入字典中，以NodeID为键。
            var nodeDictionary = nodes.ToDictionary(node => node.ApproveNodeID);

            // 找到起始节点，即那些没有其他节点指向它的节点。
            var startNode = nodes.FirstOrDefault(node => !nodes.Any(n => n.NextNodeID == node.ApproveNodeID));

            // 如果没有找到起始节点，说明链表可能有问题，返回null或空列表。
            if (startNode == null)
            {
                return null;
            }

            // 开始构建排序后的列表。
            var sortedNodes = new List<ApproveProcessNode>();

            // 从起始节点开始追踪，直到没有下一个节点。
            var currentNode = startNode;
            while (currentNode != null)
            {
                sortedNodes.Add(currentNode);
                currentNode = !string.IsNullOrEmpty(currentNode.NextNodeID) ? nodeDictionary[currentNode.NextNodeID] : null;
            }

            return sortedNodes;
        }
        /// <summary>
        /// 根据节点明细数据获取职务名称
        /// </summary>
        /// <param name="nodeDetail">节点明细</param>
        /// <returns></returns>
        private async Task<string> GetPositionNameByProcessNodeDetail(ApproveProcessNodeDetail nodeDetail)
        {
            var positionName = await _departmentToJobRepository.GetJobAndDepartmentNameByJobCode(nodeDetail.DataValue, nodeDetail.DepartmentID);
            if (nodeDetail.DepartmentID.HasValue)
            {
                var departmentName = (await _departmentListRepository.GetByIDAsync(nodeDetail.DepartmentID.Value))?.LocalShowName;
                if (!string.IsNullOrEmpty(departmentName))
                {
                    positionName = $"{departmentName}-{positionName}";
                }
            }
            return positionName;
        }
        /// <summary>
        /// 根据分类、科室获取审批流程ID
        /// </summary>
        /// <param name="proveCategory">分类码</param>
        /// <param name="departmentID">科室</param>
        /// <returns></returns>
        public async Task<(string, string)> GetProcessIDAndContentTemplateByTypeAndDepartmentID(string proveCategory, int departmentID)
        {
            // 根据分类获取审批流程列表
            var processDict = await _approveProcessRepository.GetProcessIDsByProveCategory(proveCategory);
            // 获取它们的适用科室
            var keyPairs = await _departmentToApproveProcessRepository.GetDepartmentIDsByProcessIDs(true, processDict.Keys.ToArray());
            // 先精确匹配
            var processID = keyPairs.FirstOrDefault(m => m.Value.Contains(departmentID)).Key;
            // 若无匹配项，再匹配通用的
            processID ??= keyPairs.FirstOrDefault(m => m.Value.Contains(999999)).Key;
            var contentTemplate = "";
            if (processID != null)
            {
                processDict.TryGetValue(processID, out contentTemplate);
            }
            return (processID, contentTemplate);
        }
        /// <summary>
        /// 获取分类已存启用科室集合接口
        /// </summary>
        /// <param name="proveCategory">分类码</param>
        /// <returns></returns>
        public async Task<int[]> GetEnableDepartmentIDsByProveCategory(string proveCategory)
        {
            // 根据分类获取审批流程列表
            var processDict = await _approveProcessRepository.GetProcessIDsByProveCategory(proveCategory);
            // 获取它们的适用科室
            var keyPairs = await _departmentToApproveProcessRepository.GetDepartmentIDsByProcessIDs(true, processDict.Keys.ToArray());
            var applyDepartmentIDs = keyPairs.SelectMany(m => m.Value).Distinct().ToArray();
            return applyDepartmentIDs;
        }
        /// <summary>
        /// 根据审批流程ID获取审批结束后的API
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        public async Task<string> GetApiByProcessID(string approveProcessID)
        {
            var proveCategory = await _approveProcessRepository.GetProveCategoryByProcessID(approveProcessID);
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ProveCategoryToAPI",
                SettingTypeValue = proveCategory,
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var setting = settingList.FirstOrDefault();
            if (setting == null)
            {
                return null;
            }
            return setting.SettingValue;
        }
        #endregion

        #region 增删改
        /// <summary>
        /// 新增审批流程
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        public async Task<bool> AddApproveProcess(SaveApproveProcessView saveView)
        {
            if (saveView.Nodes?.Count == 0 || string.IsNullOrEmpty(saveView.ProcessName))
            {
                throw new Exception("缺少必要参数！");
            }
            // 创建审批流程表实例
            var newProcessInfo = new ApproveProcessInfo
            {
                ProcessName = saveView.ProcessName,
                HospitalID = saveView.HospitalID,
                ProcessDescription = saveView.ProcessDescription,
                ContentTemplate = saveView.ContentTemplate,
                ProveCategory = saveView.ProveCategory,
            };
            newProcessInfo.ApproveProcessID = newProcessInfo.GetId();
            newProcessInfo.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
            await _unitOfWork.GetRepository<ApproveProcessInfo>().InsertAsync(newProcessInfo);
            // 创建审批流程节点
            foreach (var node in saveView.Nodes)
            {
                await AddNodeAndNodeDetails(node, newProcessInfo.ApproveProcessID, saveView.EmployeeID);
            }
            // 增加关联的适用科室
            var newDepartmentToApproveProcessInfos = new List<DepartmentToApproveProcessInfo>();
            foreach (var departmentID in saveView.DepartmentIDs)
            {
                var newInfo = new DepartmentToApproveProcessInfo
                {
                    ApproveProcessID = newProcessInfo.ApproveProcessID,
                    HospitalID = saveView.HospitalID,
                    DepartmentID = departmentID,
                };
                newInfo.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
                newDepartmentToApproveProcessInfos.Add(newInfo);
            }
            await _unitOfWork.GetRepository<DepartmentToApproveProcessInfo>().InsertAsync(newDepartmentToApproveProcessInfos);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 更新审批流程
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        public async Task<bool> UpdateApproveProcess(SaveApproveProcessView saveView)
        {
            if (saveView.Nodes?.Count == 0 || string.IsNullOrEmpty(saveView.ProcessName) || string.IsNullOrEmpty(saveView.ApproveProcessID))
            {
                throw new Exception("缺少必要参数！");
            }

            var oldInfo = await _approveProcessRepository.GetApproveProcessInfo(saveView.ApproveProcessID);
            if (oldInfo == null)
            {
                return false;
            }
            oldInfo.ProveCategory = saveView.ProveCategory;
            oldInfo.ProcessName = saveView.ProcessName;
            oldInfo.ProcessDescription = saveView.ProcessDescription;
            oldInfo.ContentTemplate = saveView.ContentTemplate;

            var oldNodeInfos = await _approveProcessNodeRepository.GetApproveProcessNodeInfos(saveView.ApproveProcessID);
            var oldNodeDetailInfos = await _approveProcessNodeDetailRepository.GetInfosByProcessID(saveView.ApproveProcessID);
            foreach (var node in saveView.Nodes)
            {
                var oldNodeInfo = oldNodeInfos.Find(m => m.ApproveNodeID == node.ApproveNodeID);

                if (oldNodeInfo == null)
                {
                    await AddNodeAndNodeDetails(node, oldInfo.ApproveProcessID, saveView.EmployeeID);
                    continue;
                }
                var currentOldNodeDetails = oldNodeDetailInfos.FindAll(m => m.ApproveNodeID == oldNodeInfo.ApproveNodeID);
                await UpdateNodeAndNodeDetails(oldNodeInfo, node, currentOldNodeDetails, node.NodeDetails, saveView.EmployeeID);
            }

            // 更新审批流程科室对照
            await UpdateAttachDepartments(saveView);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 新增节点&节点明细
        /// </summary>
        /// <param name="node">节点对象</param>
        /// <param name="processID">流程表主键</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        private async Task AddNodeAndNodeDetails(ApproveProcessNode node, string processID, string employeeID)
        {
            var nodeID = node.ApproveNodeID?.GetRealID(out _);
            var nextNodeID = node.NextNodeID?.GetRealID(out _);
            if (string.IsNullOrEmpty(nodeID))
            {
                return;
            }
            var nodeInfo = new ApproveProcessNodeInfo
            {
                ApproveNodeID = nodeID,
                ApproveProcessID = processID,
                ApproveNodeName = node.ApproveNodeName,
                ApproveTimeLimit = node.ApproveTimeLimit,
                NextNodeID = nextNodeID,
                ApproveModel = node.ApproveModel,
            };
            nodeInfo.Add(employeeID).Modify(employeeID);
            await _unitOfWork.GetRepository<ApproveProcessNodeInfo>().InsertAsync(nodeInfo);
            // 节点明细
            foreach (var nodeDetail in node.NodeDetails)
            {
                var nodeDetailInfo = new ApproveProcessNodeDetailInfo
                {
                    ApproveProcessID = processID,
                    ApproveNodeID = nodeID,
                    NodeDetailType = nodeDetail.NodeDetailType,
                    DataValue = nodeDetail.DataValue ?? "",
                    DepartmentID = nodeDetail.DepartmentID
                };
                nodeDetailInfo.Add(employeeID).Modify(employeeID);
                nodeDetailInfo.ApproveNodeDetailID = nodeDetailInfo.GetId();
                await _unitOfWork.GetRepository<ApproveProcessNodeDetailInfo>().InsertAsync(nodeDetailInfo);
            }
        }
        /// <summary>
        /// 更新节点
        /// </summary>
        /// <param name="oldNodeInfo">旧节点实例</param>
        /// <param name="node">新节点数据</param>
        /// <param name="oldNodeDetailInfos">旧节点明细实例集合</param>
        /// <param name="nodeDetails">新节点明细数据集合</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        private async Task UpdateNodeAndNodeDetails(ApproveProcessNodeInfo oldNodeInfo, ApproveProcessNode node, List<ApproveProcessNodeDetailInfo> oldNodeDetailInfos, List<ApproveProcessNodeDetail> nodeDetails, string employeeID)
        {
            if (node.IsDeleted)
            {
                oldNodeInfo.Delete(employeeID);
                oldNodeDetailInfos.ForEach(m => m.Delete(employeeID));
                return;
            }
            node.NextNodeID = node.NextNodeID?.GetRealID(out _);
            var isUpdate = ObjUpdater<ApproveProcessNodeInfo, ApproveProcessNode>.UpdateWithSource(oldNodeInfo, node);
            if (isUpdate)
            {
                oldNodeInfo.Modify(employeeID);
            }
            // 节点明细处理
            // 删除已不存在的节点明细
            oldNodeDetailInfos.ExceptBy(nodeDetails.Select(m => (m.NodeDetailType, m.DataValue, m.DepartmentID)), m => (m.NodeDetailType, m.DataValue, m.DepartmentID))
                .ToList().ForEach(m => m.Delete(employeeID));
            // 新增节点明细
            var newNodeDetailInfos = new List<ApproveProcessNodeDetailInfo>();
            var newNodeDetails = nodeDetails.ExceptBy(oldNodeDetailInfos.Select(m => (m.NodeDetailType, m.DataValue, m.DepartmentID)), m => (m.NodeDetailType, m.DataValue, m.DepartmentID));
            foreach (var newNodeDetail in newNodeDetails)
            {
                var nodeDetailInfo = new ApproveProcessNodeDetailInfo
                {
                    ApproveProcessID = oldNodeInfo.ApproveProcessID,
                    ApproveNodeID = oldNodeInfo.ApproveNodeID,
                    NodeDetailType = newNodeDetail.NodeDetailType,
                    DataValue = newNodeDetail.DataValue,
                    DepartmentID = newNodeDetail.DepartmentID
                };
                nodeDetailInfo.Add(employeeID).Modify(employeeID);
                nodeDetailInfo.ApproveNodeDetailID = nodeDetailInfo.GetId();
                newNodeDetailInfos.Add(nodeDetailInfo);
            }
            await _unitOfWork.GetRepository<ApproveProcessNodeDetailInfo>().InsertAsync(newNodeDetailInfos);
        }
        /// <summary>
        /// 更新流程关联科室
        /// </summary>
        /// <param name="saveView">更新View</param>
        private async Task UpdateAttachDepartments(SaveApproveProcessView saveView)
        {
            var attachDepartmentInfos = await _departmentToApproveProcessRepository.GetInfosByApproveProcessID(saveView.ApproveProcessID);

            // 获取需要删除的关联科室
            var deleteInfos = attachDepartmentInfos.FindAll(m => !saveView.DepartmentIDs.Contains(m.DepartmentID));
            deleteInfos.ForEach(m => m.Delete(saveView.EmployeeID));

            // 获取需要新增的关联科室
            var addDepartmentIDs = saveView.DepartmentIDs.Where(m => !attachDepartmentInfos.Exists(n => n.DepartmentID == m)).ToArray();
            var newInfos = new List<DepartmentToApproveProcessInfo>();
            foreach (var departmentID in addDepartmentIDs)
            {
                var newInfo = new DepartmentToApproveProcessInfo
                {
                    ApproveProcessID = saveView.ApproveProcessID,
                    DepartmentID = departmentID,
                    HospitalID = saveView.HospitalID
                };
                newInfo.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
                newInfos.Add(newInfo);
            }
            await _unitOfWork.GetRepository<DepartmentToApproveProcessInfo>().InsertAsync(newInfos);
        }
        /// <summary>
        /// 启用审批流程主表
        /// </summary>
        /// <param name="enableView">启用View</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> EnableApproveProcess(ApproveProcessStatusChangeView enableView, string employeeID)
        {
            var now = DateTime.Now;
            var isUpdated = await _transactionTool.CommitWithTrans(async () =>
            {
                // 更新流程表状态
                await _approveProcessRepository.UpdateApproveProcessStatus(enableView.ApproveProcessID, ApproveProcessStatusCode.Enable, employeeID, now);
                // 更新关联的适用科室表
                await _departmentToApproveProcessRepository.UpdateEnableDateTime(enableView.ApproveProcessID, employeeID, now);
            });
            return isUpdated;
        }
        /// <summary>
        /// 停用审批流程
        /// </summary>
        /// <param name="disableView">停用View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> DisableApproveProcess(ApproveProcessStatusChangeView disableView, string employeeID)
        {
            var isUpdated = await _transactionTool.CommitWithTrans(async () =>
            {
                // 查询流程表，更新状态
                var approveProcessInfo = await _approveProcessRepository.GetApproveProcessInfo(disableView.ApproveProcessID);
                approveProcessInfo.StatusCode = ApproveProcessStatusCode.Disable;
                approveProcessInfo.Modify(employeeID);
                // 查询关联的适用科室表，更新DisableDateTime
                var result = await _departmentToApproveProcessRepository.UpdateDisableDateTime(disableView.ApproveProcessID, employeeID, DateTime.Now);
            });
            return isUpdated;
        }
        /// <summary>
        /// 删除审批流程主表
        /// </summary>
        /// <param name="deleteView">主键</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteApproveProcess(ApproveProcessStatusChangeView deleteView, string employeeID)
        {
            var approveProcessInfo = await _approveProcessRepository.GetApproveProcessInfo(deleteView.ApproveProcessID);
            if (approveProcessInfo is null)
            {
                return false;
            }
            approveProcessInfo.Delete(employeeID);

            var approveProcessNodes = await _approveProcessNodeRepository.GetApproveProcessNodeInfos(deleteView.ApproveProcessID);
            approveProcessNodes.ForEach(approveProcessNode => approveProcessNode.Delete(employeeID));

            var departmentToApproveProcessInfos = await _departmentToApproveProcessRepository.GetInfosByApproveProcessID(deleteView.ApproveProcessID);
            departmentToApproveProcessInfos.ForEach(departmentToApproveProcessInfo => departmentToApproveProcessInfo.Delete(employeeID));

            var approveProcessNodeDetails = await _approveProcessNodeDetailRepository.GetInfosByProcessID(deleteView.ApproveProcessID);
            approveProcessNodeDetails.ForEach(approveProcessNodeDetail => approveProcessNodeDetail.Delete(employeeID));

            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        #endregion
        /// <summary>
        /// 手动提交审批
        /// </summary>
        /// <param name="approveType">审批类型</param>
        /// <param name="recordID">保存数据</param>
        /// <returns></returns>
        public async Task<bool> ManualSubmissionApproveAsync(string approveType, string recordID)
        {
            if (approveType == "SchedulingRequest")// 排班预约
            {
                var recordInfo = await _schedulingRequestRepository.GetDataByID(recordID);
                if (recordInfo == null)
                {
                    return false;
                }
                return await _serviceProvider.GetService<ISchedulingRequestService>().CreateOrUpdateApprove(recordInfo);
            }
            if (approveType == "AdjustSchedule")// 调整排班
            {
                var recordInfo = await _adjustScheduleRecordRepository.GetRecordByIDAsync(recordID);
                if (recordInfo == null)
                {
                    return false;
                }
                return await _serviceProvider.GetService<IAdjustScheduleService>().CreateOrUpdateApprove(recordInfo);
            }
            if (approveType == "EmployeeSecondmentMaintenance")// 人员借调
            {
                var recordInfo = await _employeeSecondmentRecordRepository.GetDataByID(recordID);
                if (recordInfo == null)
                {
                    return false;
                }
                return await _serviceProvider.GetService<IEmployeeService>().CreateOrUpdateEmployeeSecondmentApprove(recordInfo,false);
            }
            if (approveType == "AttendanceApproval")// 考勤审批
            {
                var recordInfo = await _attendanceApproveRecordRepository.GetRecordByRecordID(recordID);
                if (recordInfo == null)
                {
                    return false;
                }
                return await _serviceProvider.GetService<IAttendanceService>().CreateAttendanceApproval(recordInfo, "AA-090");
            }
            return false;
        }
    }
}
