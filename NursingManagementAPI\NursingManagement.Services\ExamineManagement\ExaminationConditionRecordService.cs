﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.DependencyInjection;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services.Examine
{
    public class ExaminationConditionRecordService : IExaminationConditionRecordService
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IExaminationConditionRecordRepository _examinationConditionRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IConditionMainRepository _conditionMainRepository;
        private readonly IConditionDetaiRepository _conditionDetailRepository;
        private readonly IServiceProvider _serviceProvider;
        private IConditionService _conditionService;

        public ExaminationConditionRecordService(
            IUnitOfWork unitOfWork,
            IExaminationConditionRecordRepository examinationConditionRecordRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IConditionMainRepository conditionMainRepository,
            IConditionDetaiRepository conditionDetailRepository,
            IServiceProvider serviceProvider)
        {
            _unitOfWork = unitOfWork;
            _examinationConditionRecordRepository = examinationConditionRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _conditionMainRepository = conditionMainRepository;
            _conditionDetailRepository = conditionDetailRepository;
            _serviceProvider = serviceProvider;
        }

        public IConditionService ConditionService { get => _conditionService ??= _serviceProvider.GetService<IConditionService>(); }

        /// <summary>
        /// 根据规则记录ID获取考核组卷规则
        /// </summary>
        /// <param name="conditionRecordID"></param>
        /// <returns></returns>
        public async Task<ExaminationConditionEditView> GetConditionDetailView(string conditionRecordID)
        {
            var conditionMains = await _conditionMainRepository.GetListBySourceIDs([conditionRecordID], CONDITIONRECORD_SOURCETYPE);
            if (conditionMains.Count <= 0)
            {
                _logger.Error($"获取规则详情数据失败,表ConditionMain，SourceID={conditionRecordID},SourceType={CONDITIONRECORD_SOURCETYPE}");
                return null;
            }
            var conditionDetails = await _conditionDetailRepository.GetListByMainIDs(conditionMains.Select(m => m.ConditionMainID).ToList());
            //动态过滤组件的回显数据获取
            var editView = GetFilterConditions(conditionMains, conditionDetails);

            // 获取表格形式条件筛选组件回显的数据
            var tableConditionDatas = conditionMains.Where(m => m.GroupType == "QuestionBank").ToList();
            var groupDatas = tableConditionDatas.GroupBy(m => m.GroupTypeValue);
            foreach (var groupData in groupDatas)
            {
                var conditionMainIDs = groupData.Select(m => m.ConditionMainID).ToList();
                var currDetail = conditionDetails.Where(m => conditionMainIDs.Contains(m.ConditionMainID)).ToList();
                var rowData = new
                {
                    questionBankID = groupData.Key,
                    detail = currDetail.ToDictionary(m => m.ItemID, n => n.ConditionValue)
                };
                if (editView.CountAndScoreConditions == null)
                {
                    editView.CountAndScoreConditions = [];
                }
                editView.CountAndScoreConditions.Add(rowData);
            }
            return editView;
        }

        /// <summary>
        /// 获取所有考核组卷规则记录
        /// </summary>
        /// <returns></returns>
        public async Task<List<ExaminationConditionRecordView>> GetAllRecords()
        {
            var recordViews = await _examinationConditionRecordRepository.GetAllConditionRecordViews();
            if (recordViews.Count <= 0)
            {
                return [];
            }
            var employeeIDs = recordViews.Select(m => m.AddEmployeeID).Union(recordViews.Select(m => m.ModifyEmployeeID)).ToList();
            var employeeBasicInfos = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            foreach (var viewItem in recordViews)
            {
                var addEmployeeName = employeeBasicInfos.Find(m => m.EmployeeID == viewItem.AddEmployeeID)?.EmployeeName;
                if (addEmployeeName != null)
                {
                    viewItem.AddEmployeeName = addEmployeeName;
                }
                var modifyEmployeeName = employeeBasicInfos.Find(m => m.EmployeeID == viewItem.ModifyEmployeeID)?.EmployeeName;
                if (modifyEmployeeName != null)
                {
                    viewItem.ModifyEmployeeName = modifyEmployeeName;
                }
            }
            return recordViews.OrderByDescending(m => m.AddDateTime).ToList();
        }

        /// <summary>
        /// 获取组卷规则中（题型难度、必选题）详情数据
        /// </summary>
        /// <param name="conditionMains">条件规则主表数据</param>
        /// <param name="conditionDetails">条件规则明细表数据</param>
        /// <returns></returns>
        private ExaminationConditionEditView GetFilterConditions(List<ConditionMainInfo> conditionMains, List<ConditionDetailInfo> conditionDetails)
        {
            var difficultConditionMainID = conditionMains.Where(m => m.GroupType == "CompositionPaperByDifficultyLevel")
                .Select(m => m.ConditionMainID).FirstOrDefault();
            var requiredConditionMainID = conditionMains.Where(m => m.GroupType == "CompositionPaperByRequiredChoice")
                .Select(m => m.ConditionMainID).FirstOrDefault();
            return new ExaminationConditionEditView
            {
                DifficultyLevelConditions = GetCascadeData(difficultConditionMainID),
                RequiredChoiceConditions = GetCascadeData(requiredConditionMainID)
            };

            List<FormDetailConditionView> GetCascadeData(string conditionMainID)
            {
                if (conditionMainID == null)
                {
                    return [];
                }
                return conditionDetails.Where(m => m.ConditionMainID == conditionMainID && string.IsNullOrEmpty(m.ParentID))
                        .OrderBy(m => m.Sort)
                        .Select(m => new FormDetailConditionView()
                        {
                            ItemID = m.ItemID.ToString(),
                            Condition = m.Condition,
                            Value = m.ConditionValue,
                            ConditionType = m.ConditionType,
                            Children = GenerateCascaderView(conditionDetails, m.ConditionDetailID)
                        }).ToList();
            }
        }

        /// <summary>
        /// 获取子条件
        /// </summary>
        /// <param name="conditionDetailList"></param>
        /// <param name="parentID"></param>
        /// <returns></returns>
        private static List<FormDetailConditionView> GenerateCascaderView(List<ConditionDetailInfo> conditionDetailList, string parentID)
        {
            var views = new List<FormDetailConditionView>();
            var detailList = conditionDetailList.Where(m => m.ParentID == parentID).ToList();
            foreach (var detail in detailList)
            {
                var view = new FormDetailConditionView
                {
                    ItemID = detail.ItemID.ToString(),
                    Condition = detail.Condition,
                    Value = detail.ConditionValue,
                    ConditionType = detail.ConditionType,
                    Children = GenerateCascaderView(conditionDetailList, detail.ConditionDetailID)
                };
                views.Add(view);
            }
            return views;
        }

        /// <summary>
        /// 根据组卷规则ID获取对应的组卷规则内容
        /// </summary>
        /// <param name="examinationConditionRecordID"></param>
        /// <returns></returns>
        public async Task<ExaminationConditionEditView> GetExaminationConditionEditView(string examinationConditionRecordID)
        {
            var conditionName = await _examinationConditionRecordRepository.GetConditionNameByIdAsync(examinationConditionRecordID);
            if (conditionName == null)
            {
                return null;
            }
            var conditionMains = await _conditionMainRepository.GetListBySourceIDs([examinationConditionRecordID], CONDITIONRECORD_SOURCETYPE);
            if (conditionMains.Count <= 0)
            {
                _logger.Error($"获取规则详情数据失败,表ConditionMain，SourceID={examinationConditionRecordID},SourceType={CONDITIONRECORD_SOURCETYPE}");
                return null;
            }
            ExaminationConditionEditView editView = await GetConditionDetailView(examinationConditionRecordID);
            editView.ConditionName = conditionName;
            return editView;
        }

        /// <summary>
        /// 来源-组卷规则表
        /// </summary>
        private readonly string CONDITIONRECORD_SOURCETYPE = "ExaminationConditionRecord";

        /// <summary>
        /// 保存组卷规则记录
        /// </summary>
        /// <param name="examineConditionSaveParamView">组卷规则记录保存参数</param>
        /// <param name="employeeID">当前会话人工号</param>
        /// <returns></returns>
        public async Task<string> SaveExaminationConditionRecord(ExaminationConditionSaveParamView examineConditionSaveParamView, string employeeID)
        {
            if (examineConditionSaveParamView.ConditionName == null || examineConditionSaveParamView.FilterConditions == null && examineConditionSaveParamView.FilterConditions.Count <= 0)
            {
                return null;
            }
            string sourceID;
            var examineConditionRecord = await _examinationConditionRecordRepository.GetRecordByIdAsync(examineConditionSaveParamView.ExaminationConditionRecordID);
            if (examineConditionRecord != null)
            {
                await DeleteOldCondtions(employeeID, examineConditionRecord.ExaminationConditionRecordID);
                examineConditionRecord.Score = examineConditionSaveParamView.Score;
                examineConditionRecord.ConditionName = examineConditionSaveParamView.ConditionName;
                examineConditionRecord.ConditionContent = "";
                examineConditionRecord.Modify(employeeID);
                sourceID = examineConditionRecord.ExaminationConditionRecordID;
            }
            else
            {
                // 新增组卷规则
                var addExaminationConditionRecordInfo = await GetNewConditionRecord();
                sourceID = addExaminationConditionRecordInfo.ExaminationConditionRecordID;
            }
            if (examineConditionSaveParamView.FilterConditions == null || examineConditionSaveParamView.FilterConditions.Count <= 0)
            {
                _logger.Error($"没有规则数据明细FilterConditions");
                return null;
            }
            foreach (var conditionItem in examineConditionSaveParamView.FilterConditions)
            {
                conditionItem.SourceType = CONDITIONRECORD_SOURCETYPE;
                conditionItem.SourceID = sourceID;
                conditionItem.AddFlag = true;
                conditionItem.ModifyEmployeeID = employeeID;
                conditionItem.Description = examineConditionSaveParamView.ConditionName;
                await ConditionService.HandleConditionData(conditionItem, false);
            }
            await _unitOfWork.SaveChangesAsync();
            return sourceID;
            // 获取新的组卷规则记录
            async Task<ExaminationConditionRecordInfo> GetNewConditionRecord()
            {
                var examinationConditionRecordInfo = new ExaminationConditionRecordInfo
                {
                    ExaminationConditionRecordID = Guid.NewGuid().ToString("N"),
                    ConditionName = examineConditionSaveParamView.ConditionName,
                    ConditionContent = "",
                    Score = examineConditionSaveParamView.Score,
                };
                examinationConditionRecordInfo.Add(employeeID).Modify(employeeID);
                await _unitOfWork.GetRepository<ExaminationConditionRecordInfo>().InsertAsync(examinationConditionRecordInfo);
                return examinationConditionRecordInfo;
            }
        }

        /// <summary>
        /// 更新时-删除旧规则
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="examinationConditionRecordID">组卷规则记录ID</param>
        /// <returns></returns>
        private async Task DeleteOldCondtions(string employeeID, string examinationConditionRecordID)
        {
            var conditionMainInfos = await _conditionMainRepository.GetListBySourceIDs([examinationConditionRecordID], CONDITIONRECORD_SOURCETYPE);
            if (conditionMainInfos.Count <= 0)
            {
                return;
            }
            conditionMainInfos.ForEach(m => m.Delete(employeeID));
            var conditionDetailInfos = await _conditionDetailRepository.GetListByMainIDs(conditionMainInfos.Select(m => m.ConditionMainID).ToList());
            if (conditionDetailInfos.Count <= 0)
            {
                return;
            }
            conditionMainInfos.ForEach(m => m.Delete(employeeID));
        }

        /// <summary>
        /// 删除组卷规则
        /// </summary>
        /// <param name="examinationConditionRecordID">组卷规则记录ID</param>
        /// <param name="employeeID">当前会话人工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteExaminationConditionRecord(string examinationConditionRecordID, string employeeID)
        {
            var condtionRecordInfo = await _examinationConditionRecordRepository.GetRecordByIdAsync(examinationConditionRecordID);
            if (condtionRecordInfo == null)
            {
                _logger.Error($"删除失败，找不到对应的组卷规则记录ID：examinationConditionRecordID={examinationConditionRecordID}");
                return false;
            }
            condtionRecordInfo.Delete(employeeID);

            var conditionMainInfos = await _conditionMainRepository.GetListBySourceIDs([condtionRecordInfo.ExaminationConditionRecordID], "ExaminationPaperComposition");
            var conditionMainIDs = new List<string>();
            conditionMainInfos.ForEach(m =>
            {
                conditionMainIDs.Add(m.ConditionMainID);
                m.Delete(employeeID);
            });
            var conditionDetailInfos = await _conditionDetailRepository.GetListByMainIDs(conditionMainIDs);
            conditionDetailInfos.ForEach(m => m.Delete(employeeID));
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
    }
}
