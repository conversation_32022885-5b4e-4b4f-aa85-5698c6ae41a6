﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Data.Repository
{
    public class ExaminationConditionRecordRepository : IExaminationConditionRecordRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ExaminationConditionRecordRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 获取所有考核组卷规则记录
        /// </summary>
        /// <returns></returns>
        public async Task<List<ExaminationConditionRecordView>> GetAllConditionRecordViews()
        {
            return await _nursingManagementDbContext.ExaminationConditionRecordInfos.Where(m => m.DeleteFlag != "*")
                .Select(m=>new ExaminationConditionRecordView
                {
                    ExaminationConditionRecordID = m.ExaminationConditionRecordID,
                    ConditionName = m.ConditionName,
                    ConditionContent = m.ConditionContent,
                    Score = m.Score,
                    AddEmployeeID = m.AddEmployeeID,
                    AddDateTime = m.AddDateTime,
                    ModifyEmployeeID = m.ModifyEmployeeID,
                    ModifyDateTime = m.ModifyDateTime,

                })
                .ToListAsync();
        }
        /// <summary>
        /// 根据主键ID获取组卷规则记录
        /// </summary>
        /// <param name="examinationConditionRecordID">组卷规则记录ID</param>
        /// <returns></returns>
        public async Task<ExaminationConditionRecordInfo> GetRecordByIdAsync(string examinationConditionRecordID)
        {
            return await _nursingManagementDbContext.ExaminationConditionRecordInfos.Where(m => m.ExaminationConditionRecordID == examinationConditionRecordID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取组卷条件名称
        /// </summary>
        /// <param name="conditionRecordIDs">组卷条件记录ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetConditionNameAsync(List<string> conditionRecordIDs)
        {
            return await _nursingManagementDbContext.ExaminationConditionRecordInfos
                .Where(m => conditionRecordIDs.Contains(m.ExaminationConditionRecordID) && m.DeleteFlag != "*")
                .ToDictionaryAsync(m => m.ExaminationConditionRecordID, n => n.ConditionName);
        }
        /// <summary>
        /// 获取规则名称
        /// </summary>
        /// <param name="examinationConditionRecordID"></param>
        /// <returns></returns>
        public async Task<string> GetConditionNameByIdAsync(string examinationConditionRecordID)
        {
            return await _nursingManagementDbContext.ExaminationConditionRecordInfos.Where(m => m.ExaminationConditionRecordID == examinationConditionRecordID && m.DeleteFlag != "*")
                .Select(m=>m.ConditionName).FirstOrDefaultAsync();
        }
    }
}
