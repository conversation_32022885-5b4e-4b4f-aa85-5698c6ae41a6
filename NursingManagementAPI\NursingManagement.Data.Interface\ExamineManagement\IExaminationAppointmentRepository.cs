﻿using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 考核预约仓储接口
    /// </summary>
    public interface IExaminationAppointmentRepository
    {
        /// <summary>
        /// 根据条件查询考核预约记录列表
        /// </summary>
        /// <param name="predicate">查询条件表达式</param>
        /// <param name="selectPredicate">结果集表达式</param>
        /// <returns>符合条件的考核预约记录列表</returns>
        Task<List<ExaminationAppointmentInfo>> GetListByConditionAsync(
            Expression<Func<ExaminationAppointmentInfo, bool>> predicate, Expression<Func<ExaminationAppointmentInfo, ExaminationAppointmentInfo>> selectPredicate = null);

        /// <summary>
        /// 根据主键获取考核预约记录
        /// </summary>
        /// <param name="examinationAppointmentId">主键ID</param>
        /// <returns>对应的考核预约记录（若存在）</returns>
        Task<ExaminationAppointmentInfo> GetByIDAsync(string examinationAppointmentId);

        /// <summary>
        /// 根据监考计划时间表ID获取预约记录列表
        /// </summary>
        /// <param name="examinerScheduleID">监考计划时间表ID</param>
        /// <returns>关联的考核预约记录列表</returns>
        Task<List<ExaminationAppointmentInfo>> GetByExaminerScheduleIDAsync(string examinerScheduleID);
    }
}