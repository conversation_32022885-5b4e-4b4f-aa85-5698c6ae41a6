﻿using System.Globalization;

namespace NursingManagement.Common
{
    public class DateHelper
    {
        private static readonly string[] Weekdays = { "日", "一", "二", "三", "四", "五", "六" };
        private static readonly string[] Months = { "一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月" };
        public static readonly int[] WeekendDays = { 0, 6 };

        /// <summary>
        /// 获取指定日期属于本年的第几周
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static int GetWeekOfYear(DateTime date)
        {
            var gc = new GregorianCalendar();
            int weekOfYear = gc.GetWeekOfYear(date, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
            return weekOfYear;
        }

        /// <summary>
        /// 取得某月的第一天
        /// </summary>
        /// <param name="datetime"></param>
        /// <returns></returns>
        public static DateTime GetFirstDayOfMonth(DateTime datetime)
        {
            return datetime.AddDays(1 - datetime.Day);
        }

        /// <summary>
        /// 取得某月的最后一天
        /// </summary>
        /// <param name="datetime">要取得月份最后一天的时间</param>
        /// <returns></returns>
        public static DateTime GetLastDayOfMonth(DateTime datetime)
        {
            return datetime.AddDays(1 - datetime.Day).AddMonths(1).AddDays(-1);
        }

        /// <summary>
        /// 取得指定日期是周几
        /// </summary>
        /// <param name="datetime"></param>
        /// <returns></returns>
        public static string GetWeekByDate(DateTime datetime)
        {
            var week = (int)datetime.DayOfWeek;
            return Weekdays[week];
        }

        /// <summary>
        /// 判断是否为周末
        /// </summary>
        /// <param name="datetime"></param>
        /// <returns></returns>
        public static bool IsWeekend(DateTime datetime)
        {
            var week = (int)datetime.DayOfWeek;
            return WeekendDays.Contains(week);
        }

        /// <summary>
        /// 根据时间获取当前月份中文名称
        /// </summary>
        /// <param name="datetime">日期时间</param>
        /// <returns></returns>
        public static string GetMonthByDate(DateTime datetime)
        {
            var month = (int)datetime.Month;
            return Months[month - 1];
        }

        /// <summary>
        /// 获取指定日期所属周的开始和结束日期
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static Tuple<DateTime, DateTime> GetWeekStartAndEnd(DateTime date)
        {
            // 获取当前周的第一天（默认为周日）
            // DayOfWeek.Sunday表示周日，cultureInfo可以根据需要调整首日为周一
            var cultureInfo = new CultureInfo("en-US");
            cultureInfo.DateTimeFormat.FirstDayOfWeek = DayOfWeek.Monday;

            int diff = date.DayOfWeek - cultureInfo.DateTimeFormat.FirstDayOfWeek;
            if (diff < 0)
                diff += 7;

            var startOfWeek = date.AddDays(-diff);
            var endOfWeek = startOfWeek.AddDays(6);
            return Tuple.Create(startOfWeek, endOfWeek);
        }

        /// <summary>
        /// 年月转化为年 月
        /// </summary>
        /// <param name="yearMonth"></param>
        /// <returns></returns>
        public static Tuple<int?, int?> GetYearAndMonth(string yearMonth)
        {
            if (string.IsNullOrEmpty(yearMonth))
            {
                return new Tuple<int?, int?>(null, null);
            }
            var stringArr = yearMonth.Trim().Split("-");
            if (stringArr.Length > 1)
            {
                return new Tuple<int?, int?>(Convert.ToInt32(stringArr[0]), Convert.ToInt32(stringArr[1]));
            }
            return new Tuple<int?, int?>(null, null);
        }
    }
}
