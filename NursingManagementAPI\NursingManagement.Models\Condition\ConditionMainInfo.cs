﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("ConditionMain")]
    public class ConditionMainInfo :MutiModifyInfo
    {
        /// <summary>
        /// 条件主记录ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ConditionMainID { get; set; }
        /// <summary>
        /// 来源ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }
        /// <summary>
        /// 来源类别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SourceType { get; set; }
        /// <summary>
        /// 条件描述
        /// </summary>
        [Column(TypeName = "varchar(3000)")]
        public string ConditionContent { get; set; }
        /// <summary>
        /// 条件表达式
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string ConditionExpression { get; set; }
        /// <summary>
        /// 条件名称
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Description { get; set; }
        /// <summary>
        /// 数据类型
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string DataType { get; set; }
        /// <summary>
        /// 数据类型值
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string DataTypeValue { get; set; }
        /// <summary>
        /// 分组类型
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string GroupType { get; set; }
        /// <summary>
        /// 分类下的明细值
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string GroupTypeValue { get; set; }
    }
}
