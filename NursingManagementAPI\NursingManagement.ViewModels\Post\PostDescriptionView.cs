﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class PostDescriptionView: PostDescriptionInfo
    {
        /// <summary>
        /// 全院岗位名称
        /// </summary>
        public string PostName { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyPerson { get; set; }
        /// <summary>
        /// 新增人
        /// </summary>
        public string AddPerson { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 签发人姓名
        /// </summary>
        public string SignerName { get; set; }
        /// <summary>
        /// 审核人姓名
        /// </summary>
        public string ApproverName { get; set; }
        /// <summary>
        /// 制定部门名称
        /// </summary>
        public string CreateDepartment { get; set; }
        public PostDescriptionView(PostDescriptionInfo pd)
        {
            CopyParentPropertiesToChild.SynchronizationProperties(pd, this);
        }
    }
}
