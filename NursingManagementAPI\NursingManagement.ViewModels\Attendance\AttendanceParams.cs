﻿using DocumentFormat.OpenXml.Wordprocessing;
using NursingManagement.Models;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 考勤参数
    /// </summary>
    public class AttendanceParams
    {
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 考勤年份
        /// </summary>
        public int AttendanceYear { get; set; }
        /// <summary>
        /// 考勤月份
        /// </summary>
        public int AttendanceMonth { get; set; }
        /// <summary>
        /// 应该出勤天数
        /// </summary>
        public decimal RequiredAttendanceDays { get; set; }
        /// <summary>
        /// 部门编码
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 考勤主表记录
        /// </summary>
        public string AttendanceRecordID { get; set; }
        /// <summary>
        /// 排班明细数据集合
        /// </summary>
        public List<ShiftSchedulingDetailInfo> SchedulingDetailList { get; set; }
        /// <summary>
        /// 部门岗位字典
        /// </summary>
        public List<PostSelectOptionsView> DepartmentPostList { get; set; }
        /// <summary>
        /// 午别字典
        /// </summary>
        public List<SelectOptionsView> NoonTypeList { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 当前时间
        /// </summary>
        public DateTime NowTime { get; set; }
        /// <summary>
        /// 人员编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 差勤天数(岗位考勤天数*排班天数-排班天数)
        /// </summary>
        public decimal AttendanceDifferenceDays { get; set; }
        /// <summary>
        /// 标记由考勤表生成
        /// </summary>
        public string SourceID { get; set; }
    }
}
