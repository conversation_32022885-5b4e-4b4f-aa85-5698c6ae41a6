﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 审批流程节点仓储接口
    /// </summary>
    public class ApproveProcessNodeRepository : IApproveProcessNodeRepository
    {
        private readonly NursingManagementDbContext _context;

        public ApproveProcessNodeRepository(NursingManagementDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 查询审批流程节点列表
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        public async Task<List<ApproveProcessNodeInfo>> GetApproveProcessNodeInfos(string approveProcessID)
        {
            return await _context.ApproveProcessNodeInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 查询审批流程节点列表
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        public async Task<List<ApproveProcessNode>> GetApproveProcessNodeViews(string approveProcessID)
        {
            return await _context.ApproveProcessNodeInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*")
                .Select(m => new ApproveProcessNode
                {
                    ApproveNodeID = m.ApproveNodeID,
                    ApproveNodeName = m.ApproveNodeName,
                    ApproveTimeLimit = m.ApproveTimeLimit,
                    NextNodeID = m.NextNodeID,
                    ApproveModel = m.ApproveModel,
                }).ToListAsync();
        }
    }
}
