﻿using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCFormRepository : IHierarchicalQCFormRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        private readonly IDepartmentListRepository _departmentListRepository;

        public HierarchicalQCFormRepository(
            NursingManagementDbContext nursingManagementDbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService,
            IDepartmentListRepository departmentListRepository)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
            _departmentListRepository = departmentListRepository;
        }
        /// <summary>
        /// 根据质控等级获取质控字典
        /// </summary>
        /// <param name="level"></param>
        /// <param name="formType"></param>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        public async Task<List<HierachicalQCFormOptionView>> GetQCFormByLevel(string level,string formType,List<int> departmentIDs)
        {
            var list = await GetCacheAsync() as List<HierarchicalQCFormInfo>;
            var departmentList = await _departmentListRepository.GetCacheAsync() as List<DepartmentListInfo>;
            if (!string.IsNullOrEmpty(level))
            {
                list= list.Where(m => m.HierarchicalQCFormLevel == level).ToList();
            }
            if (!string.IsNullOrEmpty(formType))
            {
                list = list.Where(m => m.FormType == formType).ToList();
            }
            if (departmentIDs != null&& departmentIDs.Count!=0)
            {
                list = list.Where(m => departmentIDs.Contains(m.AddDepartmentID)).ToList();
            }
            var returnView = list.OrderByDescending(m=>m.ModifyDateTime).Select(m => new HierachicalQCFormOptionView()
            {
                HierarchicalQCFormID = m.HierarchicalQCFormID,
                FormType = formType,
                FormName = m.FormType == "6" ? ((departmentList.Find(n => n.DepartmentID == m.AddDepartmentID)?.DepartmentContent??"") + m.FormName) : m.FormName,
                TemplateCode=m.TemplateCode
            }).ToList();

            return returnView;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            // UpdateCache();
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                return await _nursingManagementDbContext.HierarchicalQCFormInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.HierarchicalQCForm.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        /// <summary>
        /// 获取所有配置
        /// </summary>
        /// <returns></returns>
        public async Task<List<HierarchicalQCFormInfo>> GetAll()
        {
            return await GetCacheAsync() as List<HierarchicalQCFormInfo>;
        }
        /// <summary>
        /// 获取缓存的质控级别
        /// </summary>
        /// <param name="hierarchicalQCFormID">主键ID</param>
        /// <returns></returns>
        public async Task<string> GetCachedQCLevelByFormIDAsync(int hierarchicalQCFormID)
        {
            var list = await GetCacheAsync() as List<HierarchicalQCFormInfo>;
            return list.Find(m => m.HierarchicalQCFormID == hierarchicalQCFormID)?.HierarchicalQCFormLevel;
        }

        public async Task<int> GetNewID()
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            // 因缓存过滤了DeleteFlag标记，这里需要获取所有的数据，所以这里不能使用缓存
            var maxID = await _nursingManagementDbContext.HierarchicalQCFormInfos.Where(m => m.HospitalID == hospitalID && m.Language== language)
                              .OrderBy(m=>m.HierarchicalQCFormID).Select(m=>m.HierarchicalQCFormID).LastOrDefaultAsync();
            return maxID + 1;
        }
        public async Task<HierarchicalQCFormInfo> GetFormByID(int hierarchicalQCFormID)
        {
            var list = await GetCacheAsync() as List<HierarchicalQCFormInfo>;
            return list.Where(m => m.HierarchicalQCFormID== hierarchicalQCFormID).FirstOrDefault();
        }
    }
}