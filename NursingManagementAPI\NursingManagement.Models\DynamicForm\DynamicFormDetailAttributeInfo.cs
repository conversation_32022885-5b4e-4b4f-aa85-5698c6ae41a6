﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 动态表单明细项目属性表
    /// </summary>
    [Serializable]
    [Table("DynamicFormDetailAttribute")]
    public class DynamicFormDetailAttributeInfo : MutiModifyInfo
    {
        /// <summary>
        /// 动态表单明细属性ID
        /// </summary>
        public int DynamicFormDetailAttributeID { get; set; }
        /// <summary>
        /// 动态表单明细ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string DynamicFormDetailID { get; set; }
        /// <summary>
        /// 来源字典ComponentAttribute
        /// </summary>
        public int ComponentAttributeID { get; set; }
        /// <summary>
        /// 属性值
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string AttributeValue { get; set; }
    }
}
