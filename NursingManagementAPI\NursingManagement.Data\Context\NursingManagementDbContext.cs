﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NLog;
using NursingManagement.Common;
using NursingManagement.Models;
using System.Text;
using System.Text.RegularExpressions;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext : DbContext
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 用户登录表
        /// </summary>
        public DbSet<UserLoginInfo> UserLoginInfos { get; set; }
        /// <summary>
        /// 用户操作日志表
        /// </summary>
        public DbSet<OperationLogInfo> UserOperationLogInfos { get; set; }
        /// <summary>
        /// 常用文件清单表
        /// </summary>
        public DbSet<CommonFileInfo> CommonFileInfos { get; set; }
        /// <summary>
        /// 条件主表
        /// </summary>
        public DbSet<ConditionMainInfo> ConditionMainInfos { get; set; }
        /// <summary>
        /// 条件明细表
        /// </summary>
        public DbSet<ConditionDetailInfo> ConditionDetailInfos { get; set; }
        /// <summary>
        /// 规则属性主表
        /// </summary>
        public DbSet<RuleListInfo> RuleListInfos { get; set; }
        /// <summary>
        /// 规则属性明细表
        /// </summary>
        public DbSet<RuleDetailListInfo> RuleDetailListInfos { get; set; }
        /// <summary>
        /// 部门与数据类型对照表
        /// </summary>
        public DbSet<DeparmentToDataTypeInfo> DeparmentToDataTypeInfos { get; set; }

        public NursingManagementDbContext(DbContextOptions<NursingManagementDbContext> options)
          : base(options)
        {
            this.SaveChangesFailed += SaveChangesFailedHandler;
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            ArgumentNullException.ThrowIfNull(builder);
            builder.ApplyConfigurationsFromAssembly(typeof(NursingManagementDbContext).Assembly);
            // 全局
            builder.Entity<UserLoginInfo>().HasKey(t => new { t.HospitalID, t.EmployeeID });
            builder.Entity<OperationLogInfo>().HasKey(t => new { t.OperationLogID });
            // 字典表
            builder.Entity<AppConfigSettingInfo>().HasKey(t => (new { t.HospitalID, t.SettingType, t.SettingCode }));
            builder.Entity<HospitalListInfo>().HasKey(t => (new { t.HospitalID }));
            builder.Entity<AdministrationDictionaryInfo>().HasKey(t => new { t.AdministrationDictionaryID, t.HospitalID, t.Language });
            builder.Entity<DepartmentListInfo>().HasKey(t => new { t.DepartmentID, t.HospitalID, t.Language });
            builder.Entity<CapabilityLevelInfo>().HasKey(t => new { t.CapabilityLevelID, t.HospitalID, t.Language });
            builder.Entity<DepartmentToJobInfo>().HasKey(t => new { t.JobCode, t.HospitalID, t.Language, t.DepartmentID });
            builder.Entity<EmployeeToJobInfo>().HasKey(t => new { t.EmployeeID, t.HospitalID, t.JobCode, t.DepartmentID });
            builder.Entity<PerpetualCalendarInfo>().HasKey(t => new { t.Date, t.HospitalID });
            builder.Entity<ComponentListInfo>().HasKey(t => new { t.ComponentListID });
            builder.Entity<ComponentAttributeInfo>().HasKey(t => new { t.ComponentAttributeID });
            // 动态表单相关
            builder.Entity<DynamicFormRecordInfo>().HasKey(t => new { t.DynamicFormRecordID });
            builder.Entity<DynamicFormDetailInfo>().HasKey(t => new { t.DynamicFormDetailID });
            builder.Entity<DynamicFormDetailAttributeInfo>().HasKey(t => new { t.DynamicFormDetailAttributeID });
            builder.Entity<DynamicFormDetailConditionInfo>().HasKey(t => new { t.DynamicFormDetailConditionID });
            // 权限相关
            builder.Entity<EmployeeRoleInfo>().HasKey(t => new { t.HospitalID, t.EmployeeID, t.AuthorityRoleID });
            builder.Entity<AuthorityRoleInfo>().HasKey(t => new { t.AuthorityRoleID, t.HospitalID, t.Language });
            builder.Entity<AuthorityListInfo>().HasKey(t => new { t.AuthorityID, t.MenuListID, t.Language });
            builder.Entity<AuthorityRoleListInfo>().HasKey(t => new { t.AuthorityRoleID, t.AuthorityID });
            builder.Entity<MenuListInfo>().HasKey(t => new { t.MenuListID, t.HospitalID, t.Language, t.MenuType });
            builder.Entity<RouterListInfo>().HasKey(t => new { t.RouterListID, t.ClientType });
            builder.Entity<AuthorityComponentListInfo>().HasKey(t => new { t.HospitalID, t.AuthorityRoleID, t.ComponentListID, t.RouterListID, t.ClientType });
            //三级质控
            builder.Entity<HierarchicalQCFormInfo>().HasKey(t => new { t.HierarchicalQCFormID, t.HospitalID, t.Language });
            builder.Entity<HierarchicalQCSubjectInfo>().HasKey(t => new { t.HierarchicalQCSubjectID });
            builder.Entity<HierarchicalQCAssessListInfo>().HasKey(t => new { t.HierarchicalQCAssessListID, t.HospitalID, t.Language });
            builder.Entity<HierarchicalQCRemarkInfo>().HasKey(t => new { t.HierarchicalQCRemarkID });
            builder.Entity<HierarchicalQCRecordInfo>().HasKey(t => new { t.HierarchicalQCRecordID });
            builder.Entity<HierarchicalQCMainInfo>().HasKey(t => new { t.HierarchicalQCMainID });
            builder.Entity<HierarchicalQCObjectInfo>().HasKey(t => new { t.HierarchicalQCObjectID });
            //人事档案
            builder.Entity<EmployeePersonalDataInfo>().HasKey(t => new { t.EmployeeID, t.HospitalID });
            builder.Entity<EmployeeStaffDataInfo>().HasKey(t => new { t.EmployeeID, t.HospitalID });
            builder.Entity<EmployeeSecondmentRecordInfo>().HasKey(t => new { t.EmployeeSecondmentRecordID });
            builder.Entity<EmployeeTeachingRelationInfo>().HasKey(t => new { t.EmployeeTeachingRelationID });
            builder.Entity<EmployeeStrengthInfo>().HasKey(t => new { t.EmployeeStrengthID });
            //岗位管理
            builder.Entity<DepartmentPostInfo>().HasKey(t => new { t.DepartmentPostID, t.HospitalID, t.Language });
            builder.Entity<PostDescriptionInfo>().HasKey(t => new { t.PostDescriptionID });
            builder.Entity<PostInfo>().HasKey(t => new { t.PostID, t.HospitalID, t.Language });
            builder.Entity<DepartmentPostSettingInfo>().HasKey(t => new { t.PostID, t.DepartmentID, t.HospitalID, t.Type });
            builder.Entity<DepartmentPostToCapabilityLevelInfo>().HasKey(t => new { t.DepartmentPostToCapabilityLevelID });
            builder.Entity<DepartmentPostWorkingTimeInfo>().HasKey(t => new { t.DepartmentPostWorkingTimeID });
            // 年度计划
            builder.Entity<AnnualGoalListInfo>().HasKey(t => new { t.AnnualGoalID, t.HospitalID, t.Language });
            builder.Entity<AnnualIndicatorListInfo>().HasKey(t => new { t.AnnualIndicatorID, t.HospitalID, t.Language });
            builder.Entity<AnnualPlanTypeListInfo>().HasKey(t => new { t.AnnualPlanTypeID, t.HospitalID, t.Language });
            builder.Entity<InterventionListInfo>().HasKey(t => new { t.InterventionID, t.HospitalID, t.Language });

            //排班相关
            builder.Entity<AdjustScheduleRecordInfo>().HasKey(t => new { t.AdjustScheduleRecordID });
            builder.Entity<SchedulingRequestRecordInfo>().HasKey(t => new { t.SchedulingRequestRecordID });
            builder.Entity<ShiftSchedulingRecordInfo>().HasKey(t => new { t.ShiftSchedulingRecordID });
            builder.Entity<ShiftSchedulingDetailInfo>().HasKey(t => new { t.ShiftSchedulingDetailID });
            builder.Entity<ShiftSchedulingDetailMarkInfo>().HasKey(t => new { t.ShiftSchedulingDetailMarkID });
            builder.Entity<ShiftSchedulingEmployeeSortInfo>().HasKey(t => new { t.ShiftSchedulingEmployeeSortID });
            builder.Entity<ShiftSchedulingRuleInfo>().HasKey(t => t.ShiftSchedulingRuleID);
            builder.Entity<SchedulingTemplateRecordInfo>().HasKey(t => t.SchedulingTemplateRecordID);
            builder.Entity<SchedulingTemplateDetailInfo>().HasKey(t => t.SchedulingTemplateDetailID);
            builder.Entity<SchedulingTemplateDetailMarkInfo>().HasKey(t => t.SchedulingTemplateDetailMarkID);
            //常用文件清单表
            builder.Entity<CommonFileInfo>().HasKey(t => new { t.CommonFileID });
            //培训课程字典
            builder.Entity<CourseSettingInfo>().HasKey(t => t.CourseSettingID);
            builder.Entity<SignUpRecordInfo>().HasKey(t => t.SignUpRecordID);
            builder.Entity<RuleDetailListInfo>().HasKey(t => new { t.RuleDetailListID, t.Language });
            builder.Entity<RuleListInfo>().HasKey(t => new { t.RuleListID, t.HospitalID, t.Language });
            builder.Entity<RuleListInfo>().HasKey(t => new { t.RuleListID, t.HospitalID, t.Language });
            // 季度计划
            builder.Entity<QuarterPlanDetailInfo>().Property(t => t.PrincipalIDs)
                .HasConversion(new ValueConverter<string[], string>(
                v => ListToJson.ToJson(v), v => ListToJson.ToList<string[]>(v)));
            // 消息发布的部门
            builder.Entity<MessageToDepartmentInfo>().HasKey(t => new { t.MessageRecordID, t.HospitalID, t.DepartmentID });
            // 题目明细 建立题库与题目明细导航关系
            builder.Entity<ExaminationQuestionDetailInfo>().HasOne(t => t.ExaminationQuestion).WithMany(t => t.ExaminationQuestionDetails).HasForeignKey(t => t.ExaminationQuestionID);
            // 试卷记录 添加定题题目主键集合转换
            builder.Entity<ExaminationPaperMainInfo>().Property(t => t.FixedQuestionList).HasConversion(new ValueConverter<List<string>, string>(v => ListToJson.ToJson(v), v => ListToJson.ToList<List<string>>(v)));
            base.OnModelCreating(builder);
        }

        /// <summary>
        /// 监听保存失败，保留截断字符串报错日志
        /// </summary>
        /// <param name="sender">当前上下文</param>
        /// <param name="e">保存失败的相关参数</param>
        private void SaveChangesFailedHandler(object sender, SaveChangesFailedEventArgs e)
        {
            var context = (DbContext)sender;
            var ex = e.Exception;
            if (ex.HResult != -2146233088)
            {
                return;
            }
            var entries = context.ChangeTracker.Entries()
                .Where(x => x.State is EntityState.Added or EntityState.Modified)
                .ToList();
            // 打印被跟踪的实体
            foreach (var entry in entries)
            {
                // 打印每个属性的值
                foreach (var prop in entry.Properties)
                {
                    // 检查字符串长度是否超过数据库定义的最大长度
                    if (prop.Metadata.ClrType == typeof(string) && prop.CurrentValue is string value)
                    {
                        if (string.IsNullOrEmpty(value))
                        {
                            continue;
                        }
                        var typeName = prop.Metadata.GetColumnType(); // 获取字段最大长度
                        var maxLength = GetMaxLengthFromTypeName(typeName);
                        // 字节长度
                        var byteLength = Encoding.UTF8.GetByteCount(value);
                        if (maxLength.HasValue && byteLength > maxLength.Value)
                        {
                            _logger.Error($"截断字符串：表【{entry.Entity.GetType().Name}】字段【{prop.Metadata.Name}】值【{value}】,长度【{byteLength}】-【{maxLength}】");
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 获取字符串的字节长度
        /// </summary>
        /// <param name="typeName"></param>
        /// <returns></returns>
        public static int? GetMaxLengthFromTypeName(string typeName)
        {
            if (string.IsNullOrEmpty(typeName))
            {
                return null;
            }
            var match = Regex.Match(typeName, @"varchar\((\d+)\)", RegexOptions.IgnoreCase);
            if (match.Success && match.Groups.Count > 1)
            {
                if (int.TryParse(match.Groups[1].Value, out var length))
                {
                    return length;
                }
            }
            return null;
        }
    }
}
