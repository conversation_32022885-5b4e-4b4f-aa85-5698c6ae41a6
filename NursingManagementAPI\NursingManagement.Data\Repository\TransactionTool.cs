﻿using NLog;
using NursingManagement.Data.Context;

namespace NursingManagement.Data.Repository
{
    public class TransactionTool
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public TransactionTool(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        public async Task<bool> CommitWithTrans(Func<Task> task)
        {
            await using var transaction = await _nursingManagementDbContext.Database.BeginTransactionAsync();
            try
            {
                await task();
                await _nursingManagementDbContext.SaveChangesAsync();
                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.Error(ex.ToString());
                return false;
            }
        }
    }
}
