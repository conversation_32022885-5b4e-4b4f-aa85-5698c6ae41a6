﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface ITrainingRecordService
    {
        /// <summary>
        ///  查询培训记录数据
        /// </summary>
        /// <param name="departmentIDs"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<TrainingRecordView>> GetTrainingRecord(List<int> departmentIDs, string hospitalID);
        /// <summary>
        /// 删除培训记录
        /// </summary>
        /// <param name="trainingRecordID"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteTrainingRecord(string trainingRecordID, string modifyEmployeeID);
        /// <summary>
        /// 保存培训记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveTrainingRecord(TrainingRecordView view);
        /// <summary>
        /// 获取课程级联下拉框数据
        /// </summary>
        /// <returns></returns>
        Task<List<CascaderView<string>>> GetCourseSetting();
        /// <summary>
        /// 保存培训问卷模版
        /// </summary>
        /// <param name="fromView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<string> SaveEvaluationForm(FormTemplateView fromView, string employeeID);
        /// <summary>
        /// 保存培训评价记录
        /// </summary>
        /// <param name="mainAndDetailView"></param>
        /// <returns></returns>
        Task<bool> SaveEvaluationData(TrainingEvaluationMainAndDetailView mainAndDetailView);
        /// <summary>
        /// 获取培训评价模板数据
        /// </summary>
        /// <param name="evaluationMainID">培训评价主表ID</param>
        /// <param name="trainingRecordID">培训记录ID</param>
        /// <param name="evaluationType">评价类别</param>
        /// <returns></returns>
        Task<FormTemplateView> GetEvaluationFormView(string evaluationMainID, string trainingRecordID, string evaluationType);
    }
}
