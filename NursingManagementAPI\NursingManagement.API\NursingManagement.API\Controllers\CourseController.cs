﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 课程配置控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/Course")]
    [EnableCors("any")]
    public class CourseController : Controller
    {
        private readonly ISessionService _session;
        private readonly ICourseSettingService _courseSettingService;
        private readonly ITrainingRecordService _trainingRecordService;
        /// <summary>
        /// 课程配置控制器构造函数
        /// </summary>
        /// <param name="session"></param>
        /// <param name="courseSettingService"></param>
        /// <param name="trainingRecordService"></param>
        public CourseController(
            ISessionService session,
            ICourseSettingService courseSettingService,
            ITrainingRecordService trainingRecordService)
        {
            _session = session;
            _courseSettingService = courseSettingService;
            _trainingRecordService = trainingRecordService;
        }
        /// <summary>
        /// 根据课程分类获取课程配置
        /// </summary>
        /// <param name="courseTypeID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCourseSettingList")]
        public async Task<IActionResult> GetCourseSettingList(string courseTypeID)
        {

            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _courseSettingService.GetCourseSettingListAsync(courseTypeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存课程配置
        /// </summary>
        /// <param name="courseSetting">课程配置信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveCourseSetting")]
        public async Task<IActionResult> SaveCourseSettingView([FromForm] CourseSettingView courseSetting)
        {

            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _courseSettingService.SaveCourseSettingAsync(courseSetting, session.EmployeeID, session.HospitalID, session.DepartmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除课程配置
        /// </summary>
        /// <param name="courseSettingID">课程配置ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteCourseSetting")]
        public async Task<IActionResult> DeleteCourseSetting(string courseSettingID)
        {

            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _courseSettingService.DeleteCourseSettingAsync(courseSettingID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据课程名称查看对应的课程信息
        /// </summary>
        /// <param name="courseName">课程名称查询关键字</param>
        /// <param name="courseTypeID">课程类别ID，空串表示从所有课程中检索</param>
        /// <returns></returns>
        [HttpGet]
        [Route("SearchCourseSetting")]
        public async Task<IActionResult> SearchCourseSetting(string courseName, string courseTypeID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _courseSettingService.SearchCourseSettingAsync(courseName, courseTypeID);
            return result.ToJson();
        }
        /// <summary>
        /// 查询培训记录数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetTrainingRecord")]
        public async Task<IActionResult> GetTrainingRecord([FromBody] TrainingRecordView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingRecordService.GetTrainingRecord(view.DepartmentIDs, session.HospitalID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除培训记录
        /// </summary>
        /// <param name="trainingRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteTrainingRecord")]
        public async Task<IActionResult> DeleteTrainingRecord(string trainingRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingRecordService.DeleteTrainingRecord(trainingRecordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存培训记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveTrainingRecord")]
        public async Task<IActionResult> SaveTrainingRecord([FromForm] TrainingRecordView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.ModifyEmployeeID = !string.IsNullOrEmpty(view.ModifyEmployeeID) ? view.ModifyEmployeeID : session.EmployeeID;
            view.HospitalID = !string.IsNullOrEmpty(view.HospitalID) ? view.HospitalID : session.HospitalID;
            result.Data = await _trainingRecordService.SaveTrainingRecord(view);
            return result.ToJson();
        }
        /// <summary>
        /// 获取课程级联下拉框数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCourseSetting")]
        public async Task<IActionResult> GetCourseSetting()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingRecordService.GetCourseSetting();
            return result.ToJson();
        }
        /// <summary>
        /// 保存培训问卷模版
        /// </summary>
        /// <param name="fromView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEvaluationForm")]
        public async Task<IActionResult> SaveEvaluationForm([FromBody] FormTemplateView fromView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingRecordService.SaveEvaluationForm(fromView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存培训评价记录
        /// </summary>
        /// <param name="mainAndDetailView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEvaluationData")]
        public async Task<IActionResult> SaveEvaluationData([FromBody] TrainingEvaluationMainAndDetailView mainAndDetailView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            mainAndDetailView.ModifyEmployeeID = session.EmployeeID;
            result.Data = await _trainingRecordService.SaveEvaluationData(mainAndDetailView);
            return result.ToJson();
        }
        /// <summary>
        /// 获取培训评价模板数据
        /// </summary>
        /// <param name="evaluationMainID">培训评价主表ID</param>
        /// <param name="trainingRecordID">培训记录ID</param>
        /// <param name="evaluationType">评价类别</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEvaluationFormView")]
        public async Task<IActionResult> GetEvaluationFormView(string evaluationMainID, string trainingRecordID, string evaluationType)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _trainingRecordService.GetEvaluationFormView(evaluationMainID, trainingRecordID, evaluationType);
            return result.ToJson();
        }
    }
}
