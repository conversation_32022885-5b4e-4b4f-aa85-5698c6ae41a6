﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class FormExtendByHierarchicalQC : FormExtend
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IHierarchicalQCAssessListRepository _hierarchicalQCAssessListRepository;
        private readonly IHierarchicalQCRemarkRepository _hierarchicalQCRemarkRepository;
        public FormExtendByHierarchicalQC(
            IUnitOfWork unitOfWork
            , SessionCommonServer sessionCommonServer
            , IHierarchicalQCAssessListRepository hierarchicalQCAssessListRepository
            , IHierarchicalQCRemarkRepository hierarchicalQCRemarkRepository
        )
        {
            _unitOfWork = unitOfWork;
            _sessionCommonServer = sessionCommonServer;
            _hierarchicalQCAssessListRepository = hierarchicalQCAssessListRepository;
            _hierarchicalQCRemarkRepository = hierarchicalQCRemarkRepository;
        }
        /// <summary>
        /// 根据项目label获取项目ID
        /// </summary>
        /// <param name="label"></param>
        /// <returns></returns>
        public override async Task<Tuple<string, Func<Task>>> GetItemIDByLabel(string label)
        {
            var itemID = await _hierarchicalQCAssessListRepository.GetIDByContentName(label?.ToString());
            // 如果找不到就新增一条
            if (itemID != null)
            {
                return Tuple.Create(itemID.Value.ToString(), null as Func<Task>);
            }
            itemID = await _hierarchicalQCAssessListRepository.GetNewID();
            var session = _sessionCommonServer.GetSessionByCache();
            var hierarchicalQCAssessList = new HierarchicalQCAssessListInfo()
            {
                HierarchicalQCAssessListID = itemID.Value,
                HospitalID = session.HospitalID,
                Language = session.Language,
                Type = "1",
                ContentName = label,
                AddEmployeeID = "System",
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = "System",
                ModifyDateTime = DateTime.Now,
                DeleteFlag = ""
            };
            await _unitOfWork.GetRepository<HierarchicalQCAssessListInfo>().InsertAsync(hierarchicalQCAssessList);
            return Tuple.Create(itemID.ToString(), () => _hierarchicalQCAssessListRepository.UpdateCache());
        }

        public override async Task<List<KeyValueString>> GetGradeRemarkOptions(List<string> itemIDs)
        {
            var returnData = new List<KeyValueString>();
            if (itemIDs == null || itemIDs.Count <= 0)
            {
                return returnData;
            }
            var assessListIDs = itemIDs.Select(m=>int.TryParse(m,out int assessListID)? assessListID: 0).ToList();
            var list = await _hierarchicalQCRemarkRepository.GetQCRemarkViewByAssessListIDsAsync(assessListIDs);
            return list.Select(m => new KeyValueString()
            {
                ID = m.HQCAssessListID,
                Key = m.HQCRemarkID,
                Value = m.Remark
            }).ToList();
        }
    }
}
