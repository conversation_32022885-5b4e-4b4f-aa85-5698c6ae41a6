﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 个人档案信息头部呈现
    /// </summary>
    public class PersonalFileHeader
    {
        /// <summary>
        /// 人员名称
        /// </summary>
        public string EmployeeName { get;set; }
        /// <summary>
        /// 档案编号
        /// </summary>
        public string FileID { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 层级
        /// </summary>
        public string CapabilityLevelName { get; set; }

        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? EntryDate { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }
        /// <summary>
        /// 职称专业等级名称
        /// </summary>
        public string ProfessionalLevelName { get; set; }
    }
}
