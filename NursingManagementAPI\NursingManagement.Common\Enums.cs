﻿using System.ComponentModel;

namespace NursingManagement.Common
{
    public class Enums
    {
        /// <summary>
        /// 性别
        /// </summary>
        public enum Gender
        {
            /// <summary>
            /// 男性
            /// </summary>
            [Description("男")]
            Male = 1,
            /// <summary>
            /// 女性
            /// </summary>
            [Description("女")]
            Female = 2
        }
        /// <summary>
        /// 客户端类别
        /// </summary>
        public enum ClientType
        {
            /// <summary>
            /// PC端
            /// </summary>
            [Description("PC端")]
            PC = 1,
            /// <summary>
            /// 移动端
            /// </summary>
            [Description("移动端")]
            Mobile = 2
        }
        /// <summary>
        /// 菜单快捷类别
        /// </summary>
        public enum ShutCutType
        {
            Common = 0,
            /// <summary>
            /// 用户快捷
            /// </summary>
            [Description("用户快捷")]
            User = 1,
            /// <summary>
            /// 病人快捷
            /// </summary>
            [Description("病人快捷")]
            Patient = 2
        }
        /// <summary>
        /// 审批方式
        /// </summary>
        public enum ApproveModelType
        {
            /// <summary>
            /// 顺序签
            /// </summary>
            [Description("顺序签")]
            Sequential = 1,
            /// <summary>
            /// 会签
            /// </summary>
            [Description("会签")]
            Parallel = 2,
            /// <summary>
            /// 或签
            /// </summary>
            [Description("或签")]
            AnyOne = 3
        }
        /// <summary>
        /// 文档类型
        /// </summary>
        public class FileType
        {
            /// <summary>
            /// 首次评估 （入院评估）
            /// </summary>
            public const string FirstAssess = "入院评估单";
            /// <summary>
            /// 历次评估
            /// </summary>
            public const string HistoryAssess = "历次评估单";
            /// <summary>
            /// 护理问题
            /// </summary>
            public const string NursingProblem = "护理问题单";
            /// <summary>
            /// 护理计划
            /// </summary>
            public const string NursingPlan = "护理计划单";
            /// <summary>
            /// 风险评估
            /// </summary>
            public const string Risck = "风险评估单";
            /// <summary>
            /// 交班列表
            /// </summary>
            public const string HandoverList = "交班列表单";
            /// <summary>
            /// 交班明细
            /// </summary>
            public const string HandoverDetail = "交班明细单";
            public const string NursingRecords = "护理记录单";
            /// <summary>
            /// 输血单
            /// </summary>
            public const string Blood = "输血单";
        }
        /// <summary>
        /// 记录码
        /// </summary>
        public enum RecordsCode
        {
            /// <summary>
            /// 入院评估码
            /// </summary>
            AdmissionAssess,
            /// <summary>
            /// 历次评估
            /// </summary>
            PhysicalAssessment
        }

        public class RiskTableCode
        {
            public const string ADL = "ActivitiesDailyLiving";
        }

        /// <summary>
        /// 分级质控类型
        /// </summary>
        public enum QCObjectType
        {
            /// <summary>
            /// 部门
            /// </summary>
            Department = 0,
            /// <summary>
            /// 人员
            /// </summary>
            Employee = 1,
        }

        /// <summary>
        /// 表示审批状态的枚举。
        /// </summary>
        public enum ApprovalStatus
        {
            /// <summary>
            /// 待审批。
            /// </summary>
            [Description("待审批")]
            Waiting = 0,
            /// <summary>
            /// 表示审批正在进行中。
            /// </summary>
            [Description("正在进行中")]
            InProgress = 1,

            /// <summary>
            /// 表示审批已通过。
            /// </summary>
            [Description("同意")]
            Completed = 2,

            /// <summary>
            /// 表示审批已驳回。
            /// </summary>
            [Description("拒绝")]
            Rejected = 3,
            /// <summary>
            /// 撤销
            /// </summary>
            [Description("撤销")]
            Revoke = 4,
        }
        /// <summary>
        /// 年度计划状态
        /// </summary>
        public enum AnnualPlanStatus
        {
            /// <summary>
            /// 未发布
            /// </summary>
            NoPublish,
            /// <summary>
            /// 已发布
            /// </summary>
            Published
        }
    }
}