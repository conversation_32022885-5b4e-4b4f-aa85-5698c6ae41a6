﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IRemainingRestDaysRepository
    {       
        /// <summary>
        /// 根据部门ID获取剩余休息天数集合
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<List<RemainingRestDaysInfo>> GetAllRemainingRestDaysByDepartmentID(int departmentID);
        /// <summary>
        /// 根据部门ID、年份获取剩余休息天数集合
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        Task<List<RemainingRestDaysView>> GetRemainingRestDaysByDepartmentID(int departmentID, int year);
        /// <summary>
        /// 根据部门ID、年份、月份获取人员剩余休息天数
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        Task<Dictionary<string,decimal?>> GetEmployeeRestDaysDict(int departmentID, int year, int month);
    }
}
