﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 监考安排人员表
    /// </summary>
    [Table("ExaminerScheduleEmployee")]
    public class ExaminerScheduleEmployeeInfo : MutiModifyInfo
    {
        /// <summary>
        /// 监考安排人员ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminerScheduleEmployeeID { get; set; }
        /// <summary>
        /// 监考安排ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminerScheduleID { get; set; }
        /// <summary>
        /// 监考人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}