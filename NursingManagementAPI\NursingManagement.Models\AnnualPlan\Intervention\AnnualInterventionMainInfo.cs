﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划-计划制定主表
    /// </summary>
    [Serializable]
    [Table("AnnualInterventionMain")]
    public class AnnualInterventionMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 制定序号
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanInterventionMainID { get; set; }
        /// <summary>
        /// 计划主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 计划目标GUID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 计划项目明细表GUID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ProjectDetailID { get; set; }
        /// <summary>
        /// 执行项目ID，来源执行项目字典表
        /// </summary>
        public int InterventionID { get; set; }
        /// <summary>
        /// 自定义的执行项目内容
        /// </summary>
        [Column(TypeName = "nvarchar(400)")]
        public string LocalShowName { get; set; }
        /// <summary>
        /// 负责人分组名称
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string PrincipalGroupName { get; set; }
    }
}
