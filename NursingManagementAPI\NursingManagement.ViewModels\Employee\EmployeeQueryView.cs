﻿namespace NursingManagement.ViewModels
{
    public class EmployeeQueryView
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public int[] DepartmentIDs { get; set; }

        /// <summary>
        /// 层级ID
        /// </summary>
        public int[] CapabilityIDs { get; set; }

        /// <summary>
        /// 职称ID
        /// </summary>
        public string[] PositionIDs { get; set; }

        /// <summary>
        /// 学历ID
        /// </summary>
        public string[] EducationalIDs { get; set; }

        /// <summary>
        /// 入职日期
        /// </summary>
        public string[] EntryDate { get; set; }

        /// <summary>
        /// 人员姓名
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 护士标记，为true时只取护士
        /// </summary>
        public bool? NurseFlag { get; set; }
        /// <summary>
        /// 申请离职日期
        /// </summary>
        public string[] ResignationApplyDate { get; set; }
        /// <summary>
        /// 离职日期
        /// </summary>
        public string[] ResignationDate { get; set; }
    }
}
