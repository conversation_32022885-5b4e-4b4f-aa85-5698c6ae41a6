﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  考勤表   
    /// </summary>
    [Table("AttendanceRecord")]
    public class AttendanceRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考勤主表记录号，主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string AttendanceRecordID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// HR员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 部门编码，DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 考勤年份
        /// </summary>
        public int AttendanceYear { get; set; }
        /// <summary>
        /// 考勤月份
        /// </summary>
        public int AttendanceMonth { get; set; }
        /// <summary>
        /// 应该出勤天数
        /// </summary>
        public decimal RequiredAttendanceDays { get; set; }
        /// <summary>
        /// 实际出勤天数
        /// </summary>
        public decimal ActualAttendanceDays { get; set; }
        /// <summary>
        /// 休假天数
        /// </summary>
        public decimal RestDays { get; set; }
        /// <summary>
        /// 小夜天数
        /// </summary>
        public decimal? EveningShftDays { get; set; }
        /// <summary>
        /// 大夜天数
        /// </summary>
        public decimal? NightShiftDays { get; set; }
        /// <summary>
        /// 通夜天数
        /// </summary>
        public decimal? WholeNightShiftDays { get; set; }
        /// <summary>
        /// 来源，关联排班记录
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }
        /// <summary>
        /// 考勤备注
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string Remark { get; set; }
        /// <summary>
        /// 数据同步日期时间
        /// </summary>
        public DateTime? DataPumpDateTime { get; set; }
        /// <summary>
        /// 数据同步标记
        /// </summary>
        public bool? DataPumpFlag { get; set; }
    }
}