﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using Pipelines.Sockets.Unofficial.Arenas;

namespace NursingManagement.Services
{
    public class QuestionBankService : IQuestionBankService
    {
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IQuestionBankRepository _questionBankRepository;
        private readonly ICourseSettingRepository _courseSettingRepository;
        private readonly IExaminationQuestionRepository _examinationQuestionRepository;
        private readonly IExaminationQuestionDetailRepository _examinationQuestionDetailRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IExamineService _examineService;
        private readonly IExaminationPaperService _examinationPaperService;
        private readonly IRuleListRepository _ruleListRepository;

        public QuestionBankService(
            IUnitOfWork unitOfWork
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IQuestionBankRepository questionBankRepository
            , ICourseSettingRepository courseSettingRepository
            , IExaminationQuestionRepository examinationQuestionRepository
            , IExaminationQuestionDetailRepository examinationQuestionDetailRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , IDepartmentListRepository departmentListRepository
            , ISettingDictionaryService settingDictionaryService
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IExamineService examineService
            , IExaminationPaperService examinationPaperService
            , IRuleListRepository ruleListRepository
        )
        {
            _unitOfWork = unitOfWork;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _questionBankRepository = questionBankRepository;
            _courseSettingRepository = courseSettingRepository;
            _examinationQuestionRepository = examinationQuestionRepository;
            _examinationQuestionDetailRepository = examinationQuestionDetailRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _departmentListRepository = departmentListRepository;
            _settingDictionaryService = settingDictionaryService;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _examineService = examineService;
            _examinationPaperService = examinationPaperService;
            _ruleListRepository = ruleListRepository;
        }

        #region 题库
        public async Task<List<QuestionBankView>> GetQuestionBankList(string employeeID)
        {
            var viewList = new List<QuestionBankView>();
            var departmentSwitchList = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeID, true);
            var departmentIDs = departmentSwitchList.Select(m => m.DepartmentID).ToList();
            var questionBankList = await _questionBankRepository.GetQuestionBankList(departmentIDs);
            if (questionBankList.Count <= 0)
            {
                return viewList;
            }
            var bankTypeSetting = await _settingDictionaryService.GetSettingDictionaryMaintain("ExaminationManagement", "ExamineClassification");
            var organizationalDeptParam = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "OrganizationalDepartmentCode",
            };
            var organizationalDeptSetting = await _settingDictionaryRepository.GetSettingDictionary(organizationalDeptParam);
            var courseList = await _courseSettingRepository.GetCourseListAsync(questionBankList.Select(m => m.SourceID).ToList());
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(questionBankList.Select(m => m.ModifyEmployeeID).ToList());
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            foreach (var questionBankItem in questionBankList)
            {
                var view = new QuestionBankView()
                {
                    QuestionBankID = questionBankItem.QuestionBankID,
                    Content = questionBankItem.Content,
                    QuestionBankTypeName = FindParentNodes(bankTypeSetting, questionBankItem.QuestionBankType.ToString()),
                    QuestionBankType = questionBankItem.QuestionBankType.ToString(),
                    SourceID = questionBankItem.SourceID,
                    TrainingCourse = courseList.Find(m => m.CourseSettingID == questionBankItem.SourceID)?.CourseName,
                    Year = questionBankItem.Year,
                    ModifyEmployeeID = questionBankItem.ModifyEmployeeID,
                    ModifyDateTime = questionBankItem.ModifyDateTime,
                    HospitalID = questionBankItem.HospitalID,
                    ModifyEmployee = employeeList.Find(m => m.EmployeeID == questionBankItem.ModifyEmployeeID)?.EmployeeName,
                    IsPractical = questionBankItem.IsPractical,
                    OrganizationalDepartmentCode = questionBankItem.OrganizationalDepartmentCode,
                    OrganizationalDepartment = organizationalDeptSetting.Find(m => m.SettingValue == questionBankItem.OrganizationalDepartmentCode)?.Description,
                    ParentID = questionBankItem.ParentID,
                    DepartmentID = questionBankItem.DepartmentID,
                    DepartmentName = departmentList.Find(m => m.DepartmentID == questionBankItem.DepartmentID)?.LocalShowName,
                    Sort = questionBankItem.Sort,
                };
                viewList.Add(view);
            }
            return viewList.OrderByDescending(m => m.ModifyDateTime).ToList();
        }

        /// <summary>
        /// 递归获取子节点所属父节点，拼接分类名称
        /// </summary>
        /// <param name="settingList"></param>
        /// <param name="targetSettingValue"></param>
        /// <returns></returns>
        private static string FindParentNodes(List<TrainingExamineClassifiedView> settingList, string targetSettingValue)
        {
            string showName = null;
            foreach (var setting in settingList)
            {
                if (setting.SettingValue == targetSettingValue)
                {
                    showName = setting.LocalShowName;
                }
                var result = FindParentNodes(setting.Children, targetSettingValue);
                if (result != null)
                {
                    showName = setting.LocalShowName + "→" + result + showName;
                }
            }
            return showName;
        }

        public async Task<bool> SaveQuestionBank(QuestionBankView view)
        {
            if (!int.TryParse(view.QuestionBankType, out var questionBankType))
            {
                _logger.Error($"题库类型字段，类型转换失败,value={view.QuestionBankType}");
                return false;
            }
            var questionBankData = await _questionBankRepository.GetDataByID(view.QuestionBankID);
            if (questionBankData == null)
            {
                var info = new QuestionBankInfo()
                {
                    QuestionBankID = Guid.NewGuid().ToString("N"),
                    Content = view.Content,
                    QuestionBankType = questionBankType,
                    SourceID = view.SourceID,
                    Year = view.Year,
                    SourceType = view.SourceType,
                    HospitalID = view.HospitalID,
                    DeleteFlag = "",
                    IsPractical = view.IsPractical,
                    OrganizationalDepartmentCode = view.OrganizationalDepartmentCode,
                    ParentID = view.ParentID,
                    DepartmentID = view.DepartmentID
                };
                info.Add(view.ModifyEmployeeID);
                info.Modify(view.ModifyEmployeeID);
                view.QuestionBankID = info.QuestionBankID;
                await _unitOfWork.GetRepository<QuestionBankInfo>().InsertAsync(info);
            }
            else
            {
                questionBankData.Content = view.Content;
                questionBankData.QuestionBankType = questionBankType;
                questionBankData.SourceID = view.SourceID;
                questionBankData.SourceType = view.SourceType;
                questionBankData.Year = view.Year;
                questionBankData.IsPractical = view.IsPractical;
                questionBankData.OrganizationalDepartmentCode = view.OrganizationalDepartmentCode;
                questionBankData.DepartmentID = view.DepartmentID;
                questionBankData.Modify(view.ModifyEmployeeID);
            }
            // 参考上级题库内容 |保存对应的考试题目
            if (!string.IsNullOrEmpty(view.ReferenceQuestionBankID))
            {
                await SaveQuestionDatas(view.ReferenceQuestionBankID, view.QuestionBankID, view.ModifyEmployeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        public async Task<List<SelectOptionsView>> GetQuestionBankDictAsync()
        {
            return await _questionBankRepository.GetQuestionBankDictAsync();
        }

        public async Task<List<QuestionBankCascadeView>> GetQuestionBankCascaderAsync(bool? isPractical, string employeeID)
        {
            var allQuestionBanks = await _questionBankRepository.GetQuestionBankList(null, isPractical);
            // 判空，兼容之前没有该字段时的数据
            var oneLevel = allQuestionBanks.Where(m => m.OrganizationalDepartmentCode == "1" || string.IsNullOrEmpty(m.OrganizationalDepartmentCode) || m.ParentID == null).ToList();
            return GenerateCascadeView(allQuestionBanks, [.. oneLevel.OrderBy(m => m.Sort)]);
        }

        private List<QuestionBankCascadeView> GenerateCascadeView(List<QuestionBankInfo> allQuestionBanks, List<QuestionBankInfo> questionBanks)
        {
            var views = new List<QuestionBankCascadeView>();
            foreach (var viewItem in questionBanks)
            {
                var view = new QuestionBankCascadeView
                {
                    Label = viewItem.Content,
                    Value = viewItem.QuestionBankID,
                    IsPractical = viewItem.IsPractical
                };
                var subLevelView = allQuestionBanks.Where(m => m.ParentID == viewItem.QuestionBankID).ToList();
                if (subLevelView.Count > 0)
                {
                    var sortSubLevelViews = subLevelView.OrderBy(m => m.Sort).ToList();
                    view.Children = GenerateCascadeView(allQuestionBanks, sortSubLevelViews);
                }
                views.Add(view);
            }
            return views;
        }
        /// <summary>
        /// 获取题库下拉框数据内容
        /// </summary>
        /// <param name="isPractical"></param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetQuestionBankSelectList(bool? isPractical)
        {
            var viewList = new List<SelectOptionsView>();
            var questionBankList = await _questionBankRepository.GetQuestionBankList(null, isPractical);
            if (questionBankList.Count <= 0)
            {
                return viewList;
            }
            var levelOne = questionBankList.Where(m => string.IsNullOrEmpty(m.ParentID)).OrderBy(m => m.Sort).ToList();
            List<QuestionBankCascadeView> cascadeViews = GenerateCascadeView(questionBankList, levelOne);
            return FlattenTree(cascadeViews);
        }
        /// <summary>
        /// 扁平化题库树
        /// </summary>
        /// <param name="tree"></param>
        /// <returns></returns>
        public static List<SelectOptionsView> FlattenTree(List<QuestionBankCascadeView> tree)
        {
            var result = new List<SelectOptionsView>();
            foreach (var treeItem in tree)
            {
                Traverse(treeItem, null);
            }
            return result;
            void Traverse(QuestionBankCascadeView node, string parentLabel)
            {
                var label = string.IsNullOrWhiteSpace(parentLabel) ? node.Label : $"{parentLabel}  {node.Label}";
                if (node.Children == null)
                {
                    result.Add(new SelectOptionsView
                    {
                        Value = node.Value,
                        Label = label,
                    });
                    return;
                }
                foreach (var childItem in node.Children)
                {
                    Traverse(childItem, label);
                }
            }
        }
        #endregion

        #region 题目
        /// <summary>
        ///  保存题库中的题目
        /// </summary>
        /// <param name="referenceQuestionBankID">参考题库ID</param>
        /// <param name="questionBankID">新增题库ID</param>
        /// <param name="modifyEmployeeID">修改人</param>
        /// <returns></returns>
        private async Task SaveQuestionDatas(string referenceQuestionBankID, string questionBankID, string modifyEmployeeID)
        {
            var questionList = await _examinationQuestionRepository.GetListByQuestionBankID(referenceQuestionBankID, true);

            var questionDetails = await _examinationQuestionDetailRepository.GetListByQuestionIDs(questionList.Select(m => m.ExaminationQuestionID).ToList(), true);
            foreach (var questionItem in questionList)
            {
                questionItem.QuestionBankID = questionBankID;
                questionItem.Add(modifyEmployeeID);
                questionItem.Modify(modifyEmployeeID);
                await _unitOfWork.GetRepository<ExaminationQuestionInfo>().InsertAsync(questionItem);
                foreach (var detailItem in questionDetails)
                {
                    detailItem.ExaminationQuestionDetailID = detailItem.GetId();
                    detailItem.Add(modifyEmployeeID);
                    detailItem.Modify(modifyEmployeeID);
                    await _unitOfWork.GetRepository<ExaminationQuestionDetailInfo>().InsertAsync(detailItem);
                }
            }
        }

        public async Task<string> DeleteQuestionBank(string questionBankID, string employeeID)
        {
            var bankList = await GetQuestionBankWitchChildren(questionBankID);
            if (bankList.Count <= 0)
            {
                return "没有题库记录";
            }
            foreach (var data in bankList)
            {
                data.Delete(employeeID);
            }
            var bankIDList = bankList.Select(m => m.QuestionBankID).ToList();
            // 删除题库中的题目
            await DeleteQuestion(bankIDList, employeeID);

            var paperMainIDList = await _examinationPaperService.DeletePaperByQuestionBank(bankIDList, employeeID);
            if (paperMainIDList.Count <= 0)
            {
                await _unitOfWork.SaveChangesAsync();
                return null;
            }
            var message = await _examineService.DeleteExamineByPaperMainID(paperMainIDList, employeeID);
            if (string.IsNullOrEmpty(message))
            {
                await _unitOfWork.SaveChangesAsync();
                return null;
            }
            return message;
        }
        /// <summary>
        ///  删除题目和对应的明细
        /// </summary>
        /// <param name="bankIDs">题库ID集合</param>
        /// <param name="employeeID">操作人</param>
        /// <returns></returns>
        private async Task DeleteQuestion(List<string> bankIDs, string employeeID)
        {
            // 删除题目
            var questionInfos = await _examinationQuestionRepository.GetListByQuestionBankIDList(bankIDs);
            if (questionInfos.Count <= 0)
            {
                return;
            }
            questionInfos.ForEach(m => m.Delete(employeeID));
            // 删除题目明细
            var questionIDs = questionInfos.Select(m => m.ExaminationQuestionID).ToList();
            var questionDetailInfo = await _examinationQuestionDetailRepository.GetListByQuestionIDs(questionIDs);
            questionDetailInfo.ForEach(m => m.Delete(employeeID));
        }
        /// <summary>
        /// 根据题库ID获取当前题库以及所有子题库（所有层级）
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <returns></returns>
        private async Task<List<QuestionBankInfo>> GetQuestionBankWitchChildren(string questionBankID)
        {
            var bankDict = await _questionBankRepository.GetQuestionBankParentChildMapping();
            if (bankDict.Count <= 0)
            {
                return [];
            }
            var queue = new Queue<string>();
            queue.Enqueue(questionBankID);
            var allBankIDs = new HashSet<string> { questionBankID };
            while (queue.Count > 0)
            {
                var currentId = queue.Dequeue();
                if (bankDict.TryGetValue(currentId, out var children))
                {
                    foreach (var childId in children)
                    {
                        if (allBankIDs.Add(childId))
                        {
                            queue.Enqueue(childId);
                        }
                    }
                }
            }
            return await _questionBankRepository.GetListByBankIDs(allBankIDs.ToList());
        }
        public async Task<List<ExaminationQuestionView>> GetQuestionsByBankIDAsync(string questionBankID)
        {
            var returnView = new List<ExaminationQuestionView>();
            List<ExaminationQuestionInfo> questions = await _examinationQuestionRepository.GetListByQuestionBankID(questionBankID);
            var employeeIDs = questions.Select(m => m.ModifyEmployeeID).ToList();
            var questionIDList = questions.Select(m => m.ExaminationQuestionID).ToList();
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            var questionDetailList = await _examinationQuestionDetailRepository.GetAnswersByQuestionIDs(questionIDList);
            var settingParams = new SettingDictionaryParams
            {
                SettingTypeCode = "ExaminationQuestion"
            };
            var settingDictionaryList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var questionTagDict = settingDictionaryList.Where(m => m.SettingTypeValue.Contains("QuestionTag")).ToList();
            var difficultyLevelDict = settingDictionaryList.Where(m => m.SettingTypeValue == "DifficultyLevel").ToList();
            var typeDict = settingDictionaryList.Where(m => m.SettingTypeValue == "ExaminationQuestionType").ToList();
            foreach (var question in questions)
            {
                var view = new ExaminationQuestionView()
                {
                    ExaminationQuestionID = question.ExaminationQuestionID,
                    QuestionContent = question.QuestionContent,
                    ExaminationQuestionType = question.ExaminationQuestionType,
                    ExaminationQuestionTypeName = typeDict.FirstOrDefault(n => n.SettingValue == question.ExaminationQuestionType)?.Description,
                    DifficultyLevel = question.DifficultyLevel,
                    DifficultyLevelName = difficultyLevelDict.FirstOrDefault(n => n.SettingValue == question.DifficultyLevel)?.Description,
                    QuestionTag = question.QuestionTag,
                    QuestionTagArr = question.QuestionTag.Split(",").ToList(),
                    Instructions = question.Instructions,
                    Analysis = question.Analysis,
                    FilterWeight = question.FilterWeight,
                    ModifyDateTime = question.ModifyDateTime,
                    ModifyEmployeeName = employeeList.Find(n => n.EmployeeID == question.ModifyEmployeeID)?.EmployeeName ?? question.ModifyEmployeeID,
                    QuestionBankID = questionBankID,
                    QuestionDetail = questionDetailList.TryGetValue(question.ExaminationQuestionID, out var answers) ? answers : new List<ItemDetailListView>(),
                    Score = question.Score,
                    Sort = question.Sort
                };
                view.QuestionTagName = questionTagDict.Where(n => view.QuestionTagArr.Any(m => m == n.SettingValue)).Select(n => n.Description).ToList();
                returnView.Add(view);
            }
            return returnView.OrderBy(m => m.Sort).ToList();
        }

        public async Task<bool> DeleteQuestionAsync(int questionID, string employeeID)
        {
            var question = await _examinationQuestionRepository.GetDataByID(questionID);
            if (question != null)
            {
                question.Delete(employeeID);
            }
            var questionDetail = await _examinationQuestionDetailRepository.GetListByQuestionID(questionID);
            foreach (var detail in questionDetail)
            {
                detail.Delete(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 将所有题目导入到题库
        /// </summary>
        /// <param name="questionOuterView">前端导入的题目题库等信息View</param>
        /// <param name="employeeID">操作人ID</param>
        /// <returns>是否成功导入</returns>
        public async Task<bool> ImportQuestionsAsync(ExaminationQuestionOuterView questionOuterView, string employeeID)
        {
            if (questionOuterView.BankAndQuestionTree == null && questionOuterView.QuestionList == null)
            {
                return false;
            }
            var topQuestionBank = await _questionBankRepository.GetDataByID(questionOuterView.QuestionBankID);
            if (topQuestionBank == null)
            {
                _logger.Warn($"找不到对应的题库，BankID={questionOuterView.QuestionBankID}");
                return false;
            }
            if (questionOuterView.QuestionList != null && questionOuterView.QuestionList.Count > 0)
            {
                await InsertNewQuestion(questionOuterView.QuestionBankID, questionOuterView.QuestionList, employeeID);
            }
            var allDeptQuestionBanks = await _questionBankRepository.GetQuestionBankList(topQuestionBank.DepartmentID.HasValue ? [topQuestionBank.DepartmentID.Value] : null);
            List<ExaminationQuestionInfo> questionList = [];
            List<QuestionBankInfo> bankList = [];

            foreach (var bankAndQuestionTree in questionOuterView.BankAndQuestionTree)
            {
                // 导入题库与当前题库相同，不新增题库
                if (bankAndQuestionTree.Content == topQuestionBank.Content)
                {
                    Process(topQuestionBank.QuestionBankID, bankAndQuestionTree, allDeptQuestionBanks, false);
                    continue;
                }
                // 导入题库与当前题库不同，新增子题库
                if (bankAndQuestionTree.Content != topQuestionBank.Content)
                {
                    Process(topQuestionBank.QuestionBankID, bankAndQuestionTree, allDeptQuestionBanks, true);
                }
            }
            // 导入的题库排序
            bankList = SetBankSort(allDeptQuestionBanks, bankList);
            // 过滤题目，导入时，排除已经导入过的题目
            questionList = await FilterDuplicateQuestion(questionList);
            await _unitOfWork.GetRepository<QuestionBankInfo>().InsertAsync(bankList);
            await _unitOfWork.GetRepository<ExaminationQuestionInfo>().InsertAsync(questionList);
            return await _unitOfWork.SaveChangesAsync() >= 0;

            void Process(string parentID, BankAndQuestionTreeView bankAndQuestionTreeView, List<QuestionBankInfo> allDeptQuestionBanks, bool addBankFlag)
            {
                if (!addBankFlag)
                {
                    questionList.AddRange(ProcessChildren(parentID, parentID));
                    return;
                }
                // 存在题库（同一层级子题库集合中）
                var theSameBank = allDeptQuestionBanks.Find(m => m.ParentID == parentID && m.Content == bankAndQuestionTreeView.Content);
                if (theSameBank != null)
                {
                    questionList.AddRange(ProcessChildren(theSameBank.QuestionBankID, theSameBank.QuestionBankID));
                    return;
                }
                // 新增题目和题库
                var addBank = CreateQuestionBankByImportedBank(bankAndQuestionTreeView.Content, topQuestionBank, parentID, employeeID);
                bankList.Add(addBank);
                questionList.AddRange(ProcessChildren(addBank.QuestionBankID, addBank.QuestionBankID));
                // 处理子题库并新增当前题库题目
                List<ExaminationQuestionInfo> ProcessChildren(string currBankID, string childBankID)
                {
                    var tempQustions = AddNewQuestionAndDetail(bankAndQuestionTreeView.Questions, employeeID, currBankID);
                    if (bankAndQuestionTreeView.Children != null && bankAndQuestionTreeView.Children.Count > 0)
                    {
                        foreach (var childItem in bankAndQuestionTreeView.Children)
                        {
                            Process(childBankID, childItem, allDeptQuestionBanks, true);
                        }
                    }
                    return tempQustions;
                }
            }
        }
        /// <summary>
        /// 设置题库排序
        /// </summary>
        /// <param name="allDeptQuestionBanks"></param>
        /// <param name="bankList"></param>
        /// <returns></returns>
        private static List<QuestionBankInfo> SetBankSort(List<QuestionBankInfo> allDeptQuestionBanks, List<QuestionBankInfo> bankList)
        {
            var topBankSort = allDeptQuestionBanks.Count(m => m.ParentID == null);
            var countDict = allDeptQuestionBanks.Where(m => m.ParentID != null).GroupBy(m => m.ParentID).ToDictionary(m => m.Key, n => n.Count());
            foreach (var addItem in bankList)
            {
                if (addItem.ParentID == null)
                {
                    topBankSort++;
                    addItem.Sort = topBankSort;
                }
                if (countDict.TryGetValue(addItem.ParentID, out var count))
                {
                    addItem.Sort = count + 1;
                    countDict[addItem.ParentID] = count + 1;
                }
                else
                {
                    countDict.Add(addItem.ParentID, 1);
                    addItem.Sort = 1;
                }
            }
            return bankList;
        }

        /// <summary>
        /// 过滤重复题目
        /// </summary>
        /// <param name="questionList"></param>
        /// <returns></returns>
        private async Task<List<ExaminationQuestionInfo>> FilterDuplicateQuestion(List<ExaminationQuestionInfo> questionList)
        {
            var existingQuestions = await _examinationQuestionRepository.GetListByQuestionBankIDList(questionList.Select(m => m.QuestionBankID).ToList(), true);
            if (existingQuestions.Count > 0)
            {
                // 去重：仅保留不存在的题目
                questionList.RemoveAll(q =>
                    existingQuestions.Any(eq =>
                        eq.QuestionBankID == q.QuestionBankID
                        && eq.QuestionContent == q.QuestionContent
                    )
                );
            }
            return questionList;
        }

        /// <summary>
        /// 新增导入的题目和题目明细
        /// </summary>
        /// <param name="addQuestions">新增题目</param>
        /// <param name="employeeID"></param>
        /// <param name="questionBankID"></param>
        /// <returns></returns>
        private List<ExaminationQuestionInfo> AddNewQuestionAndDetail(List<ExaminationQuestionView> addQuestions, string employeeID, string questionBankID)
        {

            List<ExaminationQuestionInfo> newQuestionInfos = [];
            for (var index = 0; index < addQuestions.Count; index++)
            {
                var addQuestion = addQuestions[index];
                var newQuestion = new ExaminationQuestionInfo
                {
                    Analysis = addQuestion.Analysis ?? "",
                    DifficultyLevel = addQuestion.DifficultyLevel ?? "",
                    ComponentListID = GetComponentListIDByType(addQuestion.ExaminationQuestionType),
                    FilterWeight = 1,
                    ExaminationQuestionType = addQuestion.ExaminationQuestionType,
                    QuestionBankID = questionBankID,
                    Instructions = "",
                    QuestionContent = addQuestion.QuestionContent,
                    QuestionTag = addQuestion.QuestionTag ?? "",
                    Score = addQuestion.Score,
                    Sort = index + 1,
                };
                newQuestion.Add(employeeID).Modify(employeeID);
                newQuestionInfos.Add(newQuestion);
                if (addQuestions[index].QuestionDetail != null && addQuestions[index].QuestionDetail.Count > 0)
                {
                    var sort = 0;
                    foreach (var detailItem in SortQuestionDetail(newQuestion.ExaminationQuestionType, addQuestions[index].QuestionDetail))
                    {
                        var newQuestionDetail = new ExaminationQuestionDetailInfo
                        {
                            ExaminationQuestionID = newQuestion.ExaminationQuestionID,
                            Content = detailItem.Content,
                            AnswerFlag = detailItem.SelectFlag,
                            Sort = ++sort,
                            ExaminationQuestion = newQuestion
                        };
                        if (string.IsNullOrEmpty(detailItem.Content))
                        {
                            throw new CustomException($"题目:{newQuestion.QuestionContent},答案选项内容明细为空",true);
                        }
                        newQuestionDetail.ExaminationQuestionDetailID = newQuestionDetail.GetId();
                        newQuestionDetail.Add(employeeID).Modify(employeeID);
                        newQuestion.ExaminationQuestionDetails.Add(newQuestionDetail);
                    }
                }
            }
            return newQuestionInfos;
        }
        /// <summary>
        /// 导入的题目选项顺序矫正
        /// </summary>
        /// <param name="questionType"></param>
        /// <param name="detailListView"></param>
        /// <returns></returns>
        public IEnumerable<ItemDetailListView> SortQuestionDetail(string questionType, List<ItemDetailListView> detailListView)
        {
            if (questionType == "Judgment")
            {
                return detailListView.OrderBy(m => m.Content == "正确" ? 0 : 1);
            }
            return detailListView;
        }
        /// <summary>
        ///  根据导入的题库信息 新增题库model实例
        /// </summary>
        /// <param name="bankContent"></param>
        /// <param name="topQuestionBank"></param>
        /// <param name="parentID"></param>
        /// <param name="employeeID"></param>
        /// <param name="bankSort">题库排序</param>
        /// <returns></returns>
        private QuestionBankInfo CreateQuestionBankByImportedBank(string bankContent, QuestionBankInfo topQuestionBank, string parentID, string employeeID)
        {
            var newQuestionBank = new QuestionBankInfo
            {
                Content = bankContent,
                Year = topQuestionBank.Year,
                HospitalID = topQuestionBank.HospitalID,
                IsPractical = topQuestionBank.IsPractical,
                DepartmentID = topQuestionBank.DepartmentID,
                QuestionBankType = topQuestionBank.QuestionBankType,
                OrganizationalDepartmentCode = topQuestionBank.OrganizationalDepartmentCode,
                ParentID = parentID,
            };

            newQuestionBank.Add(employeeID).Modify(employeeID);
            newQuestionBank.QuestionBankID = newQuestionBank.GetId();
            return newQuestionBank;
        }
        /// <summary>
        /// 插入新题目（理论类试卷，只能在当前题库导入题目）
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <param name="questions"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task InsertNewQuestion(string questionBankID, List<ExaminationQuestionView> questions, string employeeID)
        {
            var baseInfo = new BaseInfo();
            List<ExaminationQuestionInfo> newQuestionList = [];
            List<ExaminationQuestionDetailInfo> newQuestionDetailList = [];
            ExaminationQuestionInfo tempQuestion = null;
            List<ExaminationQuestionDetailInfo> tempQuestionDetails = [];
            foreach (var question in questions)
            {
                tempQuestion = new ExaminationQuestionInfo
                {
                    QuestionBankID = questionBankID,
                    QuestionContent = question.QuestionContent,
                    DifficultyLevel = question.DifficultyLevel ?? "0",
                    Analysis = question.Analysis,
                    QuestionTag = question.QuestionTag ?? "",
                    AddDateTime = DateTime.Now,
                    AddEmployeeID = employeeID,
                    ModifyDateTime = DateTime.Now,
                    ModifyEmployeeID = employeeID,
                    FilterWeight = question.FilterWeight ?? 1,
                    ExaminationQuestionType = question.ExaminationQuestionType,
                    ComponentListID = GetComponentListIDByType(question.ExaminationQuestionType),
                    Instructions = question.Instructions,
                    Score = question.Score
                };
                newQuestionList.Add(tempQuestion);
                if (question.QuestionDetail == null || question.ExaminationQuestionType == "Scoring")
                {
                    continue;
                }
                int detailSortID = 1;
                tempQuestionDetails = question.QuestionDetail.Select(m => new ExaminationQuestionDetailInfo
                {
                    ExaminationQuestionDetailID = baseInfo.GetId(),
                    ExaminationQuestionID = tempQuestion.ExaminationQuestionID,
                    Content = m.Content,
                    AnswerFlag = m.SelectFlag,
                    AddDateTime = DateTime.Now,
                    AddEmployeeID = employeeID,
                    ModifyDateTime = DateTime.Now,
                    ModifyEmployeeID = employeeID,
                    DeleteFlag = "",
                    Sort = detailSortID++
                }).ToList();
                tempQuestion.ExaminationQuestionDetails = tempQuestionDetails;
            }
            // 过滤题目，导入时，排除已经导入过的题目
            newQuestionList = await FilterDuplicateQuestion(newQuestionList);
            await _unitOfWork.GetRepository<ExaminationQuestionInfo>().InsertAsync(newQuestionList);
            // 提前保存 获取题目主键ID
            await _unitOfWork.SaveChangesAsync();
        }

        /// <summary>
        /// 根据题目类型获取题目显示类型
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        private int GetComponentListIDByType(string type)
        {
            int componentListID = 101;
            switch (type)
            {
                case "SingleChoice":
                    componentListID = 103;
                    break;
                case "Judgment":
                    componentListID = 103;
                    break;
                case "MultipleChoice":
                    componentListID = 102;
                    break;
                case "ShortAnswer":
                    componentListID = 101;
                    break;
                case "Scoring":
                    componentListID = 104;
                    break;
            }
            return componentListID;
        }

        public async Task<bool> SaveQuestionData(ExaminationQuestionView questionSaveParamsView, string employeeID)
        {
            var questionInfo = await _examinationQuestionRepository.GetDataByID(questionSaveParamsView.ExaminationQuestionID);
            if (questionInfo == null)
            {
                var mainInfo = new ExaminationQuestionInfo()
                {
                    QuestionContent = questionSaveParamsView.QuestionContent,
                    ExaminationQuestionType = questionSaveParamsView.ExaminationQuestionType,
                    ComponentListID = GetComponentListIDByType(questionSaveParamsView.ExaminationQuestionType),
                    DifficultyLevel = questionSaveParamsView.DifficultyLevel ?? "",
                    QuestionTag = string.Join(",", questionSaveParamsView.QuestionTagArr ?? new List<string>()),
                    Instructions = questionSaveParamsView.Instructions,
                    FilterWeight = questionSaveParamsView.FilterWeight ?? 1,
                    Analysis = questionSaveParamsView.Analysis,
                    QuestionBankID = questionSaveParamsView.QuestionBankID,
                    Score = questionSaveParamsView.Score,
                    Sort = questionSaveParamsView.Sort,
                    DeleteFlag = "",
                };
                mainInfo.Add(employeeID);
                mainInfo.Modify(employeeID);
                await _unitOfWork.GetRepository<ExaminationQuestionInfo>().InsertAsync(mainInfo);
                await _unitOfWork.SaveChangesAsync();
                InsertQuestionDetailList(mainInfo.ExaminationQuestionID, employeeID, questionSaveParamsView.QuestionDetail);
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }

            questionInfo.QuestionContent = questionSaveParamsView.QuestionContent;
            questionInfo.ExaminationQuestionType = questionSaveParamsView.ExaminationQuestionType;
            questionInfo.ComponentListID = GetComponentListIDByType(questionSaveParamsView.ExaminationQuestionType);
            questionInfo.DifficultyLevel = questionSaveParamsView.DifficultyLevel ?? "";
            questionInfo.QuestionTag = string.Join(",", questionSaveParamsView.QuestionTagArr);
            questionInfo.Instructions = questionSaveParamsView.Instructions;
            questionInfo.FilterWeight = questionSaveParamsView.FilterWeight ?? 1;
            questionInfo.Analysis = questionSaveParamsView.Analysis;
            questionInfo.Score = questionSaveParamsView.Score;
            questionInfo.Sort = questionSaveParamsView.Sort;
            questionInfo.Modify(employeeID);
            var questionDetail = await _examinationQuestionDetailRepository.GetListByQuestionID(questionInfo.ExaminationQuestionID);
            foreach (var detail in questionDetail)
            {
                detail.Delete(employeeID);
            }
            InsertQuestionDetailList(questionInfo.ExaminationQuestionID, employeeID, questionSaveParamsView.QuestionDetail);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 插入题目明细数据
        /// </summary>
        /// <param name="examinationQuestionID"></param>
        /// <param name="employeeID"></param>
        /// <param name="detailList"></param>
        private async void InsertQuestionDetailList(int examinationQuestionID, string employeeID, List<ItemDetailListView> detailList)
        {
            if (detailList == null)
            {
                return;
            }
            int detailSort = 1;
            foreach (var detail in detailList)
            {
                var questionDetailInfo = new ExaminationQuestionDetailInfo()
                {
                    ExaminationQuestionDetailID = Guid.NewGuid().ToString("N"),
                    ExaminationQuestionID = examinationQuestionID,
                    AnswerFlag = detail.SelectFlag,
                    Content = detail.Content,
                    DeleteFlag = "",
                    Sort = detailSort++,
                };
                questionDetailInfo.Add(employeeID);
                questionDetailInfo.Modify(employeeID);
                await _unitOfWork.GetRepository<ExaminationQuestionDetailInfo>().InsertAsync(questionDetailInfo);
            }
        }

        public async Task<List<SelectOptionsView>> GetQuestionOptionList(List<string> questionBankIDs)
        {
            if (questionBankIDs == null || questionBankIDs.Count <= 0)
            {
                return [];
            }
            var questionList = await _examinationQuestionRepository.GetListByQuestionBankIDList(questionBankIDs);
            if (questionList.Count <= 0)
            {
                return [];
            }
            return questionList.Select(m => new SelectOptionsView
            {
                Label = m.QuestionContent,
                Value = m.ExaminationQuestionID,
            }).ToList();
        }

        public async Task<Dictionary<string, Dictionary<int, int>>> GetQuestionTypeCount(List<string> questionBankIDs)
        {
            var questions = await _examinationQuestionRepository.GetQuestionTypeAndIDByBankIDs(questionBankIDs);
            if (questions.Count <= 0)
            {
                return [];
            }
            var settingDictionaryParam = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationQuestion",
                SettingTypeValue = "ExaminationQuestionType",
            };
            var questionTypeSettings = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParam);
            if (questionTypeSettings.Count <= 0)
            {
                _logger.Error($"查看题目类型字典失败{ListToJson.ToJson(settingDictionaryParam)}");
                return null;
            }
            var ruleLists = await _ruleListRepository.GetListByTypeList(["CompositionPaperByCount"]);
            var ruleDict = ruleLists.ToDictionary(m => m.RuleCode, n => n.RuleListID);
            if (ruleDict.Count <= 0)
            {
                _logger.Error("获取组卷规则失败，RuleList.SystemType='CompositionPaperByCount'");
                return null;
            }
            var groupQuestions = questions.GroupBy(m => m.QuestionBankID);
            var responseDict = new Dictionary<string, Dictionary<int, int>>();
            foreach (var groupItem in groupQuestions)
            {
                // 按照匹配的题型 归纳出每个题库中各个题型的题目数量
                foreach (var typeSettingItem in questionTypeSettings)
                {
                    var questionTypeCount = groupItem.Count(m => m.ExaminationQuestionType == typeSettingItem.SettingValue);
                    if (!ruleDict.TryGetValue($"{typeSettingItem.SettingValue}Count", out var countRuleListID))
                    {
                        continue;
                    }
                    if (!responseDict.TryGetValue(groupItem.Key, out Dictionary<int, int> secondDict))
                    {
                        secondDict = new Dictionary<int, int>
                        {
                            {countRuleListID, questionTypeCount }
                        };
                        responseDict.Add(groupItem.Key, secondDict);
                        continue;
                    }
                    secondDict.Add(countRuleListID, questionTypeCount);
                }
            }
            return responseDict;
        }
        /// <summary>
        /// 更新排序
        /// </summary>
        /// <param name="bankSortView"></param>
        /// <returns></returns>
        public async Task<bool> UpdateBankSort(List<BankSortView> bankSortView)
        {
            var questionBanks = await _questionBankRepository.GetListByBankIDs(bankSortView.Select(m => m.QuestionBankID).ToList());
            foreach (var baneItem in questionBanks)
            {
                var currBank = bankSortView.Find(m => m.QuestionBankID == baneItem.QuestionBankID);
                if (currBank != null)
                {
                    baneItem.Sort = currBank.Sort;
                }
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 更新题目排序
        /// </summary>
        /// <param name="bankSortView"></param>
        /// <returns></returns>
        public async Task<bool> UpdateQuestionSort(List<BankSortView> questionSortView)
        {
            var dict = questionSortView.DistinctBy(m => m.QuestionID).ToDictionary(m => m.QuestionID, n => n.Sort);
            var questionListInfo = await _examinationQuestionRepository.GetListByIDList([.. dict.Keys]);

            foreach (var questionItem in questionListInfo)
            {
                questionItem.Sort = dict[questionItem.ExaminationQuestionID];
            }

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        #endregion
        /// <summary>
        /// 克隆题库
        /// </summary>
        /// <param name="bankId">题库ID</param>
        /// <param name="bankIds">被复制题库ID集合</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        public async Task<bool> CloneQuestionBankAsync(string questionBankID, List<string> cloneQuestionBankIDs, string employeeID)
        {
            if (string.IsNullOrEmpty(questionBankID) || cloneQuestionBankIDs == null || cloneQuestionBankIDs.Count <= 0)
            {
                return false;
            }
            var questions = await _examinationQuestionRepository.GetListByQuestionBankIDList(cloneQuestionBankIDs);
            if (questions.Count <= 0)
            {
                return false;
            }
            var result = new List<ExaminationQuestionInfo>();
            foreach (var question in questions)
            {
                result.Add(CreateExaminationQuestion(questionBankID, employeeID, question));
            }
            _unitOfWork.GetRepository<ExaminationQuestionInfo>().Insert(result);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 创建题目实体
        /// </summary>
        /// <param name="bankId">题库ID</param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="question">题目</param>
        /// <returns></returns>
        private static ExaminationQuestionInfo CreateExaminationQuestion(string questionBankID, string employeeID, ExaminationQuestionInfo question)
        {
            return new ExaminationQuestionInfo
            {
                QuestionContent = question.QuestionContent,
                ExaminationQuestionType = question.ExaminationQuestionType,
                ComponentListID = question.ComponentListID,
                DifficultyLevel = question.DifficultyLevel,
                QuestionTag = question.QuestionTag,
                Instructions = question.Instructions,
                FilterWeight = question.FilterWeight,
                Analysis = question.Analysis,
                QuestionBankID = questionBankID,
                AddEmployeeID = employeeID,
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                DeleteFlag = string.Empty,
                Score = question.Score,
                Sort = question.Sort,
            };
        }
        /// <summary>
        /// 复制题库
        /// </summary>
        /// <param name="questionBank">前端view</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        public async Task<bool> CopyQuestionBankAsync(QuestionBankView questionBank, string employeeID)
        {
            if (questionBank == null || string.IsNullOrEmpty(questionBank.QuestionBankID))
            {
                return false;
            }
            var bankIDs = await GetAllCopyBankIDs(questionBank.QuestionBankID);
            var bankInfos = await _questionBankRepository.GetListByBankIDs([.. bankIDs]);
            var maxID = await _examinationQuestionRepository.GetMaxID();
            var questions = await _examinationQuestionRepository.GetQuestionTypeAndIDByBankIDs(bankIDs);
            var questionDetails = await _examinationQuestionDetailRepository.GetListByQuestionIDs([.. questions.Select(m => m.ExaminationQuestionID).Distinct()]);
            var oldMapping = new Dictionary<string, string>();
            var newBankInfos = new List<QuestionBankInfo>();
            maxID++;
            foreach (var bankID in bankIDs)
            {
                var bankInfo = bankInfos.FirstOrDefault(m => m.QuestionBankID == bankID);
                if (bankInfo == null)
                {
                    continue;
                }
                var newBankInfo = CreateQuestionBankInfo(questionBank, employeeID, bankInfo);
                newBankInfos.Add(newBankInfo);
                oldMapping.Add(bankID, newBankInfo.QuestionBankID);
                var questionList = questions.Where(m => m.QuestionBankID == bankID);
                foreach (var question in questionList)
                {
                    ExaminationQuestionInfo newQuestion = CreateExaminationQuestionInfo(employeeID, newBankInfo, question);
                    await _unitOfWork.GetRepository<ExaminationQuestionInfo>().InsertAsync(newQuestion);
                    var questionDetailList = questionDetails.Where(m => m.ExaminationQuestionID == question.ExaminationQuestionID);
                    await SetExaminationQuestionDetail(employeeID, newQuestion, questionDetailList);
                    maxID++;
                }
            }
            ModifyRootBank(questionBank, newBankInfos);
            ModifyParentID(oldMapping, newBankInfos);
            await _unitOfWork.GetRepository<QuestionBankInfo>().InsertAsync(newBankInfos);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 修改父ID
        /// </summary>
        /// <param name="oldMapping">新旧ID关系</param>
        /// <param name="newBankInfos">新建的题库</param>
        private static void ModifyParentID(Dictionary<string, string> oldMapping, List<QuestionBankInfo> newBankInfos)
        {
            foreach (var newBankInfo in newBankInfos)
            {
                if (newBankInfo.ParentID == null)
                {
                    continue;
                }
                if (oldMapping.TryGetValue(newBankInfo.ParentID, out var newParentID))
                {
                    newBankInfo.ParentID = newParentID;
                }
            }
        }
        /// <summary>
        /// 修改根题库
        /// </summary>
        /// <param name="questionBank">根题库</param>
        /// <param name="newBankInfos">新的题库列表</param>
        private static void ModifyRootBank(QuestionBankView questionBank, List<QuestionBankInfo> newBankInfos)
        {
            var root = newBankInfos.FirstOrDefault(m => m.ParentID == null);
            root.Content = questionBank.Content;
            root.SourceID = questionBank.SourceID;
            root.SourceType = questionBank.SourceType;
            root.Year = questionBank.Year;
            root.IsPractical = questionBank.IsPractical;
            root.OrganizationalDepartmentCode = questionBank.OrganizationalDepartmentCode;
            root.DepartmentID = questionBank.DepartmentID;
            root.Modify(questionBank.ModifyEmployeeID);
        }
        /// <summary>
        /// 设置题目明细
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="newQuestion">新的题目</param>
        /// <param name="questionDetailList">题目明细列表</param>
        /// <returns></returns>
        private async Task SetExaminationQuestionDetail(string employeeID, ExaminationQuestionInfo newQuestion, IEnumerable<ExaminationQuestionDetailInfo> questionDetailList)
        {
            foreach (var newQuestionDetail in from questionDetail in questionDetailList
                                              let newQuestionDetail = new ExaminationQuestionDetailInfo
                                              {
                                                  ExaminationQuestionDetailID = Guid.NewGuid().ToString("N"),
                                                  ExaminationQuestionID = newQuestion.ExaminationQuestionID,
                                                  Content = questionDetail.Content,
                                                  AnswerFlag = questionDetail.AnswerFlag,
                                                  AddEmployeeID = employeeID,
                                                  AddDateTime = DateTime.Now,
                                                  ModifyEmployeeID = employeeID,
                                                  ModifyDateTime = DateTime.Now,
                                                  DeleteFlag = string.Empty,
                                                  Sort = questionDetail.Sort,
                                              }
                                              select newQuestionDetail)
                await _unitOfWork.GetRepository<ExaminationQuestionDetailInfo>().InsertAsync(newQuestionDetail);
        }
        /// <summary>
        /// 创建题目信息
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="newBankInfo">新的题库实体</param>
        /// <param name="question">题目</param>
        /// <returns></returns>
        private static ExaminationQuestionInfo CreateExaminationQuestionInfo(string employeeID, QuestionBankInfo newBankInfo, ExaminationQuestionInfo question)
        {
            return new ExaminationQuestionInfo
            {
                QuestionContent = question.QuestionContent,
                ExaminationQuestionType = question.ExaminationQuestionType,
                ComponentListID = question.ComponentListID,
                DifficultyLevel = question.DifficultyLevel,
                QuestionTag = question.QuestionTag,
                Instructions = question.Instructions,
                FilterWeight = question.FilterWeight,
                Analysis = question.Analysis,
                QuestionBankID = newBankInfo.QuestionBankID,
                AddEmployeeID = employeeID,
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                DeleteFlag = string.Empty,
                Sort = question.Sort,
                Score = question.Score,
            };
        }
        /// <summary>
        /// 创建题库信息
        /// </summary>
        /// <param name="questionBank">题库</param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="bankInfo">题库实体</param>
        /// <returns></returns>
        private static QuestionBankInfo CreateQuestionBankInfo(QuestionBankView questionBank, string employeeID, QuestionBankInfo bankInfo)
        {
            return new QuestionBankInfo
            {
                QuestionBankID = Guid.NewGuid().ToString("N"),
                Content = bankInfo.Content,
                QuestionBankType = bankInfo.QuestionBankType,
                SourceType = bankInfo.SourceType,
                SourceID = bankInfo.SourceID,
                Year = questionBank.Year,
                HospitalID = bankInfo.HospitalID,
                AddEmployeeID = employeeID,
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                DeleteFlag = string.Empty,
                IsPractical = bankInfo.IsPractical,
                OrganizationalDepartmentCode = bankInfo.OrganizationalDepartmentCode,
                ParentID = bankInfo.ParentID,
                DepartmentID = questionBank.DepartmentID,
                Sort = bankInfo.Sort,
            };
        }
        /// <summary>
        /// 获取所有需要复制的题库ID
        /// </summary>
        /// <param name="questionBank"></param>
        /// <returns></returns>
        private async Task<List<string>> GetAllCopyBankIDs(string questionBankID)
        {
            var parentChildMapping = await _questionBankRepository.GetQuestionBankParentChildMapping();
            var allBankIds = new HashSet<string>();
            var queue = new Queue<string>();
            queue.Enqueue(questionBankID);
            allBankIds.Add(questionBankID);
            while (queue.Count > 0)
            {
                var currentId = queue.Dequeue();
                if (parentChildMapping.TryGetValue(currentId, out var children))
                {
                    foreach (var childId in children)
                    {
                        if (allBankIds.Add(childId))
                        {
                            queue.Enqueue(childId);
                        }
                    }
                }
            }
            return [.. allBankIds];
        }
    }
}
