﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Repository;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class AttendanceApproveRecordApproval : ICommonProcessingAfterApproval
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAttendanceApproveRecordRepository _attendanceApproveRecordRepository;

        public AttendanceApproveRecordApproval(
            IUnitOfWork unitOfWork
            ,IAttendanceApproveRecordRepository attendanceApproveRecordRepository)
        {
            _unitOfWork = unitOfWork;
            _attendanceApproveRecordRepository = attendanceApproveRecordRepository;
        }
        /// <summary>
        /// 考勤审批
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public override async Task<(string, int)[]> ProcessAfterApprovalAsync(ProcessAfterApprovalView view)
        {
            var attendanceApproveRecord = await _attendanceApproveRecordRepository.GetRecordByRecordID(view.SourceID);
            if (attendanceApproveRecord == null)
            {
                _logger.Error("未找到审批记录来源，回写审批结果失败");
                return null;
            }
            //当审批状态改变的时候 回写状态
            if (attendanceApproveRecord.StatusCode != view.ApprovalResult)
            {
                attendanceApproveRecord.StatusCode = view.ApprovalResult;
                if (await _unitOfWork.SaveChangesAsync() <= 0)
                {
                    throw new Exception("更新质控信息失败，请联系管理员。");
                }
                // TODO: 待补充跳转路径
                return [];
            }
            return null;
        }
    }
}
