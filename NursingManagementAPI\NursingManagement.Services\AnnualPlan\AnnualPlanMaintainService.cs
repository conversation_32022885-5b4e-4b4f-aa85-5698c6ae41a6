﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    /// <summary>
    /// 年度计划-计划维护Service
    /// </summary>
    public class AnnualPlanMaintainService : IAnnualPlanMaintainService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualPlanTypeListRepository _typeListRepository;
        private readonly IAnnualGoalListRepository _goalListRepository;
        private readonly IAnnualIndicatorListRepository _indicatorListRepository;
        private readonly IAnnualPlanMainRepository _mainRepository;
        private readonly IAnnualPlanMainGoalRepository _mainGoalRepository;
        private readonly IAnnualPlanGoalGroupRepository _annualPlanGoalGroupRepository;
        private readonly IAnnualPlanIndicatorDetailRepository _annualPlanIndicatorDetailRepository;
        private readonly IAnnualPlanProjectDetailRepository _annualPlanProjectDetailRepository;
        private readonly IAnnualPlanInterventionService _annualPlanInterventionService;
        private readonly IQuarterPlanMaintainRepository _quarterPlanMaintainRepository;
        private readonly IMonthlyPlanMaintainRepository _monthlyPlanMaintainRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IAnnualSettingService _annualIndicatorListService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDictionaryService _dictionaryService;
        private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
        private readonly IAnnualPlanInterventionDetailRepository _interventionDetailRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IFileService _fileService;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;

        public AnnualPlanMaintainService(
            IAnnualPlanTypeListRepository annualPlanTypeListRepository,
            IAnnualGoalListRepository annualGoalListRepository,
            IAnnualIndicatorListRepository annualIndicatorListRepository,
            IAnnualPlanMainRepository mainRepository,
            IAnnualPlanMainGoalRepository mainGoalRepository,
            IAnnualPlanGoalGroupRepository annualPlanGoalGroupRepository,
            IAnnualPlanIndicatorDetailRepository annualPlanIndicatorDetailRepository,
            IAnnualPlanProjectDetailRepository annualPlanProjectDetailRepository,
            IAnnualPlanInterventionService annualPlanInterventionService,
            IQuarterPlanMaintainRepository quarterPlanMaintainRepository,
            IMonthlyPlanMaintainRepository monthlyPlanMaintainRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IAnnualSettingService annualIndicatorListService,
            IUnitOfWork unitOfWork,
            IDictionaryService dictionaryService,
            IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
            IAppConfigSettingRepository appConfigSettingRepository,
            IFileService fileService,
            IDepartmentListRepository departmentListRepository,
            IEmployeeToDepartmentRepository employeeToDepartmentRepository,
            ISettingDictionaryService settingDictionaryService
            )
        {
            _typeListRepository = annualPlanTypeListRepository;
            _goalListRepository = annualGoalListRepository;
            _indicatorListRepository = annualIndicatorListRepository;
            _mainRepository = mainRepository;
            _mainGoalRepository = mainGoalRepository;
            _annualPlanGoalGroupRepository = annualPlanGoalGroupRepository;
            _annualPlanIndicatorDetailRepository = annualPlanIndicatorDetailRepository;
            _annualPlanProjectDetailRepository = annualPlanProjectDetailRepository;
            _annualPlanInterventionService = annualPlanInterventionService;
            _quarterPlanMaintainRepository = quarterPlanMaintainRepository;
            _monthlyPlanMaintainRepository = monthlyPlanMaintainRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _annualIndicatorListService = annualIndicatorListService;
            _unitOfWork = unitOfWork;
            _dictionaryService = dictionaryService;
            _annualPlanInterventionMainRepository = annualPlanInterventionMainRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _fileService = fileService;
            _departmentListRepository = departmentListRepository;
            _employeeToDepartmentRepository = employeeToDepartmentRepository;
            _settingDictionaryService = settingDictionaryService;
        }

        #region 常量
        /// <summary>
        /// 图标类型
        /// </summary>
        private const string ICON_MODELTYPE_ANNUALPLAN = "AnnualPlan";
        /// <summary>
        /// 护理部ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;
        #endregion

        #region 主表
        /// <summary>
        /// 查询本人及上下级已制定的年度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<List<APMainView>> GetBrowseAPViews(int year, string employeeID)
        {
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var mainDepartment = employeeToDepartments.FirstOrDefault(m => m.IsMainDepartment ?? false)?.DepartmentID;
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var departIDs = await GetDepartmentIDs(employeeToDepartments, departmentList);

            var plans = await _mainRepository.GetPlanMainViewsByYearAndDepartmentIDs(year, departIDs);
            foreach (var annualPlanMain in plans)
            {
                annualPlanMain.IsMainDepartment = annualPlanMain.DepartmentID == mainDepartment;
                var departmentName = departmentList.Find(m => m.DepartmentID == annualPlanMain.DepartmentID)?.LocalShowName;
                annualPlanMain.PlanName = $"{annualPlanMain.Year}年{departmentName}年度计划";
                annualPlanMain.PlannerName = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(annualPlanMain.Planner, m => m.EmployeeName);
                // TODO:这里的附件不知道有什么用，先屏蔽
                // annualPlanMain.Attachments = await _fileService.GetFileListByClassAndSourceAsync(null, annualPlanMain.MainID);
            }
            return plans.OrderBy(m => departmentList.Find(n => n.DepartmentID == m.DepartmentID)?.Level).ToList();
        }
        /// <summary>
        /// 查询本人部门及上下级部门
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<List<CascaderView<int>>> GetBrowseAPDepartments(int year, string employeeID)
        {
            var organizationTypes = await _settingDictionaryService.GetOrganizationTypes();
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var departIDs = await GetDepartmentIDs(employeeToDepartments, departmentList);
            var options = departmentList
                .Where(m => departIDs.Contains(m.DepartmentID))
                .GroupBy(m => m.OrganizationType)
                .Select(m => new CascaderView<int>
                {
                    Label = organizationTypes[m.Key],
                    Value = Convert.ToInt32(m.Key.Trim()),
                    Disabled = false,
                    Children = m.Select(m => new CascaderView<int>()
                    {
                        Label = departmentList.FirstOrDefault(n => n.DepartmentID == m.DepartmentID)?.DepartmentContent ?? "",
                        Value = m.DepartmentID,
                        Disabled = false,
                        Children = []
                    }).ToList()
                }).ToList();
            return options;
        }

        /// <summary>
        /// 获取科室集合
        /// </summary>
        /// <param name="employeeToDepartments">当前用户的权限科室</param>
        /// <param name="departmentList">科室字典</param>
        /// <returns></returns>
        private async Task<IEnumerable<int>> GetDepartmentIDs(EmployeeToDepartmentInfo[] employeeToDepartments, List<DepartmentListInfo> departmentList)
        {
            if (employeeToDepartments.Any(m => (m.IsMainDepartment ?? false) && m.DepartmentID == DEPARTMENT_ID_405))
            {
                return departmentList.Where(m => m.OrganizationType == "1").Select(m => m.DepartmentID);
            }

            var departIDs = new HashSet<int>() { DEPARTMENT_ID_405 };
            foreach (var empToDepart in employeeToDepartments)
            {
                // 获取此部门的连续上下级部门ID集合
                var departments = await _dictionaryService.GetSuperAndSubDepartmentsByID(empToDepart.DepartmentID, false);
                departments.ForEach(m => departIDs.Add(m.DepartmentID));
            }

            return departIDs;
        }

        /// <summary>
        /// 获取当前用户制定、兼管的当年年度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">制定人</param>
        /// <returns></returns>
        public async Task<APMainView[]> GetCurrentYearAPViewsByEmployee(int year, string employeeID)
        {
            // 查询人员多组织架构表，获取部门ID集合
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var departmentIDs = employeeToDepartments.Select(m => m.DepartmentID);
            var aPMainViews = await _mainRepository.GetPlanMainViewsByYearAndDepartmentIDs(year, departmentIDs);
            var apMainIDs = aPMainViews.Select(m => m.MainID).ToArray();
            var planAndQuarterToID = await _quarterPlanMaintainRepository.GetQuarterToID(apMainIDs);
            var planAndMonthToID = await _monthlyPlanMaintainRepository.GetMonthToID(apMainIDs);
            // 补充附件、部门名称
            foreach (var aPMainView in aPMainViews)
            {
                // TODO: 不知道附件有什么用，先屏蔽
                // var attachments = await _fileService.GetFileListByClassAndSourceAsync(null, aPMainView.MainID);
                var departmentName = await _departmentListRepository.GetByIDAsync(aPMainView.DepartmentID);
                aPMainView.DepartmentName = departmentName?.LocalShowName;
                // aPMainView.Attachments = attachments;
                planAndQuarterToID.TryGetValue(aPMainView.MainID, out var quarterToIDs);
                aPMainView.QuarterToIDs = quarterToIDs;
                planAndMonthToID.TryGetValue(aPMainView.MainID, out var monthToIDs);
                aPMainView.MonthToIDs = monthToIDs;
                // 获取制定人姓名
                var plannerName = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(aPMainView.Planner, m => m.EmployeeName);
                aPMainView.PlannerName = plannerName;
            }
            return aPMainViews;
        }
        #endregion
        #region 分类
        /// <summary>
        /// 获取年度计划分类
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<int, string>> GetAnnualPlanTypes(string mainID)
        {
            var typeIDs = await _mainGoalRepository.GetAnnualPlanTypeIDs(mainID);
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var annualPlanMainTypeDict = typeList.Where(m => typeIDs.Contains(m.AnnualPlanTypeID)).ToDictionary(k => k.AnnualPlanTypeID,
                v => v.AnnualPlanTypeContent);
            return annualPlanMainTypeDict;
        }
        /// <summary>
        /// 分类排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> SortAPType(APTypeSortView sortView, string employeeID)
        {
            var mainGoalInfos = await _mainGoalRepository.GetInfosByPlanMainID(sortView.MainID, true);
            var groupInfos = await _annualPlanGoalGroupRepository.GetInfosByMainID(sortView.MainID);
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetInfosByMainID(sortView.MainID);
            var projectDetails = await _annualPlanProjectDetailRepository.GetInfosByMainID(sortView.MainID);

            // 创建更新 sort 函数
            var goalSort = 1;
            static string expression(dynamic m) => m.AnnualPlanMainGoalID;
            var updateGroupSort = CreateUpdateSortFunc(groupInfos, employeeID, expression);
            var updateIndicatorDetailSort = CreateUpdateSortFunc(indicatorDetails, employeeID, expression);
            var updateProjectDetailSort = CreateUpdateSortFunc(projectDetails, employeeID, expression);

            // 按照前端所给的分类-目标顺序更新 sort
            foreach (var typeAndGoal in sortView.TypeAndGoals)
            {
                mainGoalInfos.FindAll(m => m.AnnualPlanTypeID == typeAndGoal.TypeID).ForEach(m =>
                {
                    m.Sort = goalSort;
                    m.Modify(employeeID);
                    goalSort++;

                    updateGroupSort(m.AnnualPlanMainGoalID);
                    updateIndicatorDetailSort(m.AnnualPlanMainGoalID);
                    updateProjectDetailSort(m.AnnualPlanMainGoalID);
                });
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        #endregion
        #region 目标
        /// <summary>
        /// 获取年度计划详情
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<List<APType>> GetAnnualPlanContent(string mainID)
        {
            var goals = await _mainGoalRepository.GetAPGoalViews(mainID);
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var groups = await _annualPlanGoalGroupRepository.GetAnnualPlanGroups(mainID);
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetIndicatorDetailsByPlanMainID(mainID);
            var projectDetails = await _annualPlanProjectDetailRepository.GetProjectDetailsByPlanMainID(mainID);
            foreach (var goal in goals)
            {
                goal.GoalContent = goalList.Find(m => m.AnnualGoalID == goal.GoalID)?.GoalContent;
                goal.DepartmentID = goalList.Find(m => m.AnnualGoalID == goal.GoalID)?.DepartmentID ?? 0;
                goal.Groups = groups.FindAll(m => m.MainGoalID == goal.MainGoalID);
                goal.Groups.ForEach(m =>
                {
                    m.IndicatorDetails = indicatorDetails.Where(n => n.GroupID == m.GroupID).ToList();
                    m.ProjectDetails = projectDetails.Where(n => n.GroupID == m.GroupID).ToList();
                });
            }
            var apTypes = goals.GroupBy(m => m.TypeID).Select(m => new APType
            {
                TypeID = m.Key,
                TypeContent = typeList.Find(n => n.AnnualPlanTypeID == m.Key)?.AnnualPlanTypeContent,
                APMainGoals = m.ToList()
            }).ToList();
            return apTypes;
        }
        /// <summary>
        /// 更新目标排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> SortAPGoal(APGoalSortView sortView, string employeeID)
        {
            var mainGoals = await _mainGoalRepository.GetInfosByPlanMainID(sortView.MainID, true);
            var groupInfos = await _annualPlanGoalGroupRepository.GetInfosByMainID(sortView.MainID);
            var noRelateGroups = groupInfos.FindAll(m => m.AnnualPlanMainGoalID == sortView.DraggedMainGoalID).Count == 0;
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetInfosByMainID(sortView.MainID);
            var projectDetails = await _annualPlanProjectDetailRepository.GetInfosByMainID(sortView.MainID);

            if (sortView.NewTypeID.HasValue)
            {
                mainGoals.Find(m => sortView.DraggedMainGoalID == m.AnnualPlanMainGoalID).AnnualPlanTypeID = sortView.NewTypeID.Value;
            }

            // 创建更新 sort 函数
            var updateMainGoalSort = CreateUpdateSortFunc(mainGoals, employeeID, m => m.AnnualPlanMainGoalID);
            // 若拖拽的目标无关联分组，则无需更新其子表的 sort
            if (noRelateGroups)
            {
                Array.ForEach(sortView.SortedAPGoalIDs, updateMainGoalSort);
            }
            static string expression(dynamic m) => m.AnnualPlanMainGoalID;
            var updateGroupSort = CreateUpdateSortFunc(groupInfos, employeeID, expression);
            var updateIndicatorDetailSort = CreateUpdateSortFunc(indicatorDetails, employeeID, expression);
            var updateProjectDetailSort = CreateUpdateSortFunc(projectDetails, employeeID, expression);
            // 按照前端所给的目标ID顺序更新 sort
            foreach (var mainGoalID in sortView.SortedAPGoalIDs)
            {
                updateMainGoalSort(mainGoalID);
                updateGroupSort(mainGoalID);
                updateIndicatorDetailSort(mainGoalID);
                updateProjectDetailSort(mainGoalID);
            }

            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 创建更新与计划目标ID相关表Sort的函数
        /// </summary>
        /// <typeparam name="T">泛型</typeparam>
        /// <param name="list">待更新列表</param>
        /// <param name="employeeID">工号</param>
        /// <returns>一个函数，用于更新 list 中的 sort</returns>
        private static Action<string> CreateUpdateSortFunc<T>(List<T> list, string employeeID, Func<T, string> expression) where T : MutiModifyInfo
        {
            // 通过闭包将 sort 封入
            var sort = 1;
            return (sourceID) => list.Where(m => expression(m) == sourceID).ToList().ForEach(m =>
            {
                (m as dynamic).Sort = sort;
                m.Modify(employeeID);
                sort++;
            });
        }
        #endregion
        #region 分组
        /// <summary>
        /// 获取负责部门候选项
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<string[]> GetDepartmentOptions(string mainID)
        {
            var groups = await _annualPlanGoalGroupRepository.GetAPGroupDepartmentsByMainID(mainID);
            var separator = new[] { "\r\n", "\r", "\n", "、" };
            var departmentOptions = groups.Where(m => m is not null).SelectMany(m =>
            m.Split(separator, StringSplitOptions.RemoveEmptyEntries)).Where(m => !string.IsNullOrEmpty(m)).Distinct().ToArray();
            return departmentOptions;
        }
        /// <summary>
        /// 分组新增
        /// </summary>
        /// <param name="saveView">分组新增View</param>
        /// <returns>新GroupID</returns>
        private async Task<string> AddAnnualGoalGroup(SaveGroupView saveView)
        {
            var groupID = saveView.GroupID.GetRealID(out _);
            if (string.IsNullOrEmpty(groupID))
            {
                throw new Exception("新增失败！缺少必要参数！");
            }
            var newGroupInfo = new AnnualPlanGoalGroupInfo
            {
                AnnualPlanGoalGroupID = groupID,
                AnnualPlanMainID = saveView.MainID,
                AnnualPlanMainGoalID = saveView.MainGoalID,
                HospitalID = saveView.HospitalID,
                ResponsibleDepartments = saveView.ResponsibleDepartments != null ? string.Join("\n", saveView.ResponsibleDepartments) : "",
                Sort = saveView.Sort,
            };
            newGroupInfo.Add(saveView.EmployeeID);
            newGroupInfo.Modify(saveView.EmployeeID);
            await _unitOfWork.GetRepository<AnnualPlanGoalGroupInfo>().InsertAsync(newGroupInfo);
            // 更新其后的分组序号
            var groups = await _annualPlanGoalGroupRepository.GetAfterSortGroup(saveView.MainID, saveView.Sort);
            foreach (var group in groups)
            {
                group.Sort += 1;
                group.Modify(saveView.EmployeeID);
            }
            return newGroupInfo.AnnualPlanGoalGroupID;
        }
        /// <summary>
        /// 分组保存
        /// </summary>
        /// <param name="saveView">分组更新View</param>
        /// <returns></returns>
        public async Task<bool> SaveAnnualPlanGroup(SaveGroupView saveView)
        {
            // 若groupID带temp_，则走新增分组逻辑
            if (saveView.GroupID.Contains("temp"))
            {
                await AddAnnualGoalGroup(saveView);
                return await _unitOfWork.SaveChangesAsync() > 0;
            }

            var goalGroupInfo = await _annualPlanGoalGroupRepository.GetGoalGroup(saveView.GroupID);
            if (goalGroupInfo == null)
            {
                return false;
            }
            var isUpdate = false;
            var newResponsibleDepartments = string.Join("\n", saveView.ResponsibleDepartments);
            if (goalGroupInfo.ResponsibleDepartments != newResponsibleDepartments)
            {
                goalGroupInfo.ResponsibleDepartments = newResponsibleDepartments;
                goalGroupInfo.Modify(saveView.EmployeeID);
                isUpdate = true;
            }
            if (goalGroupInfo.Sort != saveView.Sort)
            {
                goalGroupInfo.Sort = saveView.Sort;
                goalGroupInfo.Modify(saveView.EmployeeID);
                // 更新其后的分组序号
                var groups = await _annualPlanGoalGroupRepository.GetAfterSortGroup(saveView.MainID, saveView.Sort);
                foreach (var group in groups)
                {
                    group.Sort += 1;
                    group.Modify(saveView.EmployeeID);
                }
                isUpdate = true;
            }
            if (isUpdate)
            {
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            return true;
        }
        /// <summary>
        /// 分组删除
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteAnnualGoalGroup(string groupID, string employeeID)
        {
            // 删除分组信息
            var goalGroupInfo = await _annualPlanGoalGroupRepository.GetGoalGroup(groupID);
            goalGroupInfo.Delete(employeeID);
            // 其后的分组序号减1
            var groups = await _annualPlanGoalGroupRepository.GetAfterSortGroup(goalGroupInfo.AnnualPlanMainID, goalGroupInfo.Sort);
            foreach (var group in groups)
            {
                group.Sort -= 1;
                group.Modify(employeeID);
            }
            // 删除关联的指标明细
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetIndicatorInfosByGroupID(groupID);
            indicatorDetails.ForEach(m => m.Delete(employeeID));
            // 删除关联的项目明细
            var projectDetails = await _annualPlanProjectDetailRepository.GetProjectInfosByGroupID(groupID);
            projectDetails.ForEach(m => m.Delete(employeeID));
            // 删除关联的项目明细所对应的执行项目及其明细
            var projectDetailIDs = await _annualPlanProjectDetailRepository.GetProjectDetailIDsByGroupID(groupID);
            var planMainID = projectDetails.FirstOrDefault()?.AnnualPlanMainID;
            await _annualPlanInterventionService.DeleteInterventionsByProjectDetailID(planMainID, employeeID, projectDetailIDs);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 分组排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> SortAPGroup(APGroupSortView sortView, string employeeID)
        {
            var groupInfos = await _annualPlanGoalGroupRepository.GetInfosByMainGoalID(sortView.MainGoalID);
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetInfosByMainGoalID(sortView.MainGoalID);
            var projectDetails = await _annualPlanProjectDetailRepository.GetInfosByMainGoalID(sortView.MainGoalID);

            // 创建更新 sort 函数
            static string expression(dynamic m) => m.AnnualPlanGoalGroupID;
            var updateGroupSort = CreateUpdateSortFunc(groupInfos, employeeID, expression);
            var updateIndicatorDetailSort = CreateUpdateSortFunc(indicatorDetails, employeeID, expression);
            var updateProjectDetailSort = CreateUpdateSortFunc(projectDetails, employeeID, expression);

            // 按照前端所给的分类-目标顺序更新 sort
            foreach (var group in sortView.APGroups)
            {
                updateGroupSort(group.GroupID);
                updateIndicatorDetailSort(group.GroupID);
                updateProjectDetailSort(group.GroupID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        #endregion
        #region 指标明细
        /// <summary>
        /// 获取某计划的指标明细
        /// </summary>
        /// <param name="view">查询View</param>
        /// <returns></returns>
        public async Task<List<APIndicatorDetail>> GetIndicatorDetails(APDetailsSearchView view)
        {
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetViewsByMainIDAndMainGoalIDs(view.MainID, view.MainGoalIDs);
            return indicatorDetails;
        }
        /// <summary>
        /// 获取某分组的指标明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        public async Task<List<APIndicatorDetail>> GetIndicatorDetailsByGroupID(string mainID, string groupID)
        {
            var views = await _annualPlanIndicatorDetailRepository.GetViewsByGroupID(mainID, groupID);
            return views.OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        /// 获取计划已参考的指标ID集合
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        public async Task<int[]> GetRefIndicatorIDs(string mainID)
        {
            var mainInfo = await _mainRepository.GetAnnualPlanMain(mainID);
            if (mainInfo == null)
            {
                return null;
            }
            var refIndicatorIDs = await _annualPlanIndicatorDetailRepository.GetRefIndicatorIDs(mainID);
            return refIndicatorIDs;
        }
        /// <summary>
        /// 获取某指标明细的历年情况
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">科室ID</param>
        /// <param name="indicatorID">指标ID</param>
        /// <returns></returns>
        public async Task<List<HistoryIndicator>> GetPastYearIndicators(int year, int departmentID, int indicatorID)
        {

            // TODO：待2025年时完善，显示24年指标完成情况。目前暂时使用空集合
            //      查找历年此指标的Remark，并从中解析出历年的指标完成情况
            var historyIndicators = Task.Run(() => new List<HistoryIndicator>());
            return await historyIndicators;
        }
        /// <summary>
        /// 新增指标明细
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <returns></returns>
        public async Task<bool> AddIndicatorDetail(SaveIndicatorDetailView addView)
        {
            // 若没有AnnualIndicatorID，新增指标字典
            if (addView.AnnualIndicatorID == 0)
            {
                var indicatorListView = new AnnualIndicatorListView
                {
                    DepartmentID = addView.DepartmentID,
                    IndicatorContent = addView.LocalShowName,
                    EnableYear = addView.Year,
                    HospitalID = addView.HospitalID,
                    Language = addView.Language,
                    AddEmployeeID = addView.EmployeeID,
                    EmployeeID = addView.EmployeeID
                };
                (_, addView.AnnualIndicatorID) = await _annualIndicatorListService.SaveAnnualIndicatorList(indicatorListView);
            }

            addView.GroupID = addView.GroupID.GetRealID(out _);
            var newIndicatorDetailInfo = CreateIndicatorDetailInfo(addView);
            await _unitOfWork.GetRepository<AnnualPlanIndicatorDetailInfo>().InsertAsync(newIndicatorDetailInfo);

            // 更新新增明细之后的序号
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetAfterSortDetail(addView.MainID, addView.Sort);
            foreach (var indicatorDetail in indicatorDetails)
            {
                indicatorDetail.Sort += 1;
                indicatorDetail.Modify(addView.EmployeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 创建指标明细Model实例
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <returns></returns>
        private static AnnualPlanIndicatorDetailInfo CreateIndicatorDetailInfo(SaveIndicatorDetailView addView)
        {
            var newIndicatorDetailInfo = new AnnualPlanIndicatorDetailInfo
            {
                LocalShowName = addView.LocalShowName,
                AnnualIndicatorID = addView.AnnualIndicatorID,
                AnnualPlanMainID = addView.MainID,
                AnnualPlanMainGoalID = addView.MainGoalID,
                AnnualPlanGoalGroupID = addView.GroupID,
                DepartmentID = addView.DepartmentID,
                Year = addView.Year,
                HospitalID = addView.HospitalID,
                Operator = addView.Operator ?? "",
                ReferenceValue = addView.ReferenceValue,
                Unit = addView.Unit ?? "",
                MarkID = addView.MarkID,
                Sort = addView.Sort,
                Remark = addView.Remark,
            };
            newIndicatorDetailInfo.Add(addView.EmployeeID).Modify(addView.EmployeeID);
            newIndicatorDetailInfo.DetailID = newIndicatorDetailInfo.GetId();
            return newIndicatorDetailInfo;
        }
        /// <summary>
        /// 更新指标明细
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <returns></returns>
        public async Task<bool> UpdateIndicatorDetail(UpdateIndicatorDetailView updateView)
        {
            var newDetail = updateView.NewDetailView;
            newDetail.GroupID = newDetail.GroupID.GetRealID(out _);
            var oldDetailInfo = await _annualPlanIndicatorDetailRepository.GetDetailByID(newDetail.DetailID);
            if (oldDetailInfo == null)
            {
                return false;
            }
            var isInfoUpdate = ObjUpdater<AnnualPlanIndicatorDetailInfo, SaveIndicatorDetailView>.UpdateWithSource(oldDetailInfo, newDetail);
            if (oldDetailInfo.AnnualPlanGoalGroupID != newDetail.GroupID)
            {
                isInfoUpdate = true;
                oldDetailInfo.AnnualPlanGoalGroupID = newDetail.GroupID;
            }
            if (oldDetailInfo.AnnualPlanMainGoalID != newDetail.MainGoalID)
            {
                isInfoUpdate = true;
                oldDetailInfo.AnnualPlanMainGoalID = newDetail.MainGoalID;
            }
            if (!isInfoUpdate)
            {
                return true;
            }
            oldDetailInfo.Modify(updateView.EmployeeID);
            if (updateView.ResortDetails == null || updateView.ResortDetails.Length <= 1)
            {
                return await _unitOfWork.SaveChangesAsync() > 0;
            }

            await UpdateDetailsSort(_annualPlanIndicatorDetailRepository, newDetail.MainID, updateView.ResortDetails, updateView.EmployeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 指标明细删除
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="indicatorDetailID">指标明细ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteIndicatorDetail(string mainID, string indicatorDetailID, string employeeID)
        {
            // 删除指标明细
            var detailInfo = await _annualPlanIndicatorDetailRepository.GetDetailByID(indicatorDetailID);
            detailInfo.Delete(employeeID);

            // 更新排序
            var details = await _annualPlanIndicatorDetailRepository.GetAfterSortDetail(mainID, detailInfo.Sort);
            // 不包括已被删除的指标明细，所以要从1开始
            for (int i = 1; i < details.Count; i++)
            {
                details[i].Sort -= 1;
                details[i].Modify(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        #endregion
        #region 项目明细
        /// <summary>
        /// 获取按MainGoalID分组的项目明细集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalIDs">计划目标表IDs</param>
        /// <returns></returns>
        public async Task<List<APProjectDetail>> GetProjectDetails(string mainID, string[] mainGoalIDs = null)
        {
            var projectDetails = await _annualPlanProjectDetailRepository.GetViewsByPlanMainID(mainID, mainGoalIDs);
            return projectDetails;
        }
        /// <summary>
        /// 获取某分组的项目明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        public async Task<List<APProjectDetail>> GetProjectDetailsByGroupID(string mainID, string groupID)
        {
            var views = await _annualPlanProjectDetailRepository.GetViewsByGroupID(mainID, groupID);
            return views.OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        /// 新增项目明细
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <returns></returns>
        public async Task<bool> AddProjectDetail(SaveProjectDetailView addView)
        {
            addView.GroupID = addView.GroupID.GetRealID(out _);
            // 实例化项目明细Model实例
            var newProjectDetailInfo = new AnnualPlanProjectDetailInfo
            {
                DetailID = addView.DetailID.GetRealID(out _),
                Content = addView.Content,
                AnnualPlanMainID = addView.MainID,
                AnnualPlanMainGoalID = addView.MainGoalID,
                AnnualPlanGoalGroupID = addView.GroupID,
                HospitalID = addView.HospitalID,
                MarkID = addView.MarkID,
                Sort = addView.Sort
            };
            newProjectDetailInfo.Add(addView.EmployeeID).Modify(addView.EmployeeID);
            await _unitOfWork.GetRepository<AnnualPlanProjectDetailInfo>().InsertAsync(newProjectDetailInfo);

            // 更新的序号
            var projectDetails = await _annualPlanProjectDetailRepository.GetAfterSortDetail(addView.MainID, addView.Sort);
            foreach (var projectDetail in projectDetails)
            {
                projectDetail.Sort += 1;
                projectDetail.Modify(addView.EmployeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 更新工作项目
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <returns></returns>
        public async Task<bool> UpdateProjectDetail(UpdateProjectDetailView updateView)
        {
            var newDetail = updateView.NewDetailView;
            newDetail.GroupID = newDetail.GroupID.GetRealID(out _);
            // 获取项目明细信息
            var projectDetailInfo = await _annualPlanProjectDetailRepository.GetDetailByID(newDetail.DetailID);
            var isUpdate = ObjUpdater<AnnualPlanProjectDetailInfo, SaveProjectDetailView>.UpdateWithSource(projectDetailInfo, newDetail);
            if (projectDetailInfo.AnnualPlanGoalGroupID != newDetail.GroupID)
            {
                isUpdate = true;
                projectDetailInfo.AnnualPlanGoalGroupID = newDetail.GroupID;
            }
            if (projectDetailInfo.AnnualPlanMainGoalID != newDetail.MainGoalID)
            {
                isUpdate = true;
                projectDetailInfo.AnnualPlanMainGoalID = newDetail.MainGoalID;
            }
            if (isUpdate)
            {
                projectDetailInfo.Modify(updateView.EmployeeID);
            }
            if (updateView.ResortDetails == null || updateView.ResortDetails.Length == 0)
            {
                return await _unitOfWork.SaveChangesAsync() > 0;
            }

            await UpdateDetailsSort(_annualPlanProjectDetailRepository, newDetail.MainID, updateView.ResortDetails, updateView.EmployeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 明细顺序重排
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="repository">查询仓储</param>
        /// <param name="mainID">主表ID</param>
        /// <param name="reSortDetails">新明细顺序</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private static async Task UpdateDetailsSort<T>(IBaseAPDetailRepository repository, string mainID, T[] reSortDetails, string employeeID) where T : IBaseAPDetail
        {
            // 按照前端的Sort更新明细
            var minSort = reSortDetails.First().Sort;
            var maxSort = reSortDetails.Last().Sort;
            var oldDetails = await repository.GetRangeInfosByMainID(mainID, minSort, maxSort);
            foreach (var reSortDetail in reSortDetails)
            {
                var oldDetail = oldDetails.FirstOrDefault(m => m.DetailID == reSortDetail.DetailID);
                if (oldDetail == null || oldDetail.Sort == reSortDetail.Sort)
                {
                    continue;
                }
                oldDetail.Sort = reSortDetail.Sort;
                (oldDetail as dynamic).Modify(employeeID);
            }
        }
        /// <summary>
        /// 项目明细删除
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteProjectDetail(string mainID, string projectDetailID, string employeeID)
        {
            // 删除项目明细
            var detail = await _annualPlanProjectDetailRepository.GetDetailByID(projectDetailID);
            detail?.Delete(employeeID);
            // 删除关联执行项目
            await _annualPlanInterventionService.DeleteInterventionsByProjectDetailID(detail?.AnnualPlanMainID, employeeID, projectDetailID);
            // 重新排序
            var details = await _annualPlanProjectDetailRepository.GetAfterSortDetail(mainID, detail.Sort);
            // 不包括已被删除的明细，所以要从1开始
            for (int i = 1; i < details.Count; i++)
            {
                details[i].Sort -= 1;
                details[i].Modify(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        #endregion
        #region 年度计划分类目标维护
        /// <summary>
        /// 获取年度计划分类-目标
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanMainGoalView>> GetAnnualPlanMainGoalList(int departmentID, int year)
        {
            var returnViews = new List<AnnualPlanMainGoalView>();
            var annualPlanMainID = await _mainRepository.GetMainIDByDeptIDAndYear(departmentID, year);
            if (string.IsNullOrEmpty(annualPlanMainID))
            {
                return returnViews;
            }
            var annualPlanMainGoalList = await _mainGoalRepository.GetInfosByPlanMainID(annualPlanMainID);
            if (annualPlanMainGoalList == null || annualPlanMainGoalList.Count() <= 0)
            {
                return returnViews;
            }
            //获取年度计划字典
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();
            var employeeIDs = annualPlanMainGoalList.Select(m => m.AddEmployeeID).Concat(annualPlanMainGoalList.Select(m => m.ModifyEmployeeID)).Distinct().ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            foreach (var annualPlanMainGoal in annualPlanMainGoalList)
            {
                var returnView = new AnnualPlanMainGoalView()
                {
                    AnnualPlanMainID = annualPlanMainGoal.AnnualPlanMainID,
                    AnnualPlanMainGoalID = annualPlanMainGoal.AnnualPlanMainGoalID,
                    AnnualPlanTypeID = annualPlanMainGoal.AnnualPlanTypeID,
                    AnnualPlanGoalID = annualPlanMainGoal.AnnualGoalID,
                    Sort = annualPlanMainGoal.Sort,
                    AddDateTime = annualPlanMainGoal.AddDateTime,
                    AddEmployeeID = annualPlanMainGoal.AddEmployeeID,
                    ModifyDateTime = annualPlanMainGoal.ModifyDateTime,
                    ModifyEmployeeID = annualPlanMainGoal.ModifyEmployeeID,
                    AnnualPlanGoalContent = goalList.Find(m => m.AnnualGoalID == annualPlanMainGoal.AnnualGoalID)?.GoalContent ?? "",
                    DepartmentID = goalList.Find(m => m.AnnualGoalID == annualPlanMainGoal.AnnualGoalID)?.DepartmentID ?? 0,
                    AnnualPlanTypeContent = typeList.Find(m => m.AnnualPlanTypeID == annualPlanMainGoal.AnnualPlanTypeID)?.AnnualPlanTypeContent ?? "",
                    AddEmployeeName = employees.TryGetValue(annualPlanMainGoal.AddEmployeeID, out var addEmployeeName) ? addEmployeeName : string.Empty,
                    ModifyEmployeeName = employees.TryGetValue(annualPlanMainGoal.ModifyEmployeeID, out var modifyEmployeeName) ? modifyEmployeeName : string.Empty,
                };
                returnViews.Add(returnView);
            }
            return returnViews.OrderBy(m => m.AnnualPlanTypeContent).ToList();
        }
        /// <summary>
        /// 获取分类-目标表的排序集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int>> GetAPMainGoalSortDict(string mainID)
        {
            return await _mainGoalRepository.GetMainGoalsSort(m => m.AnnualPlanMainID == mainID);
        }
        /// <summary>
        /// 获取上级部门针对某一目标制定的项目内容集合
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年度</param>
        /// <param name="goalID">目标ID</param>
        /// <returns></returns>
        public async Task<List<DictItem>> GetSuperiorProjectDetail(int departmentID, int year, int goalID)
        {
            // "4" 为红色三角标识
            var markIDs = new List<string> { "" };
            // 默认 没有连续上级部门的（例如片区） - 取护理部
            var upperDepartmentID = 405;
            var upperDepartment = await _departmentListRepository.GetUpperDepartment(departmentID);
            if (upperDepartment != null && upperDepartment.DepartmentID != 0)
            {
                upperDepartmentID = upperDepartment.DepartmentID;
            }
            var planMainID = await _mainRepository.GetMainIDByDeptIDAndYear(upperDepartmentID, year);
            if (string.IsNullOrEmpty(planMainID))
            {
                return null;
            }
            var mainGoalID = await _mainGoalRepository.GetAnnualPlanMainGoalIDByGoalIDAndMainIDAsync(planMainID, goalID);
            if (string.IsNullOrEmpty(mainGoalID))
            {
                return null;
            }
            //根据记录ID和目标获取
            var projectDetailContent = await _annualPlanProjectDetailRepository
                    .GetProjectDetailContentAsNoTracking(planMainID, markIDs, mainGoalID);
            return projectDetailContent.Select(m => new DictItem { Key = 0, Value = m }).ToList();
        }
        /// <summary>
        /// 发布年度计划
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> PublishAnnualPlan(string mainID, string employeeID)
        {
            var mainInfo = await _mainRepository.GetInfoByMainID(mainID);
            if (mainInfo == null)
            {
                return false;
            }
            mainInfo.StatusCode = ((int)AnnualPlanStatus.Published).ToString();
            mainInfo.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        #endregion
    }
}
