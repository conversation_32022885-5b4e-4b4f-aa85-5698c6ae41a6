﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.ViewModels;
using StackExchange.Redis;
using System.IO.Compression;
using System.Text;

namespace NursingManagement.Data
{
    public class RedisService : IRedisService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IDistributedCache _cache;
        private readonly IMemoryCache _memoryCache;
        private IOptions<SystemConfig> _sysConfig;
        private readonly ICacheFactory _cacheFactory;
        /// <summary>
        /// 设定内存过期时间为一周 60*60*24*7
        /// </summary>
        private const int EXPIRATION_SECONDS = 604800;

        public RedisService(
            IDistributedCache cache,
             IMemoryCache memoryCache,
               IOptions<SystemConfig> options,
               ICacheFactory cacheFactory
            )
        {
            _cache = cache;
            _memoryCache = memoryCache;
            _sysConfig = options;
            _cacheFactory = cacheFactory;
        }

        /// <summary>
        /// 增加缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSeconds">缓存多少秒到期，默认6小时到期</param>
        /// <param name="value"></param>
        /// <returns></returns>
        public async Task<bool> Add<T>(string key, int expirationSeconds, T value)
        {
            byte[] val = null;
            if (value.ToString() != "")
            {
                string data = JsonConvert.SerializeObject(value);

                val = Encoding.UTF8.GetBytes(data);
            }
            if (val == null)
            {
                return false;
            }
            //添加缓存      
            if (expirationSeconds <= 0)
            {
                expirationSeconds = EXPIRATION_SECONDS;
            }
            // 压缩数据
            byte[] compressedVal = Compress(val);
            TimeSpan expiration = TimeSpan.FromSeconds(expirationSeconds);
            await _cache.SetAsync(key, compressedVal, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration
            });
            //刷新缓存
            await _cache.RefreshAsync(key);
            return true;
        }

        /// <summary>
        /// 压缩方法
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private byte[] Compress(byte[] data)
        {
            using (var outputStream = new MemoryStream())
            {
                using (var gzipStream = new GZipStream(outputStream, CompressionLevel.Optimal))
                {
                    gzipStream.Write(data, 0, data.Length);
                }
                return outputStream.ToArray();
            }
        }

        /// <summary>
        /// 解压方法
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private byte[] Decompress(byte[] data)
        {
            using (var inputStream = new MemoryStream(data))
            using (var gzipStream = new GZipStream(inputStream, CompressionMode.Decompress))
            using (var outputStream = new MemoryStream())
            {
                gzipStream.CopyTo(outputStream);
                return outputStream.ToArray();
            }
        }
        public async Task<bool> Exists(string key)
        {
            bool check = true;

            byte[] val = await _cache.GetAsync(key);

            if (val == null || val.Length == 0)
            {
                check = false;
            }
            return check;
        }

        public async Task<T> GetAsync<T>(string key)
        {
            T datas = default(T);
            if (string.IsNullOrEmpty(key))
            {
                return datas;
            }
            var value = await _cache.GetAsync(key);
            if (value != null)
            {
                byte[] decompressedData = Decompress(value);
                string str = Encoding.UTF8.GetString(decompressedData);
                datas = JsonConvert.DeserializeObject<T>(str);
            }
            return datas;
        }

        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, Func<Task<T>> datas)
        {
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                return memoryData;
            }
            var cacheDatas = await datas.Invoke();

            await Add(key, expirationSeconds, cacheDatas);

            return cacheDatas;
        }

        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, int language, Func<int, Task<T>> datas)
        {
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                return memoryData;
            }

            var cacheDatas = await datas.Invoke(language);

            await Add(key, expirationSeconds, cacheDatas);

            return cacheDatas;
        }

        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, string hospitalID, Func<string, Task<T>> datas)
        {
            //获取缓存
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                if (memoryData is List<object> temp && temp.Count() > 0)
                {
                    return memoryData;
                }
                if (memoryData is not List<object>)
                {
                    return memoryData;
                }
            }

            //查新数据库
            var cacheDatas = await datas.Invoke(hospitalID);

            await Add(key, expirationSeconds, cacheDatas);

            return cacheDatas;
        }
        public async Task<T> GetOrCreateAsync<T>(string key, int expirationSeconds, string hospitalID, int language, Func<string, int, Task<T>> datas)
        {
            //获取内存缓存
            var memoryData = await GetAsync<T>(key);

            if (memoryData != null)
            {
                return memoryData;
            }
            var cacheDatas = await datas.Invoke(hospitalID, language);
            await Add(key, expirationSeconds, cacheDatas);

            return cacheDatas;
        }

        public async Task<bool> Update<T>(string key, int expirationSeconds, T value)
        {
            bool check = false;
            if (value == null)
            {
                return check;
            }
            if (key != "" || key != null)
            {
                if (await Remove(key))
                {
                    check = await Add(key, expirationSeconds, value.ToString());
                }
            }
            return check;
        }

        /// <summary>
        /// 根据传入的CacheMame 模糊比对删除缓存
        /// </summary>
        /// <param name="cacheName">传入all，清除所有缓存</param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<(bool, string)> RemoveCacheByNameAsync(string cacheName, string hospitalID, int language)
        {
           var result = await RemoveCacheByFuzzyKey(cacheName, hospitalID);
            var messageStr = $"成功回收缓存数量{result.Count}个,清单{ListToJson.ToJson(result)}";
            return (true, messageStr);
        }

        public async Task<bool> Remove(string key)
        {
            bool check = false;
            if (key != "" || key != null)
            {
                await _cache.RemoveAsync(key);
                if (await Exists(key) == false)
                {
                    check = true;
                }
            }
            return check;
        }

        /// <summary>
        /// 预加载所有缓存
        /// </summary>
        /// <returns></returns>
        public async Task<bool> PreloadingAllCache(string hospitalID)
        {
            bool flag = false;
            var keys = _cacheFactory.Get();
            dynamic cacheQuery = new DynamicDictionary();
            cacheQuery.PreloadingFlag = "True";
            cacheQuery.HospitalID = hospitalID;
            foreach (string key in keys)
            {
                flag = await _cacheFactory.Create(key).GetCacheAsync(cacheQuery) != null;
            }
            return flag;
        }

        /// <summary>
        /// 模糊查找Key,并且清除,all 清除所有缓存
        /// </summary>
        /// <param name="keyStr"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<List<string>> RemoveCacheByFuzzyKey(string keyStr, string hospitalID)
        {
            var keys = GetDateBaseKeys();
            var result = new List<string>();
            hospitalID = "_H" + hospitalID;
            foreach (var item in keys)
            {
                if (!item.ToString().Contains(hospitalID))
                {
                    continue;
                }
                if (item.Contains(keyStr) || keyStr.ToUpper() == "ALL")
                {
                    var flag = await Remove(item);
                    if (flag)
                    {
                        result.Add(item);
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 获取Redis链接
        /// </summary>
        /// <returns></returns>
        public List<string> GetDateBaseKeys()
        {
            var redisConnection = _sysConfig.Value.RedisConnection;
            // 创建 Redis 连接   
            var options = ConfigurationOptions.Parse(redisConnection);
            var connection = ConnectionMultiplexer.Connect(options);
            var keys = connection.GetServers().First().Keys().ToList();
            connection.Close();
            var keysStr = new List<string>();
            foreach (var item in keys)
            {
                keysStr.Add(item.ToString());
            }
            return keysStr;
        }
    }
}