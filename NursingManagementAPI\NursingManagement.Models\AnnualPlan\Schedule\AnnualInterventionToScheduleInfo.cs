﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    ///  年度计划排程与执行项目关系表    
    /// </summary>
    [Table("APInterventionDetailToSchedule")]
    [Obsolete("此表废弃")]
    public class APInterventionDetailToScheduleInfo : MutiModifyInfo
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int APInterventionDetailToScheduleID { get; set; }

        /// <summary>
        /// 排程ID    
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualScheduleMainID { get; set; }

        /// <summary>
        /// 执行项目字典ID
        /// </summary>
        public int InterventionID { get; set; }

        /// <summary>
        /// 年度计划ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }

        /// <summary>
        /// 制定序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualInterventionMainID { get; set; }

        /// <summary>
        /// 制定明细序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanInterventionDetailID { get; set; }

        /// <summary>
        /// 年度
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public byte Month { get; set; }

        /// <summary>
        /// 执行人
        /// </summary>
        public string Performer { get; set; }
    }
}
