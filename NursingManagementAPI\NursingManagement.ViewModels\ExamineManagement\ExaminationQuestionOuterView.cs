﻿namespace NursingManagement.ViewModels
{
    public class ExaminationQuestionOuterView
    {
        /// <summary>
        /// 题库ID
        /// </summary>
        public string QuestionBankID { get; set; }

        /// <summary>
        /// 新增导入的题目（已经跟新增的题库相关联）
        /// </summary>
        public List<BankAndQuestionTreeView> BankAndQuestionTree { get; set; }
        /// <summary>
        ///  导入当前题库的题目
        /// </summary>
        public List<ExaminationQuestionView> QuestionList { get; set; }
    }


    public class BankAndQuestionTreeView
    {
        /// <summary>
        /// 题库临时ID（唯一标识）
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 题库名称
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 题库层级
        /// </summary>
        public int Level { get; set; }
        /// <summary>
        /// 父级题库ID
        /// </summary>
        public string ParentID { get; set; }
        // 子节点的列表
        public List<BankAndQuestionTreeView> Children { get; set; }
        /// <summary>
        /// 新增导入的题目（已经跟新增的题库相关联）
        /// </summary>
        public List<ExaminationQuestionView> Questions { get; set; }
    }

}
