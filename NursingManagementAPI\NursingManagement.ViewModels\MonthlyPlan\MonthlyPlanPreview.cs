using static NursingManagement.Models.AnnualPlanEnums;

namespace NursingManagement.ViewModels.MonthlyPlan
{
  public class MonthlyPlanPreview
  {
    /// <summary>
    /// 月度计划主表ID
    /// </summary>
    public string MonthlyPlanMainID { get; set; }

    /// <summary>
    /// 分类分组
    /// </summary>
    public PlanType[] PlanTypes { get; set; }

    /// <summary>
    /// 分类分组
    /// </summary>
    public class PlanType
    {
      /// <summary>
      /// 分类ID
      /// </summary>
      public int TypeId { get; set; }

      /// <summary>
      /// 工作集合
      /// </summary>
      public PlanWork[] PlanWorks { get; set; }

      /// <summary>
      /// 工作
      /// </summary>
      public class PlanWork
      {
        /// <summary>
        /// 工作内容
        /// </summary>
        public string WorkContent { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string Requirement { get; set; }

        /// <summary>
        /// 负责人集合
        /// </summary>
        public string[] PrincipalIDs { get; set; }

        /// <summary>
        /// 负责人名称
        /// </summary>
        public string PrincipalName { get; set; }

        /// <summary>
        /// 工作类型
        /// </summary>
        public WorkType WorkType { get; set; }
      }
    }
  }
}
