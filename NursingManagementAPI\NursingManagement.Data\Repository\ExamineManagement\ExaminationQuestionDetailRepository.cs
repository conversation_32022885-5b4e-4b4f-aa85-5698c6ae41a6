﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class ExaminationQuestionDetailRepository : IExaminationQuestionDetailRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ExaminationQuestionDetailRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="questionDetailID"></param>
        /// <returns></returns>
        public async Task<ExaminationQuestionDetailInfo> GetDataByID(string questionDetailID)
        {
            return await _nursingManagementDbContext.ExaminationQuestionDetailInfos
                .FirstOrDefaultAsync(m => m.ExaminationQuestionDetailID == questionDetailID);
        }

        /// <summary>
        /// 根据题目ID获取答案
        /// </summary>
        /// <param name="examinationQuestionID"></param>
        /// <returns></returns>
        public async Task<List<ExaminationQuestionDetailInfo>> GetListByQuestionID(int examinationQuestionID)
        {
            return await _nursingManagementDbContext.ExaminationQuestionDetailInfos
                .Where(m => m.ExaminationQuestionID == examinationQuestionID && m.DeleteFlag != "*")
                .ToListAsync();
        }

        /// <summary>
        /// 根据是否是正确答案获取答案列表
        /// </summary>
        /// <param name="answerFlag"></param>
        /// <returns></returns>
        public async Task<List<ExaminationQuestionDetailInfo>> GetListByCorrectFlag(bool answerFlag)
        {
            return await _nursingManagementDbContext.ExaminationQuestionDetailInfos
                .Where(m => m.AnswerFlag == answerFlag && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 根据多个问题ID获取答案
        /// </summary>
        /// <param name="examinationQuestionIDs">多个问题的ID</param>
        /// <returns>字典，包含问题ID和对应的答案集合。</returns>
        public async Task<Dictionary<int, List<ItemDetailListView>>> GetAnswersByQuestionIDs(List<int> examinationQuestionIDs)
        {
            var answers = await _nursingManagementDbContext.ExaminationQuestionDetailInfos
                .Where(answer => examinationQuestionIDs.Contains(answer.ExaminationQuestionID) && answer.DeleteFlag != "*")
                .Select(answer => new ItemDetailListView
                {
                    ItemDetailID = answer.ExaminationQuestionDetailID,
                    Content = answer.Content,
                    ItemID = answer.ExaminationQuestionID,
                    SelectFlag = answer.AnswerFlag,
                    Sort = answer.Sort,
                })
                .ToListAsync();
            return answers.GroupBy(a => a.ItemID).ToDictionary(group => group.Key, group => group.OrderBy(m=>m.Sort).ToList());
        }
        /// <summary>
        /// 根据问题ID集合获取明细数据
        /// </summary>
        /// <param name="examinationQuestionIDs"></param>
        /// <param name="asNoTrack">是否跟踪</param>
        /// <returns></returns>
        public async Task<List<ExaminationQuestionDetailInfo>> GetListByQuestionIDs(List<int> examinationQuestionIDs, bool asNoTrack = false)
        {
            var query = _nursingManagementDbContext.ExaminationQuestionDetailInfos.Where(answer => examinationQuestionIDs.Any(n => n == answer.ExaminationQuestionID) && answer.DeleteFlag != "*");
            if (asNoTrack)
            {
                return await query.AsNoTracking().ToListAsync();
            }
            return await query.ToListAsync();
        }
    }
}
