
﻿using NursingManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 审批业务记录表
        /// </summary>
        public DbSet<ApproveRecordInfo> ApproveRecordInfos { get; set; }
        /// <summary>
        /// 审批明细表
        /// </summary>
        public DbSet<ApproveDetailInfo> ApproveDetailInfos { get; set; }
        /// <summary>
        /// 审批流程表
        /// </summary>
        public DbSet<ApproveProcessInfo> ApproveProcessInfos { get; set; }
        /// <summary>
        /// 审批流程节点表
        /// </summary>
        public DbSet<ApproveProcessNodeInfo> ApproveProcessNodeInfos { get; set; }
        /// <summary>
        /// 审批流程节点明细表
        /// </summary>
        public DbSet<ApproveProcessNodeDetailInfo> ApproveProcessNodeDetailInfos { get; set; }
        /// 审批业务主表ID
        /// </summary>
        public DbSet<ApproveMainInfo> ApproveMainInfos { get; set; }
        /// <summary>
        /// 科室对审批流程设置表
        /// </summary>
        public DbSet<DepartmentToApproveProcessInfo> DepartmentToApproveProcessInfos { get; set; }


        
    }
}
