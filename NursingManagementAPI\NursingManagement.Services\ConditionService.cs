﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class ConditionService : IConditionService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly IConditionMainRepository _conditionMainRepository;
        private readonly IConditionDetaiRepository _conditionDetaiRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRuleListRepository _ruleListRepository;
        private readonly IRuleDetailListRepository _ruleDetailListRepository;
        private readonly IComponentListRepository _componentListRepository;
        private readonly IExaminationQuestionRepository _examinationQuestionRepository;
        private readonly IQuestionBankRepository _questionBankRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IEmployeeRepository _employeeRepository;

        private static readonly int COMPONENT_LIST_ID_111 = 111;

        public ConditionService(
            ISettingDictionaryRepository settingDictionaryRepository
            , ICapabilityLevelRepository capabilityLevelRepository
            , IDictionaryService dictionaryService
            , IConditionMainRepository conditionMainRepository
            , IConditionDetaiRepository conditionDetaiRepository
            , IUnitOfWork unitOfWork
            , IRuleListRepository ruleListRepository
            , IRuleDetailListRepository ruleDetailListRepository
            , IComponentListRepository componentListRepository
            , IExaminationQuestionRepository examinationQuestionRepository
            , IQuestionBankRepository questionBankRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IEmployeeRepository employeeRepository
            )
        {
            _settingDictionaryRepository = settingDictionaryRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
            _dictionaryService = dictionaryService;
            _conditionMainRepository = conditionMainRepository;
            _conditionDetaiRepository = conditionDetaiRepository;
            _unitOfWork = unitOfWork;
            _ruleListRepository = ruleListRepository;
            _ruleDetailListRepository = ruleDetailListRepository;
            _componentListRepository = componentListRepository;
            _examinationQuestionRepository = examinationQuestionRepository;
            _questionBankRepository = questionBankRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _employeeRepository = employeeRepository;
        }

        /// <summary>
        /// 获取选择人员条件配置数据
        /// </summary>
        /// <returns></returns>
        public async Task<ConditionCommonView> GetEmployeeConditionSetting()
        {
            var conditionCommonView = new ConditionCommonView();
            var items = new List<Dictionary<string, string>>();
            var settingParams = new SettingDictionaryParams()
            {
                SettingType = "Common",
                SettingTypeCode = "AdvanceManagement"
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            if (settingList == null || settingList.Count <= 0)
            {
                return conditionCommonView;
            }
            //EQUALS: 等于; NOT_EQUALS: 不等于; GREATER_THAN: 大于; LESS_THAN: 小于;
            //GREATER_THAN_OR_EQUALS:大于等于; LESS_THAN_OR_EQUALS:小于等于;
            //EMPTY:为空; INCLUDES:包含; EXCLUDE:不包含; RANGE:范围
            var conditionList = new List<string>() { "EQUALS", "NOT_EQUALS", "GREATER_THAN", "LESS_THAN", "GREATER_THAN_OR_EQUALS", "LESS_THAN_OR_EQUALS" };
            var cascaderConditionList = new List<string>() { "INCLUDES", "EXCLUDE" };
            var conditionTypes = new Dictionary<string, List<string>>();
            var conditionProps = new Dictionary<string, ConditionPropsView>();
            var capabilityLevelSetting = await _capabilityLevelRepository.GetByCacheAsync();
            var departmenList = await _dictionaryService.GetDepartmentCascaderList("1", []);
            var stringDepartmebtList = new List<CascaderView<string>>();
            foreach (var item in departmenList)
            {
                var newDepartment = ConvertValueToString(item);
                stringDepartmebtList.Add(newDepartment);
            }
            foreach (var setting in settingList)
            {
                var item = new Dictionary<string, string>
                {
                    { "label", setting.Description },
                    { "id", setting.Sort.ToString() },
                };
                items.Add(item);
                var prop = new ConditionPropsView();
                if (setting.SettingValue == "Department")
                {
                    conditionTypes.Add(setting.Sort.ToString(), cascaderConditionList);
                    prop.Type = "cascaderSelector";
                    prop.Options = stringDepartmebtList;
                }
                if (setting.SettingValue == "CapabilityLevel")
                {
                    conditionTypes.Add(setting.Sort.ToString(), conditionList);
                    prop.Type = "selector";
                    prop.Options = capabilityLevelSetting.Select(m => new SelectOptionsView
                    {
                        Label = m.CapabilityLevelName,
                        Value = m.CapabilityLevelID.ToString()
                    }).ToList();
                }
                conditionProps.Add(setting.Sort.ToString(), prop);
            }
            conditionCommonView.Items = items;
            conditionCommonView.ConditionProps = conditionProps;
            conditionCommonView.ConditionTypes = conditionTypes;
            return conditionCommonView;
        }

        /// <summary>
        /// 转换级联数据类型（int =&gt; string）
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public static CascaderView<string> ConvertValueToString(CascaderView<int> item)
        {
            var view = new CascaderView<string>();
            var chiledrenView = new List<CascaderView<string>>();
            // 将 Value 字段从 T 类型转换为 string 类型
            view.Value = item.Value.ToString();
            view.Label = item.Label.ToString();
            // 递归处理 Children 列表中的每个子项
            if (item.Children != null && item.Children.Count > 0)
            {
                foreach (var child in item.Children)
                {
                    chiledrenView.Add(ConvertValueToString(child)); // 递归调用自身处理子项
                }
            }
            view.Children = chiledrenView;
            return view;
        }

        /// <summary>
        /// 保存条件主数据和条件明细数据
        /// </summary>
        /// <param name="view"></param>
        /// <param name="saveChange">默认在方法中保存</param>
        /// <returns></returns>
        public async Task<string> HandleConditionData(HandleConditionView view, bool saveChange = true)
        {
            var conditionMainID = "";
            if (view.AddFlag)
            {
                conditionMainID = await CreateNewConditionAsync(view);
            }
            else
            {
                var mainData = await _conditionMainRepository.GetDataBySourceID(view.SourceID, view.SourceType);
                if (mainData != null)
                {
                    conditionMainID = mainData.ConditionMainID;
                    mainData.ConditionExpression = view.ConditionExpression;
                    if (view.ConditionContent.Length > 1500)
                    {
                        mainData.ConditionContent = "点击查看详情";
                    }
                    else
                    { 
                        mainData.ConditionContent = view.ConditionContent;
                    }
                    mainData.Description = view.Description;
                    mainData.Modify(view.ModifyEmployeeID);
                    await UpdateDetailData(mainData.ConditionMainID, view);
                }
                else
                {
                    conditionMainID = await CreateNewConditionAsync(view);
                }
            }
            if (saveChange)
            {
                await _unitOfWork.SaveChangesAsync();
            }
            return conditionMainID;
        }

        /// <summary>
        /// 更新条件明细
        /// </summary>
        /// <param name="conditionMainID"></param>
        /// <param name="view"></param>
        /// <returns></returns>
        private async Task UpdateDetailData(string conditionMainID, HandleConditionView view)
        {
            var detailData = await _conditionDetaiRepository.GetListByMainID(conditionMainID);
            foreach (var item in detailData)
            {
                item.Delete(view.ModifyEmployeeID);
            }
            await InserCondtionDetail(conditionMainID, view.Conditions, view.ModifyEmployeeID, ""); ;
        }

        /// <summary>
        /// 新增条件记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        private async Task<string> CreateNewConditionAsync(HandleConditionView view)
        {
            var mainInfo = InserConditionMainData(view);
            await InserCondtionDetail(mainInfo.ConditionMainID, view.Conditions, view.ModifyEmployeeID, "");
            return mainInfo.ConditionMainID;
        }

        /// <summary>
        /// 插入条件主表数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        private ConditionMainInfo InserConditionMainData(HandleConditionView view)
        {
            var mainInfo = new ConditionMainInfo()
            {
                ConditionMainID = Guid.NewGuid().ToString("N"),
                SourceID = view.SourceID,
                SourceType = view.SourceType,
                ConditionContent = view.ConditionContent,
                ConditionExpression = view.ConditionExpression,
                Description = view.Description,
                DeleteFlag = "",
                GroupType = view.GroupType,
                GroupTypeValue = view.GroupTypeValue,
                DataType = view.DataType,
                DataTypeValue = view.DataTypeValue,
            };
            if (mainInfo.ConditionContent.Length > 1500)
            {
                mainInfo.ConditionContent = "点击查看详情";
            }
            mainInfo.Add(view.ModifyEmployeeID);
            mainInfo.Modify(view.ModifyEmployeeID);
            _unitOfWork.GetRepository<ConditionMainInfo>().Insert(mainInfo);
            return mainInfo;
        }

        /// <summary>
        /// 插入条件明细表数据
        /// </summary>
        /// <param name="conditionMainID"></param>
        /// <param name="conditions"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <param name="parentID"></param>
        private async Task InserCondtionDetail(string conditionMainID, List<FormDetailConditionView> conditions, string modifyEmployeeID, string parentID)
        {
            if (conditions == null || conditions.Count <= 0)
            {
                return;
            }
            for (int i = 0; i < conditions.Count; i++)
            {
                if (!int.TryParse(conditions[i].ItemID, out int itemID))
                {
                    continue;
                }
                var detailInfo = new ConditionDetailInfo()
                {
                    ConditionDetailID = Guid.NewGuid().ToString("N"),
                    ConditionMainID = conditionMainID,
                    ItemID = itemID,
                    Condition = conditions[i].Condition ?? "",
                    ConditionValue = conditions[i].Value ?? "",
                    ConditionType = conditions[i].ConditionType ?? "",
                    Sort = i + 1,
                    ParentID = parentID,
                    DeleteFlag = ""
                };
                detailInfo.Add(modifyEmployeeID);
                detailInfo.Modify(modifyEmployeeID);
                await _unitOfWork.GetRepository<ConditionDetailInfo>().InsertAsync(detailInfo);
                if (conditions[i].Children != null && conditions[i].Children.Count > 0)
                {
                    await InserCondtionDetail(conditionMainID, conditions[i].Children, modifyEmployeeID, detailInfo.ConditionDetailID);
                }
            }
        }

        /// <summary>
        /// 处理选择的人员条件数据
        /// </summary>
        /// <param name="conditionDetailList"></param>
        /// <param name="emloyeeList"></param>
        /// <param name="allConditionDetailList"></param>
        /// <returns></returns>
        public async Task<List<EmployeeStaffDataInfo>> FilterConditionDetailList(List<ConditionDetailInfo> conditionDetailList, List<EmployeeStaffDataInfo> emloyeeList, List<ConditionDetailInfo> allConditionDetailList)
        {
            //EQUALS: 等于; NOT_EQUALS: 不等于; GREATER_THAN: 大于; LESS_THAN: 小于;
            //GREATER_THAN_OR_EQUALS:大于等于; LESS_THAN_OR_EQUALS:小于等于;
            //EMPTY:为空; INCLUDES:包含; EXCLUDE:不包含; RANGE:范围
            var employeeViewList = new List<EmployeeStaffDataInfo>();
            conditionDetailList = conditionDetailList.OrderBy(m => m.Sort).ToList();
            var itemIDList = conditionDetailList.Select(m => m.ItemID).ToList();
            var ruleList = await _ruleListRepository.GetListyID(itemIDList);
            int count = 1;
            foreach (var conditionDetail in conditionDetailList)
            {
                var ruleData = ruleList.FirstOrDefault(m => m.RuleListID == conditionDetail.ItemID);
                if (ruleData == null)
                {
                    continue;
                }
                // 条件明细选择为层级，为或条件
                if (ruleData.RuleCode == "CapabilityLevel" && conditionDetail.ConditionType != "and")
                {
                    var emloyeeDatas = HandleByCapabilityLevelCondtion(conditionDetail.Condition, conditionDetail.ConditionValue, emloyeeList);
                    employeeViewList.AddRange(emloyeeDatas);
                    count++;
                }
                // 条件明细选择为部门，为或条件
                if (ruleData.RuleCode == "Department" && conditionDetail.ConditionType != "and")
                {
                    var emloyeeDatas = HandleByDepartment(conditionDetail.Condition, conditionDetail.ConditionValue, emloyeeList);
                    employeeViewList.AddRange(emloyeeDatas);
                    count++;
                }
                // 条件明细选择为层级，为与条件
                if (ruleData.RuleCode == "CapabilityLevel" && conditionDetail.ConditionType == "and")
                {
                    employeeViewList = HandleByCapabilityLevelCondtion(conditionDetail.Condition, conditionDetail.ConditionValue, count > 1 ? employeeViewList : emloyeeList);
                    count++;
                }
                // 条件明细选择为部门，为与条件
                if (ruleData.RuleCode == "Department" && conditionDetail.ConditionType == "and")
                {
                    employeeViewList = HandleByDepartment(conditionDetail.Condition, conditionDetail.ConditionValue, count > 1 ? employeeViewList : emloyeeList);
                    count++;
                }
                // 条件明细选择为部门，为与条件
                if (ruleData.RuleCode == "Employee")
                {
                    var tempEmployeeList = conditionDetail.ConditionType == "and" && count > 1 ? employeeViewList : emloyeeList;
                    employeeViewList = HandleByEmployee(conditionDetail.Condition, conditionDetail.ConditionValue, tempEmployeeList);
                    count++;
                }
                // 条件明细有自条件递归调用此方法，为或条件
                if (conditionDetail.ItemID == 0 && conditionDetail.ConditionType != "and")
                {
                    var childrenConditionList = allConditionDetailList.Where(m => m.ParentID == conditionDetail.ConditionDetailID).ToList();
                    var childrenEmployeeList = await FilterConditionDetailList(childrenConditionList, emloyeeList, allConditionDetailList);
                    employeeViewList.AddRange(childrenEmployeeList);
                }
                // 条件明细有自条件递归调用此方法，为与条件
                if (conditionDetail.ItemID == 0 && conditionDetail.ConditionType == "and")
                {
                    var chiledreaConditionList = allConditionDetailList.Where(m => m.ParentID == conditionDetail.ConditionDetailID).ToList();
                    employeeViewList = await FilterConditionDetailList(chiledreaConditionList, employeeViewList, allConditionDetailList);
                }
            }
            return employeeViewList;
        }

        /// <summary>
        /// 根据选择层级条件筛选数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="conditionValue"></param>
        /// <param name="emloyeeList"></param>
        /// <returns></returns>
        private List<EmployeeStaffDataInfo> HandleByCapabilityLevelCondtion(string condition, string conditionValue, List<EmployeeStaffDataInfo> emloyeeList)
        {
            var levelIDs = new List<int>();
            if (int.TryParse(conditionValue.Replace("\"", ""), out int levelID))
            {
                levelIDs = [levelID];
            }
            else
            {
                levelIDs = ListToJson.ToList<List<int>>(conditionValue);
            }
            switch (condition)
            {
                case "EQUALS" when levelIDs.Count == 1:
                    emloyeeList = emloyeeList.Where(m => m.CapabilityLevelID == levelIDs[0]).ToList();
                    break;
                case "NOT_EQUALS":
                    emloyeeList = emloyeeList.Where(m => !levelIDs.Contains(m.CapabilityLevelID ?? 0)).ToList();
                    break;
                case "GREATER_THAN" when levelIDs.Count == 1:
                    emloyeeList = emloyeeList.Where(m => m.CapabilityLevelID > levelIDs[0]).ToList();
                    break;
                case "LESS_THAN" when levelIDs.Count == 1:
                    emloyeeList = emloyeeList.Where(m => m.CapabilityLevelID < levelIDs[0]).ToList();
                    break;
                case "GREATER_THAN_OR_EQUALS" when levelIDs.Count == 1:
                    emloyeeList = emloyeeList.Where(m => m.CapabilityLevelID >= levelIDs[0]).ToList();
                    break;
                case "LESS_THAN_OR_EQUALS" when levelIDs.Count == 1:
                    emloyeeList = emloyeeList.Where(m => m.CapabilityLevelID <= levelIDs[0]).ToList();
                    break;
                case "INCLUDES":
                    emloyeeList = emloyeeList.Where(m => m.CapabilityLevelID.HasValue && levelIDs.Contains(m.CapabilityLevelID.Value)).ToList();
                    break;
                default:
                    break;
            }
            return emloyeeList;
        }

        /// <summary>
        /// 根据选择部门条件筛选数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="conditionValue"></param>
        /// <param name="emloyeeList"></param>
        /// <returns></returns>
        private List<EmployeeStaffDataInfo> HandleByDepartment(string condition, string conditionValue, List<EmployeeStaffDataInfo> emloyeeList)
        {
            var departmentIDs = new List<int>();
            if (int.TryParse(conditionValue.Replace("\"", ""), out int departmentID))
            {
                departmentIDs = [departmentID];
            }
            else
            {
                departmentIDs = ListToJson.ToList<List<int>>(conditionValue);
            }
            switch (condition)
            {
                case "INCLUDES":
                    emloyeeList = emloyeeList.Where(m => departmentIDs.Contains(m.DepartmentID ?? 0)).ToList();
                    break;
                case "EXCLUDE":
                    emloyeeList = emloyeeList.Where(m => !departmentIDs.Contains(m.DepartmentID ?? 0)).ToList();
                    break;
                default:
                    break;
            }
            return emloyeeList;
        }

        /// <summary>
        /// 根据选择部门条件筛选数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="conditionValue"></param>
        /// <param name="emloyeeList"></param>
        /// <returns></returns>
        private static List<EmployeeStaffDataInfo> HandleByEmployee(string condition, string conditionValue, List<EmployeeStaffDataInfo> emloyeeList)
        {
            switch (condition)
            {
                case "EQUALS":
                    var employeeID = ListToJson.ToList<string>(conditionValue);
                    emloyeeList = emloyeeList.Where(m => m.EmployeeID == employeeID).ToList();
                    break;
                case "INCLUDES":
                    var employeeIDs = ListToJson.ToList<List<string>>(conditionValue);
                    emloyeeList = [.. emloyeeList.Where(m => employeeIDs.Contains(m.EmployeeID))];
                    break;
                default:
                    break;
            }
            return emloyeeList;
        }

        /// <summary>
        /// 获取条件配置数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="filterIDs"></param>
        /// <returns></returns>
        public async Task<ConditionCommonView> GetConditionSelectComponent(string type, List<string> filterIDs)
        {
            var ruleList = await _ruleListRepository.GetListByType(type);
            if (ruleList.Count <= 0)
            {
                return null;
            }
            var compositionPaperByCountRule = await _ruleListRepository.GetListByType("CompositionPaperByCount");

            var componentList = await _componentListRepository.GetComponentListByType("DynamicFilter");
            var conditionCommonView = new ConditionCommonView();
            var items = new List<Dictionary<string, string>>();
            var conditionTypes = new Dictionary<string, List<string>>();
            var conditionProps = new Dictionary<string, ConditionPropsView>();
            foreach (var rule in ruleList)
            {
                var item = new Dictionary<string, string>
                {
                    { "label", rule.ShowName },
                    { "id", rule.RuleListID.ToString() },
                    { "unit", rule.SystemType == "CompositionPaperByDifficultyLevel" ? "%" : "" }
                };
                items.Add(item);
                conditionTypes.Add(rule.RuleListID.ToString(), [.. rule.ConditionCode.Split(",")]);
                var prop = await GetConditionProps(rule, componentList, filterIDs, compositionPaperByCountRule);
                if (prop != null)
                {
                    // 考核计划支持 条件多选
                    if (type == "ExaminationByRule" || type == "ExaminationRecord" || type == "TrainingRecord" || type == "CompositionPaperByRequiredChoice")
                    {
                        prop.Multiple = true;
                    }
                    conditionProps.Add(rule.RuleListID.ToString(), prop);
                }
            }
            conditionCommonView.Items = items;
            conditionCommonView.ConditionProps = conditionProps;
            conditionCommonView.ConditionTypes = conditionTypes;
            return conditionCommonView;
        }

        /// <summary>
        /// 获取条件配置prop
        /// </summary>
        /// <param name="rule">规则信息</param>
        /// <param name="componentList">组件信息</param>
        /// <param name="filterIDs">过滤器 ID 列表</param>
        /// <param name="compositionPaperByCountRule">按计数规则的组成规则列表</param>
        /// <returns>条件属性视图</returns>
        private async Task<ConditionPropsView> GetConditionProps(RuleListInfo rule, List<ComponentListInfo> componentList, List<string> filterIDs, List<RuleListInfo> compositionPaperByCountRule)
        {
            var componentData = componentList.FirstOrDefault(m => m.ComponentListID == rule.ComponentListID);
            if (componentData == null)
            {
                return null;
            }
            return rule.RuleCode switch
            {
                "Department" => await GetDepartmentConditionProps(componentData),
                "CapabilityLevel" => await GetCapabilityLevelConditionProps(componentData),
                "ChoiceQuestion" => await GetChoiceQuestionConditionProps(componentData, filterIDs, compositionPaperByCountRule),
                "QuestionBank" => await GetQuestionBankConditionProps(componentData),
                "Employee" => await GetEmployeeConditionProps(componentData),
                _ when componentData.ControlerType == "selector" => await GetSelectorConditionProps(rule, componentData),
                _ => new ConditionPropsView { Type = componentData.ControlerType },
            };
        }

        /// <summary>
        /// 获取部门条件属性视图
        /// </summary>
        /// <param name="componentData">组件信息</param>
        /// <returns>条件属性视图</returns>
        private async Task<ConditionPropsView> GetDepartmentConditionProps(ComponentListInfo componentData)
        {
            var departmentList = await _dictionaryService.GetDepartmentCascaderList("1", []);
            var stringDepartmentList = departmentList.Select(item => ConvertValueToString(item)).ToList();

            return new ConditionPropsView
            {
                Type = componentData.ControlerType,
                Options = stringDepartmentList,
            };
        }

        /// <summary>
        /// 获取能力级别条件属性视图
        /// </summary>
        /// <param name="componentData">组件信息</param>
        /// <returns>条件属性视图</returns>
        private async Task<ConditionPropsView> GetCapabilityLevelConditionProps(ComponentListInfo componentData)
        {
            var capabilityLevelSetting = await _capabilityLevelRepository.GetByCacheAsync();

            return new ConditionPropsView
            {
                Type = componentData.ControlerType,
                Options = capabilityLevelSetting.Select(m => new SelectOptionsView
                {
                    Label = m.CapabilityLevelName,
                    Value = m.CapabilityLevelID.ToString()
                }).ToList(),
            };
        }

        /// <summary>
        /// 获取选择题条件属性视图
        /// </summary>
        /// <param name="componentData">组件信息</param>
        /// <param name="filterIDs">过滤器 ID 列表</param>
        /// <param name="compositionPaperByCountRule">按计数规则的组成规则列表</param>
        /// <returns>条件属性视图</returns>
        private async Task<ConditionPropsView> GetChoiceQuestionConditionProps(ComponentListInfo componentData, List<string> filterIDs, List<RuleListInfo> compositionPaperByCountRule)
        {
            var questionList = await _examinationQuestionRepository.GetListByQuestionBankIDList(filterIDs);

            return new ConditionPropsView
            {
                Type = componentData.ControlerType,
                Options = questionList.Select(m => new SelectOptionsView
                {
                    Label = m.QuestionContent,
                    Value = m.ExaminationQuestionID.ToString(),
                    Type = compositionPaperByCountRule.Find(n => n.RuleCode == m.ExaminationQuestionType)?.RuleListID + ""
                }).ToList(),
            };
        }

        /// <summary>
        /// 获取题库条件属性视图
        /// </summary>
        /// <param name="componentData">组件信息</param>
        /// <returns>条件属性视图</returns>
        private async Task<ConditionPropsView> GetQuestionBankConditionProps(ComponentListInfo componentData)
        {
            var questionBankList = await _questionBankRepository.GetQuestionBankDictAsync();

            return new ConditionPropsView
            {
                Type = componentData.ControlerType,
                Options = questionBankList,
            };
        }

        /// <summary>
        /// 获取员工条件属性视图
        /// </summary>
        /// <param name="componentData">组件信息</param>
        /// <returns>条件属性视图</returns>
        private async Task<ConditionPropsView> GetEmployeeConditionProps(ComponentListInfo componentData)
        {
            var employees = await _dictionaryService.GetEmployeeDict(null, false);

            return new ConditionPropsView
            {
                Type = componentData.ControlerType,
                Options = employees,
            };
        }

        /// <summary>
        /// 获取选择器条件属性视图
        /// </summary>
        /// <param name="rule">规则信息</param>
        /// <param name="componentData">组件信息</param>
        /// <returns>条件属性视图</returns>
        private async Task<ConditionPropsView> GetSelectorConditionProps(RuleListInfo rule, ComponentListInfo componentData)
        {
            var ruleDetailList = await _ruleDetailListRepository.GetListByID(rule.RuleListID);

            return new ConditionPropsView
            {
                Type = componentData.ControlerType,
                Options = ruleDetailList.Select(m => new SelectOptionsView
                {
                    Label = m.Value,
                    Value = m.RuleDetailListID.ToString()
                }).ToList(),
            };
        }

        /// <summary>
        /// 保存条件数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> SaveConditionData(HandleConditionView view)
        {
            if (view.AddFlag)
            {
                var mainInfo = InserConditionMainData(view);
                await InserCondtionDetail(mainInfo.ConditionMainID, view.Conditions, view.ModifyEmployeeID, "");
            }
            else
            {
                var mainData = await _conditionMainRepository.GetDataBySourceID(view.SourceID, view.SourceType);
                mainData.ConditionExpression = view.ConditionExpression;
                mainData.ConditionContent = view.ConditionContent;
                mainData.Description = view.Description;
                mainData.Modify(view.ModifyEmployeeID);
                var detailData = await _conditionDetaiRepository.GetListByMainID(mainData.ConditionMainID);
                foreach (var item in detailData)
                {
                    item.Delete(view.ModifyEmployeeID);
                }
                await InserCondtionDetail(mainData.ConditionMainID, view.Conditions, view.ModifyEmployeeID, "");
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 根据来源ID删除条件
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <param name="sourceType">来源类别</param>
        /// <param name="userID">工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteConditionMainAndDetails(string sourceID, string sourceType, string userID)
        {
            var conditionMain = await _conditionMainRepository.GetDataBySourceID(sourceID, sourceType);
            if (conditionMain == null)
            {
                return false;
            }
            var conditionDetails = await _conditionDetaiRepository.GetListByMainID(conditionMain.ConditionMainID);
            conditionMain.Delete(userID);
            conditionDetails.ForEach(detail => detail.Delete(userID));
            return true;
        }

        #region 规则主表及明细表维护

        /// <summary>
        /// 获取规则属性
        /// </summary>
        /// <param name="systemType">规则使用系统类型</param>
        /// <returns></returns>
        public async Task<List<RuleView>> GetRuleListByType(string systemType)
        {
            var returnViews = new List<RuleView>();
            var ruleList = await _ruleListRepository.GetListByType(systemType);
            if (ruleList.Count() <= 0)
            {
                _logger.Warn($"未获取到相关规则属性列表，SystemType：{systemType}");
                return returnViews;
            }
            var componentList = await _componentListRepository.GetComponentListByType("DynamicFilter");
            var conditionSettingParams = new SettingDictionaryParams()
            {
                SettingType = "Common",
                SettingTypeCode = "RuleList",
                SettingTypeValue = "ConditionCode"
            };
            var conditionSetting = await _settingDictionaryRepository.GetSettingDictionary(conditionSettingParams);
            var systemTypeParams = new SettingDictionaryParams()
            {
                SettingType = "Common",
                SettingTypeCode = "RuleList",
                SettingTypeValue = "SystemType"
            };
            var systemTypeSetting = await _settingDictionaryRepository.GetSettingDictionary(systemTypeParams);
            var ruleListIDs = ruleList.Select(m => m.RuleListID).ToList();
            var ruleDetails = await _ruleDetailListRepository.GetListByIDs(ruleListIDs);
            var employeeIDs = ruleList.Select(m => m.ModifyEmployeeID).ToList();
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            foreach (var rule in ruleList)
            {
                var returnView = CreateRuleView(systemType, componentList, conditionSetting, systemTypeSetting, ruleDetails, employeeList, rule);
                if (returnView == null)
                {
                    continue;
                }
                returnViews.Add(returnView);
            }
            return returnViews;
        }

        /// <summary>
        /// 创建返回前端View
        /// </summary>
        /// <param name="systemType">规则使用系统类型</param>
        /// <param name="componentList">组件集合</param>
        /// <param name="conditionSetting">条件组配置</param>
        /// <param name="systemTypeSetting">使用系统类型配置</param>
        /// <param name="ruleDetails">规则明细数据</param>
        /// <param name="employeeList">员工信息集合</param>
        /// <param name="rule">规则记录</param>
        /// <returns></returns>
        private static RuleView CreateRuleView(string systemType, List<ComponentListInfo> componentList, List<SettingDictionaryInfo> conditionSetting, List<SettingDictionaryInfo> systemTypeSetting, List<RuleDetailListInfo> ruleDetails, List<EmployeePersonalDataListView> employeeList, RuleListInfo rule)
        {
            var component = componentList.FirstOrDefault(m => m.ComponentListID == rule.ComponentListID);
            if (component == null)
            {
                return null;
            }
            var defaultValue = rule.DefaultValue;
            // 默认值的明细ID集合
            var defaultDetailIDs = new List<int>();
            var details = ruleDetails.Where(m => m.RuleListID == rule.RuleListID).ToList();
            var conditionCodes = rule.ConditionCode.Split(",").ToList();
            var conditionDescriptions = conditionSetting.Where(m => conditionCodes.Contains(m.SettingValue)).Select(m => m.Description).ToList();
            if (component.ControlerType == "selector" && !string.IsNullOrEmpty(rule.DefaultValue))
            {
                defaultDetailIDs = rule.DefaultValue.Split(',').Select(int.Parse).ToList();
                var defaultDetailValue = details.Where(m => defaultDetailIDs.Contains(m.RuleDetailListID)).Select(m => m.Value).ToList();
                defaultValue = string.Join(",", defaultDetailValue);
            }
            var ruleDetailListView = details.Select(m => new ItemDetailListView
            {
                ItemDetailID = m.RuleDetailListID.ToString(),
                Content = m.Value,
                ItemID = m.RuleListID,
                SelectFlag = defaultDetailIDs.Contains(m.RuleDetailListID)
            }).ToList();
            var returnView = new RuleView
            {
                RuleListID = rule.RuleListID,
                ShowName = rule.ShowName,
                Description = rule.Description,
                DefaultValue = defaultValue,
                ComponentListID = component.ComponentListID,
                ComponentType = component.Description,
                ConditionCode = conditionCodes,
                Condition = string.Join(",", conditionDescriptions),
                SystemType = systemType,
                SystemTypeName = systemTypeSetting.FirstOrDefault(m => m.SettingValue == rule.SystemType)?.Description ?? "",
                RuleDetail = ruleDetailListView,
                ModifyEmployeeName = employeeList.Find(n => n.EmployeeID == rule.ModifyEmployeeID)?.EmployeeName ?? rule.ModifyEmployeeID,
                ModifyDateTime = rule.ModifyDateTime,
                RuleCode = rule.RuleCode
            };
            return returnView;
        }

        /// <summary>
        /// 删除规则数据
        /// </summary>
        /// <param name="ruleListID">规则主记录ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> DeleteRule(int ruleListID, Session session)
        {
            var ruleInfo = await _ruleListRepository.GetRuleDataByID(ruleListID);
            if (ruleInfo == null)
            {
                _logger.Error($"未获取到规则数据，RuleListID：{ruleListID}");
                return false;
            }
            ruleInfo.Delete(session.EmployeeID);
            var ruleDetailList = await _ruleDetailListRepository.GetListByID(ruleListID);
            ruleDetailList.ForEach(detail => detail.Delete(session.EmployeeID));

            _unitOfWork.GetRepository<RuleListInfo>().Update(ruleInfo);
            _unitOfWork.GetRepository<RuleDetailListInfo>().Update(ruleDetailList);
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await _ruleListRepository.UpdateCache();
                await _ruleDetailListRepository.UpdateCache();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 保存规则及明细数据
        /// </summary>
        /// <param name="ruleView">规则及明细数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> SaveRule(RuleView ruleView, Session session)
        {
            if (ruleView == null)
            {
                _logger.Error("保存规则及明细数据失败，前端传递参数为空！！");
                return false;
            }
            var isAddFlag = ruleView.RuleListID == 0;
            var ruleListInfo = await _ruleListRepository.GetRuleDataByID(ruleView.RuleListID);
            if (!isAddFlag && ruleListInfo == null)
            {
                _logger.Error($"修改规则失败，未找到相关记录，RuleListID：{ruleView.RuleListID}");
                return false;
            }
            var maxID = await _ruleListRepository.GetMaxID();
            var ruleListID = isAddFlag ? (maxID + 1) : ruleView.RuleListID;
            var (successFlag, defaultValueIDs) = await SaveRuleDetailList(ruleView, session, ruleListID);
            if (successFlag)
            {
                _ = !isAddFlag
                    ? await UpdateRuleListInfo(ruleView, session, ruleListInfo, defaultValueIDs)
                    : await CreateRuleListInfo(ruleView, session, maxID, defaultValueIDs);
            }
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                await _ruleListRepository.UpdateCache();
                await _ruleDetailListRepository.UpdateCache();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 创建规则主表数据
        /// </summary>
        /// <param name="ruleView">规则及明细数据</param>
        /// <param name="session">缓存</param>
        /// <param name="maxID">规则主表最大键</param>
        /// <param name="defaultValueIDs">明细主键（默认值）</param>
        /// <returns></returns>
        private async Task<bool> CreateRuleListInfo(RuleView ruleView, Session session, int maxID, List<int> defaultValueIDs)
        {
            var defaultValue = ruleView.ComponentListID == COMPONENT_LIST_ID_111 && defaultValueIDs.Count > 0 ? string.Join(",", defaultValueIDs) : ruleView.DefaultValue;
            //新增规则主表数据
            var ruleListInfo = new RuleListInfo
            {
                RuleListID = maxID + 1,
                RuleCode = "",
                ShowName = ruleView.ShowName,
                Description = ruleView.Description,
                ComponentListID = ruleView.ComponentListID,
                DefaultValue = defaultValue,
                ConditionCode = string.Join(",", ruleView.ConditionCode),
                SystemType = ruleView.SystemType,
                HospitalID = session.HospitalID,
                Language = session.Language,
                DeleteFlag = ""
            };
            ruleListInfo.Add(session.EmployeeID);
            ruleListInfo.Modify(session.EmployeeID);
            await _unitOfWork.GetRepository<RuleListInfo>().InsertAsync(ruleListInfo);
            return true;
        }

        /// <summary>
        /// 修改规则主表数据
        /// </summary>
        /// <param name="ruleView">规则及明细数据</param>
        /// <param name="session">缓存</param>
        /// <param name="ruleListInfo">规则主表记录</param>
        /// <param name="defaultValueIDs">明细主键（默认值）</param>
        /// <returns></returns>
        private async Task<bool> UpdateRuleListInfo(RuleView ruleView, Session session, RuleListInfo ruleListInfo, List<int> defaultValueIDs)
        {
            //默认值默认为空
            var defaultValue = "";
            //是下拉框，根据是否勾选默认值组装
            if (ruleView.ComponentListID == COMPONENT_LIST_ID_111 && defaultValueIDs.Count > 0)
            {
                defaultValue = string.Join(",", defaultValueIDs);
            }
            //不是下拉框，直接使用默认值
            if (ruleView.ComponentListID != COMPONENT_LIST_ID_111)
            {
                defaultValue = ruleView.DefaultValue;
                //由下拉框切换为其他类型，删除对应明细
                if (ruleListInfo.ComponentListID == COMPONENT_LIST_ID_111)
                {
                    var details = await _ruleDetailListRepository.GetListByID(ruleView.RuleListID);
                    details.ForEach(detail => detail.Delete(session.EmployeeID));
                    _unitOfWork.GetRepository<RuleDetailListInfo>().Update(details);
                }
            }
            ruleListInfo.ShowName = ruleView.ShowName;
            ruleListInfo.Description = ruleView.Description;
            ruleListInfo.ComponentListID = ruleView.ComponentListID;
            ruleListInfo.DefaultValue = defaultValue;
            ruleListInfo.ConditionCode = string.Join(",", ruleView.ConditionCode);
            ruleListInfo.SystemType = ruleView.SystemType;
            ruleListInfo.Modify(session.EmployeeID);
            _unitOfWork.GetRepository<RuleListInfo>().Update(ruleListInfo);
            return true;
        }

        /// <summary>
        /// 保存规则明细数据
        /// </summary>
        /// <param name="ruleView">规则及明细数据</param>
        /// <param name="session">缓存</param>
        /// <param name="ruleListID">规则主表主键ID</param>
        /// <returns></returns>
        private async Task<(bool, List<int>)> SaveRuleDetailList(RuleView ruleView, Session session, int ruleListID)
        {
            var detailList = new List<RuleDetailListInfo>();
            var defaultValue = new List<int>();
            if (ruleView.ComponentListID != COMPONENT_LIST_ID_111)
            {
                return (true, defaultValue);
            }
            var details = await _ruleDetailListRepository.GetListByID(ruleView.RuleListID);
            var detailMaxID = await _ruleDetailListRepository.GetMaxID();
            // 存放在原来基础上修改的数据
            var updateDetailIDs = new List<int>();
            int count = 1;
            foreach (var detail in ruleView.RuleDetail)
            {
                if (string.IsNullOrEmpty(detail.Content))
                {
                    continue;
                }
                var ruleDetailListInfo = details.FirstOrDefault(m => m.RuleDetailListID.ToString().Trim() == detail.ItemDetailID.Trim());
                if (ruleDetailListInfo == null)
                {
                    var ruleDetail = CreateRuleDetailListInfo(session, ruleListID, detailMaxID, count, detail);
                    detailList.Add(ruleDetail);
                }
                else
                {
                    ruleDetailListInfo.Sort = count;
                    ruleDetailListInfo.Value = detail.Content;
                    ruleDetailListInfo.Modify(session.EmployeeID);
                    updateDetailIDs.Add(ruleDetailListInfo.RuleDetailListID);
                    _unitOfWork.GetRepository<RuleDetailListInfo>().Update(ruleDetailListInfo);
                }
                if (detail.SelectFlag)
                {
                    defaultValue.Add(ruleDetailListInfo == null ? detailMaxID + count : ruleDetailListInfo.RuleDetailListID);
                }
                count++;
            }
            if (detailList.Count > 0)
            {
                await _unitOfWork.GetRepository<RuleDetailListInfo>().InsertAsync(detailList);
            }
            var needDeleteDetails = details.Where(m => !updateDetailIDs.Contains(m.RuleDetailListID)).ToList();
            needDeleteDetails.ForEach(detail => detail.Delete(session.EmployeeID));
            _unitOfWork.GetRepository<RuleDetailListInfo>().Update(needDeleteDetails);
            return (await _unitOfWork.SaveChangesAsync() >= 0, defaultValue);
        }

        /// <summary>
        /// 创建规则明细数据
        /// </summary>
        /// <param name="session">缓存</param>
        /// <param name="ruleListID">规则主键ID</param>
        /// <param name="detailMaxID">规则明细表最大值</param>
        /// <param name="count">第几个明细（排序使用）</param>
        /// <param name="detail">前端传递明细数据</param>
        /// <returns></returns>
        private static RuleDetailListInfo CreateRuleDetailListInfo(Session session, int ruleListID, int detailMaxID, int count, ItemDetailListView detail)
        {
            var ruleDetail = new RuleDetailListInfo
            {
                RuleDetailListID = detailMaxID + count,
                RuleListID = ruleListID,
                Value = detail.Content,
                Language = session.Language,
                Sort = count,
                DeleteFlag = ""
            };
            ruleDetail.Add(session.EmployeeID);
            ruleDetail.Modify(session.EmployeeID);
            return ruleDetail;
        }

        /// <summary>
        ///获取条件筛选题目类型和分数时使用的 表格呈现格式数据
        /// </summary>
        /// <returns></returns>
        public async Task<object> GetConditionTableFormat()
        {
            List<string> ruleTypes = ["CompositionPaperByCount", "CompositionPaperBySingleScore"];
            var ruleLists = await _ruleListRepository.GetListByTypeList(ruleTypes);
            var respDataList = new List<object>();
            var settingDictionaryParam = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationQuestion",
                SettingTypeValue = "ExaminationQuestionType",
            };
            var questionTypeSettings = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParam);
            if (questionTypeSettings == null)
            {
                _logger.Error($"获取题型配置失败,参数为{ListToJson.ToJson(settingDictionaryParam)}");
                return null;
            }
            foreach (var item in questionTypeSettings)
            {
                var countRule = ruleLists.Find(m => m.RuleCode == $"{item.SettingValue}Count");
                var scoreRule = ruleLists.Find(m => m.RuleCode == $"{item.SettingValue}Score");
                if (countRule == null || scoreRule == null)
                {
                    continue;
                }
                respDataList.Add(new ConditionTablePropView
                {
                    Group = item.SettingValue,
                    Label = item.Description,
                    Prop = countRule.RuleListID,
                    Children = [
                        new ConditionTablePropView
                        {
                            Group = item.SettingValue,
                            Label = countRule.ShowName,
                            Prop = countRule.RuleListID,
                        },
                        new ConditionTablePropView
                        {
                            Group = item.SettingValue,
                            Label = scoreRule.ShowName,
                            Prop = scoreRule.RuleListID,
                        }
                    ]
                });
            }
            return respDataList;
        }

        #endregion
    }
}
