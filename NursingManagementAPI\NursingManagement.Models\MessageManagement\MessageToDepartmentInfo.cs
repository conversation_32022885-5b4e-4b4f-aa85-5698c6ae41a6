﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("MessageToDepartment")] 
    public class MessageToDepartmentInfo : MutiModifyInfo
    {       
        /// <summary>
        /// 消息记录表主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string MessageRecordID { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 部门序号
        /// </summary>
        public int DepartmentID { get; set; }
    }
}
