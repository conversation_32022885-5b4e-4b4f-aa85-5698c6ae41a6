diff a/NursingManagementAPI/NursingManagement.API/NursingManagement.API/Controllers/ExamineController.cs b/NursingManagementAPI/NursingManagement.API/NursingManagement.API/Controllers/ExamineController.cs	(rejected hunks)
@@ -755,7 +755,6 @@
              result.Data = await _examineService.GetPracticeExamineMainList(employeeID ?? session.EmployeeID, statusCode, startDate, endDate);
              return result.ToJson();
          }
-         #endregion
 -        /// <summary>
 -        /// 根据题库ID集合获取每个题库中各个题型的题目数量
 -        /// </summary>
