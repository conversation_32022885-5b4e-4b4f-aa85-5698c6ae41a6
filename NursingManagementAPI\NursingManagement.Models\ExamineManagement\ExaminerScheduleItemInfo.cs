﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 监考安排项目表
    /// </summary>
    [Table("ExaminerScheduleItem")]
    public class ExaminerScheduleItemInfo : MutiModifyInfo
    {
        /// <summary>
        /// 监考安排项目ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminerScheduleItemID { get; set; }
        /// <summary>
        /// 监考安排ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminerScheduleID { get; set; }
        /// <summary>
        /// 考试记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationRecordID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}