﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 调班记录保存| 前端传参
    /// </summary>
    public class AdjustScheduleParamView
    {
        /// <summary>
        /// 调班记录ID
        /// </summary>
        public string AdjustScheduleRecordID { get; set; }

        /// <summary>
        /// 申请调班日期
        /// </summary>
        public DateTime AdjustDate { get; set; }

        /// <summary>
        /// 申请调整岗位，根据申请人和申请日期自动提取已经排好的值班岗
        /// </summary>
        public string AdjustDepartmentPost { get; set; }

        /// <summary>
        /// 调班目标日期
        /// </summary>
        public DateTime TargetDate { get; set; }

        /// <summary>
        /// 调班目标人员ID
        /// </summary>
        public string TargetEmployeeID { get; set; }

        /// <summary>
        /// 目标岗位，根据目标人和目标日期自动提取已经排好的值班岗
        /// </summary>
        public string TargetDepartmentPost { get; set; }

        /// <summary>
        /// 申请审核流程状态
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// 申请原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 申请午别
        /// </summary>
        public string AdjustNoonType { get; set; }

        /// <summary>
        /// 换班午别
        /// </summary>
        public string TargetNoonType { get; set; }

        /// <summary>
        /// 自动排班开关（true打开）
        /// </summary>
        public bool? AutoSchedule { get; set; }
    }
}
