﻿namespace NursingManagement.ViewModels
{
    public class SavePostDescriptionView
    {
        /// <summary>
        /// 部门岗位编号
        /// </summary>
        public int PostID { get; set; }
        /// <summary>
        ///  部门
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号，主键
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言序号，主键
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 岗位编码，护理部制定的编码
        /// </summary>
        public string PostDescriptionCode { get; set; }
        /// <summary>
        /// 岗位说明书名称
        /// </summary>
        public string PostDescriptionName { get; set; }
        /// <summary>
        /// 直接上级
        /// </summary>
        public string Superiors { get; set; }
        /// <summary>
        /// 直接下级
        /// </summary>
        public string Junior { get; set; }
        /// <summary>
        /// 岗位定员
        /// </summary>
        public string PostNumber { get; set; }
        /// <summary>
        /// 所辖人数
        /// </summary>
        public string HeadCount { get; set; }
        /// <summary>
        /// 制定部门ID
        /// </summary>
        public int CreateDepartmentID { get; set; }
        /// <summary>
        /// 版本号；1.0.1，当前修改的为启用状态，则自动新增版本，
        /// </summary>
        public string Version { get; set; }
        /// <summary>
        /// 状态 0：停用、1：启用（签发）、2：待审核、3、审核
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 审核人，HREmployeeID
        /// </summary>
        public string Approver { get; set; }
        /// <summary>
        /// 审核日期
        /// </summary>
        public DateTime? ApproveDateTime { get; set; }
        /// <summary>
        /// 签发人，HREmployeeID
        /// </summary>
        public string Signer { get; set; }
        /// <summary>
        /// 签发/发布日期
        /// </summary>
        public DateTime? SignDateTime { get; set; }
        /// <summary>
        /// 从业资格要求
        /// </summary>
        public string QualificationRequirements { get; set; }
        /// <summary>
        /// 教育水平
        /// </summary>
        public string EducationalLevel { get; set; }
        /// <summary>
        /// 培训经历
        /// </summary>
        public string TrainingRecord { get; set; }
        /// <summary>
        /// 其他
        /// </summary>
        public string Other { get; set; }
        /// <summary>
        /// 岗位职责
        /// </summary>
        public string Responsibility { get; set; }
        /// <summary>
        /// 绩效评价
        /// </summary>
        public string PerformanceEvaluation { get; set; }
        /// <summary>
        /// 岗位SOP标准
        /// </summary>
        public string SOP { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>   
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 制定科室名称
        /// </summary>
        public string CreateDepartment { get; set; }
        /// <summary>
        /// 审批人姓名
        /// </summary>
        public string ApproverName { get; set; }
        /// <summary>
        /// 签发人姓名
        /// </summary>
        public string SignerName { get; set; }
        /// <summary>
        /// 异动人姓名(批量保存用)
        /// </summary>
        public string ModifyPerson { get; set; }
        /// <summary>
        /// 新增人姓名(批量保存用)
        /// </summary>
        public string AddEmployee { get; set; }
        /// <summary>
        /// 批量保存标志
        /// </summary>
        public bool BatcFlag { get; set; } = false;
    }
}
