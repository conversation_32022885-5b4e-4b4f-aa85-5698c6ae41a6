﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModel;
using NursingManagement.ViewModels;
using System.Reflection;

namespace NursingManagement.Services
{
    public class CriticalPatientVisitsService : ICriticalPatientVisitsService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPatientProfileRecordService _patientProfileRecordService;
        private readonly IDynamicTableSettingService _dynamicTableSettingService;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IHierarchicalQCService _hierarchicalQCService;
        private readonly IHierarchicalQCFormRepository _hierarchicalQCFormRepository;
        private readonly IPatientProfileDetailRepository _profileDetailRepository;
        private readonly IPatientProfileRecordRepository _profileRecordRepository;
        private readonly IHierarchicalQCSubjectRepository _hierarchicalQCSubjectRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IHierarchicalQCRecordRepository _hierarchicalQCRecordRepository;
        private readonly IHierarchicalQCDetailRepository _hierarchicalQCDetailRepository;
        private readonly IDynamicFormDetailRepository _dynamicFormDetailRepository;
        private readonly IHierarchicalQCMainRepository _hierarchicalQCMainRepository;
        public CriticalPatientVisitsService(
              IUnitOfWork unitOfWork
            , IFileService fileService
            , IPatientProfileRecordService patientProfileRecordService
            , IDynamicTableSettingService dynamicTableSettingService
            , ISettingDictionaryRepository settingDictionaryRepository
            , IHierarchicalQCService hierarchicalQCService
            , IHierarchicalQCFormRepository hierarchicalQCFormRepository
            , IPatientProfileDetailRepository profileDetailRepository
            , IPatientProfileRecordRepository profileRecordRepository
            , IHierarchicalQCSubjectRepository hierarchicalQCSubjectRepository
            , IDepartmentListRepository departmentListRepository
            , IHierarchicalQCRecordRepository hierarchicalQCRecordRepository
            , IHierarchicalQCDetailRepository hierarchicalQCDetailRepository
            , IDynamicFormDetailRepository dynamicFormDetailRepository
            , IHierarchicalQCMainRepository hierarchicalQCMainRepository

        )
        {
            _unitOfWork = unitOfWork;
            _patientProfileRecordService = patientProfileRecordService;
            _dynamicTableSettingService = dynamicTableSettingService;
            _settingDictionaryRepository = settingDictionaryRepository;
            _hierarchicalQCService = hierarchicalQCService;
            _hierarchicalQCFormRepository = hierarchicalQCFormRepository;
            _profileDetailRepository = profileDetailRepository;
            _profileRecordRepository = profileRecordRepository;
            _hierarchicalQCSubjectRepository = hierarchicalQCSubjectRepository;
            _departmentListRepository = departmentListRepository;
            _hierarchicalQCRecordRepository = hierarchicalQCRecordRepository;
            _hierarchicalQCDetailRepository = hierarchicalQCDetailRepository;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
            _hierarchicalQCMainRepository = hierarchicalQCMainRepository;
        }

        #region 组装访视记录数据
        /// <summary>
        /// 获取危重患者访视主记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<VisitsTableView> GetCriticalPatientVisitsRecord(GetPatientProfileRecordView view)
        {
            var returnList = new VisitsTableView()
            {
                TableData = [],
                TableHeader = []
            };
            var dynamicTableHeaderQueryView = new DynamicTableHeaderQueryView()
            {
                TableType = "PatientVisits",
                TableSubType = view.ProfileID + "-Record",
                HospitalID = view.HospitalID
            };
            returnList.TableHeader = await _dynamicTableSettingService.GetDynamicTableHeader(dynamicTableHeaderQueryView);
            if (returnList.TableHeader.Count == 0)
            {
                _logger.Error("动态表格配置未找到！dynamicTableHeaderQueryView||" + ListToJson.ToJson(dynamicTableHeaderQueryView));
                return returnList;
            }
            returnList.TableData = await GetCCCPatientData(view);
            return returnList;
        }
        /// <summary>
        /// 获取危重患者记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        private async Task<List<Dictionary<string, object>>> GetCCCPatientData(GetPatientProfileRecordView view)
        {
            var getCCCDataView = new GetCCCPatientProfileDataView()
            {
                StartDate = view.StartDate,
                EndDate = view.EndDate,
                StationIDs = view.StationIDs,
                ProfileID = view.ProfileID,
            };
            var cccData = await _patientProfileRecordService.GetCCCPatientProfileRecord(getCCCDataView, view.HospitalID);
            if (cccData.Count == 0)
            {
                return [];
            }
            var templateCode = string.Empty;
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "CriticalPatientVisitType",
                FilterSettingTypeValue = view.ProfileID,
            };
            var visitsFormID = await _settingDictionaryRepository.GetSettingValue(settingParams);
            if (!string.IsNullOrEmpty(visitsFormID))
            {
                var qcFormInfo = await _hierarchicalQCFormRepository.GetFormByID(Convert.ToInt32(visitsFormID));
                templateCode = qcFormInfo.TemplateCode;
            }
            var departmentMapping = await _departmentListRepository.GetNMDepartmentByHisDepartment("3", "1", view.HospitalID);
            var sourceIDList = cccData.Where(m => m.SourceType == "PatientEvent").Select(m => m.SourceID).ToList();
            var isSaveQcRecordIDList = await _profileRecordRepository.GetRelatedTableRecordIDList("PatientEvent", sourceIDList, "HierarchicalQCRecord");
            var recordIsReadStatus = await _hierarchicalQCMainRepository.GetQCRecordIsReadStatus(isSaveQcRecordIDList.Select(m => m["hierarchicalQCRecordID"].ToString()).ToList());
            return ConvertToDictionaryList(cccData, templateCode, Convert.ToInt32(visitsFormID), isSaveQcRecordIDList, departmentMapping, recordIsReadStatus);
        }
        /// <summary>
        /// view 转化为Dictionary
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <returns></returns>
        public static List<Dictionary<string, object>> ConvertToDictionaryList(List<PatientProfileRecordView> list, string templateCode, int visitsFormID
            , List<Dictionary<string, string>> isSaveQcRecordIDList, List<Dictionary<string, object>> departmentMapping
            , Dictionary<string, bool> recordIsReadStatus)
        {
            var dictList = new List<Dictionary<string, object>>();
            foreach (var item in list)
            {
                var dict = new Dictionary<string, object>();
                foreach (PropertyInfo prop in item.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
                {
                    if (prop.Name == "ProfileDetails")
                    {
                        foreach (var detail in item.ProfileDetails)
                        {
                            dict[ToLowerFirstChar(detail.DataID)] = detail.DataValue;
                        }
                    }
                    dict[ToLowerFirstChar(prop.Name)] = prop.GetValue(item, null);
                }
                dict["templateCode"] = templateCode;
                dict["visitsFormID"] = visitsFormID;
                var successRecord = isSaveQcRecordIDList.LastOrDefault(m => m["sourceID"] == item.SourceID);
                dict["hierarchicalQCSubjectID"] = successRecord?["hierarchicalQCSubjectID"] ?? "";
                dict["hierarchicalQCFormID"] = successRecord?["hierarchicalQCFormID"] ?? "";
                var sucDepartmentMapping = departmentMapping.FirstOrDefault(m => m["stationID"].ToString() == dict["stationID"].ToString());
                dict["nmDepartmentID"] = sucDepartmentMapping?["nmDepartmentID"];
                dict["nmDepartmentName"] = sucDepartmentMapping?["nmDepartmentName"];
                dict["upNmDepartmentID"] = sucDepartmentMapping?["upNmDepartmentID"];
                var occDepartmentMapping = departmentMapping.FirstOrDefault(m => m["stationID"].ToString() == dict["occurStationID"].ToString());
                dict["occDepartmentID"] = occDepartmentMapping?["nmDepartmentID"];
                dict["occurDepartmetName"] = occDepartmentMapping?["nmDepartmentName"];
                dict["lastVisitScore"] = (successRecord != null && successRecord.TryGetValue("result", out string value)) ? Convert.ToDecimal(value).ToString("0.#") : "";
                dict["isRead"] = successRecord != null && recordIsReadStatus.TryGetValue(successRecord?["hierarchicalQCRecordID"], out bool isRead) && isRead ? "已阅读" : string.Empty;
                dictList.Add(dict);
            }
            return dictList;
        }

        /// <summary>
        /// 首字符转化为小写
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        static string ToLowerFirstChar(string input)
        {
            if (string.IsNullOrEmpty(input) || char.IsLower(input[0]))
            {
                return input;
            }

            return char.ToLower(input[0]) + input.Substring(1);
        }

        /// <summary>
        /// 获取危重患者访视数量
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetCriticalPatientVisitsCount(GetPatientProfileRecordView view)
        {
            var getCCCDataView = new GetCCCPatientProfileDataView()
            {
                StartDate = view.StartDate,
                EndDate = view.EndDate,
                StationIDs = view.StationIDs,
                ProfileID = view.ProfileID,
            };
            var cccDatas = await _patientProfileRecordService.GetCCCPatientProfileRecord(getCCCDataView, view.HospitalID);
            if (cccDatas.Count == 0)
            {
                return [];
            }
            var departmentMapping = await _departmentListRepository.GetNMDepartmentByHisDepartment("3", "1", view.HospitalID);
            var sourceIDList = cccDatas.Where(m => m.SourceType == "PatientEvent").Select(m => m.SourceID).ToList();
            var isSaveQcRecordIDList = await _profileRecordRepository.GetRelatedTableRecordIDList("PatientEvent", sourceIDList, "HierarchicalQCRecord");
            var returnDict = new Dictionary<string, (int Total, int NoVisit)>();
            foreach (var item in cccDatas)
            {
                var successRecord = isSaveQcRecordIDList.LastOrDefault(m => m["sourceID"] == item.SourceID);
                var occDepartmentMapping = departmentMapping.FirstOrDefault(m => m["stationID"].ToString() == item.OccurStationID.ToString());
                var nmDepartmentID = occDepartmentMapping?["nmDepartmentID"]?.ToString();
                // 初始化部门统计（如果不存在）
                if (!returnDict.ContainsKey(nmDepartmentID))
                {
                    returnDict[nmDepartmentID] = (0, 0);
                }
                var (total, noVisit) = returnDict[nmDepartmentID];
                if (successRecord != null && successRecord.TryGetValue("result", out string value))
                {
                    returnDict[nmDepartmentID] = (total + 1, noVisit);
                    continue;
                }
                returnDict[nmDepartmentID] = (total + 1, noVisit + 1);
            }
            // 转换为最终的输出格式
            var dictList = returnDict.Select(dict =>
                new Dictionary<string, object>
                {
                    ["occDepartmentID"] = int.Parse(dict.Key),
                    ["noVisitCount"] = $"{dict.Value.NoVisit}/{dict.Value.Total}"
                }).ToList();
            return dictList;
        }

        #endregion

        #region 获取访视质控表单
        /// <summary>
        /// 获取访视质控内容
        /// </summary>
        /// <param name="profileID"></param>
        /// <param name="qcMainID"></param>
        /// <returns></returns>
        public async Task<FormTemplateView> GetVisitsQcAssessView(string profileID, string qcMainID)
        {
            var returnView = new FormTemplateView();
            if (string.IsNullOrEmpty(profileID))
            {
                return returnView;
            };
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "CriticalPatientVisitType",
                FilterSettingTypeValue = profileID
            };
            var visitsFormID = await _settingDictionaryRepository.GetSettingValue(settingParams);
            if (string.IsNullOrEmpty(visitsFormID))
            {
                _logger.Error("未找到访视settingDictionary配置 profileID||" + profileID);
                return returnView;
            }
            var qcFormInfo = await _hierarchicalQCFormRepository.GetFormByID(Convert.ToInt32(visitsFormID));
            if (qcFormInfo == null)
            {
                _logger.Error("未找到访视hierarchicalQCForm配置 visitsFormID||" + visitsFormID);
                return returnView;
            }
            return await _hierarchicalQCService.GetAssessContentView(qcMainID, qcFormInfo.TemplateCode, false, false);
        }
        #endregion

        #region 访视保存
        /// <summary>
        /// 访视记录保存
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> SaveVisitsRecord(VisitsRecordSaveView view)
        {
            if (view == null || view.VisitsRecord == null || view.QCMainAndDetail == null)
            {
                _logger.Error("危重患者访视保存失败！ 保存参数有误！");
                return false;
            }
            var visitsFormID = view.VisitsFormID;
            var relatedTableRecordID = view.VisitsRecord.RelatedTableRecordID;
            //每月首次新增自动添加本片区主题
            if (string.IsNullOrEmpty(relatedTableRecordID))
            {
                view.QCMainAndDetail.QcSubjectID = await SaveVisitsSubject(view, visitsFormID);
            }
            // 质控记录保存
            relatedTableRecordID = await _hierarchicalQCService.SaveHierarchicalQCMainAndDetailsAsync(view.QCMainAndDetail, view.HospitalID, view.EmployID, "");
            // 访视患者信息保存
            return await SaveVisitsRecordAndDetail(view, relatedTableRecordID);
        }
        /// <summary>
        ///  每月首次新增访视记录 主题自动添加
        /// </summary>
        /// <param name="view"></param>
        /// <param name="visitsFormID"></param>
        /// <returns></returns>
        public async Task<string> SaveVisitsSubject(VisitsRecordSaveView view, int visitsFormID)
        {
            var qcDate = view.QCMainAndDetail.QcMain.ExamineDate;
            var yearMonth = qcDate.ToString("yyyy-MM");
            var qcSubject = await _hierarchicalQCSubjectRepository.GetHierarchicalQCSubjectData("2", visitsFormID, yearMonth, view.UpNMDepartmentID);
            if (qcSubject.Count > 0)
            {
                return qcSubject.FirstOrDefault().HierarchicalQCSubjectID;
            }
            var qcForm = await _hierarchicalQCFormRepository.GetFormByID(visitsFormID);
            if (qcForm == null)
            {
                _logger.Error("危重患者访视保存失败！未找到对应hierarchicalQCForm  hierarchicalQCFormID||" + visitsFormID);
                return "";
            }
            var subjectSaveView = new SubjectView()
            {
                HospitalID = view.HospitalID,
                Language = view.Language,
                HierarchicalQCFormID = visitsFormID,
                FormName = qcForm.FormName,
                HierarchicalQCFormLevel = qcForm.HierarchicalQCFormLevel,
                StartDate = Common.DateHelper.GetFirstDayOfMonth(qcDate),
                EndDate = Common.DateHelper.GetLastDayOfMonth(qcDate),
                DepartmentID = view.UpNMDepartmentID,
                EmployeeID = view.EmployID,
                FormType = qcForm.FormType,
                TemplateCode = qcForm.TemplateCode,
                YearMonth = yearMonth,
            };
            return await _hierarchicalQCService.AddSubjectPlan(subjectSaveView);
        }
        /// <summary>
        /// 患者状况主记录及明细记录保存
        /// </summary>
        /// <param name="view"></param>
        /// <param name="relatedTableRecordID"></param>
        /// <returns></returns>
        public async Task<bool> SaveVisitsRecordAndDetail(VisitsRecordSaveView view, string relatedTableRecordID)
        {
            var profileRecord = await _profileRecordRepository.GetRecordByRelatedTableID("HierarchicalQCRecord", relatedTableRecordID);
            var addFlag = profileRecord == null;
            if (addFlag)
            {
                profileRecord = view.VisitsRecord;
                profileRecord.PatientProfileRecordID = profileRecord.GetId();
                profileRecord.RelatedTableName = "HierarchicalQCRecord";
                profileRecord.RelatedTableRecordID = relatedTableRecordID;
                profileRecord.HospitalID = view.HospitalID;
                profileRecord.Add(view.EmployID);
                profileRecord.Modify(view.EmployID);
                profileRecord.DeleteFlag = "";
                await _unitOfWork.GetRepository<PatientProfileRecordInfo>().InsertAsync(profileRecord);
            }
            else
            {
                profileRecord.Modify(view.EmployID);
                var profileDetails = await _profileDetailRepository.GetDataByPatientProfileRecordID(profileRecord.PatientProfileRecordID);
                if (profileDetails.Count > 0)
                {
                    profileDetails.ForEach(x => x.Modify(view.EmployID));
                }
            }
            if ((view.VisitsDetails?.Count ?? 0) > 0)
            {
                view.VisitsDetails.ForEach(m =>
                {
                    m.PatientProfileRecordID = profileRecord.PatientProfileRecordID;
                    m.Add(view.EmployID);
                    m.Modify(view.EmployID);
                    m.PatientProfileDetailID = m.GetId();
                });
                await _unitOfWork.GetRepository<PatientProfileDetailInfo>().InsertAsync(view.VisitsDetails);
            };
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        #endregion

        #region 获取访视记录
        /// <summary>
        /// 获取访视记录
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <param name="profileID"></param>
        /// <param name="templateCode"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<VisitsTableView> GetVisitsQcRecord(string sourceID, string sourceType, string profileID, string templateCode, string hospitalID)
        {
            var returnList = new VisitsTableView()
            {
                TableData = [],
                TableHeader = []
            };
            if (string.IsNullOrEmpty(sourceID) || string.IsNullOrEmpty(sourceType) || string.IsNullOrEmpty(profileID))
            {
                _logger.Error("获取访视质控记录失败！入参有误！");
                return returnList;
            }
            var dynamicTableHeaderQueryView = new DynamicTableHeaderQueryView()
            {
                TableType = "PatientVisits",
                TableSubType = profileID + "-Main",
                HospitalID = hospitalID
            };
            returnList.TableHeader = await _dynamicTableSettingService.GetDynamicTableHeader(dynamicTableHeaderQueryView);
            if (returnList.TableHeader.Count == 0)
            {
                _logger.Error("动态表格配置未找到！dynamicTableHeaderQueryView||" + ListToJson.ToJson(dynamicTableHeaderQueryView));
                return returnList;
            }
            var visitsRecordList = await _profileRecordRepository.GetRecordDataBySourceID(sourceID, sourceType, "HierarchicalQCRecord");
            if (visitsRecordList.Count == 0)
            {
                return returnList;
            }
            var visitsRecordIDList = visitsRecordList.Select(m => m.PatientProfileRecordID).ToList();
            var visitsDetails = await _profileDetailRepository.GetDataByPatientProfileRecordIDList(visitsRecordIDList);
            var qcRecordIDList = visitsRecordList.Select(m => m.RelatedTableRecordID).ToList();
            var qcRecordView = await _hierarchicalQCRecordRepository.GetRecordDataByRecordIDs(qcRecordIDList, hospitalID);
            if (qcRecordView.Count == 0)
            {
                return returnList;
            }
            var careMainIDList = qcRecordView.Select(m => m["hierarchicalQCMainID"].ToString()).ToList();
            var groupItemIDList = await _dynamicFormDetailRepository.GetFormItemIDByFormRecordIDAndComponentListID(templateCode, 109);
            var qcDetails = new List<HierarchicalQCDetailInfo>();
            if (groupItemIDList.Count > 0)
            {
                // 类型转换
                var groupItemIDs = groupItemIDList.Where(m=>int.TryParse(m,out var id)).Select(m=>int.Parse(m)).ToList();
                qcDetails = await _hierarchicalQCDetailRepository.GetQCDetailInfosByQCMainIDs(careMainIDList, groupItemIDs);
            }
            var parentMappingSettingParams = new SettingDictionaryParams
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "CriticalPatientVisitGroupID"
            };
            var parentMappingSetting = await _settingDictionaryRepository.GetSettingDictionary(parentMappingSettingParams);
            // 获取主记录阅读状态
            var mainStatusDictionary = await _hierarchicalQCMainRepository.GetQCMainIsReadStatus(careMainIDList);
            foreach (var visitsRecord in visitsRecordList)
            {
                var sucQcRecordView = qcRecordView.FirstOrDefault(m => m["hierarchicalQCRecordID"].ToString() == visitsRecord.RelatedTableRecordID);
                if (sucQcRecordView == null)
                {
                    continue;
                }
                var tableDataItem = new Dictionary<string, object>
                {
                    { "patientProfileRecordID", visitsRecord.PatientProfileRecordID },
                    { "hierarchicalQCRecordID", sucQcRecordView["hierarchicalQCRecordID"] },
                    { "hierarchicalQCMainID", sucQcRecordView["hierarchicalQCMainID"] },
                    { "qcDepartmentID", sucQcRecordView["qcDepartmentID"] },
                    { "qcEmployeeID", sucQcRecordView["qcEmployeeID"] },
                    { "qcDepartmentName", sucQcRecordView["qcDepartmentName"] },
                    { "visitDateTime", sucQcRecordView["qcDate"] },
                    { "visitEmployName", sucQcRecordView["qcEmployeeName"] },
                    { "totalScore", sucQcRecordView["result"] },
                    { "guidance", sucQcRecordView["guidance"] },
                    { "modifyDate", sucQcRecordView["modifyDate"] },
                    { "isRead", (mainStatusDictionary.TryGetValue(sucQcRecordView["hierarchicalQCMainID"].ToString(), out bool? isRead) && isRead.HasValue && isRead.Value) ? "是" : "否" }
                };
               
                var sucPatientProfileDetail = visitsDetails.Where(m => m.PatientProfileRecordID == visitsRecord.PatientProfileRecordID).ToList();
                if (sucPatientProfileDetail.Count > 0)
                {
                    sucPatientProfileDetail.ForEach(m =>
                    {
                        tableDataItem.Add(ToLowerFirstChar(m.DataID), m.DataValue);
                    });
                }
                var sucQcDetails = qcDetails.Where(m => m.HierarchicalQCMainID == sucQcRecordView["hierarchicalQCMainID"].ToString()).ToList();
                if (sucQcDetails.Count == 0)
                {
                    returnList.TableData.Add(tableDataItem);
                    continue;
                }
                var visitsResutDictionary = GetVisitsResultDictionary(sucQcDetails, parentMappingSetting);
                tableDataItem = tableDataItem.Concat(visitsResutDictionary).ToLookup(pair => pair.Key, pair => pair.Value)
                      .ToDictionary(group => group.Key, group => group.Last());
                returnList.TableData.Add(tableDataItem);
            }
            returnList.TableData = returnList.TableData.OrderBy(m => m["visitDateTime"]).ThenBy(m => m["modifyDate"]).ToList();
            return returnList;
        }
        /// <summary>
        /// 组装分数分组明细
        /// </summary>
        /// <param name="qcDetails"></param>
        /// <param name="parentMappingSetting"></param>
        /// <returns></returns>
        private static Dictionary<string, object> GetVisitsResultDictionary(List<HierarchicalQCDetailInfo> qcDetails, List<SettingDictionaryInfo> parentMappingSetting)
        {
            var visitsResutDictionary = new Dictionary<string, object>();
            var mappingDictionary = parentMappingSetting.ToDictionary(m => m.SettingValue, m => m.SettingTypeValue);
            foreach (var key in mappingDictionary.Keys)
            {
                var sucQcDetail = qcDetails.FirstOrDefault(m => m.HierarchicalQCAssessListID.ToString() == key);
                if (sucQcDetail == null)
                {
                    continue;
                }
                visitsResutDictionary.Add(mappingDictionary[key], sucQcDetail.Result);
            }
            return visitsResutDictionary;
        }
        #endregion

        #region 记录删除
        /// <summary>
        /// 根据SourceID 删除所有访视记录
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceID"></param>
        /// <param name="relatedTableName"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteAllVisitsRecord(string sourceType, string sourceID, string relatedTableName, string employeeID)
        {
            var patientVisitsRecordIDList = await _profileRecordRepository.GetRecordIDBySourceID(sourceID, sourceType, relatedTableName);
            if (patientVisitsRecordIDList.Count == 0)
            {
                _logger.Error("未找到访视记录 删除失败！");
                return false;
            }
            return await DeleteVisitsRecord(patientVisitsRecordIDList, employeeID);
        }
        /// <summary>
        /// 根据访视记录主键集合删除数据
        /// </summary>
        /// <param name="patientProfileRecordIDs"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteVisitsRecord(List<string> patientProfileRecordIDs, string employeeID)
        {
            if ((patientProfileRecordIDs?.Count ?? 0) == 0)
            {
                _logger.Error("访视入参有误 删除失败！");
                return false;
            }
            var patientVisitsRecordList = await _profileRecordRepository.GetRecordDataByRecordIDList(patientProfileRecordIDs);
            if (patientVisitsRecordList.Count == 0)
            {
                _logger.Error("未找到访视记录 删除失败！");
                return false;
            }
            var patientVisitsDetailList = await _profileDetailRepository.GetDataByPatientProfileRecordIDList(patientProfileRecordIDs);
            if (patientVisitsRecordList.Count > 0)
            {
                patientVisitsRecordList.ForEach(m => m.Delete(employeeID));
            }
            if (patientVisitsDetailList.Count == 0)
            {
                patientVisitsDetailList.ForEach(m => m.Delete(employeeID));
            }
            //删除质控记录
            var qcRecordIDList = patientVisitsRecordList.Select(m => m.RelatedTableRecordID).ToList();
            foreach (var qcRecordID in qcRecordIDList)
            {
                await _hierarchicalQCService.DeleteHierarchicalQCRecord(qcRecordID, employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        #endregion
        /// <summary>
        /// 保存记录已读
        /// </summary>
        /// <param name="hierarchicalQCMainID">质控主表主键ID</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        public async Task<bool> SaveReadRecordAsync(string hierarchicalQCMainID, string employeeID)
        {
            var mainInfo = await _hierarchicalQCMainRepository.GetDataByMainID(hierarchicalQCMainID);
            if (mainInfo == null)
            {
                return false;
            }
            mainInfo.IsReadFlag = true;
            mainInfo.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
    }
}

