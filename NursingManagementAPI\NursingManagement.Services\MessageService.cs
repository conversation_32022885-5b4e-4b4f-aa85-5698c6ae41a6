﻿using Newtonsoft.Json;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class MessageService : IMessageService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IRequestApiService _requestApiService;
        private readonly IEmployeeContactRepository _employeeContactRepository;
        private readonly IUserLoginRepository _userLoginRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        public MessageService(
            IRequestApiService requestApiService
            , IEmployeeContactRepository employeeContactRepository
            , IUserLoginRepository userLoginRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , ISettingDictionaryService settingDictionaryService
        )
        {
            _requestApiService = requestApiService;
            _employeeContactRepository = employeeContactRepository;
            _userLoginRepository = userLoginRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _settingDictionaryService = settingDictionaryService;
        }

        public Tuple<MessageTool, MessageType> GetMessageToolAndMessageType(int messageTool, int messagetype)
        {
            MessageTool newMessageTool = MessageTool.MQ;
            switch (messageTool)
            {
                case 1:
                    newMessageTool = MessageTool.MQ;
                    break;
                case 2:
                    newMessageTool = MessageTool.Wechat;
                    break;
                case 3:
                    newMessageTool = MessageTool.Dingtalk;
                    break;
                case 4:
                    newMessageTool = MessageTool.SMS;
                    break;
            }
            MessageType newMessageType = MessageType.Notification;
            switch (messagetype)
            {
                case 1:
                    newMessageType = MessageType.Notification;
                    break;
                case 2:
                    newMessageType = MessageType.Alert;
                    break;
                case 3:
                    newMessageType = MessageType.Confirm;
                    break;
                case 4:
                    newMessageType = MessageType.Message;
                    break;
            }
            return Tuple.Create(newMessageTool, newMessageType);
        }
        /// <summary>
        /// 发送消息公共方法
        /// </summary>
        /// <param name="messageView"></param>
        /// <returns></returns>
        public async Task<bool> SendMessage(MessageView messageView)
        {
            var result = false;
            if (messageView == null || messageView.MessageCondition == null)
            {
                _logger.Error("SendMessage方法缺少发送人参数！");
                return result;
            }
            //判断当前通知类型是否打开
            var messageToolList = await GetMessageToolList();
            if (messageView.MessageTools != null && messageView.MessageTools.Count > 0)
            {
                messageToolList = messageToolList.Intersect(messageView.MessageTools).ToList();
            }
            if (messageToolList == null || messageToolList.Count <= 0)
            {
                _logger.Error("SendMessage方法，没有找到消息工具！");
                return result;
            }
            if (messageView.MessageCondition.Message is string)
            {
                messageView.MessageCondition.MessageContent = messageView.MessageCondition.Message.ToString();
            }
            // 如果Message是对象 且 消息格式化不为空 转换消息
            //if (messageView.MessageCondition.Message is object && string.IsNullOrWhiteSpace(messageView.MessageCondition.MessageFormatter))
            //{
            //    // 暂时不需要，不写逻辑
            //    messageView.MessageCondition.MessageContent = "";
            //}
            foreach (var messageTool in messageToolList)
            {
                switch (messageTool)
                {
                    case MessageTool.MQ:
                        result = await SendMQMessage(messageView);
                        break;
                    case MessageTool.Wechat:
                        result = await SendWechatMessage(messageView);
                        break;
                    case MessageTool.Dingtalk:
                        result = await SendDingtalkMessage(messageView);
                        break;
                    case MessageTool.SMS:
                        result = await SendSMSMessage(messageView);
                        break;
                }
            }
            return result;
        }
        /// <summary>
        /// 发送MQ消息
        /// </summary>
        /// <param name="messageView"></param>
        /// <returns></returns>
        private async Task<bool> SendMQMessage(MessageView messageView)
        {
            var mqMessageView = new MQMessageView();
            // 如果有接收人，走单人MQ消息
            if (!string.IsNullOrWhiteSpace(messageView.Receiver))
            {
                mqMessageView.MessageType = MQMessageType.SINGLE;
                mqMessageView.ExchangeName = messageView.Receiver;
            }
            else
            {
                if (messageView.MessageCondition == null || string.IsNullOrWhiteSpace(messageView.MessageCondition.MQExchangeName))
                {
                    _logger.Error("SendMQMessage缺少参数！");
                    return false;
                }
                mqMessageView.MessageType = MQMessageType.BROADCAST;
                mqMessageView.ExchangeName = messageView.MessageCondition.MQExchangeName;
                if (!string.IsNullOrWhiteSpace(messageView.MessageCondition.MQRoutingKey))
                {
                    mqMessageView.RoutingKey = messageView.MessageCondition.MQRoutingKey;
                }
            }
            mqMessageView.Body = new Dictionary<string, object>() {
                { "Type", messageView.MessageCondition.Type.ToString() },
                { "Message", messageView.MessageCondition.Message },
                { "MessageFormatter", messageView.MessageCondition.MessageFormatter },
                { "Link", messageView.MessageCondition.Url },
                { "ClientType", messageView.MessageCondition.ClientType },
                { "UrlParams", messageView.MessageCondition.UrlParams }

            };
            try
            {
                // 根据settingCode调用ApiSetting中配置的接口
                var result = await _requestApiService.RequestAPI("PushMessage", ListToJson.ToJson(mqMessageView));
                return result.ToString() == "true";
            }
            catch (Exception ex)
            {
                _logger.Error($"调用mq 推送信息PushMessage失败，参数：{ListToJson.ToJson(mqMessageView)},exception:{ex}");
            }
            return false;

        }

        /// <summary>
        /// 发送微信消息
        /// http://10.1.33.52:8002/MessageSystem/swagger/index.html
        /// </summary>
        /// <param name="messageView"></param>
        /// <returns></returns>
        private async Task<bool> SendWechatMessage(MessageView messageView)
        {
            if (messageView.ClientType == 1)
            {
                _logger.Info($"消息不是移动端的，不处理");
                return true;
            }
            var userData = await _userLoginRepository.GetByEmployeeID(messageView.EmployeeID);
            if (userData == null || string.IsNullOrEmpty(userData?.WechatWebOpenID))
            {
                _logger.Error($"SendPublicAccountMessage方法中获取用户OpenID失败,用户编号{messageView.EmployeeID}");
                return false;
            }
            var appConfigList = await _appConfigSettingRepository.GetBySettingType("Wechat");
            var SendMessageUrl = appConfigList.Find(m => m.SettingCode == "SendMessageUrl")?.SettingValue;
            if (string.IsNullOrWhiteSpace(SendMessageUrl))
            {
                _logger.Error("SendPublicAccountMessage方法中获取发送微信公众号消息API失败，SettingCode:SendMessageUrl");
                return false;
            }
            var publicAccountMessageID = appConfigList.Find(m => m.SettingCode == "PublicAccountMessageID")?.SettingValue;
            if (string.IsNullOrWhiteSpace(publicAccountMessageID))
            {
                _logger.Error("SendPublicAccountMessage方法中获取发送微信公众号消息模版ID失败，SettingCode:PublicAccountMessageID");
                return false;
            }
            var publicAccountAppID = appConfigList.Find(m => m.SettingCode == "PublicAccountAPPID")?.SettingValue;
            if (string.IsNullOrWhiteSpace(publicAccountAppID))
            {
                _logger.Error("SendPublicAccountMessage方法中获取发送微信公众号appID失败，SettingCode:PublicAccountAPPID");
                return false;
            }
            var publicAccountReferId = appConfigList.Find(m => m.SettingCode == "PublicAccountReferId")?.SettingValue;
            if (string.IsNullOrWhiteSpace(publicAccountReferId))
            {
                _logger.Error("SendPublicAccountMessage方法中获取发送微信公众号推送referId失败，SettingCode:PublicAccountReferId");
                return false;
            }
            if (!string.IsNullOrEmpty(messageView.MessageCondition.Url))
            {
                var messageSkipBaseUrl = appConfigList.Find(m => m.SettingCode == "MessageSkipUrl")?.SettingValue;
                // 这里可以拼接各种参数
                messageView.MessageCondition.Url = $"{messageSkipBaseUrl}#/pages/login/index?redirectTo={messageView.MessageCondition.Url}";
            }
            string jsonData = GetRequestJsonData(messageView, userData, publicAccountMessageID, publicAccountAppID, publicAccountReferId);
            string responseJson = null;
            try
            {
                responseJson = await HttpHelper.HttpPostAsync(SendMessageUrl, jsonData, "text/json");
                if (string.IsNullOrWhiteSpace(responseJson))
                {
                    _logger.Error($"SendPublicAccountMessage方法中调用微信消息平台失败,接口返回信息：{responseJson}");
                    return false;
                }
                var result = ListToJson.ToList<Dictionary<string, string>>(responseJson);
                if (result?["code"] == "1")
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"SendPublicAccountMessage方法中调用微信消息平台失败,接口返回信息：{responseJson}");
            }
            return false;
        }
        /// <summary>
        /// 获取发送微信消息请求需要的body参数
        /// </summary>
        /// <param name="messageView">消息信息Vew</param>
        /// <param name="userData">用户账号信息</param>
        /// <param name="publicAccountMessageID">消息模板ID</param>
        /// <param name="publicAccountAppID">appID</param>
        /// <param name="publicAccountReferId">发送微信公众号推送referId</param>
        /// <returns></returns>
        private static string GetRequestJsonData(MessageView messageView, UserLoginInfo userData, string publicAccountMessageID, string publicAccountAppID, string publicAccountReferId)
        {
            var message = new
            {
                receiver = new
                {
                    WX = new
                    {
                        OPENID = userData.WechatWebOpenID
                    }
                },
                // 转义关键字
                @params = new
                {
                    URL = messageView.MessageCondition.Url,
                    first = "",
                    keyword1 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    keyword2 = messageView.MessageCondition.MessageContent,
                    remark = "点击查看"
                },
                app_id = publicAccountAppID,
                refer_id = publicAccountReferId,
                message_id = publicAccountMessageID
            };
            var jsonData = JsonConvert.SerializeObject(message);
            return jsonData;
        }

        /// <summary>
        /// 发送钉钉消息
        /// </summary>
        /// <param name="messageView"></param>
        /// <returns></returns>
        private async Task<bool> SendDingtalkMessage(MessageView messageView)
        {
            var phoneNumber = await GetEmployeePhoneNumber(messageView);
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                _logger.Error($"发送钉钉，请求必要参数不满足,View =【{ListToJson.ToJson(messageView)}】");
                return false;
            };
            try
            {
                string param = $"?phone={phoneNumber}&msg={messageView.MessageCondition.MessageContent}";
                var result = await _requestApiService.RequestAPI("SendDTalkByUser", param, null);
                if (result == null)
                {
                    _logger.Error("发送钉钉失败：钉钉接口调用失败！");
                    return false;
                }
                var ret = ListToJson.ToList<ResponseResult>(result.ToString());
                var dTalkResult = ListToJson.ToList<Dictionary<string, object>>(ret.Data.ToString());

                if (ret.Code == 1 && dTalkResult["errcode"]?.ToString() == "0" && dTalkResult["errmsg"]?.ToString() == "ok")
                {
                    return true;
                }
                else
                {
                    _logger.Error("发送钉钉失败：" + dTalkResult["errmsg"]?.ToString());
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("调用钉钉接口失败：" + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 发送手机短信
        /// </summary>
        /// <param name="messageView"></param>
        /// <returns></returns>
        private async Task<bool> SendSMSMessage(MessageView messageView)
        {
            var phoneNumber = await GetEmployeePhoneNumber(messageView);
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                _logger.Error($"发送短信，请求必要参数不满足,View =【{ListToJson.ToJson(messageView)}】");
                return false;
            };
            var phoneMessageView = new Dictionary<string, string>() {
                {"Mobile",phoneNumber } ,
                {"Content",messageView.MessageCondition.MessageContent }
            };
            try
            {
                var result = await _requestApiService.RequestAPI("SendPhoneMessageByUser", ListToJson.ToJson(phoneMessageView), null);
                if (result == null)
                {
                    _logger.Error("发送短信失败：短信接口调用失败！");
                    return false;
                }
                var ret = ListToJson.ToList<ResponseResult>(result.ToString());
                if (ret.Code == 1)
                {
                    return true;
                }
                else
                {
                    _logger.Error("发送短信返回错误信息：" + ret.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"调用短信接口异常：", ex);
                return false;
            }
        }
        /// <summary>
        /// 获取人员的手机号
        /// </summary>
        /// <param name="messageView"></param>
        /// <returns></returns>
        private async Task<string> GetEmployeePhoneNumber(MessageView messageView)
        {
            var phoneNumber = messageView.MessageCondition.PhoneNumber;
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber;
            }
            if (string.IsNullOrEmpty(messageView.EmployeeID))
            {
                _logger.Warn("GetEmployeePhoneNumber获取人员手机号失败：messageView.EmployeeID为空！");
                return phoneNumber;
            }
            // 根据工号查询手机号
            phoneNumber = await _employeeContactRepository.GetPhoneNumberByEmployeeID(messageView.EmployeeID);
            if (string.IsNullOrEmpty(phoneNumber))
            {
                _logger.Error($"GetEmployeePhoneNumber获取人员手机号失败：{messageView.EmployeeID}在系统中没有找到对应的手机号");
                return phoneNumber;
            }
            return phoneNumber;
        }

        /// <summary>
        /// 获取消息通知工具（枚举实例）
        /// </summary>
        /// <returns></returns>
        private async Task<List<MessageTool>> GetMessageToolList()
        {
            List<(string settingTypeCode, bool switchFlag)> switchSettings = await _settingDictionaryService.GetMessageNotifySettingAsync("MessageNotify");
            var messageToolList = new List<MessageTool>();
            // 遍历配置
            foreach (var (settingTypeCode, switchFlag) in switchSettings)
            {
                if (!switchFlag)
                {
                    continue;
                }
                // 创建枚举类实例
                var messageTool = (MessageTool)typeof(MessageTool).GetField(settingTypeCode).GetValue(null);
                messageToolList.Add(messageTool);
            }
            return messageToolList;
        }
    }
}