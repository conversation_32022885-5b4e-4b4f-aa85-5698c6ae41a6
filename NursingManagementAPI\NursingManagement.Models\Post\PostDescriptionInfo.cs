﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 岗位说明书
    /// </summary>
    [Serializable]
    [Table("PostDescription")]
    public class PostDescriptionInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int PostDescriptionID { get; set; }
        /// <summary>
        /// 岗位名称编码
        /// </summary>
        public int PostID { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号，主键
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言序号，主键
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 岗位编码，护理部制定的编码
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string PostDescriptionCode { get; set; }
        /// <summary>
        /// 岗位说明书名称
        /// </summary>
        [Column(TypeName = "nvarchar(50)")]
        public string PostDescriptionName { get; set; }
        /// <summary>
        /// 直接上级
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string Superiors { get; set; }
        /// <summary>
        /// 直接下级
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string Junior { get; set; }
        /// <summary>
        /// 岗位定员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string PostNumber { get; set; }
        /// <summary>
        /// 所辖人数
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HeadCount { get; set; }
        /// <summary>
        /// 制定部门，护理管理组织架构的DepartmentID
        /// </summary>
        public int CreateDepartmentID { get; set; }
        /// <summary>
        /// 版本号；1.0.1，当前修改的为启用状态，则自动新增版本，
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Version { get; set; }
        /// <summary>
        /// 状态 0：停用、1：启用（签发）、2：待审核、3、审核
        /// </summary>
        [Column(TypeName ="varchar(50)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 审核人，HREmployeeID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Approver { get; set; }
        /// <summary>
        /// 审核日期
        /// </summary>
        public DateTime? ApproveDateTime { get; set; }
        /// <summary>
        /// 签发人，HREmployeeID
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string Signer { get; set; }
        /// <summary>
        /// 签发/发布日期
        /// </summary>
        public DateTime? SignDateTime { get; set; }
        /// <summary>
        /// 从业资格要求
        /// </summary>
        [Column(TypeName = "nvarchar(4000)")]
        public string QualificationRequirements { get; set; }
        /// <summary>
        /// 教育水平
        /// </summary>
        [Column(TypeName = "nvarchar(4000)")]
        public string EducationalLevel { get; set; }
        /// <summary>
        /// 培训经历
        /// </summary>
        [Column(TypeName = "nvarchar(4000)")]
        public string TrainingRecord { get; set; }
        /// <summary>
        /// 经验
        /// </summary>
        [Column(TypeName = "nvarchar(4000)")]
        public string Experience { get; set; }
        /// <summary>
        /// 其他
        /// </summary>
        [Column(TypeName = "nvarchar(4000)")]
        public string Other { get; set; }
        /// <summary>
        /// 岗位职责
        /// </summary>
        [Column(TypeName = "nvarchar(MAX)")]
        public string Responsibility { get; set; }
        /// <summary>
        /// 绩效评价
        /// </summary>
        [Column(TypeName = "nvarchar(4000)")]
        public string PerformanceEvaluation { get; set; }
        /// <summary>
        /// 岗位SOP标准
        /// </summary>
        [Column(TypeName = "nvarchar(MAX)")]
        public string SOP { get; set; }
    }
}
