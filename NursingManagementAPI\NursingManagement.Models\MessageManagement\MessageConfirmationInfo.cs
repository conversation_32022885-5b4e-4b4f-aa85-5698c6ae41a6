﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("MessageConfirmation")] 
    public class MessageConfirmationInfo : MutiModifyInfo
    {
        /// <summary>
        /// 消息确认表的主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string MessageConfirmationID { get; set; }

        /// <summary>
        /// 关联的消息记录表主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string MessageRecordID { get; set; }

        /// <summary>
        /// 确认状态
        /// </summary>
        public bool ConfirmationStatus { get; set; }

        /// <summary>
        /// 确认人员ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ConfirmEmployeeID { get; set; }

        /// <summary>
        /// 确认日期
        /// </summary>
        public DateTime? ConfirmationDate { get; set; }
    }
}
