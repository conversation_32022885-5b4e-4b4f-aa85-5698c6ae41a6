﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DepartmentPostRepository : IDepartmentPostRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public DepartmentPostRepository(NursingManagementDbContext db,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<List<DepartmentPostInfo>> GetAsync(int departmentID)
        {
            var list = (List<DepartmentPostInfo>)await GetCacheAsync();
            return list.Where(m => m.DepartmentID == departmentID).ToList();
        }

        public async Task<DepartmentPostInfo> GetDepartmentPostByID(int departmentPostID)
        {
            var list = (List<DepartmentPostInfo>)await GetCacheAsync();
            var departmentPost = list.Where(m => m.DepartmentPostID == departmentPostID).FirstOrDefault();
            // 如果缓存取不到就从数据库里取数据
            if (departmentPost != null)
            {
                string key = GetCacheType();
                (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
                departmentPost = await _nursingManagementDbContext.DepartmentPostInfos.Where(m => m.DepartmentPostID == departmentPostID
                                                                                && m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*")
                                                                                .FirstOrDefaultAsync();
            }
            return departmentPost;
        }

        public async Task<List<DepartmentPostInfo>> GetByPostIDs(int departmentID, List<int> postIDs)
        {
            var list = (List<DepartmentPostInfo>)await GetCacheAsync();
            return list.Where(m => (m.DepartmentID == departmentID|| m.DepartmentID == 999999)  && postIDs.Contains(m.PostID)).ToList();
        }
        public async Task<int> GetMaxID()
        {
            var info = await _nursingManagementDbContext.DepartmentPostInfos.OrderBy(m => m.DepartmentPostID).LastOrDefaultAsync();
            return info.DepartmentPostID;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DepartmentPostInfos.Where(m => m.HospitalID == hospitalID
                && m.Language == language && m.DeleteFlag != "*").OrderBy(m => m.Sort).ToListAsync();
                return result;

            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.DepartmentPost.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        /// <summary>
        /// 根据岗位ID获取数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        public async Task<bool> GetExistByPostID(int postID, string hospitalID)
        {
            return await _nursingManagementDbContext.DepartmentPostInfos.AnyAsync(m => m.HospitalID == hospitalID && m.PostID == postID);
        }
    }
}
