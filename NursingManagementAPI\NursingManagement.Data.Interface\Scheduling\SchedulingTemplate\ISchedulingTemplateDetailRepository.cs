﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ISchedulingTemplateDetailRepository
    {
        /// <summary>
        /// 获取排班模板明细记录
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <returns></returns>
        Task<List<SchedulingTemplateDetailInfo>> GetDetailByRecordID(string schedulingTemplateRecordID);
    }
}
