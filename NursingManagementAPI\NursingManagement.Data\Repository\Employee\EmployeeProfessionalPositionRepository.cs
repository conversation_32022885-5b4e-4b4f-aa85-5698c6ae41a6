﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeProfessionalPositionRepository : IEmployeeProfessionalPositionRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeProfessionalPositionRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeProfessionalPositionInfo>> GetListByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeProfessionalPositionInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<EmployeeProfessionalPositionInfo>> GetHighestCurrentProfessionalPositionView(string hospitalID, string[] employeeIDs, string[] positionIDs)
        {
            var result = _dbContext.EmployeeProfessionalPositionInfos.Where(m => m.DeleteFlag != "*" && employeeIDs.Contains(m.EmployeeID) && m.SpecialFlag == "H" && !string.IsNullOrEmpty(m.ProfessionalLevelCode));
            if (positionIDs != null && positionIDs.Length > 0)
            {
                result = result.Where(m => positionIDs.Contains(m.ProfessionalLevelCode));
            }
            return await result.Select(m => new EmployeeProfessionalPositionInfo
            {
                EmployeeID = m.EmployeeID,
                ProfessionalLevelCode = m.ProfessionalLevelCode,
                ObtainingDate = m.ObtainingDate             
            }
            ).OrderByDescending(m=>m.ObtainingDate).ToListAsync();
        }

        public async Task<EmployeeProfessionalPositionInfo> GetCurrentByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeProfessionalPositionInfos.Where(m => m.DeleteFlag != "*" && m.EmployeeID == employeeID && m.SpecialFlag == "H").FirstOrDefaultAsync();
        }
    }
}