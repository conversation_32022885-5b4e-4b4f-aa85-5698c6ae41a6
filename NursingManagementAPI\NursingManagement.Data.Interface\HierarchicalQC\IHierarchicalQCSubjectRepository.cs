﻿using NursingManagement.Models;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCSubjectRepository
    {
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<HierarchicalQCSubjectInfo> GetHierarchicalQCSubjectByID(string id);
        /// <summary>
        /// 根据条件获取质控主题集合
        /// </summary>
        /// <param name="level"></param>
        /// <param name="formID"></param>
        /// <param name="yearMonth"></param>
        /// <param name="addDeparmrntID"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCSubjectInfo>> GetHierarchicalQCSubjectData(string level, int? formID, string yearMonth, int? addDeparmrntID);
        /// <summary>
        ///  获取主题下拉框数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="switchDepartmentIDs">切换的部门IDs</param>
        /// <returns></returns>
        Task<List<HierarchicalQCSubjectInfo>> GetQCSubjectOptions(QCSubjectSearchView searchView, List<int> switchDepartmentIDs = null,bool? filterImplementationDateFlag = false);
        /// <summary>
        /// 根据templateCode获取数据（转换质控模板临时使用，线上转换完删除）
        /// </summary>
        /// <param name="formID"></param>
        /// <param name="formLevel"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCSubjectInfo>> GetQCSubjectByFormID(int formID, string formLevel);
        /// <summary>
        /// 主题维护画面获取主题
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCSubjectInfo>> GetSubject(GetQCSubjectView view);
    }
}
