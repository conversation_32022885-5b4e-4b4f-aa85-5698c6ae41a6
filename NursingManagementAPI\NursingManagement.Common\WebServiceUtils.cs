﻿using NLog;
using System;
using System.IO;
using System.Net;
using System.Text;

namespace NursingManagement.Common
{
    public class WebServiceUtils
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public static string GetResult(string requestUrl, string requestBody)
        {
            if (string.IsNullOrEmpty(requestUrl))
            {
                return "";
            }
            if (string.IsNullOrEmpty(requestBody))
            {
                return "";
            }

            byte[] dataArray = Encoding.UTF8.GetBytes(requestBody);

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(requestUrl);
            request.Method = "POST";
            request.ContentType = "application/xml";
            request.ContentLength = dataArray.Length;

            Stream dataStream = null;
            try
            {
                dataStream = request.GetRequestStream();
            }
            catch (Exception ex)
            {
                _logger.Error("WebServiceUtil请求异常" + ex.ToString());
                return "";
            }
            dataStream.Write(dataArray, 0, dataArray.Length);
            dataStream.Close();

            string result = "";
            try
            {
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                result = reader.ReadToEnd();
                reader.Close();
            }
            catch (Exception ex)
            {
                _logger.Error("WebServiceUtil响应异常" + ex.ToString());
                return "";
            }

            return result;
        }
    }
}