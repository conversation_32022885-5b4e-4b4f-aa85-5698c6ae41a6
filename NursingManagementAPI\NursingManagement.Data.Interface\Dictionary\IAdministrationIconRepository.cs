﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IAdministrationIconRepository : ICacheRepository
    {
        /// <summary>
        /// 获取图标集合
        /// </summary>
        /// <param name="moduleType"></param>
        /// <param name="groupIDs"></param>
        /// <returns></returns>
        Task<List<AdministrationIconInfo>> GetIconData(string moduleType, string[] groupIDs = null);
        /// <summary>
        /// 获取全表数据包含未删除
        /// </summary>
        /// <returns></returns>
        Task<List<AdministrationIconInfo>> GetAllIconInfos();
    }
}
