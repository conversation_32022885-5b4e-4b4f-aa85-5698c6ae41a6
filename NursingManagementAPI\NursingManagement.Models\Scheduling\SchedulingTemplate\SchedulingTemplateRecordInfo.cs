﻿using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  排班模板主表   
    /// </summary>
    [Table("SchedulingTemplateRecord")]
    public class SchedulingTemplateRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 排班模板记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SchedulingTemplateRecordID { get; set; }
        /// <summary>
        /// 部门编码，DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string TemplateName { get; set; }
        /// <summary>
        /// 模板描述
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string TemplateDescription { get; set; }
        /// <summary>
        /// 行数
        /// </summary>
        public int RowCount { get; set; }
        /// <summary>
        /// 列数
        /// </summary>
        public int ColumnCount { get; set; }
        /// <summary>
        /// 状态 0：停用、1：启用
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }
    }
}