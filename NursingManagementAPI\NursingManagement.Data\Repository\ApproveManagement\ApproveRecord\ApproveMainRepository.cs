﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ApproveMainRepository : IApproveMainRepository
    {

        private NursingManagementDbContext _dbContext;

        public ApproveMainRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<List<ApproveMainInfo>> GetApproveMainsByRecordIDAsync(string recordID)
        {
            return await _dbContext.ApproveMainInfos.Where(m => m.ApproveRecordID == recordID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<ApproveMainInfo>> GetApproveMainsByMainIDsAsync( string[] mainIDs)
        {
            return await _dbContext.ApproveMainInfos.Where(m => mainIDs.Contains(m.ApproveMainID) && m.DeleteFlag != "*").Select(m =>
            new ApproveMainInfo() 
            {
                NextApproveMainID = m.NextApproveMainID,
                ApproveRecordID = m.ApproveRecordID,
                ApproveMainID = m.ApproveMainID,
                ApproveDateTime = m.ApproveDateTime
            }).ToListAsync();
        }

        public async Task<ApproveMainInfo> GetApproveMainByIDAsync(string approveMainID)
        {
            return await _dbContext.ApproveMainInfos.Where(m => m.ApproveMainID == approveMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取已审批的main记录(approveMainID,statusCode)
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<ApproveMainInfo>> GetApprovedMainsByRecordIDAsync(string recordID)
        {
            return await _dbContext.ApproveMainInfos.Where(m => m.ApproveRecordID == recordID &&
            m.StatusCode != null && m.DeleteFlag != "*").Select(m=>new ApproveMainInfo
            {
                ApproveMainID = m.ApproveMainID,
                StatusCode = m.StatusCode,
            }).ToListAsync();
        }

    }
}
