﻿
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using SqlSugar;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 缓存控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/cache")]
    [EnableCors("any")]
    public class CacheController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IRedisService _redisService;

        /// <summary>
        /// 注入
        /// </summary>
        /// <param name="redisService"></param>
        public CacheController(
             IRedisService redisService
            )
        {
            _redisService = redisService;
        }

        /// <summary>
        /// 根据Key清除Redis缓存
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("RemoveRedisByKey")]
        public async Task<IActionResult> RemoveRedisByKey(string key)
        {
            var result = new ResponseResult
            {
                Data = await _redisService.Remove(key)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 清除系统Redis缓存
        /// </summary>
        /// <param name="cacheName">如传入Empl,则会查找所有包含Empl的缓存类型,传入all，清除所有缓存,区分大小写,不传默认all</param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("RemoveCacheByNameAsync")]
        public async Task<IActionResult> RemoveCacheByNameAsync(string cacheName, string hospitalID, int language)
        {
            bool resultFlag;
            string messageStr;
            var result = new ResponseResult();

            if (string.IsNullOrWhiteSpace(cacheName))
            {
                cacheName = "all";
            }
            (resultFlag, messageStr) = await _redisService.RemoveCacheByNameAsync(cacheName, hospitalID, language);
            if (resultFlag)
            {
                result.Sucess();
            }
            result.Data = messageStr;
            return result.ToJson();
        }

        /// <summary>
        /// 获取缓存清单
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetCacheType")]
        public IActionResult GetCacheType()
        {
            var result = new ResponseResult();
            result.Code = 1;
            result.Data = _redisService.GetDateBaseKeys();
            return result.ToJson();
        }
        ///// <summary>
        ///// 预加载所有缓存
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //[Route("PreloadingAllCache")]
        //public async Task<IActionResult> PreloadingAllCache(string hospitalID)
        //{
        //    var result = new ResponseResult
        //    {
        //        Data = await _redisService.PreloadingAllCache(hospitalID)
        //    };
        //    return result.ToJson();
        //}
    }
}