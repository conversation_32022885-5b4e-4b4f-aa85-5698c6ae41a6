﻿using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class EmployeeDeptChangeDynamicColumn
    {
        private readonly IDepartmentListRepository _departmentListRepository;
        private  readonly IEmployeeDepartmentChangeRequestRepository _employeeDepartmentChangeRequestRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;

        public EmployeeDeptChangeDynamicColumn(IDepartmentListRepository departmentListRepository, IEmployeeDepartmentChangeRequestRepository employeeDepartmentChangeRequestRepository, IEmployeePersonalDataRepository employeePersonalDataRepository)
        {
            _departmentListRepository = departmentListRepository;
            _employeeDepartmentChangeRequestRepository = employeeDepartmentChangeRequestRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
        }



        /// <summary>
        /// 根据主键来源表动态列
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetDynamicColumnListByRecordIDAsync(List<string> recordIDs, string proveCategory)
        {
            var resultList = new List<Dictionary<string, object>>();
            var departmentList = await _departmentListRepository.GetByCacheAsync();

            var recordList = await _employeeDepartmentChangeRequestRepository.GetRecordsByIDsAsNoTrackAsync(recordIDs);
            if (recordList.Count <= 0)
            {
                return null;
            }
            var employeeData = await _employeePersonalDataRepository.GetDataByEmployeeIDs(recordList.Select(m => m.EmployeeID).ToList());
            foreach (var item in recordList)
            {               //添加审批流程
                var view = CreateDynamicViewAsync(item, employeeData, departmentList);
                resultList.Add(view);
            }
            return resultList;
            
        }

        /// <summary>
        /// 创建请求审批所需参数
        /// </summary>
        /// <param name="employeeDepartmentChangeRequestInfo"></param>
        /// <param name="employeeDatas"></param>
        /// <param name="departmentList"></param>
        /// <returns></returns>
        private Dictionary<string, object> CreateDynamicViewAsync(EmployeeDepartmentChangeRequestInfo employeeDepartmentChangeRequestInfo
            , Dictionary<string, string> employeeDatas, List<DepartmentListInfo> departmentList)
        {
            // 使用初始化器语法来初始化一个包含初始值的 Dictionary
            return new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase)
            {
                { "SourceID", employeeDepartmentChangeRequestInfo.ID },
                { "ApproveRecordID", employeeDepartmentChangeRequestInfo.ApproveRecordID },
                { "ProveCategory", "HR-040" },
                { "DepartmentID", employeeDepartmentChangeRequestInfo.DepartmentID },
                { "AddEmployeeID", employeeDepartmentChangeRequestInfo.AddEmployeeID },
                { "AdjustEmployeeName", employeeDatas.FirstOrDefault(m => m.Key == employeeDepartmentChangeRequestInfo.EmployeeID).Value },
                { "DepartmentName", departmentList.Find(m => m.DepartmentID == employeeDepartmentChangeRequestInfo.DepartmentID)?.DepartmentContent },
                { "OriginalDepartmentName", departmentList.Find(m => m.DepartmentID == employeeDepartmentChangeRequestInfo.OriginalDepartmentID)?.DepartmentContent }
            };
        }
    }
}
