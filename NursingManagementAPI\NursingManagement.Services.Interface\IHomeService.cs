﻿using NursingManagement.ViewModels;
using NursingManagement.ViewModels.HomeService;

namespace NursingManagement.Services.Interface
{
    public interface IHomeService
    {
        /// <summary>
        /// 获取待办列表
        /// </summary>
        /// <param name="employeeID">当前用户ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="todoType">待办类别</param>
        /// <param name="clientType">客户端类型</param>
        /// <returns></returns>
        Task<object> GetToDoListAsync(string employeeID, int departmentID, string todoType, int clientType);

        /// <summary>
        /// 优先获取当前部门计划对应的未执行排程数据）
        /// </summary>
        /// <param name="employeeID">计划执行人</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="preOrNextFlag">获取计划月份之前OR之后，True：当月和当月之前；False：之后</param>
        /// <param name="clientType">客户端类型</param>
        /// <returns></returns>
        //Task<ToDoView> GetUnExecAnnualScheduleAsync(string employeeID, int departmentID, int? scheduleMonth, bool preOrNextFlag, int clientType);
        /// <summary>
        /// 获取主页呈现的消息列表
        /// </summary>
        /// <param name="departmentID">部门序号</param>
        /// <returns></returns>
        Task<List<HomeMessageView>> GetHomeMessageList(int departmentID);
    }
}
