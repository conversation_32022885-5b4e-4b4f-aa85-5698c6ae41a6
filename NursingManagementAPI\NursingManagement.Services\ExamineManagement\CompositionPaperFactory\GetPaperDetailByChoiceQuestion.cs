﻿using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services.Examine
{
    /// <summary>
    /// 写入必选题数据，调整输入规则题目数量及难度比例
    /// </summary>
    public class GetPaperDetailByChoiceQuestion : CompositionPaper
    {
        private readonly IExaminationQuestionDetailRepository _examinationQuestionDetailRepository;

        public GetPaperDetailByChoiceQuestion(
            IExaminationQuestionDetailRepository examinationQuestionDetailRepository
            )
        {
            _examinationQuestionDetailRepository = examinationQuestionDetailRepository;
        }

        /// <summary>
        /// 根据必选题写入数据
        /// </summary>
        /// <param name="questionType">题型</param>
        /// <param name="examinationQuestionList"></param>
        /// <param name="choicePaperQuestionlList"></param>
        /// <param name="examinationPaperMainData"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        public override async Task<List<PaperQuestionView>> FilterExaminationQuestion(string questionType, List<ExaminationQuestionInfo> examinationQuestionList, PaperCompositionRuleView paperCompositionRuleView, List<PaperQuestionView> choicePaperQuestionlList, ExaminationPaperMainInfo examinationPaperMainData, string modifyEmployeeID)
        {
            var difficultyRule = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.DifficultyRule;
            var requireQuestionIDs = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.RequireQuestion;
            if (requireQuestionIDs.Count <= 0)
            {
                return [];
            }
            // 筛选出必选题目
            var requireQuestions = examinationQuestionList.Where(m => requireQuestionIDs.Contains(m.ExaminationQuestionID)).ToList();
            if (requireQuestions.Count <= 0)
            {
                return [];
            }
            var questionDetailList = await _examinationQuestionDetailRepository.GetListByQuestionIDs(requireQuestionIDs);
            // 新增必选题 | 试卷模板明细数据
            var paperQuestionList = new List<PaperQuestionView>();
            // 分题库处理必选题
            var questionFilterConditionRules = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.QuestionFilterConditionRules;
            foreach (var questionFilterRuleItem in questionFilterConditionRules)
            {
                var questions = requireQuestions.Where(m => m.QuestionBankID == questionFilterRuleItem.QuestionBankID).ToList();
                var questionFilters = questionFilterRuleItem.QuestionFilters;
                // 按照规则查看除了必选题，还需要多少道题
                foreach (var questionFilterItem in questionFilters)
                {
                    var groupByQuestionType = questions.Where(m => m.ExaminationQuestionType == questionFilterItem.QuestionType).ToList();
                    // 新增必选题 | 试卷模板明细数据
                    var paperQuestions = AddDynamicFormDetailByRequireQuestion(groupByQuestionType, questionFilterItem, questionDetailList);
                    paperQuestionList.AddRange(paperQuestions);
                    // 记录当前题型中 必选题占各个难度题目的数量
                    foreach (var difficultyItem in difficultyRule)
                    {
                        questionFilterItem.QuestionDifficultyCounts.Add(difficultyItem.RuleCode, groupByQuestionType.Count(m => m.DifficultyLevel == difficultyItem.RuleCode));
                        questionFilterItem.QuestionFilterRequireQuestions.Add(difficultyItem.RuleCode, groupByQuestionType.Select(m => m.ExaminationQuestionID).ToList());
                    }
                    // 题目数量
                    //此处必选题目,需大于规则中规定的题库中该题型的的数量（前端制定规则时需要检核）
                    questionFilterItem.DynamicFilterCount = questionFilterItem.QuestionCount - groupByQuestionType.Count;
                    if (questionFilterItem.DynamicFilterCount < 0)
                    {
                        throw new Exception("必选题不符合当前题库题型设定，题库不能满足组卷条件！！");
                    }
                }
            }
            return paperQuestionList;
        }

        /// <summary>
        /// 新增必选题目的模板明细
        /// </summary>
        /// <param name="requireQuestions"></param>
        /// <param name="questionFilterItem"></param>
        /// <param name="questionDetailList"></param>
        /// <returns></returns>
        private List<PaperQuestionView> AddDynamicFormDetailByRequireQuestion(List<ExaminationQuestionInfo> requireQuestions, QuestionFilter questionFilterItem, List<ExaminationQuestionDetailInfo> questionDetailList)
        {
            int sort = 1;
            var paperQuestionList = new List<PaperQuestionView>();
            foreach (var question in requireQuestions)
            {
                var paperQuestion = new PaperQuestionView
                {
                    QuestionBankID = question.QuestionBankID,
                    QuestionID = question.ExaminationQuestionID.ToString(),
                    QuestionTitle = question.QuestionContent,
                    Analysis = question.Analysis,
                    FixedItemID = question.ExaminationQuestionID.ToString(),
                    ItemSourceType = "ExaminationQuestion",
                    QuestionType = question.ExaminationQuestionType,
                    ComponentListID = question.ComponentListID,
                    Score = questionFilterItem.Score,
                    Options = SortQuestionOption(question, questionDetailList).Select(m => new PaperQuestionOptionsView
                    {
                        Value = m.ExaminationQuestionDetailID,
                        Label = m.Content,
                        ItemSourceType = "ExaminationQuestionDetail",
                    }).ToList(),
                };
                sort += 1;
                paperQuestionList.Add(paperQuestion);
            }
            return paperQuestionList;
        }
    }
}
