﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    public class EmployeeDepartmentChangeRequestAfterApproval : ICommonProcessingAfterApproval
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeDepartmentChangeRequestRepository _employeeDepartmentChangeRequestRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IRequestApiService _requestApiService;

        public EmployeeDepartmentChangeRequestAfterApproval(
            IUnitOfWork unitOfWork
            , IEmployeeDepartmentChangeRequestRepository employeeDepartmentChangeRequestRepository
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , IRequestApiService requestApiService)
        {
            _unitOfWork = unitOfWork;
            _employeeDepartmentChangeRequestRepository = employeeDepartmentChangeRequestRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _requestApiService = requestApiService;
        }
        public override async Task<(string, int)[]> ProcessAfterApprovalAsync(ProcessAfterApprovalView view)
        {
            var requestInfo = await _employeeDepartmentChangeRequestRepository.GetEmployeeDepartmentChangeRequestByID(view.SourceID);
            if (requestInfo == null)
            {
                _logger.Error("未找到审批记录来源，回写审批结果失败");
                return null;
            }
            if (requestInfo.StatusCode != view.ApprovalResult)
            {
                //当审批状态改变的时候 回写状态
                requestInfo.StatusCode = view.ApprovalResult;
            }
            //审批通过，更新员工部门信息
            if (view.ApprovalResult == ApprovalStatus.Completed.ToString("d"))
            {
                requestInfo.StatusCode = "2";
                requestInfo.Modify(requestInfo.AddEmployeeID);
                //更新Staff中的员工部门
                var employeeStaffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(requestInfo.EmployeeID);
                if (employeeStaffData == null)
                {
                    _logger.Error($"未找到对应员工信息，EmployeeID：{requestInfo.EmployeeID}，回写审批结果失败");
                    return null;
                }
                employeeStaffData.DepartmentID = requestInfo.DepartmentID;
                employeeStaffData.Modify(requestInfo.AddEmployeeID);
                _unitOfWork.GetRepository<EmployeeStaffDataInfo>().Update(employeeStaffData);
            }
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                if (view.ApprovalResult == "2")
                {
                    //更新缓存
                    await _employeeStaffDataRepository.UpdateCache();
                    //调用同步程序接口，向中间库表存放消息
                    await _requestApiService.RequestAPI("EmployeeDepartmentChangeRequest", ListToJson.ToJson(requestInfo));
                }
                // TODO: 待补充跳转路径
                return [];
            }
            return null;
        }
    }
}
