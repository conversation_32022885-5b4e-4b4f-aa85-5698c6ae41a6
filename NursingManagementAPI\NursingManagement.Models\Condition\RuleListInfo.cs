﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 规则字典主表
    /// </summary>
    [Serializable]
    [Table("RuleList")]
    public class RuleListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 规则属性主表ID（非自增）
        /// </summary>
        public int RuleListID { get; set; }
        /// <summary>
        /// 规则属性Code
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string RuleCode { get; set; }
        /// <summary>
        /// 规则呈现名称
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ShowName { get; set; }
        /// <summary>
        /// 规则属性表述
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string Description { get; set; }
        /// <summary>
        /// 规则前端值呈现控件类型(配置在ComponentList)
        /// </summary>
        public int ComponentListID { get; set; }
        /// <summary>
        /// 默认值（当默认值非文本类型时，需要判断字典明细表是否存在）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string DefaultValue { get; set; }
        /// <summary>
        /// 使用的条件组（使用逗号拼接）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ConditionCode { get; set; }
        /// <summary>
        /// 使用系统（培训、考核、排班）
        /// </summary>
        [Column(TypeName = "varchar(40)")]
        public string SystemType { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言序号
        /// </summary>
        public int Language { get; set; }
    }
}
