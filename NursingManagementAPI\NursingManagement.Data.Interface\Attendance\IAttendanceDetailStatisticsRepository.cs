﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IAttendanceDetailStatisticsRepository
    {
        /// <summary>
        /// 获取考勤明细统计
        /// </summary>
        /// <param name="attendanceRecordID"></param>
        /// <returns></returns>
        Task<List<AttendanceDetailStatisticsInfo>> GetDetailStatisticsByRecordID(string attendanceRecordID);

        /// <summary>
        /// 获取批量考勤明细统计
        /// </summary>
        /// <param name="attendanceRecordIDs"></param>
        /// <returns></returns>
        Task<List<AttendanceDetailStatisticsInfo>> GetDetailStatisticsByRecordIDs(List<string> attendanceRecordIDs);
    }
}
