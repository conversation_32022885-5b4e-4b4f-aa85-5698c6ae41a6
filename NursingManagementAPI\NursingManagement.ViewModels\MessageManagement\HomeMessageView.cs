﻿namespace NursingManagement.ViewModels
{
    public class HomeMessageView
    {
        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime PublishTime { get; set; }
        /// <summary>
        /// 消息标题
        /// </summary>
        public string MessageTitle { get; set; }
        /// <summary>
        /// 消息内容
        /// </summary>
        public string MessageRecordID { get; set; }
        /// <summary>
        /// 消息类型
        /// </summary>
        public string MessageType { get; set; }
        /// <summary>
        /// 是否为当前最新讯息
        /// </summary>
        public bool IsNew { get; set; }
        /// <summary>
        /// 路由ID
        /// </summary>
        public int? RouterListID { get; set; }
        /// <summary>
        /// 路由路径
        /// </summary>
        public string RouterPath { get; set; }
        /// <summary>
        /// 置顶
        /// </summary>
        public bool IsTop { get; set; }
        /// <summary>
        /// 消息类型名称
        /// </summary>
        public string MessageTypeName { get; set; }
        /// <summary>
        /// 消息内容
        /// </summary>
        public string MessageContent { get; set; }
    }

    public class HomeMessageViewList
    {
        /// <summary>
        /// 消息列表
        /// </summary>
        public List<HomeMessageView> MessageList { get; set; }
        /// <summary>
        /// 置顶消息列表
        /// </summary>
        public List<HomeMessageView> TopMessageList { get; set; }
    }
}
