﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels.EmployeeGroup;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 用户组仓储
    /// </summary>
    /// <param name="dbContext">上下文</param>
    public class EmployeeGroupRepository(
        NursingManagementDbContext dbContext
        ) : IEmployeeGroupRepository
    {

        public async Task<EmployeeGroupDto[]> GetEmployeeGroups()
        {
            return await dbContext.EmployeeGroupInfos.Where(m => m.DeleteFlag != "*").Select(m => new EmployeeGroupDto(
                m.EmployeeGroupID,
                m.GroupName,
                m.EmployeeIDs,
                m.ModifyEmployeeID,
                m.ModifyDateTime
                )).ToArrayAsync();
        }

        public async Task<EmployeeGroupInfo> GetEmployeeGroupInfo(int employeeGroupID)
        {
            var employeeGroup = await dbContext.EmployeeGroupInfos.FirstOrDefaultAsync(m => m.EmployeeGroupID == employeeGroupID && m.DeleteFlag != "*");
            return employeeGroup;
        }
    }
}
