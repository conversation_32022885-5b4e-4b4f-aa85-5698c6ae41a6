﻿using NursingManagement.Models.Examine;

namespace NursingManagement.Data.Interface
{
    public interface IExaminationDetailRepository
    {
        /// <summary>
        /// 根据MainID获取明细记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<List<ExaminationDetailInfo>> GetListByMainIDAsNoTracking(string mainID);
        /// <summary>
        /// 根据MainID获取明细记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<List<ExaminationDetailInfo>> GetListByMainID(string mainID);
        /// <summary>
        /// 根据MainID获取所有状态明细记录数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationDetailInfo>> GetAllListByMainIDsAsNoTracking(List<string> mainIDs);
    }
}
