using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 培训报名记录仓储接口
    /// </summary>
    public class SignUpRecordRepository : ISignUpRecordRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public SignUpRecordRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 报名状态 - 同意
        /// </summary>
        private const string STATUSCODE_AGREE = "1";
        /// <summary>
        /// 根据来源ID和来源类型获取签到集合
        /// </summary>
        /// <param name="sourceID">来源ID：课程培训记录ID 或者考核记录ID</param>
        /// <param name="sourceType">来源类型：培训、考核等 select * from Settingdictionary where settingTypeCode ='TrainingExaminationSignUp'</param>
        /// <returns></returns>
        public async Task<List<SignUpRecordInfo>> GetListByRecordID(string sourceID, string sourceType)
        {
            return await _dbContext.SignUpRecordInfos.Where(m => m.SourceID == sourceID && m.SourceType == sourceType && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主键获取报名信息
        /// </summary>
        /// <param name="signUpRecordID">主键ID</param>
        /// <returns>报名信息</returns>
        public async Task<SignUpRecordInfo> GetByIDAsync(string signUpRecordID)
        {
            return await _dbContext.SignUpRecordInfos.FirstOrDefaultAsync(m => m.SignUpRecordID == signUpRecordID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据 SourceID 获取报名信息
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <returns>报名信息</returns>
        public async Task<SignUpRecordInfo> GetBySourceIDAsNoTrackAsync(string sourceID)
        {
            return await _dbContext.SignUpRecordInfos
                .Where(m => m.SourceID == sourceID && m.DeleteFlag != "*")
                .Select(m => new SignUpRecordInfo
                {
                    SignUpRecordID = m.SignUpRecordID,
                    SourceID = m.SourceID,
                    SourceType = m.SourceType,
                    StatusCode = m.StatusCode,
                    SignUpType = m.SignUpType,
                    EmployeeID = m.EmployeeID,
                    AddDateTime = m.AddDateTime
                })
                .AsNoTracking()
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据 EmployeeID 获取 EmployeeID 和 StatusCode
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <returns>包含 EmployeeID 和 StatusCode 的报名信息列表</returns>
        public async Task<List<SignUpRecordInfo>> GetEmployeeIDAndStatusCodeAsNoTrackAsync(string employeeID)
        {
            return await _dbContext.SignUpRecordInfos
                .Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*")
                .Select(m => new SignUpRecordInfo
                {
                    EmployeeID = m.EmployeeID,
                    StatusCode = m.StatusCode
                })
                .AsNoTracking()
                .ToListAsync();
        }

        /// <summary>
        /// 根据条件表达式获取报名信息列表 (完整字段，跟踪)
        /// </summary>
        /// <param name="predicate">查询条件表达式</param>
        /// <returns>报名信息列表</returns>
        public async Task<List<SignUpRecordInfo>> GetListByConditionAsync(Expression<Func<SignUpRecordInfo, bool>> predicate)
        {
            return await _dbContext.SignUpRecordInfos.Where(m => m.DeleteFlag != "*")
                .Where(predicate).ToListAsync();
        }

        /// <summary>
        /// 根据条件表达式获取报名信息列表 (部分字段，不跟踪)
        /// </summary>
        /// <param name="predicate">查询条件表达式</param>
        /// <returns>报名信息列表</returns>
        public async Task<List<SignUpRecordInfo>> GetListByConditionAsNoTrackAsync(Expression<Func<SignUpRecordInfo, bool>> predicate)
        {
            return await _dbContext.SignUpRecordInfos.Where(m => m.DeleteFlag != "*")
                .Where(predicate)
                .Select(m => new SignUpRecordInfo
                {
                    SignUpRecordID = m.SignUpRecordID,
                    SourceID = m.SourceID,
                    SourceType = m.SourceType,
                    StatusCode = m.StatusCode,
                    SignUpType = m.SignUpType,
                    EmployeeID = m.EmployeeID,
                    AddDateTime = m.AddDateTime,
                    AddEmployeeID = m.AddEmployeeID,
                }).ToListAsync();
        }
        /// <summary>
        /// 根据来源ID获取对应的报名人员工号（报名通过的人员）
        /// </summary>
        /// <param name="sourceID">报名来源</param>
        /// <returns></returns>
        public async Task<List<string>> GetSignUpEmployeeIDsBySourceID(string sourceID)
        {
            return await _dbContext.SignUpRecordInfos.Where(m => m.SourceID == sourceID && m.StatusCode == STATUSCODE_AGREE && m.DeleteFlag != "*").Select(m => m.EmployeeID).ToListAsync();
        }
        /// <summary>
        /// 根据来源id集合,来源类别获取数据
        /// </summary>
        /// <param name="sourceIDs">来源id集合</param>
        /// <param name="sourceType">来源类别</param>
        /// <returns></returns>
        public async Task<List<SignUpRecordInfo>> GetListBySourceIDAsNoTrackAsync(List<string> sourceIDs, string sourceType)
        {
            return await _dbContext.SignUpRecordInfos
                .Where(m => sourceIDs.Any(n => n == m.SourceID) && m.SourceType == sourceType && m.DeleteFlag != "*")
                .Select(m => new SignUpRecordInfo
                {
                    EmployeeID = m.EmployeeID,
                    StatusCode = m.StatusCode
                })
                .AsNoTracking()
                .ToListAsync();
        }
    }

}
