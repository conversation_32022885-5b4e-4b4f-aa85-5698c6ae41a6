﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Commands;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 年度计划排程controller
    /// </summary>
    [Produces("application/json")]
    [Route("api/annualSchedule")]
    [EnableCors("any")]
    public class AnnualScheduleController : Controller
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualScheduleService _annualScheduleService;
        private readonly ISessionService _session;
        /// <summary>
        /// 年度计划排程
        /// </summary>
        /// <param name="annualScheduleService"></param>
        /// <param name="session"></param>
        public AnnualScheduleController(
            IAnnualScheduleService annualScheduleService,
            ISessionService session)
        {
            _annualScheduleService = annualScheduleService;
            _session = session;
        }
        /// <summary>
        /// 获取年度计划排程主记录
        /// </summary>
        /// <param name="scheduleMonth">计划月份</param>
        /// <param name="scheduleYear">计划年度</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAPSchedules")]
        public async Task<IActionResult> GetAPSchedules(int? scheduleMonth, int scheduleYear)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.GetAPSchedules(session.EmployeeID, scheduleMonth, scheduleYear, session.DepartmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取年度计划排程明细
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualScheduleDetails")]
        public async Task<IActionResult> GetAnnualScheduleDetails(string scheduleMainID)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.GetAnnualScheduleDetailsAsync(scheduleMainID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存年度计划排程
        /// </summary>
        /// <param name="scheduleParamsView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualSchedule")]
        public async Task<IActionResult> SaveAnnualSchedule([FromBody] AnnualScheduleParamsView scheduleParamsView)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.SaveAnnualScheduleAsync(scheduleParamsView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除年度计划排程
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteAnnualSchedule")]
        public async Task<IActionResult> DeleteAnnualSchedule(string scheduleMainID)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            (result.Data, result.Message) = await _annualScheduleService.DeleteAnnualScheduleAsync(scheduleMainID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取年度计划排程每月数量统计量
        /// </summary>
        /// <param name="schedulePerformer"></param>
        /// <param name="scheduleYear"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualScheduleStatistics")]
        public async Task<IActionResult> GetAnnualScheduleStatistics(string schedulePerformer, int scheduleYear)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.GetAnnualScheduleStatisticsAsync(schedulePerformer, scheduleYear);
            return result.ToJson();
        }
        /// <summary>
        /// 展出下月任务
        /// </summary>
        /// <param name="request">新增视图</param>
        /// <returns></returns>
        [HttpPost]
        [Route("CreateOrUpdateTasks")]
        public async Task<IActionResult> CreateOrUpdateTasks([FromBody] CreateOrUpdateTasksRequest request)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var cmd = new CreateOrUpdateTasksCommand(request.DepartmentID, request.Year, request.Month, request.Works, session.HospitalID, session.EmployeeID);
            result.Data = await _annualScheduleService.CreateOrUpdateTasks(cmd);
            return result.ToJson();
        }

        /// <summary>
        /// 获取一个月的排程量
        /// </summary>
        /// <param name="scheduleYear"></param>
        /// <param name="scheduleMonth"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetStatisticValueByMonth")]
        public async Task<IActionResult> GetStatisticValueByMonth(int scheduleYear, int scheduleMonth)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.GetStatisticValueByMonthAsync(scheduleYear, scheduleMonth, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 发送年度计划提醒每个月1号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SendAnnualPlanRemind")]
        public async Task<IActionResult> SendAnnualPlanRemind()
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.SendAnnualPlanRemindAsync();
            return result.ToJson();
        }

        /// <summary>
        /// 获取计划来源
        /// </summary>
        /// <param name="annualScheduleMainID"></param>
        /// <param name="interventionID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualScheduleSource")]
        public async Task<IActionResult> GetAnnualScheduleSource(string annualScheduleMainID, int interventionID)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.GetTaskSource(annualScheduleMainID, interventionID);
            return result.ToJson();
        }
        /// <summary>
        /// 手动加入年度计划
        /// </summary>
        /// <param name="scheduleDate">预计执行日期</param>
        /// <param name="scheduleContent">措施内容</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddAnnualScheduleManual")]
        public async Task<IActionResult> AddAnnualScheduleManual(DateTime scheduleDate, string scheduleContent)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualScheduleService.AddAnnualScheduleManual(scheduleDate, scheduleContent, session.HospitalID, session.EmployeeID, session.DepartmentID);
            return result.ToJson();
        }
    }
}
