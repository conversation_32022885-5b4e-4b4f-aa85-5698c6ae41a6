using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;

namespace NursingManagement.UnitTest
{
    public class UnitTest1
    {
        private readonly IAnnualPlanGoalGroupRepository _annualPlanGoalGroupRepository;
        private readonly IUnitOfWork<NursingManagementDbContext> _unitOfWork;

        public UnitTest1(IAnnualPlanGoalGroupRepository annualPlanGoalGroupRepository,
            IUnitOfWork<NursingManagementDbContext> unitOfWork)
        {
            _annualPlanGoalGroupRepository = annualPlanGoalGroupRepository;
            _unitOfWork = unitOfWork;
        }

        [Fact]
        public async Task Test1()
        {

            await using var transaction = await _unitOfWork.DbContext.Database.BeginTransactionAsync();
            var goalGroups = await _annualPlanGoalGroupRepository.GetGoalGroups(new List<string> { "7a5f94ad7d5d4bf68db5153d4ea4db1a" });
            Assert.Single(goalGroups);
            var newVal = (int.Parse(goalGroups.First().ModifyEmployeeID) + 1).ToString();
            goalGroups.First().ModifyEmployeeID = newVal;
            await _unitOfWork.DbContext.SaveChangesAsync();
            await transaction.CommitAsync();
        }
    }
}