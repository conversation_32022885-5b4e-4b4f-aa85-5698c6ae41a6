﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository
{
    public class EmployeeStaffDataRepository : IEmployeeStaffDataRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IRedisService _redisService;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;

        public EmployeeStaffDataRepository(NursingManagementDbContext dbContext
            , SessionCommonServer sessionCommonServer
            , IRedisService redisService
            , IDepartmentListRepository departmentListRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
        )
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
            _departmentListRepository = departmentListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
        }

        public async Task<EmployeeStaffDataInfo> GetEmployeeStaffDataByID(string employeeID)
        {
            var employeeStaffDataInfos = await GetEmployeeStaffDataView();
            var employee = employeeStaffDataInfos.Where(m => m.EmployeeID == employeeID).FirstOrDefault();
            if (employee == null)
            {
                employee = await _dbContext.EmployeeStaffDataInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").FirstOrDefaultAsync();
            }
            return employee;
        }

        //TODO未进行优化
        public async Task<T> GetFieldValueByEmployeeIDAsync<T>(string employeeID, [DisallowNull] Expression<Func<EmployeeStaffDataInfo, T>> predicate)
        {
            var whereCondition = _dbContext.EmployeeStaffDataInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*");
            if (predicate != null)
            {
                return await whereCondition.Select(predicate).FirstOrDefaultAsync();
            }

            return default;
        }

        public async Task<List<EmployeeListView>> GetStaffByEmployeeQueryView(EmployeeQueryView employeeQueryView)
        {
            var employeeStaffDataInfos = await GetEmployeeStaffDataView();
            var employeePersonalDataInfos = await _employeePersonalDataRepository.GetEmployeePersonalDataView();
            var departmentListInfos = await _departmentListRepository.GetByCacheAsync();

            var result = (from staff in employeeStaffDataInfos
                          join personal in employeePersonalDataInfos
                          on new { staff.EmployeeID }
                          equals new { personal.EmployeeID }
                          join departemnt in departmentListInfos
                          on staff.DepartmentID equals departemnt.DepartmentID
                          where staff.StatusCode != "0"
                          select new EmployeeListView
                          {
                              EmployeeID = personal.EmployeeID,
                              DepartmentID = staff.DepartmentID,
                              DepartmentName = departemnt.LocalShowName,
                              HrpEmployeeID = staff.HrpEmployeeID,
                              EmployeeName = personal.EmployeeName,
                              GenderCode = personal.GenderCode,
                              NationCode = personal.NationCode,
                              Birthdate = personal.Birthdate,
                              EntryDate = staff.EntryDate,
                              HomeAddress = personal.HomeAddress,
                              ActualAddress = personal.ActualAddress,
                              NativePlace = personal.NativePlace,
                              FileID = staff.FileID,
                              JobCategoryCode = staff.JobCategoryCode,
                              CapabilityLevelID = staff.CapabilityLevelID,
                              StatusCode = staff.StatusCode
                          });
            result = result.IfWhere(employeeQueryView.NurseFlag.HasValue && employeeQueryView.NurseFlag.Value, m => !string.IsNullOrEmpty(m.FileID) && m.FileID.StartsWith("HL-"));
            result = result.IfWhere(!string.IsNullOrEmpty(employeeQueryView.EmployeeName), m => m.EmployeeName.Contains(employeeQueryView.EmployeeName));
            result = result.IfWhere(employeeQueryView.EntryDate?.Length > 0,
                m => Convert.ToDateTime(employeeQueryView.EntryDate[0]) <= m.EntryDate.Value && Convert.ToDateTime(employeeQueryView.EntryDate[1]) >= m.EntryDate.Value);
            result = result.IfWhere(employeeQueryView.DepartmentIDs?.Length > 0, m => m.DepartmentID.HasValue && employeeQueryView.DepartmentIDs.Contains(m.DepartmentID.Value));
            return result.ToList();
        }

        public async Task<List<string>> GetEmployeeIDsByDepartmentIDAsync(int? departmentID, bool nurseFlag = true)
        {
            var employeeStaffDataInfos = await GetEmployeeStaffDataView();
            var data = employeeStaffDataInfos.Where(m => m.StatusCode != "0")
                .IfWhere(departmentID.HasValue, m => m.DepartmentID == departmentID || departmentID.Value == 999999)
                .IfWhere(nurseFlag, m => !string.IsNullOrEmpty(m.FileID) && m.FileID.StartsWith("HL"))
                .Select(m => m.EmployeeID);
            return data.ToList();
        }

        /// <summary>
        /// 获取员工信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startFileIDs">档案编号</param>
        /// <returns></returns>
        public async Task<List<EmployeeStaffDataInfo>> GetStaffsByDepartmentID(int? departmentID, string[] startFileIDs = null)
        {
            var staffs = await GetEmployeeStaffDataView();
            var data = staffs.Where(m => m.StatusCode != "0")
                .IfWhere(departmentID.HasValue, m => m.DepartmentID == departmentID)
                .IfWhere(startFileIDs == null, m => !string.IsNullOrEmpty(m.FileID) && m.FileID.StartsWith("HL"))
                .IfWhere(startFileIDs != null, m => !string.IsNullOrEmpty(m.FileID) && startFileIDs.Any(n => m.FileID.StartsWith(n)))
                .ToList();
            return data;
        }

        /// <summary>
        /// 根据科室ID获取护士信息
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        public async Task<List<EmployeeListView>> GetEmployeeViewByDepartmentIDAsync(int departmentID)
        {
            var employeeStaffDataInfos = await GetEmployeeStaffDataView();
            var employeePersonalDataInfos = await _employeePersonalDataRepository.GetEmployeePersonalDataView();
            var departmentListInfos = await _departmentListRepository.GetByCacheAsync();
            employeeStaffDataInfos = employeeStaffDataInfos.Where(m => m.DepartmentID == departmentID && !string.IsNullOrEmpty(m.FileID) && m.FileID.StartsWith("HL")).ToList();

            var result = (from staff in employeeStaffDataInfos
                          join personal in employeePersonalDataInfos
                          on new { staff.EmployeeID }
                          equals new { personal.EmployeeID }
                          join departemnt in departmentListInfos
                          on staff.DepartmentID equals departemnt.DepartmentID
                          where staff.StatusCode != "0"
                          select new EmployeeListView
                          {
                              EmployeeID = personal.EmployeeID,
                              DepartmentID = staff.DepartmentID,
                              DepartmentName = departemnt.LocalShowName,
                              EmployeeName = personal.EmployeeName,
                              GenderCode = personal.GenderCode,
                              CapabilityLevelID = staff.CapabilityLevelID,
                              NamePinyin = personal.NamePinyin
                          }).ToList();
            return result;
        }

        public async Task<List<EmployeeStaffDataInfo>> GetEmployeeListByDeptIDOrEmployeeIDs(int statusCode, int? departmentID, string[] employeeIDs = null)
        {
            var EmployeeStaffList = await GetEmployeeStaffDataView();
            return EmployeeStaffList.Where(m => m.StatusCode == statusCode.ToString())
                    .IfWhere(departmentID != null, m => m.DepartmentID == departmentID)
                    .IfWhere(employeeIDs != null, m => employeeIDs.Contains(m.EmployeeID))
                    .ToList();
        }

        public async Task<List<EmployeeStaffDataInfo>> GetEmployeeStaffDataView()
        {
            return await GetCacheAsync() as List<EmployeeStaffDataInfo>;
        }

        /// <summary>
        /// 获取有护理层级的人员数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<EmployeeStaffDataInfo>> GetHaveCapabilityLevelList()
        {
            var datats = await GetCacheAsync() as List<EmployeeStaffDataInfo>;
            return datats.Where(m => m.CapabilityLevelID.HasValue).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                return await _dbContext.EmployeeStaffDataInfos.Where(m =>
                  m.HospitalID == hospitalID && m.DeleteFlag != "*" && m.SpecialFlag != "1").ToListAsync();
            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeStaffData.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 获取所有在职人员
        /// </summary>
        /// <returns></returns>
        public async Task<List<EmployeeStaffDataInfo>> GetOnJobEmployeeStaffDataView()
        {
            var list = await GetCacheAsync() as List<EmployeeStaffDataInfo>;
            return list.Where(m => m.StatusCode != "0").ToList();
        }

        /// <summary>
        /// 获取离职人员信息
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="departmentIDs">工号集合</param>
        /// <param name="beginResignationApplyDate">申请开始日期</param>
        /// <param name="endResignationApplyDate">申请结束日期</param>
        /// <param name="beginResignationDate">开始日期</param>
        /// <param name="endResignationDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<EmployeeResignationView>> GetEmployeeResignationListAsync(string hospitalID, List<string> departmentIDs, DateTime? beginResignationApplyDate
        , DateTime? endResignationApplyDate, DateTime? beginResignationDate, DateTime? endResignationDate)
        {
            var query = (from a in _dbContext.EmployeePersonalDataInfos
                         join b in _dbContext.EmployeeStaffDataInfos on a.EmployeeID equals b.EmployeeID
                         where a.DeleteFlag != "*" && b.DeleteFlag != "*" && a.HospitalID == hospitalID && b.HospitalID == hospitalID && b.ResignationApplyDate != null && b.ResignationDate != null
                         select new EmployeeResignationView
                         {
                             EmployeeID = a.EmployeeID,
                             EmployeeName = a.EmployeeName,
                             Gender = a.GenderCode,
                             Title = b.Title,
                             DepartmentID = b.DepartmentID.Value.ToString(),
                             ResignationApplyDate = b.ResignationApplyDate,
                             ResignationDate = b.ResignationDate
                         })
                        .IfWhere(departmentIDs != null && departmentIDs.Count > 0, m => departmentIDs.Contains(m.DepartmentID))
                        .IfWhere(beginResignationApplyDate.HasValue, m => m.ResignationApplyDate.HasValue && m.ResignationApplyDate >= beginResignationApplyDate.Value)
                        .IfWhere(endResignationApplyDate.HasValue, m => m.ResignationApplyDate.HasValue && m.ResignationApplyDate <= endResignationApplyDate.Value)
                        .IfWhere(beginResignationDate.HasValue, m => m.ResignationDate.HasValue && m.ResignationDate >= beginResignationDate.Value)
                        .IfWhere(endResignationDate.HasValue, m => m.ResignationDate.HasValue && m.ResignationDate <= endResignationDate.Value);
            return await query.ToListAsync();
        }

        public async Task<Dictionary<string, int?>> GetEmployeeDepartmentIDs(IEnumerable<string> employeeIDs)
        {
            if (employeeIDs == null || !employeeIDs.Any())
            {
                return [];
            }
            var employeeStaffDataInfos = await GetEmployeeStaffDataView();
            return employeeStaffDataInfos.Where(m => employeeIDs.Contains(m.EmployeeID) && m.StatusCode == "1")
                .ToDictionary(m => m.EmployeeID, m => m.DepartmentID);
        }
    }
}
