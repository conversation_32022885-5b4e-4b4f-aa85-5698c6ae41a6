﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员培训记录表
    /// </summary>
    [Serializable]
    [Table("TrainingLearner")]
    public class TrainingLearnerInfo : MutiModifyInfo
    {
        /// <summary>
        /// 人员培训记录ID
        /// </summary>
        [Key]
        [Column(TypeName = "VARCHAR(32)")]
        public string TrainingLearnerID { get; set; }

        /// <summary>
        /// 培训记录ID
        /// </summary>
        [Required]
        [Column(TypeName = "VARCHAR(32)")]
        public string TrainingRecordID { get; set; }

        /// <summary>
        /// 医院编码
        /// </summary>
        [Required]
        [Column(TypeName = "VARCHAR(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 培训人员员工编号
        /// </summary>
        [Required]
        [Column(TypeName = "VARCHAR(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        [Required]
        public int DepartmentID { get; set; }
        /// <summary>
        /// 最后培训时间
        /// </summary>
        public DateTime? LastTrainingTime { get; set; }

        /// <summary>
        /// 参与培训时长
        /// </summary>
        public decimal? TrainingDuration { get; set; }

        /// <summary>
        /// 培训进度，参与培训时长/总时长*100
        /// </summary>
        public decimal? Progress { get; set; }

        /// <summary>
        /// 培训总学习次数
        /// </summary>
        public int? LearningCount { get; set; }

        /// <summary>
        /// 班长标记
        /// </summary>
        [Required]
        public bool MonitorFlag { get; set; }

        /// <summary>
        /// 培训过程表现（积极、消极等）
        /// </summary>
        [Column(TypeName = "VARCHAR(50)")]
        public string TrainingComment { get; set; }

        /// <summary>
        /// 课程满意度（分）
        /// </summary>
        public int? CourseSatisfaction { get; set; }

        /// <summary>
        /// 课程建议
        /// </summary>
        [Column(TypeName = "NVARCHAR(255)")]
        public string CourseRecommendations { get; set; }
    }
}