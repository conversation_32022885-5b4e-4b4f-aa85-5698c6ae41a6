﻿using NursingManagement.Models;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Data.Interface
{
    public interface IExaminationConditionRecordRepository
    {
        /// <summary>
        /// 获取所有考核组卷规则记录
        /// </summary>
        /// <returns></returns>
        Task<List<ExaminationConditionRecordView>> GetAllConditionRecordViews();
        /// <summary>
        /// 根据主键ID获取组卷规则记录
        /// </summary>
        /// <param name="examinationConditionRecordID">组卷规则记录ID</param>
        /// <returns></returns>
        Task<ExaminationConditionRecordInfo> GetRecordByIdAsync(string examinationConditionRecordID);
        /// <summary>
        /// 获取组卷条件名称
        /// </summary>
        /// <param name="conditionRecordIDs">组卷条件记录ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string,string>> GetConditionNameAsync(List<string> conditionRecordIDs);
        /// <summary>
        /// 获取规则名称
        /// </summary>
        /// <param name="examinationConditionRecordID"></param>
        /// <returns></returns>
        Task<string> GetConditionNameByIdAsync(string examinationConditionRecordID);
    }
}
