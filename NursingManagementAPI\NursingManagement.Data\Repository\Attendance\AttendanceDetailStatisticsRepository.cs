﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AttendanceDetailStatisticsRepository : IAttendanceDetailStatisticsRepository
    {
        private readonly NursingManagementDbContext _context;

        public AttendanceDetailStatisticsRepository(NursingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<List<AttendanceDetailStatisticsInfo>> GetDetailStatisticsByRecordID(string attendanceRecordID)
        {
            return await _context.AttendanceDetailStatisticsInfos.Where(m => m.AttendanceRecordID == attendanceRecordID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<AttendanceDetailStatisticsInfo>> GetDetailStatisticsByRecordIDs(List<string> attendanceRecordIDs)
        {
            return await _context.AttendanceDetailStatisticsInfos.Where(m => attendanceRecordIDs.Contains(m.AttendanceRecordID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
