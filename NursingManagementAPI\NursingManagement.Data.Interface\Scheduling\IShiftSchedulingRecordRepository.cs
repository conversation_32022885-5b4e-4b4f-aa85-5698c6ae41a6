﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IShiftSchedulingRecordRepository
    {
        /// <summary>
        /// 获取部门排班主记录
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="statusCode">状态：1已发布，0暂存，默认已发布</param>
        /// <returns></returns>
        Task<ShiftSchedulingRecordInfo> GetRecordByDepartmentID(int departmentID, DateTime startDate, DateTime endDate, string statusCode = "1");

        /// <summary>
        /// 获取部门排班主记录
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <returns></returns>
        Task<ShiftSchedulingRecordInfo> GetRecordByID( string shiftSchedulingRecordID);
    }
}
