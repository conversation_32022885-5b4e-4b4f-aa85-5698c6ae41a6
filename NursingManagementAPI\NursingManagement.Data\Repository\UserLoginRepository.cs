﻿using Microsoft.EntityFrameworkCore;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Text;

namespace NursingManagement.Data.Repository
{
    public class UserLoginRepository : IUserLoginRepository
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public UserLoginRepository(
            NursingManagementDbContext db
            , IRedisService redisService
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<UserLoginInfo> CheckOAUser(string oaUserID, string oaPassword)
        {
            try
            {
                var userLogin = await GetByOAUserID(oaUserID);
                // 若密码被修改过，尝试使用二进制编码对比
                if (userLogin.PasswordChanged && compareByCode(userLogin.OAPassword))
                {
                    return userLogin;
                }
                // 使用SQL Server加密函数对比
                return compareBySql() ? userLogin : null;
            }
            catch (Exception ex)
            {
                _logger.Error($"使用OA账户登录时解密报错{ex}");
                return null;
            }
            bool compareBySql()
            {
                var sql = $"select PWDCOMPARE('{oaPassword}',OAPassword) from UserLogin where OAUserID='{oaUserID}'";
                var result = _nursingManagementDbContext.Database.SqlQueryRaw<int>(sql).ToList();
                return result.Count == 1 && result[0] == 1;
            };
            bool compareByCode(byte[] password)
            {
                return password == Encoding.UTF8.GetBytes(oaPassword);
            };
        }

        public async Task<UserLoginInfo> GetByOAUserID(string oaUserID)
        {
            var userLoginList = await GetCacheAsync() as List<UserLoginInfo>;
            return userLoginList.FirstOrDefault(m => m.OAUserID == oaUserID);
        }

        public async Task<UserLoginInfo> GetByHISUserID(string hisUserID)
        {
            var userLoginList = await GetCacheAsync() as List<UserLoginInfo>;
            return userLoginList.FirstOrDefault(m => m.HisUserID == hisUserID);
        }

        public async Task<UserLoginInfo> GetByWechatWebOpenID(string wechatWebOpenID)
        {
            var userLoginList = await GetCacheAsync() as List<UserLoginInfo>;
            return userLoginList.FirstOrDefault(m => m.WechatWebOpenID == wechatWebOpenID);
        }

        public async Task<UserLoginInfo> GetByWechatMiniProgramOpenID(string wechatMiniProgramOpenID)
        {
            var userLoginList = await GetCacheAsync() as List<UserLoginInfo>;
            return userLoginList.FirstOrDefault(m => m.WechatMiniProgramOpenID == wechatMiniProgramOpenID);
        }

        public async Task<UserLoginInfo> GetByWechatUnionID(string wechatUnionID)
        {
            var userLoginList = await GetCacheAsync() as List<UserLoginInfo>;
            return userLoginList.FirstOrDefault(m => m.WechatUnionID == wechatUnionID);
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.UserLoginInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.UserLogin.GetKey(_sessionCommonServer);
        }

        public async Task<UserLoginInfo> GetByEmployeeID(string employeeID)
        {
            var userLoginList = await GetCacheAsync() as List<UserLoginInfo>;
            return userLoginList.FirstOrDefault(m => m.EmployeeID == employeeID);
        }
        /// <summary>
        ///  获取绑定微信服务号的员工ID结果集
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetEmployeeIDsByWechatUnionIDIsNotNull()
        {
            var userLoginList = await GetCacheAsync() as List<UserLoginInfo>;
            return userLoginList.Where(m => !string.IsNullOrEmpty(m.WechatWebOpenID) ).Select(m=>m.EmployeeID).ToList();
        }
    }
}
