﻿using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;

namespace NursingManagement.Services
{
    /// <summary>
    /// 审批页面动态类内容拼接
    /// </summary>
    public class ApproveDynamicColumnService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IDynamicColumnAttributeRepository _dynamicColumnAttributeRepository;
        private readonly ScheduleRequestDynamicColumn _scheduleRequestDynamicColumn;
        private readonly AdjustScheduleDynamicColumn _adjustScheduleDynamicColumn;
        private readonly AttendanceDynamicColumn _attendanceDynamicColumn;
        private readonly EmployeeDeptChangeDynamicColumn _employeeDeptChangeDynamicColumn;
        private readonly EmployeeSecondmentDynamicColumn _employeeSecondmentDynamicColumn;
        private readonly HierarchicalQcDynamicColumn _hierarchicalQcDynamicColumn;

        public ApproveDynamicColumnService(
            IDynamicColumnAttributeRepository dynamicColumnAttributeRepository,
            ScheduleRequestDynamicColumn scheduleRequestDynamicColumn,
            AdjustScheduleDynamicColumn adjustScheduleDynamicColumn,
            AttendanceDynamicColumn attendanceDynamicColumn,
            HierarchicalQcDynamicColumn hierarchicalQcDynamicColumn,
            EmployeeSecondmentDynamicColumn employeeSecondmentDynamicColumn)
        {
            _dynamicColumnAttributeRepository = dynamicColumnAttributeRepository;
            _scheduleRequestDynamicColumn = scheduleRequestDynamicColumn;
            _adjustScheduleDynamicColumn = adjustScheduleDynamicColumn;
            _attendanceDynamicColumn = attendanceDynamicColumn;
            _hierarchicalQcDynamicColumn = hierarchicalQcDynamicColumn;
            _employeeSecondmentDynamicColumn = employeeSecondmentDynamicColumn;
        }
        /// <summary>
        /// 根据审批类别获取对应的在审批页面呈现的动态列
        /// </summary>
        /// <param name="proveCategory"></param>
        /// <param name="sourceIDs"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, Dictionary<string, object>>> GetTableListByProveCategory(string proveCategory, List<string> sourceIDs)
        {
            //根据审批类别获取来源表动态数据
            var fieldNames = await _dynamicColumnAttributeRepository.GetTableFieldNamesByTableTypeAsync("ProveCategory", proveCategory);
            if (fieldNames == null || fieldNames.Count <= 0)
            {
                _logger.Warn("无审批类别动态列配置");
                return null;
            }
            var sourceList = await SwitchSourceList(proveCategory, sourceIDs);
            if (sourceList == null || sourceList.Count <= 0)
            {
                _logger.Error($"审批类别动态列数据获取失败，{nameof(proveCategory)}={proveCategory}, {nameof(sourceIDs)}={string.Join(",", sourceIDs)}");
                throw new CustomException("发生内部异常，请联系管理员");
            }
            // 根据动态表格筛选出需要的字段
            Dictionary<string, object> dict;
            var dictList = new Dictionary<string, Dictionary<string, object>>();
            foreach (var sourceItem in sourceList)
            {
                dict = new Dictionary<string, object>();
                // 为了跟审批内容绑定
                if (!sourceItem.TryGetValue("SourceID", out var sourceIDVlaue))
                {
                    continue;
                }
                foreach (var fieldName in fieldNames)
                {
                    if (!sourceItem.TryGetValue(fieldName, out var value) || value == null)
                    {
                        continue;
                    }
                    dict.TryAdd(fieldName, value);                    
                }
                dictList.Add(sourceIDVlaue.ToString(), dict);
            }
            return dictList;
        }
        /// <summary>
        /// 根据审批类别获取来源表动态数据
        /// </summary>
        /// <param name="proveCategory"></param>
        /// <param name="sourceIDs"></param>
        /// <returns></returns>
        private async Task<List<Dictionary<string, object>>> SwitchSourceList(string proveCategory, List<string> sourceIDs)
        {
            return (proveCategory) switch
            {
                "AA-021" => await _scheduleRequestDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs),
                "AA-020" => await _adjustScheduleDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs),
                "AA-090" => await _attendanceDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs, proveCategory),
                "HR-040" => await _employeeDeptChangeDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs, proveCategory),
                "HR-041" => await _employeeSecondmentDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs, proveCategory),
                "HR-042" => await _employeeSecondmentDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs, proveCategory),
                "HR-043" => await _employeeSecondmentDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs, proveCategory),
                "MG-073" => await _hierarchicalQcDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs, proveCategory),
                "MG-077" => await _hierarchicalQcDynamicColumn.GetDynamicColumnListByRecordIDAsync(sourceIDs, proveCategory),
                _ => new List<Dictionary<string, object>>()
            };

        }
    }
}
