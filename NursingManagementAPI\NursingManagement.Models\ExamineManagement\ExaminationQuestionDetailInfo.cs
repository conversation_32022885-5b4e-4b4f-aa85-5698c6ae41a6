using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 考核题目明细表
    /// </summary>
    [Table("ExaminationQuestionDetail")]
    public class ExaminationQuestionDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 答案ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminationQuestionDetailID { get; set; }
        /// <summary>
        /// 题目ID
        /// </summary>
        [ForeignKey("ExaminationQuestion")]
        public int ExaminationQuestionID { get; set; }
        /// <summary>
        /// 题目明细内容
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string Content { get; set; }

        /// <summary>
        /// 是否是正确答案
        /// </summary>
        public bool AnswerFlag { get; set; }
        /// <summary>
        /// 呈现顺序
        /// </summary>
        public int Sort { get; set; }

        // 导航属性
        public virtual ExaminationQuestionInfo ExaminationQuestion { get; set; }
    }
}
