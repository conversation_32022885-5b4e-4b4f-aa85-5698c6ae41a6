﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DepartmentToApproveProcessRepository : IDepartmentToApproveProcessRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public DepartmentToApproveProcessRepository(
            NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 获取流程对应的适用科室ID列表
        /// </summary>
        /// <param name="onlyEnable">是否仅查询已启用的科室</param>
        /// <param name="processIDs">流程ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int[]>> GetDepartmentIDsByProcessIDs(bool onlyEnable, params string[] processIDs)
        {
            var data = await _dbContext.DepartmentToApproveProcessInfos.Where(m => processIDs.Contains(m.ApproveProcessID) && m.DeleteFlag != "*")
                .IfWhere(onlyEnable, m => !m.DisableDateTime.HasValue)
                .GroupBy(m => m.ApproveProcessID).ToDictionaryAsync(m => m.Key, n => n.Select(o => o.DepartmentID).ToArray());
            return data;
        }
        /// <summary>
        /// 更新启用时间
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <param name="employeeID">工号</param>
        /// <param name="now">启用时间</param>
        /// <returns></returns>
        public async Task<bool> UpdateEnableDateTime(string approveProcessID, string employeeID, DateTime now)
        {
            return await _dbContext.DepartmentToApproveProcessInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*")
                .ExecuteUpdateAsync(m => m.SetProperty(n => n.EnableDateTime, now).SetProperty(n => n.ModifyEmployeeID, employeeID)
                .SetProperty(n => n.ModifyDateTime, now)) > 0;
        }
        /// <summary>
        /// 更新停用时间
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <param name="employeeID">工号</param>
        /// <param name="now">停用时间</param>
        /// <returns></returns>
        public async Task<bool> UpdateDisableDateTime(string approveProcessID, string employeeID, DateTime now)
        {
            return await _dbContext.DepartmentToApproveProcessInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*")
                .ExecuteUpdateAsync(m => m.SetProperty(n => n.DisableDateTime, now).SetProperty(n => n.ModifyEmployeeID, employeeID)
                .SetProperty(n => n.ModifyDateTime, now)) > 0;
        }
        /// <summary>
        /// 获取审批流程对应的科室数据集合
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <returns></returns>
        public async Task<List<DepartmentToApproveProcessInfo>> GetInfosByApproveProcessID(string approveProcessID)
        {
            return await _dbContext.DepartmentToApproveProcessInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
