﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IMonthlyPlanMaintainRepository
    {

        /// <summary>
        /// 获取月度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表iD</param>
        /// <param name="quarter">月度</param>
        /// <returns></returns>
        Task<string> GetMonthlyPlanMainID(string annualPlanMainID, int quarter);

        /// <summary>
        /// 获取月度计划
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        Task<MonthlyPlanMainInfo> GetMonthlyPlanMain(string monthlyPlanMainID);

        /// <summary>
        /// 获取月度计划状态
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        Task<int> GetMonthlyPlanStatus(string monthlyPlanMainID);

        /// <summary>
        /// 获取月度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <returns></returns>
        Task<Dictionary<string, Dictionary<int, string>>> GetMonthToID(string[] annualPlanMainIDs);
        /// <summary>
        /// 获取月度计划
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        Task<TieredPlanWorksByType[]> GetMonthlyWorks(string monthlyPlanMainID);

        /// <summary>
        /// 获取工作ID与工作内容
        /// </summary>
        /// <param name="workIDs">工作ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs);

        /// <summary>
        /// 获取选择工作视图
        /// </summary>
        /// <param name="apMainIDs">年度计划主表ID</param>
        /// <returns></returns>
        Task<List<TieredPlanWorksByPlanThenType>> GetWorkViews(string[] apMainIDs, int? includeApInterventionID = null, int[] excludeApInterventionIDs = null, bool onlyPriority = false, bool includeTempWork = true);
        /// <summary>
        /// 获取月度计划工作
        /// </summary>
        /// <param name="workIDs">工作ID集合</param>
        /// <returns></returns>
        Task<List<MonthlyPlanDetailInfo>> GetMonthlyPlanWorks(string[] workIDs);
        /// <summary>
        /// 获取月度计划工作
        /// </summary>
        /// <param name="quarterWorkID">主键</param>
        /// <returns></returns>
        Task<MonthlyPlanDetailInfo> GetMonthlyWork(string quarterWorkID);

        /// <summary>
        /// 获取某月度计划工作关联字典ID集合
        /// </summary>
        /// <param name="qpMainID">月度计划主表ID</param>
        /// <param name="quarter">月度</param>
        /// <returns></returns>
        Task<int[]> GetMpWorkInterventionIDs(string qpMainID, int quarter);
        /// <summary>
        /// 获取月度计划工作关联字典ID集合
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="departmentIDs">病区集合</param>
        /// <returns></returns>
        Task<List<APMainView>> GetMonthlyPlanMainViewsByYearAndDepartmentIDs(int year, IEnumerable<int> departmentIDs);
    }
}
