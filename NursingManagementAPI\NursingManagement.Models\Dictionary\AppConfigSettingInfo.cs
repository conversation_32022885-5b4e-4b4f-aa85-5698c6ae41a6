﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("AppConfigSetting")]
    public class AppConfigSettingInfo : ModifyInfo
    {
        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SettingType { get; set; }

        /// <summary>
        /// 配置码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingCode { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        [Column(TypeName = "varchar(300)")]
        public string SettingValue { get; set; }

        /// <summary>
        /// API属于那个系统。Medical、External
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SystemType { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        [Column(TypeName = "nvarchar(400)")]
        public string Description { get; set; }
    }
}
