﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;

namespace NursingManagement.API
{
    /// <summary>
    /// 消息确认控制器
    /// </summary>
    [Route("api/MessageConfirmation")]
    [Produces("application/json")]
    [EnableCors("any")]
    public class MessageConfirmationController : ControllerBase
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IMessageConfirmationService _messageConfirmationService;
        private readonly ISessionService _sessionService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="messageConfirmationService"></param>
        /// <param name="sessionService"></param>
        public MessageConfirmationController(IMessageConfirmationService messageConfirmationService, ISessionService sessionService)
        {
            _messageConfirmationService = messageConfirmationService;
            _sessionService = sessionService;
        }

        /// <summary>
        /// 获取消息确认列表
        /// </summary>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <returns>消息确认列表</returns>
        [HttpGet("GetMessageConfirmationList")]
        public async Task<IActionResult> GetMessageConfirmationList(string messageRecordId)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _messageConfirmationService.GetRecordsByMessageRecordID(messageRecordId);
            result.Sucess();
            return result.ToJson();
        }

        /// <summary>
        /// 确认消息
        /// </summary>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <returns>确认结果</returns>
        [HttpPost("SaveMessageConfirmation")]
        public async Task<IActionResult> SaveMessageConfirmation(string messageRecordId)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageConfirmationService.SaveMessageConfirmation(messageRecordId,session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 检查消息是否已确认
        /// </summary>
        /// <param name="messageRecordId">消息ID</param>
        /// <returns>确认状态</returns>
        [HttpGet("IsConfirmed")]
        public async Task<IActionResult> IsConfirmed(string messageRecordId)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageConfirmationService.IsConfirmedAsync(session.EmployeeID,messageRecordId);
            return result.ToJson();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetPendingConfirmationMessages")]
        public async Task<IActionResult> GetPendingConfirmationMessages()
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _messageConfirmationService.GetPendingConfirmationMessages(session.EmployeeID);
            return result.ToJson();
        }
        
    }
}
