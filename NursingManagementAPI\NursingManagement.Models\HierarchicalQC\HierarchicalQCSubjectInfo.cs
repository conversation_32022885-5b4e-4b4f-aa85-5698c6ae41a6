using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 质控主题表
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCSubject")]
    public class HierarchicalQCSubjectInfo : MutiModifyInfo
    {
        /// <summary>
        /// 质控主题主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCSubjectID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 获取QualityControlFormContent明细数据
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string TemplateCode { get; set; }
        /// <summary>
        /// 质控表单序号
        /// </summary>
        public int HierarchicalQCFormID { get; set; }
        /// <summary>
        /// 表单名称
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string FormName { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 开始时间（主题使用）
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 结束时间（主题使用）
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 0：停用 1：启用
        /// </summary>
        public short StatusCode { get; set; }
        /// <summary>
        /// 质控报告文件
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ReportFileID { get; set; }
        /// <summary>
        /// 添加部门，护理管理组织架构的DepartmentID
        /// </summary>
        public int AddDepartmentID { get; set; }
        /// <summary>
        /// 质控类型
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string FormType { get; set; }
        /// <summary>
        /// 考核单条内容的最大分数(追踪考核筛选使用）
        /// </summary>
        public int? ScoreThreshold { get; set; }
        /// <summary>
        /// 开始实施日期时间
        /// </summary>
        public DateTime? ImplementationStartDate { get; set; }
        /// <summary>
        /// 达标分数
        /// </summary>
        public int? MinPassingScore { get; set; }
    }
}