﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Common
{
    public class Session
    {
        /// <summary>
        /// 授权口令
        /// </summary>
        public string Token { get; set; }
        /// <summary>
        /// HR系统员工编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 登录类型
        /// </summary>
        public string LoginType { get; set; }
        /// <summary>
        /// OA账号
        /// </summary>
        public string OAUserID { get; set; }
        /// <summary>
        /// HIS账号
        /// </summary>
        public string HisUserID { get; set; }
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 用户照片
        /// </summary>
        public string PhotoUrl { get; set; }
        /// <summary>
        /// 登录时间
        /// </summary>
        public DateTime LoginTime { get; set; }
        /// <summary>
        /// HR部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// HIS病区序号
        /// </summary>
        public int HisStationID { get; set; }
        /// <summary>
        /// HIS科室序号
        /// </summary>
        public int HisDepartmentID { get; set; }
        /// <summary>
        /// 微信扫码登录的唯一ID
        /// </summary>
        public string WechatWebOpenID { get; set; }
        /// <summary>
        /// 微信笑程序唯一ID
        /// </summary>
        public string WechatMiniProgramOpenID { get; set; }
        /// <summary>
        /// 微信唯一ID
        /// </summary>
        public string WechatUnionID { get; set; }
        /// <summary>
        /// 人员角色序号
        /// </summary>
        public List<int> Roles { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 命令码
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 客户端类型 1：PC，2：H5，3：小程序，4：微信H5
        /// </summary>
        public int ClientType { get; set; }
    }
}