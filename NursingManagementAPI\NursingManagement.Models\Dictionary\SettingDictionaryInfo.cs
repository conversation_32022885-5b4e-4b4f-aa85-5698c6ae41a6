﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 护理管理参数配置字典(存储非国标类字典配置及开关)
    /// </summary>
    [Table("SettingDictionary")]
    public class SettingDictionaryInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键，自增
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int SettingDictionaryID { get; set; }

        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 数据类别（1、字典类；2、开关类）
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string DataType { get; set; }

        /// <summary>
        /// 类别(一阶)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingType { get; set; }

        /// <summary>
        /// 类别码(二阶)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingTypeCode { get; set; }

        /// <summary>
        /// 类别值(三阶)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingTypeValue { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingValue { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        [Column(TypeName = "varchar(300)")]
        public string Description { get; set; }

        /// <summary>
        /// 对应国标字典AdministrationDictionary
        /// </summary>
        public int? AdministrationDictionaryID { get; set; }

        /// <summary>
        /// 是否允许前端程序修改
        /// </summary>
        public bool ModifyFlag { get; set; }
        /// <summary>
        /// 排序Sort
        /// </summary>
        public int Sort { get; set; }
    }
}
