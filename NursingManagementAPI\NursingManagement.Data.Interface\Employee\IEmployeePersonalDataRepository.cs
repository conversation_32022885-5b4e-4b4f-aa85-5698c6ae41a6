﻿using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeePersonalDataRepository : ICacheRepository
    {
        /// <summary>
        /// 获取指定字段信息
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="employeeID"></param>
        /// <param name="predicate"></param>
        /// <returns></returns>
        Task<T> GetFieldValueByEmployeeIDAsync<T>(string employeeID, [DisallowNull] Expression<Func<EmployeePersonalDataInfo, T>> predicate);

        /// <summary>
        /// 根据employeID获取数据
        /// </summary>
        /// <param name="employeID"></param>
        /// <returns></returns>
        Task<EmployeePersonalDataInfo> GetDataByEmployeeID(string employeID);

        /// <summary>
        /// 根据employeID集合获取数据
        /// </summary>
        /// <param name="employeIDs"></param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetDataByEmployeeIDs(IEnumerable<string> employeIDs);

        /// <summary>
        /// 根据医院序号获取数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<EmployeePersonalDataInfo>> GetAllDataByHospital(string hospitalID);

        /// <summary>
        /// 获取所有人员（ID 名称）
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeePersonalDataListView>> GetIDAndNameData();

        /// <summary>
        /// 根据EmployeeID获取员工基本信息List
        /// </summary>
        /// <param name="employeIDs"></param>
        /// <returns></returns>
        Task<List<EmployeePersonalDataListView>> GetListByEmployeeIDs(List<string> employeIDs);

        /// <summary>
        /// 获取EmployeePersonalDataListView非删除数据
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeePersonalDataListView>> GetEmployeePersonalDataView();

        /// <summary>
        /// 根据名字集合获取数据
        /// </summary>
        /// <param name="employeeNameList"></param>
        /// <returns></returns>
        Task<List<EmployeePersonalDataListView>> GetListByNameList(List<string> employeeNameList);

        /// <summary>
        /// 模糊查询人员信息
        /// </summary>
        /// <param name="employeeName">姓名</param>
        Task<Dictionary<string, string>> GetEmployeeIDByName(string employeeName);

        /// <summary>
        /// 根据人员ID获取员工姓名
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        Task<string> GetEmployeeNameByID(string employeeID);
        /// <summary>
        /// 根据ID 集合获取前端人员组件 optons
        /// </summary>
        /// <param name="employeIDs"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetEmployOptionViewByEmployeeIDs(List<string> employeIDs);
        /// <summary>
        /// 获取所有人员（ID 名称）
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetAllEmployees();

        /// <summary>
        /// 获取员工ID和姓名
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, (string name, string pinyin)>> GetEmployeeIDAndNames();
    }
}
