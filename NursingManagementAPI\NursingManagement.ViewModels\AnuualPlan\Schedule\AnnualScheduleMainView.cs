﻿using NursingManagement.Models;
using static NursingManagement.Models.AnnualPlanEnums;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 年度计划排程
    /// </summary>
    public class AnnualScheduleMainView
    {
        /// <summary>
        /// 排程主键ID
        /// </summary>
        public string AnnualScheduleMainID { get; set; }
        /// <summary>
        /// 措施ID
        /// </summary>
        public int? InterventionID { get; set; }
        /// <summary>
        /// 任务名称
        /// </summary>
        public string InterventionName { get; set; }
        /// <summary>
        /// 完成要求
        /// </summary>
        public string Requirement { get; set; }
        /// <summary>
        /// 计划执行时间
        /// </summary>
        public DateTime ScheduleDateTime { get; set; }
        /// <summary>
        /// 排程实际执行时间
        /// </summary>
        public DateTime? PerformDateTime { get; set; }
        /// <summary>
        /// 执行内容
        /// </summary>
        public string PerformComment { get; set; }
        /// <summary>
        /// 排程状态    
        /// </summary>
        public APScheduleStatus Status { get; set; }
        /// <summary>
        /// 延迟执行的原因类型
        /// </summary>
        public string Reason { get; set; }
        /// <summary>
        /// 部门ID，仅作连接查询使用
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 延迟执行备注
        /// </summary>
        public string DelayContent { get; set; }
        /// <summary>
        /// 年度计划内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 导航属性
        /// </summary>
        public ICollection<MonthlyWorkToTaskInfo> MonthlyWorkToTasks { get; set; }
    }
}
