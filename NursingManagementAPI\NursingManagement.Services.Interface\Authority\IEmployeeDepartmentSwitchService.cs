﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IEmployeeDepartmentSwitchService
    {
        /// <summary>
        /// 获取人员多部门列表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentSwitchView>> GetEmployeeDepartmentSwitchListAsync(int? departmentID);
        /// <summary>
        /// 根据工号删除人员多部门配置
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteEmployeeDepartmentSwitchAsync(string employeeID, string modifyEmployeeID);
        /// <summary>
        /// 保存人员多部门配置
        /// </summary>
        /// <param name="employeeDepartmentSwitchParamView"></param>
        /// <param name="modifyEmployeeID">修改人</param>
        /// <returns></returns>
        Task<bool> SaveEmployeeDepartmentSwitchAsync(EmployeeDepartmentSwitchParamView employeeDepartmentSwitchParamView, string modifyEmployeeID);
        /// <summary>
        /// 获取用户拥有权限部门的级联Option
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<CascaderView<int>>> GetEmployeeSwitchCascader(string employeeID);
    }
}
