﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class MessageManagementService : IMessageManagementService
    {
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IMessageRecordRepository _messageRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly IUserLoginRepository _userloginRepository;
        private readonly IMessageService _messageService;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IRouterListRepository _routerRepository;
        private readonly IMessageConfirmationRepository _messageConfirmationRepository;
        private readonly IMessageToDepartmentRepository _messageToDepartmentRepository;

        public MessageManagementService(
            IUnitOfWork unitOfWork,
            SessionCommonServer sessionCommonServer,
            IMessageRecordRepository messageRecordRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            ISettingDictionaryService settingDictionaryService,
            IUserLoginRepository userloginRepository,
            IMessageService messageService,
            ISettingDictionaryRepository settingDictionaryRepository,
            IRouterListRepository routerRepository,
            IMessageConfirmationRepository messageConfirmationRepository,
            IMessageToDepartmentRepository messageToDepartmentRepository
        )
        {
            _unitOfWork = unitOfWork;
            _sessionCommonServer = sessionCommonServer;
            _messageRecordRepository = messageRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _settingDictionaryService = settingDictionaryService;
            _userloginRepository = userloginRepository;
            _messageService = messageService;
            _settingDictionaryRepository = settingDictionaryRepository;
            _routerRepository = routerRepository;
            _messageConfirmationRepository = messageConfirmationRepository;
            _messageToDepartmentRepository = messageToDepartmentRepository;
        }
        /// <summary>
        /// 0
        /// </summary>
        private const int ZERO = 0;
        /// <summary>
        /// 移动端
        /// </summary>
        private const int MOBILE_CLIENTTYPE = 2;
        /// <summary>
        /// 网页端
        /// </summary>
        private const int WEB_CLIENTTYPE = 1;
        /// <summary>
        /// 默认置顶天数
        /// </summary>
        private const int DEFAULT_TOP_DAYS = 1;
        /// <summary>
        /// 未发布状态
        /// </summary>
        private const string STATUS_UN_PUBLISH = "0";
        /// <summary>
        /// 已发布状态
        /// </summary>
        private const string STATUS_PUBLISH = "1";
        /// <summary>
        /// 系统消息
        /// </summary>
        private const string MESSAGE_TYPE_SYSTEM = "99";
        /// <summary>
        /// 全院部门序号
        /// </summary>
        private const int DEPARTMENT_ID_999999 = 999999;

        public async Task<List<MessageRecordView>> GetMessageListAsync(string messageType, List<int> departmentIDs)
        {
            var messageRecordList = await _messageRecordRepository.GetListAsNoTrack(messageType);
            if (messageRecordList.Count <= 0)
            {
                return [];
            }
            var employeeIDs = messageRecordList.Select(m => m.AddEmployeeID).Union(messageRecordList.Select(m => m.ModifyEmployeeID)).ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var messageSettingDicts = await _settingDictionaryService.GetSettingDictionaryByCodeValue("MessageManagement", ["MessageStatus", "MessageType"]);
            // 如果部门里不包含全院，才依据部门集合过滤数据
            if (!departmentIDs.Contains(DEPARTMENT_ID_999999))
            {
                var messageRecordIDs = await _messageToDepartmentRepository.GetMessageRecordIDsByDepartmentIDs(departmentIDs);
                messageRecordList = messageRecordList.Where(m => messageRecordIDs.Contains(m.MessageRecordID)).ToList();
            }
            return messageRecordList.OrderByDescending(m => m.AddDateTime)
                        .ThenByDescending(m => m.PublishTime ?? DateTime.MinValue)
                        .Select(m => MapToMessageRecordView(m, employees, messageSettingDicts)).ToList();
        }

        /// <summary>
        /// 将 MessageRecord 实体映射为 MessageRecordView 视图模型
        /// </summary>
        /// <param name="messageRecordInfo">消息记录实体</param>
        /// <param name="employees">人员信息</param>
        /// <param name="messageSettingDicts">字典配置</param>
        /// <returns>消息记录视图模型</returns>
        private MessageRecordView MapToMessageRecordView(MessageRecordInfo messageRecordInfo, Dictionary<string, string> employees, Dictionary<string, List<KeyValuePair<string, string>>> messageSettingDicts)
        {
            var view = new MessageRecordView
            {
                MessageRecordID = messageRecordInfo.MessageRecordID,
                MessageTitle = messageRecordInfo.MessageTitle,
                MessageType = messageRecordInfo.MessageType,
                TopDays = messageRecordInfo.TopDays == ZERO ? default(int?) : messageRecordInfo.TopDays,
                MessageStatus = messageRecordInfo.MessageStatus,
                PublishTime = messageRecordInfo.PublishTime,
                AddEmployeeID = messageRecordInfo.AddEmployeeID,
                AddDateTime = messageRecordInfo.AddDateTime,
                ModifyEmployeeID = messageRecordInfo.ModifyEmployeeID,
                ModifyDateTime = messageRecordInfo.ModifyDateTime,
                IsTop = messageRecordInfo.TopDays != ZERO,
                StatusDescription = GetSettingDescription(nameof(messageRecordInfo.MessageStatus), messageRecordInfo.MessageStatus, messageSettingDicts),
                MessageTypeDescription = GetSettingDescription(nameof(messageRecordInfo.MessageType), messageRecordInfo.MessageType, messageSettingDicts),
                AddEmployeeName = employees.TryGetValue(messageRecordInfo.AddEmployeeID, out var addEmployeeName) ? addEmployeeName : string.Empty,
                ModifyEmployeeName = employees.TryGetValue(messageRecordInfo.ModifyEmployeeID, out var modifyEmployeeName) ? modifyEmployeeName : string.Empty
            };

            return view;
        }
        /// <summary>
        /// 获取配置字典的描述信息
        /// </summary>
        /// <param name="settingTypeCode">配置项目类别码</param>
        /// <param name="settingTypeValue">具体配置项</param>
        /// <param name="messageSettingDicts">配置项集合</param>
        /// <returns></returns>
        private string GetSettingDescription(string settingTypeCode, string settingTypeValue, Dictionary<string, List<KeyValuePair<string, string>>> messageSettingDicts)
        {
            KeyValuePair<string, string> selectedKeyValue;
            if (messageSettingDicts.TryGetValue(settingTypeCode, out var settingList))
            {
                selectedKeyValue = settingList.Where(m => m.Key == settingTypeValue).FirstOrDefault();
                if (selectedKeyValue.Value != null)
                {
                    return selectedKeyValue.Value;
                }
            }
            return "";
        }
        /// <summary>
        /// 获取消息详情
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <returns>消息详情</returns>
        public async Task<string> GetMessageDetailAsync(string messageRecordID)
        {
            var messageContent = await _messageRecordRepository.GetContentByID(messageRecordID);
            return messageContent;
        }

        /// <summary>
        /// 添加新消息
        /// </summary>
        /// <param name="messageView">消息信息</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns>添加结果</returns>
        public async Task<bool> AddMessageAsync(MessageRecordView messageView, string employeeID)
        {
            var message = new MessageRecordInfo
            {
                MessageTitle = messageView.MessageTitle,
                MessageType = messageView.MessageType,
                MessageContent = messageView.MessageContent,
                TopDays = messageView.IsTop ? messageView.TopDays ?? DEFAULT_TOP_DAYS : ZERO,
                MessageStatus = messageView.MessageStatus ?? STATUS_UN_PUBLISH,
                PublishTime = messageView.MessageStatus == STATUS_PUBLISH ? DateTime.Now : null,
                AddEmployeeID = employeeID,
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                DeleteFlag = ""
            };
            message.MessageRecordID = message.GetId();
            await _unitOfWork.GetRepository<MessageRecordInfo>().InsertAsync(message);
            // 插入发布的部门
            await InsertMessageToDepartment(messageView.DepartmentIDs, message.MessageRecordID, employeeID);
            var needSendMessage = false;
            if (message.MessageType == MESSAGE_TYPE_SYSTEM && message.MessageStatus == STATUS_PUBLISH)
            {
                needSendMessage = true;
                // 新增现有用户系统通知确认记录
                await InsertMessageConfirmation(message.MessageRecordID, employeeID);
            }
            var result = await _unitOfWork.SaveChangesAsync() > 0;
            if (result && needSendMessage)
            {
                await SendSystemNoticeAfterUpdate(message.MessageRecordID, message.MessageContent);
            }
            return result;
        }

        /// <summary>
        /// 更新消息
        /// </summary>
        /// <param name="messageView">更新的消息信息</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateMessageAsync(MessageRecordView messageView, string employeeID)
        {
            var message = await _messageRecordRepository.GetRecordByIdAsync(messageView.MessageRecordID);
            if (message == null || message.DeleteFlag == "*")
            {
                return false;
            }
            if (message.MessageType == MESSAGE_TYPE_SYSTEM)
            {
                // 取消发布时，删除确认记录
                if (messageView.MessageStatus == STATUS_UN_PUBLISH && message.MessageStatus == STATUS_PUBLISH)
                {
                    var confirmRecords = await _messageConfirmationRepository.GetByMessageRecordIdAsync(messageView.MessageRecordID);
                    confirmRecords.ForEach(m => m.Modify(employeeID).Delete(employeeID));
                }
                // 发布时新增现有用户系统通知确认记录
                if (messageView.MessageStatus == STATUS_PUBLISH && message.MessageStatus == STATUS_UN_PUBLISH)
                {
                    await InsertMessageConfirmation(message.MessageRecordID, employeeID);
                }
            }
            // 更新消息属性
            message.MessageTitle = messageView.MessageTitle;
            message.MessageContent = messageView.MessageContent;
            message.TopDays = messageView.IsTop ? messageView.TopDays ?? DEFAULT_TOP_DAYS : ZERO;
            message.Modify(employeeID);
            // 更新消息状态
            message.MessageStatus = messageView.MessageStatus ?? STATUS_UN_PUBLISH;
            message.PublishTime = messageView.MessageStatus == STATUS_PUBLISH ? DateTime.Now : null;
            // 获取消息发布的部门消息
            var messageToDepartmentList = await _messageToDepartmentRepository.GetMessageToDepartmentByMessageID(message.MessageRecordID);
            var oldDepartmentIDs = messageToDepartmentList.Select(m => m.DepartmentID).ToList();
            if (messageView.DepartmentIDs?.Count <= 0)
            {
                messageView.DepartmentIDs = [DEPARTMENT_ID_999999];
            }
            // 如果消息发布的部门发生改变，删除旧数据新增新数据
            if (messageView.DepartmentIDs.Except(oldDepartmentIDs).Any() || oldDepartmentIDs.Except(messageView.DepartmentIDs).Any())
            {
                messageToDepartmentList.ForEach(m => m.Modify(employeeID).Delete(employeeID));
                await InsertMessageToDepartment(messageView.DepartmentIDs, message.MessageRecordID, employeeID);
            }
            var result = await _unitOfWork.SaveChangesAsync() > 0;
            if (result && message.MessageType == MESSAGE_TYPE_SYSTEM && message.MessageStatus == STATUS_PUBLISH)
            {
                await SendSystemNoticeAfterUpdate(message.MessageRecordID, message.MessageContent);
            }
            return result;
        }
        /// <summary>
        /// 插入消息发布的部门
        /// </summary>
        /// <param name="departmentIDs"></param>
        /// <param name="messageRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task InsertMessageToDepartment(List<int> departmentIDs, string messageRecordID, string employeeID)
        {
            if (departmentIDs?.Count <= 0)
            {
                return;
            }
            var session = _sessionCommonServer.GetSessionByCache();
            foreach (var departmentID in departmentIDs)
            {
                var messageToDepartment = new MessageToDepartmentInfo()
                {
                    MessageRecordID = messageRecordID,
                    DepartmentID = departmentID,
                    HospitalID = session.HospitalID
                };
                messageToDepartment.Add(employeeID).Modify(employeeID);
                await _unitOfWork.GetRepository<MessageToDepartmentInfo>().InsertAsync(messageToDepartment);
            }
        }

        /// <summary>
        /// 新增消息确认记录
        /// </summary>
        /// <param name="messageRecordID">消息记录ID</param>
        /// <param name="employeeID">操作人</param>
        /// <returns></returns>
        private async Task InsertMessageConfirmation(string messageRecordID, string employeeID)
        {
            var userLogins = await _userloginRepository.GetAll<UserLoginInfo>();
            foreach (var userLogin in userLogins)
            {
                var messageConfirmationInfo = new MessageConfirmationInfo
                {
                    MessageRecordID = messageRecordID,
                    ConfirmationStatus = false,
                    ConfirmEmployeeID = userLogin.EmployeeID,
                };
                messageConfirmationInfo.MessageConfirmationID = messageConfirmationInfo.GetId();
                messageConfirmationInfo.Add(employeeID).Modify(employeeID);
                await _unitOfWork.GetRepository<MessageConfirmationInfo>().InsertAsync(messageConfirmationInfo);
            }
        }
        /// <summary>
        /// 删除消息
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteMessageAsync(string messageRecordID, string employeeID)
        {
            var message = await _messageRecordRepository.GetRecordByIdAsync(messageRecordID);
            if (message == null)
            {
                return false;
            }
            // 删除消息
            message.DeleteFlag = "*";
            message.ModifyEmployeeID = employeeID;
            message.ModifyDateTime = DateTime.Now;
            // 删除确认记录
            var confirmRecords = await _messageConfirmationRepository.GetByMessageRecordIdAsync(messageRecordID);
            confirmRecords.ForEach(m => m.Modify(employeeID).Delete(employeeID));
            // 删除消息发布的部门记录
            var messageToDepartmentList = await _messageToDepartmentRepository.GetMessageToDepartmentByMessageID(message.MessageRecordID);
            messageToDepartmentList.ForEach(m => m.Modify(employeeID).Delete(employeeID));
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        public async Task<bool> PublishMessageAsync(string messageRecordID, string messageStatus, string employeeID)
        {
            var message = await _messageRecordRepository.GetRecordByIdAsync(messageRecordID);
            // 设置为已发布状态
            message.MessageStatus = messageStatus;
            message.PublishTime = messageStatus == STATUS_PUBLISH ? DateTime.Now : null;
            message.Modify(employeeID);
            var needSendMessage = false;
            if (message.MessageType == MESSAGE_TYPE_SYSTEM && message.MessageStatus == STATUS_PUBLISH)
            {
                needSendMessage = true;
                // 新增现有用户系统通知确认记录
                await InsertMessageConfirmation(message.MessageRecordID, employeeID);
            }
            var result = await _unitOfWork.SaveChangesAsync() > 0;
            if (result && needSendMessage)
            {
                await SendSystemNoticeAfterUpdate(message.MessageRecordID, message.MessageContent);
            }
            return result;
        }

        public async Task<bool> SendSystemPreUpdateMessage(string messageRecordID, string customMessage)
        {
            if (string.IsNullOrWhiteSpace(messageRecordID))
            {
                _logger.Warn("消息ID为空，无法发送系统通知消息");
                return false;
            }
            return await _messageService.SendMessage(new MessageView
            {
                MessageTools = [MessageTool.MQ],
                ClientType = 1,
                MessageCondition = new MessageConditionView
                {
                    ClientType = 1,
                    MQExchangeName = "MQNotification",
                    MQRoutingKey = "SystemNotice",
                    Type = MessageType.Notification,
                    Message = customMessage ?? "系统预计今天更新，请注意提前保存！"
                },
            });
        }
        /// <summary>
        /// 发送系统通知消息|更新后
        /// </summary>
        /// <param name="messageRecordID"></param>
        /// <param name="messageTitle"></param>
        /// <returns></returns>
        private async Task<bool> SendSystemNoticeAfterUpdate(string messageRecordID, string messageTitle)
        {
            var (mobileSkipPath, webSkipPath) = await GetRouterUrl();
            var urlParams = new Dictionary<string, string> { { "messageRecordID", messageRecordID } };
            // 发送广播通知
            await _messageService.SendMessage(new MessageView
            {
                MessageTools = [MessageTool.MQ],
                ClientType = WEB_CLIENTTYPE,
                MessageCondition = new MessageConditionView
                {
                    ClientType = WEB_CLIENTTYPE,
                    MQExchangeName = "MQNotification",
                    MQRoutingKey = "SystemNotice",
                    Type = MessageType.Notification,
                    Message = "系统更新完成，请重新登录！",
                    Url = webSkipPath,
                    UrlParams = urlParams
                },
            });
            var employeeIDs = await _userloginRepository.GetEmployeeIDsByWechatUnionIDIsNotNull();
            Func<string, Task> parallelTask = async (employeeID) =>
            {
                await _messageService.SendMessage(new MessageView
                {
                    MessageTools = [MessageTool.Wechat],
                    ClientType = MOBILE_CLIENTTYPE,
                    EmployeeID = employeeID,
                    MessageCondition = new MessageConditionView
                    {
                        ClientType = MOBILE_CLIENTTYPE,
                        Type = MessageType.Notification,
                        Message = "护理管理系统更新完成，点击查看更新详情！",
                        Url = mobileSkipPath + $"?messageRecordID={messageRecordID}&messageTitle={messageTitle}",
                    },
                });
            };
            // 并发发送微信消息通知
            await RunWithLimitedConcurrency(employeeIDs, parallelTask, null);

            return true;
        }
        /// <summary>
        /// 获取路由地址
        /// </summary>
        /// <returns></returns>
        private async Task<(string, string)> GetRouterUrl()
        {
            var routerListIDsStr = await _settingDictionaryRepository.GetSettingValue(new SettingDictionaryParams
            {
                SettingType = "Common",
                SettingTypeCode = "MessageSkipPathToRoute",
                SettingTypeValue = "SystemNotice"
            });
            // 获取跳转路径
            var mobileSkipPath = "";
            var webSkipPath = "";
            if (!string.IsNullOrEmpty(routerListIDsStr) && int.TryParse(routerListIDsStr, out int routerListID))
            {
                var routerList = await _routerRepository.GetInfosByRouterListID(routerListID);
                mobileSkipPath = routerList.Where(m => m.ClientType == MOBILE_CLIENTTYPE).Select(m => m.Path).FirstOrDefault();
                webSkipPath = routerList.Where(m => m.ClientType == WEB_CLIENTTYPE).Select(m => m.Path).FirstOrDefault();
            }
            return (mobileSkipPath, webSkipPath);
        }

        /// <summary>
        /// 限制并发数量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="items"></param>
        /// <param name="taskFunc"></param>
        /// <param name="maxConcurrency"></param>
        /// <returns></returns>
        private async Task RunWithLimitedConcurrency<T>(IEnumerable<T> items, Func<T, Task> taskFunc, int? maxConcurrency, bool waitResult = false)
        {
            var semaphore = new SemaphoreSlim(maxConcurrency ?? 5);
            var tasks = items.Select(async item =>
            {
                await semaphore.WaitAsync();
                try
                {
                    await taskFunc(item);
                }
                catch (Exception ex)
                {
                    _logger.Error($"RunWithLimitedConcurrency报错， 异常信息：{ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }).ToList();
            if (waitResult)
            {
                // 等待所有任务完成
                await Task.WhenAll(tasks);
                return;
            }
            await Task.WhenAny(tasks);
        }
        public async Task<MessageRecordView> GetLastSystemUpdateRecord()
        {
            return await _messageRecordRepository.GetLastSystemUpdateRecord();
        }
    }
}