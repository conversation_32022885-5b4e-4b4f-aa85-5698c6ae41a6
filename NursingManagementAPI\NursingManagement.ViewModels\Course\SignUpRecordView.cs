﻿
namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 培训报名视图模型
    /// </summary>
    public class SignUpRecordView
    {
        /// <summary>
        /// 报名记录ID
        /// </summary>
        public string SignUpRecordID { get; set; }
        /// <summary>
        /// 来源ID
        /// </summary>
        public string SourceID { get; set; }
        /// <summary>
        /// 来源类别
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 来源类别名称
        /// </summary>
        public string SourceTypeDescription { get; set; }
        /// <summary>
        /// 状态(0:不同意，1：同意)
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription {  get; set; }
        /// <summary>
        /// 报名类型(SelfSignUp：自主报名；TrainingChoose:新增培训选中)
        /// </summary>
        public string SignUpType { get; set; }
        /// <summary>
        /// 自主报名,新增培训选中
        /// </summary>
        public string SignUpTypeDescription { get; set; }
        /// <summary>
        /// 人员工号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 人员姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 新增人员姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }        
    }
}
