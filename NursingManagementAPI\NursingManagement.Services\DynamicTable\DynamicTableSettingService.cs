﻿using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 动态表格配置Service
    /// </summary>
    public class DynamicTableSettingService : IDynamicTableSettingService
    {
        private readonly IDynamicTableSettingRepository _dynamicTableSettingRepository;
        private readonly IDynamicTableListRepository _dynamicTableListRepository;
        private readonly IDynamicColumnAttributeRepository _dynamicColumnAttributeRepository;
        public DynamicTableSettingService(
              IDynamicTableSettingRepository dynamicTableSettingRepository
            , IDynamicTableListRepository dynamicTableListRepository
            , IDynamicColumnAttributeRepository dynamicColumnAttributeRepository
            )
        {
            _dynamicTableSettingRepository = dynamicTableSettingRepository;
            _dynamicTableListRepository = dynamicTableListRepository;
            _dynamicColumnAttributeRepository = dynamicColumnAttributeRepository;
        }
        /// <summary>
        ///  获取动态表格表头配置
        /// </summary>
        /// <param name="queryView"></param>
        /// <returns></returns>
        public async Task<List<DynamicTableHeaderView>> GetDynamicTableHeader(DynamicTableHeaderQueryView queryView)
        {
            var tableHeaderView = new List<DynamicTableHeaderView>();
            var dynamicTableID = await _dynamicTableListRepository.GetIDByType(queryView.TableType, queryView.TableSubType);
            if (dynamicTableID == default)
            {
                //公共模板
                dynamicTableID = 999999;
            }
            var dynamicTableColumList = await _dynamicTableSettingRepository.GetViewListByID(dynamicTableID);
            var columnAttributeList = await _dynamicColumnAttributeRepository.GetListByID(dynamicTableID);
            var oneLevelHeaders = dynamicTableColumList.Where(m => m.Level == 1).ToList();
            if (dynamicTableColumList.Count <=0 ||  oneLevelHeaders.Count == 0)
            {
                return tableHeaderView;
            }
            foreach (var oneLevelHeader in oneLevelHeaders)
            {
                var oneLevelColumnAttributeList = columnAttributeList.Where(m => m.ColumnID == oneLevelHeader.ColumnID).ToList();
                if (oneLevelColumnAttributeList.Count <= 0)
                {
                    continue;
                }
                var header = CreateTableHeaderView(oneLevelHeader, oneLevelColumnAttributeList);
                var childrenColumnAttribute = columnAttributeList.Where(m => m.AttributeCode == "ChildrenLevel" && m.AttributeValue == oneLevelHeader.ColumnID.ToString()).ToList();
                if (childrenColumnAttribute.Count <= 0)
                {
                    tableHeaderView.Add(header);
                    continue;
                }
                var twoLevelHeaders = dynamicTableColumList.Where(m => childrenColumnAttribute.Any(n => n.ColumnID == m.ColumnID) && m.Level == 2).ToList();
                if (twoLevelHeaders.Count == 0)
                {
                    tableHeaderView.Add(header);
                    continue;
                }
                foreach (var twoLevelHeader in twoLevelHeaders)
                {
                    var twoLevelColumnAttributeList = columnAttributeList.Where(m => m.ColumnID == twoLevelHeader.ColumnID).ToList();
                    if (twoLevelColumnAttributeList.Count <= 0)
                    {
                        continue;
                    }
                    header.Children.Add(CreateTableHeaderView(twoLevelHeader, twoLevelColumnAttributeList));
                };
                tableHeaderView.Add(header);
            }
            return tableHeaderView.OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        /// 构造动态表头View
        /// </summary>
        /// <param name="dynamicTableColum"></param>
        /// <param name="columnAttributeList"></param>
        /// <returns></returns>
        private static DynamicTableHeaderView CreateTableHeaderView(DynamicTableSettingView dynamicTableColum, List<DynamicColumnAttributeInfo> columnAttributeList)
        {
            var view = new DynamicTableHeaderView
            {
                Prop = GetDynamicTableHeaderField("Prop"),
                TableColumnWidth = GetDynamicTableHeaderField("Width"),
                MinWidthFlag = (null != GetDynamicTableHeaderField("MinWidthFlag")),
                FixedPosition = GetDynamicTableHeaderField("FixedPosition"),
                Align = GetDynamicTableHeaderField("Align"),
                HeaderAlign = GetDynamicTableHeaderField("HeaderAlign"),
                SlotName = GetDynamicTableHeaderField("SlotName"),
                ColumnClassName = GetDynamicTableHeaderField("ClassName"),
                ColumnStyle = GetDynamicTableHeaderField("Style"),
                Label = dynamicTableColum.ColumnShowName,
                Sort = short.TryParse(GetDynamicTableHeaderField("Sort"), out var sort) ? sort : null,
                SortFlag = bool.TryParse(GetDynamicTableHeaderField("SortFlag"), out var sortFlag) && sortFlag,
                ColumnSpan = int.TryParse(GetDynamicTableHeaderField("ColumnSpan"), out var columnSpan)? columnSpan : null
            };
            return view;
            //从配置中获取对应的表头属性
            string GetDynamicTableHeaderField(string attributeCode, string userID = null)
            {
                var fieldContent = columnAttributeList.Where(m => m.AttributeCode == attributeCode);
                string result = null;
                if (string.IsNullOrEmpty(userID))
                {
                    return fieldContent.FirstOrDefault(m => m.UserID == "999999")?.AttributeValue;
                }
                if (userID != "999999")
                {
                    result = fieldContent.FirstOrDefault(m => m.UserID == userID)?.AttributeValue;
                }
                if (result == null)
                {
                    return fieldContent.FirstOrDefault(m => m.UserID == "999999")?.AttributeValue;
                }
                return result;
            }
        }
    }
}
