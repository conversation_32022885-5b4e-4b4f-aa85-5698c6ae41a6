﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class TrainingClassService : ITrainingClassService
    {
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly ITrainingClassMainRepository _trainingClassMainRepository;
        private readonly ITrainingClassDetailRepository _trainingClassDetailRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly ICourseSettingRepository _courseSettingRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISignUpRecordRepository _signUpRecordRepository;

        public TrainingClassService(ITrainingClassMainRepository trainingClassMainRepository
            , ITrainingClassDetailRepository trainingClassDetailRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , ICourseSettingRepository courseSettingRepository
            , IUnitOfWork unitOfWork
            , ISignUpRecordRepository signUpRecordRepository)
        {
            _trainingClassMainRepository = trainingClassMainRepository;
            _trainingClassDetailRepository = trainingClassDetailRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _courseSettingRepository = courseSettingRepository;
            _unitOfWork = unitOfWork;
            _signUpRecordRepository = signUpRecordRepository;
        }

        /// <summary>
        /// 获取培训群组列表
        /// </summary>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<List<TrainingClassView>> GetTrainingClassList(Session session)
        {
            var trainingClassViews = new List<TrainingClassView>();
            var mainInfos = await _trainingClassMainRepository.GetTrainingClassViewList();
            if (mainInfos.Count == 0)
            {
                return trainingClassViews;
            }
            var trainingClassMainIDs = mainInfos.Select(m => m.TrainingClassMainID).ToList();
            var employeeIDs = mainInfos.Select(m => m.AddEmployeeID).ToList().Union(mainInfos.Select(m => m.ModifyEmployeeID).ToList());
            var employeeNameList = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var detailInfos = await _trainingClassDetailRepository.GetListByMainIDs(trainingClassMainIDs);
            //获取培训群组数据
            var signUpRecordList = await _signUpRecordRepository.GetListByConditionAsNoTrackAsync(m => m.SourceType == "2");
            foreach (var item in mainInfos)
            {
                var trainingClassView = new TrainingClassView();
                var signUpRecordInfo = signUpRecordList.FirstOrDefault(m=>m.EmployeeID == session.EmployeeID && m.SourceID == item.TrainingClassMainID);
                trainingClassView.TrainingClassMainID = item.TrainingClassMainID;
                trainingClassView.TrainingClassName = item.TrainingClassName;
                trainingClassView.StartDate = item.StartDate;
                trainingClassView.EndDate = item.EndDate;
                trainingClassView.TrainingDuration = item.TrainingDuration;
                trainingClassView.CompleteDate = item.CompleteDate;
                trainingClassView.AddDateTime = item.AddDateTime;
                trainingClassView.AddEmployeeName = employeeNameList.TryGetValue(item.AddEmployeeID, out var addEmployeeName) ? addEmployeeName : item.AddEmployeeID;
                trainingClassView.ModifyDateTime = item.ModifyDateTime;
                trainingClassView.ModifyEmployeeName = employeeNameList.TryGetValue(item.ModifyEmployeeID, out var modifyEmployeeName) ? modifyEmployeeName : item.AddEmployeeID;
                trainingClassView.CourseSettingIDArr = detailInfos.Where(m => m.TrainingClassMainID == item.TrainingClassMainID).Select(m => m.CourseSettingID).ToList();
                trainingClassView.SignUpRecord = signUpRecordInfo == null ? new SignUpRecordInfo() : signUpRecordInfo;
                trainingClassViews.Add(trainingClassView);
            }
            return trainingClassViews.OrderByDescending(m => m.ModifyDateTime).ToList();
        }

        /// <summary>
        /// 保存培训群组
        /// </summary>
        /// <param name="trainingClassView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> SaveTrainingClass(TrainingClassView trainingClassView, string employeeID)
        {
            if (trainingClassView == null)
            {
                return false;
            }
            if (string.IsNullOrEmpty(trainingClassView.TrainingClassMainID))
            {
                return await InsertTrainingClassAsync(trainingClassView, employeeID);
            }
            return await UpdateTrainingClassAsync(trainingClassView, employeeID);
        }

        /// <summary>
        /// 更新培训群组
        /// </summary>
        /// <param name="trainingClassView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<bool> UpdateTrainingClassAsync(TrainingClassView trainingClassView, string employeeID)
        {
            var oldMain = await _trainingClassMainRepository.GetListByMainID(trainingClassView.TrainingClassMainID);
            if (oldMain != null)
            {
                oldMain.TrainingClassName = trainingClassView.TrainingClassName;
                oldMain.StartDate = trainingClassView.StartDate;
                oldMain.EndDate = trainingClassView.EndDate;
                oldMain.TrainingDuration = trainingClassView.TrainingDuration;
                oldMain.CompleteDate = trainingClassView.CompleteDate;
                oldMain.Modify(employeeID);
            }
            var oldDetailList = await _trainingClassDetailRepository.GetListByMainID(trainingClassView.TrainingClassMainID);
            if (trainingClassView.CourseSettingIDArr == null || trainingClassView.CourseSettingIDArr.Count == 0)
            {
                oldDetailList.ForEach(m => m.Delete(employeeID));
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            var newCourseSettingIDs = trainingClassView.CourseSettingIDArr;
            var oldCourseSettingIDs = oldDetailList.Select(m => m.CourseSettingID).ToList();
            //新无旧有删除
            var delDetailInfos = oldDetailList.Where(m => !newCourseSettingIDs.Contains(m.CourseSettingID)).ToList();
            delDetailInfos.ForEach(m => m.Delete(employeeID));
            //新有旧无新增
            var newCourseList = newCourseSettingIDs.Where(m => !oldCourseSettingIDs.Contains(m)).ToList();
            var detailInfos = new List<TrainingClassDetailInfo>();
            foreach (var item in newCourseList)
            {
                var detailInfo = new TrainingClassDetailInfo();
                detailInfo.TrainingClassDetailID = detailInfo.GetId();
                detailInfo.TrainingClassMainID = trainingClassView.TrainingClassMainID;
                detailInfo.CourseSettingID = item;
                detailInfo.Add(employeeID);
                detailInfo.Modify(employeeID);
                detailInfos.Add(detailInfo);
            }
            await _unitOfWork.GetRepository<TrainingClassDetailInfo>().InsertAsync(detailInfos);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 新增培训群组
        /// </summary>
        /// <param name="trainingClassView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<bool> InsertTrainingClassAsync(TrainingClassView trainingClassView, string employeeID)
        {
            var mainInfo = new TrainingClassMainInfo();
            mainInfo.TrainingClassMainID = mainInfo.GetId();
            mainInfo.TrainingClassName = trainingClassView.TrainingClassName;
            mainInfo.StartDate = trainingClassView.StartDate;
            mainInfo.EndDate = trainingClassView.EndDate;
            mainInfo.TrainingDuration = trainingClassView.TrainingDuration;
            mainInfo.CompleteDate = trainingClassView.CompleteDate;
            mainInfo.Add(employeeID);
            mainInfo.Modify(employeeID);
            if (trainingClassView.CourseSettingIDArr != null && trainingClassView.CourseSettingIDArr.Count > 0)
            {
                var detailInfos = new List<TrainingClassDetailInfo>();
                foreach (var courseSettingID in trainingClassView.CourseSettingIDArr)
                {
                    var detailInfo = new TrainingClassDetailInfo();
                    detailInfo.TrainingClassDetailID = detailInfo.GetId();
                    detailInfo.TrainingClassMainID = mainInfo.TrainingClassMainID;
                    detailInfo.CourseSettingID = courseSettingID;
                    detailInfo.Add(employeeID);
                    detailInfo.Modify(employeeID);
                    detailInfos.Add(detailInfo);
                }
                await _unitOfWork.GetRepository<TrainingClassDetailInfo>().InsertAsync(detailInfos);
            }
            await _unitOfWork.GetRepository<TrainingClassMainInfo>().InsertAsync(mainInfo);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 删除培训群组
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteTrainingClassByID(string trainingClassMainID, string employeeID)
        {
            var main = await _trainingClassMainRepository.GetListByMainID(trainingClassMainID);
            if (main == null)
            {
                return false;
            }
            main.Delete(employeeID);
            var detailList = await _trainingClassDetailRepository.GetListByMainID(trainingClassMainID);
            detailList.ForEach(m => m.Delete(employeeID));
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 根据培训群组ID获取培训群组课程列表
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        public async Task<List<TrainingClassCourseView>> GetTrainingClassCourseListByMainID(string trainingClassMainID)
        {
            var trainingClassCourseViews = new List<TrainingClassCourseView>();
            var detailList = await _trainingClassDetailRepository.GetListByMainID(trainingClassMainID);
            if (detailList.Count == 0)
            {
                return trainingClassCourseViews;
            }
            var trainingClassCourseList = await GetTrainingClassCourseListAsync(detailList);
            var parentCourseInfos = trainingClassCourseList.Where(m => m.ParentID == null).OrderBy(m => m.Year).ToList();
            var childCourseInfos = trainingClassCourseList.Except(parentCourseInfos).ToList();
            TrainingClassCourseView classCourseView = null;
            foreach (var parentCourseInfo in parentCourseInfos)
            {
                //转换父节点Model to View
                classCourseView = CourseModelTransforView(parentCourseInfo);
                //转换子节点Model to View
                var childCourses = childCourseInfos.Where(m => m.ParentID == parentCourseInfo.CourseSettingID)
                    .Select(CourseModelTransforView).OrderBy(m => m.Year).ToList();
                classCourseView.ChildCourses = childCourses;
                trainingClassCourseViews.Add(classCourseView);
            }
            return trainingClassCourseViews;
            // Model 转 View
            static TrainingClassCourseView CourseModelTransforView(CourseSettingInfo m)=>new TrainingClassCourseView()
            {
                CourseSettingID = m.CourseSettingID.ToString(),
                CourseName = m.CourseName,
                CourseIntroduction = m.CourseIntroduction,
                Year = m.Year,
                CourseTypeID = m.CourseTypeID,
                CourseTypeName = "",
                Level = m.Level,
                ParentID = m.ParentID,
            };
        }
        /// <summary>
        /// 获取培训群组课程列表
        /// </summary>
        /// <param name="detailList"></param>
        /// <returns></returns>
        private async Task<List<CourseSettingInfo>> GetTrainingClassCourseListAsync(List<TrainingClassDetailInfo> detailList)
        {
            var trainingClassCourseList = new List<CourseSettingInfo>();
            var courseSettingInfos = await _courseSettingRepository.GetAllCourseSetting();
            var courseSettingIDs = detailList.Select(m => m.CourseSettingID).ToList();

            foreach (var courseSettingID in courseSettingIDs)
            {
                GetAllRelatedCourses(courseSettingInfos, courseSettingID, trainingClassCourseList);
            }
            return trainingClassCourseList;
        }
        /// <summary>
        /// 获取所有相关课程
        /// </summary>
        /// <param name="courseSettingInfos"></param>
        /// <param name="courseSettingID"></param>
        /// <param name="trainingClassCourseList"></param>
        private static void GetAllRelatedCourses(List<CourseSettingInfo> courseSettingInfos, string courseSettingID, List<CourseSettingInfo> trainingClassCourseList)
        {
            var course = courseSettingInfos.FirstOrDefault(m => m.CourseSettingID == courseSettingID);
            if (course == null)
            {
                return;
            }
            trainingClassCourseList.Add(course);
            if (course.ParentID != null)
            {
                GetAllRelatedCourses(courseSettingInfos, course.ParentID, trainingClassCourseList);
            }
        }
        /// <summary>
        /// 获取培训群组下拉选项集合
        /// </summary>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetTrainClassOptions()
        {
            var traingingClassMains = await _trainingClassMainRepository.GetTrainClassOptions();

            return traingingClassMains;
        }
    }
}