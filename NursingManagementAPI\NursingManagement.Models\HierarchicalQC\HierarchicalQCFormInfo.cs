using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 质控表单字典表
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCForm")]
    public class HierarchicalQCFormInfo : MutiModifyInfo
    {

        /// <summary>
        /// 质控表单序号
        /// </summary>
        public int HierarchicalQCFormID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(50)")]
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 获取QualityControlFormContent明细数据
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string TemplateCode { get; set; }
        /// <summary>
        /// 字典分类
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string FormType { get; set; }
        /// <summary>
        /// 表单名称
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string FormName { get; set; }
        /// <summary>
        /// 0：停用 1：启用
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 添加部门，护理管理组织架构的DepartmentID
        /// </summary>
        public int AddDepartmentID { get; set; }
    }
}
