﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Post;

namespace NursingManagement.Services.Interface
{
    public interface IPostService
    {
        /// <summary>
        /// 获取科室岗位字典
        /// </summary>
        /// <param name="departmentCode">科室代码</param>
        /// <returns></returns>
        Task<List<DepartmentPostView>> GetDepartmentPostsAsync(int departmentCode);

        /// <summary>
        /// 临时导数据使用
        /// </summary>
        /// <param name="postDescriptionList"></param>
        /// <returns></returns>
        Task<bool> SavePostDescriptionList(List<PostDescriptionInfo> postDescriptionList);
        /// <summary>
        /// 获取科室岗位说明书
        /// </summary>
        /// <param name="departmentID">科室代码</param>
        /// <returns></returns>
        Task<List<PostDescriptionView>> GetPostDescriptionInfosAsync(int departmentID,string hospitalID, int language);
        /// <summary>
        /// 获取岗位说明书
        /// </summary>
        /// <param name="departmentID">科室代码</param>
        /// <param name="postID">岗位序号</param>
        /// <returns></returns>
        Task<PostDescriptionInfo> GetPostDescriptionInfoAsync(int departmentID, int postID,string hospitalID,int language);

      /// <summary>
      /// 更新部门岗位
      /// </summary>
      /// <param name="updateDepartmentPostView"></param>
      /// <returns></returns>
        Task<bool> UpdatePostStatus(UpdateDepartmentPostView updateDepartmentPostView);

        /// <summary>
        /// 获取科室岗位设置数据
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<object> GetDepartmentPostSettingAsync(int? departmentID);
        /// <summary>
        /// 保存部门岗位设置数据
        /// </summary>
        /// <param name="departmentPostSetting"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SaveDepartmentPostSettingAsync(DepartmentPostSettingInfo departmentPostSetting,string hospitalID,string employeeID);
        /// <summary>
        /// 删除岗位设定记录
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="departmentID">岗位ID</param>
        /// <param name="employeeID">执行人工号</param>
        /// <param name="type">l类型</param>
        /// <returns></returns>
        Task<bool> DeleteDepartmentPostSettingAsync(int postID, int departmentID, string employeeID, string type);
        /// <summary>
        /// 获取部门岗位对能级记录
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="postID"></param>
        /// <returns></returns>
        Task<List<DepartmentPostToCapabilityLevelView>> GetDepartmentPostToCapabilityLevelsAsync(int? departmentID, int? postID);
        /// <summary>
        /// 新增和修改岗位能级对照关系处理
        /// </summary>
        /// <param name="view"></param>
        /// <param name="hospitalID">i医院累呗</param>
        /// <param name="employeeID">执行人工号</param>
        /// <returns></returns>
        Task<bool> SaveDepartmentPostToCapabilityLevelAsync(DepartmentPostToCapabilityLevelInfo view,string hospitalID, string employeeID);
        /// <summary>
        /// 删除部门岗位与能级对照关系记录
        /// </summary>
        /// <param name="deleteDepartmentPostToCapabilityLevelView">参数</param>
        /// <param name="employeeID">会话</param>
        /// <returns></returns>
        Task<bool> DeleteDepartmentPostToCapabilityLevelAsync(DeleteDepartmentPostToCapabilityLevelView deleteDepartmentPostToCapabilityLevelView, string employeeID);
        /// <summary>
        /// 根据部门编号和护理能级获取符合条件的科室岗位集合
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="capabilityLevelID"></param>
        /// <returns></returns>
        Task<List<int>> GetDepartmentPostIDsByCapabilityLevel(int departmentID, int capabilityLevelID);
        /// <summary>
        /// 保存岗位说明数据
        /// </summary>
        /// <param name="saveData"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<string> SaveDepartmentPost(SaveDepartmentPostView saveData, string hospitalID, int language);
        /// <summary>
        ///批量插入岗位说明数据
        /// </summary>
        /// <param name="saveDatas"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<bool> BatchSaveDepartmentPost(List<SaveDepartmentPostView> saveDatas, string hospitalID, int language);
        /// <summary>
        /// 批量保存部门岗位设置数据
        /// </summary>
        /// <param name="departmentPostSetting"></param>
        /// <returns></returns>
        Task<bool> BatchSaveDepartmentPostSettingAsync(List<DepartmentPostSettingInfo> departmentPostSettings, string hospitalID, string employeeID);
        /// <summary>
        /// 批量插入岗位能级数据
        /// </summary>
        /// <param name="bodyView"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<ResponseResult> SaveDepartmentPostToCapabilityLevelListAsync(List<DepartmentPostToCapabilityLevelInfo> bodyView, string hospitalID, string employeeID);
        /// 删除部门岗位说明书数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> DeletePostDescription(DeletePostDescriptionView view);
        /// <summary>
        /// 检核数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> CheckPostDescriptionData(SavePostDescriptionView view);
        /// <summary>
        /// 保存岗位说明书明细内容
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SavePostDescription(SavePostDescriptionView view);
        /// <summary>
        /// 批量保存岗位说明书数据
        /// </summary>
        /// <param name="list"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<bool> BatchSavePostDescription(List<SavePostDescriptionView> list, string hospitalID, int language);
        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <returns></returns>
        Task<List<PostView>> GetPostListAsync();
        /// <summary>
        /// 保存岗位数据
        /// </summary>
        /// <param name="view"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SavePostDataAsync(PostView view, string employeeID);

        /// <summary>
        /// 更新岗位数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        Task<bool> DeletePostDataAsync(int postID, string employeeID);
        /// <summary>
        /// 激活岗位数据
        /// </summary>
        /// <param name="postID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> ActivatePostDataAsync(int postID, string employeeID);
        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        Task<bool> GetPostWhetherDataAsync(int postID, string hospitalID);
    }
}
