﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IExaminationPaperTemplateService
    {
        /// <summary>
        /// 根据题库ID获取试卷模版（理论类）
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <param name="isPractice">是否为刷题练习</param>
        /// <param name="paperTitle"></param>
        /// <returns></returns>
        Task<(FormTemplateView, int)> CreateTemplateByBankID(string questionBankID, bool? isPractice = false, string paperTitle = null);
        /// <summary>
        /// 获取理论试卷模版gruop内容
        /// </summary>
        /// <param name="questionList"></param>
        /// <param name="paperType"></param>
        /// <returns></returns>
        Task<List<PaperGroupView>> GetTheoryPaperPaperGroup(List<PaperQuestionView> questionList, string paperType);
        /// <summary>
        /// 根据题库ID获取试卷模板（实操类）
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <param name="paperTitle"></param>
        /// <param name="isPractical"></param>
        /// <returns></returns>
        Task<(FormTemplateView, int)> GetPaperTemplateByBankID(string questionBankID, string paperTitle, bool isPractical);
        /// <summary>
        /// 将试卷数据转换为试卷模板
        /// </summary>
        /// <param name="paperGroups"></param>
        /// <param name="paperQuestionList"></param>
        /// <param name="paperTitle"></param>
        /// <param name="isPractical"></param>
        /// <param name="oneGroupIsDisplay"></param>
        /// <param name="paperQuestionMode"></param>
        /// <returns></returns>
        Task<FormTemplateView> ConvertToFormTemplate(List<PaperGroupView> paperGroups, List<PaperQuestionView> paperQuestionList, string paperTitle, bool isPractical, bool oneGroupIsDisplay = true, string paperQuestionMode = "1");
    }
}
