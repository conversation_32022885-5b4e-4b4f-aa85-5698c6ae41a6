﻿using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services.Examine
{
    public class CompositionPaper
    {
        /// <summary>
        /// 项目分数明细项
        /// </summary>
        public const int COMPONENT_ATTRIBUTE_ITEM_SCORE = 63;

        /// <summary>
        /// 题目类型-判断题
        /// </summary>
        private const string QUESTION_TYPE_JUDGMENT = "Judgment";

        public virtual Task<List<PaperQuestionView>> FilterExaminationQuestion(string questionType, List<ExaminationQuestionInfo> examinationQuestionList, PaperCompositionRuleView paperCompositionRuleView, List<PaperQuestionView> choicePaperQuestionlList, ExaminationPaperMainInfo examinationPaperMainData, string modifyEmployeeID)
        {
            return Task.FromResult(new List<PaperQuestionView>());
        }
        /// <summary>
        ///  统一处理组卷过程中题目选项顺序问题（后续答案选项顺序自定义可以在这里做）
        /// </summary>
        /// <param name="question"></param>
        /// <param name="questionDetailList"></param>
        /// <returns></returns>
        internal IEnumerable<ExaminationQuestionDetailInfo> SortQuestionOption(ExaminationQuestionInfo question, List<ExaminationQuestionDetailInfo> questionDetailList)
        {
            IEnumerable<ExaminationQuestionDetailInfo> detailEnumerables = questionDetailList.Where(detail => question.ExaminationQuestionID == detail.ExaminationQuestionID);
            var options = questionDetailList.Where(m => m.ExaminationQuestionID == question.ExaminationQuestionID)
                                          .OrderBy(m => m.Sort)
                                          .Select(m => new PaperQuestionOptionsView()
                                          {
                                              Value = m.ExaminationQuestionDetailID,
                                              Label = m.Content,
                                              FixedItemID = m.ExaminationQuestionDetailID
                                          }).ToList();
            if (question.ExaminationQuestionType == QUESTION_TYPE_JUDGMENT)
            {
                detailEnumerables = detailEnumerables.OrderBy(m => m.Content == "正确" ? 0 : 1).ThenBy(m => m.Content == "对" ? 0 : 1);
            }
            else
            {
                detailEnumerables = detailEnumerables.OrderBy(m => m.Sort);
            }
            return detailEnumerables;
        }
    }
}
