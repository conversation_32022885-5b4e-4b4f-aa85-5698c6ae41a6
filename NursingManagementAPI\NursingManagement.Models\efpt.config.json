﻿{
   "CodeGenerationMode": 2,
   "ContextClassName": "NursingManagementContext",
   "ContextNamespace": null,
   "DefaultDacpacSchema": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Employee",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "NursingManagement.Models",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[EmployeeClothingSizes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeContact]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeEducationalExperience]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeEmploymentRecord]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeNurseLevel]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeePersonalData]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeProfessionalPosition]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeRelatives]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeReward]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeSkill]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeStaffData]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmployeeWorkExperience]",
         "ObjectType": 0
      }
   ],
   "UiHint": "iz25m9s36q6z.NursingManagement.dbo",
   "UncountableWords": null,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": true,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false
}