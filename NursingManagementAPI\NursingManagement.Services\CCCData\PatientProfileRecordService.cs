﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModel;

namespace NursingManagement.Services
{
    public class PatientProfileRecordService : IPatientProfileRecordService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRequestApiService _requestApiService;
        private readonly IDepartmentListRepository _departmentListRepository;

        public PatientProfileRecordService(
              IUnitOfWork unitOfWork
            , IRequestApiService requestApiService
            , IDepartmentListRepository departmentListRepository
        )
        {
            _unitOfWork = unitOfWork;
            _requestApiService = requestApiService;
            _departmentListRepository = departmentListRepository;
        }
        /// <summary>
        /// 获取CCC某种状况患者信息（病危、病重……）
        /// </summary>
        /// <param name="view"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileRecordView>> GetCCCPatientProfileRecord(GetCCCPatientProfileDataView view,string hospitalID)
        {
            var returnView = new List<PatientProfileRecordView>();
            //护理管理部门与CCC病区转换
            var deparmentMapping = await _departmentListRepository.GetNMDepartmentByHisDepartment("3", "1", hospitalID);
            view.StationIDs ??= new List<int>();
            deparmentMapping = deparmentMapping.Where(m=> view.StationIDs.Contains(Convert.ToInt32(m["nmDepartmentID"]))).ToList();
            if (deparmentMapping.Count > 0)
            {
                var stationIDs = deparmentMapping.Select(m => m["stationID"].ToString()).ToList();
                foreach (var stationID in stationIDs)
                {
                    if (int.TryParse(stationID,out var stationIDInt))
                    {
                        view.StationIDs.Add(stationIDInt);
                    }
                }
            }
            object result;
            try
            {
                result = await _requestApiService.RequestAPI("GetCCCPatientProfile", ListToJson.ToJson(view));
            }
            catch (Exception ex)
            {
                _logger.Error(ex + "获取CCC患者数据失败 参数||" + ListToJson.ToJson(view));
                return returnView;
            }
            string stringResult = result == null ? "" : result.ToString();
            if (string.IsNullOrEmpty(stringResult))
            {
                _logger.Error("获取CCC患者数据失败 参数||" + ListToJson.ToJson(view));
                return returnView;
            }
            var response = ListToJson.ToList<ResponseResult>(stringResult);
            if (response.Data != null)
            {
                returnView = ListToJson.ToList<List<PatientProfileRecordView>>(response.Data.ToString());
            }
            return returnView;
        }

    }
}