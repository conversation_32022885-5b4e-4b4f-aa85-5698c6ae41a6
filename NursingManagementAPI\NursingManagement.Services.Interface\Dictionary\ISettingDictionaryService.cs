﻿using NursingManagement.ViewModels;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Services.Interface
{
    public interface ISettingDictionaryService
    {
        /// <summary>
        /// 获取 质控表单类型
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <param name="qcType"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetQCFormType(string settingType, string settingTypeCode, QCTypeEnum? qcType);
        /// <summary>
        /// 获取非国标字典
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetSettingDictionaryDict(SettingDictionaryParams settingDictionaryParams);
        /// <summary>
        ///  获取部门岗位工作时间配置
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="currentDate"></param>
        /// <returns></returns>
        Task<string> GetCurrentPostSeason(int departmentID, DateTime? currentDate);
        /// <summary>
        /// 获取消息通知工具开关配置
        /// </summary>
        /// <param name="settingTypeCode"></param>
        /// <returns></returns>
        Task<List<(string settingTypeCode, bool switchFlag)>> GetMessageNotifySettingAsync(string settingTypeCode);
        /// <summary>
        /// 获取语言列表
        /// </summary>
        /// <returns></returns>
        Task<List<Dictionary<string, object>>> GetLanguageList();
        /// <summary>
        /// 获取审批页面中那些审批项目具有跳转到源业务页面的按钮和逻辑
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, bool>> GetApprovalJumpSettingAsync();
        /// <summary>
        /// 获取开关类配置
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        Task<bool> GetSettingSwitchAsync(SettingDictionaryParams settingDictionaryParams);
        /// <summary>
        /// 获取审批分类级联选择器数据
        /// </summary>
        /// <returns></returns>
        Task<List<CascaderView<string>>> GetProveCategoryCascaderList();
        /// <summary>
        /// 获取组织架构类型
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetOrganizationTypes();
        /// <summary>
        /// 获取考核/培训分类数据
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <returns></returns>
        Task<List<TrainingExamineClassifiedView>> GetSettingDictionaryMaintain(string settingType, string settingTypeCode);

        /// <summary>
        ///  删除分类数据
        /// </summary>
        /// <param name="settingValue"></param>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteSettingDictionaryMaintain(string settingValue, string settingType, string settingTypeCode, string employeeID);
        /// <summary>
        /// 保存分类数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveSettingDictionaryMaintain(TrainingExamineClassifiedView view);

        /// <summary>
        ///  根据SettingTypeCode和SettingTypeValue获取配置键值对配置
        /// </summary>
        /// <param name="settingTypeCode"></param>
        /// <param name="settingTypeValueList"></param>
        /// <returns></returns>
        Task<Dictionary<string, List<KeyValuePair<string, string>>>> GetSettingDictionaryByCodeValue(string settingTypeCode, List<string> settingTypeValueList);
        /// <summary>
        ///  根据SettingTypeCode和SettingTypeValue获取配置级联数据
        /// </summary>
        /// <param name="settingTypeCode"></param>
        /// <param name="settingTypeValueList"></param>
        /// <returns></returns>
        Task<Dictionary<string, List<CascaderView<string>>>> GetCascaderSettingDictionary(string settingTypeCode, List<string> settingTypeValueList);
    }
}
