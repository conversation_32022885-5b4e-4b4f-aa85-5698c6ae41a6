﻿using DocumentFormat.OpenXml.Bibliography;
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DepartmentPostToCapabilityLevelRepository : IDepartmentPostToCapabilityLevelRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public DepartmentPostToCapabilityLevelRepository(
            NursingManagementDbContext db
            , SessionCommonServer sessionCommonServer
            , IRedisService redisService
        )
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<List<DepartmentPostToCapabilityLevelInfo>> GetByDepartmentIDAsync(int departmentID)
        {
            var datas = await GetCacheAsync() as List<DepartmentPostToCapabilityLevelInfo>;
            return datas.Where(m => m.DepartmentID == departmentID).ToList();
        }

        public async Task<List<DepartmentPostToCapabilityLevelInfo>> GetByDepartmentIDAndPostIDAsync(int departmentID, int? postID)
        {
            var datas = await GetCacheAsync() as List<DepartmentPostToCapabilityLevelInfo>;
            return datas.Where(m => m.DepartmentID == departmentID && (!postID.HasValue || m.PostID == postID)).ToList();
        }

        public async Task<DepartmentPostToCapabilityLevelInfo> GetRecordByAllKeysAsync(int departmentID, int postID, int capabilityLevelID)
        {
            var datas = await GetCacheAsync() as List<DepartmentPostToCapabilityLevelInfo>;
            return datas.Where(m => m.DepartmentID == departmentID && m.PostID == postID && m.CapabilityLevelID == capabilityLevelID).FirstOrDefault();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DepartmentPostToCapabilityLevelInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.DepartmentPostToCapabilityLevel.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public async Task<List<DepartmentPostToCapabilityLevelInfo>> GetByIDsAsync(List<int> departmentPostToCapabilityLevelIDs)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _nursingManagementDbContext.DepartmentPostToCapabilityLevelInfos.
                Where(m => departmentPostToCapabilityLevelIDs.Contains(m.DepartmentPostToCapabilityLevelID)
                &&  m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<DepartmentPostToCapabilityLevelInfo> GetByIDAsync(int departmentPostToCapabilityLevelID)
        {
            var datas = await GetCacheAsync() as List<DepartmentPostToCapabilityLevelInfo>;
            return datas.Where(m => 
                m.DepartmentPostToCapabilityLevelID == departmentPostToCapabilityLevelID).FirstOrDefault();
        }
    }
}
