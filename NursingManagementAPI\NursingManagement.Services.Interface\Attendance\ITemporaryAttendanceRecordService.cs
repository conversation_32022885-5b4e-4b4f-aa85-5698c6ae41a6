﻿using NursingManagement.Common;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface ITemporaryAttendanceRecordService
    {
        /// <summary>
        /// 保存临时出勤记录
        /// </summary>
        /// <param name="view"></param>
        /// <param name="session"></param>
        /// <returns></returns>
        Task<bool> SaveTemporaryAttendanceData(TemporaryAttendanceRecordView view, Session session);
        /// <summary>
        /// 获取临时出勤记录数据
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="showAllFlag">是否查看全科开关</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        Task<List<TemporaryAttendanceRecordView>> GetTemporaryAttendanceList(string employeeID, bool showAllFlag, int departmentID, DateTime month);
        /// <summary>
        ///  删除临时出勤记录
        /// </summary>
        /// <param name="recordID">临时出勤记录ID(表主键ID)</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        Task<bool> DeleteTemporaryAttendanceData(int recordID, string employeeID);
        /// <summary>
        /// 获取临时出勤岗位标识数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<DictItem>> GetTemporaryAttendancePostSetting(int departmentID);
    }
}
