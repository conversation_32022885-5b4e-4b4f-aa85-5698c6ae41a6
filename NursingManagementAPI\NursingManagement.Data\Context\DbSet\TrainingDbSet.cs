﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 培训群组主表
        /// </summary>
        public DbSet<TrainingClassMainInfo> TrainingClassMainInfos { get; set; }

        /// <summary>
        /// 培训群组明细表
        /// </summary>
        public DbSet<TrainingClassDetailInfo> TrainingClassDetailInfos { get; set; }
        /// <summary>
        /// 人员培训记录表
        /// </summary>
        public DbSet<TrainingLearnerInfo> TrainingLearnerInfos { get; set; }
        /// <summary>
        ///  培训评价主表
        /// </summary>
        public DbSet<TrainingEvaluationMainInfo> TrainingEvaluationMainInfos { get; set; }
        /// <summary>
        /// 培训评价明细表
        /// </summary>
        public DbSet<TrainingEvaluationDetailInfo> TrainingEvaluationDetailInfos { get; set; }
        /// <summary>
        /// 培训记录
        /// </summary>
        public DbSet<TrainingRecordInfo> TrainingRecordInfos { get; set; }
        /// <summary>
        /// 培训签到表
        /// </summary>
        public DbSet<SignUpRecordInfo> SignUpRecordInfos { get; set; }
    }
}