﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Table("DepartmentVSDepartment")]
    public class DepartmentVSDepartmentInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int ID { get; set; }
        /// <summary>
        /// 人事对应部门
        /// </summary>
        public int DepartmentID1 { get; set; }
        /// <summary>
        /// 组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门 
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string OrganizationType1 { get; set; }
        /// <summary>
        /// 护理管理对应部门
        /// </summary>
        public int DepartmentID2 { get; set; }
        /// <summary>
        /// 组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门 
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string OrganizationType2 { get; set; }
        /// <summary>
        /// 医院ID	
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
    }
}
