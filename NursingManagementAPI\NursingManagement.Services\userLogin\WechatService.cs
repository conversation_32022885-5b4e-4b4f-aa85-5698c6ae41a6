﻿using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;

namespace NursingManagement.Services
{
    public class WechatService : IWechatService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IUserLoginRepository _userLoginRepository;
        private readonly IRedisService _redisService;

        public WechatService(
            IAppConfigSettingRepository appConfigSettingRepository
            , IUserLoginRepository userLoginRepository
            , IRedisService redisService
        )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
            _userLoginRepository = userLoginRepository;
            _redisService = redisService;
        }
        public async Task<Dictionary<string, object>> CheckMiniProgramBinding(string loginCode)
        {
            var result = new Dictionary<string, object>()
            {
                { "isBinding" , false }, { "openID" , ""}
            };
            var miniProgramAppID = await _appConfigSettingRepository.GetConfigSettingValue("Wechat", "MiniProgramAppID");
            var miniProgramSecret = await _appConfigSettingRepository.GetConfigSettingValue("Wechat", "MiniProgramSecret");
            var miniProgramOpenIDUrl = await _appConfigSettingRepository.GetConfigSettingValue("Wechat", "GetMiniProgramOpenIDUrl");
            if (string.IsNullOrWhiteSpace(miniProgramAppID))
            {
                _logger.Error("WechatService.CheckMiniProgramBinding从AppCconfigSetting中获取配置失败，参数：Wechat,MiniProgramAppID");
                return result;
            }
            if (string.IsNullOrWhiteSpace(miniProgramSecret))
            {
                _logger.Error("WechatService.CheckMiniProgramBinding从AppCconfigSetting中获取配置失败，参数：Wechat,MiniProgramSecret");
                return result;
            }
            if (string.IsNullOrWhiteSpace(miniProgramOpenIDUrl))
            {
                _logger.Error("WechatService.CheckMiniProgramBinding从AppCconfigSetting中获取配置失败，参数：Wechat,GetMiniProgramOpenIDUrl");
                return result;
            }
            miniProgramOpenIDUrl = miniProgramOpenIDUrl.Replace("{appid}", miniProgramAppID).Replace("{secret}", miniProgramSecret).Replace("{code}", loginCode);
            var ret = await HttpHelper.HttpGetAsync(miniProgramOpenIDUrl);
            if (string.IsNullOrWhiteSpace(ret))
            {
                return null;
            }
            var resultDic = ListToJson.ToList<Dictionary<string, string>>(ret);
            if (resultDic == null || resultDic.ContainsKey("errcode") || string.IsNullOrWhiteSpace(resultDic["openid"]))
            {
                return result;
            }
            var openID = resultDic["openid"];
            result["openID"] = openID;
            var userLogin = await _userLoginRepository.GetByWechatMiniProgramOpenID(openID);
            if (userLogin == null)
            {
                return result;
            }
            result["isBinding"] = true;
            return result;
        }

        public async Task<bool> SendMiniProgramMessage(string openID)
        {
            // JSON格式
            //{
            // "touser": "oKbKD61j9YwoW85fslHMCBl3zjAU",
            // "template_id": "TqTtgMXiuo-4WUB1sGi-Rj6gdRWQw5R5uByWUNQfTzo",
            // "page": "pages/login/login?a=22",
            // "miniprogram_state": "developer",
            // "lang": "zh_CN",
            // "data": {
            //  "thing8": {
            //   "value": "信息科"
            //  },
            //  "thing7": {
            //   "value": "年假"
            //  },
            //  "thing6": {
            //   "value": "苏军志"
            //  },
            //  "time9": {
            //   "value": "2023-08-14"
            //  },
            //  "thing12": {
            //   "value": "测试护理管理系统小程序审批提醒消息"
            //  }
            // }
            //};
            var appid = "wxabaed18404474e3b";
            var secret = "12c3fff080ad60697f28ad647158671f";
            var access_token = await GetWechatAccessToken(appid, secret);
            // 测试先写死，可能正式不会用，如果正式要使用时 配置到AppConfigSetting
            var url = $"https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={access_token}";
            var param = new Dictionary<string, object>()
            {
                {"touser",openID },
                {"template_id","TqTtgMXiuo-4WUB1sGi-Rj6gdRWQw5R5uByWUNQfTzo" },
                {"page","pages/login/login?a=22" },
                {"miniprogram_state","developer" }, // 可省略
                {"lang","zh_CN" }, // 可省略
                {"data",new Dictionary<string,object>()
                    {
                        { "thing8", new Dictionary<string,string>() { { "value","信息科" } } },
                        { "thing7", new Dictionary<string,string>() { { "value", "年假" } } },
                        { "thing6", new Dictionary<string,string>() { { "value", "苏军志" } } },
                        { "time9", new Dictionary<string,string>() { { "value", "2023-08-14" } } },
                        { "thing12", new Dictionary<string,string>() { { "value", "测试护理管理系统小程序审批提醒消息" } } }
                    }
                }
            };


            var ret = HttpHelper.HttpPost(url, ListToJson.ToJson(param), "application/json");
            if (!string.IsNullOrWhiteSpace(ret))
            {
                var result = ListToJson.ToList<Dictionary<string, string>>(ret);
                if (result != null && result["errcode"] == "0")
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取访问微信服务的令牌
        /// </summary>
        /// <returns></returns>
        private async Task<string> GetWechatAccessToken(string appid, string secret)
        {
            var accessToken = "";
            //var appid = "wxabaed18404474e3b";
            //var secret = "12c3fff080ad60697f28ad647158671f";
            // 测试先写死，正式使用时 配置到AppConfigSetting
            var url = $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}";
            var ret = await HttpHelper.HttpGetAsync(url);
            if (!string.IsNullOrWhiteSpace(ret))
            {
                var result = ListToJson.ToList<Dictionary<string, string>>(ret);
                if (result != null && !string.IsNullOrWhiteSpace(result["access_token"]))
                {
                    accessToken = result["access_token"];
                }
            }
            return accessToken;
        }
        /// <summary>
        /// 检核微信浏览器登录账号是否绑定
        /// </summary>
        /// <param name="openID"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, object>> CheckBrowserBinding(string openID)
        {
            var result = new Dictionary<string, object>()
            {
                { "isBinding" , false },
                { "openID" , ""}
            };
            var userLogin = await _userLoginRepository.GetByWechatWebOpenID(openID);
            if (userLogin == null)
            {
                return result;
            }
            result["isBinding"] = true;
            result["openID"] = userLogin.WechatWebOpenID;
            result["hospitalID"] = userLogin.HospitalID;
            return result;
        }
        public async Task<Dictionary<string, string>> GetWxConfig(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                return null;
            }
            //var wxConfig = await _redisService.GetAsync<Dictionary<string, string>>("WxConfig");
            //if (wxConfig!=null && !string.IsNullOrWhiteSpace(wxConfig["signature"]))
            //{
            //    _logger.Info("从缓存中返回微信配置");
            //    return wxConfig;
            //}
            var appID = await _appConfigSettingRepository.GetConfigSettingValue("Wechat", "PublicAccountAPPID");
            var getWxConfigAPI = await _appConfigSettingRepository.GetConfigSettingValue("Wechat", "GetWxConfigAPI");

            if (string.IsNullOrWhiteSpace(appID) || string.IsNullOrWhiteSpace(getWxConfigAPI))
            {
                return null;
            }
            var ret = await HttpHelper.HttpGetAsync($"{getWxConfigAPI}?app_id={appID}&page_url={url}");
            if (string.IsNullOrWhiteSpace(ret))
            {
                _logger.Error("访问微信接口失败!");
                return null;
            }
            var result = ListToJson.ToList<ResponseResult>(ret);
            if (result == null || !result.IsSuccess())
            {
                _logger.Error("获取微信接口错误：" + result != null ? result.Message : "");
                return null;
            }
            var wxConfig = ListToJson.ToList<Dictionary<string, string>>(result.Data.ToString());
            //await _redisService.Add("WxConfig", 7000, wxConfig);
            return wxConfig;
        }
    }
}
