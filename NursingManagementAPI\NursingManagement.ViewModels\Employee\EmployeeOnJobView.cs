﻿
namespace NursingManagement.ViewModels
{
    public class EmployeeOnJobView
    {
        /// <summary>
        /// 奖惩记录
        /// </summary>
        public List<EmployeeRewardView> EmployeeReward { get; set; }
        /// <summary>
        /// 在职信息
        /// </summary>
        public List<EmployeeStaffDataView> EmployeeStaffData { get; set; }
        /// <summary>
        /// 岗位层级
        /// </summary>
        public List<EmployeeCapabilityLevelView> EmployeeCapabilityLevel { get; set; }
        /// <summary>
        /// 在本公司的任职记录
        /// </summary>
        public List<EmployeeEmploymentView> EmployeeEmploymentRecord { get; set; }
        /// <summary>
        /// 人员工号
        /// </summary>
        public string EmployeeID { get; set; }
    }
}
