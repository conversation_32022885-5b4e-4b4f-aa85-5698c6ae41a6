﻿namespace NursingManagement.ViewModels
{
    public class EmployeeRoleView
    {
        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 人员名称
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 角色名称（可能多个角色名称拼接）
        /// </summary>
        public List<string> RoleNames { get; set; }
        /// <summary>
        /// 员工当前所有角色对应的角色ID
        /// </summary>
        public List<int> AuthorityRoleIDs { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public DateTime? ModifyDateTime { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
    }
}