﻿namespace NursingManagement.ViewModels.Employee
{
    /// <summary>
    /// 人员工作经历视图
    /// </summary>
    public class EmployeeWorkExperienceView
    {
        public string EmployeeWorkExperienceID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 就职开始时间
        /// </summary>
        public string StartDate { get; set; }

        /// <summary>
        /// 就职结束时间
        /// </summary>
        public string EndDate { get; set; }

        /// <summary>
        /// 就职公司
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// 所在部门
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 担任职务
        /// </summary>
        public string Post { get; set; }
        /// <summary>
        /// 备注
        /// </summary>

        public string Remark { get; set; }
    }
}
