﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class ShiftSchedulingDetailRepository : IShiftSchedulingDetailRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="nursingManagementDbContext"></param>
        /// <param name="sessionCommonServer"></param>
        public ShiftSchedulingDetailRepository(
            NursingManagementDbContext nursingManagementDbContext
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<ShiftSchedulingDetailInfo>> GetDetailByRecordID(string shiftSchedulingRecordID, DateTime? startDate, DateTime? endDate)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContext.ShiftSchedulingDetailInfos
                .Where(m => m.ShiftSchedulingRecordID == shiftSchedulingRecordID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                .IfWhere(startDate != null, m => m.SchedulingDate.Date >= startDate.Value.Date)
                .IfWhere(endDate != null, m => m.SchedulingDate.Date <= endDate.Value.Date)
                .ToListAsync();
        }
        /// <summary>
        /// 根据人员编号获取排班明细数据
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<List<ShiftSchedulingDetailInfo>> GetDetailByEmployeeID(string employeeID, int departmentID, string startNoon, string endNoon, DateTime? startDate, DateTime? endDate)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            if (!string.IsNullOrEmpty(startNoon) && !string.IsNullOrEmpty(endNoon))
            {
                return await (from m in _nursingManagementDbContext.ShiftSchedulingDetailInfos.Where(m => m.EmployeeID == employeeID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                          .IfWhere(startDate != null, m => m.SchedulingDate.Date > startDate.Value.Date || (m.SchedulingDate.Date == startDate.Value.Date && string.Compare(m.NoonType, startNoon) >= 0))
                          .IfWhere(startDate != null && endDate != null, m => m.SchedulingDate.Date < endDate.Value.Date || (m.SchedulingDate.Date == endDate.Value.Date && string.Compare(m.NoonType, endNoon) <= 0))
                          .IfWhere(startDate == null && endDate != null, m => m.SchedulingDate.Date > endDate.Value.Date || (m.SchedulingDate.Date == endDate.Value.Date && string.Compare(m.NoonType, endNoon) >= 0))
                              join n in _nursingManagementDbContext.ShiftSchedulingRecordInfos.Where(n => n.DepartmentID == departmentID)
                              on m.ShiftSchedulingRecordID equals n.ShiftSchedulingRecordID
                              select m).ToListAsync();
            }
            if (string.IsNullOrEmpty(startNoon) && !string.IsNullOrEmpty(endNoon))
            {
                return await (from m in _nursingManagementDbContext.ShiftSchedulingDetailInfos.Where(m => m.EmployeeID == employeeID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                          .IfWhere(startDate == null && endDate != null, m => m.SchedulingDate.Date > endDate.Value.Date || (m.SchedulingDate.Date == endDate.Value.Date && string.Compare(m.NoonType, endNoon) > 0))
                              join n in _nursingManagementDbContext.ShiftSchedulingRecordInfos.Where(n => n.DepartmentID == departmentID)
                              on m.ShiftSchedulingRecordID equals n.ShiftSchedulingRecordID
                              select m).ToListAsync();
            }
            return [];
        }
        /// <summary>
        /// 获取排班明细记录
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <returns></returns>
        public async Task<List<ShiftSchedulingDetail>> GetDetailByRecordID(string shiftSchedulingRecordID)
        {

            return await (from m in _nursingManagementDbContext.ShiftSchedulingDetailInfos.Where(m => m.ShiftSchedulingRecordID == shiftSchedulingRecordID)
                          join n in _nursingManagementDbContext.DepartmentPostInfos.Where(n => n.DepartmentPostName.Contains("责护"))
                          on m.DepartmentPostID equals n.DepartmentPostID
                          select new ShiftSchedulingDetail
                          {
                              ShiftSchedulingDetailID = m.ShiftSchedulingDetailID,
                              ShiftSchedulingRecordID = m.ShiftSchedulingRecordID,
                              HospitalID = m.HospitalID,
                              DepartmentPostID = m.DepartmentPostID,
                              DepartmentPostName = n.DepartmentPostName,
                              EmployeeID = m.EmployeeID,
                              SchedulingDate = m.SchedulingDate,
                              NoonType = m.NoonType,
                              AdjustScheduleRecordID = m.AdjustScheduleRecordID,
                          }).ToListAsync();
        }
        public async Task<List<ShiftSchedulingDetailInfo>> GetExcludeDepartmentDetailsByEmployeeIDs(List<string> employeeIDs, DateTime startDate, DateTime endDate, int excludeDepartmentID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            var shiftSchedulingDetails = await _nursingManagementDbContext.ShiftSchedulingDetailInfos.Where(m => employeeIDs.Contains(m.EmployeeID) && m.SchedulingDate.Date >= startDate.Date
                                            && m.SchedulingDate.Date <= endDate.Date && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
            var shiftSchedulingRecords = await _nursingManagementDbContext.ShiftSchedulingRecordInfos.Where(n => n.DepartmentID != excludeDepartmentID && n.StatusCode != "0" && n.DeleteFlag != "*")
                                        .OrderByDescending(j => j.ModifyDateTime).GroupBy(j => new { j.DepartmentID, j.StartDate, j.EndDate }).Select(j => j.FirstOrDefault()).ToListAsync();

            return (from m in shiftSchedulingDetails
                    join n in shiftSchedulingRecords
                    on m.ShiftSchedulingRecordID equals n.ShiftSchedulingRecordID
                    select m).ToList();
        }
        public async Task<Dictionary<string, int>> GetEmployeePostDays(List<string> employeeIDs, int departmentPostID, DateTime startDate, DateTime endDate)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            var shiftSchedulingDetails = await (from detail in _nursingManagementDbContext.ShiftSchedulingDetailInfos.Where(m => m.SchedulingDate >= startDate && m.SchedulingDate < endDate
                                                 && m.DepartmentPostID == departmentPostID && employeeIDs.Contains(m.EmployeeID)
                                                 && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                                         join record in _nursingManagementDbContext.ShiftSchedulingRecordInfos.Where(n => n.StatusCode=="1" && n.HospitalID == session.HospitalID && n.DeleteFlag != "*")
                                         on detail.ShiftSchedulingRecordID equals record.ShiftSchedulingRecordID
                                         select detail).ToListAsync();
            // 历史数据可能存在排班明细重复的情况
            return shiftSchedulingDetails.GroupBy(m => new { m.EmployeeID, m.SchedulingDate, m.NoonType })
                                         .Select(m => m.FirstOrDefault())
                                         .GroupBy(m => m.EmployeeID)
                                         .ToDictionary(m => m.FirstOrDefault().EmployeeID, m => m.Count() / 2);
        }
    }
}
