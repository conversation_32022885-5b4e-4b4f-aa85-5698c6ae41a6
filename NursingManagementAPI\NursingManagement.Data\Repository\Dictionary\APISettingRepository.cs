﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
namespace NursingManagement.Data.Repository
{
    public class APISettingRepository : IAPISettingRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public APISettingRepository(
              NursingManagementDbContext nursingManagementDbContext
            , IRedisService redisService
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var datas = await _redisService.GetOrCreateAsync(key, 0,  async () =>
            {
                var result = await _nursingManagementDbContext.APISettingInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public bool HasCache(string cacheType) => GetCacheType() == cacheType;

        public string GetCacheType() => CacheType.APISetting.GetKey(_sessionCommonServer);
    }
}
