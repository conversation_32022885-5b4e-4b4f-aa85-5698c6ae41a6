﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.ViewModels;
using StackExchange.Redis;

namespace NursingManagement.API
{
    /// <summary>
    /// 需要验证
    /// </summary>
    public class AuthorizationAttribute : ActionFilterAttribute
    {
        private readonly Logger _logger;
        private IRedisService _cache;
        private readonly IAppConfigSettingRepository  _appConfigSettingRepository;
        private readonly IOptions<SystemConfig> _config;

        /// <summary>
        /// 构造器注入
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="config"></param>
        /// <param name="appConfigSettingRepository"></param>
        public AuthorizationAttribute(
              IRedisService cache
              , IOptions<SystemConfig> config
            , IAppConfigSettingRepository  appConfigSettingRepository
           )
        {
            _logger = LogManager.GetCurrentClassLogger();
            _cache = cache;
           _config = config;
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        /// <summary>
        /// 接口执行时调用
        /// </summary>
        /// <param name="context"></param>
        public override async void OnActionExecuting(ActionExecutingContext context)
        {
            var isDefined = false;
            var controllerActionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;
            if (controllerActionDescriptor != null)
            {
                isDefined = controllerActionDescriptor.MethodInfo.GetCustomAttributes(inherit: true)
                   .Any(a => a.GetType().Equals(typeof(NoAuthorizationAttribute)));
            }
            if (isDefined)
            {
                base.OnActionExecuting(context);
                return;
            }
            //白名单
           // var clientIP = context.HttpContext.Connection.RemoteIpAddress.ToString();


           // var whiteList = _appConfigSettingRepository.GetConfigSettingValue("Configs", "WhiteListIPs").Result;
            //_logger.Debug("whiteList地址:" + whiteList);
            //_logger.Debug("医院代码:" + _config.Value.HospitalID);

            //if (whiteList != null && whiteList.Contains(clientIP))
            //{
            //    base.OnActionExecuting(context);
            //    return;
            //}

            //string token = context.HttpContext.GetToken();
            //if (token == null)
            //{
            //    context.HttpContext.NoAuthorization();
            //    context.Result = new JsonResult("No Authorization");
            //    _logger.Info("No Authorization，请求未发现token，IP：" + clientIP);
            //    return;
            //}

            //if (await _cache.GetAsync(token) == null)
            //{
            //    context.HttpContext.NoAuthorization();
            //    context.Result = new JsonResult("No Authorization");
            //    _logger.Info("No Authorization，服务器token不存在，IP：" + clientIP);
            //    return;
            //}
            base.OnActionExecuting(context);
        }
    }
}