﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划目标字典
    /// </summary>
    public class AnnualGoalListRepository : IAnnualGoalListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="nursingManagementDbContext"></param>
        /// <param name="sessionCommonServer"></param>
        /// <param name="redisService"></param>
        public AnnualGoalListRepository(
            NursingManagementDbContext nursingManagementDbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        /// <summary>
        /// 获取年度计划目标（无缓存）
        /// </summary>
        /// <param name="goalID">目标ID集合</param>
        /// <returns></returns>
        public async Task<AnnualGoalListInfo> GetGoalListInfoNoCache(int goalID)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            return await _nursingManagementDbContext.AnnualGoalListInfos.FirstOrDefaultAsync(m =>
            m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*" && goalID == m.AnnualGoalID);
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AnnualGoalListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.AnnualGoalList.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task  UpdateCache()
        {
            string key = GetCacheType();
           await _redisService.Remove(key);
        }
        /// <summary>
        /// 获取目标类型最大值
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxGoalID()
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var annualGoalList = await _nursingManagementDbContext.AnnualGoalListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language).Select(m => m.AnnualGoalID).ToListAsync();
                
            if (annualGoalList.Count<=0)
            {
                return 1;
            }
            return annualGoalList.Max()+1;
        }
    }
}
