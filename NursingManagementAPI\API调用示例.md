# 常态工作控制提醒API调用示例

## 外部定时任务调用示例

### 1. 每日三天提醒任务

```bash
# 每日上午9:00执行
curl -X POST "http://your-api-host/api/NormalWorkingReminder/ExecuteThreeDayReminder" \
  -H "Content-Type: application/json"
```

### 2. 每日六天提醒任务

```bash
# 每日上午9:05执行（建议在三天提醒之后）
curl -X POST "http://your-api-host/api/NormalWorkingReminder/ExecuteSixDayReminder" \
  -H "Content-Type: application/json"
```

## PowerShell脚本示例

```powershell
# 定时任务PowerShell脚本
$apiHost = "http://your-api-host"

# 执行三天提醒
try {
    $response1 = Invoke-RestMethod -Uri "$apiHost/api/NormalWorkingReminder/ExecuteThreeDayReminder" -Method POST
    Write-Host "三天提醒执行结果: $($response1.message)"
    if ($response1.data) {
        Write-Host "成功提醒: $($response1.data.successfulReminders), 失败: $($response1.data.failedReminders)"
    }
} catch {
    Write-Error "三天提醒执行失败: $($_.Exception.Message)"
}

# 等待5秒后执行六天提醒
Start-Sleep -Seconds 5

try {
    $response2 = Invoke-RestMethod -Uri "$apiHost/api/NormalWorkingReminder/ExecuteSixDayReminder" -Method POST
    Write-Host "六天提醒执行结果: $($response2.message)"
    if ($response2.data) {
        Write-Host "成功提醒: $($response2.data.successfulReminders), 失败: $($response2.data.failedReminders)"
    }
} catch {
    Write-Error "六天提醒执行失败: $($_.Exception.Message)"
}
```

## Python脚本示例

```python
import requests
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def execute_reminder(api_host, reminder_type):
    """执行提醒"""
    if reminder_type == 3:
        url = f"{api_host}/api/NormalWorkingReminder/ExecuteThreeDayReminder"
        type_name = "三天"
    elif reminder_type == 6:
        url = f"{api_host}/api/NormalWorkingReminder/ExecuteSixDayReminder"
        type_name = "六天"
    else:
        logger.error(f"不支持的提醒类型: {reminder_type}")
        return False
    
    try:
        response = requests.post(url, timeout=300)  # 5分钟超时
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"{type_name}提醒执行结果: {result.get('message', '未知')}")
        
        if result.get('data'):
            data = result['data']
            logger.info(f"成功提醒: {data.get('successfulReminders', 0)}, "
                       f"失败: {data.get('failedReminders', 0)}")
        
        return result.get('success', False)
    except requests.exceptions.RequestException as e:
        logger.error(f"{type_name}提醒执行失败: {str(e)}")
        return False

def main():
    """主函数"""
    api_host = "http://your-api-host"
    
    # 执行三天提醒
    logger.info("开始执行三天提醒...")
    success1 = execute_reminder(api_host, 3)
    
    # 等待5秒
    time.sleep(5)
    
    # 执行六天提醒
    logger.info("开始执行六天提醒...")
    success2 = execute_reminder(api_host, 6)
    
    # 总结
    if success1 and success2:
        logger.info("所有提醒任务执行成功")
    else:
        logger.warning("部分提醒任务执行失败")

if __name__ == "__main__":
    main()
```

## Windows任务计划程序配置

### 1. 创建基本任务
- 打开"任务计划程序"
- 点击"创建基本任务"
- 名称：常态工作控制提醒
- 描述：每日执行护理质控问题提醒

### 2. 触发器设置
- 触发器：每天
- 开始时间：上午9:00
- 重复间隔：不重复

### 3. 操作设置
- 操作：启动程序
- 程序/脚本：powershell.exe
- 参数：-File "C:\Scripts\NursingReminder.ps1"

## 响应处理示例

```javascript
// JavaScript/Node.js示例
const axios = require('axios');

async function executeReminders() {
    const apiHost = 'http://your-api-host';
    
    try {
        // 执行三天提醒
        console.log('执行三天提醒...');
        const response1 = await axios.post(`${apiHost}/api/NormalWorkingReminder/ExecuteThreeDayReminder`);
        
        if (response1.data.success) {
            console.log('三天提醒成功:', response1.data.message);
            if (response1.data.data) {
                console.log(`成功: ${response1.data.data.successfulReminders}, 失败: ${response1.data.data.failedReminders}`);
            }
        } else {
            console.error('三天提醒失败:', response1.data.message);
        }
        
        // 等待5秒
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 执行六天提醒
        console.log('执行六天提醒...');
        const response2 = await axios.post(`${apiHost}/api/NormalWorkingReminder/ExecuteSixDayReminder`);
        
        if (response2.data.success) {
            console.log('六天提醒成功:', response2.data.message);
            if (response2.data.data) {
                console.log(`成功: ${response2.data.data.successfulReminders}, 失败: ${response2.data.data.failedReminders}`);
            }
        } else {
            console.error('六天提醒失败:', response2.data.message);
        }
        
    } catch (error) {
        console.error('执行提醒时发生错误:', error.message);
    }
}

// 执行
executeReminders();
```

## 监控和日志

### 1. 日志监控
- 关注应用程序日志中的提醒执行记录
- 监控错误日志，及时处理异常情况

### 2. 性能监控
- 监控API响应时间
- 监控数据库查询性能
- 监控消息发送成功率

### 3. 告警设置
- 当提醒失败率超过阈值时发送告警
- 当API响应时间过长时发送告警
- 当连续多次执行失败时发送告警

## 注意事项

1. **网络超时**：建议设置较长的超时时间（5分钟以上）
2. **重试机制**：建议在外部脚本中实现重试逻辑
3. **错误处理**：妥善处理网络错误和API错误
4. **日志记录**：记录每次执行的结果，便于问题排查
5. **监控告警**：设置监控和告警，确保及时发现问题
