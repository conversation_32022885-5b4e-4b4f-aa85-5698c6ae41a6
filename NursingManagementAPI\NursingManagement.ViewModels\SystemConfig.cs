﻿using System.ComponentModel;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 配置参数()
    /// </summary>
    public class SystemConfig
    {

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 服务器类型，对应ApiSetting表中的SettingType
        /// </summary>
        public int ServerType { get; set; }
        /// <summary>
        /// 文件上传路径
        /// </summary>
        [Description("文件上传路径")]
        public string UploadDocumentPath { get; set; }
        /// <summary>
        /// 文件下载基路径
        /// </summary>
        [Description("文件下载基路径")]
        public string FileBaseUrl { get; set; }

        /// <summary>
        /// Redis链接
        /// </summary>
        public string RedisConnection { get; set; }
    }
}