﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 连接配置
        /// </summary>
        public DbSet<AppConfigSettingInfo> AppConfigSettingInfos { get; set; }
        /// <summary>
        /// API配置表
        /// </summary>
        public DbSet<APISettingInfo> APISettingInfos { get; set; }
        /// <summary>
        /// 医院列表
        /// </summary>
        public DbSet<HospitalListInfo> HospitalListInfos { get; set; }
        /// <summary>
        /// 政府编码字典表
        /// </summary>
        public DbSet<AdministrationDictionaryInfo> AdministrationDictionaryInfos { get; set; }
        /// <summary>
        /// 部门字典
        /// </summary>
        public DbSet<DepartmentListInfo> DepartmentListInfos { get; set; }
        /// <summary>
        /// 能级字典表
        /// </summary>
        public DbSet<CapabilityLevelInfo> CapabilityLevelInfos { get; set; }
        /// <summary>
        /// 注记图示表
        /// </summary>
        public DbSet<AdministrationIconInfo> AdministrationIconInfos { get; set; }
        /// <summary>
        /// 人事部门，护理管理部门对照表
        /// </summary>
        public DbSet<DepartmentVSDepartmentInfo> DepartmentVSDepartmentInfos { get; set; }
        /// <summary>
        /// 科室职务对照表
        /// </summary>
        public DbSet<DepartmentToJobInfo> DepartmentToJobInfos { get; set; }
        /// <summary>
        /// 人员职务对照表
        /// </summary>
        public DbSet<EmployeeToJobInfo> EmployeeToJobInfos { get; set; }
        /// <summary>
        /// 业务流水号记录表
        /// </summary>
        public DbSet<SerialNumberRecordsInfo> SerialNumberRecordsInfos { get; set; }
        /// <summary>
        /// 万年历
        /// </summary>
        public DbSet<PerpetualCalendarInfo> PerpetualCalendarInfos { get; set; }
        /// <summary>
        /// 前端画面使用组件列表
        /// </summary>
        public DbSet<ComponentListInfo> ComponentListInfos { get; set; }
        /// <summary>
        /// 组件属性表
        /// </summary>
        public DbSet<ComponentAttributeInfo> ComponentAttributeInfos { get; set; }
        /// <summary>
        /// 培训课程字典
        /// </summary>
        public DbSet<CourseSettingInfo> CourseSettingInfos { get; set; }
        /// <summary>
        /// 非国标配置字典
        /// </summary>
        public DbSet<SettingDictionaryInfo> SettingDictionaryInfos { get; set; }

    }
}
