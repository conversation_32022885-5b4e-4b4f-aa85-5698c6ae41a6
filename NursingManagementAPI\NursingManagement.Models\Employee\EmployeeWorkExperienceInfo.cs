﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员工作经历（非本公司）
    /// </summary>
    [Serializable]
    [Table("EmployeeWorkExperience")]
    public class EmployeeWorkExperienceInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeWorkExperienceID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 就职开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 就职结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 就职公司
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string Company { get; set; }

        /// <summary>
        /// 所在部门
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string Department { get; set; }

        /// <summary>
        /// 担任职务
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string Post { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string Remark { get; set; }
    }
}