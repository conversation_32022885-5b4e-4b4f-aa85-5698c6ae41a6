﻿namespace NursingManagement.ViewModels
{
    public class ApproveView
    {
        /// <summary>
        /// 节点名称
        /// </summary>
        public string ApproveNodeName { get; set; }
        /// <summary>
        /// 审批节点状态（1：同意、2：拒绝，空为未审批）
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 当前节点下的审批明细 | 或签、会签
        /// </summary>
        public List<ApproveDetailView> ApproveDetailViews { get; set; }
        /// <summary>
        /// 审批方式（【默认】1表示顺序签、2表示会签、3表示或签）
        /// </summary>
        public string ApproveModel { get; set; }
        /// <summary>
        /// 存储需要审批的文本内容
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 当前节点对应的业务主记录ID
        /// </summary>
        public string ApproveMainID { get; set; }
        /// <summary>
        /// 标记是头，发起者
        /// </summary>
        public bool HeadFlag { get; set; }
    }
}
