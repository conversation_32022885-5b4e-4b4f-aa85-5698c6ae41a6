﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员联系信息表
    /// </summary>
    [Serializable]
    [Table("EmployeeContact")]
    public class EmployeeContactInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeContactID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 联系方式(10:手机、20:邮箱、30:办公固话、40:手机短号)
        /// </summary>
        [Column(TypeName = "varchar50")]
        public string ContactWayCode { get; set; }

        /// <summary>
        /// 联系内容
        /// </summary>
        [Column(TypeName = "varchar(255)")]
        public string ContactContent { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        [Column(TypeName = "varchar(255)")]
        public string Remark { get; set; }
    }
}