﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class ProblemRectificationRepository : IProblemRectificationRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        public ProblemRectificationRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据质控问题MainID获取对应最近一条的整改记录
        /// </summary>
        /// <param name="hierarchicalQCMainIDs">质控Main表主键集合</param>
        /// <returns></returns>
        public async Task<List<ProblemRectificationView>> GetProblemRectificationByHierarchicalQCMainID(List<string> hierarchicalQCMainIDs)
        {
            var query = await _dbContext.ProblemRectificationInfos.Where(m => hierarchicalQCMainIDs.Contains(m.HierarchicalQCMainID)
                        && m.DeleteFlag != "*")
                        .Select(m => new ProblemRectificationView
                        {
                            HierarchicalQCMainID = m.HierarchicalQCMainID,
                            RectificationDateTime = m.RectificationDateTime,
                            EmployeeID = m.EmployeeID,
                            RectificationRemarks = m.RectificationRemarks,
                            ModifyDateTime = m.ModifyDateTime,
                        }).ToListAsync();
            return query.GroupBy(m=>m.HierarchicalQCMainID)
                        .Select(group=> group.OrderByDescending(n=>n.ModifyDateTime).First())
                        .ToList();
        }
        /// <summary>
        /// 根据质控问题MainID和员工编号获取对应的整改记录
        /// </summary>
        /// <param name="hierarchicalQCMainID">质控Main表主键</param>
        /// <param name="employeeID">员工编号</param>
        /// <returns></returns>
        public async Task<ProblemRectificationInfo> GetRecordByHierarchicalQCMainIDAndEmployeeID(string hierarchicalQCMainID, string employeeID)
        {
            return await _dbContext.ProblemRectificationInfos.FirstOrDefaultAsync(m => m.HierarchicalQCMainID == hierarchicalQCMainID
                        && m.EmployeeID == employeeID && m.DeleteFlag != "*");
        }
    }
}
