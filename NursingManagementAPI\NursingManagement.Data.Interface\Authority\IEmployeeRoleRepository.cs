﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeRoleRepository : ICacheRepository
    {
        /// <summary>
        /// 根据EmployeeID获取角色清单
        /// </summary>
        /// <returns></returns>
        Task<List<int>> GetRolesByEmployeeID(string employeeID);
        /// <summary>
        /// 根据EmployeeID获取相关角色配置，用于角色更新
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeRoleInfo>> GetEmployeeRoleInfoByEmployeeID(string employeeID);

        /// <summary>
        /// 获取病人所有的角色（包含删除的）
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeRoleInfo>> GetAllEmployeeRoleByEmployeeIDAsync(string employeeID);
    }
}
