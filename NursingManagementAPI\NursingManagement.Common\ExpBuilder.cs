﻿using System.Linq.Expressions;

namespace NursingManagement.Common
{
    public static class ExpBuilder
    {
        public static Expression<Func<T, bool>> True<T>()
        {
            return f => true;
        }

        public static Expression<Func<T, bool>> False<T>()
        {
            return f => false;
        }

        /// <summary>
        /// 组合表达式
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="first">原表达式</param>
        /// <param name="second">拼接表达式</param>
        /// <param name="merge">合并方式函数</param>
        /// <returns></returns>
        private static Expression<T> Compose<T>(this Expression<T> first, Expression<T> second,
            Func<Expression, Expression, Expression> merge)
        {
            // build parameter map (from parameters of second to parameters of first)  
            var map = first.Parameters.Select((f, i) => new { f, s = second.Parameters[i] })
                .ToDictionary(p => p.s, p => p.f);

            // replace parameters in the second lambda expression with parameters from the first  
            var secondBody = ParameterRebinder.ReplaceParameters(map, second.Body);

            // apply composition of lambda expression bodies to parameters from the first expression   
            return Expression.Lambda<T>(merge(first.Body, secondBody), first.Parameters);
        }

        /// <summary>
        /// 表达式且
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="first">原表达式</param>
        /// <param name="second">拼接表达式</param>
        /// <returns></returns>
        public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> first, Expression<Func<T, bool>> second)
        {
            return first.Compose(second, Expression.AndAlso);
        }
        /// <summary>
        /// 条件表达式且
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="first">原表达式</param>
        /// <param name="condition">条件</param>
        /// <param name="second">拼接表达式</param>
        /// <returns></returns>
        public static Expression<Func<T, bool>> IfAnd<T>(this Expression<Func<T, bool>> first, bool condition, Expression<Func<T, bool>> second)
        {
            return condition ? first.Compose(second, Expression.AndAlso) : first;
        }
        /// <summary>
        /// 表达式或
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="first">原表达式</param>
        /// <param name="second">拼接表达式</param>
        /// <returns></returns>
        public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> first, Expression<Func<T, bool>> second)
        {
            return first.Compose(second, Expression.OrElse);
        }
        /// <summary>
        /// 条件表达式或
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="first">原表达式</param>
        /// <param name="condition">条件</param>
        /// <param name="second">拼接表达式</param>
        /// <returns></returns>
        public static Expression<Func<T, bool>> IfOr<T>(this Expression<Func<T, bool>> first, bool condition, Expression<Func<T, bool>> second)
        {
            return condition ? first.Compose(second, Expression.OrElse) : first;
        }
    }
}