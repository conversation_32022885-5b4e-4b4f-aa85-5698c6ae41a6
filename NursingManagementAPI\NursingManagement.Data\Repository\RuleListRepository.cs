﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class RuleListRepository : IRuleListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public RuleListRepository(
            NursingManagementDbContext nursingManagementDbContext
            , IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="ruleLisIDs"></param>
        /// <returns></returns>
        public async Task<List<RuleListInfo>> GetListyID(List<int> ruleLisIDs)
        {
            var datas = await GetAllCacheAsync();
            return datas.Where(m => ruleLisIDs.Any(n => n == m.RuleListID)).ToList();
        }
        /// <summary>
        /// 根据类别获取数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<List<RuleListInfo>> GetListByType(string type)
        {
            var datas = await GetAllCacheAsync();
            return datas.Where(m => m.SystemType == type).ToList();
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<RuleListInfo>> GetAllCacheAsync()
        {
            return (List<RuleListInfo>)await GetCacheAsync();
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.RuleListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.RuleListInfo.GetKey(_sessionCommonServer);
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="ruleLisID"></param>
        /// <returns></returns>
        public async Task<RuleListInfo> GetRuleDataByID(int ruleLisID)
        {
            var datas = await GetAllCacheAsync();
            return datas.FirstOrDefault(m => m.RuleListID == ruleLisID);
        }
        /// <summary>
        /// 获取最大值（主键）
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxID()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var maxID = await _nursingManagementDbContext.RuleListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language)
                .MaxAsync(m => (int?)m.RuleListID);
            return maxID ?? 0;
        }
        /// <summary>
        /// 根据类别列表获取数据
        /// </summary>
        /// <param name="typeList">使用系统TypeList</param>
        /// <returns></returns>
        public async Task<List<RuleListInfo>> GetListByTypeList(List<string> typeList)
        {
            var datas = await GetAllCacheAsync();
            return datas.Where(m => typeList.Any(n => n == m.SystemType)).ToList();
        }
        /// <summary>
        /// 根据类别获取数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<List<RuleListInfo>> GetListByContainsType(string type)
        {
            var datas = await GetAllCacheAsync();
            return datas.Where(m => m.SystemType.Contains(type)).ToList();
        }
    }
}
