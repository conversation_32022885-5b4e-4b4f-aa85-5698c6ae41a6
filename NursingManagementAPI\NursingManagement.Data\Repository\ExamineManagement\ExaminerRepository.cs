﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.ViewModels.Examine;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository.Examine
{
    public class ExaminerRepository : IExaminerRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public ExaminerRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<List<ExaminerInfo>> GetListBySourceIDsAsync(string sourceType, List<string> sourceIDs)
        {
            return await _dbContext.ExaminerInfos.Where(x => x.SourceType == sourceType && sourceIDs.Contains(x.SourceID) && x.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据来源获取主考人信息
        /// </summary>
        /// <param name="sourceType">来源类型：ExaminationRecord、ExaminationMain</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        public async Task<List<ExaminerInfo>> GetListBySourceAsync(string sourceType, string sourceID)
        {
            return await _dbContext.ExaminerInfos
                .IfWhere(sourceType != null, x => x.SourceType == sourceType).
                Where(x => x.SourceID == sourceID && x.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据来源获取主考人信息
        /// </summary>
        /// <param name="sourceType">来源类型：ExaminationRecord、ExaminationMain</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        public async Task<ExaminerInfo> GetBySourceAsync(string sourceType, string sourceID)
        {
            return await _dbContext.ExaminerInfos
                .IfWhere(sourceType != null, x => x.SourceType == sourceType).
                Where(x => x.SourceID == sourceID && x.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据条件获取主考人列表
        /// </summary>
        /// <param name="predicate">>where 条件</param>
        /// <returns></returns>
        public async Task<List<ExaminerInfo>> GetListByConditionAsync(Expression<Func<ExaminerInfo, bool>> predicate)
        {
            return await _dbContext.ExaminerInfos
                .Where(x => x.DeleteFlag != "*")
                .Where(predicate)
                .ToListAsync();
        }

        /// <summary>
        /// 根据条件获取主考人列表（不跟踪）
        /// </summary>
        /// <param name="predicate">where 条件</param>
        /// <returns></returns>
        public async Task<List<ExaminerInfo>> GetListByConditionAsNoTrackAsync(Expression<Func<ExaminerInfo, bool>> predicate)
        {
            return await _dbContext.ExaminerInfos.Where(x => x.DeleteFlag != "*").Where(predicate)
                .Select(m => new ExaminerInfo
                {
                    EmployeeID = m.EmployeeID,
                    SourceID = m.SourceID,
                    SourceType = m.SourceType,
                }).ToListAsync();
        }
    }
}
