using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 内容明细字典表
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCAssessList")]
    public class HierarchicalQCAssessListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 质控表单明细内容序号
        /// </summary>
        public int HierarchicalQCAssessListID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 明细分类
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Type { get; set; }
        /// <summary>
        /// 质控表单明细内容
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string ContentName { get; set; }
    }
}