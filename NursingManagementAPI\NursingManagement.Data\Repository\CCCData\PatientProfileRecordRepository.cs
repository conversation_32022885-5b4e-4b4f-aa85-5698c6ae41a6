﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class PatientProfileRecordRepository : IPatientProfileRecordRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public PatientProfileRecordRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 根据sourceType和sourceID集合获取relatedTableRecordID集合
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceIDList"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, string>>> GetRelatedTableRecordIDList(string sourceType, List<string> sourceIDList, string relatedTableName)
        {
            return await (from a in _dbContext.PatientProfileRecordInfos
                          join b in _dbContext.HierarchicalQCRecordInfos on a.RelatedTableRecordID equals b.HierarchicalQCRecordID
                          join c in _dbContext.HierarchicalQCMainInfos on b.HierarchicalQCRecordID equals c.HierarchicalQCRecordID
                          where a.SourceType == sourceType && sourceIDList.Contains(a.SourceID) && a.RelatedTableName == relatedTableName && a.DeleteFlag != "*"
                          && b.DeleteFlag != "*" && c.DeleteFlag != "*"
                          orderby c.AssessDate, c.ModifyDateTime
                          select new Dictionary<string, string>()
                          {
                              {"sourceID",a.SourceID },
                              {"hierarchicalQCRecordID",b.HierarchicalQCRecordID},
                              {"hierarchicalQCSubjectID",b.HierarchicalQCSubjectID },
                              {"hierarchicalQCFormID",b.HierarchicalQCFormID.ToString() },
                              {"result",c.Result.ToString() }
                          }
                          ).ToListAsync();
        }

        /// <summary>
        /// 根据relatedTableName和relatedTableID获取数据
        /// </summary>
        /// <param name="relatedTableName"></param>
        /// <param name="relatedTableID"></param>
        /// <returns></returns>
        public async Task<PatientProfileRecordInfo> GetRecordByRelatedTableID(string relatedTableName, string relatedTableID)
        {
            return await _dbContext.PatientProfileRecordInfos.Where(m => m.RelatedTableName == relatedTableName && m.RelatedTableRecordID == relatedTableID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据sourceID获取患者状况
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="souceType"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileRecordInfo>> GetRecordDataBySourceID(string sourceID, string souceType, string relatedTableName)
        {
            return await _dbContext.PatientProfileRecordInfos.Where(m => m.SourceID == sourceID && m.SourceType == souceType && m.DeleteFlag != "*" && m.RelatedTableName == relatedTableName).ToListAsync();
        }

        /// <summary>
        /// 根据主键集合获取数据
        /// </summary>
        /// <param name="recordIDList"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileRecordInfo>> GetRecordDataByRecordIDList(List<string> recordIDList)
        {
            return await _dbContext.PatientProfileRecordInfos.Where(m => recordIDList.Contains(m.PatientProfileRecordID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据sourceID获取患者访视记录主键集合
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="souceType"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        public async Task<List<string>> GetRecordIDBySourceID(string sourceID, string souceType, string relatedTableName)
        {
            return await _dbContext.PatientProfileRecordInfos.Where(m => m.SourceID == sourceID && m.SourceType == souceType && m.DeleteFlag != "*" && m.RelatedTableName == relatedTableName).Select(m => m.PatientProfileRecordID).ToListAsync();
        }

        /// <summary>
        /// 根据sourceID获取患者状况
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="souceType"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        public async Task<List<SensitiveRecordView>> GetRecordDataBySourceIDAndType(List<string> sourceIDs, string souceType, string relatedTableName)
        {
            return await (from a in _dbContext.PatientProfileRecordInfos
                          join b in _dbContext.HierarchicalQCRecordInfos on a.RelatedTableRecordID equals b.HierarchicalQCRecordID
                          join c in _dbContext.HierarchicalQCMainInfos on b.HierarchicalQCRecordID equals c.HierarchicalQCRecordID
                          join d in _dbContext.HierarchicalQCDetailInfos on c.HierarchicalQCMainID equals d.HierarchicalQCMainID
                          where a.DeleteFlag != "*"
                                && b.DeleteFlag != "*"
                                && c.DeleteFlag != "*"
                                && d.DeleteFlag != "*"
                                && sourceIDs.Contains(a.SourceID)
                                && a.SourceType == souceType
                                && a.RelatedTableName == relatedTableName
                                && d.GroupID.HasValue
                                && d.ParentID.HasValue
                          select new SensitiveRecordView
                          {
                              PatientProfileRecordID = a.PatientProfileRecordID,
                              GroupID = d.GroupID.Value,
                              ParentID = d.ParentID.Value,
                              GuiDance = c.Guidance,
                              HierarchicalQCRecordID = b.HierarchicalQCRecordID,
                              HierarchicalQCMainID = c.HierarchicalQCMainID,
                              DepartmentID = c.DepartmentID,
                              AssessDate = c.AssessDate ?? c.AddDateTime,
                              AssessEmployID = c.ModifyEmployeeID,
                              HierarchicalQCAssessListID = d.HierarchicalQCAssessListID,
                              PatientName = a.PatientName,
                              BedNumber = a.BedNumber,
                              NurseEmployeeID = b.VerifierEmployeeID,
                              SupervisionRecordTime = a.AddDateTime,
                              SourceID = a.SourceID
                          }).ToListAsync();
        }

        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<PatientProfileRecordInfo> GetRecordDataByRecordIDAsync(string recordID)
        {
            return await _dbContext.PatientProfileRecordInfos.FirstOrDefaultAsync(m => recordID == m.PatientProfileRecordID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据时间范围和ProfileID获取记录数据
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="profileID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetRecordDataByTimeRangeAsync(DateTime startTime, DateTime endTime,int profileID)
        {
            return await _dbContext.PatientProfileRecordInfos.Where(m => m.ProfileID == profileID && m.DeleteFlag != "*" 
            && m.OccurDateTime.Date >= startTime && m.OccurDateTime.Date <= endTime).Select(m=>m.ChartNo).Distinct().ToListAsync();
        }

        /// <summary>
        /// 获取忽略记录数据
        /// </summary>
        /// <param name="chartNoList"></param>
        /// <returns></returns>
        public async Task<List<string>> GetIgnoreRecordDataByChartNo(List<string> chartNoList)
        {
            return await _dbContext.PatientProfileRecordInfos.Where(m => chartNoList.Contains(m.ChartNo) && m.RecordStatus == "*").Select(m => m.ChartNo).Distinct().ToListAsync();
        }
    }
}
