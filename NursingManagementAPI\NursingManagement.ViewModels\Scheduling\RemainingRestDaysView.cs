﻿namespace NursingManagement.ViewModels
{
    public class RemainingRestDaysView
    {
        /// <summary>
        /// 剩余休假天数表主键ID
        /// </summary>
        public int RemainingRestDaysID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// 休假可用天数
        /// </summary>
        public decimal? Days { get; set; }
    }
}
