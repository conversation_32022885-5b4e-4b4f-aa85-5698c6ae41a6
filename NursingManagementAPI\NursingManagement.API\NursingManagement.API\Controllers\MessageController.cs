using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 消息服务
    /// </summary>
    [Produces("application/json")]
    [Route("api/message")]
    [EnableCors("any")]
    public class MessageController : ControllerBase
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IMessageService _messageService;

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="messageService"></param>
        public MessageController(
            IMessageService messageService
        )
        {
            _messageService = messageService;
        }

        /// <summary>
        /// 测试消息，根据员工编号发送
        /// </summary>
        /// <param name="messageTool">消息分类，1:MQ；2:微信；3:钉钉；4:手机短信</param>
        /// <param name="messageType">消费端消息类型：1:Notification；2:Alert；3:Confirm；4:Message</param>
        /// <param name="employeeID">员工编号</param>
        /// <param name="message">消息内容</param>
        /// <param name="skipUrl">跳转url</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SendMassageByEmployeeID")]
        public async Task<IActionResult> SendMassageByEmployeeID(int messageTool = 1, int messageType = 1, string employeeID = "111111", string message = "护理管理系统测试消息",string skipUrl="")
        {
            var result = new ResponseResult();

            var messageToolAndType = _messageService.GetMessageToolAndMessageType(messageTool, messageType);
            var messageView = new MessageView()
            {
                MessageTools = new List<MessageTool>() { messageToolAndType.Item1 },
                EmployeeID = employeeID,
                MessageCondition = new MessageConditionView()
                {
                    Type = messageToolAndType.Item2,
                    Message = message,
                    SendMessageDateTime = DateTime.Now,
                    Url = string.IsNullOrEmpty(skipUrl) ? "http://localhost:6066/schedulingRequest": skipUrl
                }
            };
            result.Data = await _messageService.SendMessage(messageView);

            return result.ToJson();
        }

        /// <summary>
        /// 测试MQ消息，需要消息消费端监听路由键
        /// </summary>
        /// <param name="messageType">消息类型：1:Notification；2:Alert；3:Confirm；4:Message</param>
        /// <param name="exchangeName">路由键</param>
        /// <param name="routingKey">路由条件</param>
        /// <param name="message">消息内容</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SendMQMassageByExchangeName")]
        public async Task<IActionResult> SendMQMassageByExchangeName(int messageType = 1, string exchangeName = "MQNotification", string routingKey = "", string message = "")
        {
            var result = new ResponseResult();

            var messageToolAndType = _messageService.GetMessageToolAndMessageType(1, messageType);
            object newMessage = message;
            if (string.IsNullOrWhiteSpace(message))
            {
                newMessage = new Dictionary<string, string>() {
                        { "name","张三"},
                        { "age","18"}
                    };
            }
            var messageView = new MessageView()
            {
                MessageTools = new List<MessageTool>() { messageToolAndType.Item1 },
                MessageCondition = new MessageConditionView()
                {
                    Type = messageToolAndType.Item2,
                    MQExchangeName = exchangeName,
                    MQRoutingKey = routingKey,
                    Message = newMessage,
                    Url = "http://localhost:6066/schedulingRequest"
                }
            };
            result.Data = await _messageService.SendMessage(messageView);

            return result.ToJson();
        }
    }
}