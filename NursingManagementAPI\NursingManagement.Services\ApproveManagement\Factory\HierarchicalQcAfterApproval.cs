﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    public class HierarchicalQcAfterApproval(
        IHierarchicalQCMainRepository _hierarchicalQCMainRepository,
        IUnitOfWork _unitOfWork,
        IRouterListRepository _routerListRepository,
        ISettingDictionaryRepository _settingDictionaryRepository
        ) : ICommonProcessingAfterApproval
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();

        public override async Task<(string, int)[]> ProcessAfterApprovalAsync(ProcessAfterApprovalView view)
        {
            if (string.IsNullOrEmpty(view.ApprovalResult) || view.ApprovalResult == ApprovalStatus.InProgress.ToString("d"))
            {
                return null;
            }
            var qcMainInfo = await _hierarchicalQCMainRepository.GetDataByMainID(view.SourceID);
            if (qcMainInfo == null)
            {
                _logger.Error($"根据主键【{view.SourceID}】获取质控维护记录失败");
                return null;
            }
            //根据最终审批结果更新
            qcMainInfo.AuditStatus = view.ApprovalResult;
            qcMainInfo.AuditDateTime = DateTime.Now;
            if (await _unitOfWork.SaveChangesAsync() <= 0)
            {
                throw new Exception("更新质控信息失败，请联系管理员。");
            }
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ProveCategoryToRoute",
                SettingTypeValue = $"MG-073-{qcMainInfo.HierarchicalQCFormLevel}"
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var setting = settingList.FirstOrDefault();
            if(setting == null || !int.TryParse(setting.SettingValue, out var routerListID))
            {
                _logger.Error($"发送完成审批通知失败，找不到SettingType=ProveCategoryToRoute, SettingTypeCode=MG-073, Level={qcMainInfo.HierarchicalQCFormLevel}的配置");
                return null;
            }
            var routers = await _routerListRepository.GetInfosByRouterListID(routerListID);
            // 获取跳转 path，PC端、移动端
            var paths = routers.Select(r => (r.Path, r.ClientType)).ToArray();
            return paths;
        }
    }

}
