﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCRemarkRepository
    {
        /// <summary>
        /// 根据assesslistID获取对应的备注内容
        /// </summary>
        /// <param name="assessListID"></param>
        /// <returns><see cref="List{HierarchicalQCRemarkView}"/></returns>
        Task<List<HierarchicalQCRemarkView>> GetQCRemarkViewByAssessListIDAsync(int assessListID);
        /// <summary>
        /// 根据assesslistID数组集合获取对应的备注内容
        /// </summary>
        /// <param name="assessListIDs"></param>
        /// <returns><see cref="List{HierarchicalQCRemarkView}"/></returns>
        Task<List<HierarchicalQCRemarkView>> GetQCRemarkViewByAssessListIDsAsync(List<int> assessListIDs);
        /// <summary>
        /// 根据主键获取对应的备注内容
        /// </summary>
        /// <param name="remarkID">主键</param>
        /// <returns><see cref="HierarchicalQCRemarkInfo"/></returns>
        Task<HierarchicalQCRemarkInfo> GetQCRemarkViewAsync(string remarkID);
    }
}
