﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 消息记录视图模型
    /// </summary>
    public class MessageRecordView
    {
        /// <summary>
        /// 消息记录ID
        /// </summary>
        public string MessageRecordID { get; set; }

        /// <summary>
        /// 消息标题
        /// </summary>
        public string MessageTitle { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        public string MessageType { get; set; }

        /// <summary>
        /// 消息类型描述
        /// </summary>
        public string MessageTypeDescription { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string MessageContent { get; set; }

        /// <summary>
        /// 置顶
        /// </summary>
        public bool IsTop { get; set; }

        /// <summary>
        /// 置顶天数
        /// </summary>
        public int? TopDays { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public List<int> DepartmentIDs { get; set; }

        /// <summary>
        /// 状态：0-草稿，1-已发布
        /// </summary>
        public string MessageStatus { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 添加人员ID
        /// </summary>
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 添加人员
        /// </summary>
        public string AddEmployeeName { get; set; }

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddDateTime { get; set; }

        /// <summary>
        /// 修改人员ID
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
    }
}
