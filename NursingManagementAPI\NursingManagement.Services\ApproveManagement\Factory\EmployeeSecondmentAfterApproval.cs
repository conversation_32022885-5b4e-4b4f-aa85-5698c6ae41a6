﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Data.Interface.Employee;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class EmployeeSecondmentAfterApproval : ICommonProcessingAfterApproval
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeSecondmentRecordRepository _employeeSecondmentRecordRepository;

        public EmployeeSecondmentAfterApproval(
            IUnitOfWork unitOfWork
            , IEmployeeSecondmentRecordRepository employeeSecondmentRecordRepository)
        {
            _unitOfWork = unitOfWork;
            _employeeSecondmentRecordRepository = employeeSecondmentRecordRepository;
        }
        public override async Task<(string, int)[]> ProcessAfterApprovalAsync(ProcessAfterApprovalView view)
        {
            var employeeSecondmentRecord = await _employeeSecondmentRecordRepository.GetDataByID(view.SourceID);
            if (employeeSecondmentRecord == null)
            {
                _logger.Error("未找到人员借调审批记录来源，回写审批结果失败");
                return null;
            }
            if (employeeSecondmentRecord.StatusCode != view.ApprovalResult)
            {
                //当审批状态改变的时候 回写状态
                employeeSecondmentRecord.StatusCode = view.ApprovalResult;
            }
            if (await _unitOfWork.SaveChangesAsync() <= 0)
            {
                throw new Exception("更新人员借调信息失败，请联系管理员。");
            }
            // TODO: 待补充跳转路径
            return [];
        }

    }
}
