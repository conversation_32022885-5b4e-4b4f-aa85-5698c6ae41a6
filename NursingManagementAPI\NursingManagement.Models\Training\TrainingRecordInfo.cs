﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 培训记录表
    /// </summary>
    [Serializable]
    [Table("TrainingRecord")]
    public class TrainingRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 培训记录ID(主键ID)
        /// </summary>
        [Key]
        [Column("TrainingRecordID", TypeName = "varchar(32)")]
        public string TrainingRecordID { get; set; }
        /// <summary>
        /// 课程ID组
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string CourseSettingIDs { get; set; }
        /// <summary>
        /// 培训地点
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string TrainingLocation { get; set; }
        /// <summary>
        /// 培训方式
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string TrainingMethod { get; set; }
        /// <summary>
        /// 培训内容
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string TrainingContent { get; set; }
        /// <summary>
        /// 培训目标
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string TrainingTarget { get; set; }
        /// <summary>
        /// 培训讲师
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string TrainingLecturer { get; set; }
        /// <summary>
        /// 培训主持人
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string TrainingHost { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartDateTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDateTime { get; set; }
        /// <summary>
        /// 培训状态
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 考核主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationRecordID { get; set; }
        /// <summary>
        /// 培训评价问卷ID，关联评价问卷ID（DynamicFormRecord表）
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string EvaluationID { get; set; }
        /// <summary>
        /// 护士长评价问卷ID，关联评价问卷ID（DynamicFormRecord表）
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HeadNurseEvaluationID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = ("varchar(20)"))]
        public string HospitalID { get; set; }
        /// <summary>
        /// 文件ID集合
        /// </summary>
        [Column(TypeName = ("varchar(1000)"))]
        public string FileIDs { get; set; }
        /// <summary>
        /// 培训群组主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string TrainingClassMainID { get; set; }
        /// <summary>
        /// 是否需要签到标记
        /// </summary>
        public bool SignInFlag { get; set; }
        /// <summary>
        /// 二维码刷新时间（单位：秒），0不刷新
        /// </summary>
        public int QRCodeRefreshTime { get; set; }
    }
}
