﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels.HierarchicalQC;
using System.Reflection;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCSubjectRepository : IHierarchicalQCSubjectRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public HierarchicalQCSubjectRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<HierarchicalQCSubjectInfo> GetHierarchicalQCSubjectByID(string id)
        {
            return await _dbContext.HierarchicalQCSubjectInfos.Where(m => m.HierarchicalQCSubjectID == id && m.DeleteFlag != "*" && m.StatusCode == 1).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据条件获取质控主题集合
        /// </summary>
        /// <param name="level"></param>
        /// <param name="formID"></param>
        /// <param name="yearMonth"></param>
        /// <param name="addDeparmrntID"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCSubjectInfo>> GetHierarchicalQCSubjectData(string level, int? formID, string yearMonth, int? addDeparmrntID)
        {
            var yearAndMonth = GetYearAndMonth(yearMonth);
            var list = _dbContext.HierarchicalQCSubjectInfos.Where(m => m.HierarchicalQCFormLevel == level && m.DeleteFlag != "*" && m.StatusCode == 1);
            if (formID.HasValue)
            {
                list = list.Where(m => m.HierarchicalQCFormID == formID);
            }
            if (yearAndMonth.Item1.HasValue)
            {
                list = list.Where(m => m.Year == yearAndMonth.Item1);
            }
            if (yearAndMonth.Item2.HasValue)
            {
                list = list.Where(m => m.Month == yearAndMonth.Item2);
            }
            if (addDeparmrntID.HasValue)
            {
                list = list.Where(m => m.AddDepartmentID == addDeparmrntID.Value);
            }
            return await list.OrderBy(m => m.ModifyDateTime).ToListAsync();
        }
        /// <summary>
        /// 获取主题
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="switchDepartmentIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCSubjectInfo>> GetQCSubjectOptions(QCSubjectSearchView searchView, List<int> switchDepartmentIDs = null,bool? filterImplementationDateFlag = false)
        {
            var list =await _dbContext.HierarchicalQCSubjectInfos.Where(m => m.HierarchicalQCFormLevel == searchView.HierarchicalQCFormLevel && m.DeleteFlag != "*" && m.StatusCode == 1).ToListAsync();
            var yearAndMonth = GetYearAndMonth(searchView.YearMonth);
            var yearMonthrangeStart = GetYearAndMonth(searchView.YearMonthrangeStart);
            var yearMonthrangeEnd = GetYearAndMonth(searchView.YearMonthrangeEnd);
            //科室质控及人员质控区分开
            if (searchView.IsQcEmployeeFlag.HasValue && searchView.IsQcEmployeeFlag.Value)
            {
                list = list.Where(m => QCSubjectSearchView.QCEmployeeFormType.Contains(m.FormType)).ToList();
            }
            else
            {
                list = list.Where(m => QCSubjectSearchView.QCDepartmentFormType.Contains(m.FormType)).ToList();
            }
            if (!string.IsNullOrEmpty(searchView.FormType))
            {
                list = list.Where(m => m.FormType == searchView.FormType).ToList();
            }
            if (yearMonthrangeStart.Item1.HasValue && yearMonthrangeEnd.Item1.HasValue)
            {
                list = list.Where(m => m.Year >= yearMonthrangeStart.Item1 && m.Year <= yearMonthrangeEnd.Item1).ToList();
                if (yearMonthrangeStart.Item2.HasValue && yearMonthrangeEnd.Item2.HasValue)
                {
                    list = list.Where(m => m.Month >= yearMonthrangeStart.Item2 && m.Month <= yearMonthrangeEnd.Item2).ToList();
                }
            }
            else
            {
                list = list.IfWhere(yearAndMonth.Item1.HasValue, m => m.Year == yearAndMonth.Item1).ToList();
                list = list.IfWhere(yearAndMonth.Item2.HasValue, m => m.Month == yearAndMonth.Item2).ToList();
            }
            // 显示当前用户拥有权限部门的数据
            if (switchDepartmentIDs != null && switchDepartmentIDs.Count > 0)
            {
                list = list.Where(m => switchDepartmentIDs.Contains(m.AddDepartmentID)).ToList();
            }
            if (searchView.DepartmentID.HasValue)
            {
                list = list.Where(m => m.AddDepartmentID == searchView.DepartmentID.Value).ToList();
            }
            if (filterImplementationDateFlag.HasValue && filterImplementationDateFlag.Value)
            {
                list = list.Where(m => !m.ImplementationStartDate.HasValue || (m.ImplementationStartDate.HasValue && DateTime.Now >= m.ImplementationStartDate.Value)).ToList();
            }
            return  list.OrderByDescending(m => m.AddDateTime).ToList();
        }
        /// <summary>
        /// 年月转化为年 月
        /// </summary>
        /// <param name="yearMonth"></param>
        /// <returns></returns>
        private static Tuple<int?, int?> GetYearAndMonth(string yearMonth)
        {
            if (string.IsNullOrEmpty(yearMonth))
            {
                return new Tuple<int?, int?>(null, null);
            }
            var stringArr = yearMonth.Trim().Split("-");
            if (stringArr.Length > 1)
            {
                return new Tuple<int?, int?>(Convert.ToInt32(stringArr[0]), Convert.ToInt32(stringArr[1]));
            }
            return new Tuple<int?, int?>(null, null);
        }
        /// <summary>
        /// 根据templateCode获取数据（转换质控模板临时使用，线上转换完删除）
        /// </summary>
        /// <param name="formID"></param>
        /// <param name="formLevel"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCSubjectInfo>> GetQCSubjectByFormID(int formID, string formLevel)
        {
            return await _dbContext.HierarchicalQCSubjectInfos.Where(m => m.HierarchicalQCFormID == formID && m.HierarchicalQCFormLevel == formLevel && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 主题维护画面获取主题
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCSubjectInfo>>GetSubject(GetQCSubjectView view)
        {
            var list = await _dbContext.HierarchicalQCSubjectInfos.Where(m =>
            m.HospitalID==view.HospitalID 
            && m.HierarchicalQCFormLevel == view.QCLevel 
            && m.DeleteFlag != "*" 
            && m.StatusCode == 1
            && (!view.DepartmentID.HasValue || m.AddDepartmentID==view.DepartmentID)
            && (!view.FormID.HasValue || m.HierarchicalQCFormID == view.FormID)
            ).ToListAsync();
            var yearMonthrangeStart = GetYearAndMonth(view.StartYearMonth);
            var yearMonthrangeEnd = GetYearAndMonth(view.EndYearMonth);
            if (yearMonthrangeStart.Item1.HasValue && yearMonthrangeEnd.Item1.HasValue)
            {
                list = list.Where(m => m.Year > yearMonthrangeStart.Item1 ||
                           (m.Year == yearMonthrangeStart.Item1 && m.Month >= yearMonthrangeStart.Item2) &&
                           (m.Year < yearMonthrangeEnd.Item1 ||
                           (m.Year == yearMonthrangeEnd.Item1 && m.Month <= yearMonthrangeEnd.Item2)))
                .ToList();

            }
            if (view.QCType.HasValue)
            {
                var typeView = new QCFormTypeView();
                if (typeView.FormTypes.TryGetValue(view.QCType.Value, out var formTypeSet))
                {
                    list = list.Where(m => formTypeSet.Contains(m.FormType)).ToList();
                }
            }
            if (!string.IsNullOrEmpty(view.FormType))
            {
                list = list.Where(m => m.FormType==view.FormType).ToList();
            }
            if (!string.IsNullOrEmpty(view.HierarchicalQCSubjectID))
            {
                list = list.Where(m => m.HierarchicalQCSubjectID==view.HierarchicalQCSubjectID).ToList();
            }
            return list;
        }
    }
}