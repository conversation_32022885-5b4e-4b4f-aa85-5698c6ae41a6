﻿using NursingManagement.Models;

namespace NursingManagement.Services.Interface
{
    public interface IAuthorityCommonService
    {
        /// <summary>
        ///  根据角色获取有权限的菜单、路由清单
        /// </summary>
        /// <param name="roles"></param>
        /// <param name="clientType"></param>
        /// <returns></returns>
        Task<Tuple<List<MenuListInfo>, List<RouterListInfo>>> GetMenuAndRouterList(List<int> roles, int clientType);

        /// <summary>
        /// 重组路由地址，将数据库配置的路由参数拆分出来，根据isRouter重新组装地址
        /// </summary>
        /// <param name="routerPath"></param>
        /// <param name="isRouter">是否为路由，true：是路由，组装参数占位符；false:是菜单，组装参数值</param>
        /// <returns></returns>
        string AssemblePath(string routerPath, bool isRouter);
    }
}
