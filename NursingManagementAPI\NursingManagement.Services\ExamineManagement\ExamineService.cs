﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.DependencyInjection;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Models.Examine;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    public class ExamineService : IExamineService
    {
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IServiceProvider _serviceProvider;
        private readonly IMessageService _messageService;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IExaminationQuestionRepository _examinationQuestionRepository;
        private readonly IExaminationQuestionDetailRepository _examinationQuestionDetailRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IExaminationPaperMainRepository _examinationPaperMainRepository;
        private readonly IDynamicFormDetailRepository _dynamicFormDetailRepository;
        private readonly IExaminationRecordRepository _examinationRecordRepository;
        private readonly IConditionMainRepository _conditionMainRepository;
        private readonly IConditionDetaiRepository _conditionDetaiRepository;
        private readonly IConditionService _conditionService;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IExaminationMainRepository _examinationMainRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IExaminationDetailRepository _examinationDetailRepository;
        private readonly IRouterListRepository _routerListRepository;
        private readonly ISignUpRecordService _signUpRecordService;
        private readonly ISignUpRecordRepository _signUpRecordRepository;
        private readonly IExaminerRepository _examinerRepository;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IDynamicFormDetailAttributeRepository _dynamicFormDetailAttributeRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly IExaminationPaperTemplateService _examinationPaperTemplateService;
        private readonly IDynamicFormService _dynamicFormService;

        /// <summary>
        /// 员工状态，在职
        /// </summary>
        private static readonly int ON_JOB_STAFF_STATUS_CODE = 1;

        /// <summary>
        /// 考核记录待考核状态
        /// </summary>
        private static readonly string EXAMINATION_MAIN_STATUS_CODE_1 = "1";

        /// <summary>
        /// 刷题练习考核/试卷类型
        /// </summary>
        private static readonly string EXAMINE_PRACTICE_PAPER_TYPE = "4";

        /// <summary>
        /// 考核明细未作答状态
        /// </summary>
        private static readonly string EXAMINATION_DETAIL_NA_STATUSCODE = "1";

        /// <summary>
        /// 考核明细作答正确状态
        /// </summary>
        private static readonly string EXAMINATION_DETAIL_RIGHT_STATUSCODE = "2";

        /// <summary>
        /// 考核明细作答正确状态
        /// </summary>
        private static readonly string EXAMINATION_DETAIL_ERROR_STATUSCODE = "3";

        /// <summary>
        /// 实操类考核
        /// </summary>
        private static readonly string PAPER_TYPE_2 = "2";

        /// <summary>
        /// 跟考核记录表关联的表中SourceType字段 的值
        /// </summary>
        private static readonly string SOURCETYPE_EXAMINATION_RECORD = "ExaminationRecord";

        /// <summary>
        /// 跟考核主表关联的表中SourceType字段 的值
        /// </summary>
        private static readonly string SOURCETYPE_EXAMINATION_MAIN = "ExaminationMain";

        /// <summary>
        /// 试卷模板中的分组组件ID
        /// </summary>
        private const int COMPONENT_LIST_ID_GROUP = 109;

        /// <summary>
        /// 理论考核
        /// </summary>
        private static readonly string THEORY_EXAMINATION_TYPE = "1";

        /// <summary>
        /// 实操考核
        /// </summary>
        private static readonly string PRACTICAL_EXAMINATION_TYPE = "2";

        /// <summary>
        /// 预约状态
        /// </summary>
        private static readonly string STATUSCODE_APPOINTMENT = "7";

        /// <summary>
        /// 待考核状态
        /// </summary>
        private static readonly string STATUSCODE_EVALUATION_PENDING = "1";

        /// <summary>
        /// 考核中状态
        /// </summary>
        private static readonly string STATUSCODE_UNDER_ASSESSMENT = "3";

        /// <summary>
        /// 考评考试结束后的状态（包含结束）
        /// </summary>
        private static readonly List<string> STATUSCODE_COMPLETE_ASSESSMENT = ["4", "5", "6"];

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="serviceProvider"></param>
        /// <param name="messageService"></param>
        /// <param name="employeePersonalDataRepository"></param>
        /// <param name="examinationQuestionRepository"></param>
        /// <param name="examinationQuestionDetailRepository"></param>
        /// <param name="settingDictionaryRepository"></param>
        /// <param name="examinationPaperMainRepository"></param>
        /// <param name="dynamicFormDetailRepository"></param>
        /// <param name="examinationRecordRepository"></param>
        /// <param name="conditionMainRepository"></param>
        /// <param name="conditionDetaiRepository"></param>
        /// <param name="conditionService"></param>
        /// <param name="employeeStaffDataRepository"></param>
        /// <param name="examinationMainRepository"></param>
        /// <param name="departmentListRepository"></param>
        /// <param name="examinationDetailRepository"></param>
        /// <param name="routouterListRepository"></param>
        /// <param name="signUpRecordService"></param>
        /// <param name="signUpRecordRepository"></param>
        /// <param name="examinerRepository"></param>
        /// <param name="employeeDepartmentSwitchRepository"></param>
        /// <param name="dynamicFormDetailAttributeRepository"></param>
        /// <param name="capabilityLevelRepository"></param>
        /// <param name="examinationAppointmentRepository"></param>
        /// <param name="examinationPaperService"></param>
        /// <param name="dynamicFormService"></param>
        public ExamineService(
            IUnitOfWork unitOfWork
            , IServiceProvider serviceProvider
            , IMessageService messageService
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IExaminationQuestionRepository examinationQuestionRepository
            , IExaminationQuestionDetailRepository examinationQuestionDetailRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , IExaminationPaperMainRepository examinationPaperMainRepository
            , IDynamicFormDetailRepository dynamicFormDetailRepository
            , IExaminationRecordRepository examinationRecordRepository
            , IConditionMainRepository conditionMainRepository
            , IConditionDetaiRepository conditionDetaiRepository
            , IConditionService conditionService
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , IExaminationMainRepository examinationMainRepository
            , IDepartmentListRepository departmentListRepository
            , IExaminationDetailRepository examinationDetailRepository
            , IRouterListRepository routouterListRepository
            , ISignUpRecordService signUpRecordService
            , ISignUpRecordRepository signUpRecordRepository
            , IExaminerRepository examinerRepository
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IDynamicFormDetailAttributeRepository dynamicFormDetailAttributeRepository
            , ICapabilityLevelRepository capabilityLevelRepository
            , IExaminationPaperTemplateService examinationPaperTemplateService
            , IDynamicFormService dynamicFormService
        )
        {
            _unitOfWork = unitOfWork;
            _serviceProvider = serviceProvider;
            _messageService = messageService;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _examinationQuestionRepository = examinationQuestionRepository;
            _examinationQuestionDetailRepository = examinationQuestionDetailRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _examinationPaperMainRepository = examinationPaperMainRepository;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
            _examinationRecordRepository = examinationRecordRepository;
            _conditionMainRepository = conditionMainRepository;
            _conditionDetaiRepository = conditionDetaiRepository;
            _conditionService = conditionService;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _examinationMainRepository = examinationMainRepository;
            _departmentListRepository = departmentListRepository;
            _examinationDetailRepository = examinationDetailRepository;
            _routerListRepository = routouterListRepository;
            _signUpRecordService = signUpRecordService;
            _signUpRecordRepository = signUpRecordRepository;
            _examinerRepository = examinerRepository;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _dynamicFormDetailAttributeRepository = dynamicFormDetailAttributeRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
            _examinationPaperTemplateService = examinationPaperTemplateService;
            _dynamicFormService = dynamicFormService;
        }

        public async Task<string> DeleteExamineByPaperMainID(List<string> paperMainIDList, string employeeID)
        {
            var examintionRecordList = await _examinationRecordRepository.GetListByPaperMainIDs(paperMainIDList);
            if (examintionRecordList.Count <= 0)
            {
                return null;
            }
            var recordIDList = examintionRecordList.Select(m => m.ExaminationRecordID).ToList();
            foreach (var record in examintionRecordList)
            {
                record.Delete(employeeID);
            }
            var examintionMainList = await _examinationMainRepository.GetListByRecordIDList(recordIDList);
            if (examintionMainList.Count <= 0)
            {
                return null;
            }
            var startFlag = examintionMainList.Any(m => m.StartDateTime.HasValue);
            if (startFlag)
            {
                return "有相关联考核已开始";
            }
            foreach (var main in examintionMainList)
            {
                main.Delete(employeeID);
            }
            return null;
        }

        #region 获取考核计划列表

        public async Task<List<ExaminationRecordView>> GetExaminationRecordList(DateTime startDate, DateTime endDate, string type, string employeeID)
        {
            var switchDeptIDs = await _employeeDepartmentSwitchRepository.GetSwitchDepartmentIDsAsync(employeeID);
            var examinationRecordList = await _examinationRecordRepository.GetListByDate(startDate, endDate, type, switchDeptIDs);
            if (examinationRecordList.Count <= 0)
            {
                return [];
            }
            // 获取考核级别字典
            var examinationLevelParams = new SettingDictionaryParams { SettingType = "ExaminationManagement", SettingTypeCode = "ExaminationRecord", SettingTypeValue = "ExaminationLevel" };
            var examinationLevelList = await _settingDictionaryRepository.GetSettingDictionary(examinationLevelParams);
            // 获取人员工号
            var (examiners, employeeList) = await GetEmployeeList(examinationRecordList);
            var paperMainIDList = examinationRecordList.Select(m => m.ExaminationPaperMainID).ToList();
            var paperBankDict = await _examinationPaperMainRepository.GetBankIdDictByPaperIds(paperMainIDList);

            var departmentList = await _departmentListRepository.GetAllDictAsync();
            var examinationRecordIDList = examinationRecordList.Select(m => m.ExaminationRecordID).ToList();
            var conditionMainList = await _conditionMainRepository.GetListBySourceIDs(examinationRecordIDList, SOURCETYPE_EXAMINATION_RECORD);
            // 获取考核条件明细
            var conditionDetailList = await _conditionDetaiRepository.GetListByMainIDs(conditionMainList.Select(m => m.ConditionMainID).ToList());
            // 考核记录关联的主考人信息
            var returnView = new List<ExaminationRecordView>();
            foreach (var record in examinationRecordList)
            {
                paperBankDict.TryGetValue(record.ExaminationPaperMainID, out var bankID);
                var conditionMainData = conditionMainList.Find(m => m.SourceID == record.ExaminationRecordID);

                var view = AssemblyExaminationRecordView(examinationLevelList, departmentList, employeeList, record, bankID, examiners);
                view = SetExaminationConditions(view, conditionDetailList, conditionMainData);
                // TODO 需要显示练习进度
                if (type == EXAMINE_PRACTICE_PAPER_TYPE)
                {
                    view.PracticeProgress = "";
                }
                returnView.Add(view);
            }
            return [.. returnView.OrderByDescending(m => m.StartDateTime).ThenByDescending(m => m.ModifyDateTime)];
        }

        /// <summary>
        /// 获取使用到的用户信息
        /// </summary>
        /// <param name="examinationRecordList"></param>
        /// <returns></returns>
        private async Task<(List<ExaminerInfo>, List<EmployeePersonalDataListView>)> GetEmployeeList(List<ExaminationRecordInfo> examinationRecordList)
        {
            var employeeIDs = new List<string>();
            var addEmployeeIDs = examinationRecordList.Select(m => m.AddEmployeeID).Distinct().ToList();
            var modifyEmployeeIDs = examinationRecordList.Select(m => m.ModifyEmployeeID).Distinct().ToList();
            employeeIDs.AddRange(addEmployeeIDs);
            employeeIDs.AddRange(modifyEmployeeIDs);
            var reordIDList = examinationRecordList.Select(m => m.ExaminationRecordID).ToList();
            var (examiners, examinerIDs) = await GetExaminersBySourceTypeAndSourceID(SOURCETYPE_EXAMINATION_RECORD, reordIDList);
            employeeIDs.AddRange(examinerIDs);
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            return (examiners, employeeList);
        }

        /// <summary>
        /// 设置条件
        /// </summary>
        /// <param name="view"></param>
        /// <param name="conditionDetailList"></param>
        /// <param name="conditionMainData"></param>
        /// <returns></returns>
        private ExaminationRecordView SetExaminationConditions(ExaminationRecordView view, List<ConditionDetailInfo> conditionDetailList, ConditionMainInfo conditionMainData)
        {
            var conditions = conditionDetailList.Where(m => conditionMainData != null
                && m.ConditionMainID == conditionMainData.ConditionMainID && string.IsNullOrEmpty(m.ParentID))
               .OrderBy(m => m.Sort)
               .Select(m => new FormDetailConditionView()
               {
                   ItemID = m.ItemID.ToString(),
                   Condition = m.Condition,
                   Value = m.ConditionValue,
                   ConditionType = m.ConditionType,
                   Children = GenerateCascaderView(conditionDetailList, m.ConditionDetailID)
               }).ToList();
            view.Conditions = conditions;
            view.ConditionContent = conditionMainData?.ConditionContent;
            view.ConditionExpression = conditionMainData?.ConditionExpression;
            return view;
        }

        /// <summary>
        /// 组装ExaminationRecordView VModel对象
        /// </summary>
        /// <param name="examinationLevelList">考试级别字典</param>
        /// <param name="departmentList">部门信息</param>
        /// <param name="employeeList">人员信息</param>
        /// <param name="record">考核记录</param>
        /// <param name="bankID">练习考核对应的题库</param>
        /// <param name="examiners">所有记录的主考人信息</param>
        /// <returns>ExaminationRecordView对象</returns>
        private static ExaminationRecordView AssemblyExaminationRecordView(List<SettingDictionaryInfo> examinationLevelList, List<DepartmentListInfo> departmentList, List<EmployeePersonalDataListView> employeeList, ExaminationRecordInfo record, string bankID, List<ExaminerInfo> examiners)
        {
            var examinerIDs = examiners.Where(m => m.SourceID == record.ExaminationRecordID).Select(m => m.EmployeeID).ToList();
            var examinerNames = employeeList.Where(m => examinerIDs.Contains(m.EmployeeID)).Select(m => m.EmployeeName).Distinct().ToList();
            return new ExaminationRecordView
            {
                ExaminationRecordID = record.ExaminationRecordID,
                ExaminationPaperMainID = record.ExaminationPaperMainID,
                StartDateTime = record.StartDateTime,
                EndDateTime = record.EndDateTime,
                Duration = record.Duration,
                DurationName = $"{(int)record.Duration}分钟",
                Instructions = record.Instructions,
                Type = record.Type,
                AddDateTime = record.AddDateTime,
                AddEmployeeName = employeeList.FirstOrDefault(m => m.EmployeeID == record.AddEmployeeID)?.EmployeeName,
                ModifyDateTime = record.ModifyDateTime,
                ModifyEmployeeName = employeeList.FirstOrDefault(m => m.EmployeeID == record.ModifyEmployeeID)?.EmployeeName,
                ExaminationName = record.ExaminationName,
                ExamineEmployeeID = examinerIDs,
                ExamineEmployeeName = examinerNames.Count > 0 ? examinerNames.Aggregate((nameA, nameB) => $"{nameA}、{nameB}") : "",
                TotalScore = record.TotalScore,
                PassingScore = record.PassingScore,
                MinAnswerTime = record.MinAnswerTime,
                DepartmentID = record.DepartmentID,
                DepartmentName = record.DepartmentID.HasValue ? departmentList.Find(m => m.DepartmentID == record.DepartmentID)?.LocalShowName : "",
                QRCodeRefreshTime = record.QRCodeRefreshTime,
                SignInFlag = record.SignInFlag,
                PublishFlag = record.PublishFlag,
                QuestionBankID = bankID,
                ExaminationLevel = record.ExaminationLevel,
                ExaminationLevelName = examinationLevelList.FirstOrDefault(m => m.SettingValue == record.ExaminationLevel)?.Description,
                OnePageQuestionFlag = record.OnePageQuestionFlag
            };
        }

        /// <summary>
        /// 获取子条件
        /// </summary>
        /// <param name="conditionDetailList"></param>
        /// <param name="parentID"></param>
        /// <returns></returns>
        private List<FormDetailConditionView> GenerateCascaderView(List<ConditionDetailInfo> conditionDetailList, string parentID)
        {
            var views = new List<FormDetailConditionView>();
            var detailList = conditionDetailList.Where(m => m.ParentID == parentID).ToList();
            foreach (var detail in detailList)
            {
                var view = new FormDetailConditionView
                {
                    ItemID = detail.ItemID.ToString(),
                    Condition = detail.Condition,
                    Value = detail.ConditionValue,
                    ConditionType = detail.ConditionType,
                    Children = GenerateCascaderView(conditionDetailList, detail.ConditionDetailID)
                };
                views.Add(view);
            }
            return views;
        }

        #endregion

        public async Task<bool> DeleteExaminationRecordData(string recordID, string employeeID)
        {
            var examintionRecord = await _examinationRecordRepository.GetDataByID(recordID);
            if (examintionRecord == null)
            {
                return false;
            }
            examintionRecord.Delete(employeeID);
            var examineMainList = await _examinationMainRepository.GetListByRecordID(examintionRecord.ExaminationRecordID);
            if (examineMainList.Count > 0)
            {
                foreach (var main in examineMainList)
                {
                    main.Delete(employeeID);
                }
            }
            var conditionMain = await _conditionMainRepository.GetDataBySourceID(examintionRecord.ExaminationRecordID, "ExaminationRecord");
            if (conditionMain == null)
            {
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            var conditionDetailList = await _conditionDetaiRepository.GetListByMainID(conditionMain.ConditionMainID);
            if (conditionDetailList.Count > 0)
            {
                foreach (var conditionDetail in conditionDetailList)
                {
                    conditionDetail.Delete(employeeID);
                }
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 保存考核主记录数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> SaveExaminationRecordData(ExaminationRecordView view)
        {
            var examintionPaperMain = new ExaminationPaperMainInfo();
            if (!string.IsNullOrWhiteSpace(view.ExaminationPaperMainID))
            {
                examintionPaperMain = await _examinationPaperMainRepository.GetDataByID(view.ExaminationPaperMainID);
            }
            var examinationRecordID = view.ExaminationRecordID ?? examintionPaperMain.GetId();
            // 保存试卷规则
            await SavePublishExamRule(view, examinationRecordID);
            //动态组卷，前端设置及格分数
            var passingScore = examintionPaperMain?.PassingScore;
            var totalScore = examintionPaperMain != null && examintionPaperMain.TotalScore != 0 ? examintionPaperMain.TotalScore : view.TotalScore;
            var successFlag = false;
            if (string.IsNullOrEmpty(view.ExaminationRecordID))
            {
                successFlag = await AddExaminationRecordAndExaminer(view, totalScore, examinationRecordID, passingScore);
            }
            else
            {
                var recordData = await _examinationRecordRepository.GetDataByID(view.ExaminationRecordID);
                if (recordData == null)
                {
                    _logger.Error($"获取考核记录失败，ExaminationRecordID=【{view.ExaminationRecordID}】");
                    return false;
                }
                successFlag = await UpdateExamineRecordAndExaminer(view, passingScore, totalScore, recordData);
            }
            // 试卷发布
            if (successFlag && view.PublishFlag)
            {
                view.ExaminationRecordID = examinationRecordID;
                await PublishExamine(view);
            }
            return successFlag;
        }

        /// <summary>
        /// 新增考核记录
        /// </summary>
        /// <param name="view"></param>
        /// <param name="totalScore">试卷总分</param>
        /// <param name="examinationRecordID">新增考核记录的ID</param>
        /// <param name="passingScore">及格分数</param>
        /// <returns></returns>
        private async Task<bool> AddExaminationRecordAndExaminer(ExaminationRecordView view, decimal totalScore, string examinationRecordID, decimal? passingScore)
        {
            var recordInfo = new ExaminationRecordInfo
            {
                ExaminationRecordID = examinationRecordID,
                ExaminationPaperMainID = view.ExaminationPaperMainID,
                StartDateTime = view.StartDateTime,
                EndDateTime = view.EndDateTime,
                Duration = view.Duration,
                Instructions = view.Instructions,
                Type = view.Type,
                TotalScore = totalScore,
                ExaminationName = view.ExaminationName,
                DeleteFlag = "",
                PassingScore = passingScore,
                MinAnswerTime = view.MinAnswerTime,
                OrganizationalDepartmentCode = "",
                QRCodeRefreshTime = view.SignInFlag ? view.QRCodeRefreshTime : 0,
                SignInFlag = view.SignInFlag,
                PublishFlag = view.PublishFlag,
                DepartmentID = view.DepartmentID,
                ExaminationLevel = view.ExaminationLevel,
                OnePageQuestionFlag = view.OnePageQuestionFlag
            };
            recordInfo.Add(view.EmployeeID).Modify(view.EmployeeID);
            await _unitOfWork.GetRepository<ExaminationRecordInfo>().InsertAsync(recordInfo);
            var addExaminers = new List<ExaminerInfo>();
            foreach (var employeeID in view.ExamineEmployeeID)
            {
                var examinerInfo = new ExaminerInfo
                {
                    EmployeeID = employeeID,
                    SourceID = examinationRecordID,
                    SourceType = "ExaminationRecord"
                };
                examinerInfo.Add(view.EmployeeID).Modify(view.EmployeeID);
                addExaminers.Add(examinerInfo);
            }
            await _unitOfWork.GetRepository<ExaminerInfo>().InsertAsync(addExaminers);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 保存发布试卷的筛选规则 （调用方法中统一保存）
        /// </summary>
        /// <param name="view"></param>
        /// <param name="examinationRecordID">规则关联的考核计划ID</param>
        /// <returns></returns>
        private async Task SavePublishExamRule(ExaminationRecordView view, string examinationRecordID)
        {
            // 查询条件表数据，组装写入条件表view
            var existFlag = view.ExaminationRecordID != null && await _conditionMainRepository.ExistBySource(view.ExaminationRecordID, "ExaminationRecord");
            var conditionView = new HandleConditionView
            {
                ConditionContent = view.ConditionContent,
                ConditionExpression = view.ConditionExpression,
                Conditions = view.Conditions,
                SourceID = examinationRecordID,
                SourceType = "ExaminationRecord",
                ModifyEmployeeID = view.EmployeeID,
                AddFlag = !existFlag
            };
            //保存组卷规则
            await _conditionService.HandleConditionData(conditionView, false);
        }

        /// <summary>
        /// 修改试卷记录和主考人信息
        /// </summary>
        /// <param name="view"></param>
        /// <param name="passingScore"></param>
        /// <param name="totalScore"></param>
        /// <param name="recordData"></param>
        /// <returns></returns>
        private async Task<bool> UpdateExamineRecordAndExaminer(ExaminationRecordView view, decimal? passingScore, decimal totalScore, ExaminationRecordInfo recordData)
        {
            recordData.ExaminationPaperMainID = view.ExaminationPaperMainID;
            recordData.StartDateTime = view.StartDateTime;
            recordData.EndDateTime = view.EndDateTime;
            recordData.Duration = view.Duration;
            recordData.Instructions = view.Instructions;
            recordData.Type = view.Type;
            recordData.TotalScore = totalScore;
            recordData.ExaminationName = view.ExaminationName;
            recordData.PassingScore = passingScore;
            recordData.MinAnswerTime = view.MinAnswerTime;
            recordData.OrganizationalDepartmentCode = "";
            recordData.QRCodeRefreshTime = view.SignInFlag ? view.QRCodeRefreshTime : 0;
            recordData.SignInFlag = view.SignInFlag;
            recordData.PublishFlag = view.PublishFlag;
            recordData.DepartmentID = view.DepartmentID;
            recordData.ExaminationLevel = view.ExaminationLevel;
            recordData.OnePageQuestionFlag = view.OnePageQuestionFlag;
            recordData.Modify(view.EmployeeID);
            var examinerInfos = await _examinerRepository.GetListBySourceAsync(SOURCETYPE_EXAMINATION_RECORD, recordData.ExaminationRecordID);
            examinerInfos?.ForEach(m => m.Delete(view.EmployeeID));
            List<ExaminerInfo> addExaminers = [];
            foreach (var employeeID in view.ExamineEmployeeID)
            {
                var examinerInfo = new ExaminerInfo
                {
                    EmployeeID = employeeID,
                    SourceID = recordData.ExaminationRecordID,
                    SourceType = "ExaminationRecord"
                };
                examinerInfo.Add(view.EmployeeID).Modify(view.EmployeeID);
                addExaminers.Add(examinerInfo);
            }
            await _unitOfWork.GetRepository<ExaminerInfo>().InsertAsync(addExaminers);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #region 发布考核

        /// <summary>
        /// 发布考核
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> PublishExamine(ExaminationRecordView view)
        {
            if (view == null || view.ExamineEmployeeID == null || view.ExamineEmployeeID.Count <= 0)
            {
                return false;
            }
            var examineRecord = await _examinationRecordRepository.GetDataByID(view.ExaminationRecordID);
            if (examineRecord == null)
            {
                return false;
            }
            examineRecord.PublishFlag = true;
            examineRecord.Modify(view.EmployeeID);
            var parcticePageTypeSettingss = await _settingDictionaryRepository.GetSettingDictionary(
                new SettingDictionaryParams
                {
                    SettingType = "ExaminationManagement",
                    SettingTypeCode = "ExaminationType",
                    SettingTypeValue = "Practice"
                });
            var practicePageTypes = parcticePageTypeSettingss.Select(m => m.SettingValue).ToList();
            // 试卷考核和操作考核根据条件去筛选参加考核人员，个人练习根据选择的人员写入数据
            var examineEmployeeList = practicePageTypes.Contains(view.Type)
            ? [await _employeeStaffDataRepository.GetEmployeeStaffDataByID(view.ExamineEmployeeID.First())]
            : await GetExamineMainEmployeeList(view);
            if (examineEmployeeList.Count <= 0)
            {
                return false;
            }
            var (toAddIDs, toDeleteIDs) = await HandleExamineMainData(examineRecord, examineEmployeeList, view.EmployeeID);
            if (toAddIDs.Count > 0)
            {
                //发送至移动端 clientType为3
                var messageViewList = await GetPublishExamineEmployeeAndContentAsync(toAddIDs, examineRecord, (int)ClientType.Mobile, 76);
                foreach (var messageView in messageViewList)
                {
                    await _messageService.SendMessage(messageView);
                }
                foreach (var employeeID in view.ExamineEmployeeID)
                {
                    // 通知主考人已经成功发布考核
                    var examinerMessageView = await GetNotificationToExaminerAsync(examineRecord, employeeID, 76);
                    await _messageService.SendMessage(examinerMessageView);
                }
                return true;
            }
            return true;
        }

        /// <summary>
        /// 获取对主考人发送的消息推送view
        /// </summary>
        /// <param name="examinationRecord"></param>
        /// <param name="examiner"></param>
        /// <param name="routerListID"></param>
        /// <param name="clientType"></param>
        /// <returns></returns>
        public async Task<MessageView> GetNotificationToExaminerAsync(ExaminationRecordInfo examinationRecord, string examiner, int routerListID)
        {
            var router = await _routerListRepository.GetDataByRouterListID(routerListID);
            var messageView = new List<MessageView>();

            var view = new MessageView
            {
                MessageTools = [MessageTool.Wechat],
                EmployeeID = examiner,
                ClientType = (int)ClientType.Mobile,
                MessageCondition = new MessageConditionView
                {
                    // 移动端与PC端使用不同的交换机
                    MQExchangeName = "MQNotification",
                    MQRoutingKey = examiner,
                    Message = $"您已成功发布考核，考核题目：{examinationRecord.ExaminationName}；考核开始时间：{examinationRecord.StartDateTime:yyyy:MM:dd HH:mm}，" +
                    $"结束时间：{examinationRecord.EndDateTime:yyyy:MM:dd HH:mm}；",
                    Url = router?.Path,
                    ClientType = (int)ClientType.Mobile,
                }
            };

            return view;
        }

        /// <summary>
        /// 获取写入考核主表人员数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        private async Task<List<EmployeeStaffDataInfo>> GetExamineMainEmployeeList(ExaminationRecordView view)
        {
            // 查询条件表数据，组装写入条件表view
            var conditionMain = await _conditionMainRepository.GetDataBySourceID(view.ExaminationRecordID, "ExaminationRecord");
            if (conditionMain == null)
            {
                return [];
            }
            // 获取人员和写入的条件明细数据
            var emloyeeList = await _employeeStaffDataRepository.GetOnJobEmployeeStaffDataView();
            var conditionDetailList = await _conditionDetaiRepository.GetListByMainID(conditionMain.ConditionMainID);
            var oneLevelConditionDetailList = conditionDetailList.Where(m => string.IsNullOrEmpty(m.ParentID)).ToList();
            // 根据前端输入条件筛选人员数据
            var examineEmployeeList = await _conditionService.FilterConditionDetailList(oneLevelConditionDetailList, emloyeeList, conditionDetailList);

            return examineEmployeeList;
        }

        /// <summary>
        /// 写入人员考核记录数据
        /// </summary>
        /// <param name="examinationRecord"></param>
        /// <param name="employeeList"></param>
        /// <param name="employeeID">操作人</param>
        /// <returns></returns>
        private async Task<(List<string>, List<string>)> HandleExamineMainData(ExaminationRecordInfo examinationRecord, List<EmployeeStaffDataInfo> employeeList, string employeeID)
        {
            // 获取现有考试主数据
            var existingMains = await _examinationMainRepository.GetListByRecordID(examinationRecord.ExaminationRecordID);

            // 获取试卷ID
            var paperID = "";
            if (!string.IsNullOrEmpty(examinationRecord.ExaminationPaperMainID))
            {
                var paperMainData = await _examinationPaperMainRepository.GetDataByID(examinationRecord.ExaminationPaperMainID);
                paperID = paperMainData?.PaperID ?? "";
            }
            // 处理需要删除的记录(不在employeeList中且状态为"1"或"7")
            var employeeIds = employeeList.Select(e => e.EmployeeID).ToList();
            var toDelete = existingMains.Where(m => !employeeIds.Contains(m.EmployeeID) && (m.StatusCode == STATUSCODE_APPOINTMENT || m.StatusCode == STATUSCODE_EVALUATION_PENDING)).ToList();
            var toDeleteIDs = new List<string>();
            foreach (var mainItem in toDelete)
            {
                mainItem.Delete(employeeID);
                toDeleteIDs.Add(mainItem.EmployeeID);
            }
            // 处理需要新增的记录(employeeList中有但数据库中不存在)
            var existingEmployeeIds = existingMains.Select(m => m.EmployeeID).ToList();
            var toAddEmployees = employeeList.Where(e => !existingEmployeeIds.Contains(e.EmployeeID)).ToList();
            var toAddIDs = new List<string>();
            foreach (var employee in toAddEmployees)
            {
                toAddIDs.Add(employee.EmployeeID);
                var mainData = new ExaminationMainInfo()
                {
                    ExaminationMainID = Guid.NewGuid().ToString("N"),
                    ExaminationPaperMainID = examinationRecord.ExaminationPaperMainID ?? "",
                    ExaminationRecordID = examinationRecord.ExaminationRecordID,
                    DepartmentID = employee.DepartmentID ?? 0,
                    EmployeeID = employee.EmployeeID,
                    StatusCode = examinationRecord.Type == PRACTICAL_EXAMINATION_TYPE ? STATUSCODE_APPOINTMENT : STATUSCODE_EVALUATION_PENDING,
                    RetakeFlag = false,
                    PaperID = paperID,
                    DeleteFlag = ""
                };
                mainData.Add(employeeID);
                mainData.Modify(employeeID);
                await _unitOfWork.GetRepository<ExaminationMainInfo>().InsertAsync(mainData);
            }
            await _unitOfWork.SaveChangesAsync();
            return (toAddIDs, toDeleteIDs);
        }

        /// <summary>
        /// 创建发送消息视图
        /// </summary>
        /// <param name="employeeIDs">发布考核的人员</param>
        /// <param name="examinationRecord">考核记录数据</param>
        /// <param name="clientType">客户端类型</param>
        /// <param name="routerListID">路由ID</param>
        /// <returns></returns>
        private async ValueTask<List<MessageView>> GetPublishExamineEmployeeAndContentAsync(List<string> employeeIDs, ExaminationRecordInfo examinationRecord, int clientType, int routerListID)
        {
            var router = await _routerListRepository.GetDataByRouterListID(routerListID);
            var messageView = new List<MessageView>();
            foreach (var employeeID in employeeIDs)
            {
                var view = new MessageView
                {
                    MessageTools = [MessageTool.Wechat],
                    EmployeeID = employeeID,
                    ClientType = clientType,
                    MessageCondition = new MessageConditionView
                    {
                        // 移动端与PC端使用不同的交换机
                        MQExchangeName = "MQNotification",
                        MQRoutingKey = employeeID,
                        Message = $"您有新的考核，考核开始时间：{examinationRecord.StartDateTime.ToString("yyyy-MM-dd HH:mm")}，" +
                        $"结束时间：{examinationRecord.EndDateTime.ToString("yyyy-MM-dd HH:mm")}；考核内容：{examinationRecord.ExaminationName}；请您准时参加",
                        Url = router?.Path,
                        ClientType = clientType,
                    }
                };
                messageView.Add(view);
            }
            return messageView;
        }

        #endregion

        /// <summary>
        /// 获取考核主表数据
        /// </summary>
        /// <param name="examineEmployeeID">主考人工号</param>
        /// <param name="examineRecordID">考核记录工号</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="examinationType">考核类型</param>
        /// <returns></returns>
        public async Task<List<ExaminationMainView>> GetExamineMainList(string examineEmployeeID, string examineRecordID, DateTime startDate, DateTime endDate, string examinationType)
        {
            var examineMainList = await FilterExaminationMainByCondition(examineEmployeeID, examineRecordID, startDate, endDate);
            if (examineMainList.Count <= 0)
            {
                return [];
            }
            var examinationRecordIDList = examineMainList.Select(m => m.ExaminationRecordID).ToList();
            var examinationRecordList = await _examinationRecordRepository.GetListByIDs(examinationRecordIDList);
            // 按照考核类型筛选考核记录
            examineMainList = FilterMainByExaminationType(examineMainList, examinationRecordList, examinationType);
            if (examineMainList.Count == 0)
            {
                return [];
            }
            var departmentList = await _departmentListRepository.GetByOrganizationType("1");
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationMain",
                SettingTypeValue = "StatusCode"
            };
            var statusSettings = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            settingParams = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationRecord",
                SettingTypeValue = "ExaminationLevel"
            };
            var examinationLevelSettings = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            // 通过 examineEmployeeID 获取相关员工信息
            var employeeIDs = examineMainList.SelectMany(m => new[] { m.EmployeeID, m.ModifyEmployeeID, m.AddEmployeeID }).Distinct().ToList();
            // 获取所有相关的 ExamMainID 和 RecordID
            var examinationMainIDList = examineMainList.Select(m => m.ExaminationMainID).ToList();
            // 获取相关主考人信息
            var recordIDs = examinationRecordList.Where(m => m.Type != PRACTICAL_EXAMINATION_TYPE).Select(m => m.ExaminationRecordID).ToList();
            var (examiners, examinerIDs) = await GetExaminersBySourceTypeAndSourceID(SOURCETYPE_EXAMINATION_RECORD, recordIDs);
            var (praticalExaminers, praticalExaminerIDs) = await GetExaminersBySourceTypeAndSourceID(SOURCETYPE_EXAMINATION_MAIN, examinationMainIDList);
            examiners.AddRange(praticalExaminers);
            var unionEmployeeIDs = employeeIDs.Concat(examinerIDs).Concat(praticalExaminerIDs).ToList();
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(unionEmployeeIDs);
            var employeeStaffList = await _employeeStaffDataRepository.GetEmployeeListByDeptIDOrEmployeeIDs(ON_JOB_STAFF_STATUS_CODE, null, [.. employeeIDs]);
            var capabilityLevelList = await _capabilityLevelRepository.GetByCacheAsync();
            return CreateMangeExaminationMainView(examineMainList, departmentList, examiners, employeeList, employeeStaffList, statusSettings, examinationRecordList, capabilityLevelList, examinationLevelSettings);
        }

        /// <summary>
        /// 按照类型筛选
        /// </summary>
        /// <param name="examineMainList"></param>
        /// <param name="examinationRecordList"></param>
        /// <param name="examinationType"></param>
        /// <returns></returns>
        private List<ExaminationMainInfo> FilterMainByExaminationType(List<ExaminationMainInfo> examineMainList, List<ExaminationRecordInfo> examinationRecordList, string examinationType)
        {
            if (!string.IsNullOrEmpty(examinationType))
            {
                var recordIDs = examinationRecordList.Where(m => m.Type == examinationType).Select(m => m.ExaminationRecordID);
                return examineMainList.Where(m => recordIDs.Contains(m.ExaminationRecordID)).ToList();
            }
            return examineMainList;
        }

        /// <summary>
        /// 转换管理端考核主记录VIew
        /// </summary>
        /// <param name="examineMainList">考核主记录数据</param>
        /// <param name="departmentList">部门数据</param>
        /// <param name="examiners">考核人</param>
        /// <param name="employeeList">人员基本信息字典</param>
        /// <param name="employeeStaffList">人员在职数据</param>
        /// <param name="statusSettings">考核状态配置</param>
        /// <param name="examinationRecordList">考核主表(考核计划数据)</param>
        /// <param name="capabilityLevelList">层级配置字典</param>
        /// <param name="examinationLevelSettings">考核级别配置字典</param>
        /// <returns></returns>
        private List<ExaminationMainView> CreateMangeExaminationMainView(List<ExaminationMainInfo> examineMainList, List<DepartmentListInfo> departmentList, List<ExaminerInfo> examiners,
                List<EmployeePersonalDataListView> employeeList, List<EmployeeStaffDataInfo> employeeStaffList, List<SettingDictionaryInfo> statusSettings,
                List<ExaminationRecordInfo> examinationRecordList, List<CapabilityLevelInfo> capabilityLevelList, List<SettingDictionaryInfo> examinationLevelSettings)
        {
            var returnView = new List<ExaminationMainView>();
            // 创建视图对象并优化映射
            foreach (var examineMain in examineMainList)
            {
                var departmentName = departmentList.Find(m => m.DepartmentID == examineMain.DepartmentID)?.DepartmentContent ?? "";
                var examinationRecord = examinationRecordList.Find(m => examineMain.ExaminationRecordID == m.ExaminationRecordID);
                var sourceID = examinationRecord.Type == THEORY_EXAMINATION_TYPE ? examineMain.ExaminationRecordID : examineMain.ExaminationMainID;
                var examinerIDs = examiners.Where(m => m.SourceID == sourceID).Select(m => m.EmployeeID).ToList();
                var examinerNames = employeeList.Where(m => examinerIDs.Contains(m.EmployeeID)).Select(m => m.EmployeeName);
                var examinerName = examinerNames.Any() ? examinerNames.Aggregate((nameA, nameB) => $"{nameA}、{nameB}") : "";
                var employeeName = employeeList.Find(m => m.EmployeeID == examineMain.EmployeeID)?.EmployeeName ?? "";
                var statusName = statusSettings.Find(m => m.SettingValue == examineMain.StatusCode)?.Description ?? "";
                var capabilityLevelID = employeeStaffList.Find(m => m.EmployeeID == examineMain.EmployeeID)?.CapabilityLevelID;
                var capabilityLevelName = capabilityLevelID.HasValue ? capabilityLevelList.Find(m => m.CapabilityLevelID == capabilityLevelID.Value)?.CapabilityLevelName ?? "" : "";
                var examinationLevelName = string.IsNullOrWhiteSpace(examinationRecord.ExaminationLevel) ? "" : examinationLevelSettings.Find(m => m.SettingValue == examinationRecord.ExaminationLevel)?.Description ?? "";
                var examinationMainView = new ExaminationMainView
                {
                    ExaminationMainID = examineMain.ExaminationMainID,
                    ExaminationPaperMainID = examineMain.ExaminationPaperMainID,
                    ExaminationRecordID = examineMain.ExaminationRecordID,
                    DepartmentID = examineMain.DepartmentID,
                    DepartmentName = departmentName,
                    EmployeeID = examineMain.EmployeeID,
                    EmployeeName = employeeName,
                    StatusCode = examineMain.StatusCode,
                    StatusName = statusName,
                    Examiners = examinerIDs,
                    ExaminerName = examinerName,
                    Score = examineMain.Score,
                    RetakeFlag = examineMain.RetakeFlag,
                    CheatedCount = examineMain.CheatedCount,
                    StartDateTime = examineMain.StartDateTime,
                    EndDateTime = examineMain.EndDateTime,
                    CapabilityLevelID = capabilityLevelID,
                    CapabilityLevelName = capabilityLevelName,
                    RetakeScore = examineMain.RetakeScore,
                    PassingScore = examinationRecord.PassingScore,
                    ExamineName = examinationRecord.ExaminationName,
                    ExaminationLevel = examinationRecord.ExaminationLevel,
                    ExaminationLevelName = examinationLevelName
                };
                returnView.Add(examinationMainView);
            }
            // 排序
            return [.. returnView.OrderBy(m => m.DepartmentID).ThenByDescending(m => m.StartDateTime)];
        }

        /// <summary>
        /// 根据条件获取考核主表数据
        /// </summary>
        /// <param name="examineEmployeeID"></param>
        /// <param name="examineRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        private async Task<List<ExaminationMainInfo>> FilterExaminationMainByCondition(string examineEmployeeID, string examineRecordID, DateTime startDate, DateTime endDate)
        {
            // examineRecordID有值优先取
            if (!string.IsNullOrEmpty(examineRecordID))
            {
                var examineMainList = await _examinationMainRepository.GetListByRecordID(examineRecordID);
                if (!string.IsNullOrEmpty(examineEmployeeID))
                {
                    examineMainList = examineMainList.Where(m => m.ExaminationRecordID == examineRecordID).ToList();
                }
                return examineMainList;
            }
            if (string.IsNullOrEmpty(examineEmployeeID))
            {
                return await _examinationMainRepository.GetListByDate(startDate, endDate, null);
            }
            return await _examinationMainRepository.GetListByDate(startDate, endDate, examineEmployeeID);
        }

        /// <summary>
        /// 获取主考人信息 （ 需要那个表的主考人信息，就传那个表的数据）
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceIDs"></param>
        /// <returns></returns>
        private async Task<(List<ExaminerInfo>, List<string> employeeIDs)> GetExaminersBySourceTypeAndSourceID(string sourceType, List<string> sourceIDs)
        {
            var examiners = await _examinerRepository.GetListByConditionAsNoTrackAsync(m => m.SourceType == sourceType && sourceIDs.Contains(m.SourceID));
            var examinerIDs = examiners.Select(m => m.EmployeeID).ToList();
            return (examiners, examinerIDs);
        }

        /// <summary>
        /// 更改考核主表状态
        /// </summary>
        /// <param name="examinationMainID"></param>
        /// <param name="statusCode"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> ChangeExamineMainStatus(string examinationMainID, string statusCode, string employeeID)
        {
            var examineMain = await _examinationMainRepository.GetDataByMainID(examinationMainID);
            if (examineMain == null)
            {
                return false;
            }
            // 更改状态为未参加
            if (statusCode == "2")
            {
                examineMain.Score = null;
                examineMain.StartDateTime = null;
                examineMain.EndDateTime = null;
            }
            examineMain.StatusCode = statusCode;
            examineMain.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 补考
        /// </summary>
        /// <param name="examinationMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> SupplementaryExamineMain(string examinationMainID, string employeeID)
        {
            var examineMain = await _examinationMainRepository.GetDataByMainID(examinationMainID);
            if (examineMain == null)
            {
                return false;
            }
            if (examineMain.Score.HasValue || examineMain.RetakeScore.HasValue)
            {
                examineMain.StatusCode = EXAMINATION_MAIN_STATUS_CODE_1;
                examineMain.Modify(employeeID);
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            var mainInfo = new ExaminationMainInfo
            {
                ExaminationMainID = Guid.NewGuid().ToString("N"),
                ExaminationPaperMainID = examineMain.ExaminationPaperMainID,
                ExaminationRecordID = examineMain.ExaminationRecordID,
                DepartmentID = examineMain.DepartmentID,
                EmployeeID = examineMain.EmployeeID,
                PaperID = examineMain.PaperID,
                StatusCode = "1",
                RetakeFlag = true,
                CheatedCount = 0,
                DeleteFlag = ""
            };
            mainInfo.Add(employeeID);
            mainInfo.Modify(employeeID);
            await _unitOfWork.GetRepository<ExaminationMainInfo>().InsertAsync(mainInfo);
            examineMain.Delete(employeeID);
            // 补考调整预约的主考人
            var examinerInfos = await _examinerRepository.GetListBySourceAsync(SOURCETYPE_EXAMINATION_MAIN, examineMain.ExaminationMainID);
            List<ExaminerInfo> addExaminers = [];
            foreach (var item in examinerInfos)
            {
                var examiner = new ExaminerInfo
                {
                    EmployeeID = item.EmployeeID,
                    SourceType = SOURCETYPE_EXAMINATION_MAIN,
                    SourceID = mainInfo.ExaminationMainID
                };
                examiner.Add(employeeID).Modify(employeeID);
                addExaminers.Add(examiner);
            }
            await _unitOfWork.GetRepository<ExaminerInfo>().InsertAsync(addExaminers);
            examinerInfos.ForEach(m => m.Delete(employeeID));
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取考核记录(个人理论考核以及主考的实操考核)
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <param name="statusCodes">作答状态</param>
        /// <param name="filterExaminerFlag">根据主考人筛选数据开关</param>
        /// <param name="examinationType">考核类型</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<ExaminationMainView>> GetEmployeeExamineMainList(string employeeID, string statusCodes, bool filterExaminerFlag, string examinationType, DateTime? startDate, DateTime? endDate)
        {
            var statusCodeList = statusCodes != null ? statusCodes.Split(",").ToList() : new List<string>() { "4", "5", "6" };
            var examineMainList = new List<ExaminationMainInfo>();
            if (examinationType == THEORY_EXAMINATION_TYPE)
            {
                if (filterExaminerFlag)
                {
                    examineMainList = await _examinationMainRepository.GetListByExaminer(employeeID, statusCodeList);
                }
                else
                {
                    examineMainList = await _examinationMainRepository.GetListByType(employeeID, statusCodeList, examinationType);
                }
            }
            // 实操类考核 - 主考人可以查看，考试人员可以查看
            if (examinationType == PRACTICAL_EXAMINATION_TYPE)
            {
                // 监考人监考的记录
                var examinationRecordIDs = await _examinationRecordRepository.GetRecordIDsByTypeAndExaminer(examinationType, employeeID);
                examineMainList = await _examinationMainRepository.GetListAsNoTrackByRecordIDAndStatus(examinationRecordIDs, statusCodeList);
                var tempStatusCodes = statusCodes != null ? ["1", "2", "3", "7"] : statusCodeList;
                var examineMainListByEmployee = await _examinationMainRepository.GetListAsNoTrackByEmployeeIDAndType(employeeID, tempStatusCodes, examinationType);
                foreach (var mainItem in examineMainListByEmployee)
                {
                    if (examineMainList.Find(m => m.ExaminationMainID == mainItem.ExaminationMainID) != null)
                    {
                        continue;
                    }
                    examineMainList.Add(mainItem);
                }
            }

            return await CreateExaminationMainView(examineMainList, startDate, endDate, true, employeeID);
        }

        /// <summary>
        /// 保存考核明细记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> SaveExamineDetail(SaveExaminationDetailView view)
        {
            _logger.Warn($"SaveExamineDetail入参：{ListToJson.ToJson(view)}");
            var examineMainData = await _examinationMainRepository.GetDataByMainID(view.ExaminationMainID);
            if (examineMainData == null)
            {
                return false;
            }
            var paperMainData = await _examinationPaperMainRepository.GetDataByID(examineMainData.ExaminationPaperMainID);
            if (paperMainData == null)
            {
                return false;
            }
            // 实操类不允许多次保存考核数据
            if (examineMainData != null && paperMainData.PaperType == PRACTICAL_EXAMINATION_TYPE && STATUSCODE_COMPLETE_ASSESSMENT.Contains(examineMainData.StatusCode))
            {
                throw new CustomException("该考生已经评分，无需重复评分");
            }
            // 考核的实际开始结束时间(刷题练习暂存时，没有结束时间)
            if (!STATUSCODE_COMPLETE_ASSESSMENT.Contains(examineMainData.StatusCode))
            {
                examineMainData.StartDateTime = view.StartDateTime;
            }
            examineMainData.EndDateTime = view.EndDateTime;
            examineMainData.Modify(view.ModifyEmployeeID);
            var dynamicFormDetailList = await _dynamicFormDetailRepository.GetFormDetailListByFormRecordID(examineMainData.PaperID);
            dynamicFormDetailList = dynamicFormDetailList.Where(m => m.ComponentListID != COMPONENT_LIST_ID_GROUP).ToList();
            if (dynamicFormDetailList == null || dynamicFormDetailList.Count <= 0)
            {
                return false;
            }
            List<ExaminationQuestionDetailInfo> questionDetailList = null;
            List<ExaminationQuestionInfo> questionList = null;
            List<DynamicFormDetailAttributeInfo> dynamicFormDetailAttributeDetailList = null;
            if (paperMainData.PaperType != PAPER_TYPE_2)
            {
                examineMainData.CurrentQuestionID = view.CurrentQuestionID;
                var examineDetailList = await _examinationDetailRepository.GetListByMainID(view.ExaminationMainID);
                foreach (var detail in examineDetailList)
                {
                    detail.Delete(view.ModifyEmployeeID);
                }
                var questionIDList = dynamicFormDetailList.Select(m => int.TryParse(m.ItemID, out int itemID) ? itemID : 0).ToList();
                questionList = await _examinationQuestionRepository.GetListByIDList(questionIDList);
                questionDetailList = await _examinationQuestionDetailRepository.GetListByQuestionIDs(questionIDList);
                dynamicFormDetailAttributeDetailList = await _dynamicFormDetailAttributeRepository.GetDetailAttributeListByRecordID(examineMainData.PaperID);
            }
            var allScore = 0m;
            foreach (var detail in dynamicFormDetailList)
            {
                if (!int.TryParse(detail.ItemID, out int examinationQuestionID))
                {
                    continue;
                }
                var examinationDetail = view.ExaminationDetailList.Where(m => !string.IsNullOrWhiteSpace(m.ParentID) && m.ParentID == detail.ItemID).ToList();
                if (examinationDetail.Count <= 0)
                {
                    var examinationDetailInfo = new ExaminationDetailInfo
                    {
                        ExaminationDetailID = Guid.NewGuid().ToString("N"),
                        ExaminationMainID = examineMainData.ExaminationMainID,
                        ExaminationQuestionID = examinationQuestionID,
                        StatusCode = EXAMINATION_DETAIL_NA_STATUSCODE
                    };
                    examinationDetailInfo.Add(view.ModifyEmployeeID).Modify(view.ModifyEmployeeID);
                    await _unitOfWork.GetRepository<ExaminationDetailInfo>().InsertAsync(examinationDetailInfo);
                    continue;
                }
                if (paperMainData.PaperType == PAPER_TYPE_2)
                {
                    allScore += await InsertPracticalExaminationDetail(examineMainData, examinationDetail, view.ModifyEmployeeID);
                }
                else
                {
                    allScore += await InsertExaminationDetailAndCountPoints(examineMainData, examinationDetail, questionDetailList, questionList, detail, dynamicFormDetailAttributeDetailList, dynamicFormDetailList, view.ModifyEmployeeID);
                }
            }
            // 非暂存时，更新答题分数
            if (view.TempSave == null || !view.TempSave.Value)
            {
                if (examineMainData.RetakeFlag)
                {
                    examineMainData.RetakeScore = allScore;
                }
                else
                {
                    examineMainData.Score = allScore;
                }
                examineMainData.StatusCode = view.StatusCode;
                examineMainData.EndDateTime = DateTime.Now;
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 写入考核明细数据，获取分数
        /// </summary>
        /// <param name="examineMainData">考核主表数据</param>
        /// <param name="examinationDetail">考核作答明细记录</param>
        /// <param name="questionDetailList">题目明细数据</param>
        /// <param name="questionList">题目数据</param>
        /// <param name="dynamicFormDetail">试卷明细数据</param>
        /// <param name="dynamicFormDetailAttributeDetailList">试卷明细属性数据</param>
        /// <param name="modifyEmployeeID">异动人工号</param>
        /// <returns></returns>
        private async Task<Decimal> InsertExaminationDetailAndCountPoints(ExaminationMainInfo examineMainData, List<ExaminationDetailFormValueView> examinationDetail, List<ExaminationQuestionDetailInfo> questionDetailList, List<ExaminationQuestionInfo> questionList, DynamicFormDetailInfo dynamicFormDetail, List<DynamicFormDetailAttributeInfo> dynamicFormDetailAttributeDetailList, List<DynamicFormDetailInfo> dynamicFormDetailList, string modifyEmployeeID)
        {
            var score = 0m;
            var multipleChoiceIDs = new HashSet<string>();
            var groupExaminationDetails = examinationDetail.Where(m => !string.IsNullOrWhiteSpace(m.ParentID)).GroupBy(m => m.ParentID);
            foreach (var groupItem in groupExaminationDetails)
            {
                var questionData = questionList.FirstOrDefault(q => q.ExaminationQuestionID.ToString() == groupItem.Key);
                if (questionData == null)
                {
                    continue;
                }
                var examinationDetails = CreateExaminationDetails(examineMainData.ExaminationMainID, [.. groupItem], EXAMINATION_DETAIL_NA_STATUSCODE, false);
                if (examinationDetails.Count <= 0)
                {
                    continue;
                }
                switch (questionData.ExaminationQuestionType)
                {
                    case "ShortAnswer":
                        score += HandleShortAnswer(groupItem.ElementAt(0), questionDetailList, dynamicFormDetailAttributeDetailList, examinationDetails[0], dynamicFormDetail);
                        break;
                    case "Judgment":
                    case "SingleChoice":
                        score += HandleSingleOrTrueFalse(groupItem.ElementAt(0), questionDetailList, dynamicFormDetailAttributeDetailList, examinationDetails[0], dynamicFormDetail);
                        break;
                    case "MultipleChoice":
                        score += HandleMultipleChoice([.. groupItem], questionDetailList, dynamicFormDetailAttributeDetailList, examinationDetails, groupItem.Key, dynamicFormDetail);
                        break;
                }
                examinationDetails.ForEach(m => m.Add(modifyEmployeeID).Modify(modifyEmployeeID));
                await _unitOfWork.GetRepository<ExaminationDetailInfo>().InsertAsync(examinationDetails);
            }
            return score;
        }

        /// <summary>
        /// 实操类考核保存明细内容
        /// </summary>
        /// <param name="examineMainData"></param>
        /// <param name="examinationDetail"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        private async Task<int> InsertPracticalExaminationDetail(ExaminationMainInfo examineMainData, List<ExaminationDetailFormValueView> examinationDetail, string modifyEmployeeID)
        {
            var score = 0;
            foreach (var detailView in examinationDetail)
            {
                _ = int.TryParse(detailView.Score, out int points);
                score += points;
            }
            var examinationDetails = CreateExaminationDetails(examineMainData.ExaminationMainID, examinationDetail, EXAMINATION_DETAIL_RIGHT_STATUSCODE, true);
            examinationDetails.ForEach(m => m.Add(modifyEmployeeID).Modify(modifyEmployeeID));
            await _unitOfWork.GetRepository<ExaminationDetailInfo>().InsertAsync(examinationDetails);
            return score;
        }

        /// <summary>
        /// 创建考核明细数据
        /// </summary>
        /// <param name="examinationMainID">考核主表ID</param>
        /// <param name="items">考核试卷作答明细数据</param>
        /// <param name="isPractical">是否为实操类考核</param>
        /// <returns></returns>
        private static List<ExaminationDetailInfo> CreateExaminationDetails(string examinationMainID, List<ExaminationDetailFormValueView> items, string statusCode, bool isPractical)
        {
            return
                items.Select(item => new ExaminationDetailInfo
                {
                    ExaminationDetailID = Guid.NewGuid().ToString("N"),
                    ExaminationMainID = examinationMainID,
                    ExaminationQuestionID = int.TryParse(item.ParentID, out int parentID) ? parentID : 0,
                    ExaminationQuestionDetailID = item.ID,
                    Value = isPractical ? item.Score : item.Value.ToString(),
                    Remark = item.Remark,
                    DeleteFlag = "",
                    StatusCode = statusCode,
                }).ToList();
            ;
        }

        /// <summary> 处理简答题 </summary> <param name="item">试卷作答明细数据</param> <param
        /// name="questionDetailList">题目明细数据</param> <param
        /// name="dynamicFormDetailAttributeDetailList"><试卷明细属性数据/param> <param
        /// name="examinationDetailInfo">考核明细数据</param> <param
        /// name="dynamicFormDetail">试卷明细数据</param> <returns></returns>
        private decimal HandleShortAnswer(ExaminationDetailFormValueView item, List<ExaminationQuestionDetailInfo> questionDetailList,
            List<DynamicFormDetailAttributeInfo> dynamicFormDetailAttributeDetailList, ExaminationDetailInfo examinationDetailInfo, DynamicFormDetailInfo dynamicFormDetail)
        {
            var questionDetails = questionDetailList.Where(q => q.ExaminationQuestionID.ToString() == item.ParentID).ToList();
            var isCorrect = questionDetails.All(q => item.Value.ToString().Contains(q.Content));
            examinationDetailInfo.StatusCode = isCorrect ? EXAMINATION_DETAIL_RIGHT_STATUSCODE : EXAMINATION_DETAIL_ERROR_STATUSCODE;
            if (isCorrect)
            {
                var points = GetPoints(dynamicFormDetailAttributeDetailList, dynamicFormDetail);
                return points;
            }
            return 0;
        }

        /// <summary> 处理单选题/判断题 </summary> <param name="item">试卷作答明细数据</param> <param
        /// name="questionDetailList">题目明细数据</param> <param
        /// name="dynamicFormDetailAttributeDetailList"><试卷明细属性数据/param> <param
        /// name="examinationDetailInfo">考核明细数据</param> <param
        /// name="dynamicFormDetail">试卷题目对应的模板明细记录</param> <returns></returns>
        private decimal HandleSingleOrTrueFalse(ExaminationDetailFormValueView item, List<ExaminationQuestionDetailInfo> questionDetailList,
            List<DynamicFormDetailAttributeInfo> dynamicFormDetailAttributeDetailList, ExaminationDetailInfo examinationDetailInfo, DynamicFormDetailInfo dynamicFormDetail)
        {
            var questionDetail = questionDetailList.FirstOrDefault(q => q.ExaminationQuestionID.ToString() == item.ParentID && q.AnswerFlag);
            if (questionDetail != null && item.ID == questionDetail.ExaminationQuestionDetailID)
            {
                examinationDetailInfo.StatusCode = EXAMINATION_DETAIL_RIGHT_STATUSCODE;
                return GetPoints(dynamicFormDetailAttributeDetailList, dynamicFormDetail);
            }
            examinationDetailInfo.StatusCode = EXAMINATION_DETAIL_ERROR_STATUSCODE;
            return 0;
        }

        /// <summary> 处理多选题 </summary> <param name="examinationDetail">试卷作答明细数据</param> <param
        /// name="questionDetailList">题目明细数据</param> <param
        /// name="dynamicFormDetailAttributeDetailList"><试卷明细属性数据/param> <param
        /// name="examinationDetailInfos">考核明细数据</param> <param
        /// name="examinationQuestionID">题目ID</param> <param
        /// name="dynamicFormDetail">试卷题目对应的模板明细记录</param> <returns></returns>
        private decimal HandleMultipleChoice(List<ExaminationDetailFormValueView> examinationDetail, List<ExaminationQuestionDetailInfo> questionDetailList, List<DynamicFormDetailAttributeInfo> dynamicFormDetailAttributeDetailList,
                                        List<ExaminationDetailInfo> examinationDetailInfos, string examinationQuestionID, DynamicFormDetailInfo dynamicFormDetail)
        {
            var correctQuestionDetails = questionDetailList.Where(q => q.ExaminationQuestionID.ToString() == examinationQuestionID && q.AnswerFlag).ToList();
            // 答案是否相同
            var sameAnswerCountFlag = examinationDetail.Count == correctQuestionDetails.Count;
            var correctFlag = true;
            foreach (var correctQuestionDetail in correctQuestionDetails)
            {
                var chosenDetail = examinationDetail.Find(m => m.ID == correctQuestionDetail.ExaminationQuestionDetailID);
                if (chosenDetail == null)
                {
                    correctFlag = false;
                    continue;
                }
                var detailInfo = examinationDetailInfos.Find(m => m.ExaminationQuestionDetailID == correctQuestionDetail.ExaminationQuestionDetailID);
                detailInfo.StatusCode = EXAMINATION_DETAIL_RIGHT_STATUSCODE;
            }
            // 将不是正确答案的打上错误标记
            examinationDetailInfos.ForEach(m =>
            {
                if (m.StatusCode != EXAMINATION_DETAIL_RIGHT_STATUSCODE)
                {
                    m.StatusCode = EXAMINATION_DETAIL_ERROR_STATUSCODE;
                }
            });
            // 没有错误答案且答案数量一致 得分
            if (correctFlag && sameAnswerCountFlag)
            {
                return GetPoints(dynamicFormDetailAttributeDetailList, dynamicFormDetail);
            }
            return 0;
        }

        /// <summary>
        /// 获取题目分数
        /// </summary>
        /// <param name="dynamicFormDetailAttributeDetailList">试卷明细属性数据</param>
        /// <param name="dynamicFormDetail"></param>
        /// <returns></returns>
        private decimal GetPoints(List<DynamicFormDetailAttributeInfo> dynamicFormDetailAttributeDetailList, DynamicFormDetailInfo dynamicFormDetail)
        {
            var dynamicFormDetailID = dynamicFormDetail?.DynamicFormDetailID;
            var attribute = dynamicFormDetailAttributeDetailList.FirstOrDefault(a => a.DynamicFormDetailID == dynamicFormDetailID && a.ComponentAttributeID == 63);
            _ = decimal.TryParse(attribute?.AttributeValue, out decimal points);
            return points;
        }

        #region 考核扫码签到

        /// <summary>
        /// 考核扫码签到
        /// </summary>
        /// <param name="examinationRecordID">考核主表记录</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<(bool, string)> ExaminationSignIn(string examinationRecordID, long timeStamp, string employeeID)
        {
            var (flag, returnMessage, employeeExaminationMain) = await CheckSignInData(examinationRecordID, timeStamp, employeeID);
            if (!string.IsNullOrEmpty(returnMessage))
            {
                return (flag, returnMessage);
            }
            employeeExaminationMain.StatusCode = "2";
            var signUpRecord = new SignUpRecordInfo()
            {
                SourceID = employeeExaminationMain.ExaminationMainID,
                SourceType = "3",
                StatusCode = "1",
                SignUpType = "2",
            };
            returnMessage = await _signUpRecordService.SaveSignUpRecordAsync(signUpRecord, employeeID) ? "签到成功" : "签到失败";
            return (true, returnMessage);
        }

        /// <summary>
        /// 检核签到信息
        /// </summary>
        /// <param name="examinationRecordID">考核主表记录</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        private async Task<(bool, string, ExaminationMainInfo)> CheckSignInData(string examinationRecordID, long timeStamp, string employeeID)
        {
            var returnMessage = "";
            var nowDateTime = DateTime.Now;
            var examinationRecord = await _examinationRecordRepository.GetDataByID(examinationRecordID);
            if (examinationRecord == null)
            {
                returnMessage = "签到失败，未查到本场考核";
                _logger.Error($"{returnMessage},ExaminationRecordID:{examinationRecordID}");
                return (false, returnMessage, null);
            }
            if (examinationRecord.SignInFlag && examinationRecord.QRCodeRefreshTime > 0)
            {
                // 获取当前时间戳（秒）
                var currentTimeStamp = ((DateTimeOffset)nowDateTime).ToUnixTimeSeconds();
                // 比较当前时间戳和传入的二维码时间戳
                if (currentTimeStamp - (timeStamp / 1000) > examinationRecord.QRCodeRefreshTime)
                {
                    returnMessage = "签到码已过期";
                    return (false, returnMessage, null);
                }
            }
            if (examinationRecord.EndDateTime <= nowDateTime)
            {
                return (false, $"签到失败，【{examinationRecord.ExaminationName}】考核已结束", null);
            }
            var employeeExaminationMain = await _examinationMainRepository.GetEmployeeExaminationMainInfo(examinationRecordID, employeeID);
            if (employeeExaminationMain == null)
            {
                returnMessage = $"签到失败，【{examinationRecord.ExaminationName}】没有您的考核记录";
                _logger.Error($"{returnMessage},ExaminationRecordID:{examinationRecordID}，EmployeeID：{employeeID}");
                return (false, returnMessage, null);
            }
            var statusExchangeFlag = int.TryParse(employeeExaminationMain.StatusCode, out int statusCode);
            if (!statusExchangeFlag)
            {
                return (false, $"签到失败，【{examinationRecord.ExaminationName}】考核状态码异常", null);
            }
            var signUpRecordInfo = await _signUpRecordRepository.GetBySourceIDAsNoTrackAsync(employeeExaminationMain.ExaminationMainID);
            if (signUpRecordInfo != null)
            {
                switch (statusCode)
                {
                    case 2:
                        return (true, $"您在{signUpRecordInfo.AddDateTime:yyyy-MM-dd HH:mm}已签到", null);
                    case 3:
                        return (true, "您正在考核中，请尽快完成作答交卷", null);
                    case >= 3:
                        return (true, $"您的【{examinationRecord.ExaminationName}】考核已完成", null);
                    default:
                        break;
                }
            }
            return (true, returnMessage, employeeExaminationMain);
        }

        #endregion

        /// <summary>
        /// 获取刷题练习数据
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="statusCode">考核状态</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<ExaminationMainView>> GetPracticeExamineMainList(string employeeID, string statusCode, DateTime? startDate, DateTime? endDate)
        {
            var statusCodeList = statusCode != null ? [.. statusCode.Split(",")] : STATUSCODE_COMPLETE_ASSESSMENT;
            var examineMainList = await _examinationMainRepository.GetListByType(employeeID, statusCodeList, EXAMINE_PRACTICE_PAPER_TYPE);
            return await CreateExaminationMainView(examineMainList, startDate, endDate);
        }

        /// <summary>
        /// 创建考核主记录View
        /// </summary>
        /// <param name="examineMainList"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="filterPracticeFlag">是否过滤刷题练习类试卷</param>
        /// <param name="employeeID">查询考核记录人的工号 ：用来判断是否为本人监考以及本人的考核记录</param>
        /// <returns></returns>
        private async Task<List<ExaminationMainView>> CreateExaminationMainView(List<ExaminationMainInfo> examineMainList, DateTime? startDate, DateTime? endDate, bool filterPracticeFlag = false, string employeeID = null)
        {
            // 有传开始结束时间，根据时间区间筛选
            examineMainList = FilterByDateRange(examineMainList, startDate, endDate);
            if (examineMainList.Count <= 0)
            {
                return [];
            }
            var (examineRecordDict, allExaminationDetail) = await GetRecordAndDetailByMainsAsync(examineMainList, filterPracticeFlag);
            var examineRecordIDList = examineRecordDict.Keys.ToList();
            // 获取监考人人员数据
            var (examiners, examineEmployeeIDs) = await GetExaminersBySourceTypeAndSourceID(SOURCETYPE_EXAMINATION_RECORD, examineRecordIDList);
            var examinerDict = examiners.GroupBy(m => m.SourceID).ToDictionary(m => m.Key, n => n.Select(m => m.EmployeeID));
            var praticalRecordIDs = examineRecordDict.Values.Where(m => m.Type == PRACTICAL_EXAMINATION_TYPE).Select(m => m.ExaminationRecordID).ToList();
            var practicalMainID = examineMainList.Where(m => praticalRecordIDs.Contains(m.ExaminationRecordID)).Select(m => m.ExaminationMainID).ToList();
            var (practicalExaminers, practicalExamineEmployeeIDs) = await GetExaminersBySourceTypeAndSourceID(SOURCETYPE_EXAMINATION_MAIN, practicalMainID);
            var practicalExaminerDict = practicalExaminers.GroupBy(m => m.SourceID).ToDictionary(m => m.Key, n => n.Select(m => m.EmployeeID));
            // 获取状态配置
            var statusCodeSetting = await GetStatusCodeSettingAsync();
            // 获取考核预约信息和对应的监考信息 获取 EmployeeDict
            var employeeList = await GetEmployeeDictAsync(examineMainList, examineEmployeeIDs, practicalExamineEmployeeIDs);
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationRecord",
                SettingTypeValue = "ExaminationLevel"
            };
            var examinationLevelSettings = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var returnView = new List<ExaminationMainView>();
            foreach (var item in examineMainList)
            {
                if (!examineRecordDict.TryGetValue(item.ExaminationRecordID, out var examineRecord))
                {
                    continue;
                }
                // 生成考核记录View
                ExaminationMainView view = CreateBasicExaminationMainView(employeeList, statusCodeSetting, item, examineRecord, examinationLevelSettings);
                // 设置监考人信息（根据StatusCode和考试类型）
                view = SetExaminerInfo(view, examineRecord, examinerDict, employeeList, practicalExaminerDict);
                // 非本人考核 且 非本人监考的记录不显示
                if (employeeID != null && view.EmployeeID != employeeID && (view.Examiners == null || !view.Examiners.Contains(employeeID)))
                {
                    continue;
                }
                // 刷题练习 设置答题详细信息
                if (!filterPracticeFlag)
                {
                    var examinationDetailList = allExaminationDetail.Where(m => m.ExaminationMainID == item.ExaminationMainID);
                    view.DetailResponseRecord = FormatDetailResponse(examinationDetailList);
                }
                returnView.Add(view);
            }
            return [.. returnView.OrderByDescending(m => m.ExamineStartDateTime)];
        }

        #region CreateExaminationMainView中使用的方法

        /// <summary>
        /// 获取状态码Setting
        /// </summary>
        /// <returns></returns>
        private async Task<List<SettingDictionaryInfo>> GetStatusCodeSettingAsync()
        {
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationMain",
                SettingTypeValue = "StatusCode"
            };
            var statusCodeSetting = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            return statusCodeSetting;
        }

        /// <summary>
        /// 获取使用到的人员信息字典
        /// </summary>
        /// <param name="examineMainList"></param>
        /// <param name="examineMainEmployeeIDs"></param>
        /// <param name="practicalExamineEmployeeIDs"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> GetEmployeeDictAsync(List<ExaminationMainInfo> examineMainList, List<string> examineMainEmployeeIDs, List<string> practicalExamineEmployeeIDs)
        {
            // 考核人员工号 + 考核记录修改人 + 考核计划主考人工号 + 监考计划中的监考人工号
            var employeeIDList = examineMainList.Select(m => m.EmployeeID).Concat(examineMainEmployeeIDs).Concat(practicalExamineEmployeeIDs).Distinct().ToList();

            return await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDList);
        }

        /// <summary>
        /// 获取 1.考核计划信息 2、考核结果明细数据
        /// </summary>
        /// <param name="examineMainList"></param>
        /// <param name="filterPracticeFlag"></param>
        /// <returns></returns>
        private async Task<(Dictionary<string, ExaminationRecordInfo>, IEnumerable<ExaminationDetailInfo>)> GetRecordAndDetailByMainsAsync(List<ExaminationMainInfo> examineMainList, bool filterPracticeFlag)
        {
            // 获取考核计划主表数据
            var examineRecordIDList = examineMainList.Select(m => m.ExaminationRecordID).ToList();
            var examineRecordList = await _examinationRecordRepository.GetListByIDs(examineRecordIDList);
            if (filterPracticeFlag)
            {
                examineRecordList = examineRecordList.Where(m => m.Type != EXAMINE_PRACTICE_PAPER_TYPE).ToList();
            }
            var examineRecordDict = examineRecordList.ToDictionary(r => r.ExaminationRecordID);

            // 获取作答记录
            var examineIDList = examineMainList.Select(m => m.ExaminationMainID).ToList();
            var allExaminationDetail = await _examinationDetailRepository.GetAllListByMainIDsAsNoTracking(examineIDList);

            return (examineRecordDict, allExaminationDetail);
        }

        /// <summary>
        /// 设置监考人信息
        /// </summary>
        /// <param name="view"></param>
        /// <param name="examineRecord"></param>
        /// <param name="examinerDict"></param>
        /// <param name="employeeDict"></param>
        /// <param name="practicalExaminerDict"></param>
        /// <returns></returns>
        private ExaminationMainView SetExaminerInfo(ExaminationMainView view, ExaminationRecordInfo examineRecord, Dictionary<string, IEnumerable<string>> examinerDict, Dictionary<string, string> employeeDict, Dictionary<string, IEnumerable<string>> practicalExaminerDict)
        {
            if (examineRecord.Type != PRACTICAL_EXAMINATION_TYPE)
            {
                // 理论考核、模拟考核 设置监考人 | 刷题练习没有监考人
                examinerDict.TryGetValue(view.ExaminationRecordID, out var examinerIDs);
                if (examinerIDs == null || !examinerIDs.Any())
                {
                    return view;
                }
                view.Examiners = examinerIDs.ToList();
                view.ExaminerName = SplicingName(employeeDict, view.Examiners);
                return view;
            }
            practicalExaminerDict.TryGetValue(view.ExaminationMainID, out var tempExaminerIDs);
            if (tempExaminerIDs != null)
            {
                view.Examiners = tempExaminerIDs.ToList();
                view.ExaminerName = SplicingName(employeeDict, view.Examiners);
            }

            return view;
        }

        /// <summary>
        /// 按照时间区间过滤数据
        /// </summary>
        /// <param name="examineMainList"></param>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        private static List<ExaminationMainInfo> FilterByDateRange(List<ExaminationMainInfo> examineMainList, DateTime? startDateTime, DateTime? endDateTime)
        {
            if (startDateTime == null)
            {
                return examineMainList;
            }
            var startDate = startDateTime.Value.Date;
            bool hasTime(ExaminationMainInfo m) => m.StartDateTime.HasValue;
            bool isWithinDateRange(ExaminationMainInfo m) => m.StartDateTime.Value.Date >= startDate && m.EndDateTime.Value.Date >= startDate;
            bool coversStartDate(ExaminationMainInfo m) => m.StartDateTime.Value.Date <= startDate && m.EndDateTime.Value.Date >= startDate;
            // 获取 1、开始时间在试卷区间 2、没有开始时间的（未预约的实操类记录） 1、实操类考核预约后有开始时间，预约之前为空 2理论考核默认考核当天
            var tempExamineMainList = examineMainList.Where(hasTime)
                .Where(m => isWithinDateRange(m) || coversStartDate(m))
                .Union(examineMainList.Where(m => m.StartDateTime == null)).ToList();

            return examineMainList;
        }

        /// <summary>
        /// 根据考核Main记录生成前端呈现的View
        /// </summary>
        /// <param name="employeeList">人员信息</param>
        /// <param name="statusCodeSettings">考核状态码配置</param>
        /// <param name="examinationMain">考核Main记录</param>
        /// <param name="examineRecord">考核计划</param>

        private static ExaminationMainView CreateBasicExaminationMainView(Dictionary<string, string> employeeList, List<SettingDictionaryInfo> statusCodeSettings, ExaminationMainInfo examinationMain, ExaminationRecordInfo examineRecord, List<SettingDictionaryInfo> examinationLevelSettings)
        {
            var view = new ExaminationMainView
            {
                ExaminationMainID = examinationMain.ExaminationMainID,
                ExaminationPaperMainID = examinationMain.ExaminationPaperMainID,
                ExaminationRecordID = examinationMain.ExaminationRecordID,
                ExamineName = examineRecord?.ExaminationName,
                DepartmentID = examinationMain.DepartmentID,
                EmployeeID = examinationMain.EmployeeID,
                EmployeeName = employeeList.TryGetValue(examinationMain.EmployeeID, out var name) ? name : examinationMain.EmployeeID,
                StatusCode = examinationMain.StatusCode,
                StatusName = statusCodeSettings.FirstOrDefault(m => m.SettingValue == examinationMain.StatusCode)?.Description,
                Score = examinationMain.Score,
                RetakeFlag = examinationMain.RetakeFlag,
                StartDateTime = examinationMain.StartDateTime,
                EndDateTime = examinationMain.EndDateTime,
                ExamineStartDateTime = examineRecord.StartDateTime,
                ExamineEndDateTime = examineRecord.EndDateTime,
                Duration = examineRecord?.Duration,
                ExamineType = examineRecord.Type,
                PassingScore = examineRecord.PassingScore,
                MinAnswerTime = examineRecord.MinAnswerTime,
                SignInFlag = examineRecord.SignInFlag,
                OnePageQuestionFlag = examineRecord.OnePageQuestionFlag,
                ModifyEmployeeID = examinationMain.ModifyEmployeeID,
                ExaminationLevel = examineRecord.ExaminationLevel,
                ExaminationLevelName = string.IsNullOrWhiteSpace(examineRecord.ExaminationLevel) ? "" : examinationLevelSettings.Find(m => m.SettingValue == examineRecord.ExaminationLevel)?.Description ?? ""
            };
            return view;
        }

        /// <summary>
        /// 拼接 呈现的名称
        /// </summary>
        /// <param name="employeeList"></param>
        /// <param name="employeeIDs"></param>
        /// <returns></returns>
        private string SplicingName(Dictionary<string, string> employeeList, List<string> employeeIDs)
        {
            var employeeNames = employeeList.Where(m => employeeIDs.Contains(m.Key)).Select(m => m.Value);
            return employeeNames.Any() ? employeeNames.Aggregate((nameA, nameB) => $"{nameA}、{nameB}") : "";
        }

        /// <summary>
        /// 生成练习考核记录描述
        /// </summary>
        /// <param name="details"></param>
        /// <returns></returns>
        private string FormatDetailResponse(IEnumerable<ExaminationDetailInfo> details)
        {
            // 还没有开始练习刷题
            if (!details.Any())
            {
                return "";
            }
            var correct = details.Count(d => d.StatusCode == "2");
            var mistake = details.Count(d => d.StatusCode == "3");
            var notAnswered = details.Count(d => d.StatusCode == "1");
            return $"正确：{correct}道；错误：{mistake}道；未作答：{notAnswered}道；";
        }

        #endregion

        /// <summary>
        /// 停止发布考核
        /// </summary>
        /// <param name="examinationRecordID">考核主记录ID</param>
        /// <param name="employeeID">会话人员</param>
        /// <returns></returns>
        public async Task<bool> StopPublishExamine(string examinationRecordID, string employeeID)
        {
            if (string.IsNullOrEmpty(examinationRecordID))
            {
                return false;
            }
            var examineRecord = await _examinationRecordRepository.GetDataByID(examinationRecordID);
            if (examineRecord == null)
            {
                return false;
            }
            examineRecord.PublishFlag = false;

            var examinerScheduleService = _serviceProvider.GetService<IExaminerScheduleService>();
            if (examinerScheduleService != null)
            {
                await examinerScheduleService.DeleteExaminerScheduleByRecordID(examinationRecordID, employeeID);
            }
            examineRecord.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        public async Task<List<ExaminationRecordData>> GetNotFinishedPracticalRecords()
        {
            var examinationRecordDatas = new List<ExaminationRecordData>();
            // 获取所有的已发布的考核计划
            var notFinishedPracticalRecords = await _examinationRecordRepository.GetNotFinishedPracticalRecords();
            if (notFinishedPracticalRecords.Count == 0)
            {
                return examinationRecordDatas;
            }
            var examinationRecordIDs = notFinishedPracticalRecords.Select(m => m.ExaminationRecordID).ToList();
            var allExaminers = await _examinerRepository.GetListBySourceIDsAsync("ExaminationRecord", examinationRecordIDs);
            var employeeDict = await _employeePersonalDataRepository.GetDataByEmployeeIDs(allExaminers.Select(m => m.EmployeeID).ToList());
            foreach (var record in notFinishedPracticalRecords)
            {
                var examiners = allExaminers.Where(m => m.SourceID == record.ExaminationRecordID)
                .Select(m => new SelectOptionsView
                {
                    Label = employeeDict.TryGetValue(m.EmployeeID, out var name) ? name : m.EmployeeID,
                    Value = m.EmployeeID
                }).ToList();
                examinationRecordDatas.Add(new ExaminationRecordData()
                {
                    ExaminationRecordID = record.ExaminationRecordID,
                    ExaminationName = record.ExaminationName,
                    StartDateTime = record.StartDateTime,
                    EndDateTime = record.EndDateTime,
                    Examiners = examiners
                });
            }
            return examinationRecordDatas;
        }

        /// <summary>
        /// 检查考核是否已经考评完成
        /// </summary>
        /// <param name="examinationMainID">考核记录ID</param>
        /// <param name="sessionEmployeeID">当前会话用户ID</param>
        /// <returns></returns>
        public async Task<ExaminationStatusView> CheckExamineCompleteAssessOrNot(string examinationMainID, string sessionEmployeeID)
        {
            var examinationMain = await _examinationMainRepository.GetDataByMainID(examinationMainID);
            if (examinationMain == null)
            {
                return null;
            }
            var statusView = new ExaminationStatusView
            {
                StatusCode = examinationMain.StatusCode,
            };
            // 考评完成提醒
            if (STATUSCODE_COMPLETE_ASSESSMENT.Contains(examinationMain.StatusCode))
            {
                if (examinationMain.ModifyEmployeeID != sessionEmployeeID)
                {
                    var employeeName = await _employeePersonalDataRepository.GetEmployeeNameByID(examinationMain.ModifyEmployeeID);
                    statusView.LastModifyEmployeeName = employeeName;
                    return statusView;
                }
            }
            // 如果记录为考核中，且当前用户不是考核记录的最后修改人，返回最后修改人姓名，进行相关题型
            if (examinationMain.StatusCode == STATUSCODE_UNDER_ASSESSMENT)
            {
                if (examinationMain.ModifyEmployeeID != sessionEmployeeID)
                {
                    var employeeName = await _employeePersonalDataRepository.GetEmployeeNameByID(examinationMain.ModifyEmployeeID);
                    statusView.LastModifyEmployeeName = employeeName;
                    return statusView;
                }
            }

            return null;
        }

        #region 刷题练习

        public async Task<bool> SavePracticeExaminationRecord(string questionBankID, string questionBankName, int departmentID, string employeeID, string hospitalID)
        {
            var newQuestionBankName = $"{questionBankName}题库练习（{DateTime.Now:yyyy-MM-dd HH:mm}）";
            (var templateData, var questionCount) = await _examinationPaperTemplateService.CreateTemplateByBankID(questionBankID, true);
            var paperID = await _dynamicFormService.SaveFormTemplate(templateData, employeeID);
            var examinationPaperMainID = await InsertPracticePaperMainData(employeeID, newQuestionBankName, questionCount, paperID, questionBankID);
            var examinationRecordID = await InsertExaminationRecordData(employeeID, departmentID, examinationPaperMainID, newQuestionBankName);
            await InsertExaminationMainData(examinationRecordID, examinationPaperMainID, paperID, employeeID, departmentID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 刷题练习计划-新增试卷记录插入
        /// </summary>
        /// <param name="employeeID">新增人员工号，同时是刷题人工号</param>
        /// <param name="questionBankName">题库名称</param>
        /// <param name="questionCount">刷题试卷题目数量</param>
        /// <param name="paperID">生成的试卷模板ID</param>
        /// <param name="questionBankID">试卷对应的题库ID</param>
        /// <returns></returns>
        private async Task<string> InsertPracticePaperMainData(string employeeID, string questionBankName, int questionCount, string paperID, string questionBankID)
        {
            var examinationPaperMainData = new ExaminationPaperMainInfo
            {
                PaperTitle = questionBankName,
                TotalScore = 0,
                DifficultyLevel = 0,
                PassingScore = null,
                QuestionCount = questionCount,
                PaperType = EXAMINE_PRACTICE_PAPER_TYPE,
                PaperID = paperID,
                QuestionBankID = questionBankID
            };
            examinationPaperMainData.ExaminationPaperMainID = examinationPaperMainData.GetId();
            examinationPaperMainData.Add(employeeID).Modify(employeeID);
            await _unitOfWork.GetRepository<ExaminationPaperMainInfo>().InsertAsync(examinationPaperMainData);
            return examinationPaperMainData.ExaminationPaperMainID;
        }

        /// <summary>
        /// 插入考核记录表数据
        /// </summary>
        /// <param name="employeeID">人员编号</param>
        /// <param name="departmentID">部门编号</param>
        /// <param name="examinationPaperMainID">试卷记录ID</param>
        /// <param name="questionBankName">题库名称</param>
        private async Task<string> InsertExaminationRecordData(string employeeID, int departmentID, string examinationPaperMainID, string questionBankName)
        {
            var examinationRecord = new ExaminationRecordInfo
            {
                ExaminationPaperMainID = examinationPaperMainID,
                StartDateTime = DateTime.Now,
                EndDateTime = DateTime.Now.AddDays(7),
                Duration = 0,
                Instructions = questionBankName,
                Type = EXAMINE_PRACTICE_PAPER_TYPE,
                TotalScore = 0,
                ExaminationName = questionBankName,
                PassingScore = 0,
                MinAnswerTime = 0,
                DepartmentID = departmentID,
                SignInFlag = false,
                QRCodeRefreshTime = 0,
                OnePageQuestionFlag = true
            };
            examinationRecord.ExaminationRecordID = examinationRecord.GetId();
            examinationRecord.Add(employeeID).Modify(employeeID);
            await _unitOfWork.GetRepository<ExaminationRecordInfo>().InsertAsync(examinationRecord);
            return examinationRecord.ExaminationRecordID;
        }

        /// <summary>
        /// 插入考核主记录数据
        /// </summary>
        /// <param name="examinationRecordID">考核计划序号</param>
        /// <param name="examinationPaperMainID">试卷记录ID</param>
        /// <param name="paperID">试卷ID</param>
        /// <param name="employeeID">人员编号</param>
        /// <param name="departmentID">部门编号</param>
        /// <returns></returns>
        private async Task InsertExaminationMainData(string examinationRecordID, string examinationPaperMainID, string paperID, string employeeID, int departmentID)
        {
            var examinationMain = new ExaminationMainInfo()
            {
                ExaminationPaperMainID = examinationPaperMainID,
                ExaminationRecordID = examinationRecordID,
                DepartmentID = departmentID,
                EmployeeID = employeeID,
                StatusCode = EXAMINATION_MAIN_STATUS_CODE_1,
                RetakeFlag = false,
                PaperID = paperID,
            };
            examinationMain.ExaminationMainID = examinationMain.GetId();
            examinationMain.Add(employeeID).Modify(employeeID);
            await _unitOfWork.GetRepository<ExaminationMainInfo>().InsertAsync(examinationMain);
        }

        #endregion
    }
}
