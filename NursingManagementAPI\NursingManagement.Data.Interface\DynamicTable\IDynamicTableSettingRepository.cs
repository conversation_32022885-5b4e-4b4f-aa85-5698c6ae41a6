﻿using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IDynamicTableSettingRepository
    {
        /// <summary>
        /// 根据表格ID获取表格列配置
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        Task<List<DynamicTableSettingView>> GetViewListByID(int dynamicTableListID);
        /// <summary>
        /// 根据表格ID获取所有表格列配置
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        Task<List<DynamicTableSettingView>> GetCommonViewByID(int dynamicTableListID);
       
    }
}
