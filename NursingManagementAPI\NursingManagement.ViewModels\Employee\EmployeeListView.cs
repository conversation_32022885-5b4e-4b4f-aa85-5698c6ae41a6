﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 人员清单
    /// </summary>
    public class EmployeeListView
    {
        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 科室
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// HRP编号
        /// </summary>
        public string HrpEmployeeID { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string GenderCode { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 民族
        /// </summary>
        public string Nation { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public DateTime? Birthdate { get; set; }

        /// <summary>
        /// 周岁年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? EntryDate { get; set; }

        /// <summary>
        /// 工龄
        /// </summary>
        public double? EntryAge { get; set; }

        /// <summary>
        /// 家庭地址
        /// </summary>
        public string HomeAddress { get; set; }

        /// <summary>
        /// 实际住址
        /// </summary>
        public string ActualAddress { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string NationCode { get; set; }

        /// <summary>
        /// 籍贯
        /// </summary>
        public string NativePlace { get; set; }

        /// <summary>
        /// 档案编号
        /// </summary>
        public string FileID { get; set; }

        /// <summary>
        /// 在职形式编码
        /// </summary>
        public string JobCategoryCode { get; set; }

        /// <summary>
        /// 在职形式
        /// </summary>
        public string JobCategory { get; set; }

        /// <summary>
        /// 层级
        /// </summary>
        public string CapabilityLevel { get; set; }

        /// <summary>
        /// 层级序号
        /// </summary>
        public int? CapabilityLevelID { get; set; }

        /// <summary>
        /// 晋升时间
        /// </summary>
        public DateTime? PromotionDate { get; set; }

        /// <summary>
        /// 职称
        /// </summary>
        public string ProfessionalLevel { get; set; }
        /// <summary>
        /// 职称编码
        /// </summary>
        public string ProfessionalCode { get; set; }

        /// <summary>
        /// 获取职称时间
        /// </summary>
        public DateTime? ObtainingDate { get; set; }

        /// <summary>
        /// 第一学历
        /// </summary>
        public string FirstDegree { get; set; }
        /// <summary>
        /// 第一学历编码
        /// </summary>
        public string FirstDegreeCode { get; set; }
        /// <summary>
        /// 最高学历
        /// </summary>
        public string HighestDegree { get; set; }
        /// <summary>
        /// 最高学历编码
        /// </summary>
        public string HighestDegreeCode { get; set; }
        /// <summary>
        /// 姓名简拼
        /// </summary>
        public string NamePinyin { get; set; }
        /// <summary>
        /// 在职状态(1在职,2预离职)
        /// </summary>
        public int StaffStatusCode { get; set; }
        /// <summary>
        /// 是否为住院病区员工（似乎只针对护士）
        /// </summary>
        public bool IsInHospitalStationEmployee { get; set; }
    }
}