﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划指标字典
    /// </summary>
    public class AnnualIndicatorListRepository : IAnnualIndicatorListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        private readonly IOptions<SystemConfig> _config;

        public AnnualIndicatorListRepository(
            NursingManagementDbContext nursingManagementDbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService,
            IOptions<SystemConfig> options)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
            _config = options;
        }

        public async Task<AnnualIndicatorListInfo> GetInfoByID(int annualIndicatorID, bool noCache = false)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            Expression<Func<AnnualIndicatorListInfo, bool>> predicate = m => m.AnnualIndicatorID == annualIndicatorID &&
            m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*";
            var list = noCache ? await _nursingManagementDbContext.AnnualIndicatorListInfos.FirstOrDefaultAsync(predicate)
            : (await GetCacheAsync() as List<AnnualIndicatorListInfo>).FirstOrDefault(predicate.Compile());
            return list;
        }
        /// <summary>
        /// 获取最大指标字典ID
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxID()
        {
            var maxID = await _nursingManagementDbContext.AnnualIndicatorListInfos
                .Where(m => _config.Value.HospitalID == m.HospitalID && m.Language == _config.Value.Language)
                .Select(m => m.AnnualIndicatorID).DefaultIfEmpty().MaxAsync(m => m);
            return maxID;
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AnnualIndicatorListInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.AnnualIndicatorList.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
           await _redisService.Remove(key);
        }
    }
}
