﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class MenuListRepository : IMenuListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public MenuListRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.MenuListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }
        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.MenuList.GetKey(_sessionCommonServer);
        }

        public async Task<List<MenuListInfo>> GetAllMenuList()
        {
            return await GetCacheAsync() as List<MenuListInfo>;
        }
        /// <summary>
        /// 根据菜单ID获取菜单详情
        /// </summary>
        /// <param name="menuListID"></param>
        /// <returns></returns>
        public async Task<int?> GetRouterListIDByID(int menuListID)
        {
            var menuLists = await GetCacheAsync() as List<MenuListInfo>;
            return menuLists.Find(m => m.MenuListID == menuListID)?.RouterListID;
        }
    }
}
