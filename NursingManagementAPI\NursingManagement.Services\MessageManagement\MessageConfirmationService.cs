﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class MessageConfirmationService : IMessageConfirmationService
    {
        private readonly IMessageConfirmationRepository _messageConfirmationRepository;
        private readonly IMessageRecordRepository _messageRecordRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public MessageConfirmationService(
            IMessageConfirmationRepository messageConfirmationRepository,
            IUnitOfWork unitOfWork,
            IMessageRecordRepository messageRecordRepository)
        {
            _messageConfirmationRepository = messageConfirmationRepository;
            _unitOfWork = unitOfWork;
            _messageRecordRepository = messageRecordRepository;
        }

        /// <summary>
        /// 获取消息确认记录
        /// </summary>
        /// <param name="messageRecordID">消息记录ID</param>
        /// <returns>返回该消息记录的所有消息确认记录</returns>
        public async Task<List<MessageConfirmationInfo>> GetRecordsByMessageRecordID(string messageRecordID)
        {
            return await _messageConfirmationRepository.GetByMessageRecordIdAsync(messageRecordID);
        }
        /// <summary>
        /// 判断消息是否已经确认
        /// </summary>
        /// <param name="employeeId">确认人ID</param>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <returns></returns>
        public async Task<bool> IsConfirmedAsync(string employeeId, string messageRecordId)
        {
            return await _messageConfirmationRepository.IsConfirmedAsync(messageRecordId, employeeId);
        }

        /// <summary>
        /// 确认消息
        /// </summary>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns>确认是否成功</returns>
        public async Task<bool> SaveMessageConfirmation(string messageRecordId, string employeeID)
        {
            if (string.IsNullOrEmpty(messageRecordId) || string.IsNullOrEmpty(employeeID))
            {
                return false;
            }
            var messageConfirmationInfo = await _messageConfirmationRepository.SingleRecordAsync(m => m.MessageRecordID == messageRecordId && m.ConfirmEmployeeID == employeeID);
            if (messageConfirmationInfo == null)
            {
                _logger.Warn("没有获取到待确认记录");
                return false;
            }
            if (messageConfirmationInfo.ConfirmationStatus && messageConfirmationInfo.ConfirmationDate.HasValue)
            {
                return true;
            }
            messageConfirmationInfo.ConfirmationStatus = true;
            messageConfirmationInfo.ConfirmEmployeeID = employeeID;
            messageConfirmationInfo.ConfirmationDate = DateTime.Now;
            messageConfirmationInfo.Modify(employeeID);

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 获取待确认消息
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<MessageConfirmationView> GetPendingConfirmationMessages(string employeeID)
        {
            // 获取该员工的待确认消息
            var messageRecordIDs = await _messageConfirmationRepository.GetMessageRecordIDsByEmployeeIdAsync(employeeID, false);
            if (messageRecordIDs.Count == 0)
            {
                return null;
            }
            // 获取最后一条系统通知消息
            var lastRecord = await _messageRecordRepository.GetLastSystemUpdateRecord();
            if (!messageRecordIDs.Contains(lastRecord.MessageRecordID))
            {
                return null;
            }
            return new MessageConfirmationView
            {
                MessageRecordID = lastRecord.MessageRecordID,
                MessageTitle = lastRecord.MessageTitle,
                MessageContent = lastRecord.MessageContent,
            };
        }
    }

}
