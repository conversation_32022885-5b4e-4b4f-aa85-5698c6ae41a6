﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 培训群组ViewModel
    /// </summary>
    public class TrainingClassView
    {
        /// <summary>
        /// 培训群组主表ID
        /// </summary>
        public string TrainingClassMainID { get; set; }

        /// <summary>
        /// 培训群组名称
        /// </summary>
        public string TrainingClassName { get; set; }

        /// <summary>
        /// 开班日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 闭班日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 培训时长
        /// </summary>
        public int TrainingDuration { get; set; }

        /// <summary>
        /// 结业日期
        /// </summary>
        public DateTime CompleteDate { get; set; }

        /// <summary>
        ///新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }

        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }

        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 课程ID集合
        /// </summary>
        public List<string> CourseSettingIDArr { get; set; }

        /// <summary>
        /// 当前登录人对应不同群组的报名信息
        /// </summary>
        public SignUpRecordInfo SignUpRecord { get; set; }
    }
}
