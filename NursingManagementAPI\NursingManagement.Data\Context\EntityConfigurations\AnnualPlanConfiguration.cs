﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NursingManagement.Models;

namespace NursingManagement.Data.Context.EntityConfigurations
{
    public class AnnualPlanMainGoalConfiguration : IEntityTypeConfiguration<AnnualPlanMainGoalInfo>
    {
        public void Configure(EntityTypeBuilder<AnnualPlanMainGoalInfo> builder)
        {
            builder.ToTable("AnnualPlanMainGoal");
            builder.HasKey(m => m.AnnualPlanMainGoalID);
            builder.Property(m => m.AnnualPlanMainGoalID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.AnnualPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.HospitalID).HasColumnType("varchar(20)").HasMaxLength(20);
            // 一个MainGoal对应多个PlanGroups，一个PlanGroup对应一个MainGoal
            builder.HasMany(m => m.PlanGroups).WithOne(m => m.AnnualPlanMainGoal)
                .HasForeignKey(m => m.AnnualPlanMainGoalID);
        }
    }

    public class AnnualPlanGoalGroupConfiguration : IEntityTypeConfiguration<AnnualPlanGoalGroupInfo>
    {
        public void Configure(EntityTypeBuilder<AnnualPlanGoalGroupInfo> builder)
        {
            builder.ToTable("AnnualPlanGoalGroup");
            builder.HasKey(m => m.AnnualPlanGoalGroupID);
            builder.Property(m => m.AnnualPlanGoalGroupID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.AnnualPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.AnnualPlanMainGoalID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.HospitalID).HasColumnType("varchar(20)").HasMaxLength(20);
            builder.Property(m => m.ResponsibleDepartments).HasColumnType("varchar(500)").HasMaxLength(500);
            // 一个Group对应多个PlanIndicators，一个PlanIndicator对应一个Group
            builder.HasMany(m => m.PlanIndicators).WithOne(m => m.AnnualPlanGoalGroup)
                .HasForeignKey(m => m.AnnualPlanGoalGroupID);
            // 一个Group对应多个PlanProjects，一个PlanProject对应一个Group
            builder.HasMany(m => m.PlanProjects).WithOne(m => m.AnnualPlanGoalGroup)
                .HasForeignKey(m => m.AnnualPlanGoalGroupID);
        }
    }
}
