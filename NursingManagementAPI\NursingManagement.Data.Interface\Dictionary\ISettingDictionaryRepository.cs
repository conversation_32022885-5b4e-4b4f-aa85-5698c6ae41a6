﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface ISettingDictionaryRepository : ICacheRepository
    {
        /// <summary>
        /// 获取所有缓存
        /// </summary>
        /// <returns></returns>
        Task<List<SettingDictionaryInfo>> GetAllCacheAsync();
        /// <summary>
        ///  获取SettingDictionary开关配置
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        Task<bool> GetSettingSwitch(SettingDictionaryParams settingDictionaryParams);
        /// <summary>
        /// 获取一组开关配置
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        Task<List<SettingDictionaryInfo>> GetSettingSwitchList(SettingDictionaryParams settingDictionaryParams);
        /// <summary>
        /// 获取配置字典
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        Task<List<SettingDictionaryInfo>> GetSettingDictionary(SettingDictionaryParams settingDictionaryParams);

        /// <summary>
        /// 获取字典值
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        Task<string> GetSettingValue(SettingDictionaryParams settingDictionaryParams);
    }
}
