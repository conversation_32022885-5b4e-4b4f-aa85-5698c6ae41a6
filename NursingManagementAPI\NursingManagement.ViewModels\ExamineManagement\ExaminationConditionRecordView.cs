﻿namespace NursingManagement.ViewModels.Examine
{
    public class ExaminationConditionRecordView
    {
        /// <summary>
        /// 考核条件表ID
        /// </summary>
        public string ExaminationConditionRecordID { get; set; }

        /// <summary>
        /// 条件名称
        /// </summary>
        public string ConditionName { get; set; }

        /// <summary>
        /// 条件明细内容
        /// </summary>
        public string ConditionContent { get; set; }

        /// <summary>
        /// 规则分数（依据明细计算出的总分）
        /// </summary>
        public Decimal Score { get; set; }

        /// <summary>
        /// 新增人工号
        /// </summary>
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 新增人
        /// </summary>
        public string AddEmployeeName { get; set; }

        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDateTime { get; set; }

        /// <summary>
        /// 修改人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }

        public Dictionary<string, object> FilterConditionDict { get; set; }
    }
}
