﻿namespace NursingManagement.ViewModels
{
    public class DepartmentMaintenanceView
    {
        /// <summary>
        /// 护理管理组织部门名称
        /// </summary>
        public string ManagementDepartment { get; set; }
        /// <summary>
        /// 护理管理组织部门ID
        /// </summary>
        public int ManagementDepartmentID { get; set; }
        /// <summary>
        /// HR组织部门
        /// </summary>
        public string HRDepartment { get; set; }
        /// <summary>
        /// HR组织部门ID
        /// </summary>
        public int[] HRDepartmentID { get; set; }
        /// <summary>
        /// 部门层级
        /// </summary>
        public int Level { get; set; }
        /// <summary>
        /// 父级部门ID
        /// </summary>
        public int UpperLevelDepartmentID { get; set; }
        /// <summary>
        /// 是否启用（未被删除）
        /// </summary>
        public bool IsActived { get; set; }
        /// <summary>
        /// 组织类型
        /// </summary>
        public string OrganizationType { get; set; }
        /// <summary>
        /// 子集数据
        /// </summary>
        public List<DepartmentMaintenanceView> Children { get; set; }
    }
}
