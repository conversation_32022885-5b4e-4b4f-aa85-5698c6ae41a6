﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class RouterListRepository : IRouterListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public RouterListRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.RouterListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.RouterList.GetKey(_sessionCommonServer);
        }

        public async Task<List<RouterListInfo>> GetRouterListByClientType(int clientType)
        {
            var routerList = await GetCacheAsync() as List<RouterListInfo>;
            if (routerList.Count <= 0)
            {
                return new List<RouterListInfo>();
            }
            if (clientType == 3 || clientType == 4)
            {
                clientType = 2;
            }
            return routerList.Where(m => m.ClientType == clientType).OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        /// 根据路由键获取路由信息
        /// </summary>
        /// <param name="routerListID">路由键</param>
        /// <returns></returns>
        public async Task<RouterListInfo[]> GetInfosByRouterListID(int routerListID)
        {
            var routerList = await GetCacheAsync() as List<RouterListInfo>;
            if (routerList.Count <= 0)
            {
                return [];
            }
            return routerList.Where(m => m.RouterListID == routerListID).ToArray();
        }
        /// <summary>
        /// 根据ID获取数据
        /// </summary>
        /// <param name="routerListID">路由ID</param>
        /// <returns></returns>
        public async Task<RouterListInfo> GetDataByRouterListID(int routerListID)
        {
            var routerList = await GetCacheAsync() as List<RouterListInfo>;
            if (routerList.Count <= 0)
            {
                return null;
            }
            return routerList.FirstOrDefault(m => m.RouterListID == routerListID);
        }
    }
}
