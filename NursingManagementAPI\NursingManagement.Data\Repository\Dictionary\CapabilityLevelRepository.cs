﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class CapabilityLevelRepository : ICapabilityLevelRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly IRedisService _redisService;
        private readonly SessionCommonServer _sessionCommonServer;

        public CapabilityLevelRepository(
            NursingManagementDbContext db,
            IRedisService redisService,
            SessionCommonServer sessionCommonServer
            )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<CapabilityLevelInfo> GetRecordByKeysAsync(int capabilityLevelID)
        {
            var datas = await GetByCacheAsync();
            return datas.Where(m => m.CapabilityLevelID == capabilityLevelID).FirstOrDefault();
        }
        public async Task<List<CapabilityLevelInfo>> GetByCacheAsync()
        {
            var list = await GetCacheAsync() as List<CapabilityLevelInfo>;
            return list;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 600000, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.CapabilityLevelInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").OrderBy(m => m.Sort).ToListAsync();
                return result;

            });
            return datas;
        }      


        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
           return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.CapabilityLevel.GetKey(_sessionCommonServer);
        }

    }
}
