﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IExaminationPaperMainRepository
    {
        /// <summary>
        /// 根据新增时间获取数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="departmentIDs">部门ID,为空时不限制部门条件</param>
        /// <returns></returns>
        Task<List<ExaminationPaperMainInfo>> GetListByDate(DateTime startDate, DateTime endDate, List<int> departmentIDs);
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="mainID">主键</param>
        /// <returns></returns>
        Task<ExaminationPaperMainInfo> GetDataByID(string mainID);
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationPaperMainInfo>> GetDataByIDList(List<string> mainIDs);
        /// <summary>
        /// 根据实操标记获取数据
        /// </summary>
        /// <param name="paperType">试卷类型</param>
        /// <param name="switchDeptIDs">拥有权限的部门:为空时，不进行部门筛选</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetDataByPaperType(string paperType, List<int> switchDeptIDs);
        /// <summary>
        /// 根据模版ID获取试卷数据
        /// </summary>
        /// <param name="paperIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationPaperMainInfo>> GetDataByPaperIDList(List<string> paperIDs);
        /// <summary>
        /// 根据组卷条件记录ID获取数据
        /// </summary>
        /// <param name="examinationConditionRecordIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationPaperMainInfo>> GetDataByExaminationConditionRecordIDList(List<string> examinationConditionRecordIDs);
        /// <summary>
        /// 根据试卷获取对应的题库
        /// </summary>
        /// <param name="paperIDs"></param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetBankIdDictByPaperIds(List<string> paperIDs);
    }
}
