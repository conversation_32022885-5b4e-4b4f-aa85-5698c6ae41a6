﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DynamicColumnAttributeRepository : IDynamicColumnAttributeRepository
    {
        private NursingManagementDbContext _dbContext;
        private SessionCommonServer _sessionCommonServer;
        public DynamicColumnAttributeRepository(
             NursingManagementDbContext dbContext
            , SessionCommonServer sessionCommonServer   
            )
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 根据表格ID获取数据
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        public async Task<List<DynamicColumnAttributeInfo>> GetListByID(int dynamicTableListID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _dbContext.DynamicColumnAttributeInfos.Where(m => 
                m.DynamicTableListID == dynamicTableListID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据用户ID获取数据
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<List<DynamicColumnAttributeInfo>> GetListByUserID(int dynamicTableListID, string userID, string hospitalID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _dbContext.DynamicColumnAttributeInfos.Where(m => 
            m.DynamicTableListID == dynamicTableListID && m.HospitalID == session.HospitalID && m.UserID == userID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据类别获取Column中的字段名
        /// </summary>
        /// <param name="tableType"></param>
        /// <param name="tableSubType"></param>
        /// <returns></returns>
        public async Task<List<string>> GetTableFieldNamesByTableTypeAsync(string tableType, string tableSubType)
        {
            var query = from m in _dbContext.DynamicTableListInfos.Where(m => m.DeleteFlag != "*")
                        join n in _dbContext.DynamicColumnAttributeInfos.Where(m => m.DeleteFlag != "*")
                        on m.DynamicTableListID equals n.DynamicTableListID
                        where m.TableType == tableType && m.TableSubType == tableSubType && (n.AttributeCode == "Prop" || n.AttributeCode == "SlotName")
                        select n.AttributeValue;

            return await query.ToListAsync();
        }
    }
}
