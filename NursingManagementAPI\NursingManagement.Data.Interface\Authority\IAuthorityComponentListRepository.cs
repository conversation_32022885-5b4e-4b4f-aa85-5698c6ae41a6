﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IAuthorityComponentListRepository : ICacheRepository
    {
        /// <summary>
        /// 根据角色获取组件使用按钮清单
        /// </summary>
        /// <param name="roleIDs"></param>
        /// <param name="routerListIDs"></param>
        /// <param name="clientType"></param>
        /// <returns></returns>
        Task<List<AuthorityComponentListInfo>> GetComponentListByRoleIDs(List<int> roleIDs, List<int> routerListIDs, int clientType);
    }
}
