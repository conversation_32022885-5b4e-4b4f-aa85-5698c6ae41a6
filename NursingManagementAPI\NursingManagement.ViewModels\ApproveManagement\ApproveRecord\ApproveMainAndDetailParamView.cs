﻿namespace NursingManagement.ViewModels
{
    public class ApproveMainAndDetailParamView
    {
        /// <summary>
        /// 发起审批的来源表主键ID
        /// </summary>
        public string SourceID { get; set; }
        /// <summary>
        /// 提交审批记录序号
        /// </summary>
        public string ApproveRecordID { get; set; }
        /// <summary>
        /// 审批分类码
        /// </summary>
        public string ProveCategory { get; set; }
        /// <summary>
        /// 操作人工号
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 部门科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 需要的审批内容，汇总成字符串
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public string StartDate { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public string EndDate { get; set; }
        /// <summary>
        /// 开始午别
        /// </summary>
        public string StartNoon { get; set; }
        /// <summary>
        /// 结束午别
        /// </summary>
        public string EndNoon { get; set; }

        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string AdjustEmployeeName { get; set; }

        /// <summary>
        /// 换班人姓名
        /// </summary>
        public string TargetEmployeeName { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 原部门名称
        /// </summary>
        public string OriginalDepartmentName { get; set; }
        /// <summary>
        /// 自选审批人
        /// </summary>
        public List<string> SelfSelectedApprover { get;set; }
        /// <summary>
        /// 申请原因
        /// </summary>
        public string Reason { get; set; }
        /// <summary>
        /// 排班预约-今日第n个预约
        /// </summary>
        public int DayCount { get; set; }
        /// <summary>
        /// 排班预约-本月该员工第n个预约
        /// </summary>
        public int MonthCount { get; set; }
        /// <summary>
        /// 排班预约-本年该员工第n个预约
        /// </summary>
        public int YearCount { get; set; }
        /// <summary>
        /// 借调天数
        /// </summary>
        public decimal? SecondmentDays { get; set; }
        /// <summary>
        /// 申请午别(上午,2下午)
        /// </summary>
        public string AdjustTimeOfDay { get; set; }
        /// <summary>
        /// 换班午别(上午,2下午)
        /// </summary>
        public string targetNoonType { get; set; }
        /// <summary>
        /// 自动排班开关（是打开）
        /// </summary>
        public string AutoSchedule { get; set; }
    }
}
