﻿namespace NursingManagement.ViewModels
{
    public class ExaminationConditionSaveParamView
    {
        /// <summary>
        /// 组卷规则ID
        /// </summary>
        public string ExaminationConditionRecordID { get; set; }
        /// <summary>
        /// 组卷规则名称
        /// </summary>
        public string ConditionName { get; set; }
        /// <summary>
        /// 組卷規則内容
        /// </summary>
        public string ConditionContent { get; set; }
        /// <summary>
        /// 过滤筛选条件
        /// </summary>
        public List<HandleConditionView> FilterConditions { get; set; }
        /// <summary>
        /// 该条规则组卷总分
        /// </summary>
        public Decimal Score { get; set; }
    }

    public class FilterConditionView { 
        /// <summary>
        /// 分组类型
        /// </summary>
        public string GroupType { get; set; }
        /// <summary>
        /// 分组类型值
        /// </summary>
        public string GroupTypeValue { get; set; }
        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }
        /// <summary>
        /// 数据类型值
        /// </summary>
        public string DataTypeValue { get; set; }
        /// <summary>
        /// 选择的明细内容
        /// </summary>
        public List<FormDetailConditionView> Conditions { get; set; }
        /// <summary>
        /// 组卷条件文本内容
        /// </summary>
        public string ConditionContent { get; set; }
        /// <summary>
        /// 组卷条件表达式
        /// </summary>
        public string ConditionExpression { get; set; }
        

    }
}
