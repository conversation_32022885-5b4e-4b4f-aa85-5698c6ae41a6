﻿namespace NursingManagement.ViewModels
{
    public class QuestionBankView
    {
        /// <summary>
        /// 考核题库ID
        /// </summary>
        public string QuestionBankID { get; set; }
        /// <summary>
        /// 考核题库名称
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 分类名称
        /// </summary>
        public string QuestionBankTypeName { get; set; }
        /// <summary>
        /// 分类
        /// </summary>
        public string QuestionBankType { get; set; }
        /// <summary>
        /// 来源类别
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 来源ID
        /// </summary>
        public string SourceID { get; set; }
        /// <summary>
        /// 培训课程
        /// </summary>
        public string TrainingCourse { get; set; }
        /// <summary>
        /// 年份(版本)
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 实操类考核标记
        /// </summary>
        public bool IsPractical { get; set; }
        /// <summary>
        /// 修改人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployee { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 组织部门编码
        /// </summary>
        public string OrganizationalDepartmentCode { get; set; }
        /// <summary>
        /// 组织部门
        /// </summary>
        public string OrganizationalDepartment { get; set; }
        /// <summary>
        /// 父层级ID
        /// </summary>
        public string ParentID { get; set; }
        /// <summary>
        /// 参考题库ID
        /// </summary>
        public string ReferenceQuestionBankID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentID { get; set; }
        /// <summary>
        /// 组织部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 题库排序
        /// </summary>
        public int Sort { get; set; }
    }
}
