﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface.Employee;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeSecondmentRecordRepository : IEmployeeSecondmentRecordRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        public EmployeeSecondmentRecordRepository(
         NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据记录ID（主键）获取数据
        /// </summary>
        /// <param name="employeeSecondmentRecordID"></param>
        /// <returns></returns>
        public async Task<EmployeeSecondmentRecordInfo> GetDataByID(string employeeSecondmentRecordID)
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.FirstOrDefaultAsync(m => m.EmployeeSecondmentRecordID == employeeSecondmentRecordID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据原部门ID获取数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetDataByDepartmentID(int departmentID, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.Where(m => m.DepartmentID == departmentID && m.DeleteFlag != "*" 
                                    && (string.IsNullOrEmpty(m.StatusCode) || m.StatusCode=="2")
                                    && ((m.StartDate >= startDate  && m.StartDate <= endDate)
                                    || (m.StartDate <= startDate && (m.ActualEndDate ?? m.EndDate) >= endDate)
                                    || (m.EndDate >= startDate && (m.ActualEndDate ?? m.EndDate) <= endDate))).ToListAsync();
        }

        /// <summary>
        /// 根据借调部门ID获取数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetDataBySecondmentDepartmentID(int departmentID, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.Where(m => m.SecondmentDepartmentID == departmentID && m.DeleteFlag != "*"
                                    && (string.IsNullOrEmpty(m.StatusCode) || m.StatusCode == "2")
                                    && ((m.StartDate >= startDate && m.StartDate <= endDate)
                                    || ( m.StartDate <= startDate && (m.ActualEndDate ?? m.EndDate) >= endDate )
                                    || (m.EndDate >= startDate && (m.ActualEndDate ?? m.EndDate) <= endDate)
                                    )).ToListAsync();
        }
        /// <summary>
        /// 根据部门ID集合获取数据
        /// </summary>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetDataByDepartmentIDList(List<int> departmentIDs)
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.Where(m => (departmentIDs.Any(n => n == m.DepartmentID) 
                    || departmentIDs.Any(n => n == m.SecondmentDepartmentID)) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        ///  获取所有数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetAllData()
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.Where(m =>  m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据员工编号获取借调数据
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetDataByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主键获取记录
        /// </summary>
        /// <param name="recordIDs">主键ID集合</param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetRecordsByIDsAsNoTrackAsync(List<string> recordIDs)
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.Where(m => recordIDs.Contains(m.EmployeeSecondmentRecordID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据患者ID获取在指定时间范围内的借调记录
        /// </summary>
        /// <param name="employeeID">患者ID</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetPartDataByEmployeeIDAndDateRange(string employeeID, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.EmployeeSecondmentRecordInfo.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*" && 
                // 时间交叉 
                 m.StartDate <= endDate && m.EndDate >= startDate && ( m.ActualEndDate == null ||  m.ActualEndDate >= startDate ) && m.StatusCode == "2"
            ).Select(m=>new EmployeeSecondmentRecordInfo
            {
                EmployeeID = m.EmployeeID,
                StartDate = m.StartDate,
                EndDate = m.EndDate,
                StartNoon = m.StartNoon,
                EndNoon = m.EndNoon,
                ActualEndDate = m.ActualEndDate,
                ActualEndNoon = m.ActualEndNoon,
                SecondmentDepartmentID = m.SecondmentDepartmentID
            }).ToListAsync();
        }
    }
}
