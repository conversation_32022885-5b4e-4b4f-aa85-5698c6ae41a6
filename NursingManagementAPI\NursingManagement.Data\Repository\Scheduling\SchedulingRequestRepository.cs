﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class SchedulingRequestRepository : ISchedulingRequestRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        public SchedulingRequestRepository(
            NursingManagementDbContext db
            )
        {
            _nursingManagementDbContext = db;
        }
        /// <summary>
        /// 获取单人所有排班预约数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<SchedulingRequestRecordInfo>> GetSingleSchedulingAsync(string employeeID)
        {
            return await _nursingManagementDbContext.SchedulingRequestRecordInfos.Where(m => m.AddEmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取部门派班预约数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<SchedulingRequestRecordInfo>> GetDepartmentSchedulingAsync(int departmentID)
        {
            return await _nursingManagementDbContext.SchedulingRequestRecordInfos.Where(m => m.DepartmentID == departmentID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="schedulingRequestRrecordID"></param>
        /// <returns></returns>
        public async Task<SchedulingRequestRecordInfo> GetDataByID(string schedulingRequestRecordID)
        {
            return await _nursingManagementDbContext.SchedulingRequestRecordInfos.Where(m => m.SchedulingRequestRecordID == schedulingRequestRecordID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取部门审批通过的排班预约数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<SchedulingRequestRecordInfo>> GetSchedulingRequestByDate(int departmentID, DateTime startDate, DateTime endDate)
        {
            return await _nursingManagementDbContext.SchedulingRequestRecordInfos.Where(m => m.DepartmentID == departmentID && m.StatusCode == "2"
                                            && ((m.StartDate >= startDate && m.StartDate <= endDate)
                                            || (m.StartDate <= startDate && m.EndDate >= endDate)
                                            || (m.EndDate >= startDate && m.EndDate <= endDate))).ToListAsync();
        }
        /// <summary>
        /// 获取该部门某日的排班预约数据View
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startDate">开始时间</param>
        /// <returns></returns>
        public async Task<ApproveReqView[]> GetViewsByDepartmentIDAndStartDate(int departmentID, DateTime startDate)
        {

            return await _nursingManagementDbContext.SchedulingRequestRecordInfos
                .Where(m => m.DepartmentID == departmentID && m.StartDate == startDate && m.DeleteFlag != "*")
                .Select(m => new ApproveReqView
                {
                    SchedulingRequestRecordID = m.SchedulingRequestRecordID,
                    EmployeeID = m.AddEmployeeID,
                    StartDate = m.StartDate,
                    AddDateTime = m.AddDateTime,
                }).ToArrayAsync();
        }
        /// <summary>
        /// 获取该员工的排班预约记录
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <returns></returns>
        public async Task<ApproveReqView[]> GetViewsByEmployee(string employeeID)
        {
            return await _nursingManagementDbContext.SchedulingRequestRecordInfos
                .Where(m => m.AddEmployeeID == employeeID && m.DeleteFlag != "*")
                .Select(m => new ApproveReqView
                {
                    SchedulingRequestRecordID = m.SchedulingRequestRecordID,
                    EmployeeID = m.AddEmployeeID,
                    StartDate = m.StartDate,
                    AddDateTime = m.AddDateTime,
                }).ToArrayAsync();
        }
        /// <summary>
        /// 获取该时间点之后的所有排班预约记录
        /// </summary>
        /// <param name="addDateTime">添加时间</param>
        /// <returns></returns>
        public async Task<List<SchedulingRequestRecordInfo>> GetRecordsByAddDate(DateTime addDateTime)
        {
            return await _nursingManagementDbContext.SchedulingRequestRecordInfos.Where(m => m.AddDateTime > addDateTime && m.DeleteFlag != "*")
                .AsNoTracking().ToListAsync();
        }

        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<SchedulingRequestRecordInfo>> GetRecordsByIDAsNoTrackAsync(List<string> recordIDs)
        {
            return await _nursingManagementDbContext.SchedulingRequestRecordInfos.Where(m => 
                recordIDs.Contains(m.SchedulingRequestRecordID) && m.DeleteFlag != "*").AsNoTracking().ToListAsync();
        }
    }
}
