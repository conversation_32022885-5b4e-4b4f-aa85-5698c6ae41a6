﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("PerpetualCalendar")]
    public class PerpetualCalendarInfo
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }
        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 阴历日期
        /// </summary>
        [Column(TypeName = "char(8)")]
        public string LunarCalendar { get; set; }
        /// <summary>
        /// 阳历节日
        /// </summary>
        [Column(TypeName = "nvarchar(12)")]
        public string Vacation { get; set; }
        /// <summary>
        /// 阴历节日
        /// </summary>
        [Column(TypeName = "nvarchar(12)")]
        public string LunarFestival { get; set; }
        /// <summary>
        /// 24节气
        /// </summary>
        [Column(TypeName = "nvarchar(12)")]
        public string SolarTerm { get; set; }
        /// <summary>
        /// 星期
        /// </summary>
        [Column(TypeName = "nvarchar(3)")]
        public string WeekDays { get; set; }
        /// <summary>
        /// 带薪假期
        /// </summary>
        public decimal? Holiday { get; set; }
        /// <summary>
        /// 公休假标记
        /// </summary>
        public bool? PublicLeaveFlag { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>   
        [Column(TypeName = "varchar(20)")]
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime ModifyDateTime { get; set; }
    }
}
