﻿namespace NursingManagement.ViewModels
{
    public class HierachicalQCFormView
    {
        /// <summary>
        /// 表单ID
        /// </summary>
        public int? HierarchicalQCFormID { get; set; }
        /// <summary>
        /// 模板码
        /// </summary>
        public string TemplateCode { get; set; }
        /// <summary>
        /// 质控级别码
        /// </summary>
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 质控级表
        /// </summary>
        public string HierarchicalQCFormLevelName { get; set; }
        /// <summary>
        /// 表单类型
        /// </summary>
        public string FormType { get; set; }
        /// <summary>
        /// 表单类型
        /// </summary>
        public string FormTypeName { get; set; }
        /// <summary>
        /// 表单名称
        /// </summary>
        public string FormName { get; set; }
        /// <summary>
        /// 状态码
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string StatusCodeName { get; set; }
        /// <summary>
        /// 新增部门编码
        /// </summary>
        public int AddDepartmentID { get; set; }
        /// <summary>
        /// 新增部门
        /// </summary>
        public string AddDepartmentName { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployee { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 表单模板
        /// </summary>
        public FormTemplateView FormTemplateView { get; set; }
    }
}
