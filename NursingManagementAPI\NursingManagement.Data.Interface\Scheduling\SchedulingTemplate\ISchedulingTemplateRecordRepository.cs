﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ISchedulingTemplateRecordRepository
    {
        /// <summary>
        /// 获取部门排班模板主记录集合
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="statusCode">状态：1启用，0停用，默认启用</param>
        /// <returns></returns>
        Task<List<SchedulingTemplateRecordInfo>> GetRecordListByDepartmentID(int departmentID, string statusCode = "1");

        /// <summary>
        /// 获取部门排班模板主记录
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <returns></returns>
        Task<SchedulingTemplateRecordInfo> GetRecordByID(string schedulingTemplateRecordID);
    }
}
