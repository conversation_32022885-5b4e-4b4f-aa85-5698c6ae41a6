﻿namespace NursingManagement.Models
{
    /// <summary>
    /// 员工带教关系表
    /// </summary>
    public class EmployeeTeachingRelationView
    {
        /// <summary>
        /// 员工带教关系唯一ID
        /// </summary>
        public int EmployeeTeachingRelationID { get; set; }
        /// <summary>
        /// 员工ID
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 带教员工ID
        /// </summary>
        public string TeacherEmployeeID { get; set; }
        /// <summary>
        /// 带教员工姓名
        /// </summary>
        public string TeacherName { get; set; }
        /// <summary>
        /// 性别(GB/T 2261.1-2003)
        /// 0：未知的性别；1：男性；2：女性；9：未说明的性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 员工层级
        /// </summary>
        public string CapabilityLevel { get; set; }
        /// <summary>
        /// 员工科室
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 带教老师层级
        /// </summary>
        public string TeacherCapabilityLevel { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
