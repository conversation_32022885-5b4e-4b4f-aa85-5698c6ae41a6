﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 年度计划-计划维护
    /// </summary>
    public interface IAnnualPlanMaintainService
    {
        /// <summary>
        /// 查询本人及上下级已制定的年度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<List<APMainView>> GetBrowseAPViews(int year, string employeeID);
        /// <summary>
        /// 查询本人部门及上下级部门
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<List<CascaderView<int>>> GetBrowseAPDepartments(int year, string employeeID);
        /// <summary>
        /// 获取当前用户制定、兼管的当年年度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">制定人</param>
        /// <returns></returns>
        Task<APMainView[]> GetCurrentYearAPViewsByEmployee(int year, string employeeID);
        /// <summary>
        /// 获取年度计划分类
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<Dictionary<int, string>> GetAnnualPlanTypes(string mainID);
        /// <summary>
        /// 获取按分类分组的目标集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<List<APType>> GetAnnualPlanContent(string mainID);
        #region 分类目标
        /// <summary>
        /// 分类排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> SortAPType(APTypeSortView sortView, string employeeID);
        /// <summary>
        /// 更新目标排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> SortAPGoal(APGoalSortView sortView, string employeeID);
        /// <summary>
        /// 获取年度计划分类-目标
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年度</param>
        /// <returns></returns>
        Task<List<AnnualPlanMainGoalView>> GetAnnualPlanMainGoalList(int departmentID, int year);
        /// <summary>
        /// 获取分类-目标表的排序集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        Task<Dictionary<string, int>> GetAPMainGoalSortDict(string mainID);
        #endregion
        #region 分组业务
        /// <summary>
        /// 获取负责部门候选项
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<string[]> GetDepartmentOptions(string mainID);
        /// <summary>
        /// 分组保存
        /// </summary>
        /// <param name="saveView">分组更新View</param>
        /// <returns></returns>
        Task<bool> SaveAnnualPlanGroup(SaveGroupView saveView);
        /// <summary>
        /// 分组删除
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        Task<bool> DeleteAnnualGoalGroup(string groupID, string employeeID);
        /// <summary>
        /// 分组排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> SortAPGroup(APGroupSortView sortView, string employeeID);
        #endregion
        #region 指标明细业务
        /// <summary>
        /// 获取某计划的指标明细
        /// </summary>
        /// <param name="view">查询View</param>
        /// <returns></returns>
        Task<List<APIndicatorDetail>> GetIndicatorDetails(APDetailsSearchView view);
        /// <summary>
        /// 获取某分组的指标明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        Task<List<APIndicatorDetail>> GetIndicatorDetailsByGroupID(string mainID, string groupID);
        /// <summary>
        /// 获取计划已参考的指标ID集合
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        Task<int[]> GetRefIndicatorIDs(string mainID);
        /// <summary>
        /// 获取某指标明细的历年情况
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">科室ID</param>
        /// <param name="indicatorID">指标ID</param>
        /// <returns></returns>
        Task<List<HistoryIndicator>> GetPastYearIndicators(int year, int departmentID, int indicatorID);
        /// <summary>
        /// 新增指标明细
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <returns></returns>
        Task<bool> AddIndicatorDetail(SaveIndicatorDetailView addView);
        /// <summary>
        /// 更新指标明细
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <returns></returns>
        Task<bool> UpdateIndicatorDetail(UpdateIndicatorDetailView updateView);
        /// <summary>
        /// 指标明细删除
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="indicatorDetailID">指标明细ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        Task<bool> DeleteIndicatorDetail(string mainID, string indicatorDetailID, string employeeID);
        #endregion
        #region 项目明细业务
        /// <summary>
        /// 获取项目明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalIDs">计划目标表IDs</param>
        /// <returns></returns>
        Task<List<APProjectDetail>> GetProjectDetails(string mainID, string[] mainGoalIDs = null);
        /// <summary>
        /// 获取某分组的项目明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        Task<List<APProjectDetail>> GetProjectDetailsByGroupID(string mainID, string groupID);
        /// <summary>
        /// 新增项目明细
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <returns></returns>
        Task<bool> AddProjectDetail(SaveProjectDetailView addView);
        /// <summary>
        /// 更新工作项目
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <returns></returns>
        Task<bool> UpdateProjectDetail(UpdateProjectDetailView updateView);
        /// <summary>
        /// 项目明细删除
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        Task<bool> DeleteProjectDetail(string mainID, string projectDetailID, string employeeID);
        #endregion
        /// <summary>
        /// 获取上级部门针对某一目标制定的项目内容集合
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年度</param>
        /// <param name="goalID">目标ID</param>
        /// <returns></returns>
        Task<List<DictItem>> GetSuperiorProjectDetail(int departmentID, int year, int goalID);
        /// <summary>
        /// 发布年度计划
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<bool> PublishAnnualPlan(string mainID, string employeeID);
    }
}