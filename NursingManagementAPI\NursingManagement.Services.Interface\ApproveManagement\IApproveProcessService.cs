﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 审批流程服务接口
    /// </summary>
    public interface IApproveProcessService
    {
        #region 查询
        /// <summary>
        /// 获取审批流程列表
        /// </summary>
        /// <param name="employeeID">HR工号</param>
        /// <param name="roles">角色</param>
        /// <returns></returns>
        Task<List<ApproveProcess>> GetApproveProcesses(string hospitalID);
        /// <summary>
        /// 查询审批流程节点列表
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        Task<List<ApproveProcessNode>> GetApproveProcessNodes(string approveProcessID);
        /// <summary>
        /// 根据分类、科室获取审批流程ID
        /// </summary>
        /// <param name="proveCategory">分类码</param>
        /// <param name="departmentID">科室</param>
        /// <returns></returns>
        Task<(string, string)> GetProcessIDAndContentTemplateByTypeAndDepartmentID(string proveCategory, int departmentID);
        /// <summary>
        /// 获取分类已存启用科室集合接口
        /// </summary>
        /// <param name="proveCategory">分类码</param>
        /// <returns></returns>
        Task<int[]> GetEnableDepartmentIDsByProveCategory(string proveCategory);
        /// <summary>
        /// 根据审批流程ID获取审批结束后的API
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        Task<string> GetApiByProcessID(string approveProcessID);
        /// <summary>
        /// 手动提交审批
        /// </summary>
        /// <param name="approveType">审批类型</param>
        /// <param name="saveView">保存数据</param>
        /// <returns></returns>
        Task<bool> ManualSubmissionApproveAsync(string approveType, string recordID);
        #endregion
        #region 增删改
        /// <summary>
        /// 新增审批流程
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        Task<bool> AddApproveProcess(SaveApproveProcessView saveView);
        /// <summary>
        /// 修改审批流程
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        Task<bool> UpdateApproveProcess(SaveApproveProcessView saveView);
        /// <summary>
        /// 启用审批流程
        /// </summary>
        /// <param name="enableView">启用View</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        Task<bool> EnableApproveProcess(ApproveProcessStatusChangeView enableView, string employeeID);
        /// <summary>
        /// 停用审批流程
        /// </summary>
        /// <param name="disableView">停用View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> DisableApproveProcess(ApproveProcessStatusChangeView disableView, string employeeID);
        /// <summary>
        /// 删除审批流程主表
        /// </summary>
        /// <param name="deleteView">主键</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        Task<bool> DeleteApproveProcess(ApproveProcessStatusChangeView deleteView, string employeeID);
        #endregion
    }
}
