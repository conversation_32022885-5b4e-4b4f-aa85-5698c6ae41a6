﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class HospitalListRepository : IHospitalListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly IRedisService _redisService;
        public HospitalListRepository(
            NursingManagementDbContext db
            , IRedisService redisService
        )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var datas = await _redisService.GetOrCreateAsync(key, 0, async () =>
            {
                var result = await _nursingManagementDbContext.HospitalListInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.HospitalList.ToString();
        }

        public async Task<List<HospitalListInfo>> GetHospitalList()
        {
            return await GetCacheAsync() as List<HospitalListInfo>;
        }
    }
}
