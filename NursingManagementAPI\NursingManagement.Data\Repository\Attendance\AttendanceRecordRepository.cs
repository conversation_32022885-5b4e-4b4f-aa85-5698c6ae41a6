﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AttendanceRecordRepository : IAttendanceRecordRepository
    {
        private readonly NursingManagementDbContext _context;
        private readonly SessionCommonServer _sessionCommonServer;

        public AttendanceRecordRepository(
            NursingManagementDbContext context
            , SessionCommonServer sessionCommonServer
        )
        {
            _context = context;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<AttendanceRecordInfo>> GetRecordByEmployeeIDs(int departmentID, int attendanceYear, int attendanceMonth, List<string> employeeIDs)
        {
            var datas = await GetRecordByDepartmentID(departmentID, attendanceYear, attendanceMonth);
            return datas.Where(m => employeeIDs.Contains(m.EmployeeID)).ToList();
        }

        public async Task<List<AttendanceRecordInfo>> GetRecordByDepartmentID(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.AttendanceRecordInfos.Where(m =>
                                                m.DepartmentID == departmentID
                                                && m.AttendanceYear == attendanceYear
                                                && m.AttendanceMonth == attendanceMonth
                                                && m.HospitalID == session.HospitalID
                                                && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
