﻿using NursingManagement.Services.Interface;
using System.Diagnostics;

namespace NursingManagement.UnitTest
{
    public class SerialNumberServiceTest
    {
        private readonly ISerialNumberService _serialNumberService;

        public SerialNumberServiceTest(ISerialNumberService serialNumberService)
        {
            _serialNumberService = serialNumberService;
        }

        /// <summary>
        /// 无配置测试
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task OnlySerialNumberTest()
        {
            var serialNumber = await _serialNumberService.GetSerialNumber("AnnualIndicatorList");
            // 预期返回纯数字
            Assert.Equal("1", serialNumber);
        }

        /// <summary>
        /// 有配置、无数据库数据测试
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task ProveSerialNumberTest()
        {
            var serialNumber = await _serialNumberService.GetSerialNumber("ProveCategory");
            // 预期返回组装后流水号，起始为1
            Assert.Equal("AA-02124010001", serialNumber);
        }

        /// <summary>
        /// 有配置、有数据库数据测试
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task ProveSerialNumberTest1()
        {
            // 需要在数据库插入一条数据
            /*
             insert into SerialNumberRecords(BizCategoryCode,SerialNumber,AddDateTime,AddEmployeeID,ModifyDateTime,ModifyEmployeeID,DeleteFlag)
            values
            ('AA-020','1',GETDATE(),'System',GETDATE(),'System','')
             */
            var serialNumber = await _serialNumberService.GetSerialNumber("ProveCategory");
            Assert.Equal("AA-02024010002", serialNumber);
        }

        /// <summary>
        /// 测试清零
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task ReturnZeroTest()
        {
            // 需要一条数据库数据，修改时间为上一个月
            var serialNumber = await _serialNumberService.GetSerialNumber("ProveCategory");
            Assert.Equal("AA-02024010001", serialNumber);
        }

        /// <summary>
        /// 测试清零
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task TestAsync()
        {
            List<int> numbers = new List<int> { 1, 2, 3, 4, 5 };

            // 使用 forEach 遍历集合
            var tasks = numbers.Select(async number =>
            {
                // 模拟异步操作
                await Task.Delay(1000);
                Debug.WriteLine(number);
                return number;
            });
            await Task.WhenAll(tasks);
            
            Debug.WriteLine("forEach 循环完成");

            // 使用 for 循环遍历集合
            //foreach (int number in numbers)
            //{
            //    // 模拟异步操作
            //    await Task.Delay(1000);
            //    Debug.WriteLine(number);
            //}
            //Debug.WriteLine("for 循环完成");
        }
    }
}
