﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class EmployeeDepartmentChangeRequestRepository : IEmployeeDepartmentChangeRequestRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeDepartmentChangeRequestRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 获取人员部门申请信息（取未审核的）
        /// </summary>
        /// <param name="employeeIDs">人员ID集合</param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentChangeRequestInfo>> GetListByEmployeeIDs(List<string> employeeIDs, string hospitalID)
        {
            var query = await _dbContext.EmployeeDepartmentChangeRequestInfos.Where(m => m.HospitalID == hospitalID &&
            employeeIDs.Contains(m.EmployeeID) && m.DeleteFlag != "*" && m.StatusCode != "2" && m.StatusCode != "3").ToListAsync();
            return query.OrderByDescending(m => m.ModifyDateTime).ToList();
        }
        /// <summary>
        /// 根据部门ID获取人员部门变更记录
        /// </summary>
        /// <param name="hospitalID">医院编码</param>
        /// <param name="requestStartDate">申请开始时间</param>
        /// <param name="requestEndDate">申请结束时间</param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentChangeView>> GetEmployeeDepartmentChangeRequestViews(string hospitalID, DateTime requestStartDate, DateTime requestEndDate)
        {
            var query = await _dbContext.EmployeeDepartmentChangeRequestInfos.Where(m => m.HospitalID == hospitalID 
            && m.DeleteFlag != "*" && m.AddDateTime.Date >= requestStartDate.Date && m.AddDateTime.Date <= requestEndDate.Date).Select(m=>new EmployeeDepartmentChangeView
            {
                ID = m.ID,
                EmployeeID = m.EmployeeID,
                DepartmentID =m.DepartmentID,
                OriginalDepartmentID =m.OriginalDepartmentID,
                StatusCode = m.StatusCode,
                AddDateTime =m.AddDateTime,
                AddEmployeeID = m.AddEmployeeID
            }).ToListAsync();
            return query.OrderByDescending(m => m.AddDateTime).ToList();
        }
        /// <summary>
        /// 根据主键ID获取申请记录
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns></returns>
        public async Task<EmployeeDepartmentChangeRequestInfo> GetEmployeeDepartmentChangeRequestByID(string id)
        {
            return await _dbContext.EmployeeDepartmentChangeRequestInfos.Where(m => m.ID == id).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主键获取调岗申请记录
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentChangeRequestInfo>> GetRecordsByIDsAsNoTrackAsync(List<string> recordIDs)
        {
            return await _dbContext.EmployeeDepartmentChangeRequestInfos
                .AsNoTracking().Where(m => recordIDs.Contains(m.ID )).ToListAsync();
        }
    }
}