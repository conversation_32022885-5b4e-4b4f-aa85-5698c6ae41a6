﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 部门岗位字典
        /// </summary>
        public DbSet<DepartmentPostInfo> DepartmentPostInfos { get; set; }
        /// <summary>
        /// 岗位说明书
        /// </summary>
        public DbSet<PostDescriptionInfo> PostDescriptionInfos { get; set; }
        /// <summary>
        /// 岗位字典
        /// </summary>
        public DbSet<PostInfo> PostInfos { get; set; }
        /// <summary>
        /// 岗位部门-能级对照表
        /// </summary>
        public DbSet<DepartmentPostToCapabilityLevelInfo> DepartmentPostToCapabilityLevelInfos { get;  set; }
        /// <summary>
        /// 岗位部门-能级对照表
        /// </summary>
        public DbSet<DepartmentPostSettingInfo> DepartmentPostSettingInfos { get; set; }
        /// <summary>
        /// 部门岗位工作时间表
        /// </summary>
        public DbSet<DepartmentPostWorkingTimeInfo> DepartmentPostWorkingTimeInfos { get; set; }

    }
}
