﻿﻿using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface.NormalWorkingReminder;
using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 常态工作控制提醒控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class NormalWorkingReminderController : ControllerBase
    {
        private readonly INormalWorkingReminderService _normalWorkingReminderService;

        public NormalWorkingReminderController(INormalWorkingReminderService normalWorkingReminderService)
        {
            _normalWorkingReminderService = normalWorkingReminderService ?? throw new ArgumentNullException(nameof(normalWorkingReminderService));
        }

        /// <summary>
        /// 执行三天未整改提醒（提醒护士长）
        /// </summary>
        /// <returns>提醒执行结果</returns>
        [HttpPost]
        [Route("ExecuteThreeDayReminder")]
        public async Task<IActionResult> ExecuteThreeDayReminder()
        {
            var requestView = new ReminderRequestView
            {
                ReminderType = 3 // 三天提醒
            };

            var reminderResult = await _normalWorkingReminderService.ExecuteReminderAsync(requestView);
            var result = new ResponseResult { Data = reminderResult };

            if (reminderResult?.Success == true)
            {
                result.Sucess();
            }
            else
            {
                result.Error(reminderResult?.Message ?? "执行三天提醒失败");
            }

            return result.ToJson();
        }

        /// <summary>
        /// 执行六天未整改提醒（提醒片区主任）
        /// </summary>
        /// <returns>提醒执行结果</returns>
        [HttpPost]
        [Route("ExecuteSixDayReminder")]
        public async Task<IActionResult> ExecuteSixDayReminder()
        {
            var requestView = new ReminderRequestView
            {
                ReminderType = 6 // 六天提醒
            };

            var reminderResult = await _normalWorkingReminderService.ExecuteReminderAsync(requestView);
            var result = new ResponseResult { Data = reminderResult };

            if (reminderResult?.Success == true)
            {
                result.Sucess();
            }
            else
            {
                result.Error(reminderResult?.Message ?? "执行六天提醒失败");
            }

            return result.ToJson();
        }
    }
}
