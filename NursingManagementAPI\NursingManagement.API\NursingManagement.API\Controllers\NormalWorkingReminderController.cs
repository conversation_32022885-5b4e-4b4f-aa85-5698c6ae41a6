﻿﻿using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.Services.Interface.NormalWorkingReminder;
using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 常态工作控制提醒控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class NormalWorkingReminderController : ControllerBase
    {
        private readonly INormalWorkingReminderService _normalWorkingReminderService;

        public NormalWorkingReminderController(
            INormalWorkingReminderService normalWorkingReminderService)
        {
            _normalWorkingReminderService = normalWorkingReminderService;
        }

        /// <summary>
        /// 执行常态工作控制提醒
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        [HttpPost]
        [Route("ExecuteReminder")]
        public async Task<IActionResult> ExecuteReminder([FromBody] ReminderRequestView requestView)
        {
            var result = new ResponseResult();
            // 参数验证
            if (requestView == null)
            {
                result.Error("请求参数不能为空");
                return result.ToJson();
            }

            // 执行提醒
            var reminderResult = await _normalWorkingReminderService.ExecuteReminderAsync(requestView);
            result.Data = reminderResult;

            if (reminderResult?.Success == true)
            {
                result.Sucess();
            }
            else
            {
                result.Error(reminderResult?.Message ?? "执行提醒失败");
            }

            return result.ToJson();

        }

        /// <summary>
        /// 执行三天未整改提醒（提醒护士长）
        /// </summary>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <param name="forceRemind">是否强制提醒</param>
        /// <returns>提醒执行结果</returns>
        [HttpPost]
        [Route("ExecuteThreeDayReminder")]
        public async Task<IActionResult> ExecuteThreeDayReminder([FromQuery] int? departmentID = null, [FromQuery] bool forceRemind = false)
        {
            var result = new ResponseResult();
            // 执行三天提醒
            var reminderResult = await _normalWorkingReminderService.ExecuteThreeDayReminderAsync(session.HospitalID, departmentID, forceRemind);
            result.Data = reminderResult;

            if (reminderResult?.Success == true)
            {
                result.Sucess();
            }
            else
            {
                result.Error(reminderResult?.Message ?? "执行三天提醒失败");
            }

            return result.ToJson();

        }

        /// <summary>
        /// 执行六天未整改提醒（提醒片区主任）
        /// </summary>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <param name="forceRemind">是否强制提醒</param>
        /// <returns>提醒执行结果</returns>
        [HttpPost]
        [Route("ExecuteSixDayReminder")]
        public async Task<IActionResult> ExecuteSixDayReminder([FromQuery] int? departmentID = null, [FromQuery] bool forceRemind = false)
        {
            var result = new ResponseResult();
            // 执行六天提醒
            var reminderResult = await _normalWorkingReminderService.ExecuteSixDayReminderAsync(session.HospitalID, departmentID, forceRemind);
            result.Data = reminderResult;

            if (reminderResult?.Success == true)
            {
                result.Sucess();
            }
            else
            {
                result.Error(reminderResult?.Message ?? "执行六天提醒失败");
            }

            return result.ToJson();

        }

        /// <summary>
        /// 查询未整改问题列表
        /// </summary>
        /// <param name="requestView">查询请求参数</param>
        /// <returns>未整改问题列表</returns>
        [HttpPost]
        [Route("QueryUnrectifiedProblems")]
        public async Task<IActionResult> QueryUnrectifiedProblems([FromBody] QueryUnrectifiedProblemsRequestView requestView)
        {
            var result = new ResponseResult();
            // 参数验证
            if (requestView == null)
            {
                result.Error("请求参数不能为空");
                return result.ToJson();
            }


            // 查询未整改问题
            var problems = await _normalWorkingReminderService.QueryUnrectifiedProblemsAsync(requestView);
            result.Data = problems;
            result.Sucess();

            return result.ToJson();

        }
    }
}
