using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 监考计划监考人员仓储实现类
    /// </summary>
    public class ExaminerScheduleEmployeeRepository : IExaminerScheduleEmployeeRepository
    {
        private readonly NursingManagementDbContext _context;

        public ExaminerScheduleEmployeeRepository(NursingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<List<ExaminerScheduleEmployeeInfo>> GetByExaminerScheduleID(string examinerScheduleID)
        {
            return await _context.ExaminerScheduleEmployeeInfos
                .Where(x => x.ExaminerScheduleID == examinerScheduleID && x.DeleteFlag != "*")
                .ToListAsync();
        }

        public async Task<List<ExaminerScheduleEmployeeInfo>> GetByExaminerScheduleIDs(List<string> examinerScheduleIDs)
        {
            return await _context.ExaminerScheduleEmployeeInfos
                .Where(x => examinerScheduleIDs.Contains(x.ExaminerScheduleID) && x.DeleteFlag != "*")
                .ToListAsync();
        }
    }
}