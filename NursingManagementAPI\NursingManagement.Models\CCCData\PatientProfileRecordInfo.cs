﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// CCC患者状况主表
    /// </summary>
    [Serializable]
    [Table("PatientProfileRecord")]
    public class PatientProfileRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 患者状况唯一号
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string PatientProfileRecordID { get; set; }
        /// <summary>
        /// 患者状况类型（SettingDictionary）
        /// </summary>
        public int ProfileID { get; set; }
        /// <summary>
        /// 病历号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ChartNo { get; set; }
        /// <summary>
        /// 患者住院号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string CaseNumber { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string PatientName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Gender { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Age { get; set; }
        /// <summary>
        /// 入院日期
        /// </summary>
        public DateTime AdmissionDateTime { get; set; }
        /// <summary>
        /// 出院日期
        /// </summary>
        public DateTime? DischargeDateTime { get; set; }
        /// <summary>
        /// CCC病区ID
        /// </summary>
        public int StationID { get; set; }
        /// <summary>
        /// 床号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string BedNumber { get; set; }
        /// <summary>
        /// 来源类型（PatientEvent）
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SourceType { get; set; }
        /// <summary>
        /// 状况来源ID（PatientEventID）
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }
        /// <summary>
        /// 关联护理管理表名称（质控：HierarchicalQCRecord .....）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string RelatedTableName { get; set; }
        /// <summary>
        /// 关联护理管理表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string RelatedTableRecordID { get; set; }
        /// <summary>
        /// 发生时间（患者事件时间等）
        /// </summary>
        public DateTime OccurDateTime { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
    }
}
