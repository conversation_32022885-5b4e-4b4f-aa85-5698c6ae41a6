﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班标记设置视图
    /// </summary>
    public class SchedulingMarkSettingsView
    {
        /// <summary>
        /// 标记主键
        /// </summary>
        public int? AdministrationIconID { get; set; }
        /// <summary>
        /// 群组号病区ID
        /// </summary>
        public string GroupID { get; set; }
        /// <summary>
        /// 标记图标
        /// </summary>
        public string Icon { get; set; }
        /// <summary>
        /// 标记文本
        /// </summary>
        public string Text { get; set; }
        /// <summary>
        /// 标识说明
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 前景色
        /// </summary>
        public string Color { get; set; }
        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackGroundColor { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string UserID { get; set; }
        /// <summary>
        /// 修改标记
        /// </summary>
        public bool EditFlag { get; set; }
    }
}
