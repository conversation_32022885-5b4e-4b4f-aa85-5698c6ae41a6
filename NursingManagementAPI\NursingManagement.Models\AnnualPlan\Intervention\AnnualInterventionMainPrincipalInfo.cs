﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划执行项目负责人表
    /// </summary>
    [Serializable]
    [Table("AnnualInterventionMainPrincipal")]
    public class AnnualInterventionMainPrincipalInfo : MutiModifyInfo
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public int ID { get; set; }
        /// <summary>
        /// 外键，年度计划执行项目主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualInterventionMainID { get; set; }
        /// <summary>
        /// 计划主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 人员工号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
    }
}
