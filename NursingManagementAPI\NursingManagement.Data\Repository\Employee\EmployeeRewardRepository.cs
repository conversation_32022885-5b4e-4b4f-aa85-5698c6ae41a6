﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeRewardRepository : IEmployeeRewardRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeRewardRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据员工工号获取员工奖惩信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public Task<List<EmployeeRewardInfo>> GetRecordListAsync(string employeeID)
        {
            return _dbContext.EmployeeRewardInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag !="*").ToListAsync();
        }
    }
}