﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeEmploymentRecordRepository : IEmployeeEmploymentRecordRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeEmploymentRecordRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 根据工号和岗位类型获取任职记录
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="postType">岗位类型</param>
        /// <returns></returns>
        public async Task<List<EmployeeEmploymentRecordInfo>> GetRecordListAsync(string employeeID,int postType)
        {
            return await _dbContext.EmployeeEmploymentRecordInfos.Where(m => m.EmployeeID == employeeID && m.PostType == postType && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据工号获取任职记录
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="postType"></param>
        /// <returns></returns>
        public async Task<List<EmployeeEmploymentRecordInfo>> GetAllRecordListAsync(string employeeID)
        {
            return await _dbContext.EmployeeEmploymentRecordInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据ID获取数据
        /// </summary>
        /// <param name="employeeEmploymentRecordID"></param>
        /// <returns></returns>
        public async Task<EmployeeEmploymentRecordInfo> GetDataByID(string employeeEmploymentRecordID)
        {
            return await _dbContext.EmployeeEmploymentRecordInfos.FirstOrDefaultAsync(m => m.EmployeeEmploymentRecordID == employeeEmploymentRecordID && m.DeleteFlag != "*");
        }

        public async Task<List<EmployeeEmploymentRecordInfo>> GetLastRecordByEmployeeIDs(List<string> employeeIDs)
        {
            return await _dbContext.EmployeeEmploymentRecordInfos.Where(m => employeeIDs.Contains(m.EmployeeID) && m.DeleteFlag != "*")
                .GroupBy(m=> new { m.EmployeeID,m.DepartmentID,m.StartDate})
                .Select(g=>g.OrderByDescending(m=>m.StartDate).FirstOrDefault())
                .ToListAsync();
        }
    }
}