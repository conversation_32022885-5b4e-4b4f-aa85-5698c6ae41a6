﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    ///  监考计划controller
    /// </summary>
    [Produces("application/json")]
    [Route("api/ExaminerSchedule")]
    [EnableCors("any")]
    public class ExaminerScheduleController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IExaminerScheduleService _examinerScheduleService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="examinerScheduleService"></param>
        /// <param name="session"></param>
        public ExaminerScheduleController(
            ISessionService session,
            IExaminerScheduleService examinerScheduleService
        )
        {
            _session = session;
            _examinerScheduleService = examinerScheduleService;
        }

        /// <summary>
        /// 获取监考计划列表
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="examinationRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExaminerScheduleList")]
        public async Task<IActionResult> GetExaminerScheduleList(string employeeID, string examinationRecordID, DateTime startDate, DateTime endDate)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _examinerScheduleService.GetExaminerScheduleList(employeeID, examinationRecordID, startDate, endDate);
            return result.ToJson();
        }

        /// <summary>
        /// 保存监考计划
        /// </summary>
        /// <param name="saveExaminerScheduleView">监考计划信息</param>
        /// <returns>保存结果</returns>
        [HttpPost]
        [Route("SaveExaminerSchedule")]
        public async Task<IActionResult> SaveExaminerSchedule([FromBody] SaveExaminerScheduleView saveExaminerScheduleView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _examinerScheduleService.SaveExaminerSchedule(saveExaminerScheduleView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 删除监考计划
        /// </summary>
        /// <param name="examinerScheduleID">监考计划ID</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        [Route("DeleteExaminerSchedule")]
        public async Task<IActionResult> DeleteExaminerSchedule(string examinerScheduleID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _examinerScheduleService.DeleteExaminerSchedule(examinerScheduleID, session.EmployeeID);
            return result.ToJson();
        }
    }
}
