﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers.AnnualPlan
{
    /// <summary>
    /// 年度计划设置表维护
    /// </summary>
    [Route("api/AnnualPlanSetting")]
    [ApiController]
    [EnableCors("any")]
    public class AnnualPlanSettingController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualSettingService _annualSettingService;
        private readonly ISessionService _session;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="session"></param>
        /// <param name="annualIndicatorListService"></param>
        public AnnualPlanSettingController(ISessionService session, IAnnualSettingService annualIndicatorListService)
        {
            _annualSettingService = annualIndicatorListService;
            _session = session;
        }

        #region 项目字典
        /// <summary>
        /// 获取年计划执行项目字典
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="showUpperIntervention">是否呈现上级执行项目</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualInterventionList")]
        public async Task<IActionResult> GetAnnualInterventionList(int departmentID, bool showUpperIntervention)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.GetAnnualInterventionList(departmentID, showUpperIntervention);
            return result.ToJson();
        }
        /// <summary>
        /// 保存执行项目字典
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualInterventionList")]
        public async Task<IActionResult> SaveAnnualInterventionList([FromBody] APInterventionListSaveView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(saveView, session);
            result.Data = await _annualSettingService.SaveAnnualInterventionList(saveView);
            return result.ToJson();
        }
        #endregion
        #region 指标字典
        /// <summary>
        /// 获取指标字典
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="showUpperIndicator">是否呈现连续上级指标</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualIndicatorList")]
        public async Task<IActionResult> GetAnnualIndicatorList(int departmentID, bool showUpperIndicator)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.GetAnnualIndicatorList(departmentID, showUpperIndicator);
            return result.ToJson();
        }
        /// <summary>
        /// 保存指标字典
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualIndicatorList")]
        public async Task<IActionResult> SaveAnnualIndicatorList([FromBody] AnnualIndicatorListView annualIndicatorListView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(annualIndicatorListView, session);
            (result.Data, _) = await _annualSettingService.SaveAnnualIndicatorList(annualIndicatorListView);
            return result.ToJson();
        }
        /// <summary>
        /// 删除指标字典
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteAnnualIndicatorList")]
        public async Task<IActionResult> DeleteAnnualIndicatorList([FromBody] AnnualIndicatorListView annualIndicatorListView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(annualIndicatorListView, session);
            result.Data = await _annualSettingService.DeleteAnnualIndicatorList(annualIndicatorListView);
            return result.ToJson();
        }
        #endregion
        #region 目标字典
        /// <summary>
        /// 获取目标字典
        /// </summary>
        /// <param name="goalIds">目标集合</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetGoalList")]
        public async Task<IActionResult> GetGoalList([FromForm] int[] goalIds)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.GetGoalList(goalIds);
            return result.ToJson();
        }
        /// <summary>
        /// 更新目标名称
        /// </summary>
        /// <param name="goalID">目标字典ID</param>
        /// <param name="content">目标名称</param>
        /// <returns></returns>
        [HttpGet]
        [Route("UpdateGoalContent")]
        public async Task<IActionResult> UpdateGoalContent(int goalID, string content)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.UpdateGoalContent(goalID, content, session.HisUserID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存年度计划分类-目标
        /// </summary>
        /// <param name="mainGoalView">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualPlanMainGoal")]
        public async Task<IActionResult> SaveAnnualPlanMainGoal([FromBody] AnnualPlanMainGoalView mainGoalView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.SaveAnnualPlanMainGoal(mainGoalView, session);
            return result.ToJson();
        }

        /// <summary>
        /// 删除年度计划可选目标集合
        /// </summary>
        /// <param name="mainGoalView">删除参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteAnnualPlanMainGoal")]
        public async Task<IActionResult> DeleteAnnualPlanMainGoal([FromBody] AnnualPlanMainGoalView mainGoalView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.DeleteAnnualPlanMainGoal(mainGoalView.AnnualPlanMainGoalID, mainGoalView.AnnualPlanMainID, session);
            return result.ToJson();
        }

        /// <summary>
        /// 检核年度计划目标
        /// </summary>
        /// <param name="mainGoalView">前端参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("CheckAnnualPlanMainGoal")]
        public async Task<IActionResult> CheckAnnualPlanMainGoal([FromBody] AnnualPlanMainGoalView mainGoalView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var checkResult = await _annualSettingService.CheckAnnualPlanMainGoal(mainGoalView);
            if (checkResult.Item1)
            {
                result.Data = checkResult.Item1;
            }
            else
            {
                result.Error(checkResult.Item2);
            }
            return result.ToJson();
        }
        #endregion
        #region 分类字典

        /// <summary>
        /// 获取年计划执行项目字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanTypeList")]
        public async Task<IActionResult> GetAnnualPlanTypeList(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.GetAnnualPlanTypeList(departmentID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取分类字典
        /// </summary>
        /// <param name="typeIds">分类集合</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetTypeList")]
        public async Task<IActionResult> GetTypeList([FromForm] int[] typeIds)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.GetTypeList(typeIds);
            return result.ToJson();
        }

        /// <summary>
        /// 保存年度计划分类
        /// </summary>
        /// <param name="annualPlanType">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualPlanTypeList")]
        public async Task<IActionResult> SaveAnnualPlanTypeList([FromBody] AnnualPlanTypeListView annualPlanType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.SaveAnnualPlanTypeList(session, annualPlanType);
            return result.ToJson();
        }
        /// <summary>
        /// 删除分类
        /// </summary>
        /// <param name="annualPlanType">分类信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteAnnualPlanType")]
        public async Task<IActionResult> DeleteAnnualPlanType([FromBody] AnnualPlanTypeListView annualPlanType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.DeleteAnnualPlanType(annualPlanType, session);
            return result.ToJson();
        }

        /// <summary>
        /// 获取年度计划分类（本部门及上级、间接上级部门分类）
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanTypeListByDepartment")]
        public async Task<IActionResult> GetAnnualPlanTypeListByDepartment(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualSettingService.GetAnnualPlanTypeListByDepartment(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 检核年度计划分类
        /// </summary>
        /// <param name="annualPlanType">分类信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("CheckAnnualPlanTypeList")]
        public async Task<IActionResult> CheckAnnualPlanTypeList([FromBody] AnnualPlanTypeListView annualPlanType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var checkResult = await _annualSettingService.CheckAnnualPlanTypeList(annualPlanType.AnnualPlanTypeID, session);
            if (checkResult.Item1)
            {
                result.Data = checkResult.Item1;
            }
            else
            {
                result.Error(checkResult.Item2);
            }
            return result.ToJson();
        }
        #endregion
    }
}
