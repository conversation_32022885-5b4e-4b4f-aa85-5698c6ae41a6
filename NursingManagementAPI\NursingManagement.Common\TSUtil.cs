﻿//using BJCA_TS_CLIENTCOMLib;
//using NLog;

//namespace NursingManagement.Common
//{
//    /// <summary>
//    /// 时间戳工具类
//    /// </summary>
//    public class TSUtil
//    {
//        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

//        /// <summary>
//        /// 获取时间戳数据
//        /// </summary>
//        /// <param name="originalData">需加时间戳的原文</param>
//        /// <returns></returns>
//        public static string GetTSData(string originalData)
//        {
//            #region 初始宣告

//            var isAttachCert = 0;
//            var tsResult = "";
//            BJCATSEngine tsClass = new BJCATSEngine();

//            #endregion 初始宣告

//            //获取时间戳
//            tsResult = GetTSResult(tsClass, originalData, isAttachCert);
//            if (string.IsNullOrEmpty(tsResult))
//            {
//                return tsResult;
//            }

//            //验证时间戳
//            var verifyTS = tsClass.VerifyTS(tsResult, originalData);
//            if (verifyTS != 0)
//            {
//                SaveVerifyTSLog(verifyTS);
//                return tsResult;
//            }

//            return tsResult;
//        }

//        #region 保存验证时间戳失败的日志

//        /// <summary>
//        /// 保存验证时间戳失败的日志
//        /// </summary>
//        /// <param name="verifyTS"></param>
//        private static void SaveVerifyTSLog(int verifyTS)
//        {
//            switch (verifyTS)
//            {
//                case -1:
//                    _logger.Error("时间戳验证不通过");
//                    break;

//                case -2:
//                    _logger.Error("原文验证不通过");
//                    break;

//                case -3:
//                    _logger.Error("不是所信任的根");
//                    break;

//                case -4:
//                    _logger.Error("证书未生效");
//                    break;

//                case -5:
//                    _logger.Error("查询不到此证书");
//                    break;

//                case -6:
//                    _logger.Error("签发时间戳时服务器证书过期");
//                    break;

//                default:
//                    _logger.Error("验证时间戳失败");
//                    break;
//            }
//        }

//        #endregion 保存验证时间戳失败的日志

//        #region 获取由时间戳请求产生的时间戳

//        /// <summary>
//        /// 获取由时间戳请求产生的时间戳
//        /// </summary>
//        /// <param name="tsClass"></param>
//        /// <param name="originalData"></param>
//        /// <param name="isAttachCert"></param>
//        /// <returns></returns>
//        private static string GetTSResult(BJCATSEngine tsClass, string originalData, int isAttachCert)
//        {
//            var tsRequest = tsClass.CreateTSRequest(originalData, isAttachCert);
//            if (tsRequest == null)
//            {
//                _logger.Error("创建时间戳请求失败");
//                return "";
//            }

//            var ts = tsClass.CreateTS(tsRequest);
//            if (ts == null)
//            {
//                _logger.Error("由时间戳请求产生时间戳失败");
//                return "";
//            }

//            return ts;
//        }

//        #endregion 获取由时间戳请求产生的时间戳
//    }
//}