﻿
using NursingManagement.Models;
namespace NursingManagement.Data.Interface
{
    public interface IPerpetualCalendarRepository : ICacheRepository
    {

        /// <summary>
        /// 获取万年历
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<PerpetualCalendarInfo>> GetCalendarByDate(DateTime startDate, DateTime endDate);
        /// <summary>
        /// 获取万年历公休日期
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<PerpetualCalendarInfo>> GetPublicLeaveByDate(DateTime startDate, DateTime endDate);
    }
}
