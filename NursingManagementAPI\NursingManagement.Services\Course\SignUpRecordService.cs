﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 培训报名服务
    /// </summary>
    public class SignUpRecordService : ISignUpRecordService
    {
        private readonly ISignUpRecordRepository _signUpRecordRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;

        public SignUpRecordService(
            ISignUpRecordRepository signUpRecordRepository,
            IUnitOfWork unitOfWork,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            ISettingDictionaryRepository settingDictionaryRepository
            )
        {
            _signUpRecordRepository = signUpRecordRepository;
            _unitOfWork = unitOfWork;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
        }

        /// <summary>
        /// 根据来源类型查询报名记录
        /// </summary>
        /// <param name="sourceType">来源类别</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns>报名信息视图模型列表</returns>
        public async Task<List<SignUpRecordView>> GetSignUpRecordListAsync(string sourceType, string sourceID)
        {
            var records = await _signUpRecordRepository.GetListByConditionAsNoTrackAsync(m => (string.IsNullOrEmpty(sourceType) || m.SourceType == sourceType)
                        && (string.IsNullOrEmpty(sourceID) || m.SourceID == sourceID) && m.StatusCode != "2");
            if (records.Count <= 0)
            {
                return [];
            }
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "Common",
                SettingTypeCode = "TrainingExaminationSignUp"
            };
            var signUpSettings =  await _settingDictionaryRepository.GetSettingDictionary(settingParams);

            var employeeIDs = records.Select(m => m.AddEmployeeID).Union(records.Select(m => m.EmployeeID)).Distinct();
            var employeeDict = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            return records.Select(record => MapToView(record, employeeDict, signUpSettings)).ToList();

        }
        /// <summary>
        /// 保存报名信息
        /// </summary>
        /// <param name="signUpRecord">报名信息</param>
        /// <param name="employeeID">操作人员ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveSignUpRecordAsync(SignUpRecordInfo signUpRecord, string employeeID)
        {
            if (string.IsNullOrEmpty(signUpRecord.SignUpRecordID))
            {
                signUpRecord.SignUpRecordID = signUpRecord.GetId();
                signUpRecord.SourceID ??="";
                signUpRecord.SourceType ??="";
                signUpRecord.StatusCode ??= "0";
                signUpRecord.SignUpType ??= "1";
                signUpRecord.EmployeeID ??= employeeID;
                signUpRecord.Add(employeeID);
                signUpRecord.Modify(employeeID);
                await _unitOfWork.GetRepository<SignUpRecordInfo>().InsertAsync(signUpRecord);
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }

            var signUpRecordInfo = await _signUpRecordRepository.GetByIDAsync(signUpRecord.SignUpRecordID);
            if (signUpRecord.StatusCode != null && signUpRecord.StatusCode != signUpRecordInfo.StatusCode)
            {
                signUpRecordInfo.StatusCode = signUpRecord.StatusCode;
                signUpRecordInfo.Modify(employeeID);
            }

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 删除报名信息
        /// </summary>
        /// <param name="signUpRecordID">报名记录ID</param>
        /// <param name="employeeID">操作人员ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteSignUpRcordAsync(string signUpRecordID, string employeeID)
        {
            if (string.IsNullOrEmpty(signUpRecordID))
            {
                _logger.Warn("删除报名记录失败,主键参数为空");
                return false;
            }
            var recordInfo = await _signUpRecordRepository.GetByIDAsync(signUpRecordID);
            if (recordInfo == null)
            {
                return false;
            }

            recordInfo.Delete(employeeID);

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 将实体模型映射到视图模型
        /// </summary>
        /// <param name="recordInfo">实体模型</param>
        /// <param name="employeeDict">员工信息字典</param>
        /// <returns>映射后的视图模型</returns>
        private static SignUpRecordView MapToView(SignUpRecordInfo recordInfo, Dictionary<string, string> employeeDict, List<Models.SettingDictionaryInfo> signUpSettings)
        {
            return new SignUpRecordView
            {
                SignUpRecordID = recordInfo.SignUpRecordID,
                SourceID = recordInfo.SourceID,
                SourceType = recordInfo.SourceType,
                SourceTypeDescription = GetSettingDescripiton(nameof(recordInfo.SourceType), recordInfo.SourceType),
                StatusCode = recordInfo.StatusCode,
                StatusDescription = recordInfo.StatusCode == "0" ? "未同意" : "同意",
                SignUpType = recordInfo.SignUpType,
                SignUpTypeDescription = GetSettingDescripiton(nameof(recordInfo.SignUpType), recordInfo.SignUpType),
                EmployeeID = recordInfo.EmployeeID,
                EmployeeName = employeeDict.TryGetValue(recordInfo.EmployeeID, out var name) ? name : recordInfo.EmployeeID,
                AddEmployeeName = employeeDict.TryGetValue(recordInfo.AddEmployeeID, out var addName) ? addName : recordInfo.AddEmployeeID,
                AddDateTime = recordInfo.AddDateTime,
            };
            //根据类型获取对应的描述信息
            string GetSettingDescripiton(string settingTypeValue, string settingValue) =>                  
                signUpSettings.Find(m => m.SettingTypeValue == settingTypeValue && m.SettingValue == settingValue)?.Description ?? "";
        }
    }
}

