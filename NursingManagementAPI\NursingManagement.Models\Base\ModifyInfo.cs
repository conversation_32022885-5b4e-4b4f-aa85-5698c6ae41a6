﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    public class ModifyInfo : BaseInfo
    {
        /// <summary>
        /// 修改人员
        /// </summary>   
        [Column(TypeName = "varchar(20)")]
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>  
        [Column(TypeName = "varchar(1)")]
        public string DeleteFlag { get; set; } = "";

        public ModifyInfo Modify(string modifyEmployeeID, DateTime? modifyDateTime = null)
        {
            ModifyDateTime = modifyDateTime ?? DateTime.Now;
            ModifyEmployeeID = modifyEmployeeID;
            return this;
        }
        /// <summary>
        /// 设置删除
        /// </summary>
        public void Delete(string modifyEmployeeID, DateTime? modifyDateTime = null)
        {
            Modify(modifyEmployeeID, modifyDateTime);
            DeleteFlag = "*";
        }
    }
}