﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class TrainingClassDetailRepository : ITrainingClassDetailRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public TrainingClassDetailRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据trainingClassMainID获取培训群组明细集合
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        public async Task<List<TrainingClassDetailInfo>> GetListByMainID(string trainingClassMainID)
        {
            return await _dbContext.TrainingClassDetailInfos.Where(m => m.DeleteFlag != "*" && m.TrainingClassMainID == trainingClassMainID).ToListAsync();
        }
        /// <summary>
        /// 根据trainingClassMainIDs获取培训群组明细集合
        /// </summary>
        /// <param name="trainingClassMainIDs"></param>
        /// <returns></returns>
        public async Task<List<TrainingClassDetailInfo>> GetListByMainIDs(List<string> trainingClassMainIDs)
        {
            return await _dbContext.TrainingClassDetailInfos.Where(m => m.DeleteFlag != "*" && trainingClassMainIDs.Contains(m.TrainingClassMainID)).ToListAsync();
        }
        /// <summary>
        /// 根据培训群组主记录ID获取对应的课程ID集合
        /// </summary>
        /// <param name="trainClassMainID">培训群组主记录ID</param>
        /// <returns></returns>
        public async Task<List<string>> GetCourseSettingIDs(string trainClassMainID)
        {
            return await _dbContext.TrainingClassDetailInfos.Where(m => m.TrainingClassMainID == trainClassMainID && m.DeleteFlag != "*").
                Select(m => m.CourseSettingID).ToListAsync();
        }
    }
}