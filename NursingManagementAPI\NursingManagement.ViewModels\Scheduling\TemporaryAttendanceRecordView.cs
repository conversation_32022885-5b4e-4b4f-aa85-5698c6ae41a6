﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 临时出勤记录视图
    /// </summary>
    public class TemporaryAttendanceRecordView
    {
        /// <summary>
        /// 临时出勤记录ID
        /// </summary>
        public int? TemporaryAttendanceRecordID { get; set; }
        /// <summary>
        /// 出勤人
        /// </summary>
        public string AttendanceEmployee { get; set; }
        /// <summary>
        /// 出勤人ID
        /// </summary>
        public string AttendanceEmployeeID { get; set; }
        /// <summary>
        /// 出勤日期
        /// </summary>
        public DateTime AttendanceDate { get; set; }
        /// <summary>
        /// 出勤小时
        /// </summary>
        public decimal AttendanceHours { get; set; }
        /// <summary>
        /// 备份
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 人员ID
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 临时出勤岗位
        /// </summary>
        public string TemporaryAttendanceName { get; set; }
        /// <summary>
        /// 临时出勤iconID
        /// </summary>
        public int? TemporaryAttendanceID { get; set; }
    }
}
