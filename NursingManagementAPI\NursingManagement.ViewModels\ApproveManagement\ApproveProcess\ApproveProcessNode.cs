﻿namespace NursingManagement.ViewModels
{
    public class ApproveProcessNode
    {
        /// <summary>
        /// 审批节点唯一码，Guid
        /// </summary>
        public string ApproveNodeID { get; set; }
        /// <summary>
        /// 审批节点名称
        /// </summary>
        public string ApproveNodeName { get; set; }
        /// <summary>
        /// 节点审批时限
        /// </summary>
        public int ApproveTimeLimit { get; set; }
        /// <summary>
        /// 下一节点ID
        /// </summary>
        public string NextNodeID { get; set; }
        /// <summary>
        /// 审批模式
        /// </summary>
        public string ApproveModel { get; set; }
        /// <summary>
        /// 节点明细
        /// </summary>
        public List<ApproveProcessNodeDetail> NodeDetails { get; set; }
        /// <summary>
        /// 当前节点是否被User标记为删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
