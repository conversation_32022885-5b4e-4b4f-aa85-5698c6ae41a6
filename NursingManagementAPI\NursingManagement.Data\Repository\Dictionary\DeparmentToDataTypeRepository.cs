﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DeparmentToDataTypeRepository : IDeparmentToDataTypeRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly IRedisService _redisService;
        public DeparmentToDataTypeRepository(
            NursingManagementDbContext db
            , IRedisService redisService
        )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var datas = await _redisService.GetOrCreateAsync(key, 0, async () =>
            {
                var result = await _nursingManagementDbContext.DeparmentToDataTypeInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.HospitalList.ToString();
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<DeparmentToDataTypeInfo>> GetAllDataAsync()
        {
            return await GetCacheAsync() as List<DeparmentToDataTypeInfo>;
        }
        /// <summary>
        /// 根据tableName和dataTypeKey获取对应数据
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="dataTypeKey"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetDataByTableNameAndKey(string tableName,string dataTypeKey)
        {
            var list =  await GetCacheAsync() as List<DeparmentToDataTypeInfo>;
            return list.Where(m => m.TableName == tableName && m.DataTypeKey == dataTypeKey).Select(m => new Dictionary<string, object>{
                {"departmentID",m.DeparmentID },
                {"formType",m.DataTypeValue }
            }).ToList();
        }
    }
}
