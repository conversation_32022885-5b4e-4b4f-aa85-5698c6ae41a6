﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.DependencyInjection;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 调班申请记录Service
    /// </summary>
    public class AdjustScheduleService : IAdjustScheduleService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAdjustScheduleRecordRepository _adjustScheduleRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IApproveRecordRepository _approveRecordRepository;
        private readonly IApproveProcessService _approveProcessService;
        private readonly ISettingDictionaryService _settingDictionaryService;
        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="adjustScheduleRecordRepository"></param>
        /// <param name="employeePersonalDataRepository"></param>
        /// <param name="departmentPostRepository"></param>
        /// <param name="departmentListRepository"></param>
        /// <param name="dictionaryService"></param>
        /// <param name="serviceProvider"></param>
        /// <param name="approveRecordRepository"></param>
        /// <param name="approveProcessService"></param>
        /// <param name="settingDictionaryService"></param>
        public AdjustScheduleService(
            IUnitOfWork unitOfWork
            , IAdjustScheduleRecordRepository adjustScheduleRecordRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IDepartmentPostRepository departmentPostRepository
            , IDepartmentListRepository departmentListRepository
            , IDictionaryService dictionaryService
            , IServiceProvider serviceProvider
            , IApproveRecordRepository approveRecordRepository
            , IApproveProcessService approveProcessService
            , ISettingDictionaryService settingDictionaryService

        )
        {
            _adjustScheduleRecordRepository = adjustScheduleRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentPostRepository = departmentPostRepository;
            _departmentListRepository = departmentListRepository;
            _unitOfWork = unitOfWork;
            _dictionaryService = dictionaryService;
            _serviceProvider = serviceProvider;
            _approveRecordRepository = approveRecordRepository;
            _approveProcessService = approveProcessService;
            _settingDictionaryService = settingDictionaryService;
        }

        /// <summary>
        /// 获取调班申请数据
        /// </summary>
        /// <param name="employeeID">员工工号</param>
        /// <param name="departmentID">部门科室ID</param>
        /// <param name="showDeptDataSwitch">是否显示全科数据</param>
        /// <returns>List<AdjustScheduleRecordView></returns>
        public async Task<List<AdjustScheduleRecordView>> GetAdjustScheduleRecordsAsync(string employeeID, int? departmentID, bool showDeptDataSwitch)
        {
            if (string.IsNullOrEmpty(employeeID)  || (departmentID == null && showDeptDataSwitch))
            {
                _logger.Info($"GetAdjustScheduleRecordsAsync方法参数为空[employeeID]={employeeID},[departmentID]={departmentID},[showDeptDataSwitch]={showDeptDataSwitch}");
                return default;
            }

            List<AdjustScheduleRecordInfo> recordInfos;
            recordInfos = showDeptDataSwitch
               ? await _adjustScheduleRecordRepository.GetListByDepartmentIDAsync(departmentID.Value, true)
               : await _adjustScheduleRecordRepository.GetListByEmployeeIDAsync(employeeID, true);
            if (recordInfos.Count <= 0)
            {
                return default;
            }
            //进行排序，保证前端逆序排列
            var orderedRecords = recordInfos.OrderByDescending(m => m.AdjustDate).ThenByDescending(m=>m.TargetDate).ThenByDescending(m=>m.AddDateTime).ToList();
            //实体数据转换成前端呈现数据
            var views = await GetAdjustScheduleRecordViewsAsync(orderedRecords);
            return views;
        }
        /// <summary>
        /// 根据数据实体数据转换成前端呈现数据
        /// </summary>
        /// <param name="recordInfos">调班申请数据</param>
        /// <returns></returns>
        private async Task<List<AdjustScheduleRecordView>> GetAdjustScheduleRecordViewsAsync(List<AdjustScheduleRecordInfo> recordInfos)
        {
            AdjustScheduleRecordView view;
            var views = new List<AdjustScheduleRecordView>();
            var deptPosts = await _departmentPostRepository.GetAll<DepartmentPostInfo>();
            var personDatas = await _employeePersonalDataRepository.GetAll<EmployeePersonalDataListView>();
            var settingDictionaryParams = new SettingDictionaryParams() {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveRecord",
                SettingTypeValue = "StatusCode"
            };
            var statusDict = await _settingDictionaryService.GetSettingDictionaryDict(settingDictionaryParams);
            var depts = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var approveRecordIDs = recordInfos.Select(m => m.ApproveRecordID).ToList();
            var revokeResonDict = await _approveRecordRepository.GetRevokeReasonsByRecordIDsAsync(approveRecordIDs);
            foreach (var recordInfo in recordInfos)
            {
                view = new AdjustScheduleRecordView
                {
                    AdjustScheduleRecordID = recordInfo.AdjustScheduleRecordID,
                    DepartmentID = recordInfo.DepartmentID,
                    HospitalID = recordInfo.HospitalID,
                    AdjustDate = recordInfo.AdjustDate,
                    ApproveRecordID = recordInfo.ApproveRecordID,
                    StatusCode = recordInfo.StatusCode,
                    TargetDate = recordInfo.TargetDate,
                    TargetEmployeeID = recordInfo.TargetEmployeeID,
                    Department = depts.Find(m => m.DepartmentID == recordInfo.DepartmentID)?.DepartmentContent,
                    TargetEmployee = personDatas.Find(m => m.EmployeeID == recordInfo.TargetEmployeeID)?.EmployeeName ?? recordInfo.TargetEmployeeID,
                    TargetDepartmentPost = recordInfo.TargetDepartmentPost ?? "",
                    AdjustDepartmentPost = recordInfo.AdjustDepartmentPost ?? "",
                    AddEmployeeID = recordInfo.AddEmployeeID,
                    AddEmployee = personDatas.Find(m => m.EmployeeID == recordInfo.AddEmployeeID)?.EmployeeName ?? recordInfo.AddEmployeeID,
                    Status = statusDict.Find(m=>m.Value.Equals(recordInfo.StatusCode))?.Label?.ToString(),
                    AddDateTime = recordInfo.AddDateTime,
                    Reason = recordInfo.Reason,
                    AdjustNoonType = recordInfo.AdjustNoonType,
                    AdjustNoonTypeName = recordInfo.AdjustNoonType == "1" ? "上午" : (recordInfo.AdjustNoonType == "2" ? "下午" : "全天"),
                    TargetNoonType = recordInfo.TargetNoonType,
                    TargetNoonTypeName = recordInfo.TargetNoonType == "1" ? "上午" : (recordInfo.TargetNoonType == "2" ? "下午" : "全天"),
                    AutoSchedule = recordInfo.AutoScheduleFlag.HasValue ? recordInfo.AutoScheduleFlag.Value : false,
                    RevokeReason = revokeResonDict.TryGetValue(recordInfo.ApproveRecordID, out var revokeReason) ? revokeReason : "",
                    ApproveFlag = revokeResonDict.ContainsKey(recordInfo.ApproveRecordID)
                };
                views.Add(view);
            }
            return views.OrderByDescending(m=>m.AddDateTime).ToList();
        }

        /// <summary>
        /// 保存调班申请数据
        /// </summary>
        /// <param name="paramView">保存数据</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工工号</param>
        /// <param name="departmentID">部门科室ID</param>
        /// <returns></returns>
        public async Task<SaveReponseView> SaveAdjustScheduleRecordAsync(AdjustScheduleParamView paramView, string hospitalID, string employeeID, int departmentID)
        {
            var result = new SaveReponseView
            {
                RecordSaveFlag = false,
                ApproveSaveFlag = false
            };
            if (paramView == null)
            {
                _logger.Warn(" 保存调班申请SaveAdjustScheduleRecordAsync方法报错，参数为空paramView=null");
                return result;
            }
            var record = await GetOrCreateRecordAsync(paramView, hospitalID, employeeID, departmentID);
            if (record == null)
            {
                return result;
            }
            var isSuccess = await _unitOfWork.SaveChangesAsync() > 0;
            result.RecordSaveFlag = isSuccess;
            if (!isSuccess)
            {
                return result;
            }
            result.ApproveSaveFlag = await CreateOrUpdateApprove(record);
            return result;
        }
        /// <summary>
        /// 创建或更新审批记录
        /// </summary>
        /// <param name="adjustScheduleRecordInfo"></param>
        /// <returns></returns>
        public async Task<bool> CreateOrUpdateApprove(AdjustScheduleRecordInfo adjustScheduleRecordInfo)
        {
            var approveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            ApproveMainAndDetailParamView view = await CreateApproveMainAndDetailParamViewAsync(adjustScheduleRecordInfo);
            var (approveProcessID, contentTemplate) = await _approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(view.ProveCategory, view.DepartmentID);
            if (string.IsNullOrWhiteSpace(adjustScheduleRecordInfo.AdjustScheduleRecordID))
            {
                view.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                return await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID);
            }
            else
            {
                var approveRecordInfo = await _approveRecordRepository.GetApproveRecordByRecordIDAsync(adjustScheduleRecordInfo.ApproveRecordID);
                if (approveRecordInfo == null)
                {
                    view.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                    if (!await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID).ConfigureAwait(false))
                    {
                        return false;
                    }
                }
                else 
                {
                    approveRecordInfo.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                    approveRecordInfo.Modify(view.AddEmployeeID);
                }
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 获取或创建调班申请主记录
        /// </summary>
        /// <param name="paramView"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        private async Task<AdjustScheduleRecordInfo> GetOrCreateRecordAsync(AdjustScheduleParamView paramView, string hospitalID, string employeeID, int departmentID)
        {
            AdjustScheduleRecordInfo record;
            if (string.IsNullOrWhiteSpace(paramView.AdjustScheduleRecordID))
            {
                record = await AddAdjustScheduleRecordAsync(paramView, hospitalID, employeeID, departmentID);
            }
            else
            {
                record = await _adjustScheduleRecordRepository.GetRecordByIDAsync(paramView.AdjustScheduleRecordID);
                if (record == null)
                {
                    return null;
                }
                var isUpdate = UpdateAdjustScheduleRecord(record, paramView, employeeID);
                if (!isUpdate)
                {
                    return null;
                }
            }

            return record;
        }
        /// <summary>
        /// 更新调班记录
        /// </summary>
        /// <param name="existedRecord">旧的调班申请记录</param>
        /// <param name="paramView">保存参数</param>
        /// <param name="employeeID">员工工号</param>
        /// <returns></returns>
        private bool UpdateAdjustScheduleRecord(AdjustScheduleRecordInfo existedRecord, AdjustScheduleParamView paramView, string employeeID)
        {
            var update = false;
            if (existedRecord.TargetEmployeeID != paramView.TargetEmployeeID)
            {
                update = true;
                existedRecord.TargetEmployeeID = paramView.TargetEmployeeID;
            }
            if (existedRecord.TargetDate != paramView.TargetDate)
            {
                update = true;
                existedRecord.TargetDate = paramView.TargetDate;
            }
            if (existedRecord.AdjustDate != paramView.AdjustDate)
            {
                update = true;
                existedRecord.AdjustDate = paramView.AdjustDate;
            }
            if (existedRecord.AdjustDepartmentPost != paramView.AdjustDepartmentPost)
            {
                update = true;
                existedRecord.AdjustDepartmentPost = paramView.AdjustDepartmentPost;
            }
            if (existedRecord.TargetDepartmentPost != paramView.TargetDepartmentPost)
            {
                update = true;
                existedRecord.TargetDepartmentPost = paramView.TargetDepartmentPost;
            }
            if (existedRecord.Reason != paramView.Reason)
            {
                update = true;
                existedRecord.Reason = paramView.Reason;
            }
            if (existedRecord.AdjustNoonType != paramView.AdjustNoonType)
            {
                update = true;
                existedRecord.AdjustNoonType = paramView.AdjustNoonType;
            }
            if (existedRecord.TargetNoonType != paramView.TargetNoonType)
            {
                update = true;
                existedRecord.TargetNoonType = paramView.TargetNoonType;
            }
            if (existedRecord.AutoScheduleFlag != paramView.AutoSchedule)
            {
                update = true;
                existedRecord.AutoScheduleFlag = paramView.AutoSchedule;
            }
            if (update)
            {
                existedRecord.Modify(employeeID);
            }
            return update;
        }

        /// <summary>
        /// 新增调班记录
        /// </summary>
        /// <param name="paramView">保存数据</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工工号</param>
        /// <param name="departmentID">部门科室ID</param>
        /// <returns></returns>
        private async Task<AdjustScheduleRecordInfo> AddAdjustScheduleRecordAsync(AdjustScheduleParamView paramView, string hospitalID, string employeeID, int departmentID)
        {
            var record = new AdjustScheduleRecordInfo
            {
                AddEmployeeID = employeeID,
                AdjustDate = paramView.AdjustDate,
                AdjustDepartmentPost = paramView.AdjustDepartmentPost,
                TargetEmployeeID = paramView.TargetEmployeeID,
                TargetDate = paramView.TargetDate,
                TargetDepartmentPost = paramView.TargetDepartmentPost,
                DepartmentID = departmentID,
                HospitalID = hospitalID,
                StatusCode = paramView.StatusCode ?? "0",
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                DeleteFlag = "",
                Reason = paramView.Reason,
                AdjustNoonType = paramView.AdjustNoonType,
                TargetNoonType = paramView.TargetNoonType,
                AutoScheduleFlag = paramView.AutoSchedule
            };
            record.AdjustScheduleRecordID = record.GetId();
            record.ApproveRecordID = record.GetId();
            await _unitOfWork.GetRepository<AdjustScheduleRecordInfo>().InsertAsync(record);

            return record;
        }
        /// <summary>
        /// 删除调班申请
        /// </summary>
        /// <param name="adjustScheduleRecordID">删除调班申请主键</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<bool> DeleteAdjustScheduleRecordAsync(string adjustScheduleRecordID, string employeeID)
        {
            var apparoveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            if (string.IsNullOrEmpty(adjustScheduleRecordID))
            {
                return false;
            }
            var record = await _adjustScheduleRecordRepository.GetRecordByIDAsync(adjustScheduleRecordID);
            if(record != null)
            {
                record.Delete(employeeID);
                await apparoveRecordService.StopApprovalAsync(record.AdjustScheduleRecordID, employeeID);
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            return false;
        }

        /// <summary>
        /// 创建请求审批所需参数
        /// </summary>
        /// <param name="adjustScheduleRecordInfo">调班申请记录</param>
        /// <returns></returns>
        private async Task<ApproveMainAndDetailParamView> CreateApproveMainAndDetailParamViewAsync(AdjustScheduleRecordInfo adjustScheduleRecordInfo)
        {
            var employeeData = await _employeePersonalDataRepository.GetDataByEmployeeIDs(new List<string> { adjustScheduleRecordInfo.AddEmployeeID, adjustScheduleRecordInfo.TargetEmployeeID });
            var autoSchedule = "否";
            if (adjustScheduleRecordInfo.AutoScheduleFlag.HasValue)
            {
                 autoSchedule = adjustScheduleRecordInfo.AutoScheduleFlag.Value ? "是" : "否";
            }
            return new ApproveMainAndDetailParamView
            {
                SourceID = adjustScheduleRecordInfo.AdjustScheduleRecordID,
                ApproveRecordID = adjustScheduleRecordInfo.ApproveRecordID,
                ProveCategory = "AA-020",
                DepartmentID = adjustScheduleRecordInfo.DepartmentID,
                AddEmployeeID = adjustScheduleRecordInfo.AddEmployeeID,
                StartDate = adjustScheduleRecordInfo.AdjustDate.ToString("yyyy-MM-dd"),
                EndDate = adjustScheduleRecordInfo.TargetDate.ToString("yyyy-MM-dd"),
                AdjustEmployeeName = employeeData.FirstOrDefault(m => m.Key == adjustScheduleRecordInfo.AddEmployeeID).Value,
                TargetEmployeeName = employeeData.FirstOrDefault(m => m.Key == adjustScheduleRecordInfo.TargetEmployeeID).Value,
                Reason = adjustScheduleRecordInfo.Reason,
                AdjustTimeOfDay = adjustScheduleRecordInfo.AdjustNoonType == "1" ? "上午" : (adjustScheduleRecordInfo.AdjustNoonType == "2" ? "下午" : "全天"),
                targetNoonType = adjustScheduleRecordInfo.TargetNoonType == "1" ? "上午" : (adjustScheduleRecordInfo.TargetNoonType == "2" ? "下午" : "全天"),
                AutoSchedule  = autoSchedule
            };
        }

    }
}
