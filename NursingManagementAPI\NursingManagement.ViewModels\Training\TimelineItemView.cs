﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 表示时间线上的一个项目。
    /// </summary>
    public class TimelineItemView
    {
        /// <summary>
        /// 项目的类型：卡片或文本。
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 项目的时间戳，通常以字符串形式表示。
        /// </summary>
        public string Timestamp { get; set; }

        /// <summary>
        /// 卡片类型项目的标题。
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 卡片类型项目的描述。
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 时间线节点类型：primary, success, warning, danger, info。
        /// </summary>
        public string NodeType { get; set; }
    }
}
