﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("TrainingClassDetail")]
    public class TrainingClassDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 培训群组明细表ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string TrainingClassDetailID { get; set; }

        /// <summary>
        /// 培训群组主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string TrainingClassMainID { get; set; }

        /// <summary>
        /// 课程ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string CourseSettingID { get; set; }
    }
}