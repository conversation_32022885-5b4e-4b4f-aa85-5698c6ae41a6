﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 审批流程仓储接口
    /// </summary>
    public interface IApproveProcessRepository
    {
        /// <summary>
        /// 查询审批流程列表
        /// </summary>
        /// <param name="approveProcessID">主键</param>
        /// <returns></returns>
        Task<ApproveProcessInfo> GetApproveProcessInfo(string approveProcessID);
        /// <summary>
        /// 查询Api
        /// </summary>
        /// <param name="approveProcessID">主键</param>
        /// <returns></returns>
        Task<string> GetProveCategoryByProcessID(string approveProcessID);
        /// <summary>
        /// 获取流程ID对应分类码
        /// </summary>
        /// <param name="processIDs">流程ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetProcessTypeCodeDictByProcessIDs(params string[] processIDs);
        /// <summary>
        /// 查询审批流程列表
        /// </summary>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        Task<List<ApproveProcess>> GetApproveProcessViews(string hospitalID);

        /// <summary>
        /// 根据分类码查询审批流程列表
        /// </summary>
        /// <param name="proveCategory">分类码</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetProcessIDsByProveCategory(string proveCategory);
        /// <summary>
        /// 更新审批流程表状态
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <param name="enable">启用/禁用</param>
        /// <param name="employeeID">工号</param>
        /// <param name="now">更新时间</param>
        /// <returns></returns>
        Task<bool> UpdateApproveProcessStatus(string approveProcessID, ApproveProcessStatusCode enable, string employeeID, DateTime now);
        /// <summary>
        /// 获取审批内容模板
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <returns></returns>
        Task<string> GetContentTemplateByProcessID(string approveProcessID);

        /// <summary>
        /// 根据审批类别码获取对应的审批流程ID
        /// </summary>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        Task<string> GetProcessIDByCategoryAsNoTrackAsync(string proveCategory);
        /// <summary>
        /// 获取审批流程的名称
        /// </summary>
        /// <returns></returns>
        Task<List<Tuple<string, string>>> GetAllProcessNameAsync();
        /// <summary>
        /// 获取所有已启用的审批流程
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetAllCategoryAndProcessID();
    }
}
