﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class TemporaryAttendanceRecordRepository : ITemporaryAttendanceRecordRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public TemporaryAttendanceRecordRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据科室ID获取数据
        /// </summary>
        /// <param name="departmentID">部门id</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<TemporaryAttendanceRecordInfo>> GetListByDepartmentID(int departmentID, DateTime startDate, DateTime endDate)
        {
            return await _dbContext.TemporaryAttendanceRecordInfos.Where(m => m.DepartmentID == departmentID && m.AttendanceDate >= startDate && m.AttendanceDate <= endDate && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主键ID获取数据
        /// </summary>
        /// <param name="recordID">记录ID</param>
        /// <returns></returns>
        public async Task<TemporaryAttendanceRecordInfo> GetDataByID(int recordID)
        {
            return await _dbContext.TemporaryAttendanceRecordInfos.FirstOrDefaultAsync(m => m.TemporaryAttendanceRecordID == recordID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据科室ID获取数据
        /// </summary>
        /// <param name="departmentID">部门id</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<TemporaryAttendanceRecordInfo>> GetListByEmployeeIDs(List<string> employeeIDs, int departmentID, DateTime startDate, DateTime endDate)
        {
            var list = await GetListByDepartmentID(departmentID, startDate, endDate);
            return list.Where(m => employeeIDs.Contains(m.AttendanceEmployeeID)).ToList();
        }
    }
}
