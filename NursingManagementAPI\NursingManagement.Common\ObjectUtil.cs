﻿namespace NursingManagement.Common
{
    /// <summary>
    /// 对象工具
    /// </summary>
    public static class ObjectUtil
    {
        #region 判断对象是否为空

        /// <summary>
        /// 判断对象是否为空，为空返回true
        /// </summary>
        /// <typeparam name="T">要验证的对象的类型</typeparam>
        /// <param name="data">要验证的对象</param>
        public static bool IsNullOrEmpty<T>(T data)
        {
            if (data == null)
            {
                return true;
            }
            //空串
            if (data.GetType() == typeof(String))
            {
                if (string.IsNullOrEmpty(data.ToString().Trim()))
                {
                    return true;
                }
            }
            //不为空
            return false;
        }

        /// <summary>
        /// 判断对象集合是否为空，为空返回true
        /// </summary>
        /// <typeparam name="T">要验证的对象的类型</typeparam>
        /// <param name="data">要验证的对象</param>
        public static bool IsNullOrEmpty<T>(List<T> data)
        {
            if (data == null)
            {
                return true;
            }
            //空集合
            if (data.Count <= 0)
            {
                return true;
            }
            //不为空
            return false;
        }

        /// <summary>
        /// 判断对象是否为空，为空返回true
        /// </summary>
        /// <param name="data">要验证的对象</param>
        public static bool IsNullOrEmpty(object data)
        {
            if (data == null)
            {
                return true;
            }
            //空串
            if (data.GetType() == typeof(String))
            {
                if (string.IsNullOrEmpty(data.ToString().Trim()))
                {
                    return true;
                }
            }
            //不为空
            return false;
        }

        #endregion 判断对象是否为空


        /// <summary>
        /// 根据类型字符串获取类型
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static Type GetTypeByString(string type)
        {
            switch (type.ToLower())
            {
                case "bool":
                    return Type.GetType("System.Boolean", true, true);
                case "byte":
                    return Type.GetType("System.Byte", true, true);
                case "char":
                    return Type.GetType("System.Char", true, true);
                case "decimal":
                    return Type.GetType("System.Decimal", true, true);
                case "double":
                    return Type.GetType("System.Double", true, true);
                case "float":
                    return Type.GetType("System.Single", true, true);
                case "int":
                    return Type.GetType("System.Int32", true, true);
                case "long":
                    return Type.GetType("System.Int64", true, true);
                case "short":
                    return Type.GetType("System.Int16", true, true);
                case "string":
                    return Type.GetType("System.String", true, true);
                case "date":
                case "datetime":
                    return Type.GetType("System.DateTime", true, true);
                case "object":
                    return Type.GetType("System.Object", true, true);
                case "list":
                    return Type.GetType("System.Collections.Generic.List`1", true, true);
                default:
                    return Type.GetType(type, true, true);
            }
        }
    }
}