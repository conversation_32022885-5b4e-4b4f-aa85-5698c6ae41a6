using Microsoft.AspNetCore.Http;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.file;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Services.Interface
{
    public interface IHierarchicalQCService
    {
        /// <summary>
        /// 新增修改质控主题
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        Task<bool> SubjectPlanSave(SubjectView saveView);
        /// <summary>
        /// 质控主题新增
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        Task<string> AddSubjectPlan(SubjectView saveView);
        /// <summary>
        ///主题复制
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        Task<bool> CopySubjectPlan(SubjectView saveView);
        /// <summary>
        ///  获取质控主题表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="employeeID">当前登录操作用户ID</param>
        /// <returns></returns>
        Task<List<SubjectView>> GetSubjectTableView(GetQCSubjectView searchView, string employeeID);
        /// <summary>
        /// 获取质控表单清单
        /// </summary>
        /// <returns></returns>
        Task<List<HierachicalQCFormView>> GetHierarchicalQCFormList();
        /// <summary>
        /// 获取质控考核结果主记录
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="employeeID">操作人工号</param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordTableView>> GetHierarchicalQCRecordList(GetQCRecordView searchView, string employeeID);
        /// <summary>
        /// 删除质控考核结果主记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteHierarchicalQCRecord(string recordID, string employeeID);
        /// <summary>
        /// 根据主记录ID获取质控考核结果维护记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCMainTableView>> GetHierarchicalQCMainList(string recordID, string hospitalID);
        /// <summary>
        /// 删除质控维护考核记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<bool> DeleteHierarchicalQCMain(string mainID);
        /// <summary>
        /// 获取被质控科室
        /// </summary>
        /// <param name="subjectID"></param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="formLevel">质控等级</param>
        /// <param name="organizationType">组织类别</param>
        /// <returns></returns>
        Task<List<AssignDepartmentView>> GetQCAssignDepartmentList(string subjectID, string employeeID, string formLevel, string organizationType = "1");
        /// <summary>
        /// 获取质控质控及审核人员
        /// </summary>
        /// <param name="formLevel">质控等级</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        Task<List<AssignEmployeeView>> GetQCAssignEmployeeList(string formLevel, string employeeID);
        /// <summary>
        /// 质控指派保存
        /// </summary>
        /// <param name="view"></param>
        /// <param name="employee"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<bool> SaveSubjectAssign(SubjectAssignView view, string employee, string hospitalID);
        /// <summary>
        /// 主题指派页面回显
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        Task<SubjectAssignView> GetSubjectAssignView(string subjectID);
        /// <summary>
        /// 删除质控主题
        /// </summary>
        /// <param name="hierarchicalQCSubjectID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteSubject(string hierarchicalQCSubjectID, string employeeID);
        /// <summary>
        /// 根据AssessListID获取对应的备注内容
        /// </summary>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        Task<List<KeyValueString>> GeHierarchicalQCRemarkAsync(int assessListID);
        /// <summary>
        /// 保存新增的质控备注内容
        /// </summary>
        /// <param name="remarkView">保存的质控备注参数</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">执行人工号</param>
        /// <returns></returns>
        Task<object> SaveHierarchicalQCRemarkAsync(HierarchicalQCRemarkView remarkView, string hospitalID, string employeeID);
        /// <summary>
        /// 获取质控评估明细|根据维护记录ID
        /// </summary>
        /// <param name="careMainID"></param>
        /// <param name="trackFlag">跟踪标记为true 满分禁止勾选</param>
        /// <returns></returns>
        Task<object> GetHierarchicalQCDetailsAsync(string careMainID, bool? trackFlag);
        /// <summary>
        /// 保存质控评估维护记录和明细内容
        /// </summary>
        /// <param name="mainAndDetailView">前端参数</param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        Task<string> SaveHierarchicalQCMainAndDetailsAsync(HierarchicalQCMainAndDetailView mainAndDetailView, string hospitalID, string employeeID, string nurseEmployeeID);
        /// <summary>
        /// 获取追踪考核表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCMainTableView>> GetTrackTableView(GetQCRecordView searchView, string employeeID);
        /// <summary>
        /// 根据条件获取主题下拉框数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCSubjectInfo>> GetSubjectOptions(QCSubjectSearchView searchView);
        /// <summary>
        /// 获取人员在质控表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        Task<List<NormalWorkingProcessControlTableView>> GetNormalWorkingTableData(GetQCSubjectView searchView);
        /// <summary>
        /// 停止级别质控审批
        /// </summary>
        /// <param name="hierarchicalMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> StopHierarchicalQCApprovalAsync(string hierarchicalMainID, string employeeID);
        /// <summary>
        /// 提交审批记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<SaveReponseView> SubmitForApprovalAsync(ApproveMainAndDetailParamView view);
        /// <summary>
        /// 获取质控字典表
        /// </summary>
        /// <returns></returns>
        Task<List<ComponentOptionView>> GetQCAssessList();
        /// <summary>
        /// 保存质控模板
        /// </summary>
        /// <param name="hierachicalQCFormView"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<bool> SaveQCForm(HierachicalQCFormView hierachicalQCFormView, string employeeID, string hospitalID, int language);
        /// <summary>
        /// 删除质控模板
        /// </summary>
        /// <param name="hierarchicalQCFormID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteQCForm(int hierarchicalQCFormID, string employeeID);
        /// <summary>
        /// 保存质控主题的质控模板
        /// </summary>
        /// <param name="subjectFormView"></param>
        /// <param name="employeeID"></param>
        Task<bool> SaveQCSubjectForm(SubjectFormView subjectFormView, string employeeID);
        /// <summary>
        /// 获取质控评估模板
        /// </summary>
        /// <param name="careMainID"></param>
        /// <param name="templateCode"></param>
        /// <param name="trackFlag"></param>
        /// <param name="computeGroupScore"></param>
        /// <returns></returns>
        Task<FormTemplateView> GetAssessContentView(string careMainID, string templateCode, bool? trackFlag, bool computeGroupScore = true);
        /// <summary>
        /// 节点式督导获取质控人员
        /// </summary>
        /// <param name="qcLevel"></param>
        /// <param name="yearMonth"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID">当前登陆人工号</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetQCEmployeeOptions(string qcLevel, string yearMonth, string hospitalID, string employeeID);
        /// <summary>
        /// 保存问题整改记录
        /// </summary>
        /// <param name="saveView">保存参数</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> SaveProblemRectificationData(ProblemRectificationView saveView, Session session);
        /// <summary>
        /// 确认整改
        /// </summary>
        /// <param name="hierarchicalQCMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> ConfirmRectification(string hierarchicalQCMainID, string employeeID);
        /// <summary>
        /// 上传质控管理主体维护文件
        /// </summary>
        /// <param name="formFile"></param>
        /// <param name="qCFileView"></param>
        /// <returns></returns>
        Task<bool> UploadHierarchicalReportAsync(IFormFile formFile, HierarchicalQCFileView qCFileView);
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="subjectID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        Task<bool> DeleteQCFileAsync(string subjectID, string userID);
        /// <summary>
        /// 获取评价和指导内容
        /// </summary>
        /// <param name="careMainID"></param>
        /// <returns>(guidance,improvement)</returns>
        Task<(string guidance, string improvement)> GetGuidanceAndImprovement(string careMainID);
        /// <summary>
        /// 获取主题下拉框options
        /// </summary>
        /// <param name="view"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<Dictionary<string, string>>> GetSubjectSelectOptions(GetQCSubjectView view, string employeeID);
        /// <summary>
        /// 获取评价标准
        /// </summary>
        /// <param name="templateCode">模板ID</param>
        /// <returns></returns>
        Task<Dictionary<int, string>> GetQuestionTitles(string templateCode);
        /// <summary>
        /// 获取主记录下的考核记录
        /// </summary>
        /// <param name="careMainID">主ID</param>
        /// <returns></returns>
        Task<List<HQcMainApproveView>> GetQcMainViews(string careMainID);
        /// <summary>
        /// 获取质控预览图片
        /// </summary>
        /// <param name="careMainID">主键</param>
        /// <param name="templateCode">模板编号</param>
        /// <returns></returns>
        Task<ImgPreviewList> GetPreviewImageAsync(string careMainID, string templateCode);
    }
}