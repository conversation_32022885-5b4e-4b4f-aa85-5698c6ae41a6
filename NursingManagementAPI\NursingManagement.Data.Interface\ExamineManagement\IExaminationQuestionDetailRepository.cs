﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IExaminationQuestionDetailRepository
    {
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="questionDetailID"></param>
        /// <returns></returns>
        Task<ExaminationQuestionDetailInfo> GetDataByID(string questionDetailID);
        /// <summary>
        /// 根据题目ID获取答案
        /// </summary>
        /// <param name="examinationQuestionID"></param>
        /// <returns></returns>
        Task<List<ExaminationQuestionDetailInfo>> GetListByQuestionID(int examinationQuestionID);
        /// <summary>
        /// 根据是否是正确答案获取答案
        /// </summary>
        /// <param name="answerFlag"></param>
        /// <returns></returns>
        Task<List<ExaminationQuestionDetailInfo>> GetListByCorrectFlag(bool answerFlag);
        /// <summary>
        /// 根据多个问题ID获取答案
        /// </summary>
        /// <param name="examinationQuestionIDs">多个问题的ID</param>
        /// <returns>字典，包含问题ID和对应的答案集合。</returns>
        Task<Dictionary<int, List<ItemDetailListView>>> GetAnswersByQuestionIDs(List<int> examinationQuestionIDs);
        /// <summary>
        /// 根据问题ID集合获取明细数据
        /// </summary>
        /// <param name="examinationQuestionIDs"></param>
        /// <param name="asNoTrack">是否跟踪</param>
        /// <returns></returns>
        Task<List<ExaminationQuestionDetailInfo>> GetListByQuestionIDs(List<int> examinationQuestionIDs, bool asNoTrack = false);
    }
}
