using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDepartmentToApproveProcessRepository
    {
        /// <summary>
        /// 获取流程ID对开启的适用科室ID列表
        /// </summary>
        /// <param name="onlyEnable">是否仅获取启用科室</param>
        /// <param name="processIDs">流程ID</param>
        /// <returns></returns>
        Task<Dictionary<string, int[]>> GetDepartmentIDsByProcessIDs(bool onlyEnable, params string[] processIDs);
        /// <summary>
        /// 更新启用时间
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <param name="employeeID">工号</param>
        /// <param name="now">启用时间</param>
        /// <returns></returns>
        Task<bool> UpdateEnableDateTime(string approveProcessID, string employeeID, DateTime now);
        /// <summary>
        /// 获取审批流程对应的科室数据集合
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <returns></returns>
        Task<List<DepartmentToApproveProcessInfo>> GetInfosByApproveProcessID(string approveProcessID);
        /// <summary>
        /// 更新停用时间
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <param name="employeeID">工号</param>
        /// <param name="now">停用时间</param>
        /// <returns></returns>
        Task<bool> UpdateDisableDateTime(string approveProcessID, string employeeID, DateTime now);
    }
}
