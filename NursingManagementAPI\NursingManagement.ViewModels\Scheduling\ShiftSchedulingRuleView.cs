using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 排班规则
    /// </summary>
    [Serializable]
    public class ShiftSchedulingRuleView
    {
        /// <summary>
        /// 排班规则配置记录ID
        /// </summary>
        public string ShiftSchedulingRuleID { get; set; }
        /// <summary>
        /// 部门编号
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 规则ID，关联SettingDictionary表的SettingValue字段
        /// </summary>
        public string RuleID { get; set; }
        /// <summary>
        /// 规则值
        /// </summary>
        public string RuleValue { get; set; }
        /// <summary>
        /// 规则说明
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 组件类型
        /// </summary>
        public string ComponentType { get; set; }
        /// <summary>
        /// 规则关联字典表的key
        /// </summary>
        public string RuleKey { get; set; }
    }
}
