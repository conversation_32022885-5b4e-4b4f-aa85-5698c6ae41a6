﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public class AnnualPlanInterventionDetailRepository : IAnnualPlanInterventionDetailRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public AnnualPlanInterventionDetailRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 获取年度计划-计划制定明细列表
        /// </summary>
        /// <param name="interventionMainIDs">执行项目主表ID集合</param>
        /// <returns></returns>
        public async Task<APInterventionMonthsView[]> GetDetailsByMainID(List<string> interventionMainIDs)
        {
            return await _dbContext.AnnualPlanInterventionDetailInfos.Where(x => interventionMainIDs.Contains(x.AnnualPlanInterventionMainID) && x.DeleteFlag != "*")
                .GroupBy(m => m.AnnualPlanInterventionMainID)
                .Select(m => new APInterventionMonthsView
                {
                    AnnualPlanInterventionMainID = m.Key,
                    PlanMonths = m.Select(n => n.PlanMonth).ToArray()
                }).ToArrayAsync();
        }
        /// <summary>
        /// 获取年度计划-计划制定明细列表
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<APInterventionMonthsView[]> GetDetailsByPlanMainID(string planMainID)
        {
            return await _dbContext.AnnualPlanInterventionDetailInfos.Where(x => planMainID == x.AnnualPlanMainID && x.DeleteFlag != "*")
                .GroupBy(m => m.AnnualPlanInterventionMainID)
                .Select(m => new APInterventionMonthsView
                {
                    AnnualPlanInterventionMainID = m.Key,
                    PlanMonths = m.Select(n => n.PlanMonth).ToArray()
                }).ToArrayAsync();
        }
        /// <summary>
        /// 获取年度计划-计划制定明细列表
        /// </summary>
        /// <param name="interventionMainIDs">计划制定主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionDetailInfo>> GetDetailsByInterventionMainIDs(params string[] interventionMainIDs)
        {
            return await _dbContext.AnnualPlanInterventionDetailInfos.Where(x => interventionMainIDs.Contains(x.AnnualPlanInterventionMainID) && x.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 依据年度计划ID获取部分字段
        /// </summary>
        /// <param name="annualPlanMainID">年度计划ID</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionDetailInfo>> GetPartByAnnualPlanMainID(string annualPlanMainID)
        {
            return await _dbContext.AnnualPlanInterventionDetailInfos.Where(x => x.AnnualPlanMainID == annualPlanMainID && x.DeleteFlag != "*")
                .Select(m => new AnnualInterventionDetailInfo
                {
                    PlanDate = m.PlanDate,
                    PlanMonth = m.PlanMonth,
                    AnnualPlanInterventionDetailID = m.AnnualPlanInterventionDetailID,
                    AnnualPlanInterventionMainID = m.AnnualPlanInterventionMainID,
                }).ToListAsync();
        }
        /// <summary>
        /// 获取不跟踪的执行项目明细集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionDetailInfo>> GetInfosByPlanMainIDAsNoTracking(string annualPlanMainID)
        {
            return await _dbContext.AnnualPlanInterventionDetailInfos.AsNoTracking().Where(m => m.AnnualPlanMainID == annualPlanMainID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
