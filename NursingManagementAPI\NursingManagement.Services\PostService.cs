﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Post;
using System.Data;

namespace NursingManagement.Services
{
    public class PostService : IPostService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly IPostDescriptionRepository _postDescriptionRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;
        private readonly IPostRepository _postRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentPostSettingRepository _departmentPostSettingRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IDepartmentPostToCapabilityLevelRepository _departmentPostToCapabilityLevelRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly IDepartmentPostWorkingTimeRepository _departmentPostWorkingTimeRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IOptions<SystemConfig> _config;
        /// <summary>
        /// 出差岗位
        /// </summary>
        private const int POST_ID_998 = 998;
        /// <summary>
        /// 下夜班岗位
        /// </summary>
        private const int POST_ID_999 = 999;

        public PostService(
            IUnitOfWork unitOfWork
            , NursingManagementDbContext dbContext
            , IPostDescriptionRepository postDescriptionRepository
            , IDepartmentPostRepository departmentPostRepository
            , IPostRepository postRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IDepartmentPostSettingRepository departmentPostSettingRepository
            , IDepartmentListRepository departmentListRepository
            , IDepartmentPostToCapabilityLevelRepository departmentPostToCapabilityLevelRepository
            , ICapabilityLevelRepository capabilityLevelRepository
            , IDepartmentPostWorkingTimeRepository departmentPostWorkingTimeRepository
            , IDictionaryService dictionaryService
            , ISettingDictionaryService settingDictionaryService
            , ISettingDictionaryRepository settingDictionaryRepository
            , IOptions<SystemConfig> config
        )
        {
            _unitOfWork = unitOfWork;
            _dbContext = dbContext;
            _postDescriptionRepository = postDescriptionRepository;
            _departmentPostRepository = departmentPostRepository;
            _postRepository = postRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentPostSettingRepository = departmentPostSettingRepository;
            _departmentListRepository = departmentListRepository;
            _departmentPostToCapabilityLevelRepository = departmentPostToCapabilityLevelRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
            _departmentPostWorkingTimeRepository = departmentPostWorkingTimeRepository;
            _dictionaryService = dictionaryService;
            _settingDictionaryService = settingDictionaryService;
            _settingDictionaryRepository = settingDictionaryRepository;
            _config = config;
        }

        public async Task<List<DepartmentPostView>> GetDepartmentPostsAsync(int departmentID)
        {
            var result = new List<DepartmentPostView>();
            var departmentPostList = await _departmentPostRepository.GetAsync(departmentID);
            if (departmentPostList.Count == 0)
            {
                return result;
            }
            var employeeIDs = departmentPostList.Select(m => m.AddEmployeeID).ToList();
            employeeIDs.AddRange(departmentPostList.Select(m => m.ModifyEmployeeID).ToList());
            employeeIDs = employeeIDs.Distinct().ToList();

            var postTimes = await _departmentPostWorkingTimeRepository.GetByDepartmenPostIDs(departmentPostList.Select(m => m.DepartmentPostID).Distinct().ToList());
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var posts = await _postRepository.GetByCacheAsync();
            PostInfo post;
            // 岗位类型
            var dictParams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "Post",
                SettingTypeValue = "PostType"
            };
            // 岗位类型
            var postTypeDict = await _settingDictionaryService.GetSettingDictionaryDict(dictParams);
            // 岗位性质
            var postNatureSettingParams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "PostNature",
                SettingTypeValue = "PostNature"
            };
            var postNatureSettings = await _settingDictionaryRepository.GetSettingDictionary(postNatureSettingParams);
            // 岗位班次
            var postShiftSettingParams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "PostShift",
            };
            var postShiftSettings = await _settingDictionaryRepository.GetSettingDictionary(postShiftSettingParams);
            // 半天岗计算考勤方式
            var halfDayAttendanceCalcParams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "HalfDayAttendanceCalc",
            };
            var halfDayAttendanceCalctSettings = await _settingDictionaryRepository.GetSettingDictionary(halfDayAttendanceCalcParams);
            foreach (var departmentPost in departmentPostList)
            {
                DepartmentPostView view = new(departmentPost);
                if (employees.TryGetValue(view.ModifyEmployeeID, out string modifyEmployeeID))
                {
                    view.ModifyPerson = modifyEmployeeID;
                }
                if (employees.TryGetValue(view.AddEmployeeID, out string addEmployeeID))
                {
                    view.AddPerson = addEmployeeID;
                }
                view.StatusCode = departmentPost.StatusCode;
                view.AttendanceDays = departmentPost.AttendanceDays;
                // 获取岗位班别名称
                if (!string.IsNullOrWhiteSpace(departmentPost.PostShiftID))
                {
                    view.PostShift = postShiftSettings.Find(m => m.SettingValue == departmentPost.PostShiftID)?.Description;
                }
                // 获取岗位名称、类型、性质
                post = posts.Find(m => m.PostID == departmentPost.PostID);
                if (post != null)
                {
                    view.PostName = post.PostName;
                    view.PostType = postTypeDict.Find(m => m.Value.ToString() == post.PostTypeID)?.Label;
                    view.PostNature = postNatureSettings.Find(m => m.SettingValue == post.PostNatureID)?.Description;
                }
                view.HalfDayAttendanceCalc = departmentPost.HalfDayAttendanceCalc;
                view.HalfDayAttendanceCalcDescription = halfDayAttendanceCalctSettings.Find(m=>m.SettingValue == departmentPost.HalfDayAttendanceCalc)?.Description;
                view.SummerTimeRange = GetPostTimeRange(postTimes, departmentPost.DepartmentPostID, "Summer");
                view.WinterTimeRange = GetPostTimeRange(postTimes, departmentPost.DepartmentPostID, "Winter");
                view.Color = departmentPost.Color;
                view.BackGroundColor = departmentPost.BackGroundColor;
                result.Add(view);
            }
            return result;
        }

        public async Task<bool> SavePostDescriptionList(List<PostDescriptionInfo> postDescriptionList)
        {
            await _unitOfWork.GetRepository<PostDescriptionInfo>().InsertAsync(postDescriptionList);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 获取科室岗位说明书
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<PostDescriptionView>> GetPostDescriptionInfosAsync(int departmentID, string hospitalID, int language)
        {
            var result = new List<PostDescriptionView>();
            var list = await _postDescriptionRepository.GetListByDeparment(departmentID, hospitalID, language);
            if (list.Count <= 0)
            {
                return result;
            }
            var posts = await _postRepository.GetByCacheAsync();
            var postdescriptionCodeList = list.Select(m => m.PostDescriptionCode).Distinct().ToList();
            if (postdescriptionCodeList.Count <= 0)
            {
                return result;
            }
            var postDescriptionList = new List<PostDescriptionInfo>();
            foreach (var coed in postdescriptionCodeList)
            {
                var varsionDescription = list.OrderByDescending(m => GetVersionString(m.Version)).FirstOrDefault(m => m.PostDescriptionCode == coed);
                postDescriptionList.Add(varsionDescription);
            }
            if (postDescriptionList.Count <= 0)
            {
                return result;
            }
            var employeeIDs = list.Select(m => m.AddEmployeeID.Trim()).ToList();
            employeeIDs.AddRange(list.Select(m => m.ModifyEmployeeID.Trim()).ToList());
            employeeIDs.AddRange(list.Where(m => !string.IsNullOrEmpty(m.Signer)).Select(m => m.Signer.Trim()).ToList());
            employeeIDs.AddRange(list.Where(m => !string.IsNullOrEmpty(m.Approver)).Select(m => m.Approver.Trim()).ToList());
            employeeIDs = employeeIDs.Distinct().ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var departMentList = await _departmentListRepository.GetByOrganizationType("1");
            var dictParams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "PostDescription",
                SettingTypeValue = "StatusCode"
            };
            var postDescStatusDict = await _settingDictionaryService.GetSettingDictionaryDict(dictParams);

            foreach (var item in postDescriptionList)
            {
                var view = new PostDescriptionView(item);
                view.Status = postDescStatusDict.Find(m => m.Value.ToString() == item.StatusCode)?.Label.ToString();
                if (employees.ContainsKey(view.ModifyEmployeeID)) view.ModifyPerson = employees[item.ModifyEmployeeID];
                if (employees.ContainsKey(view.AddEmployeeID)) view.AddPerson = employees[view.AddEmployeeID];
                if (!string.IsNullOrEmpty(view.Signer) && employees.ContainsKey(view.Signer)) view.SignerName = employees[item.Signer];
                if (!string.IsNullOrEmpty(view.Approver) && employees.ContainsKey(view.Approver)) view.ApproverName = employees[item.Approver];
                view.PostName = posts.Find(m => m.PostID == item.PostID)?.PostName;
                view.CreateDepartment = departMentList.FirstOrDefault(m => m.DepartmentID == view.CreateDepartmentID)?.DepartmentContent;
                result.Add(view);
            }
            return result.OrderBy(m=>m.PostDescriptionCode).ToList();
        }
        /// <summary>
        /// 版本号处理
        /// </summary>
        /// <param name="version"></param>
        /// <returns></returns>
        private string GetVersionString(string version)
        {
            var retStr = "";
            var strs = version.Split(".");
            foreach (var str in strs)
            {
                var newStr = str.PadLeft(5, '0');
                retStr += newStr;
            }
            return retStr;
        }
        public async Task<PostDescriptionInfo> GetPostDescriptionInfoAsync(int departmentID, int postID, string hospitalID, int language)
        {
            return await _postDescriptionRepository.GetData(departmentID, postID, hospitalID, language);
        }
        /// <summary>
        /// 更新岗位状态
        /// </summary>
        /// <param name="postInfo"></param>
        /// <returns></returns>
        public async Task<bool> UpdatePostStatus(UpdateDepartmentPostView updateDepartmentPostView)
        {
            if (updateDepartmentPostView == null || updateDepartmentPostView.DepartmentPosts?.Count <= 0)
            {
                return false;
            }
            foreach (var departmentPost in updateDepartmentPostView.DepartmentPosts)
            {
                var oldDepartmentPost = await _departmentPostRepository.GetDepartmentPostByID(departmentPost.DepartmentPostID);
                if (oldDepartmentPost == null)
                {
                    continue;
                }
                // 通过反射取值
                var updateFieldValue = ReflexUtil.GetPropertyValue(departmentPost, updateDepartmentPostView.Field);
                // 通过反射赋值
                ReflexUtil.SetProperty(oldDepartmentPost, updateDepartmentPostView.Field, updateFieldValue);
                oldDepartmentPost.Modify(updateDepartmentPostView.ModifyEmployeeID);
                //从缓存中获取的数据，要使用update
                _unitOfWork.GetRepository<DepartmentPostInfo>().Update(oldDepartmentPost);
            }
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            if (result)
            {
                await _departmentPostRepository.UpdateCache();
            }
            return result;
        }
        /// <summary>
        /// 获取科室岗位设置数据
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<object> GetDepartmentPostSettingAsync(int? departmentID)
        {
            var views = new List<DepartmentPostSettingView>();
            var cacheList = await _departmentPostSettingRepository.GetAllCacheAsync();
            if (cacheList.Count <= 0)
            {
                return views;
            }
            if (departmentID != null)
            {
                cacheList = cacheList.Where(m => m.DepartmentID == departmentID.Value)
                         .GroupBy(m => m.PostID)
                         .SelectMany(g => g.OrderBy(m => m.Type))
                         .ToList();
            }
            if (cacheList.Count <= 0)
            {
                return views;
            }
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "DepartmentPostSetting",
                SettingTypeValue = "Type"
            };
            var typeDict = await _settingDictionaryService.GetSettingDictionaryDict(settingDictionaryParams);
            var employeeIDs = cacheList.Select(m => m.AddEmployeeID).Union(cacheList.Select(m => m.ModifyEmployeeID)).Distinct().ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var posts = await _postRepository.GetByCacheAsync();
            List<DepartmentListInfo> departmentList = null;
            departmentList = departmentID.HasValue ?
                new List<DepartmentListInfo>
                {
                    await _departmentListRepository.GetByIDAsync(departmentID.Value)
                }
                : await _departmentListRepository.GetAll<DepartmentListInfo>();

            foreach (var item in cacheList)
            {
                var view = new DepartmentPostSettingView(item);

                view.AddPerson = employees.TryGetValue(item.AddEmployeeID, out var addPerson) ? addPerson : null;
                view.ModifyPerson = employees.TryGetValue(item.ModifyEmployeeID, out var modifyPerson) ? modifyPerson : null;
                view.DepartmentName = departmentList.Find(m => m.DepartmentID == item.DepartmentID)?.LocalShowName;
                view.PostName = posts.Find(m => m.PostID == item.PostID)?.PostName;
                view.TypeDesc = typeDict.Find(m => m.Value.Equals(item.Type))?.Label?.ToString();
                views.Add(view);
            }
            return views;
        }
        /// <summary>
        /// 保存部门岗位设置数据
        /// </summary>
        /// <param name="departmentPostSetting"></param>
        /// <returns></returns>
        public async Task<bool> SaveDepartmentPostSettingAsync(DepartmentPostSettingInfo departmentPostSetting, string hospitalID, string employeeID)
        {
            if (departmentPostSetting == null || departmentPostSetting.PostID == 0 || departmentPostSetting.DepartmentID == 0 || string.IsNullOrEmpty(departmentPostSetting.Type))
            {
                return false;
            }
            var departPostSetting = await _departmentPostSettingRepository.GetRecordIncludeDelete(departmentPostSetting.PostID, departmentPostSetting.DepartmentID, departmentPostSetting.Type);
            //新增
            if (departPostSetting == null)
            {
                departmentPostSetting.HospitalID ??= hospitalID;
                departmentPostSetting.Modify(employeeID);
                departmentPostSetting.Add(employeeID);
                departmentPostSetting.DeleteFlag = "";
                await _unitOfWork.GetRepository<DepartmentPostSettingInfo>().InsertAsync(departmentPostSetting);
            }
            //修改
            else
            {
                UpdateDepartmentPostSetting(departmentPostSetting, departPostSetting, employeeID);
            }
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await _departmentPostSettingRepository.UpdateCache();
            }
            return true;
        }
        /// <summary>
        /// 更新数据库中记录
        /// </summary>
        /// <param name="departmentPostSetting"></param>
        /// <param name="departPostSetting"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private static void UpdateDepartmentPostSetting(DepartmentPostSettingInfo departmentPostSetting, DepartmentPostSettingInfo departPostSetting, string employeeID)
        {
            if (departPostSetting.ExpectedCount != departmentPostSetting.ExpectedCount)
            {
                departPostSetting.ExpectedCount = departmentPostSetting.ExpectedCount;
            }
            if (departPostSetting.PublishDate != departmentPostSetting.PublishDate)
            {
                departPostSetting.PublishDate = departmentPostSetting.PublishDate;
            }
            if (departPostSetting.DeleteFlag == "*")
            {
                departPostSetting.DeleteFlag = "";
                departPostSetting.Add(employeeID);
            }

            departPostSetting.Modify(employeeID);
        }
        /// <summary>
        /// 根据联合主键删除记录
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteDepartmentPostSettingAsync(int postID, int departmentID, string employeeID, string type)
        {
            var departPostSetting = await _departmentPostSettingRepository.GetRecordByKeysAsync(postID, departmentID, type);
            if (departPostSetting == null)
            {
                return false;
            }
            departPostSetting.Delete(employeeID);

            var success = await _unitOfWork.SaveChangesAsync() > 0;
            if (success)
            {
                await _departmentPostSettingRepository.UpdateCache();
            }
            return success;
        }
        /// <summary>
        /// 根据参数获取部门岗位和能级对照关系
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="postID"></param>
        /// <returns></returns>
        public async Task<List<DepartmentPostToCapabilityLevelView>> GetDepartmentPostToCapabilityLevelsAsync(int? departmentID, int? postID)
        {
            var resultList = new List<DepartmentPostToCapabilityLevelView>();
            if (departmentID == null)
            {
                return resultList;
            }
            var list = await _departmentPostToCapabilityLevelRepository.GetByDepartmentIDAndPostIDAsync(departmentID.Value, postID);
            list = list.GroupBy(m => new { m.PostID, m.DepartmentID, m.CapabilityLevelID })
                .Select(m => m.OrderBy(m => m.StartDate).Last()).ToList();
            if (list.Count <= 0)
            {
                return resultList;
            }
            var capabilityLevels = await _capabilityLevelRepository.GetAll<CapabilityLevelInfo>();
            if (capabilityLevels == null)
            {
                return resultList;
            }
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "HierarchicalQC",
                SettingTypeCode = "HierarchicalQCForm",
                SettingTypeValue = "StatusCode"
            };
            var statusDict = await _settingDictionaryService.GetSettingDictionaryDict(settingDictionaryParams);
            //获取工号数据
            var employeeIDs = list.Select(m => m.AddEmployeeID).ToList();
            employeeIDs.AddRange(list.Select(m => m.ModifyEmployeeID).ToList());
            employeeIDs = employeeIDs.Distinct().ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);

            var posts = await _postRepository.GetByCacheAsync();
            var departs = await _departmentListRepository.GetAll<DepartmentListInfo>();

            foreach (var item in list)
            {
                resultList.Add(item: SetDepartmentPostToCapabilityLevelView(item, statusDict, employees, departs, capabilityLevels, posts));
            }
            return resultList.OrderBy(m => m.PostID).ThenBy(m => m.StartDate).ToList();
        }
        /// <summary>
        /// 根据查询出来的数据库实体生成View
        /// </summary>
        /// <param name="item"></param>
        /// <param name="statusDict"></param>
        /// <param name="employees"></param>
        /// <param name="departs"></param>
        /// <param name="capabilityLevels"></param>
        /// <param name="posts"></param>
        /// <returns></returns>
        private DepartmentPostToCapabilityLevelView SetDepartmentPostToCapabilityLevelView(DepartmentPostToCapabilityLevelInfo item, List<SelectOptionsView> statusDict, Dictionary<string, string> employees, List<DepartmentListInfo> departs, List<CapabilityLevelInfo> capabilityLevels, List<PostInfo> posts)
        {
            var view = new DepartmentPostToCapabilityLevelView(item);
            if (employees.ContainsKey(view.ModifyEmployeeID))
            {
                view.ModifyPerson = employees[item.ModifyEmployeeID];
            }
            if (employees.ContainsKey(view.AddEmployeeID))
            {
                view.AddPerson = employees[view.AddEmployeeID];
            }
            view.PostName = posts.Find(m => m.PostID == item.PostID)?.PostName;
            view.DepartmentName = departs.Find(m => m.DepartmentID == item.DepartmentID)?.DepartmentContent;
            view.Status = statusDict.Find(m => m.Value.ToString() == item.StatusCode)?.Label?.ToString();
            view.CapabilityLevel = capabilityLevels.Find(m => m.CapabilityLevelID == item.CapabilityLevelID)?.CapabilityLevelName;

            return view;
        }

        /// <summary>
        /// 新增和修改岗位能级对照关系处理
        /// </summary>
        /// <param name="view"></param>
        /// <param name="hospitalID">i医院累呗</param>
        /// <param name="employeeID">执行人工号</param>
        /// <returns></returns>
        public async Task<bool> SaveDepartmentPostToCapabilityLevelAsync(DepartmentPostToCapabilityLevelInfo view, string hospitalID, string employeeID)
        {
            if (view == null)
            {
                return false;
            }
            var result = true;
            bool saveChange;
            var record = await _departmentPostToCapabilityLevelRepository.GetByIDAsync(view.DepartmentPostToCapabilityLevelID);
            //修改
            if (record != null)
            {
                saveChange = UpdateDepartmentPostToCapabilityLevel(record, view, employeeID);
            }
            else
            {
                saveChange = true;
                //新增
                view.HospitalID ??= hospitalID;
                view.Add(employeeID);
                view.Modify(employeeID);
                view.DeleteFlag = "";
                await _unitOfWork.GetRepository<DepartmentPostToCapabilityLevelInfo>().InsertAsync(view);
            }
            if (saveChange)
            {
                result = await _unitOfWork.SaveChangesAsync() >= 0;
                await _departmentPostToCapabilityLevelRepository.UpdateCache();
            }

            return result;
        }
        /// <summary>
        /// 修改岗位与能级对照记录
        /// </summary>
        /// <param name="record"></param>
        /// <param name="view"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private bool UpdateDepartmentPostToCapabilityLevel(DepartmentPostToCapabilityLevelInfo record, DepartmentPostToCapabilityLevelInfo view, string employeeID)
        {
            var saveChange = false;
            if (record.StartDate != view.StartDate)
            {
                saveChange = true;
                record.StartDate = view.StartDate;
            }
            if (record.EndDate != view.EndDate)
            {
                saveChange = true;
                record.EndDate = view.EndDate;
            }
            if (record.StatusCode != view.StatusCode)
            {
                saveChange = true;
                record.StatusCode = view.StatusCode;
            }
            if (record.Condition != view.Condition)
            {
                saveChange = true;
                record.Condition = view.Condition;
            }
            if (record.CapabilityLevelID != view.CapabilityLevelID)
            {
                saveChange = true;
                record.CapabilityLevelID = view.CapabilityLevelID;
            }
            if (record.PostID != view.PostID)
            {
                saveChange = true;
                record.PostID = view.PostID;
            }
            if (!saveChange)
            {
                return false;
            }
            //因为record是从缓存获取的
            record.Modify(employeeID);
            _unitOfWork.GetRepository<DepartmentPostToCapabilityLevelInfo>().Update(record);
            return true;
        }

        /// <summary>
        /// 删除科室岗位与能级对照关系记录
        /// </summary>
        /// <param name="deleteView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteDepartmentPostToCapabilityLevelAsync(DeleteDepartmentPostToCapabilityLevelView deleteView, string employeeID)
        {
            var data = await _departmentPostToCapabilityLevelRepository.GetRecordByAllKeysAsync(deleteView.DepartmentID, deleteView.PostID, deleteView.CapabilityLevelID);
            if (data == null)
            {
                return false;
            }
            data.Delete(employeeID);
            _unitOfWork.GetRepository<DepartmentPostToCapabilityLevelInfo>().Update(data);
            var result = await _unitOfWork.SaveChangesAsync() > 0;
            if (result)
            {
                await _departmentPostToCapabilityLevelRepository.UpdateCache();
            }
            return result;
        }

        /// <summary>
        /// 获取岗位工作时间区间
        /// </summary>
        /// <param name="times"></param>
        /// <param name="deptPostID"></param>
        /// <param name="season"></param>
        /// <returns></returns>
        private static string GetPostTimeRange(List<DepartmentPostWorkingTimeInfo> times, int deptPostID, string season)
        {
            times = times.Where(m => m.DepartmentPostID == deptPostID && m.Season == season).OrderBy(m => m.Sort).ToList();
            return times.Count == 0 ? "" : string.Join("<br />", times.Select(m => m.StartTime.ToString(@"hh\:mm") + "-" + m.EndTime.ToString(@"hh\:mm")));
        }

        public async Task<List<int>> GetDepartmentPostIDsByCapabilityLevel(int departmentID, int capabilityLevelID)
        {
            var newPostIDs = new List<int>() { POST_ID_998, POST_ID_999 };
            var capabilityLevelList = await _capabilityLevelRepository.GetByCacheAsync();
            var capabilityLevel = capabilityLevelList.Find(m => m.CapabilityLevelID == capabilityLevelID);
            if (capabilityLevel == null)
            {
                return newPostIDs;
            }
            var departmentPostToCapabilityLevels = await _departmentPostToCapabilityLevelRepository.GetByDepartmentIDAsync(departmentID);
            var postIDs = departmentPostToCapabilityLevels.Select(m => m.PostID).Distinct().ToList();
            foreach (var postID in postIDs)
            {
                var flag = false;
                var postToCapabilityLevels = departmentPostToCapabilityLevels.Where(m => m.PostID == postID).ToList();
                foreach (var postLevel in postToCapabilityLevels)
                {
                    var capability = capabilityLevelList.Find(m => m.CapabilityLevelID == postLevel.CapabilityLevelID);
                    if (capabilityLevel == null || capability == null)
                    {
                        continue;
                    }
                    // 组装表达式
                    var expression = $"{capabilityLevel.Level}{postLevel.Condition}{capability.Level}";
                    // 计算表达式
                    var res = new DataTable().Compute(expression, "");
                    if (!(bool)res)
                    {
                        break;
                    }
                    flag = true;
                }
                if (flag)
                {
                    newPostIDs.Add(postID);
                }
            }
            var departmentPosts = await _departmentPostRepository.GetByPostIDs(departmentID, newPostIDs);
            return departmentPosts.Select(m => m.DepartmentPostID).ToList();
        }
        /// <summary>
        /// 保存岗位数据
        /// </summary>
        /// <param name="saveData"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<string> SaveDepartmentPost(SaveDepartmentPostView saveData, string hospitalID, int language)
        {
            string result = "";
            #region 配置加载
            var posts = await _postRepository.GetByCacheAsync();
            var post = posts.Find(m => m.PostID == saveData.PostID);
            if (post == null)
            {
                return result;
            }
            #endregion
            if (!saveData.DepartmentPostID.HasValue)
            {
                var departmentPostInfos = await SetDepartmentPost(posts, [saveData], hospitalID, language);
                var departmentPostInfo = departmentPostInfos.FirstOrDefault();
                var departmentP = departmentPostInfos.Where(m => m.DepartmentPostName == departmentPostInfo.DepartmentPostName && m.ShortName == departmentPostInfo.ShortName).OrderByDescending(m => m.AddDateTime).FirstOrDefault();
                if (departmentP == null)
                {
                    _logger.Warn($"未找到插入departmentPost表的数据，插入DepartmentPostWorkingTime表数据失败，PostID：{saveData.PostID}，DepartmentPostName{departmentPostInfo.DepartmentPostName}");
                    return result;
                }
                var insertInfos = new List<DepartmentPostWorkingTimeInfo>();

                var summerMorning = CreateDepartmentPostWorkingTimeInfo(hospitalID, departmentP.DepartmentPostID, "Summer", 1, saveData.SummerBeginTime1, saveData.SummerEndTime1, saveData.AddEmployeeID);
                if (summerMorning != null)
                {
                    insertInfos.Add(summerMorning);
                }
                var summerAfternoon = CreateDepartmentPostWorkingTimeInfo(hospitalID, departmentP.DepartmentPostID, "Summer", 2, saveData.SummerBeginTime2, saveData.SummerEndTime2, saveData.AddEmployeeID);
                if (summerAfternoon != null)
                {
                    insertInfos.Add(summerAfternoon);
                }
                var winterMorning = CreateDepartmentPostWorkingTimeInfo(hospitalID, departmentP.DepartmentPostID, "Winter", 1, saveData.WinterBeginTime1, saveData.WinterEndTime1, saveData.AddEmployeeID);
                if (winterMorning != null)
                {
                    insertInfos.Add(winterMorning);
                }
                var winterAfternoon = CreateDepartmentPostWorkingTimeInfo(hospitalID, departmentP.DepartmentPostID, "Winter", 2, saveData.WinterBeginTime2, saveData.WinterEndTime2, saveData.AddEmployeeID);
                if (winterAfternoon != null)
                {
                    insertInfos.Add(winterAfternoon);
                }

                result = "Add";
                await _unitOfWork.GetRepository<DepartmentPostWorkingTimeInfo>().InsertAsync(insertInfos);
            }
            else
            {
                var oldDepartmentPostInfo = await _departmentPostRepository.GetDepartmentPostByID(saveData.DepartmentPostID.Value);
                if (oldDepartmentPostInfo == null)
                {
                    return result;
                }

                UpdateDepartmentPost(oldDepartmentPostInfo, saveData, post);

                var postTimes = await _departmentPostWorkingTimeRepository.GetByDepartmenPostIDs(new List<int> { saveData.DepartmentPostID.Value });

                await UpdateWorkingTime(postTimes, "Summer", 1, saveData.SummerBeginTime1, saveData.SummerEndTime1, saveData.AddEmployeeID, hospitalID, oldDepartmentPostInfo.DepartmentPostID);
                await UpdateWorkingTime(postTimes, "Summer", 2, saveData.SummerBeginTime2, saveData.SummerEndTime2, saveData.AddEmployeeID, hospitalID, oldDepartmentPostInfo.DepartmentPostID);
                await UpdateWorkingTime(postTimes, "Winter", 1, saveData.WinterBeginTime1, saveData.WinterEndTime1, saveData.AddEmployeeID, hospitalID, oldDepartmentPostInfo.DepartmentPostID);
                await UpdateWorkingTime(postTimes, "Winter", 2, saveData.WinterBeginTime2, saveData.WinterEndTime2, saveData.AddEmployeeID, hospitalID, oldDepartmentPostInfo.DepartmentPostID);
                result = "modify";
            }
            //保存数据
            var ret = await _unitOfWork.SaveChangesAsync() >= 0;
            //更新缓存
            if (ret)
            {
                await _departmentPostRepository.UpdateCache();
                await _departmentPostWorkingTimeRepository.UpdateCache();
            }
            return result;
        }
        /// <summary>
        /// 更新岗位
        /// </summary>
        /// <param name="departmentPostInfo"></param>
        /// <param name="saveData"></param>
        /// <param name="post"></param>
        private static void UpdateDepartmentPost(DepartmentPostInfo departmentPostInfo, SaveDepartmentPostView saveData, PostInfo post)
        {
            departmentPostInfo.PostID = saveData.PostID;
            departmentPostInfo.DepartmentID = saveData.DepartmentID;
            departmentPostInfo.DepartmentPostName = post.PostName + (saveData.ShiftValue.HasValue ? saveData.ShiftValue.Value.ToString() : "");
            departmentPostInfo.ShortName = saveData.BriefName;
            departmentPostInfo.StatusCode = saveData.StatusCode;
            departmentPostInfo.DailyStatisticalMark = saveData.DailyStatisticalMark;
            departmentPostInfo.MonthlyStatisticalMark = saveData.MonthlyStatisticalMark;
            departmentPostInfo.ClinicalFlag = post.PostNatureID.Trim() == "1";
            departmentPostInfo.AttendanceDays = decimal.Parse(saveData.AttendanceDays);
            departmentPostInfo.HalfDayAttendanceCalc = saveData.HalfDayAttendanceCalc;
            departmentPostInfo.PostShiftID = saveData.PostShiftID;
            departmentPostInfo.Color = saveData.Color;
            departmentPostInfo.BackGroundColor = saveData.BackGroundColor;
            departmentPostInfo.Modify(saveData.AddEmployeeID);
        }
        /// <summary>
        /// 更新工作时间
        /// </summary>
        /// <param name="postTimes"></param>
        /// <param name="season"></param>
        /// <param name="sort"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="modifyEmployeeID"></param>
        private async Task UpdateWorkingTime(List<DepartmentPostWorkingTimeInfo> postTimes, string season, int sort, TimeSpan? startTime, TimeSpan? endTime
            , string modifyEmployeeID, string hospitalID, int departmentPostID)
        {
            var timeInfo = postTimes.Find(m => m.Season == season && m.Sort == sort);
            if (timeInfo != null)
            {
                timeInfo.StartTime = startTime.Value;
                timeInfo.EndTime = endTime.Value;
                timeInfo.CrossDayFlag = endTime < startTime;
                timeInfo.Modify(modifyEmployeeID);
                _unitOfWork.GetRepository<DepartmentPostWorkingTimeInfo>().Update(timeInfo);
            }
            if (timeInfo == null && startTime.HasValue && endTime.HasValue)
            {
                var workingTime = CreateDepartmentPostWorkingTimeInfo(hospitalID, departmentPostID, season, sort, startTime, endTime, modifyEmployeeID);
                await _unitOfWork.GetRepository<DepartmentPostWorkingTimeInfo>().InsertAsync(workingTime);
            }
        }
        /// <summary>
        /// 创建岗位时间表实体
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="departmentPostID"></param>
        /// <param name="season"></param>
        /// <param name="sort"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="addEmployeeID"></param>
        /// <returns></returns>
        private static DepartmentPostWorkingTimeInfo CreateDepartmentPostWorkingTimeInfo(string hospitalID, int departmentPostID, string season, int sort, TimeSpan? startTime
            , TimeSpan? endTime, string addEmployeeID)
        {
            if (!startTime.HasValue || !endTime.HasValue)
            {
                return null;
            }
            return new DepartmentPostWorkingTimeInfo
            {
                HospitalID = hospitalID,
                DepartmentPostID = departmentPostID,
                Season = season,
                Sort = sort,
                StartTime = startTime.Value,
                EndTime = endTime.Value,
                AddEmployeeID = addEmployeeID,
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = addEmployeeID,
                ModifyDateTime = DateTime.Now,
                DeleteFlag = "",
                CrossDayFlag = endTime < startTime
            };
        }
        /// <summary>
        /// 写入数据至DepartmentPost表
        /// </summary>
        /// <param name="posts"></param>
        /// <param name="saveDatas"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        private async Task<List<DepartmentPostInfo>> SetDepartmentPost(List<PostInfo> posts, List<SaveDepartmentPostView> saveDatas, string hospitalID, int language)
        {
            var departmentPostInfos = new List<DepartmentPostInfo>();
            var autoCount = await _departmentPostRepository.GetMaxID();
            foreach (var saveData in saveDatas)
            {
                var post = posts.Find(m => m.PostID == saveData.PostID);
                if (post == null)
                {
                    continue;
                }
                autoCount++;
                var departmentPost = new DepartmentPostInfo
                {
                    DepartmentPostID = autoCount,
                    HospitalID = hospitalID,
                    Language = language,
                    PostID = saveData.PostID,
                    DepartmentID = saveData.DepartmentID,
                    ShortName = saveData.BriefName,
                    StatusCode = saveData.StatusCode,
                    DailyStatisticalMark = saveData.DailyStatisticalMark,
                    MonthlyStatisticalMark = saveData.MonthlyStatisticalMark,
                    Sort = autoCount,
                    AddEmployeeID = saveData.AddEmployeeID,
                    AddDateTime = DateTime.Now,
                    ModifyEmployeeID = saveData.AddEmployeeID,
                    ModifyDateTime = DateTime.Now,
                    DeleteFlag = "",
                    DepartmentPostName = post.PostName + ((saveData.ShiftValue.HasValue) ? saveData.ShiftValue.Value.ToString() : ""),
                    ClinicalFlag = post.PostNatureID.Trim() == "1",
                    AttendanceDays = decimal.Parse(saveData.AttendanceDays),
                    HalfDayAttendanceCalc = saveData.HalfDayAttendanceCalc,
                    PostShiftID = saveData.PostShiftID,
                    Color = saveData.Color,
                    BackGroundColor = saveData.BackGroundColor,
                };
                departmentPostInfos.Add(departmentPost);
            }
            await _unitOfWork.GetRepository<DepartmentPostInfo>().InsertAsync(departmentPostInfos);
            return departmentPostInfos;
        }
        /// <summary>
        ///批量插入岗位说明数据
        /// </summary>
        /// <param name="saveDatas"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<bool> BatchSaveDepartmentPost(List<SaveDepartmentPostView> saveDatas, string hospitalID, int language)
        {
            if (saveDatas == null || saveDatas.Count == 0)
            {
                return false;
            }
            #region 配置加载
            var posts = await _postRepository.GetByCacheAsync();
            #endregion
            var departmentPostInfos = await SetDepartmentPost(posts, saveDatas, hospitalID, language);
            foreach (var saveData in saveDatas)
            {
                var post = posts.Find(m => m.PostID == saveData.PostID);
                if (post == null)
                {
                    continue;
                }
                var departmentP = departmentPostInfos.Find(m => m.DepartmentPostName == (post.PostName + ((saveData.ShiftValue.HasValue) ? saveData.ShiftValue.Value.ToString() : "")) && m.ShortName == saveData.BriefName);
                if (departmentP == null)
                {
                    continue;
                }
                var timePeriods = new[]
                {
                    new { Season = "Summer", Order = 1, BeginTime = saveData.SummerBeginTime1, EndTime = saveData.SummerEndTime1 },
                    new { Season = "Summer", Order = 2, BeginTime = saveData.SummerBeginTime2, EndTime = saveData.SummerEndTime2 },
                    new { Season = "Winter", Order = 1, BeginTime = saveData.WinterBeginTime1, EndTime = saveData.WinterEndTime1 },
                    new { Season = "Winter", Order = 2, BeginTime = saveData.WinterBeginTime2, EndTime = saveData.WinterEndTime2 }
                };

                var insertInfos = timePeriods
                    .Select(tp => CreateDepartmentPostWorkingTimeInfo(hospitalID, departmentP.DepartmentPostID, tp.Season, tp.Order, tp.BeginTime, tp.EndTime, saveData.AddEmployeeID))
                    .Where(info => info != null)
                    .ToList();

                await _unitOfWork.GetRepository<DepartmentPostWorkingTimeInfo>().InsertAsync(insertInfos);
            }
            var flag = await _unitOfWork.SaveChangesAsync() >= 0;
            //更新缓存
            if (flag)
            {
                await _departmentPostRepository.UpdateCache();
                await _departmentPostWorkingTimeRepository.UpdateCache();
            }
            return flag;
        }
        /// <summary>
        /// 批量保存部门岗位设置数据
        /// </summary>
        /// <param name="departmentPostSettings"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> BatchSaveDepartmentPostSettingAsync(List<DepartmentPostSettingInfo> departmentPostSettings, string hospitalID, string employeeID)
        {
            if (departmentPostSettings.Count == 0)
            {
                return false;
            }
            var departPostSettings = await _departmentPostSettingRepository.GetAllCacheAsync();
            foreach (var item in departmentPostSettings)
            {
                if (item.PostID == 0 || item.DepartmentID == 0 || string.IsNullOrEmpty(item.Type))
                {
                    continue;
                }
                var departPostSetting = departPostSettings.Find(m => m.PostID == item.PostID && m.DepartmentID == item.DepartmentID && m.Type == item.Type);
                if (departPostSetting == null)
                {
                    item.HospitalID ??= hospitalID;
                    item.Modify(employeeID);
                    item.Add(employeeID);
                    item.DeleteFlag = "";
                    await _unitOfWork.GetRepository<DepartmentPostSettingInfo>().InsertAsync(item);
                }
                else
                {
                    UpdateDepartmentPostSetting(item, departPostSetting, employeeID);
                    _unitOfWork.GetRepository<DepartmentPostSettingInfo>().Update(departPostSetting);
                }
            }
            var flag = await _unitOfWork.SaveChangesAsync() >= 0;
            if (flag)
            {
                await _departmentPostSettingRepository.UpdateCache();
            }
            return flag;
        }
        /// 批量保存岗位能级数据
        /// </summary>
        /// <param name="bodyView"></param>
        /// <param name="hospitalID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<ResponseResult> SaveDepartmentPostToCapabilityLevelListAsync(List<DepartmentPostToCapabilityLevelInfo> bodyView, string hospitalID, string employeeID)
        {
            var result = new ResponseResult
            {
                Code = 1,
                Data = false,
            };
            if (bodyView == null || bodyView.Count <= 0)
            {
                result.Data = false;
                result.Message = "导入数据无内容";
                return result;
            }
            var idList = bodyView.Where(m => m.DepartmentPostToCapabilityLevelID != 0)
                .Select(m => m.DepartmentPostToCapabilityLevelID).ToList();
            var departmentPostToCapabilityLevelList =
                await _departmentPostToCapabilityLevelRepository.GetByIDsAsync(idList);
            var count = 0;
            bool saveChange = false;
            DepartmentPostToCapabilityLevelInfo departmentPostToCapabilityInfo = null;
            foreach (var view in bodyView)
            {
                if (view == null)
                {
                    continue;
                }
                departmentPostToCapabilityInfo = null;
                //比对数据是否有效
                var flag = CheckDepartmentPostToCapabilityLevelInfo(view);
                if (!flag)
                {
                    result.Data = false;
                    result.Message = "第" + count + "行数据中'能级条件'输入错误";
                    return result;
                }
                count++;
                if (view.DepartmentPostToCapabilityLevelID != 0)
                {
                    departmentPostToCapabilityInfo = departmentPostToCapabilityLevelList.Find(m => m.DepartmentPostToCapabilityLevelID == view.DepartmentPostToCapabilityLevelID);
                }
                //修改
                if (departmentPostToCapabilityInfo != null)
                {
                    saveChange = UpdateDepartmentPostToCapabilityLevel(departmentPostToCapabilityInfo, view, employeeID);
                }
                else
                {
                    saveChange = true;
                    //新增
                    view.HospitalID ??= hospitalID;
                    view.Add(employeeID).Modify(employeeID);
                    view.DeleteFlag = "";
                    await _unitOfWork.GetRepository<DepartmentPostToCapabilityLevelInfo>().InsertAsync(view);
                }
            }
            if (saveChange)
            {
                result.Data = await _unitOfWork.SaveChangesAsync() > 0;
                await _departmentPostToCapabilityLevelRepository.UpdateCache();
                return result;
            }
            return result;

        }

        private bool CheckDepartmentPostToCapabilityLevelInfo(DepartmentPostToCapabilityLevelInfo bodyView)
        {
            if (bodyView.Condition == ">=" || bodyView.Condition == "<=")
            {
                return true;
            }
            return false;
        }
        /// <summary>
        ///  删除岗位说明书数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> DeletePostDescription(DeletePostDescriptionView view)
        {
            if (view.PostID == 0 || string.IsNullOrEmpty(view.PostDescriptionCode) || view.DepartmentID == 0)
            {
                return false;
            }
            var postDescriptionData = await _postDescriptionRepository.GetByCode(view.DepartmentID, view.PostID, view.PostDescriptionCode, view.Version, view.HospitalID, view.Language);
            if (postDescriptionData == null)
            {
                return false;
            }
            postDescriptionData.DeleteFlag = "*";
            postDescriptionData.Modify(view.ModifyEmployeeID);
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 检核数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> CheckPostDescriptionData(SavePostDescriptionView view)
        {
            var postDescriptionData = await _postDescriptionRepository.GetByCode(view.DepartmentID, view.PostID, view.PostDescriptionCode, view.Version, view.HospitalID, view.Language);
            if (postDescriptionData != null && postDescriptionData.StatusCode == "0" && view.StatusCode == postDescriptionData.StatusCode)
            {
                return false;
            }
            return true;
        }
        /// <summary>
        /// 保存岗位说明书明细内容
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> SavePostDescription(SavePostDescriptionView view)
        {
            var postDescriptionData = await _postDescriptionRepository.GetByCode(view.DepartmentID, view.PostID, view.PostDescriptionCode, view.Version, view.HospitalID, view.Language);
            var deletePostDescriptionData = await _postDescriptionRepository.GetDeleteByCode(view.DepartmentID, view.PostID, view.PostDescriptionCode, view.HospitalID, view.Language);
            if (deletePostDescriptionData != null)
            {
                _unitOfWork.GetRepository<PostDescriptionInfo>().Delete(deletePostDescriptionData);
            }
            var posts = await _postRepository.GetByCacheAsync();
            if (string.IsNullOrEmpty(view.PostDescriptionName))
            {
                view.PostDescriptionName = posts.Find(m => m.PostID == view.PostID)?.PostName;
            }
            if (postDescriptionData == null)
            {
                await InserPostDescription(view);
            }
            else
            {
                bool updateFlag = false;
                if (postDescriptionData.Version == view.Version && postDescriptionData.StatusCode == "0" && view.StatusCode != "0")
                {
                    ModifyPostDescription(postDescriptionData, view);
                    updateFlag = true;
                }
                if (postDescriptionData.Version == view.Version && postDescriptionData.StatusCode != "0" && view.StatusCode == "0")
                {
                    ModifyPostDescription(postDescriptionData, view);
                    updateFlag = true;
                }
                postDescriptionData.Modify(view.ModifyEmployeeID);
                if (postDescriptionData.Version == view.Version && !updateFlag)
                {
                    postDescriptionData.StatusCode = "0";
                    var versionArr = view.Version.Split(".");
                    var version = "";
                    for (int i = 0; i < versionArr.Length; i++)
                    {
                        if (i == versionArr.Length - 1)
                        {
                            int.TryParse(versionArr[i], out int lastNumber);
                            var lastData = lastNumber + 1;
                            version = version + lastData;
                        }
                        else
                        {
                            version = version + versionArr[i] + ".";
                        }
                    }
                    view.Version = version;
                    view.StatusCode = view.StatusCode;
                }
                if (!updateFlag)
                {
                    await InserPostDescription(view);
                }
            }
            if (view.BatcFlag)
            {
                return true;
            }
            try
            {
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
            }
            return false;
        }
        /// <summary>
        /// 批量保存岗位说明书数据
        /// </summary>
        /// <param name="list"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<bool> BatchSavePostDescription(List<SavePostDescriptionView> list, string hospitalID, int language)
        {
            var batchSaveFlag = true;
            var employeeNameList = new List<string>();
            employeeNameList = list.Where(m => !string.IsNullOrEmpty(m.ApproverName)).Select(m => m.ApproverName).ToList();
            employeeNameList.AddRange(list.Where(m => !string.IsNullOrEmpty(m.SignerName)).Select(m => m.SignerName).ToList());
            employeeNameList.AddRange(list.Where(m => !string.IsNullOrEmpty(m.AddEmployee)).Select(m => m.AddEmployee).ToList());
            employeeNameList.AddRange(list.Where(m => !string.IsNullOrEmpty(m.ModifyPerson)).Select(m => m.ModifyPerson).ToList());
            var departmentList = await _departmentListRepository.GetByNameList(list.Where(m => !string.IsNullOrEmpty(m.CreateDepartment)).Select(m => m.CreateDepartment).ToList(), "1");
            var employeeList = await _employeePersonalDataRepository.GetListByNameList(employeeNameList);
            foreach (var postDescriptionView in list)
            {
                postDescriptionView.HospitalID = hospitalID;
                postDescriptionView.Language = language;
                if (departmentList != null && departmentList.Count > 0)
                {
                    var departmenData = departmentList.FirstOrDefault(m => m.DepartmentContent.Contains(postDescriptionView.CreateDepartment));
                    postDescriptionView.CreateDepartmentID = departmenData != null ? departmenData.DepartmentID : 0;
                }
                if (employeeList != null && employeeList.Count > 0)
                {
                    postDescriptionView.Approver = employeeList.FirstOrDefault(m => m.EmployeeName.Contains(postDescriptionView.ApproverName))?.EmployeeID;
                    postDescriptionView.Signer = employeeList.FirstOrDefault(m => m.EmployeeName.Contains(postDescriptionView.SignerName))?.EmployeeID;
                    postDescriptionView.ModifyEmployeeID = postDescriptionView.ModifyEmployeeID;
                    postDescriptionView.AddEmployeeID = postDescriptionView.AddEmployeeID;
                }
                postDescriptionView.BatcFlag = true;
                if (!await SavePostDescription(postDescriptionView))
                {
                    batchSaveFlag = false;
                }
            }
            if (!batchSaveFlag)
            {
                return false;
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 向岗位说明书Info中插入数据
        /// </summary>
        /// <param name="view"></param>
        private async Task InserPostDescription(SavePostDescriptionView view)
        {
            var postDescriptionInfo = new PostDescriptionInfo()
            {
                PostID = view.PostID,
                DepartmentID = view.DepartmentID,
                HospitalID = view.HospitalID,
                Language = view.Language,
                PostDescriptionCode = view.PostDescriptionCode,
                PostDescriptionName = view.PostDescriptionName,
                Superiors = view.Superiors,
                Junior = view.Junior,
                PostNumber = view.PostNumber,
                HeadCount = view.HeadCount,
                CreateDepartmentID = view.CreateDepartmentID,
                Version = view.Version,
                StatusCode = view.StatusCode,
                Approver = view.Approver,
                ApproveDateTime = view.ApproveDateTime,
                Signer = view.Signer,
                SignDateTime = view.SignDateTime,
                QualificationRequirements = view.QualificationRequirements,
                EducationalLevel = view.EducationalLevel,
                TrainingRecord = view.TrainingRecord,
                Other = view.Other,
                Responsibility = view.Responsibility,
                PerformanceEvaluation = view.PerformanceEvaluation,
                SOP = view.SOP,
                AddEmployeeID = view.AddEmployeeID,
                AddDateTime = view.AddDateTime,
                ModifyEmployeeID = view.ModifyEmployeeID,
                ModifyDateTime = view.ModifyDateTime,
                DeleteFlag = "",
            };
            await _unitOfWork.GetRepository<PostDescriptionInfo>().InsertAsync(postDescriptionInfo);
        }
        /// <summary>
        /// 更新岗位说明书数据
        /// </summary>
        /// <param name="postDescriptionData"></param>
        /// <param name="view"></param>
        private static void ModifyPostDescription(PostDescriptionInfo postDescriptionData, SavePostDescriptionView view)
        {
            postDescriptionData.PostID = view.PostID;
            postDescriptionData.DepartmentID = view.DepartmentID;
            postDescriptionData.PostDescriptionName = view.PostDescriptionName;
            postDescriptionData.Superiors = view.Superiors;
            postDescriptionData.Junior = view.Junior;
            postDescriptionData.PostNumber = view.PostNumber;
            postDescriptionData.HeadCount = view.HeadCount;
            postDescriptionData.CreateDepartmentID = view.CreateDepartmentID;
            postDescriptionData.Version = view.Version;
            postDescriptionData.StatusCode = view.StatusCode;
            postDescriptionData.Approver = view.Approver;
            postDescriptionData.ApproveDateTime = view.ApproveDateTime;
            postDescriptionData.Signer = view.Signer;
            postDescriptionData.SignDateTime = view.SignDateTime;
            postDescriptionData.QualificationRequirements = view.QualificationRequirements;
            postDescriptionData.EducationalLevel = view.EducationalLevel;
            postDescriptionData.TrainingRecord = view.TrainingRecord;
            postDescriptionData.Other = view.Other;
            postDescriptionData.Responsibility = view.Responsibility;
            postDescriptionData.PerformanceEvaluation = view.PerformanceEvaluation;
            postDescriptionData.SOP = view.SOP;
        }
        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<PostView>> GetPostListAsync()
        {
            var postList = await _postRepository.GetByCacheAsync();
            var employees = await _employeePersonalDataRepository.GetAllEmployees();
            var result = new List<PostView>();
            foreach (var item in postList.Where(m => m.PostTypeID != "5"))
            {
                result.Add(new()
                {
                    PostID = item.PostID,
                    PostName = item.PostName,
                    PostTypeID = item.PostTypeID,
                    PostNatureID = item.PostNatureID,
                    AddEmployeeID = item.AddEmployeeID,
                    AddDateTime = item.AddDateTime,
                    ModifyEmployeeID = item.ModifyEmployeeID,
                    ModifyDateTime = item.ModifyDateTime,
                    AddEmployeeName = item.AddEmployeeID == "System" ? "系统管理员" : employees.TryGetValue(item.AddEmployeeID, out string addEmployee) ? addEmployee : string.Empty,
                    ModifyEmployeeName = item.ModifyEmployeeID == "System" ? "系统管理员" : employees.TryGetValue(item.ModifyEmployeeID, out string modifyEmployee) ? modifyEmployee : string.Empty,
                    DeleteFlag = item.DeleteFlag,
                });
            }
            return result;
        }
        /// <summary>
        /// 保存岗位数据
        /// </summary>
        /// <param name="view">岗位视图</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        public async Task<bool> SavePostDataAsync(PostView view,string employeeID)
        {
            if (view == null)
            {
                return false;
            }
            if (view.PostID == 0)
            {
                var postList = await _postRepository.GetByCacheAsync();
                var newPostIDs = new List<int>() { POST_ID_998, POST_ID_999 };
                int maxPostID = postList.Where(m => !newPostIDs.Contains(m.PostID)).Max(m => m.PostID);
                PostInfo post = CreateNewPostInfo(view, employeeID, maxPostID);
                await _unitOfWork.GetRepository<PostInfo>().InsertAsync(post);
                
            }
            else
            {
                var post = await _postRepository.GetByPostIDNoCache(view.PostID);
                if (post == null)
                {
                    return false;
                }
                post.PostName = view.PostName;
                post.PostTypeID = view.PostTypeID;
                post.PostNatureID = view.PostNatureID;
                post.Modify(employeeID);
            }
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                await _postRepository.UpdateCache();
            }
            return true;
        }
        /// <summary>
        /// 创建新的岗位数据
        /// </summary>
        /// <param name="view"></param>
        /// <param name="employeeID"></param>
        /// <param name="maxPostID"></param>
        /// <returns></returns>
        private PostInfo CreateNewPostInfo(PostView view, string employeeID, int maxPostID)
        {
            return new PostInfo
            {
                PostName = view.PostName,
                PostTypeID = view.PostTypeID,
                PostNatureID = view.PostNatureID,
                AddEmployeeID = employeeID,
                AddDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                HospitalID = _config.Value.HospitalID,
                Language = _config.Value.Language,
                DeleteFlag = string.Empty,
                PostID = maxPostID + 1,
                Sort = maxPostID + 1
            };
        }
        /// <summary>
        /// 删除岗位数据
        /// </summary>
        /// <param name="postID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeletePostDataAsync(int postID,string employeeID)
        {
            var post = await _postRepository.GetByPostIDNoCache(postID);
            if (post == null)
            {
                return false;
            }
            post.Delete(employeeID);
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                await _postRepository.UpdateCache();
            }
            return true;
        }
        /// <summary>
        /// 激活岗位数据
        /// </summary>
        /// <param name="postID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> ActivatePostDataAsync(int postID, string employeeID)
        {
            var post = await _postRepository.GetByPostIDNoCache(postID);
            if (post == null)
            {
                return false;
            }
            post.DeleteFlag = string.Empty;
            post.Modify(employeeID);
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                await _postRepository.UpdateCache();
            }
            return true;
        }
        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        public async Task<bool> GetPostWhetherDataAsync(int postID,string hospitalID)
        {
           return await _departmentPostRepository.GetExistByPostID(postID, hospitalID);
        }
    }
}
