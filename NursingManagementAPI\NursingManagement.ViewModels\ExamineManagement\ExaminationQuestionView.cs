﻿namespace NursingManagement.ViewModels
{
    public class ExaminationQuestionView
    {
        /// <summary>
        /// 题目ID
        /// </summary>
        public int ExaminationQuestionID { get; set; }
        /// <summary>
        /// 考核题目名字
        /// </summary>
        public string QuestionContent { get; set; }
        /// <summary>
        /// 题目类型，来源SettingDictionary（判断题、多选题、单选题）
        /// </summary>
        public string ExaminationQuestionType { get; set; }

        /// <summary>
        /// 题目类型名称（判断题、多选题、单选题）
        /// </summary>
        public string ExaminationQuestionTypeName { get; set; }
        /// <summary>
        /// 难度等级，来源SettingDictionary
        /// </summary>
        public string DifficultyLevel { get; set; }
        /// <summary>
        /// 难度等级名称
        /// </summary>
        public string DifficultyLevelName { get; set; }
        /// <summary>
        /// 题目标签，来源SettingDictionary
        /// </summary>
        public string QuestionTag { get; set; }
        /// <summary>
        /// 题目标签集合，来源SettingDictionary
        /// </summary>
        public List<string> QuestionTagArr { get; set; }
        /// <summary>
        /// 标签名称
        /// </summary>
        public List<string> QuestionTagName { get; set; }
        /// <summary>
        /// 题目说明
        /// </summary>
        public string Instructions { get; set; }
        /// <summary>
        /// 解析
        /// </summary>
        public string Analysis { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 权重
        /// </summary>
        public decimal? FilterWeight { get; set; }
        /// <summary>
        /// 题库ID
        /// </summary>
        public string QuestionBankID { get; set; }
        /// <summary>
        /// 答案集合
        /// </summary>
        public List<ItemDetailListView> QuestionDetail { get; set; }
        /// <summary>
        /// 实操题分值
        /// </summary>
        public int? Score { get; set; }
        /// <summary>
        /// 排序(默认为0)
        /// </summary>
        public int Sort { get; set; } = 0;
    }
}
