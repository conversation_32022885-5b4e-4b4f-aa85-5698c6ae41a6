﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class TrainingEvaluationMainRepository : ITrainingEvaluationMainRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public TrainingEvaluationMainRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据学习人员记录ID和被评价人员ID获取数据
        /// </summary>
        /// <param name="trainingLearnerID">培训人员记录ID</param>
        /// <param name="beEvaluationEmployeeID">被评价人员ID</param>
        /// <returns></returns>
        public async Task<List<TrainingEvaluationMainInfo>> GetListByLearnerID(string trainingLearnerID, string beEvaluationEmployeeID)
        {
            return await _dbContext.TrainingEvaluationMainInfos.Where(m => m.TrainingLearnerID == trainingLearnerID && m.BeEvaluationEmployeeID == beEvaluationEmployeeID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
