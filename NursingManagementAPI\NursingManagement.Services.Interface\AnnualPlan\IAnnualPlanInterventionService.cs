﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IAnnualPlanInterventionService
    {
        /// <summary>
        /// 获取按MainGoalID分组的项目明细集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<Dictionary<string, List<APProjectDetail>>> GetProjectDetailsGroupedByMainGoal(string mainID);
        /// <summary>
        /// 获取执行项目
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <returns></returns>
        Task<List<APInterventionsGroup>> GetAnnualInterventions(string mainID, string projectDetailID);
        /// <summary>
        /// 保存一条执行项目
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        Task<bool> SaveAnnualIntervention(AnnualInterventionSaveView saveView);
        /// <summary>
        /// 联动触发删除年度计划信息
        /// </summary>
        /// <param name="planMainID">主表ID</param>
        /// <param name="projectDetailIDs">工作项目ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        Task<bool> DeleteInterventionsByProjectDetailID(string planMainID, string employeeID, params string[] projectDetailIDs);
        /// <summary>
        /// 删除年度计划执行项目主表数据
        /// </summary>
        /// <param name="interventionMainID">执行项目主表ID</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> DeleteAnnualInterventionByID(string interventionMainID, string employeeID);
        /// <summary>
        /// 获取年度计划计划总览数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanGeneralView>> GetAnnualPlanGeneralView(int year, int departmentID);
        /// <summary>
        /// 获取当前计划已有的负责人选项情况
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<APInterventionRecPrincipal>> GetRecPrincipalOptions(string planMainID);

        /// <summary>
        /// 获取执行项目转换视图
        /// </summary>
        /// <param name="planMainID">年度计划主表ID</param>
        /// <param name="months">月集合</param>
        /// <returns></returns>
        Task<List<APInterventionConvertView>> GetAPInterventionConvertViews(string planMainID, int[] months);
    }
}
