﻿namespace NursingManagement.ViewModels
{
    public class ExaminationAppointmentView
    {
        /// <summary>
        /// 考核预约ID（主键）
        /// </summary>
        public string ExaminationAppointmentID { get; set; }
        /// <summary>
        /// 预约考核人员ID
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 考核计划ID（关联考核计划表）
        /// </summary>
        public string ExaminationRecordID { get; set; }
        /// <summary>
        /// 监考计划时间表ID（关联监考安排表）
        /// </summary>
        public string ExaminerScheduleID { get; set; }
        /// <summary>
        /// 预约时间
        /// </summary>
        public DateTime AppointmentDate { get; set; }
        /// <summary>
        /// 监考计划开始时间
        /// </summary>
        public TimeSpan ScheduleStartTime { get; set; }
        /// <summary>
        /// 预约状态（取值：已预约/已取消）
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 取消预约的原因（可为空）
        /// </summary>
        public string CancelReason { get; set; }
        /// <summary>
        /// 考核Main表ID
        /// </summary>
        public string ExaminationMainID { get; set; }
    }
}
