﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Linq.Expressions;


namespace NursingManagement.Data.Repository
{
    public class TrainingLearnerRepository : ITrainingLearnerRepository
    {
        private readonly NursingManagementDbContext _dbContext;
        private static string HospitalID = null;

        public TrainingLearnerRepository(
            NursingManagementDbContext dbContext,
            SessionCommonServer sessionCommonServer)
        {
            _dbContext = dbContext;
            HospitalID = sessionCommonServer.GetSessionByCache()?.HospitalID;
        }
        /// <summary>
        /// 根据ID获取培训学员信息
        /// </summary>
        /// <param name="trainingLearnerId">培训学员ID</param>
        /// <returns>培训学员信息</returns>
        public async Task<TrainingLearnerInfo> GetByIdAsync(string trainingLearnerId)
        {
            return await _dbContext.TrainingLearnerInfos
                .FirstOrDefaultAsync(t => HospitalID == t.HospitalID && t.TrainingLearnerID == trainingLearnerId && t.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据条件获取培训学员列表
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <param name="asNoTrackFlag">是否跟踪实体</param>
        /// <returns>培训学员列表</returns>
        public async Task<IEnumerable<TrainingLearnerInfo>> GetListByWherePredicateAsync(Expression<Func<TrainingLearnerInfo, bool>> predicate, bool asNoTrackFlag = false)
        {
            if (asNoTrackFlag)
            {
                return await _dbContext.TrainingLearnerInfos.Where(predicate)
                    .Where(t => HospitalID == t.HospitalID && t.DeleteFlag != "*")
                    .AsNoTracking().ToListAsync();
            }
            return await _dbContext.TrainingLearnerInfos
                .Where(predicate)
                .Where(t => HospitalID == t.HospitalID && t.DeleteFlag != "*")
                .ToListAsync();
        }

        /// <summary>
        /// 根据培训记录ID获取培训学员信息
        /// </summary>
        /// <param name="trainingRecordId">培训记录ID</param>
        /// <returns>培训学员信息</returns>
        public async Task<List<TrainingLearnerInfo>> GetByTrainingRecordIdAsync(string trainingRecordId)
        {
            return await _dbContext.TrainingLearnerInfos
                .Where(t => HospitalID == t.HospitalID && t.TrainingRecordID == trainingRecordId && t.DeleteFlag != "*")
                .ToListAsync();
        }

        /// <summary>
        /// 根据员工ID获取培训学员列表
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>培训学员列表</returns>
        public async Task<List<TrainingLearnerView>> GetByEmployeeIdAsync(string employeeId)
        {
            return await _dbContext.TrainingLearnerInfos.Where(t => HospitalID == t.HospitalID && t.EmployeeID == employeeId && t.DeleteFlag != "*")
                .Select(m => new TrainingLearnerView
                {
                    TrainingLearnerID = m.TrainingLearnerID,
                    EmployeeID = m.EmployeeID,
                    LastTrainingTime = m.LastTrainingTime,
                    TrainingRecordID = m.TrainingRecordID,
                    DepartmentID = m.DepartmentID,
                    LearningCount = m.LearningCount,
                    MonitorFlag = m.MonitorFlag,
                    Progress = m.Progress,
                    TrainingComment = m.TrainingComment,
                    TrainingDuration = m.TrainingDuration,
                    AddDateTime = m.AddDateTime,
                    CourseSatisfaction = m.CourseSatisfaction,
                    CourseRecommendations = m.CourseRecommendations,
                })
                .ToListAsync();
        }
        /// <summary>
        /// 根据培训主记录ID和员工ID获取培训学员信息
        /// </summary>
        /// <param name="trainingRecordId">培训主记录ID</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>培训学员信息</returns>
        public async Task<TrainingLearnerInfo> GetByRecordIdAndEmployeeId(string trainingRecordId, string employeeId)
        {
            return await _dbContext.TrainingLearnerInfos
                .FirstOrDefaultAsync(t => HospitalID == t.HospitalID && t.TrainingRecordID == trainingRecordId && t.EmployeeID == employeeId && t.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据条件获取对应的人员培训记录
        /// </summary>
        /// <param name="trainingRecordIDs">培训记录ID</param>
        /// <returns></returns>
        public async Task<List<TrainingLearnerView>> GetViewListByTrainingRecordIDsAsync(List<string> trainingRecordIDs)
        {
            return await _dbContext.TrainingLearnerInfos.AsNoTracking().Where(m => HospitalID == m.HospitalID && m.DeleteFlag != "*" && trainingRecordIDs.Contains(m.TrainingRecordID))
                .Select(m => new TrainingLearnerView
                {
                    TrainingLearnerID = m.TrainingLearnerID,
                    EmployeeID = m.EmployeeID,
                    LastTrainingTime = m.LastTrainingTime,
                    TrainingRecordID = m.TrainingRecordID,
                    DepartmentID = m.DepartmentID,
                    LearningCount = m.LearningCount, 
                    MonitorFlag = m.MonitorFlag,
                    Progress = m.Progress,
                    TrainingComment = m.TrainingComment,
                    TrainingDuration = m.TrainingDuration,
                    AddDateTime = m.AddDateTime,
                    CourseSatisfaction = m.CourseSatisfaction,
                    CourseRecommendations = m.CourseRecommendations,
                })
                .ToListAsync();
        }
    }
}