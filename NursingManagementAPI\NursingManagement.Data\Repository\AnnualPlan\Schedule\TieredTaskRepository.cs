﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using static NursingManagement.Models.AnnualPlanEnums;

namespace NursingManagement.Data.Repository
{
    public class TieredTaskRepository : ITieredTaskRepository
    {
        private readonly NursingManagementDbContext _dbContext;
        private readonly IOptions<SystemConfig> _config;

        public TieredTaskRepository(
            NursingManagementDbContext dbContext,
            IOptions<SystemConfig> options)
        {
            _dbContext = dbContext;
            _config = options;
        }

        /// <summary>
        /// 根据主键获取执行任务主记录
        /// </summary>
        /// <param name="scheduleMainID">任务主表ID</param>
        /// <returns></returns>
        public Task<AnnualScheduleMainInfo> GetTieredTaskByID(string scheduleMainID)
        {
            return _dbContext.AnnualScheduleMainInfos.Where(m => m.AnnualScheduleMainID == scheduleMainID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据集合获取年度计划排程主记录
        /// </summary>
        /// <param name="scheduleIDs">排程ID集合</param>
        /// <returns></returns>
        public async Task<List<AnnualScheduleMainInfo>> GetTieredTasksByIDs(IEnumerable<string> scheduleIDs)
        {
            return await _dbContext.AnnualScheduleMainInfos.Where(m => scheduleIDs.Contains(m.AnnualScheduleMainID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取某人某月之任务
        /// </summary>
        /// <param name="schedulePerformer">计划执行人</param>
        /// <param name="scheduleMonth">计划月份</param>
        /// <param name="scheduleYear">计划年度</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<AnnualScheduleMainView[]> GetTieredTaskViews(string schedulePerformer, int scheduleMonth, int scheduleYear, int departmentID)
        {

            var tasks = await _dbContext.AnnualScheduleMainInfos
                .Include(m => m.MonthlyWorkToTasks)
                .Where(m => m.HospitalID == _config.Value.HospitalID && m.SchedulePerformer == schedulePerformer && m.ScheduleMonth == scheduleMonth && m.ScheduleYear == scheduleYear && m.DeleteFlag != "*")
                .Select(m => new AnnualScheduleMainView
                {
                    InterventionID = m.InterventionID,
                    AnnualScheduleMainID = m.AnnualScheduleMainID,
                    ScheduleDateTime = m.ScheduleDateTime,
                    PerformDateTime = m.PerformDateTime,
                    PerformComment = m.PerformComment,
                    Status = m.Status,
                    Reason = m.Reason,
                    DelayContent = m.DelayContent,
                    Content = m.Content,
                    MonthlyWorkToTasks = m.MonthlyWorkToTasks
                }).ToListAsync();
            return [.. tasks.OrderBy(m => m.MonthlyWorkToTasks.Any(n => n.DepartmentID == m.DepartmentID) ? 0 : 1)];
        }

        /// <summary>
        /// 获取{schedulePerformer}在{scheduleYear}年每月的排程统计
        /// </summary>
        /// <param name="schedulePerformer">预计执行人</param>
        /// <param name="scheduleYear">年份</param>
        /// <returns></returns>
        public async Task<List<ScheduleStatisticsView>> GetTasksMonthlyCountViews(string schedulePerformer, int scheduleYear)
        {
            return await _dbContext.AnnualScheduleMainInfos.Where(m =>
                m.HospitalID == _config.Value.HospitalID && m.SchedulePerformer == schedulePerformer
                && m.ScheduleYear == scheduleYear && m.DeleteFlag != "*")
                .GroupBy(m => m.ScheduleMonth).Select(m => new ScheduleStatisticsView
                {
                    Month = m.Key,
                    StatisticValue = m.Where(m => m.Status == APScheduleStatus.Performed || m.Status == APScheduleStatus.Delay).Count(),
                    StatisticTotalValue = m.Count()
                }).OrderBy(m => m.Month).ToListAsync();
        }

        /// <summary>
        /// 获取某月未执行排程数量
        /// </summary>
        /// <param name="scheduleYear">年份</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="schedulePerformer">预计执行人</param>
        /// <returns></returns>
        public async Task<int?> GetMonthlyUnPerformTasksCount(int scheduleYear, int scheduleMonth, string schedulePerformer)
        {
            return await _dbContext.AnnualScheduleMainInfos.Where(m =>
            m.ScheduleYear == scheduleYear && m.SchedulePerformer == schedulePerformer && m.ScheduleMonth == scheduleMonth && m.DeleteFlag != "*")
                .Where(m => m.Status == APScheduleStatus.UnPerform).CountAsync();
        }
        /// <summary>
        /// 根据日期获取未执行的计划的排程ID
        /// </summary>
        /// <param name="scheduleDate">计划执行日期</param>
        /// <returns></returns>
        public async Task<List<AnnualScheduleMainInfo>> GetDailyUnPerformTaskRelationWorks(DateTime scheduleDate)
        {
            return await _dbContext.AnnualScheduleMainInfos
                .Include(m => m.MonthlyWorkToTasks)
                .Where(m => m.ScheduleDateTime == scheduleDate && m.DeleteFlag != "*" && m.Status == APScheduleStatus.UnPerform)
                .Select(m => new AnnualScheduleMainInfo
                {
                    SchedulePerformer = m.SchedulePerformer,
                    MonthlyWorkToTasks = m.MonthlyWorkToTasks
                })
                .ToListAsync();
        }
        /// <summary>
        /// 获取关联的排程ID
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="planMonth">月份</param>
        /// <param name="interventionID">措施ID</param>
        /// <param name="principalID">执行人</param>
        /// <returns>排程ID</returns>
        public async Task<string> GetRelationTaskID(int year, int planMonth, int interventionID, string principalID)
        {
            return await _dbContext.AnnualScheduleMainInfos.Where(m => m.HospitalID == _config.Value.HospitalID && m.ScheduleYear == year && m.ScheduleMonth == planMonth && m.InterventionID == interventionID && m.SchedulePerformer == principalID && m.DeleteFlag != "*")
                .Select(m => m.AnnualScheduleMainID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取未执行任务
        /// </summary>
        /// <param name="schedulePerformer">计划执行人</param>
        /// <param name="scheduleYear">计划年份</param>
        /// <param name="startMonth">月份</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="preOrNextFlag">获取计划月份之前OR之后，True：当月和当月之前；False：当月</param>
        /// <returns></returns>
        public async Task<AnnualScheduleMainView[]> GetUnPerformTaskViews(string schedulePerformer, int scheduleYear, int? startMonth, int departmentID, bool preOrNextFlag)
        {
            var unPerformTasks = await _dbContext.AnnualScheduleMainInfos
                .Include(m => m.MonthlyWorkToTasks)
                .IfWhere(startMonth.HasValue, m => preOrNextFlag ? m.ScheduleMonth <= startMonth.Value : m.ScheduleMonth == startMonth.Value)
                .Where(m => m.HospitalID == _config.Value.HospitalID
                                        && m.SchedulePerformer == schedulePerformer
                                        && m.ScheduleYear == scheduleYear
                                        && m.DeleteFlag != "*"
                                        && m.Status == APScheduleStatus.UnPerform)
                .Select(m => new AnnualScheduleMainView
                {
                    ScheduleDateTime = m.ScheduleDateTime,
                    MonthlyWorkToTasks = m.MonthlyWorkToTasks
                }).ToArrayAsync();
            return unPerformTasks;
        }
        /// <summary>
        /// 获取某月的任务集合
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns></returns>
        public async Task<List<AnnualScheduleMainInfo>> GetMonthlyTasks(int year, int month, string hospitalID)
        {
            return await _dbContext.AnnualScheduleMainInfos
                .Where(m => m.HospitalID == hospitalID && m.ScheduleYear == year && m.ScheduleMonth == month && m.DeleteFlag != "*")
                .Include(m => m.MonthlyWorkToTasks)
                .ToListAsync();
        }
    }
}
