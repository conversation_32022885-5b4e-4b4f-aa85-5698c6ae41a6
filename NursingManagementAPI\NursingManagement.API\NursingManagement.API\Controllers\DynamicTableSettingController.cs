﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 动态表格
    /// </summary>
    [Produces("application/json")]
    [Route("api/DynamicTableSetting")]
    [EnableCors("any")]
    public class DynamicTableSettingController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IDynamicTableSettingService _dynamicTableSettingService;
        private readonly ISessionService _session;
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="session"></param>
        /// <param name="dynamicTableSettingService"></param>
        public DynamicTableSettingController(
            ISessionService session
            , IDynamicTableSettingService dynamicTableSettingService

            )
        {
            _session = session;
            _dynamicTableSettingService = dynamicTableSettingService;
        }
        /// <summary>
        /// 获取动态表格表头配置
        /// </summary>
        /// <param name="queryView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDynamicTableHeader")]
        public async Task<IActionResult> GetDynamicTableHeader(DynamicTableHeaderQueryView queryView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.Code = 0;
                result.Message = "登录超时,请重新登入";
                return result.ToJson();
            }
            queryView.HospitalID = session.HospitalID;
            result.Data = await _dynamicTableSettingService.GetDynamicTableHeader(queryView);
            return result.ToJson();
        }
    }
}
