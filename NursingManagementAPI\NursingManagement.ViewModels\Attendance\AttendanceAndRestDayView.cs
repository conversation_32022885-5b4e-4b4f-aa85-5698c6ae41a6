﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 考勤及休假天数
    /// </summary>
    public class AttendanceAndRestDayView
    {
        /// <summary>
        /// 考勤天数
        /// </summary>
        public decimal AttendanceDays { get; set; }
        /// <summary>
        /// 休假天数
        /// </summary>
        public decimal RestDays { get; set; }
        /// <summary>
        /// 差勤天数(岗位考勤天数*排班天数-排班天数)
        /// </summary>
        public decimal AttendanceDifferenceDays { get; set; }
        /// <summary>
        /// 小夜天数
        /// </summary>
        public decimal? EveningShftDays { get; set; }
        /// <summary>
        /// 大夜天数
        /// </summary>
        public decimal? NightShiftDays { get; set; }
        /// <summary>
        /// 通夜天数
        /// </summary>
        public decimal? WholeNightShiftDays { get; set; }        
    }
}
