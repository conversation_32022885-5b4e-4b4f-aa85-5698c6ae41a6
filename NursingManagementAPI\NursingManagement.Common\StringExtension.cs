﻿using System.Collections.Generic;
using System.Diagnostics.Metrics;

namespace NursingManagement.Common
{
    public static class StringExtension
    {
        public static string[] Split(this string str, string splitStr)
        {
            var list = new List<string>();
            var temp = 0;
            var splitLenth = splitStr.Length;
            var endStr = str.Substring(str.Length - splitLenth);
            if (!string.Equals(endStr, splitStr))
            {
                str += splitStr;
            }
            for (var i = 0; i < str.Length; i++)
            {
                var str1 = string.Empty;
                for (var j = 0; j < splitLenth; j++)
                {
                    if (i + j >= str.Length)
                    {
                        break;
                    }
                    str1 += str[i + j];
                }
                if (string.Equals(str1, splitStr))
                {
                    list.Add(str.Substring(temp, i - temp));
                    temp = i;
                    str = str.Remove(i, splitStr.Length);
                }
            }
            return list.ToArray();
        }
        /// <summary>
        /// 拼接字符串
        /// </summary>
        /// <param name="originalString">原始字符串</param>
        /// <param name="addString">要拼接的字符串</param>
        /// <param name="separator">分隔符</param>
        /// <param name="isAddHead">是否拼接到最前面</param>
        /// <returns></returns>
        public static string ConcatString(string originalString, string addString, string separator, bool isAddHead)
        {
            if (string.IsNullOrEmpty(originalString))
            {
                return addString;
            }
            if (string.IsNullOrWhiteSpace(addString))
            {
                return originalString;
            }
            string ret="";
            if (isAddHead)
            {
                ret = addString + separator + originalString;
            }
            else
            {
                ret = originalString + separator + addString;
            }
            return ret;
        }
        /// <summary>
        /// 获取真实主键ID（去除temp_）
        /// </summary>
        /// <param name="source">源字符串</param>
        /// <param name="isSubstring">是否有截取</param>
        /// <returns></returns>
        public static string GetRealID(this string source, out bool isSubstring)
        {
            isSubstring = false;
            if (source.StartsWith("temp_"))
            {
                isSubstring = true;
                return source[5..];
            }
            return source;
        }
        /// <summary>
        /// 首字母转小写
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string FirstLetterToLowerCase(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
            {
                return str;
            }
            if (str.Length == 1)
            {
                return char.ToLower(str[0]).ToString();
            }
            return char.ToLower(str[0]) + str[1..];
        }

        /// <summary>
        /// 生成GUID
        /// </summary>
        /// <returns></returns>
        public static string NewGuid(this string _)
        {
            return Guid.NewGuid().ToString("N");
        }
    }
}
