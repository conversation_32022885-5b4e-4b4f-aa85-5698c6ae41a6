﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.AspNetCore.Http;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class FileService : IFileService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ICommonFileRepository _commonFileRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IAPISettingService _aPISettingService;
        /// <summary>
        /// 本地文件目录
        /// </summary>
        private String LOCAL_FILE_URL = AppContext.BaseDirectory + "files";

        public FileService(
            IAppConfigSettingRepository appConfigSettingRepository
            , ICommonFileRepository commonFileRepository
            , IUnitOfWork unitOfWork
            , SessionCommonServer sessionCommonServer
            , IAPISettingService aPISettingService

        )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
            _commonFileRepository = commonFileRepository;
            _unitOfWork = unitOfWork;
            _sessionCommonServer = sessionCommonServer;
            _appConfigSettingRepository = appConfigSettingRepository;
            _aPISettingService = aPISettingService;
        }

        public async Task<FileUploadReturnView> UpLoadFile(IFormFile formFile, DocumentView view)
        {
            var result = await UploadFileToRemoteServer(view, formFile);
            if (result == null || !result.UpLoadFlag)
            {
                _logger.Error($"文件上传文件服务器失败，DocumentView={ListToJson.ToJson(view)}");
                return null;
            }
            var upLoadView = new FileUploadView()
            {
                FileClass = view.DocumentMainView.DocumentTypeID.ToString(),
                SourceID = result.DocumentMainID,
            };
            // 写文件记录
            var fileID = await SaveCommonFileInfoAsync(formFile.FileName, upLoadView, null);
            // 如果文件记录保存失败 需要删除
            if (string.IsNullOrWhiteSpace(fileID))
            {
                _logger.Error("UpLoadFile方法，文件记录保存传失败！");
                await DeleteFileOfRemoteServer(upLoadView.SourceID, view.DocumentMainView.UserID);
                return null;
            }
            return new FileUploadReturnView
            {
                Url =  result.DocumentUrl ,
                FileID = fileID ,
            };
        }
        /// <summary>
        /// 上传文件到远端服务器
        /// </summary>
        /// <param name="view"></param>
        /// <param name="formFile"></param>
        /// <returns></returns>
        private async Task<UpLoadDocumentResult> UploadFileToRemoteServer(DocumentView view, IFormFile formFile)
        {
            var uploadSettingView = await _aPISettingService.GetAPIAddressByCode("UploadDocument");
            if (uploadSettingView == null)
            {
                _logger.Error("UpLoadFile方法，APISetting获取配置失败！");
                return null;
            }
            //获取远程上传文件需要的FormData参数
            var formDataParams = GetFormDataParams(view, formFile);
            if (formDataParams == null)
            {
                return null;
            }
            //测试使用
            //uploadSettingView.ApiUrl = "http://**************:9102/api/DocumentManagement/UploadDocument";
            var responseResult = await HttpHelper.HttpPostAsync(uploadSettingView.ApiUrl, formDataParams, "multipart/form-data", 30);
            if (string.IsNullOrWhiteSpace(responseResult))
            {
                return null;
            }
            try
            {
                var result = ListToJson.ToList<ResponseResult>(responseResult);
                if (result == null || result.Data == null)
                {
                    _logger.Error("UpLoadFile方法，文件上传失败！," + result?.Message);
                    return null;
                }
                return ListToJson.ToList<UpLoadDocumentResult>(result.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("UpLoadFile方法，文件上传失败！," + ex.ToString());
            }
            return null;
        }
        /// <summary>
        /// 删除远端文件服务器上的文件
        /// </summary>
        /// <param name="documentID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteFileOfRemoteServer(string documentID, string userID)
        {
            var deleteSettingView = await _aPISettingService.GetAPIAddressByCode("DeleteDocumentByID");
            if (deleteSettingView == null)
            {
                _logger.Error("UpLoadFile方法，APISetting获取删除文件url地址配置失败！");
                return false;
            }
            ResponseResult result = null;
            try
            {
                string url = deleteSettingView.ApiUrl + "?documentID=" + documentID + "&userID=" + userID;
                var responseResult = await HttpHelper.HttpPostAsync(url, "", "application/json", 30);
                result = ListToJson.ToList<ResponseResult>(responseResult);
            }
            catch (Exception ex)
            {
                _logger.Error("DeleteFileOfRemoteServer方法，DeleteDocumentByID远程调用执行失败！," + ex.ToString());
            }

            return result.Data is bool successFlag && successFlag;

        }
        /// <summary>
        /// 获取远程上传文件需要的FormData参数
        /// </summary>
        /// <param name="documentView"></param>
        /// <param name="formFile"></param>
        /// <returns></returns>
        private static MultipartFormDataContent GetFormDataParams(DocumentView documentView, IFormFile formFile)
        {
            var formData = MultipartFormDataHelper.ConvertToMultipartFormDataContentAsync(documentView);
            // 将文件流作为文件内容添加到表单中
            if (formFile != null)
            {
                var fileContent = new StreamContent(formFile.OpenReadStream());
                formData.Add(fileContent, "formFile", formFile.FileName);
            }
            return formData;
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="file"></param>
        /// <param name="view"></param>
        /// <returns>返回值返回文件存储地址？直接返回用来回显文件下载</returns>
        public async Task<(string fileID, string errMessage)> UpLoadFileAsync(IFormFile file, FileUploadView view)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    _logger.Warn("上传的文件不能为空");
                    return (null, "上传的文件不能为空");
                }
                // 获取文件的完整路径
                string fileExtension = Path.GetExtension(file.FileName);
                //生成随机文件名
                string guidFileName = Guid.NewGuid().ToString("N") + fileExtension;

                string filePath = Path.Combine(LOCAL_FILE_URL, guidFileName);
                if (!CheckOrCreateDirectory(LOCAL_FILE_URL))
                {
                    return (null, "无法保存文件到指定目录");
                };
                // 将文件保存到服务器上
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(fileStream);
                }
                return (await SaveCommonFileInfoAsync(file.FileName, view, guidFileName), null);
            }
            catch (Exception ex)
            {
                // 处理异常，例如记录日志等
                _logger.Error(ex, "上传文件异常，请联系维护人员，查看日志");
                return (null, "上传文件异常，请联系维护人员，查看日志");
            }
        }
        /// <summary>
        /// 检查文件目录是否存在，不存在，进行创建
        /// </summary>
        /// <param name="localFilePath"></param>
        /// <returns></returns>
        private bool CheckOrCreateDirectory(string localFilePath)
        {
            if (!Directory.Exists(localFilePath))
                try
                {
                    Directory.CreateDirectory(localFilePath);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"文件目录创建失败Directory={localFilePath}");
                    return false;
                }
            return true;
        }
        /// <summary>
        /// 保存文件记录
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="view"></param>
        /// <param name="guidFileName"></param>
        /// <returns></returns>
        private async Task<string> SaveCommonFileInfoAsync(string fileName, FileUploadView view, string guidFileName)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            if (session == null)
            {
                return null;
            }
            var fileInfo = new CommonFileInfo
            {
                HospitalID = session.HospitalID,
                Content = fileName,
                Class = view.FileClass,
                PublishDateTime = DateTime.Now,
                SourceID = view.SourceID,
                Link = guidFileName ?? "",
                DeleteFlag = ""
            };
            fileInfo.CommonFileID = fileInfo.GetId();
            fileInfo.Add(session.EmployeeID);
            fileInfo.Modify(session.EmployeeID);
            await _unitOfWork.GetRepository<CommonFileInfo>().InsertAsync(fileInfo);

            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                return fileInfo.CommonFileID;
            }
            return null;
        }
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="fileID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteFileAsync(string fileID, string employeeID)
        {
            var fileInfo = await _commonFileRepository.GetFileByFileIDAsync(fileID);
            if (fileInfo == null)
            {
                _logger.Warn($"没有文件需要删除，表中查找不到的文件fileID={fileID}的相关记录");
            }
            fileInfo.Delete(employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        ///  根据分类获取文件集合
        /// </summary>
        /// <param name="fileClass"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<FileView>> GetFileListByClassAndSourceAsync(string fileClass, string sourceID)
        {
            var fileViews = new List<FileView>();
            var fileServerUrl = await GetFileServerUrl();
            var fileList = await _commonFileRepository.GetFileListByClassAndSourceAsync(fileClass, sourceID);
            if (fileList.Count <= 0)
            {
                return fileViews;
            }
            var remoteDocuments = await GetFileAccessInfosFromFileSystemAsync(fileList.Select(m => m.SourceID).ToList());
            foreach (var item in fileList)
            {
                var document = remoteDocuments?.Find(m => m.DocumentMainID == item.SourceID);
                if (document != null)
                {
                    fileViews.Add(new FileView
                    {
                        FileName = document.DocumentTitle,
                        Url = document.DocumentUrl,
                        PublishDateTime = item.PublishDateTime,
                        FileID = item.CommonFileID,
                    });
                    continue;
                }
                if (!string.IsNullOrEmpty(item.Link)) {
                    var link = fileServerUrl + @"/" + item.Link;
                    fileViews.Add(new FileView()
                    {
                        FileName = item.Content,
                        Url = link,
                        PublishDateTime = item.PublishDateTime,
                        FileID = item.CommonFileID,
                    });
                }

            }
            return fileViews;
        }
        /// <summary>
        /// 文件存储的服务器链接
        /// </summary>
        /// <returns></returns>
        private async Task<string> GetFileServerUrl()
        {
            var fileServerUrl = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "FileServerUrl");
            if (string.IsNullOrEmpty(fileServerUrl))
            {
                _logger.Error("没有配置文件服务器地址【\"Configs\", \"FileServerUrl\"】");
                return null;
            }
            return fileServerUrl;
        }
        /// <summary>
        /// 根据文件唯一ID获取远端文件系统重的文件访问信息
        /// </summary>
        /// <param name="fileIDs"></param>
        /// <returns>可能返回空集合</returns>
        public async Task<List<DocumentMainView>> GetFileAccessInfosFromFileSystemAsync(List<string> fileIDs)
        {
            var fileSettingUrl = await _aPISettingService.GetAPIAddressByCode("GetDocumentByIDs");
            if (fileSettingUrl == null)
            {
                return null;
            }
            var isInnerServer = _sessionCommonServer.GetServerEnvironment();
            var header = new Dictionary<string, string> { { Constant.IS_INNER_SERVER , isInnerServer.ToString() } };
            try
            {
                var responseResult = await HttpHelper.HttpPostAsync(fileSettingUrl.ApiUrl, ListToJson.ToJson(fileIDs), "application/json",30, header);
                if (responseResult == null)
                {
                    return null;
                }
                var result = ListToJson.ToList<ResponseResult>(responseResult);
                if (result != null && result.Data != null)
                {
                    return ListToJson.ToList<List<DocumentMainView>>(result.Data.ToString());
                }
                else 
                {
                    _logger.Warn("【请检查DocumentManagement版本是否最新，文档是否存在】获取文档信息失败documentMainIDs=" + ListToJson.ToJson(fileIDs));
                }
            }
            catch (Exception ex)
            {
                _logger.Warn($"获取文件信息失败fileIDs={ListToJson.ToJson(fileIDs)}", ex);
                return null;
            }
            return null;
        }
        /// <summary>
        /// 上传文件并返回上传结果信息
        /// </summary>
        /// <param name="file"></param>
        /// <param name="employeeID">操作人工号</param>
        /// <param name="employeeName">操作人名称</param>
        /// <returns></returns>
        public async Task<FileUploadReturnView> UploadRichTextFile(IFormFile file, string employeeID, string employeeName)
        {
            var documentView = new DocumentView
            {
                DocumentMainView = new DocumentMainView
                {
                    DocumentSize = (int)(file.Length / 1024),
                    DocumentTitle = file.FileName.Split('.')[0],
                    DocumentType = file.FileName.Split('.')?[1] ?? "",
                    UserID = employeeID,
                    UserName = employeeName,
                    DocumentTypeID = 1,
                    SourceSystem = "NursingManagement",
                    DocumentStatus = 30,
                },
                DocumentTagViews = [],
                DocumentDetailViews = [new() { GroupID = 1, ItemID = 1, Value = "测试" }],
            };
            var responseDict = await UpLoadFile(file, documentView);
            if (responseDict == null)
            {
                return null;
            }
            return responseDict;
        }
    }
}
