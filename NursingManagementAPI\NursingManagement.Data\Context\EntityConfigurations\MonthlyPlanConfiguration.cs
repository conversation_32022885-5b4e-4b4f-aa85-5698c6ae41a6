﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NursingManagement.Models;

namespace NursingManagement.Data.Context.EntityConfigurations
{
    public class MonthlyPlanMainConfiguration : IEntityTypeConfiguration<MonthlyPlanMainInfo>
    {
        public void Configure(EntityTypeBuilder<MonthlyPlanMainInfo> builder)
        {
            builder.ToTable("MonthlyPlanMain");
            builder.HasKey(m => m.MonthlyPlanMainID);
            builder.Property(m => m.MonthlyPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.AnnualPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
        }
    }

    public class MonthlyPlanDetailConfiguration : IEntityTypeConfiguration<MonthlyPlanDetailInfo>
    {
        public void Configure(EntityTypeBuilder<MonthlyPlanDetailInfo> builder)
        {
            builder.ToTable("MonthlyPlanDetail");
            builder.HasKey(m => m.MonthlyPlanDetailID);
            builder.Property(m => m.MonthlyPlanDetailID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.MonthlyPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.WorkContent).HasColumnType("nvarchar(200)").HasMaxLength(200);
            builder.Property(m => m.Requirement).HasColumnType("nvarchar(200)").HasMaxLength(200);
            builder.Property(m => m.PrincipalGroupName).HasColumnType("varchar(50)").HasMaxLength(50);
            // 一个Work对应多个WorkToTask，一个WorkToTask对应一个Work
            builder.HasMany(m => m.MonthlyWorkToTasks).WithOne(m => m.MonthlyPlanDetail)
                .HasForeignKey(m => m.MonthlyPlanDetailID);
        }
    }
}
