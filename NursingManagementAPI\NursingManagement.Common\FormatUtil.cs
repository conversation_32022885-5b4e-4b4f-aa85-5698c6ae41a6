﻿using System;
using System.Collections.Generic;

namespace NursingManagement.Common
{
    public class FormatUtil
    {
        /// <summary>
        /// 传入一个字符串列表，返回一个格式化后的字符串
        /// </summary>
        /// <param name="strList"></param>
        /// <returns></returns>
        public static string GetStrFromList(List<string> strList)
        {
            string signsStr = string.Empty;
            if (strList == null)
            {
                return null;
            }
            for (int i = 0; i < strList.Count; i++)
            {
                if (i != 0)
                {
                    signsStr += "\n";
                }
                signsStr += strList[i];
            }
            return signsStr;
        }
        /// <summary>
        /// 移除字符串中的标签
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string GetRemoveLabelStr(string str)
        {

            if (str == null) return "";
            str = str.Replace("<p>", "");
            str = str.Replace("</p>", "");
            str = str.Replace("<br/>", "\n");
            str = str.Replace("<br />", "\n");
            str = str.Replace("< br />", "\n");
            str = str.Replace("< br />", "\n");
            str = str.Replace("<br />", "\n");
            str = str.Replace("<strong>", "");
            str = str.Replace("</strong>", "");
            str = str.Replace("</b>", "");
            str = str.Replace("<b>", "");
            return str;
        }
        /// <summary>
        /// 获取日期 获取失败返回一个默认值
        /// </summary>
        /// <param name="dateTimeStr"></param>
        /// <returns></returns>
        public static DateTime GetDate(string dateTimeStr)
        {
            DateTime dt = DateTime.Now.Date;
            if (DateTime.TryParse(dateTimeStr.Split(' ')[0], out dt)) return dt.Date;
            return DateTime.Now.Date;
        }
        /// <summary>
        /// 获取一个可以为空的时间字符串
        /// </summary>
        /// <param name="dateTimeStr"></param>
        /// <returns></returns>
        public static DateTime? GetDateMayNull(string dateTimeStr)
        {
            DateTime dt = DateTime.Now;
            DateTime? dtWithNull = null;
            try
            {
                if (DateTime.TryParse(dateTimeStr.Split(' ')[0], out dt))
                {
                    dtWithNull = dt.Date;
                }
            }
            catch { }

            return dtWithNull;
        }
        public static TimeSpan? GetTimeMayNull(string dateTimeStr)
        {
            TimeSpan ts;
            try
            {
                if (TimeSpan.TryParse(dateTimeStr.Split(' ')[1], out ts)) return ts;
            }
            catch { }
            return null;
        }
        /// <summary>
        /// 获取时间
        /// </summary>
        /// <param name="dateTimeStr"></param>
        /// <returns></returns>
        public static TimeSpan GetTime(string dateTimeStr)
        {
            TimeSpan ts = new TimeSpan(8, 0, 0);
            if (dateTimeStr.Contains(" "))
            {
                if (TimeSpan.TryParse(dateTimeStr.Split(' ')[1], out ts)) return ts;
            }
            else
            {
                if (TimeSpan.TryParse(dateTimeStr, out ts)) return ts;
            }
            return ts;
        }
        /// <summary>
        /// 获取是否首日标记
        /// </summary>
        /// <param name="dateStr"></param>
        /// <returns></returns>
        public static string GetFirstDayFlag(string dateStr)
        {
            DateTime dt = DateTime.Now;
            if (DateTime.TryParse(dateStr, out dt))
            {
                if (DateTime.Compare(dt.Date, DateTime.Now.Date) == 0)
                {
                    return "*";
                }
            }
            return "";
        }
        /// <summary>
        /// 根据日期时间字符串 获取日期时间 
        /// </summary>
        /// <param name="dateStr"></param>
        /// <returns></returns>
        public static DateTime GetDateTime(string dateStr)
        {
            DateTime dt = DateTime.Now;
            if (DateTime.TryParse(dateStr, out dt))
            {
                return dt;
            }
            else
            {
                return DateTime.Now;
            }
        }
        /// <summary>
        /// 获取医嘱排程状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        public static int GetStatus(string status)
        {
            int intStatus = 0;
            if (int.TryParse(status, out intStatus)) return intStatus;
            return 0;
        }
        /// <summary>
        /// 通过日期和时间类型获取 日期时间
        /// </summary>
        /// <param name="date"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        public static DateTime? GetDateTime(DateTime? date, TimeSpan? time)
        {
            if (date == null || time == null)
            {
                return null;
            }
            else
            {
                return new DateTime(date.Value.Year, date.Value.Month, date.Value.Day, time.Value.Hours, time.Value.Minutes, time.Value.Seconds);
            }
        }
    }
}
