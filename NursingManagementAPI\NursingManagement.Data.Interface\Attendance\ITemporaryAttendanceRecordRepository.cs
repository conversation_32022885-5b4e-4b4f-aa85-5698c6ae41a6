﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ITemporaryAttendanceRecordRepository
    {
        /// <summary>
        /// 根据科室ID获取数据
        /// </summary>
        /// <param name="departmentID">部门id</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<TemporaryAttendanceRecordInfo>> GetListByDepartmentID(int departmentID, DateTime startDate, DateTime endDate);
        /// <summary>
        /// 根据主键ID获取数据
        /// </summary>
        /// <param name="recordID">记录ID</param>
        /// <returns></returns>
        Task<TemporaryAttendanceRecordInfo> GetDataByID(int recordID);
        /// <summary>
        /// 根据人员集合获取临时出勤记录
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<TemporaryAttendanceRecordInfo>> GetListByEmployeeIDs(List<string> employeeIDs, int departmentID, DateTime startDate, DateTime endDate);
    }
}
