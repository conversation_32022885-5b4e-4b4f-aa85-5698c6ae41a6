﻿namespace NursingManagement.ViewModels
{
    public class SubjectAssignView
    {
        /// <summary>
        /// 质控主题主键
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }
        /// <summary>
        ///  指派表格数据
        /// </summary>
        public List<AssignTableItem> AssignTableData { get; set; }
    };

    public class AssignTableItem
    {
        /// <summary>
        /// 质控科室
        /// </summary>
        public List<AssignDepartmentView> DepartmentViews;
        /// <summary>
        /// 质控人员
        /// </summary>
        public List<AssignEmployeeView> HierarchicalQCEmploy;
        /// <summary>
        /// 审核人员
        /// </summary>
        public List<AssignEmployeeView> VerifierEmployee;
        
    };

    public class AssignDepartmentView
    {
        /// <summary>
        /// 科室Code
        /// </summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string Department { get; set;}
        /// <summary>
        /// 本科室是否已考核
        /// </summary>
        public bool CheckFlag { get; set; }
        /// <summary>
        /// 上级部门 | 一般为片区
        /// </summary>
        public int UpperLevelDepartmentID { get; set; }
    } 

    public class AssignEmployeeView
    {
        /// <summary>
        /// 人员ID
        /// </summary>
        public string EmployeeID { get; set;}
        /// <summary>
        /// 人员姓名
        /// </summary>
        public string EmployeeName { get; set;}
        /// <summary>
        /// 人员姓名
        /// </summary>
        public string NamePinyin { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 层级
        /// </summary>
        public string CapabilityLevel { get; set; }
        /// <summary>
        /// 当前部门，护理管理组织架构的DepartmentID
        /// </summary>
        public int? DepartmentID { get; set; }
    }
}
