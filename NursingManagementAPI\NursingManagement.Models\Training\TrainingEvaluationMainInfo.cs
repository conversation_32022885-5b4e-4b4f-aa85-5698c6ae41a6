﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 培训评价主表
    /// </summary>
    [Serializable]
    [Table("TrainingEvaluationMain")]
    public class TrainingEvaluationMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 培训评价主表ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string TrainingEvaluationMainID { get; set; }
        /// <summary>
        /// 培训人员记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string TrainingLearnerID { get; set; }
        /// <summary>
        /// 评价类型(评价学生、评价老师，来源于SettingDictionary)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EvaluationType { get; set; }
        /// <summary>
        /// 被评价人员ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string BeEvaluationEmployeeID { get; set; }
        /// <summary>
        /// 模版ID
        /// </summary>
        [Column(TypeName ="varchar(32)")]
        public string DynamicFormRecordID { get; set; }
    }
}
