﻿using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCDetailRepository
    {
        /// <summary>
        /// 根据质控主记录获取明细数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCDetailInfo>> GetQCDetailByQCRecordID(string id);
        /// <summary>
        /// 获取明细内容：根据维护记录
        /// </summary>
        /// <param name="careMainID">维护记录主键</param>
        /// <returns></returns>
        Task<List<HierarchicalQCDetailView>> GetQCDetailByQCMainID(string careMainID);
        /// <summary>
        /// 获取明细内容：根据维护记录
        /// </summary>
        /// <param name="careMainID">维护记录主键</param>
        /// <returns></returns>
        Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfoByQCMainID(string careMainID);
        /// <summary>
        /// 获取需追踪考核明细
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <param name="score"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCDetailInfo>> GetQCDetailByQCMainIDs(List<string> careMainIDs);
        /// <summary>
        /// 获取考核内容
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        Task<List<NormalWorkingDetail>> GetQCDetailsByQCMainIDs(List<string> careMainIDs);
        /// <summary>
        /// 根据质控主记录获取明细数据
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfosByQCMainIDs(List<string> careMainIDs);
        /// <summary>
        /// 根据维护记录ID集合和组ID集合获取数据
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <param name="groupIDList"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfosByQCMainIDs(List<string> careMainIDs, List<int> groupIDList);
        /// <summary>
        /// 根据SubjectId获取ParentId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<List<int>> GetParentIdBySubjectId(string id);
        /// 根据质控主记录获取明细数据
        /// </summary>
        /// <param name="careMainID">主记录表主键</param>
        /// <returns></returns>
        Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfosByQCMainID(string careMainID);
    }
}
