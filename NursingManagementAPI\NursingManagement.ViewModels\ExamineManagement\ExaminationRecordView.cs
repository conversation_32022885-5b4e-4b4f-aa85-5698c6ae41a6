﻿namespace NursingManagement.ViewModels
{
    public class ExaminationRecordView
    {
        /// <summary>
        /// 考核主记录ID
        /// </summary>
        public string ExaminationRecordID { get; set; }

        /// <summary>
        /// 试卷主记录ID
        /// </summary>
        public string ExaminationPaperMainID { get; set; }

        /// <summary>
        /// 考核开始时间
        /// </summary>
        public DateTime StartDateTime { get; set; }

        /// <summary>
        /// 考核结束时间
        /// </summary>
        public DateTime EndDateTime { get; set; }

        /// <summary>
        /// 考核时长
        /// </summary>
        public decimal Duration { get; set; }

        /// <summary>
        /// 考核时长内容
        /// </summary>
        public string DurationName { get; set; }

        /// <summary>
        /// 考核说明
        /// </summary>
        public string Instructions { get; set; }

        /// <summary>
        /// 考核类型(配置在SettingDictionary中)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 新增人
        /// </summary>
        public string AddEmployeeName { get; set; }

        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime? AddDateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyDateTime { get; set; }

        /// <summary>
        /// 条件内容
        /// </summary>
        public string ConditionContent { get; set; }

        /// <summary>
        /// 条件表达式
        /// </summary>
        public string ConditionExpression { get; set; }

        /// <summary>
        /// 条件选择内容
        /// </summary>
        public List<FormDetailConditionView> Conditions { get; set; }

        /// <summary>
        /// 主考人（考察老师）
        /// </summary>
        public List<string> ExamineEmployeeID { get; set; }

        /// <summary>
        /// 总分
        /// </summary>
        public decimal TotalScore { get; set; }

        /// <summary>
        /// 考核名称
        /// </summary>
        public string ExaminationName { get; set; }

        /// <summary>
        /// 及格分数
        /// </summary>
        public decimal? PassingScore { get; set; }

        /// <summary>
        /// 最低答卷时长（分钟）
        /// </summary>
        public short? MinAnswerTime { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 二维码刷新时间（单位：秒）
        /// </summary>
        public int QRCodeRefreshTime { get; set; }

        /// <summary>
        /// 是否需要签到标记
        /// </summary>
        public bool SignInFlag { get; set; }

        /// <summary>
        /// 获取题库ID
        /// </summary>
        public string QuestionBankID { get; set; }

        /// <summary>
        /// 发布状态
        /// </summary>
        public bool PublishFlag { get; set; }

        /// <summary>
        /// 监考人、主考人信息
        /// </summary>
        public string ExamineEmployeeName { get; set; }

        /// <summary>
        /// 练习进度
        /// </summary>
        public string PracticeProgress { get; set; }

        /// <summary>
        /// 考核级别
        /// </summary>
        public string ExaminationLevel { get; set; }

        /// <summary>
        /// 考核级别名称(配置在SettingDictionary中)
        /// </summary>
        public string ExaminationLevelName { get; set; }

        /// <summary>
        /// 一页一题标记
        /// </summary>
        public bool? OnePageQuestionFlag { get; set; }
    }
}
