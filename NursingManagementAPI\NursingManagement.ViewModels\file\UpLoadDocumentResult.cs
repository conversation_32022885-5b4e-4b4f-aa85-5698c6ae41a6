﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 文件上传返回结果(根据文件管理服务返回对象一致
    /// </summary>
    public class UpLoadDocumentResult
    {
        public UpLoadDocumentResult()
        {
            UpLoadFlag = false;
            DocumentMainID = "";
            DocumentSize = 0;
            UpLoadMesage = "";
        }

        /// <summary>
        /// 文件主记录ID
        /// </summary>
        public string DocumentMainID { set; get; }

        /// <summary>
        ///文件标题
        ///</summary>
        public string DocumentTitle { get; set; }

        /// <summary>
        ///文件大小(KB)
        ///</summary>
        public int DocumentSize { get; set; }

        /// <summary>
        /// 文件上传是否成功
        /// </summary>
        public bool UpLoadFlag { get; set; }

        /// <summary>
        /// 文件上传结果信息
        /// </summary>
        public string UpLoadMesage { get; set; }

        /// <summary>
        /// 文件地址
        /// </summary>
        public string DocumentUrl { get; set; }
    }
}
