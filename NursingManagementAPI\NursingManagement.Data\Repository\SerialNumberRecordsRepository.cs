﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class SerialNumberRecordsRepository : ISerialNumberRecordsRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;

        public SerialNumberRecordsRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 获取流水号
        /// </summary>
        /// <param name="bizCategoryCode">业务分类码</param>
        /// <returns></returns>
        public async Task<SerialNumberRecordsInfo> GetSerialNumberByBizCategoryCode(string bizCategoryCode)
        {
            var serialNumberRecord = await _nursingManagementDbContext.SerialNumberRecordsInfos.Where(m => m.BizCategoryCode == bizCategoryCode && m.DeleteFlag != "*")
                .FirstOrDefaultAsync();
            return serialNumberRecord;
        }
    }
}
