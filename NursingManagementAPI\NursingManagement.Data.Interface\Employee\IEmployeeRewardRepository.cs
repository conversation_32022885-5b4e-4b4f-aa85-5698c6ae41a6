﻿using NursingManagement.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeRewardRepository
    {
        /// <summary>
        /// 根据员工工号获取员工奖惩信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeRewardInfo>>GetRecordListAsync(string employeeID); 
    }
}
