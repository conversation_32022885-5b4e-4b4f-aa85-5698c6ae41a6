﻿namespace NursingManagement.ViewModels
{
    public class DeletePostDescriptionView
    {
        /// <summary>
        /// 岗位编号
        /// </summary>
        public int PostID { get; set; }

        /// <summary>
        /// 岗位编码
        /// </summary>
        public string PostDescriptionCode { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 异动人员
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
    }
}
