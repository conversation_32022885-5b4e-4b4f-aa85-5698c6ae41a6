﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 排班模板相关业务服务层
    /// </summary>
    public interface ISchedulingTemplateService
    {
        /// <summary>
        ///  获取部门所有模板记录
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<SchedulingTemplateRecordView>> GetTemplateRecords(int departmentID);
        /// <summary>
        /// 获取排班标记集合
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingMarkView>> GetShiftSchedulingMarks(int departmentID);
        /// <summary>
        ///  获取排班模板数据
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <returns></returns>
        Task<SchedulingTemplateData> GetTemplateData(string schedulingTemplateRecordID);
        /// <summary>
        /// 保存排班模板
        /// </summary>
        /// <param name="schedulingTemplateView"></param>
        /// <returns></returns>
        Task<bool> SaveTemplateRecord(SchedulingTemplateView schedulingTemplateView);
        /// <summary>
        /// 复制排班模板
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> CopyTemplate(string schedulingTemplateRecordID, string employeeID);
        /// <summary>
        /// 删除排班模板
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteTemplate(string schedulingTemplateRecordID, string employeeID);
        /// <summary>
        /// 更新排班模板状态
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <param name="statusCode"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> UpdateTemplateStatus(string schedulingTemplateRecordID, string statusCode, string employeeID);

    }
}
