﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    ///  岗位部门-能级对照表    
    /// Composite Primary Key: t.PostID, t.DepartmentID, t.CapabilityLevelID,t.HospitalID
    /// </summary>
    [Table("DepartmentPostToCapabilityLevel")]
    public class DepartmentPostToCapabilityLevelInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int DepartmentPostToCapabilityLevelID { get;set;}
        /// <summary>
        /// 岗位序号    
        /// </summary>
        public int PostID { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 能级编码    
        /// </summary>
        public int CapabilityLevelID { get; set; }

        /// <summary>
        /// 医院序号    
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 条件，>=、<=  EnergyLevelID对应EnergyLeve表中的Leve字段  
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Condition { get; set; }

        /// <summary>
        /// 生效日期，用于查询历年晋升标准记录    
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 到期时间    
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 状态 ，0、停用、1、启用    
        /// </summary>
        public string StatusCode { get; set; }

    }
}