﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 审批流程控制器
    /// </summary>
    [Route("api/ApproveProcess")]
    [ApiController]
    [EnableCors("any")]
    public class ApproveProcessController : Controller
    {
        private readonly IApproveProcessService _approveProcessService;
        private readonly ISessionService _session;

        /// <summary>
        /// 审批流程控制器
        /// </summary>
        /// <param name="approveProcessService">审批流程业务</param>
        /// <param name="session">session</param>
        /// <returns></returns>
        public ApproveProcessController(IApproveProcessService approveProcessService, ISessionService session)
        {
            _approveProcessService = approveProcessService;
            _session = session;
        }

        #region 查询
        /// <summary>
        /// 获取制订的审批流程
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApproveProcesses")]
        public async Task<IActionResult> GetApproveProcesses()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _approveProcessService.GetApproveProcesses(session.HospitalID);

            return result.ToJson();
        }
        /// <summary>
        /// 获取审批流程节点
        /// </summary>
        /// <param name="approveProcessID">审批流程主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApproveProcessNodes")]
        public async Task<IActionResult> GetApproveProcessNodes(string approveProcessID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveProcessService.GetApproveProcessNodes(approveProcessID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取分类已启用科室集合
        /// </summary>
        /// <param name="typeCode">分类码</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEnableDepartmentIDsByProveCategory")]
        public async Task<IActionResult> GetEnableDepartmentIDsByProveCategory(string typeCode)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveProcessService.GetEnableDepartmentIDsByProveCategory(typeCode);
            return result.ToJson();
        }
        #endregion
        /// <summary>
        /// 新增审批流程
        /// </summary>
        /// <param name="saveView">保存的View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddApproveProcess")]
        public async Task<IActionResult> AddApproveProcess([FromBody] SaveApproveProcessView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(saveView, session);
            result.Data = await _approveProcessService.AddApproveProcess(saveView);
            return result.ToJson();
        }
        /// <summary>
        /// 修改审批流程，仅限于未启用的流程
        /// </summary>
        /// <param name="saveView">保存的View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateApproveProcess")]
        public async Task<IActionResult> UpdateApproveProcess([FromBody] SaveApproveProcessView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(saveView, session);
            result.Data = await _approveProcessService.UpdateApproveProcess(saveView);
            return result.ToJson();
        }
        /// <summary>
        /// 启用审批流程
        /// </summary>
        /// <param name="enableView">启用View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("EnableApproveProcess")]
        public async Task<IActionResult> EnableApproveProcess([FromBody] ApproveProcessStatusChangeView enableView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveProcessService.EnableApproveProcess(enableView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 停用审批流程
        /// </summary>
        /// <param name="disableView">停用View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DisableApproveProcess")]
        public async Task<IActionResult> DisableApproveProcess([FromBody] ApproveProcessStatusChangeView disableView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveProcessService.DisableApproveProcess(disableView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除审批流程
        /// </summary>
        /// <param name="deleteView">删除View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteApproveProcess")]
        public async Task<IActionResult> DeleteApproveProcess([FromBody] ApproveProcessStatusChangeView deleteView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveProcessService.DeleteApproveProcess(deleteView, session.EmployeeID);
            return result.ToJson();
        }
    }
}
