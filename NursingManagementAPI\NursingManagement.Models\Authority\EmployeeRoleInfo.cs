using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员角色表
    /// </summary>
    [Serializable]
    [Table("EmployeeRole")]
    public class EmployeeRoleInfo : MutiModifyInfo
    {

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 用户编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 角色权限编号
        /// </summary>
        public int AuthorityRoleID { get; set; }
    }
}
