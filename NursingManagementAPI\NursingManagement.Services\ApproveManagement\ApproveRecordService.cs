﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Interface.Employee;
using NursingManagement.Data.Repository;
using NursingManagement.Models;
using NursingManagement.Services.ApproveManagement;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Text.RegularExpressions;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    public class ApproveRecordService : IApproveRecordService
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IApproveProcessRepository _approveProcessRepository;
        private readonly IApproveProcessNodeRepository _approveProcessNodeRepository;
        private readonly IApproveDetailRepository _approveDetailRepository;
        private readonly IApproveRecordRepository _approveRecordRepository;
        private readonly IApproveProcessNodeDetailRepository _approveProcessNodeDetailRepository;
        private readonly IApproveMainRepository _approveMainRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDictionaryService _dictionaryService;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IApproveProcessService _approveProcessService;
        private readonly ProcessingAfterApprovalFactory _processingAfterApprovalFactory;
        private readonly IAdministrationDictionaryRepository _administrationDictionaryRepository;
        private readonly IEmployeeToJobRepository _employeeToJobRepository;
        private readonly IMessageService _messageService;
        private readonly IServiceProvider _serviceProvider;
        private readonly ISerialNumberService _serialNumberService;
        private readonly IDynamicColumnAttributeRepository _dynamicColumnAttributeRepository;
        private readonly ApproveDynamicColumnService _approveDynamicColumnService;
        private readonly IRouterListRepository _routerListRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IDepartmentToApproveProcessRepository _departmentToApproveProcessRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly IEmployeeSecondmentRecordRepository _employeeSecondmentRecordRepository;
        private readonly IShiftSchedulingDetailRepository _shiftSchedulingDetailRepository;
        private readonly IShiftSchedulingDetailMarkRepository _shiftSchedulingDetailMarkRepository;
        public ApproveRecordService(
            IUnitOfWork unitOfWork,
            IDictionaryService dictionaryService,
            IApproveProcessRepository approveProcessRepository,
            IApproveProcessNodeRepository approveProcessNodeRepository,
            IApproveDetailRepository approveDetailRepository,
            IApproveRecordRepository approveRecordRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IApproveProcessNodeDetailRepository approveProcessNodeDetailRepository,
            IApproveMainRepository approveMainRepository,
            IApproveProcessService approveProcessCommonService,
            ProcessingAfterApprovalFactory processingAfterApprovalFactory,
            IAdministrationDictionaryRepository administrationDictionaryRepository,
            IEmployeeToJobRepository employeeToJobRepository,
            IServiceProvider serviceProvider,
            ISerialNumberService serialNumberService,
            IDynamicColumnAttributeRepository dynamicColumnAttributeRepository,
            ApproveDynamicColumnService approveDynamicColumnService,
            IRouterListRepository routerListRepository,
            ISettingDictionaryRepository settingDictionaryRepository,
            IDepartmentToApproveProcessRepository departmentToApproveProcessRepository,
            ISettingDictionaryService settingDictionaryService,
            IEmployeeSecondmentRecordRepository employeeSecondmentRecordRepository,
            IShiftSchedulingDetailRepository shiftSchedulingDetailRepository,
            IShiftSchedulingDetailMarkRepository shiftSchedulingDetailMarkRepository
            )
        {
            _unitOfWork = unitOfWork;
            _dictionaryService = dictionaryService;
            _approveProcessRepository = approveProcessRepository;
            _approveProcessNodeRepository = approveProcessNodeRepository;
            _approveDetailRepository = approveDetailRepository;
            _approveRecordRepository = approveRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _approveProcessNodeDetailRepository = approveProcessNodeDetailRepository;
            _approveMainRepository = approveMainRepository;
            _approveProcessService = approveProcessCommonService;
            _processingAfterApprovalFactory = processingAfterApprovalFactory;
            _administrationDictionaryRepository = administrationDictionaryRepository;
            _employeeToJobRepository = employeeToJobRepository;
            _serviceProvider = serviceProvider;
            _messageService = (IMessageService)serviceProvider.GetService(typeof(IMessageService));
            _serialNumberService = serialNumberService;
            _dynamicColumnAttributeRepository = dynamicColumnAttributeRepository;
            _approveDynamicColumnService = approveDynamicColumnService;
            _routerListRepository = routerListRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _departmentToApproveProcessRepository = departmentToApproveProcessRepository;
            _settingDictionaryService = settingDictionaryService;
            _employeeSecondmentRecordRepository = employeeSecondmentRecordRepository;
            _shiftSchedulingDetailRepository = shiftSchedulingDetailRepository;
            _shiftSchedulingDetailMarkRepository = shiftSchedulingDetailMarkRepository;
        }


        /// <summary>
        /// 根据审批主记录获取审批呈现内容
        /// </summary>
        /// <param name="approveRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<ApproveView>> GetApproveDetailView(string approveRecordID, string employeeID)
        {
            List<ApproveView> approveViews = null;
            // 获取 并检查 继续执行所必须的变量
            var (approveRecordInfo, approveProcessNodes, approveMainInfos, approveDetailInfos, headApproveMain, headApproveDetail) =
                await CheckAndGetNeccessaryVariableAsync(approveRecordID);

            var employeeIDs = approveDetailInfos.Where(m => m.ApproveEmployeeID != null).Select(m => m.ApproveEmployeeID).ToList();
            employeeIDs.Add(approveRecordInfo.AddEmployeeID);
            var employeeDict = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);

            //生成头结点对应的View - 发起审批的节点
            var headApproveView = CreateHeadApproveView(approveRecordInfo, employeeDict);
            approveViews = new List<ApproveView> { headApproveView };
            //生成剩余节点
            var nextApproveMain = headApproveMain;
            while (nextApproveMain != null)
            {
                var approveView = SetApproveDetailViews(nextApproveMain, approveDetailInfos, employeeDict, approveProcessNodes);
                approveViews.Add(approveView);
                //需要审批的节点之后的节点不显示
                if (string.IsNullOrEmpty(approveView.StatusCode) || approveView.StatusCode == ApprovalStatus.Rejected.ToString("d"))
                {
                    break;
                }
                nextApproveMain = approveMainInfos.Find(m => m.ApproveMainID == nextApproveMain.NextApproveMainID);
            }

            return approveViews;
        }

        /// <summary>
        /// 获取 并检查 方法继续执行所必须的变量
        /// </summary>
        /// <param name="approveRecordID">主记录ID</param>
        /// <returns></returns>
        private async Task<(ApproveRecordInfo, List<ApproveProcessNodeInfo>, List<ApproveMainInfo>, List<ApproveDetailInfo>, ApproveMainInfo, ApproveDetailInfo)> CheckAndGetNeccessaryVariableAsync(string approveRecordID)
        {
            var approveRecordInfo = await _approveRecordRepository.GetApproveRecordByRecordIDAsync(approveRecordID);
            if (approveRecordInfo == null)
            {
                return default;
            }
            var approveProcessNodes = await _approveProcessNodeRepository.GetApproveProcessNodeInfos(approveRecordInfo.ApproveProcessID);
            if (approveProcessNodes.Count <= 0)
            {
                return default;
            }
            var approveMainInfos = await _approveMainRepository.GetApproveMainsByRecordIDAsync(approveRecordID);
            if (approveMainInfos.Count <= 0)
            {
                return default;
            }
            var approveDetailInfos = await _approveDetailRepository.GetApproveDetailsByMainIDAsync(approveMainInfos.Select(m => m.ApproveMainID).ToArray());
            //获取流程的头结点
            var headNode = GetHeadNode(approveProcessNodes);
            if (headNode == null)
            {
                _logger.Error("异常：审批流程中没有找到头结点");
                return default;
            }
            // 寻找业务明细中的头部
            var headApproveMain = approveMainInfos.Find(m => m.ApproveNodeID == headNode.ApproveNodeID);
            if (headApproveMain == null)
            {
                _logger.Error("异常：业务审批中找不到头结点对应的主记录");
                return default;
            }
            var headApproveDetail = approveDetailInfos.Where(m => m.ApproveMainID == headApproveMain.ApproveMainID).FirstOrDefault();
            if (headApproveDetail == null)
            {
                _logger.Error("流程错误,没有发起人节点");
                return default;
            }
            return (approveRecordInfo, approveProcessNodes, approveMainInfos, approveDetailInfos, headApproveMain, headApproveDetail);
        }

        /// <summary>
        /// 组装前端呈现业务审批流程点的ApproveView 集合
        /// </summary>
        /// <param name="mainInfo">审批主记录</param>
        /// <param name="allApproveDetails">审批明细集合</param>
        /// <param name="employeeDict">工号-姓名键值对</param>
        /// <param name="processNodes">审批流程节点集合</param>
        private static ApproveView SetApproveDetailViews(ApproveMainInfo mainInfo, List<ApproveDetailInfo> allApproveDetails, Dictionary<string, string> employeeDict, List<ApproveProcessNodeInfo> processNodes)
        {
            var approveView = new ApproveView
            {
                ApproveMainID = mainInfo.ApproveMainID,
                ApproveModel = mainInfo.ApproveModel,
                ApproveDetailViews = new List<ApproveDetailView>(),
                ApproveNodeName = processNodes.Find(m => m.ApproveNodeID == mainInfo.ApproveNodeID)?.ApproveNodeName,
                StatusCode = mainInfo.StatusCode
            };
            // 将节点添加到集合中
            var nextApproveDetails = allApproveDetails.Where(m => m.ApproveMainID == mainInfo.ApproveMainID).ToList();
            foreach (var nextApproveDetail in nextApproveDetails)
            {
                var approveDetailView = new ApproveDetailView
                {
                    ApproveRecordID = mainInfo.ApproveRecordID,
                    ApproveDetailID = nextApproveDetail.ApproveDetailID,
                    ApproveModel = nextApproveDetail.ApproveModel,
                    StatusCode = nextApproveDetail.StatusCode,
                    ApproveSuggestions = nextApproveDetail.ApproveSuggestions,
                    ApproveDateTime = nextApproveDetail.ApproveDateTime,
                    ApproveEmployeeID = nextApproveDetail.ApproveEmployeeID,
                    ApproveEmployeeName = employeeDict.TryGetValue(nextApproveDetail.ApproveEmployeeID, out var name) ? name : "",
                    PreApproveEmployeeIDs = nextApproveDetail.PreApproveEmployeeID?.Split("||", StringSplitOptions.RemoveEmptyEntries)?.ToList() ?? new List<string> { },
                    ModifyDateTime = nextApproveDetail.ModifyDateTime,
                };
                approveView.ApproveDetailViews.Add(approveDetailView);
            }
            return approveView;
        }

        /// <summary>
        /// 创建第一个节点呈现
        /// </summary>
        /// <param name="approveRecordInfo">审批主记录</param>
        /// <param name="employeeDict">工号-姓名键值对</param>
        /// <returns></returns>
        private static ApproveView CreateHeadApproveView(ApproveRecordInfo approveRecordInfo, Dictionary<string, string> employeeDict)
        {
            // 当前层
            var headApproveView = new ApproveView
            {
                HeadFlag = true,
                StatusCode = ApprovalStatus.Completed.ToString("d"),
                Content = approveRecordInfo.Content,
                ApproveModel = "1",
                ApproveNodeName = "发起人",
                ApproveDetailViews = new List<ApproveDetailView> {
                    new ApproveDetailView
                    {
                        ApproveDateTime = approveRecordInfo.AddDateTime,
                        ApproveEmployeeID = approveRecordInfo.AddEmployeeID,
                        ApproveEmployeeName = employeeDict.TryGetValue( approveRecordInfo.AddEmployeeID,out var name) ? name :"",
                    }
                },
            };
            return headApproveView;
        }

        /// <summary>
        /// 新增审批主记录和明细记录（各上级业务模块调用此方法）
        /// </summary>
        /// <param name="view">审批参数</param>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <param name="saveChange"></param>
        /// <returns></returns>
        public async Task<bool> AddApproveRecordAndDetailAsync(ApproveMainAndDetailParamView view, string approveProcessID)
        {
            if (string.IsNullOrEmpty(approveProcessID))
            {
                _logger.Error($"提交审批失败，无法获取审批流程view ={ListToJson.ToJson(view)}");
                return false;
            }
            var processNodes = await _approveProcessNodeRepository.GetApproveProcessNodeInfos(approveProcessID);
            if (processNodes.Count <= 0)
            {
                _logger.Error($"没有找到对应的审批流程配置 ApproveProcessID = {approveProcessID}");
                return false;
            }
            //获取流程的头结点
            var headNode = GetHeadNode(processNodes);
            if (headNode == null)
            {
                _logger.Error("异常：审批流程中没有找到头结点");
                return false;
            }
            //新增ApproveRecordInfo到数据库
            var approveRecordInfo = await CreateApproveRecordInfo(view, approveProcessID);
            string headApproveMainID = null;
            //新增ApproveDetailInfo到数据库
            if (!await InsertApproveDetailInfos(headNode, null, view.DepartmentID))
            {
                return false;
            }
            // 审批记录表中存储第一个需要审批的节点主表ID
            approveRecordInfo.ApproveMainID = headApproveMainID;
            var success = await _unitOfWork.SaveChangesAsync() > 0;
            // 通知审批状态(不等待|独立与当前线程)
            await NotifyApprovalResultAsync(approveRecordInfo.CloneObj(), null);
            return success;

            async Task<bool> InsertApproveDetailInfos(ApproveProcessNodeInfo node, ApproveDetailInfo detail, int departmentID)
            {
                ApproveProcessNodeInfo nextNode = node;
                string nextApproveMainID = null;
                //循环新增多个审批节点
                while (nextNode != null)
                {
                    // 首先访问当前节点
                    var approveMainInfo = await CreateApproveMainInfo(nextNode, approveRecordInfo.ApproveRecordID, view.AddEmployeeID, nextApproveMainID);
                    // 赋值头结点
                    headApproveMainID ??= approveMainInfo.ApproveMainID;
                    var nodeDetails = await _approveProcessNodeDetailRepository.GetNodeDetailsAsync(nextNode.ApproveNodeID);
                    if (nodeDetails.Count <= 0)
                    {
                        _logger.Error($"异常：审批流程中找不到当前节点{nextNode.ApproveNodeName}中的明细记录");
                        return false;
                    }
                    var flag = await CreateApproveDetailRecordInfo(nodeDetails, approveMainInfo.ApproveMainID, view, nextNode.ApproveModel);
                    if (!flag)
                    {
                        return false;
                    }
                    if (!string.IsNullOrEmpty(nextNode.NextNodeID))
                    {
                        //生成下一个主表节点对应的ID
                        approveMainInfo.NextApproveMainID = approveMainInfo.GetId();
                        nextApproveMainID = approveMainInfo.NextApproveMainID;
                        nextNode = processNodes.Find(m => m.ApproveNodeID == nextNode.NextNodeID);
                        continue;
                    }
                    break;
                }
                return true;
            }
        }

        /// <summary>
        /// 获取审批流程中的第一个节点
        /// </summary>
        /// <param name="processNodes">审批流程节点集合</param>
        /// <returns></returns>
        private static ApproveProcessNodeInfo GetHeadNode(List<ApproveProcessNodeInfo> processNodes)
        {
            foreach (ApproveProcessNodeInfo node in processNodes)
            {
                if (processNodes.Find(m => m.NextNodeID == node.ApproveNodeID) == null)
                {
                    return node;
                }
            }
            return null;
        }

        /// <summary>
        /// 创建审批主表数据库实体对象
        /// </summary>
        /// <param name="node"></param>
        /// <param name="approveRecordID"></param>
        /// <param name="addEmployeeID"></param>
        /// <param name="nextApproveMainID"></param>
        /// <returns></returns>
        private async Task<ApproveMainInfo> CreateApproveMainInfo(ApproveProcessNodeInfo node, string approveRecordID, string addEmployeeID, string nextApproveMainID)
        {
            var now = DateTime.Now;
            var approveMain = new ApproveMainInfo
            {
                ApproveModel = node.ApproveModel,
                ApproveNodeID = node.ApproveNodeID,
                ApproveRecordID = approveRecordID,
                // 循环中，产生下一个节点MainInfo记录时 进行赋值
                NextApproveMainID = "",
                ApproveDateTime = null,
                StatusCode = null,
                AddDateTime = now,
                AddEmployeeID = addEmployeeID,
                ModifyDateTime = now,
                ModifyEmployeeID = addEmployeeID,
                DeleteFlag = "",
            };
            approveMain.ApproveMainID = nextApproveMainID ?? approveMain.GetId();
            await _unitOfWork.GetRepository<ApproveMainInfo>().InsertAsync(approveMain);

            return approveMain;
        }

        /// <summary>
        /// 根据节点和审批主记录创建明细记录
        /// </summary>
        /// <param name="nodeDetails">审批节点明细表数据</param>
        /// <param name="approveMainID">审批主表主键</param>
        /// <param name="view">审批主表与明细表参数视图</param>
        /// <param name="approveModel">审批类型</param>
        /// <returns></returns>
        private async Task<bool> CreateApproveDetailRecordInfo(List<ApproveProcessNodeDetailInfo> nodeDetails, string approveMainID, ApproveMainAndDetailParamView view, string approveModel)
        {
            var now = DateTime.Now;
            var detailInfos = new List<ApproveDetailInfo>();
            foreach (var nodeDetail in nodeDetails)
            {
                if (nodeDetail.NodeDetailType == ApproveProcessNodeDetailType.SelfSelected && view.SelfSelectedApprover.Count <= 0)
                {
                    throw new Exception($"当前审批节点nodeDetailID{nodeDetail.ApproveNodeDetailID}找不到没有自选审批人信息");
                }
                var detailInfo = new ApproveDetailInfo
                {
                    ApproveMainID = approveMainID,
                    ApproveNodeDetailID = nodeDetail.ApproveNodeDetailID,
                    ApproveEmployeeID = "",
                    ApproveModel = approveModel,
                    PreApproveEmployeeID = await GetPreApproveEmployeeIDs(nodeDetail, view.DepartmentID, view.SelfSelectedApprover),
                    ApproveSuggestions = null,
                    ApproveDateTime = null,
                    StatusCode = null,
                    AddDateTime = now,
                    AddEmployeeID = view.AddEmployeeID,
                    ModifyDateTime = now,
                    ModifyEmployeeID = view.AddEmployeeID,
                    DeleteFlag = "",

                };
                detailInfo.ApproveDetailID = detailInfo.GetId();
                detailInfos.Add(detailInfo);
            }
            if (detailInfos.All(m => string.IsNullOrEmpty(m.PreApproveEmployeeID)))
            {
                return false;
            }
            await _unitOfWork.GetRepository<ApproveDetailInfo>().InsertAsync(detailInfos);
            return true;
        }

        /// <summary>
        /// 根据明细类型获取在职人员工号，并使用||拼接成一个字符串
        /// </summary>
        /// <param name="nodeDetail">审批节点明细</param>
        /// <param name="initiatorDepartment">审批发起人所在科室ID</param>
        /// <returns>获取不到会返回空串</returns>
        private async Task<string> GetPreApproveEmployeeIDs(ApproveProcessNodeDetailInfo nodeDetail, int initiatorDepartment, List<string> selfSelectedApprover)
        {
            // 根据职位编号获取工号
            async Task<string> getEmployeesByPosition()
            {
                // 节点明细有科室，则取对应科室；否则取发起人所在科室
                var employeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(nodeDetail.DataValue, nodeDetail.DepartmentID ?? initiatorDepartment);
                if (employeeIDs.Count == 0)
                {
                    return "";
                }
                return employeeIDs.Aggregate((a, b) => a + "||" + b);
            }
            var employeeIDs = nodeDetail.NodeDetailType switch
            {
                ApproveProcessNodeDetailType.Employee => nodeDetail.DataValue,
                ApproveProcessNodeDetailType.Position => await getEmployeesByPosition(),
                ApproveProcessNodeDetailType.SelfSelected => selfSelectedApprover.Aggregate((a, b) => a + "||" + b),
                _ => "",
            };
            return employeeIDs;
        }
        /// <summary>
        /// 创建审批主记录
        /// </summary>
        /// <param name="view">各个业务添加审批流业务的公共参数</param>
        /// <param name="approveProcessID">流程ID</param>
        /// <returns></returns>
        private async Task<ApproveRecordInfo> CreateApproveRecordInfo(ApproveMainAndDetailParamView view, string approveProcessID)
        {
            var approveRecordInfo = new ApproveRecordInfo
            {
                SourceID = view.SourceID,
                ApproveRecordID = view.ApproveRecordID,
                StatusCode = ApprovalStatus.Waiting.ToString("d"),
                AddDateTime = DateTime.Now,
                AddEmployeeID = view.AddEmployeeID,
                ModifyDateTime = DateTime.Now,
                ModifyEmployeeID = view.AddEmployeeID,
                ApproveProcessID = approveProcessID,
                Content = view.Content,
                DeleteFlag = "",
                SerialNumber = await _serialNumberService.GetSerialNumber(view.ProveCategory)
            };
            if (string.IsNullOrEmpty(approveRecordInfo.ApproveRecordID))
            {
                approveRecordInfo.ApproveRecordID = approveRecordInfo.GetId();
            }
            await _unitOfWork.GetRepository<ApproveRecordInfo>().InsertAsync(approveRecordInfo);
            return approveRecordInfo;
        }

        /// <summary>
        /// 获取需要当前登录账号审批的审批记录
        /// TODO 获取数据的顺序存在问题，因为从ApproveDetail来判断是否需要管理者进行审批的，所以
        /// 可能需要优化调整成遍历detail表，回查main和detail
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="departmentID">人员所在科室ID</param>
        /// <param name="proveCategory">审批类型</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<dynamic>> GetApproveRecordViewAsync(string employeeID, int departmentID, string proveCategory, DateTime? startDate, DateTime? endDate)
        {
            // 按照审批分类码筛选
            var (processID, _) = await _approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(proveCategory, departmentID);
            if (string.IsNullOrEmpty(processID))
            {
                return null;
            }
            var approveRecordInfos = await _approveRecordRepository.GetUnCompletedApproveRecordsAsNoTrackAsync(processID, startDate, endDate);
            if (approveRecordInfos.Count <= 0)
            {
                return null;
            }
            // 筛选出 （未审批的 && 当前账号需要审批的） 节点;
            var mainIDArrays = approveRecordInfos.Select(m => m.ApproveMainID).ToArray();
            var approveDetailInfos = await _approveDetailRepository.GetApproveDetailsByMainIDAsync(mainIDArrays);
            var unApproveDetails = approveDetailInfos.Where(m => string.IsNullOrEmpty(m.StatusCode) && m.PreApproveEmployeeID.Contains(employeeID)).ToList();
            if (unApproveDetails.Count <= 0)
            {
                return null;
            }
            // 过滤出当前节点需要登录人员审批的记录
            approveRecordInfos = approveRecordInfos.Where(m => unApproveDetails.Exists(n => n.ApproveMainID == m.ApproveMainID)).ToList();
            //补充审批主记录View类对应的内容
            var views = await GetApproveRecordViews(approveRecordInfos, proveCategory);
            // 正排序
            return views.OrderByDescending(m => m.addDateTime).ToList();

        }

        /// <summary>
        /// 获取已经完成的历史审批记录（当前用户参与的）
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="proveCategory">审批类别码</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public async Task<List<dynamic>> GetHistoryApproveRecordByEmployeeIDAsync(string employeeID, string proveCategory, int departmentID, DateTime? startDate, DateTime? endDate)
        {
            // 按照审批分类码筛选
            var (processID, _) = await _approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(proveCategory, departmentID);
            if (string.IsNullOrEmpty(processID))
            {
                return null;
            }
            List<string> approveEmployeeIDs = [employeeID];
            // 获取参与审批的相关岗位 的管理人员ID集合
            var jobCodes = await _approveProcessNodeDetailRepository.GetJobCodesByProcessIDAndNodeTypeAsync(processID);
            if (jobCodes.Count > 0)
            {
                var employeeIDsOfTheSameJobCode = await _employeeToJobRepository.GetEmployeeIDsByDepartmentIDAndJobCodesAsync(jobCodes, departmentID);
                if (employeeIDsOfTheSameJobCode.Contains(employeeID))
                {
                    approveEmployeeIDs.AddRange(employeeIDsOfTheSameJobCode);
                }
            }
            var approveRecordInfos = await _approveRecordRepository.GetCompletedApproveRecordsAsync(approveEmployeeIDs.Distinct().ToList(), processID, startDate, endDate);
            if (approveRecordInfos.Count <= 0)
            {
                return null;
            }
            //补充审批主记录View类对应的内容
            var recordViews = await GetApproveRecordViews(approveRecordInfos, proveCategory);

            return recordViews.OrderByDescending(m => m.addDateTime).ToList();
        }

        /// <summary>
        /// 动态拼接前端呈现数据
        /// </summary>
        /// <param name="recordInfos">审批主记录</param>
        /// <param name="proveCategory">审批类别码</param>
        /// <returns></returns>
        private async Task<List<dynamic>> GetApproveRecordViews(List<ApproveRecordInfo> recordInfos, string proveCategory)
        {
            if (string.IsNullOrEmpty(proveCategory))
            {
                return new List<dynamic>();
            }
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveRecord",
                SettingTypeValue = "StatusCode"
            };
            var statusDict = await _settingDictionaryService.GetSettingDictionaryDict(settingDictionaryParams);
            var personalDatas = await _employeePersonalDataRepository.GetIDAndNameData();
            // 获取各个来源表中的值
            var sourceDicts =
             await _approveDynamicColumnService.GetTableListByProveCategory(proveCategory, recordInfos.Select(m => m.SourceID).ToList());
            //拼接返回参数
            List<dynamic> resultList = new List<dynamic>();

            var approveRecordIDList = recordInfos.Select(m=>m.ApproveRecordID).ToList();
            var approveEmployeeIDs = await _approveDetailRepository.GetApproveEmployeeIDByRecordIDsAsync(approveRecordIDList);
            foreach (var item in recordInfos)
            {
                var approveEmployeeID = "";
                if (approveEmployeeIDs.ContainsKey(item.ApproveRecordID))
                {
                    approveEmployeeID = approveEmployeeIDs[item.ApproveRecordID];
                }
                var dictEntity = SetApproveDynamicView(item, personalDatas, statusDict, approveEmployeeID);
                resultList.Add(dictEntity);
                if (sourceDicts == null || !sourceDicts.TryGetValue(item.SourceID, out var sourceDict))
                {
                    continue;
                }
                // 将审批来源表对象的属性值赋值给动态对象
                foreach (var field in sourceDict)
                {
                    dictEntity[field.Key] = field.Value;
                }
            }
            return resultList;
        }
        /// <summary>
        /// 设置审批表相关字段
        /// </summary>
        /// <param name="item"></param>
        /// <param name="personalDatas"></param>
        /// <param name="statusDict"></param>
        /// <returns></returns>
        private  IDictionary<string, Object> SetApproveDynamicView(ApproveRecordInfo item, List<EmployeePersonalDataListView> personalDatas, List<SelectOptionsView> statusDict,string approveEmployeeID)
        {
            dynamic dynamicEntity = new ExpandoObject();
            var dictEntity = dynamicEntity as IDictionary<string, Object>;
            dictEntity["approveRecordID"] = item.ApproveRecordID;
            dictEntity["approveStatusCode"] = item.StatusCode;
            dictEntity["approveStatus"] = statusDict.Find(n => n.Value.Equals(item.StatusCode))?.Label;
            dictEntity["content"] = item.Content;
            dictEntity["completeDateTime"] = item.CompleteDateTime;
            dictEntity["deleteFlag"] = "";
            dictEntity["addDateTime"] = item.AddDateTime;
            dictEntity["addEmployeeName"] = personalDatas.Find(n => n.EmployeeID == item.AddEmployeeID)?.EmployeeName ?? item.AddEmployeeID;
            dictEntity["approveEmployeeName"] = personalDatas.Find(n => n.EmployeeID == approveEmployeeID)?.EmployeeName ?? approveEmployeeID;
            //用来弹窗发起审批的功能页面
            dictEntity["sourceID"] = item.SourceID;
            dictEntity["revokeReason"] = item.Reason;
            return dynamicEntity;
        }

        /// <summary>
        /// 保存审批结果
        /// </summary>
        /// <param name="approveParams"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> SaveApprovalAsync(ApproveDetailInfo approveParams, string employeeID)
        {
            if (approveParams == null || approveParams.ApproveDetailID == null || approveParams.ApproveMainID == null)
            {
                _logger.Error("参数异常");
                return false;
            }
            var currDetail = await _approveDetailRepository.GetApproveDetailByDetailIDAsync(approveParams.ApproveDetailID);
            if (currDetail == null)
            {
                _logger.Error($"保存审批结果失败,根据参数中的ApproveDetailID无法成功获取审批明细记录，Params={ListToJson.ToJson(approveParams)}");
            }
            // 修改审批明细
            currDetail.ApproveSuggestions = approveParams.ApproveSuggestions;
            currDetail.StatusCode = approveParams.StatusCode;
            currDetail.ApproveEmployeeID = employeeID;
            currDetail.ApproveDateTime = DateTime.Now;
            currDetail.Modify(employeeID);
            ApproveMainInfo currApproveMain = await _approveMainRepository.GetApproveMainByIDAsync(currDetail.ApproveMainID);
            if (currApproveMain == null)
            {
                _logger.Error($"根据ApproveMainID={currDetail.ApproveMainID}");
                return false;
            }
            var approveRecordInfo = await _approveRecordRepository.GetApproveRecordByRecordIDAsync(currApproveMain.ApproveRecordID);
            if (approveRecordInfo == null)
            {
                _logger.Error($"根据ApproveRecordID={currApproveMain.ApproveRecordID}");
                return false;
            }
            // 顺序签
            if (currApproveMain.ApproveModel == ApproveModelType.Sequential.ToString("d"))
            {
                UpdateSequentialApproval(currDetail, currApproveMain, approveRecordInfo);
            }
            // 会签
            if (currApproveMain.ApproveModel == ApproveModelType.Parallel.ToString("d"))
            {
                await UpdateCollectiveApprovalAsync(currDetail, currApproveMain, approveRecordInfo);
            }
            // 或签
            if (currApproveMain.ApproveModel == ApproveModelType.AnyOne.ToString("d"))
            {
                UpdateParallelApprovalAsync(currDetail, currApproveMain, approveRecordInfo);
            }
            if (approveRecordInfo.SerialNumber.Contains("HR"))
            {
               await DeelEmployeeSecondmentScheduleData(approveRecordInfo.SourceID, employeeID);
            }
            var success = await _unitOfWork.SaveChangesAsync() > 0;
            if (!success)
            {
                _logger.Error("保存审批结果失败");
                return false;
            }
            //每个人审批过后都会调用工厂，实时更新状态
            await ProcessingAfterApproval(approveRecordInfo, currApproveMain);

            return true;
        }

        /// <summary>
        /// 每一个节点审批结束后的逻辑处理
        /// </summary>
        /// <param name="approveRecordInfo">审批主记录</param>
        /// <param name="currApproveMain">当前审批主表记录</param>
        /// <returns></returns>
        private async Task ProcessingAfterApproval(ApproveRecordInfo approveRecordInfo, ApproveMainInfo currApproveMain)
        {
            var factory = await _processingAfterApprovalFactory.SwitchProcessingAsync(approveRecordInfo.ApproveProcessID);
            ProcessAfterApprovalView view = new()
            {
                ApprovalResult = approveRecordInfo.StatusCode,
                InformContent = "审批结束通知发起人审批结束",
                InitiatorEmployeeID = approveRecordInfo.AddEmployeeID,
                SourceID = approveRecordInfo.SourceID,
            };
            if (factory == null)
            {
                return;
            }
            var paths = await factory?.ProcessAfterApprovalAsync(view);
            // 通知审批状态(不等待|独立与当前线程)
            await NotifyApprovalResultAsync(approveRecordInfo, [currApproveMain], paths);
        }

        /// <summary>
        /// 获取通知人员以及通知信息
        /// </summary>
        /// <param name="approveRecordInfo">审批主记录</param>
        /// <param name="approveMainInfos">所有审批记录</param>
        /// <returns>通知人，跳转页面</returns>
        private async ValueTask<MessageView[]> GetNotifyEmployeeAndContentAsync(ApproveRecordInfo approveRecordInfo, List<ApproveMainInfo> approveMainInfos, (string path, int clientType)[] paths = null)
        {
            //撤销审批 - 通知已审批过的人员
            if (approveRecordInfo.StatusCode == ApprovalStatus.Revoke.ToString("d"))
            {
                return await GetRevokeApprovalNotification(approveRecordInfo, approveMainInfos);
            }
            //审批完成 - 通知发起人
            if (approveRecordInfo.CompleteDateTime.HasValue)
            {
                return GetEndApprovalNotification(approveRecordInfo, approveMainInfos, paths);
            }
            //通知下一个审批人
            return await GetNextApprovalNotificationAsync(approveRecordInfo);
        }
        /// <summary>
        /// 获取审批完成后通知申请人的 通知信息
        /// </summary>
        /// <param name="approveRecordInfo">审批主记录</param>
        /// <param name="approveMainInfos">所有审批记录</param>
        /// <param name="paths">跳转路径</param>
        /// <returns></returns>
        private MessageView[] GetEndApprovalNotification(ApproveRecordInfo approveRecordInfo, List<ApproveMainInfo> approveMainInfos, (string path, int clientType)[] paths = null)
        {
            Dictionary<string, string> employeeIDDict = [];

            var result = GetApprovalStatusDescription(approveRecordInfo, approveMainInfos.FirstOrDefault());
            if (!string.IsNullOrEmpty(result))
            {
                var content = $"您的申请已有结果；审批内容：{approveRecordInfo.Content}；审批结果：{result}。详情请登录护理管理系统查看。";
                employeeIDDict.Add(approveRecordInfo.AddEmployeeID, content);
            }
            var messageViews = employeeIDDict.Select(employee =>
            paths is not null && paths.Length > 0
            ? paths.Select(path => CreateMessageView(employee.Key, employee.Value, path.path, path.clientType))
            : [CreateMessageView(employee.Key, employee.Value)]
            ).SelectMany(m => m).ToArray();
            return messageViews;
        }
        /// <summary>
        /// 获取下一个审批节点的审批人员-通知信息
        /// </summary>
        /// <param name="approveRecordInfo">审批主记录</param>
        /// <returns></returns>
        private async Task<MessageView[]> GetNextApprovalNotificationAsync(ApproveRecordInfo approveRecordInfo)
        {
            Dictionary<string, string> employeeIDDict = [];
            // 当前节点审批结束 通知下一节点人员审批
            string content = $"您有新的审批，审批内容：{approveRecordInfo.Content}；请您及时登录护理管理系统进行审批。";

            var preEmployeeIDs = await _approveDetailRepository.GetPreApproveEmployeeIDAsNoTrackAsync(approveRecordInfo.ApproveMainID);
            preEmployeeIDs.ForEach(idStr =>
            {
                var employeeIDs = idStr.Split("||", StringSplitOptions.RemoveEmptyEntries).ToList();
                foreach (var employeeID in employeeIDs)
                {
                    employeeIDDict.TryAdd(employeeID, content);
                }
            });
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ProveCategoryToRoute"
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var setting = settingList.FirstOrDefault(m => string.IsNullOrWhiteSpace(m.SettingTypeValue));
            // 取第一个
            if (setting == null || !int.TryParse(setting.SettingValue, out var routerListID))
            {
                _logger.Error($"发送审批继续通知失败，找不到SettingType=ProveCategoryToRoute配置");
                return [];
            }
            var routers = await _routerListRepository.GetInfosByRouterListID(routerListID);
            // 获取跳转 path，PC端、移动端
            var routerPaths = routers.Select(r => (r.Path, r.ClientType)).ToArray();
            // 始终通知下一个审批人员到审批页面
            var messageViews = employeeIDDict.Select(employee =>
            routerPaths.Select(path => CreateMessageView(employee.Key, employee.Value, path.Path, path.ClientType))
            ).SelectMany(m => m).ToArray();
            return messageViews;
        }

        /// <summary>
        /// 获取审批撤销通知信息 - 前提：状态为撤销 | ApprovalStatus.Revoke
        /// </summary>
        /// <param name="approveRecordInfo">审批主记录</param>
        /// <param name="approveMainInfos">所有审批记录</param>
        /// <returns></returns>
        private async Task<MessageView[]> GetRevokeApprovalNotification(ApproveRecordInfo approveRecordInfo, List<ApproveMainInfo> approveMainInfos)
        {
            Dictionary<string, string> employeeIDDict = [];
            string content = $"撤销审批，{approveRecordInfo.Content}，撤销原因:{approveRecordInfo.Reason}。";
            var mainIDs = approveMainInfos.Where(m => m.StatusCode != null).Select(m => m.ApproveMainID).ToList();

            if (mainIDs.Count <= 0)
            {
                return null;
            }
            //获取已经完成审批的人员
            var approveEmployeeIDs = await _approveDetailRepository.GetApproveEmployeeIDAsync(mainIDs);
            approveEmployeeIDs.ForEach(employeeID => employeeIDDict.TryAdd(employeeID, content));

            var messageViews = employeeIDDict.Select(employee =>
            CreateMessageView(employee.Key, employee.Value)).ToArray();
            return messageViews;
        }

        /// <summary>
        /// 获取通知发起审批人员的信息
        /// </summary>
        /// <param name="approveRecordInfo"></param>
        /// <param name="currApproveMain"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private string GetApprovalStatusDescription(ApproveRecordInfo approveRecordInfo, ApproveMainInfo currApproveMain)
        {
            if (currApproveMain == null)
            {
                return null;
            }
            // 根据状态码获取对应的状态描述谓词
            var predicate = int.Parse(approveRecordInfo.StatusCode) switch
            {
                <= 1 => currApproveMain.StatusCode switch
                {
                    "1" => "同意",
                    "2" => "拒绝",
                    "0" or "" => "",
                    _ => "状态异常",
                },
                > 1 and <= 4 => GetEnumDescription<ApprovalStatus>(approveRecordInfo.StatusCode),
                > 4 => throw new Exception($"异常：审批状态码错误{approveRecordInfo.StatusCode}"),

            };
            return predicate;
        }

        /// <summary>
        /// 获取枚举字段的Description
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="value">枚举值</param>
        /// <returns></returns>
        public string GetEnumDescription<T>(string value)
        {
            if (!int.TryParse(value, out int intValue))
            {
                return value;
            }
            var enumName = Enum.GetName(typeof(T), intValue);
            var fieldInfo = typeof(T).GetField(enumName);
            var attributes = (DescriptionAttribute[])fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }
        /// <summary>
        /// 进行消息通知提醒
        /// </summary>
        /// <param name="approveRecordInfo">审批主记录</param>
        /// <param name="currApproveMains">当前审批记录</param>
        /// <param name="paths">工厂传递来的需跳转到的页面</param>
        /// <returns></returns>
        private async Task NotifyApprovalResultAsync(ApproveRecordInfo approveRecordInfo, List<ApproveMainInfo> currApproveMains, (string path, int clientType)[] paths = null)
        {
            var messageViews = await GetNotifyEmployeeAndContentAsync(approveRecordInfo, currApproveMains, paths);
            if (messageViews is null || messageViews.Length <= 0)
            {
                _logger.Warn("无需通知人员，不进行消息发送");
                return;
            }
            foreach (var messageView in messageViews)
            {
                await _messageService.SendMessage(messageView);
            }
        }

        /// <summary>
        /// 创建发送消息任务
        /// </summary>
        /// <param name="employeeID">用户工号</param>
        /// <param name="routingKey">路由键</param>
        /// <param name="message">消息内容</param>
        /// <param name="path">跳转页面</param>
        /// <param name="clientType">客户端类别</param>
        private static MessageView CreateMessageView(string routingKey, string message, string path = null, int clientType = 1)
        {
            var messageView = new MessageView
            {
                MessageTools = [MessageTool.MQ, MessageTool.SMS, MessageTool.Wechat],
                EmployeeID = routingKey,
                ClientType = clientType,
                MessageCondition = new MessageConditionView
                {
                    // 移动端与PC端使用不同的交换机
                    MQExchangeName = "MQNotification",
                    MQRoutingKey = routingKey,
                    Message = message,
                    Url = path,
                    ClientType = clientType,
                }
            };
            return messageView;
        }

        #region 三种审签方式处理

        /// <summary>
        /// 更新或签
        /// </summary>
        /// <param name="currDetail"></param>
        /// <param name="currApproveMain"></param>
        /// <param name="approveRecordInfo"></param>
        /// <returns>0:正常审批节点更新 1.审批结束-审批通过，2.审批结束-审批拒绝</returns>
        private static void UpdateParallelApprovalAsync(ApproveDetailInfo currDetail, ApproveMainInfo currApproveMain, ApproveRecordInfo approveRecordInfo)
        {
            currApproveMain.StatusCode = currDetail.StatusCode;
            currApproveMain.ApproveDateTime = currDetail.ApproveDateTime;
            currApproveMain.Modify(currDetail.ModifyEmployeeID);

            // 拒绝，结束
            if (currDetail.StatusCode == ApprovalStatus.Rejected.ToString("d"))
            {
                approveRecordInfo.CompleteDateTime = currDetail.ApproveDateTime;
                approveRecordInfo.StatusCode = ApprovalStatus.Rejected.ToString("d");
                approveRecordInfo.Modify(currDetail.ModifyEmployeeID);
                return;
            }
            //寻找下一个节点
            if (string.IsNullOrEmpty(currApproveMain.NextApproveMainID))
            {
                approveRecordInfo.CompleteDateTime = currDetail.ApproveDateTime;
                approveRecordInfo.StatusCode = ApprovalStatus.Completed.ToString("d");
                approveRecordInfo.Modify(currDetail.ModifyEmployeeID);
                return;
            }
            approveRecordInfo.ApproveMainID = currApproveMain.NextApproveMainID;
            approveRecordInfo.StatusCode = ApprovalStatus.InProgress.ToString("d");
            return;
        }

        /// <summary>
        /// 更新会签
        /// </summary>
        /// <param name="currDetail"></param>
        /// <param name="currApproveMain"></param>
        /// <param name="approveRecord"></param> 
        /// <returns>0:正常审批节点更新 1.审批结束-审批通过，2.审批结束-审批拒绝</returns>
        private async Task UpdateCollectiveApprovalAsync(ApproveDetailInfo currDetail, ApproveMainInfo currApproveMain, ApproveRecordInfo approveRecord)
        {
            // 会签 当前节点拒绝 结束
            if (currDetail.StatusCode == ApprovalStatus.Rejected.ToString("d"))
            {
                currApproveMain.StatusCode = currDetail.StatusCode;
                currApproveMain.ApproveDateTime = currDetail.ApproveDateTime;
                currApproveMain.Modify(currDetail.ModifyEmployeeID);
                approveRecord.CompleteDateTime = currDetail.ApproveDateTime;
                approveRecord.StatusCode = currDetail.StatusCode;
                approveRecord.Modify(currDetail.ModifyEmployeeID);
                return;
            }
            var existFlag = await _approveDetailRepository.CheckUnApproveDetailsExistOrNotByMainIDAsync(currApproveMain.ApproveMainID);
            if (existFlag)
            {
                return;
            }
            //所有会签都已完成审批 
            currApproveMain.StatusCode = currDetail.StatusCode;
            currApproveMain.ApproveDateTime = currDetail.ApproveDateTime;
            currApproveMain.Modify(currDetail.ModifyEmployeeID);

            if (string.IsNullOrEmpty(currApproveMain.NextApproveMainID))
            {
                approveRecord.CompleteDateTime = currDetail.ApproveDateTime;
                approveRecord.StatusCode = ApprovalStatus.Completed.ToString("d");
                approveRecord.Modify(currDetail.ModifyEmployeeID);
                return;
            }
            // 更新记录表中进行到的下一个节点
            approveRecord.ApproveMainID = currApproveMain.NextApproveMainID;
            // 审批中
            approveRecord.StatusCode = ApprovalStatus.InProgress.ToString("d");
            return;
        }

        /// <summary>
        /// 更新顺序签
        /// </summary>
        /// <param name="currDetail"></param>
        /// <param name="currApproveMain"></param>
        /// <param name="approveRecord"></param>
        /// <returns>0:正常审批节点更新 1.审批结束-审批通过，2.审批结束-审批拒绝</returns>
        private static void UpdateSequentialApproval(ApproveDetailInfo currDetail, ApproveMainInfo currApproveMain, ApproveRecordInfo approveRecord)
        {
            currApproveMain.StatusCode = currDetail.StatusCode;
            currApproveMain.ApproveDateTime = currDetail.ApproveDateTime;
            currApproveMain.Modify(currDetail.ModifyEmployeeID);
            //顺序签拒绝 -结束
            if (currDetail.StatusCode == ApprovalStatus.Rejected.ToString("d"))
            {
                approveRecord.CompleteDateTime = currDetail.ApproveDateTime;
                approveRecord.StatusCode = currDetail.StatusCode;
                approveRecord.Modify(currDetail.ModifyEmployeeID);
                return;
            }
            //寻找下一个节点 (当前节点审批通过，且是最后的节点)
            if (string.IsNullOrEmpty(currApproveMain.NextApproveMainID))
            {
                approveRecord.CompleteDateTime = currDetail.ApproveDateTime;
                approveRecord.StatusCode = ApprovalStatus.Completed.ToString("d");
                approveRecord.Modify(currDetail.ModifyEmployeeID);
                return;
            }
            // 更新记录表中进行到的下一个节点
            approveRecord.ApproveMainID = currApproveMain.NextApproveMainID;
            // 审批中
            approveRecord.StatusCode = ApprovalStatus.InProgress.ToString("d");
            return;
        }
        /// <summary>
        /// 根据所传对象，替换申请模板（模板中由`{}`包裹的文字是参数 view中的属性名），返回申请内容
        /// </summary>
        /// <param name="contentTemplate">流程存储的申请模板</param>
        /// <param name="view">业务View，存储了相关数据用以替换</param>
        /// <returns></returns>
        public async Task<string> GetApproveContentByBizView(string contentTemplate, dynamic view)
        {
            if (string.IsNullOrEmpty(contentTemplate))
            {
                return "";
            }
            var proveParam = new SettingDictionaryParams()
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveProcess",
                SettingValue = view.ProveCategory
            };
            var proveCategorySetting = await _settingDictionaryRepository.GetSettingDictionary(proveParam);
            // 查找 contentTemplate 中以`{`开始`}`结束的字符串
            string pattern = @"\{([^\}]+)\}";
            string result = Regex.Replace(contentTemplate, pattern, match =>
            {
                string matchedPropName = match.Groups[1].Value;
                // 若为分类码，则转换为对应的文本
                if (matchedPropName == "ProveCategory")
                {
                    return proveCategorySetting.FirstOrDefault()?.Description;
                }
                var value = ReflexUtil.GetPropertyValue(view, matchedPropName);
                return value?.ToString() ?? "";
            });
            return result;
        }
        #endregion
        /// <summary>
        /// 停止审批
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <param name="employeeID">人员工号</param>
        /// <param name="saveChange">是否savechange</param>
        /// <returns></returns>
        public async Task<bool> StopApprovalAsync(string sourceID, string employeeID, bool saveChange = false)
        {
            var recordInfo = await _approveRecordRepository.GetApproveRecordBySourceIDAsync(sourceID);
            if (recordInfo == null || recordInfo?.StatusCode?.CompareTo(ApprovalStatus.InProgress.ToString("d")) > 0)
            {
                return false;
            }
            var mainInfos = await _approveMainRepository.GetApproveMainsByRecordIDAsync(recordInfo.ApproveRecordID);
            if (mainInfos.Count <= 0)
            {
                return false;
            }
            var detailInfos = await _approveDetailRepository.GetApproveDetailsByMainIDAsync(mainInfos.Select(m => m.ApproveMainID).ToArray());
            //删除审批记录
            recordInfo.Delete(employeeID);
            mainInfos.ForEach(m => m.Delete(employeeID));
            detailInfos.ForEach(m => m.Delete(employeeID));
            if (saveChange)
            {
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            return true;
        }

        /// <summary>
        /// 撤销审批
        /// </summary>
        /// <param name="view" cref="ApprovalRevokeParamsView">审批撤销参数</param>
        /// <param name="employeeID">撤销审批的执行人工号</param>
        /// <returns></returns>
        public async Task<bool> RevokeApprovalAsync(ApprovalRevokeParamsView view, string employeeID)
        {
            var approveRecordInfo = string.IsNullOrEmpty(view.ApproveRecordID) ?
                await _approveRecordRepository.GetApproveRecordBySourceIDAsync(view.SourceID)
                : await _approveRecordRepository.GetApproveRecordByRecordIDAsync(view.ApproveRecordID);
            if (approveRecordInfo == null)
            {
                _logger.Error($"找不到对应的审批记录！！参数：paramView={ListToJson.ToJson(view)}");
                return false;
            }
            var revokeStatus = ApprovalStatus.Revoke.ToString("d");
            // 审批未完成的时候，审批撤销，设置完成时间
            approveRecordInfo.CompleteDateTime ??= DateTime.Now;
            approveRecordInfo.StatusCode = revokeStatus;
            approveRecordInfo.Reason = view.Reason;
            approveRecordInfo.Modify(employeeID);
            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            if (!success)
            {
                return false;
            }
            var approveMainInfos = await _approveMainRepository.GetApprovedMainsByRecordIDAsync(approveRecordInfo.ApproveRecordID);

            return await ProcessAfterRevokeApproval(approveRecordInfo, approveMainInfos);
        }
        /// <summary>
        /// 撤销审批后的逻辑处理
        /// </summary>
        /// <param name="approveRecord"></param>
        /// <param name="approveMains"></param>
        /// <returns></returns>
        private async Task<bool> ProcessAfterRevokeApproval(ApproveRecordInfo approveRecord, List<ApproveMainInfo> approveMains)
        {
            var notifyEmployeeID = approveRecord.AddEmployeeID;
            // 审核结束后的处理
            var factory = await _processingAfterApprovalFactory.SwitchProcessingAsync(approveRecord.ApproveProcessID);
            ProcessAfterApprovalView view = new()
            {
                ApprovalResult = approveRecord.StatusCode,
                InformContent = "撤销审批",
                InitiatorEmployeeID = notifyEmployeeID,
                SourceID = approveRecord.SourceID,
            };
            if (factory == null)
            {
                return true;
            }
            var paths = await factory.ProcessAfterApprovalAsync(view);
            // 通知审批状态
            await NotifyApprovalResultAsync(approveRecord, approveMains, paths);
            return true;
        }
        /// <summary>
        /// 根据工号获取对方没有审批的记录
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<object> GetUnApprovalRecordAsync(string employeeID)
        {
            var approveRecordInfos = await _approveRecordRepository.GetUnCompletedApproveRecordsAsNoTrackAsync(null, null, null);
            if (approveRecordInfos.Count <= 0)
            {
                return null;
            }
            // 筛选出 （未审批的 && 当前账号需要审批的） 节点;
            var mainIDArrays = approveRecordInfos.Select(m => m.ApproveMainID).ToArray();
            var approveDetailInfos = await _approveDetailRepository.GetApproveDetailsByMainIDAsync(mainIDArrays);
            var unApproveDetails = approveDetailInfos.Where(m => string.IsNullOrEmpty(m.StatusCode) && m.PreApproveEmployeeID.Contains(employeeID)).ToList();
            if (unApproveDetails.Count <= 0)
            {
                return null;
            }
            // 过滤出当前节点需要登录人员审批的记录
            approveRecordInfos = approveRecordInfos.Where(m => unApproveDetails.Exists(n => n.ApproveMainID == m.ApproveMainID)).ToList();
            var tuples = await _approveProcessRepository.GetAllProcessNameAsync();
            List<ApproveUnCompletedView> resultList = new List<ApproveUnCompletedView>();
            foreach (var item in approveRecordInfos)
            {
                var processName = tuples.Find(m => m.Item2 == item.ApproveProcessID)?.Item1;
                resultList.Add(new ApproveUnCompletedView
                {
                    ProcessName = processName,
                    AddDateTime = item.AddDateTime.ToString("yyyy-MM-dd"),
                    Content = item.Content,
                    ApproveRecordID = item.ApproveRecordID,
                    ApproveProcessID = item.ApproveProcessID
                });
            }
            return resultList.OrderBy(m => m.ApproveProcessID).ThenBy(m => m.AddDateTime).ToList();
        }
        /// <summary>
        /// 获取审批类型及相关审批记录数量
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="isCompleteFlag">是否已审批</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<MessageCountView<string>>> GetApproveCategoryAndCount(string employeeID, int departmentID, bool isCompleteFlag, DateTime? startDate, DateTime? endDate)
        {
            var proveCategories = await _settingDictionaryRepository.GetSettingDictionary(new SettingDictionaryParams
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveProcess",
                FilterSettingTypeValue = "ProveCategoryGroup"
            });
            var returnView = proveCategories.Select(m => new MessageCountView<string>
            {
                Label = m.Description,
                Value = m.SettingValue
            }).ToList();
            var approveRecordInfos = isCompleteFlag
                    ? (await GetCompletedApproveRecords(employeeID, departmentID, startDate, endDate))
                    : (await GetUnCompletedApproveRecords(employeeID, startDate, endDate));
            var enableApproveProcess = await _approveProcessRepository.GetAllCategoryAndProcessID();
            // 获取它们的适用科室
            var enableApproveProcessDepartment = await _departmentToApproveProcessRepository.GetDepartmentIDsByProcessIDs(true, enableApproveProcess.Keys.ToArray());
            foreach (var proveCategory in returnView)
            {
                if (string.IsNullOrEmpty(proveCategory.Value))
                {
                    continue;
                }
                var processIDs = enableApproveProcess.Where(m => m.Value == proveCategory.Value).Select(m => m.Key).ToList();
                var enableDepartment = enableApproveProcessDepartment.Where(m => processIDs.Contains(m.Key)).ToDictionary();
                // 先精确匹配
                var processID = enableDepartment.FirstOrDefault(m => m.Value.Contains(departmentID)).Key;
                // 若无匹配项，再匹配通用的
                processID ??= enableDepartment.FirstOrDefault(m => m.Value.Contains(999999)).Key;
                if (string.IsNullOrEmpty(processID))
                {
                    continue;
                }
                proveCategory.Count = approveRecordInfos.Where(m => m.ApproveProcessID == processID).Count();
            }
            return returnView;
        }
        /// <summary>
        /// 获取未完成审批的记录（当前用户参与的）
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        private async Task<List<ApproveRecordInfo>> GetUnCompletedApproveRecords(string employeeID, DateTime? startDate, DateTime? endDate)
        {
            var approveRecordInfos = await _approveRecordRepository.GetUnCompletedApproveRecordsAsNoTrackAsync("", startDate, endDate);
            if (approveRecordInfos.Count <= 0)
            {
                return approveRecordInfos;
            }
            // 筛选出 （未审批的 && 当前账号需要审批的节点;
            var mainIDs = approveRecordInfos.Select(m => m.ApproveMainID).ToList();
            var unApproveMainIDs = await _approveDetailRepository.GetNotApproveMainIDsAsync(mainIDs, employeeID);
            // 过滤出当前节点需要登录人员审批的记录
            approveRecordInfos = approveRecordInfos.Where(m => unApproveMainIDs.Exists(n => n == m.ApproveMainID)).ToList();
            return approveRecordInfos;
        }

        /// <summary>
        /// 获取已经完成的历史审批记录（当前用户参与的）
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        private async Task<List<ApproveRecordInfo>> GetCompletedApproveRecords(string employeeID, int departmentID, DateTime? startDate, DateTime? endDate)
        {
            List<string> approveEmployeeIDs = [employeeID];
            // 获取参与审批的相关岗位 的管理人员ID集合
            var jobCodes = await _approveProcessNodeDetailRepository.GetJobCodesByProcessIDAndNodeTypeAsync("");
            if (jobCodes.Count > 0)
            {
                var employeeIDsOfTheSameJobCode = await _employeeToJobRepository.GetEmployeeIDsByDepartmentIDAndJobCodesAsync(jobCodes, departmentID);
                if (employeeIDsOfTheSameJobCode.Contains(employeeID))
                {
                    approveEmployeeIDs.AddRange(employeeIDsOfTheSameJobCode);
                }
            }
            return await _approveRecordRepository.GetCompletedApproveRecordsAsync(approveEmployeeIDs.Distinct().ToList(), "", startDate, endDate);
        }
        /// <summary>
        ///  处理人员借调排班数据
        /// </summary>
        /// <param name="employeeSecondmentRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task DeelEmployeeSecondmentScheduleData(string employeeSecondmentRecordID,string employeeID)
        {
            var recordInfo = await _employeeSecondmentRecordRepository.GetDataByID(employeeSecondmentRecordID);
            if (recordInfo == null)
            {
                return;
            }
            // 获取原部门在借调出去时间段的排班数据，删除
            var shiftScheduleList = await _shiftSchedulingDetailRepository.GetDetailByEmployeeID(recordInfo.EmployeeID, recordInfo.DepartmentID, recordInfo.StartNoon, recordInfo.ActualEndNoon, recordInfo.StartDate, recordInfo.ActualEndDate);
            if (shiftScheduleList.Count > 0)
            {
                foreach (var item in shiftScheduleList)
                {
                    item.Delete(employeeID);
                }
            };
            // 获取借调部门在借调结束后的排班数据，删除
            var secondmentDepartmentShiftScheduleList = await _shiftSchedulingDetailRepository.GetDetailByEmployeeID(recordInfo.EmployeeID, recordInfo.SecondmentDepartmentID, recordInfo.StartNoon, recordInfo.ActualEndNoon, null, recordInfo.ActualEndDate);
            if (secondmentDepartmentShiftScheduleList.Count > 0)
            {
                foreach (var item in secondmentDepartmentShiftScheduleList)
                {
                    item.Delete(employeeID);
                }
            };
            //  获取原部门在借调出去时间段的排班明细标识数据，删除
            var shiftScheduleRecordIDList = shiftScheduleList.Select(m => m.ShiftSchedulingRecordID).ToList();
            var shiftScheduleDetailMarkList = await _shiftSchedulingDetailMarkRepository.GetMarkByEmployeeID(shiftScheduleRecordIDList, recordInfo.EmployeeID, recordInfo.StartDate, recordInfo.EndDate);
            if (shiftScheduleDetailMarkList.Count > 0)
            {
                foreach (var item in shiftScheduleDetailMarkList)
                {
                    item.Delete(employeeID);
                }
            };
            // 获取借调部门在借调结束后的排班明细标识数据，删除
            var secondmentDepartmentShiftScheduleRecordIDLIst = secondmentDepartmentShiftScheduleList.Select(m => m.ShiftSchedulingRecordID).ToList();
            var secondmentDepartmentShiftScheduleDetailMarkList = await _shiftSchedulingDetailMarkRepository.GetMarkByEmployeeID(secondmentDepartmentShiftScheduleRecordIDLIst, recordInfo.EmployeeID, recordInfo.StartDate, recordInfo.EndDate);
            if (secondmentDepartmentShiftScheduleDetailMarkList.Count > 0)
            {
                foreach (var item in secondmentDepartmentShiftScheduleDetailMarkList)
                {
                    item.Delete(employeeID);
                }
            };
        }
    }

}