﻿using Microsoft.Extensions.Caching.Memory;
using System.Text;

namespace NursingManagement.Common
{
    public static class PaperDynamicGeneratorUtils
    {
        private static readonly Lazy<MemoryCache> _memoryCache
            = new Lazy<MemoryCache>(() => new MemoryCache(new MemoryCacheOptions()));
        private static readonly Random CommonRandom = new Random(); 
        /// <summary>
        /// 从缓存中获取字母前缀
        /// </summary>
        /// <param name="index"></param>
        /// <param name="upperCase"></param>
        /// <returns></returns>
        public static string GetMemoryCachedPrefix(int index, bool upperCase=true)
        {
            var cacheKey = $"prefix_{index}";
            return _memoryCache.Value.GetOrCreate(cacheKey, entry =>
            {
                entry.SlidingExpiration = TimeSpan.FromMinutes(30);
                return index.ToAlphabetPrefix(upperCase);
            });
        }

        #region 生成字母前缀方法
        /// <summary>
        /// 将数字索引转换为Excel风格的列字母前缀
        /// </summary>
        /// <param name="index">从0开始的索引</param>
        /// <param name="uppercase">是否大写，默认true</param>
        /// <returns>A, B, ..., Z, AA, AB等格式</returns>
        public static string ToAlphabetPrefix(this int index, bool upperCase = true)
        {
            if (index < 0)
            {
                throw new ArgumentOutOfRangeException(nameof(index), "索引值不能小于0");
            }
            var sb = new StringBuilder();
            int baseChar = upperCase ? 65 : 97; // A或a的ASCII码
            do
            {
                sb.Insert(0, (char)(baseChar + index % 26));
                index = (index / 26) - 1;
            } while (index >= 0);

            return sb.ToString();
        }

        #endregion

        #region 题目、选项试卷随机排序方法
        /// <summary>
        /// 打乱列表顺序
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="shuffingList">需要打乱的数组列表</param>
        /// <param name="seed">随机值的种子，默认使用同一个随机值种子</param>
        /// <returns></returns>
        public static List<T> CreateShuffledList<T>( List<T> shuffingList ,int? seed = null)
        {
            var random = CommonRandom;
            if (seed.HasValue)
            {
                random = new Random(seed.Value);
            }
            ShuffleList(shuffingList, CommonRandom);
            return shuffingList;
        }

        /// <summary>
        /// 打乱题目顺序
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="random"></param>
        private static void ShuffleList<T>(IList<T> list, Random random)
        {
            // Fisher-Yates洗牌算法
            for (int i = list.Count - 1; i > 0; i--)
            {
                int j = random.Next(i + 1);
                (list[j], list[i]) = (list[i], list[j]);
            }
        }
        #endregion

        #region 定题条件判断
        /// <summary>
        /// 判断题目是否有序（即顺序是否固定）
        /// </summary>
        /// <param name="questionMode">不定题条件值</param>
        /// <returns>
        /// <para><b>true</b>：有序（顺序固定）</para>
        /// <para><b>false</b>：无序（顺序不固定）</para>
        /// </returns>
        public static bool CheckQuestionHasOrder(string questionMode)
        {
            if (!int.TryParse(questionMode,out var modelValue))
            {
                return false;
            }
            var model = (PaperQuestionMode)modelValue;
            if (model == PaperQuestionMode.SameQuestionsDifferentOrder || model == PaperQuestionMode.SameQuestionsDifferentOrderAndOptions)
            {
                return false;
            }

            return true;
        }
        /// <summary>
        /// 判断题目选项是否有序（即选项顺序是否固定）
        /// </summary>
        /// <param name="questionMode">不定题条件值</param>
        /// <returns>
        /// <para><b>true</b>：选项有序（顺序固定）</para>
        /// <para><b>false</b>：选项无序（顺序不固定）</para>
        /// </returns>
        public static bool CheckQuestionOptionsHasOrder(string questionMode)
        {
            if (!int.TryParse(questionMode, out var modelValue))
            {
                return false;
            }
            var model = (PaperQuestionMode)modelValue;
            if (model == PaperQuestionMode.SameQuestionsDifferentOrderAndOptions || model == PaperQuestionMode.SameQuestionsDifferentOptions )
            {
                return false;
            }

            return true;
        }
        /// <summary>
        /// 判断题目相同
        /// </summary>
        /// <param name="questionMode">不定题条件值</param>
        /// <returns> 判断是否需要题目相同</returns>
        public static bool CheckTheSameQuestion(string questionMode)
        {
            if (!int.TryParse(questionMode, out var modelValue))
            {
                return false;
            }
            var model = (PaperQuestionMode)modelValue;
            return model switch
            {
                PaperQuestionMode.SameQuestionsDifferentOrder => true,
                PaperQuestionMode.SameQuestionsDifferentOptions => true,
                PaperQuestionMode.SameQuestionsDifferentOrderAndOptions => true,
                PaperQuestionMode.SameQuestionsSameOrderSameOptions => true,
                _ => false,
            };
        }
        /// <summary>
        /// 是否定题
        /// </summary>
        /// <param name="questionMode">不定题条件值</param>
        /// <returns></returns>
        public static bool IsFixedQuestion(string questionMode)
        {
            if (!int.TryParse(questionMode, out var modelValue))
            {
                return false;
            }
            var model = (PaperQuestionMode)modelValue;
            return model == PaperQuestionMode.SameQuestionsSameOrderSameOptions;
        }
        #endregion
    }
    /// <summary>
    /// 试卷不定题模式枚举（1-4 对应前端选项）
    /// </summary>
    public enum PaperQuestionMode
    {
        /// <summary>
        /// 1: 题目完全相同，题目顺序相同，答案顺序相同（完全一致）
        /// </summary>
        SameQuestionsSameOrderSameOptions = 1,

        /// <summary>
        /// 2: 题目不完全相同
        /// </summary>
        DifferentQuestions = 2,

        /// <summary>
        /// 3: 题目完全相同，题目顺序相同，答案顺序不同
        /// </summary>
        SameQuestionsDifferentOptions = 3,

        /// <summary>
        /// 4: 题目完全相同，题目顺序不同，答案顺序相同
        /// </summary>
        SameQuestionsDifferentOrder = 4,

        /// <summary>
        /// 5: 题目完全相同，题目顺序不同，答案顺序不同
        /// </summary>
        SameQuestionsDifferentOrderAndOptions = 5
    }
}
