using static NursingManagement.Models.AnnualPlanEnums;

namespace NursingManagement.ViewModels.MonthlyPlan
{
  public class MonthlyPlanExportView
  {
    /// <summary>
    /// 月度计划主表ID
    /// </summary>
    public string MonthlyPlanMainID { get; set; }

    /// <summary>
    /// 分类分组
    /// </summary>
    public PlanType[] PlanTypes { get; set; }

    /// <summary>
    /// 分类分组
    /// </summary>
    public class PlanType
    {
      public int TypeID { get; set; }

      /// <summary>
      /// 分类名称
      /// </summary>
      public string TypeName { get; set; }

      /// <summary>
      /// 工作集合
      /// </summary>
      public PlanWork[] PlanWorks { get; set; }

      /// <summary>
      /// 工作
      /// </summary>
      public class PlanWork
      {
        /// <summary>
        /// 工作类型
        /// </summary>
        public WorkType WorkType { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string WorkContent { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string Requirement { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public string[] PrincipalIDs { get; set; }
      }
    }
  }
}