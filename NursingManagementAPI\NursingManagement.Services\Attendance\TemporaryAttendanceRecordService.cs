﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class TemporaryAttendanceRecordService : ITemporaryAttendanceRecordService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ITemporaryAttendanceRecordRepository _temporaryAttendanceRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IUnitOfWork _unitOfWork;
        public TemporaryAttendanceRecordService(
            ITemporaryAttendanceRecordRepository temporaryAttendanceRecordRepository
           , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IDictionaryService dictionaryService
            , ISettingDictionaryRepository settingDictionaryRepository
           , IUnitOfWork unitOfWork)
        {
            _temporaryAttendanceRecordRepository = temporaryAttendanceRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _dictionaryService = dictionaryService;
            _settingDictionaryRepository = settingDictionaryRepository;
            _unitOfWork = unitOfWork;
        }
        /// <summary>
        /// 保存临时出勤记录
        /// </summary>
        /// <param name="view"></param>
        /// <param name="session"></param>
        /// <returns></returns>
        public async Task<bool> SaveTemporaryAttendanceData(TemporaryAttendanceRecordView view, Session session)
        {
            if (view == null)
            {
                return false;
            }
            view.DepartmentID = view.DepartmentID == 0 ? view.DepartmentID : session.DepartmentID;
            view.EmployeeID ??= session.EmployeeID;
            if (view.TemporaryAttendanceRecordID.HasValue)
            {
                var record = await _temporaryAttendanceRecordRepository.GetDataByID(view.TemporaryAttendanceRecordID.Value);
                if (record == null)
                {
                    return false;
                }
                record.Modify(view.EmployeeID);
                record.AttendanceEmployeeID = view.AttendanceEmployeeID;
                record.AttendanceHours = view.AttendanceHours;
                record.AttendanceDate = view.AttendanceDate;
                record.DepartmentID = view.DepartmentID;
                record.Remark = view.Remark;
                record.AdministrationIconID = view.TemporaryAttendanceID;
            }
            else
            {
                var info = new TemporaryAttendanceRecordInfo()
                {
                    AttendanceEmployeeID = view.AttendanceEmployeeID,
                    AttendanceDate = view.AttendanceDate,
                    AttendanceHours = view.AttendanceHours,
                    DepartmentID = view.DepartmentID,
                    Remark = view.Remark,
                    AdministrationIconID = view.TemporaryAttendanceID
                };
                info.Add(view.EmployeeID);
                info.Modify(view.EmployeeID);
                await _unitOfWork.GetRepository<TemporaryAttendanceRecordInfo>().InsertAsync(info);
            }
            try
            {
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
            }
            return false;
        }
        /// <summary>
        /// 获取临时出勤记录数据
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="showAllFlag">是否查看全科开关</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        public async Task<List<TemporaryAttendanceRecordView>> GetTemporaryAttendanceList(string employeeID, bool showAllFlag, int departmentID, DateTime month)
        {
            var recordView = new List<TemporaryAttendanceRecordView>();
            var startDate = month;
            var endDate = month.AddMonths(1).AddDays(-1);
            var attendanceList = await _temporaryAttendanceRecordRepository.GetListByDepartmentID(departmentID, startDate, endDate);
            if (!showAllFlag)
            {
                attendanceList = attendanceList.Where(m => m.AttendanceEmployeeID == employeeID).ToList();
            }
            if (attendanceList.Count <= 0)
            {
                return recordView;
            }
            var emporaryAttendancSetting = await GetTemporaryAttendancePostSetting(departmentID);
            var employeePersonalList = await _employeePersonalDataRepository.GetDataByEmployeeIDs(attendanceList.Select(m => m.AttendanceEmployeeID).ToList());
            foreach (var attendance in attendanceList)
            {
                var view = new TemporaryAttendanceRecordView()
                {
                    TemporaryAttendanceRecordID = attendance.TemporaryAttendanceRecordID,
                    AttendanceEmployee = employeePersonalList[attendance.AttendanceEmployeeID] ?? "",
                    AttendanceEmployeeID = attendance.AttendanceEmployeeID,
                    AttendanceHours = attendance.AttendanceHours,
                    AttendanceDate = attendance.AttendanceDate,
                    DepartmentID = attendance.DepartmentID,
                    Remark = attendance.Remark,
                    TemporaryAttendanceID = attendance.AdministrationIconID,
                    TemporaryAttendanceName = emporaryAttendancSetting.Find(m => m.Key == attendance.AdministrationIconID)?.Value
                };
                recordView.Add(view);
            }
            return recordView;
        }
        /// <summary>
        ///  删除临时出勤记录
        /// </summary>
        /// <param name="recordID">临时出勤记录ID(表主键ID)</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<bool> DeleteTemporaryAttendanceData(int recordID, string employeeID)
        {
            var record = await _temporaryAttendanceRecordRepository.GetDataByID(recordID);
            if (record == null)
            {
                return false;
            }
            record.Delete(employeeID);
            try
            {
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
            }
            return false;
        }
        /// <summary>
        /// 获取临时出勤岗位标识数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<DictItem>> GetTemporaryAttendancePostSetting(int departmentID)
        {
            var dictList = new List<DictItem>();
            string[] array = { "999999", departmentID.ToString() };
            var iconSettingList = await _dictionaryService.GetIconsByModuleType("SchedulingMark", array);
            if (iconSettingList == null || iconSettingList.Count <= 0)
            {
                return dictList;
            }
            var excludeSettingParams = new SettingDictionaryParams
            {
                SettingType = "ShiftManagement",
                SettingTypeCode = "ExcludeTemporaryAttendancIconText",
                SettingTypeValue = "IconText"
            };
            var excludeSetting = await _settingDictionaryRepository.GetSettingDictionary(excludeSettingParams);
            if (excludeSetting.Count <= 0)
            {
                foreach (var iconSetting in iconSettingList)
                {
                    var dict = new DictItem()
                    {
                        Key = iconSetting.AdministrationIconID,
                        Value = iconSetting.Text
                    };
                    dictList.Add(dict);
                }
                return dictList;
            }
            iconSettingList = iconSettingList.Where(m => !excludeSetting.Any(s => s.SettingValue == m.AdministrationIconID.ToString())).ToList();
            if (iconSettingList.Count <= 0)
            {
                return dictList;
            }
            foreach (var iconSetting in iconSettingList)
            {
                var dict = new DictItem()
                {
                    Key = iconSetting.AdministrationIconID,
                    Value = iconSetting.Text
                };
                dictList.Add(dict);
            }
            return dictList;
        }
    }
}
