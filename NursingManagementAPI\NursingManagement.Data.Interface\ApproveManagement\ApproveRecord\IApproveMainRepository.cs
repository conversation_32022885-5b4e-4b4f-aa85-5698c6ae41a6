﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IApproveMainRepository
    {
        /// <summary>
        /// 根据审批记录表ID获取审批主表信息
        /// </summary>
        /// <param name="recordID">审批记录表ID</param>
        /// <returns></returns>
        Task<List<ApproveMainInfo>> GetApproveMainsByRecordIDAsync(string recordID);
        /// <summary>
        /// 根据主键获取多个主记录
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        Task<List<ApproveMainInfo>> GetApproveMainsByMainIDsAsync(params string[] mainIDs);
        /// <summary>
        /// 业务审批主记录ID
        /// </summary>
        /// <param name="approveMainID"></param>
        /// <returns></returns>
        Task<ApproveMainInfo> GetApproveMainByIDAsync(string approveMainID);

        /// <summary>
        /// 获取已审批的main记录(approveMainID,statusCode)
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        Task<List<ApproveMainInfo>> GetApprovedMainsByRecordIDAsync(string recordID);
    }
}
