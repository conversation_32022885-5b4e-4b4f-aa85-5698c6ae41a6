﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员个人信息表
    /// </summary>
    [Serializable]
    [Table("EmployeePersonalData")]
    public class EmployeePersonalDataInfo : MutiModifyInfo
    {
        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        [Column(TypeName = "nvarchar(60)")]
        public string EmployeeName { get; set; }

        /// <summary>
        /// 姓名拼音码
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string NamePinyin { get; set; }

        /// <summary>
        /// 性别(GB/T 2261.1-2003)
        /// 0：未知的性别；1：男性；2：女性；9：未说明的性别
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string GenderCode { get; set; }

        /// <summary>
        /// 国籍(GB/T 2659.1-2022)
        /// 156：中国
        /// </summary>
        [Column(TypeName = "varchar(3)")]
        public string NationalityCode { get; set; }

        /// <summary>
        /// 民族(GB/T 3304-1991)
        /// 01：汉族
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string NationCode { get; set; }

        /// <summary>
        /// 身份证号码
        /// </summary>
        [Column(TypeName = "varchar(18)")]
        public string IDCardNo { get; set; }

        /// <summary>
        /// 公历生日
        /// </summary>
        public DateTime? Birthdate { get; set; }

        /// <summary>
        /// 农历生日
        /// </summary>
        public DateTime? LunarBirthdate { get; set; }

        /// <summary>
        /// 家庭地址(身份证)
        /// </summary>
        [Column(TypeName = "nvarchar(510)")]
        public string HomeAddress { get; set; }

        /// <summary>
        /// 实际住址
        /// </summary>
        [Column(TypeName = "nvarchar(510)")]
        public string ActualAddress { get; set; }

        /// <summary>
        /// 籍贯
        /// </summary>
        [Column(TypeName = "nvarchar(510)")]
        public string NativePlace { get; set; }

        /// <summary>
        /// 婚姻状况(GB/T 2261.2-2003)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string MarriageCode { get; set; }

        /// <summary>
        /// 生育状态(00:未育、10：已育)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string DeliverCode { get; set; }

        /// <summary>
        /// 政治面貌(GB/T 4762-1984)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string PolityCode { get; set; }
    }
}