﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班明细
    /// </summary>
    public class ShiftSchedulingDetailToAttendance
    {
        /// <summary>
        /// 排班明细记录ID
        /// </summary>
        public string ShiftSchedulingDetailID { get; set; }
        /// <summary>
        /// 排班主记录ID
        /// </summary>
        public string ShiftSchedulingRecordID { get; set; }
        /// <summary>
        /// 部门岗位编号，DepartmentPost表的主键
        /// </summary>
        public int DepartmentPostID { get; set; }
        /// <summary>
        /// 排班人
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime SchedulingDate { get; set; }
        /// <summary>
        /// 午别
        /// </summary>
        public string NoonType { get; set; }
        /// <summary>
        /// 岗位类型
        /// </summary>
        public string PostType { get; set; }
        /// <summary>
        /// 午别班次
        /// </summary>
        public string PostShiftID { get; set; }        
        /// <summary>
        /// 半天岗计算考勤天数
        /// </summary>
        public decimal HalfDayAttendanceDays { get; set; }
    }
}
