﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 审批主表仓储接口
    /// </summary>
    public interface IApproveRecordRepository
    {
        Task<ApproveRecordInfo> GetApproveRecordByRecordIDAsync(string approveRecordID);
        /// <summary>
        /// 获取还没有完成审批的审批申请记录
        /// </summary>
        ///  <param name="processID"></param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        Task<List<ApproveRecordInfo>> GetUnCompletedApproveRecordsAsNoTrackAsync(string processID, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 获取完成审批的审批申请记录
        /// </summary>
        /// <param name="employeeIDs">审批人员的工号集合</param>
        /// <param name="processID">审批流程主记录ID,null表示部分流程类别</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        Task<List<ApproveRecordInfo>> GetCompletedApproveRecordsAsync(List<string> employeeIDs, string processID, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 根据来源ID获取审批记录
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        Task<ApproveRecordInfo> GetApproveRecordBySourceIDAsync(string sourceID);
        /// <summary>
        /// 获取审批记录
        /// </summary>
        /// <param name="sourceIDs">来源主表ID</param>
        /// <returns></returns>
        Task<ApproveRecordInfo[]> GetRecordsBySourceIDs(IEnumerable<string> sourceIDs);
        /// <summary>
        /// 获取审批记录MainID集合
        /// </summary>
        /// <param name="sourceIDs"></param>
        /// <returns></returns>
        Task<string[]> GetMianIDArrayBySourceIDs(IEnumerable<string> sourceIDs);
        /// <summary>
        /// 获取审批原因
        /// </summary>
        /// <param name="recordIDs">审批记录ID</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetRevokeReasonsByRecordIDsAsync(List<string> recordIDs);
    }
}
