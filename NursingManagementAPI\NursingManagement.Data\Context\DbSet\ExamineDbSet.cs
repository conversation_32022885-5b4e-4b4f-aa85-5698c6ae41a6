﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;
using NursingManagement.Models.Examine;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 考核题库
        /// </summary>
        public DbSet<QuestionBankInfo> QuestionBankInfos { get; set; }

        /// <summary>
        /// 考核题目明细表
        /// </summary>
        public DbSet<ExaminationQuestionDetailInfo> ExaminationQuestionDetailInfos { get; set; }

        /// <summary>
        /// 考核题目表
        /// </summary>
        public DbSet<ExaminationQuestionInfo> ExaminationQuestionInfos { get; set; }
        /// <summary>
        /// 考核试卷主表
        /// </summary>
        public DbSet<ExaminationPaperMainInfo> ExaminationPaperMainInfos { get; set; }
        /// <summary>
        /// 考核记录表
        /// </summary>
        public DbSet<ExaminationRecordInfo> ExaminationRecordInfos { get; set; }
        /// <summary>
        /// 考核主表
        /// </summary>
        public DbSet<ExaminationMainInfo> ExaminationMainInfos { get; set; }
        /// <summary>
        /// 考核明细表
        /// </summary>
        public DbSet<ExaminationDetailInfo> ExaminationDetailInfos { get; set; }
        /// <summary>
        /// 考核条件表
        /// </summary>
        public DbSet<ExaminationConditionRecordInfo> ExaminationConditionRecordInfos { get; set; }
        /// <summary>
        /// 主考人表
        /// </summary>
        public DbSet<ExaminerInfo> ExaminerInfos { get; set; }
        /// <summary>
        /// 监考计划表
        /// </summary>
        public DbSet<ExaminerScheduleInfo> ExaminerScheduleInfos { get; set; }
        /// <summary>
        /// 监考计划表监考人
        /// </summary>
        public DbSet<ExaminerScheduleEmployeeInfo> ExaminerScheduleEmployeeInfos { get; set; }
        /// <summary>
        /// 监考计划表监考项目
        /// </summary>
        public DbSet<ExaminerScheduleItemInfo> ExaminerScheduleItemInfos { get; set; }
        /// <summary>
        /// 考核预约表
        /// </summary>
        public DbSet<ExaminationAppointmentInfo> ExaminationAppointmentInfos { get; set; }


    }
}
