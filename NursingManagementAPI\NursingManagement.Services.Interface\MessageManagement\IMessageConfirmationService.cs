﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 消息确认服务接口，定义了获取消息确认记录以及确认状态的方法。
    /// </summary>
    public interface IMessageConfirmationService
    {

        /// <summary>
        /// 获取指定消息记录ID的消息确认记录
        /// </summary>
        /// <param name="messageRecordID">消息记录ID</param>
        /// <returns>返回该消息记录的所有消息确认记录</returns>
        Task<List<MessageConfirmationInfo>> GetRecordsByMessageRecordID(string messageRecordID);

        /// <summary>
        /// 判断消息是否已经被指定员工确认
        /// </summary>
        /// <param name="employeeId">确认人ID</param>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <returns>如果已确认，返回true；否则返回false</returns>
        Task<bool> IsConfirmedAsync(string employeeId, string messageRecordId);
        /// <summary>
        /// 确认消息
        /// </summary>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns>确认是否成功</returns>
        Task<bool> SaveMessageConfirmation(string messageRecordId, string employeeID);
        /// <summary>
        /// 获取待确认消息
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <returns>返回待确认消息列表</returns>
        Task<MessageConfirmationView> GetPendingConfirmationMessages(string employeeID);
    }
}
