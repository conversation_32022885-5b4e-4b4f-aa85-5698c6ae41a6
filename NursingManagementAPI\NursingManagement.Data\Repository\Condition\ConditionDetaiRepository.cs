﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ConditionDetaiRepository : IConditionDetaiRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ConditionDetaiRepository
            (
            NursingManagementDbContext nursingManagementDbContext
            )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        public async Task<List<ConditionDetailInfo>> GetListByMainIDs(List<string> mainIDs)
        {
            return await _nursingManagementDbContext.ConditionDetailInfos.Where(m => mainIDs.Any(n => n == m.ConditionMainID) && m.DeleteFlag != "*").OrderBy(m => m.Sort).ToListAsync();
        }

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<List<ConditionDetailInfo>> GetListByMainID(string mainID)
        {
            return await _nursingManagementDbContext.ConditionDetailInfos.Where(m => mainID == m.ConditionMainID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
