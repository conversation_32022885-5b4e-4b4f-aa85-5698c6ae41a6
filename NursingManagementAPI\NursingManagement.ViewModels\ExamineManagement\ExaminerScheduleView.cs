﻿namespace NursingManagement.ViewModels
{
    public class ExaminerScheduleView
    {
        /// <summary>
        /// 监考计划ID
        /// </summary>
        public string ExaminerScheduleID { get; set; }
        /// <summary>
        /// 考试记录ID
        /// </summary>
        public List<string> ExaminationRecordIDs { get; set; }
        /// <summary>
        /// 考核名称
        /// </summary>
        public string ExaminationName { get; set; }
        /// <summary>
        /// 监考人工号
        /// </summary>
        public List<string> Examiners { get; set; }
        /// <summary>
        /// 监考人名称
        /// </summary>
        public string ExaminerName { get; set; }
        /// <summary>
        /// 计划日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }
        /// <summary>
        /// 计划时间段
        /// </summary>
        public string[] ScheduleTimeRange { get; set; }
        /// <summary>
        /// 状态:0未被预约，1已被预约
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 新增人编码
        /// </summary>
        public string AddEmployeeID{ get; set; }
        /// <summary>
        /// 新增人姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 考核地点
        /// </summary>
        public string Location { get; set; }
    }
}
