﻿namespace NursingManagement.ViewModels
{
    public class AnnualPlanMainGoalView
    {
        /// <summary>
        /// 计划主表ID
        /// </summary>
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 目标业务ID
        /// </summary>
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 年度计划类别序号，非自增
        /// </summary>
        public int AnnualPlanTypeID { get; set; }
        /// <summary>
        /// 年度计划类别名称
        /// </summary>
        public string AnnualPlanTypeContent { get; set; }
        /// <summary>
        /// 年度计划目标类别ID
        /// </summary>
        public int AnnualPlanGoalID { get; set; }
        /// <summary>
        /// 年度计划目标名称
        /// </summary>
        public string AnnualPlanGoalContent { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 目标序号,呈现顺序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
    }
}
