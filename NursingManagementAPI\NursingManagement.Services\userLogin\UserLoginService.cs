﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Text;

namespace NursingManagement.Services
{
    public class UserLoginService : IUserLoginService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISessionService _session;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserLoginRepository _userLoginRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IEmployeeRoleRepository _employeeRoleRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IDepartmentVSDepartmentRepository _departmentVSDepartmentRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        public UserLoginService(
            IUnitOfWork unitOfWork
            , ISessionService session
            , IHttpContextAccessor httpContextAccessor
            , IUserLoginRepository userLoginRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IEmployeeRoleRepository employeeRoleRepository
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , IDepartmentVSDepartmentRepository departmentVSDepartmentRepository
            , IDepartmentListRepository departmentListRepository
            , IAppConfigSettingRepository appConfigSettingRepository
        )
        {
            _unitOfWork = unitOfWork;
            _session = session;
            _httpContextAccessor = httpContextAccessor;
            _userLoginRepository = userLoginRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _employeeRoleRepository = employeeRoleRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _departmentVSDepartmentRepository = departmentVSDepartmentRepository;
            _departmentListRepository = departmentListRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
        }

        public async Task<Session> Login(LoginParam loginParam)
        {
            if (loginParam == null)
            {
                return null;
            }
            //  登录前记录当下登录的医院信息，便于从缓存取HospitalID和Language
            var tempToken = await _session.SetUserLoginSession(loginParam.HospitalID, loginParam.Language);
            var userLogin = await VerifyUser(loginParam);
            if (userLogin == null)
            {
                return null;
            };
            // 登录成功，创建Session
            var session = await CreateSession(loginParam, userLogin);
            // 将Session存入Redis缓存
            await _session.SetAsync(session);
            // 移除临时Token
            var tempSession = await _session.GetAsync(tempToken);
            if (tempSession != null)
            {
                await _session.RemoveAsync(tempSession);
            }
            return session;
        }

        /// <summary>
        /// 验证用户登录，并返回登录信息，如后续逻辑复杂，可扩展为工厂
        /// </summary>
        /// <param name="loginParam"></param>
        /// <returns></returns>
        private async Task<UserLoginInfo> VerifyUser(LoginParam loginParam)
        {
            // 先解密
            //loginParam.UserID = EncryptionAndDecryption.DecryptStr(loginParam.UserID);
            //loginParam.Password = EncryptionAndDecryption.DecryptStr(loginParam.Password);

            UserLoginInfo userLogin;
            // OA登录
            if (loginParam.LoginType == "oa")
            {
                userLogin = await _userLoginRepository.CheckOAUser(loginParam.UserID, loginParam.Password);
                if (userLogin == null)
                {
                    return null;
                }
                return userLogin;
            }
            // HIS登录
            if (loginParam.LoginType == "his")
            {
                userLogin = await _userLoginRepository.GetByHISUserID(loginParam.UserID);
                if (userLogin == null || userLogin.HisPassword != loginParam.Password)
                {
                    return null;
                }
                return userLogin;
            }
            // 微信小程序登录
            if (loginParam.LoginType == "wechatMiniProgram")
            {
                userLogin = await _userLoginRepository.GetByWechatMiniProgramOpenID(loginParam.UserID);
                if (userLogin == null)
                {
                    return null;
                }
                return userLogin;
            }
            // 微信H5登录
            if (loginParam.LoginType == "wechatWeb")
            {
                userLogin = await _userLoginRepository.GetByWechatWebOpenID(loginParam.UserID);
                if (userLogin != null)
                {
                    return userLogin;
                }
                return userLogin;
            }
            return null;
        }
        /// <summary>
        /// 创建Session
        /// </summary>
        /// <param name="loginParam"></param>
        /// <param name="userLogin"></param>
        /// <returns></returns>
        private async Task<Session> CreateSession(LoginParam loginParam, UserLoginInfo userLogin)
        {
            var session = await GetSessionByToken();
            // 已有Token直接返回
            if (session != null)
            {
                return session;
            }
            string token = userLogin.GetId();
            _httpContextAccessor.HttpContext.SetToken(token);
            var employee = await _employeePersonalDataRepository.GetDataByEmployeeID(userLogin.EmployeeID);
            session = new Session()
            {
                Token = token,
                LoginTime = DateTime.Now,
                LoginType = loginParam.LoginType,
                EmployeeID = userLogin.EmployeeID,
                OAUserID = userLogin.OAUserID,
                HisUserID = userLogin.HisUserID,
                WechatWebOpenID = userLogin.WechatWebOpenID,
                WechatMiniProgramOpenID = userLogin.WechatMiniProgramOpenID,
                WechatUnionID = userLogin.WechatUnionID,
                UserName = employee?.EmployeeName,
                Language = loginParam.Language,
                HospitalID = loginParam.HospitalID,
                ClientType = loginParam.ClientType,
            };
            session.Roles = await _employeeRoleRepository.GetRolesByEmployeeID(userLogin.EmployeeID);
            var staffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(userLogin.EmployeeID);
            if (staffData != null && staffData.DepartmentID != null)
            {
                session.DepartmentID = staffData.DepartmentID.Value;
            }
            return session;
        }

        public async Task<Session> GetSessionByToken()
        {
            // 获取token
            var token = _httpContextAccessor.HttpContext.GetCommonToken();
            if (string.IsNullOrWhiteSpace(token))
            {
                return null;
            }
            // 根据token获取session
            var session = await _session.GetAsync(token);
            return session;
        }
        public async Task<bool> BindWechat(BindWechatParam bindWechatParam)
        {
            if (bindWechatParam == null || string.IsNullOrWhiteSpace(bindWechatParam.OpenID))
            {
                return false;
            }
            var userLogin = await VerifyUser(bindWechatParam);
            if (userLogin == null)
            {
                return false;
            };
            // 根据客户端类型绑定
            if (bindWechatParam.ClientType == 4)
            {
                // Web微信公众号登录绑定
                userLogin.WechatWebOpenID = bindWechatParam.OpenID;
            }
            else
            {
                // 微信小程序绑定
                userLogin.WechatMiniProgramOpenID = bindWechatParam.OpenID;
            }
            _unitOfWork.GetRepository<UserLoginInfo>().Update(userLogin);
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            // 绑定成功，刷新缓存
            if (result)
            {
                await _userLoginRepository.UpdateCache();
            }
            return result;
        }

        public async Task<bool> LogOut()
        {
            // 获取token
            var session = await _session.GetSession();
            if (session != null)
            {
                return await _session.RemoveAsync(session);
            }
            return true;
        }
        public async Task<bool> UpdateDepartmentIDOfSessionAsync(int departmentID)
        {
            var session = await _session.GetSession();
            if (session?.DepartmentID != departmentID)
            {
                session.DepartmentID = departmentID;
                await _session.RemoveAsync(session);
                await _session.SetAsync(session);
            }
            return true;
        }
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="loginParam">登录信息</param>
        /// <returns></returns>
        public async Task<bool> ChangePassword(LoginParam loginParam)
        {
            if (loginParam == null || string.IsNullOrWhiteSpace(loginParam.NewPassword))
            {
                throw new ArgumentNullException(nameof(loginParam), "缺少必要参数，请联系管理员！");
            }
            var userLogin = await VerifyUser(loginParam) ?? throw new ArgumentException("用户名或密码错误！");
            userLogin.PasswordChanged = true;
            userLogin.Modify(loginParam.UserID);
            if (loginParam.LoginType.Equals("oa"))
            {
                userLogin.OAPassword = Encoding.UTF8.GetBytes(loginParam.NewPassword);
            }
            if (loginParam.LoginType.Equals("his"))
            {
                userLogin.HisPassword = loginParam.NewPassword;
            }
            _unitOfWork.GetRepository<UserLoginInfo>().Update(userLogin);
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await _userLoginRepository.UpdateCache();
            }
            return true;
        }

        /// <summary>
        /// 转换其他系统的部门ID为OrganizationType=1的部门ID
        /// </summary>
        /// <param name="departmentID">其他系统部门ID,CCC对应OrganizationType=3</param>
        /// <param name="sourceSystem">来源系统，如CCC</param>
        /// <returns>转换后的部门ID或者null</returns>
        public async Task<int?> TransformDepartmentIDAsync(int departmentID, string sourceSystem)
        {
            var sourceOrganizationType = sourceSystem switch
            {
                "CCC" => "3",
                _ => throw new NotImplementedException(),
            };
            var departmentIDByThreeOrganizationType = await _departmentListRepository.GetDepartmentIDByOrganizationAndDepartmentCodeAsync(sourceOrganizationType, departmentID.ToString());
            var departmentVsDepartmentList = await _departmentVSDepartmentRepository.GetByOrganizationTypeAndDepartmentID(sourceOrganizationType, departmentIDByThreeOrganizationType, "1");

            return departmentVsDepartmentList.Count > 0 ? departmentVsDepartmentList[0].DepartmentID2 : null;
        }

        public async Task<Session> SSOUserCheck(string token, int clientType, string loginType, string hospitalID, int language)
        {
            if (string.IsNullOrWhiteSpace(token))
            {
                throw new CustomException("SSOUserCheck方法，传入的token为空！", true);
            }
            //  登录前记录当下登录的医院信息，便于从缓存取HospitalID和Language
            var tempToken = await _session.SetUserLoginSession(hospitalID, language);
            // 根据token获取SSO账号
            var userID = await GetUserIDBySSOToken(token);
            if (string.IsNullOrWhiteSpace(userID))
            {
                throw new CustomException($"SSOUserCheck方法验证失败，token参数：{token}", true);
            }
            var userLogin = await _userLoginRepository.GetByHISUserID(userID);
            var loginParam = new LoginParam()
            {
                LoginType = loginType,
                ClientType = clientType,
                HospitalID = hospitalID,
                Language = language
            };
            if (userLogin == null)
            {
                return null;
            };
            // 登录成功，创建Session
            var session = await CreateSession(loginParam, userLogin);
            // 将Session存入Redis缓存
            await _session.SetAsync(session);
            // 移除临时Token
            var tempSession = await _session.GetAsync(tempToken);
            if (tempSession != null)
            {
                await _session.RemoveAsync(tempSession);
            }
            return session;
        }
        /// <summary>
        /// 根据token获取SSO账号
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        private async Task<string> GetUserIDBySSOToken(string token)
        {
            // 获取检验单点登录账号的接口配置
            var api = await _appConfigSettingRepository.GetConfigSettingValue("SSOConfigs", "CheckAPI");
            var appID = await _appConfigSettingRepository.GetConfigSettingValue("SSOConfigs", "AppID");
            try
            {
                var userData = await HttpHelper.HttpGetAsync($"{api}?app_id={appID}&ticket={token}");
                var result = JsonConvert.DeserializeObject<JObject>(userData);
                if (result != null && result["code"] != null)
                {
                    if (result["code"].ToString() != "1")
                    {
                        throw new CustomException($"GetUserIDBySSOToken单点验证失败，验证参数：{userData}", true);
                    }
                    var user = JsonConvert.DeserializeObject<JObject>(result["user_ext"].ToString());
                    return user["user_name"].ToString();
                }
            }
            catch (Exception ex)
            {
                throw new CustomException($"GetUserIDBySSOToken单点验证错误，验证api：{api} 错误信息：{ex}", true);
            }
            return "";
        }
    }
}