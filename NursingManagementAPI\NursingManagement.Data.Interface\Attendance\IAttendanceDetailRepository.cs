﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IAttendanceDetailRepository
    {
        /// <summary>
        /// 获取考勤明细
        /// </summary>
        /// <param name="attendanceRecordID"></param>
        /// <returns></returns>
        Task<List<AttendanceDetailInfo>> GetDetailByRecordID(string attendanceRecordID);

        /// <summary>
        /// 获取批量考勤明细
        /// </summary>
        /// <param name="attendanceRecordIDs"></param>
        /// <returns></returns>
        Task<List<AttendanceDetailInfo>> GetDetailByRecordIDs(List<string> attendanceRecordIDs);
    }
}
