﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Commands;
using System.Text;
using static NursingManagement.Models.AnnualPlanEnums;

namespace NursingManagement.Services
{
    public class AnnualScheduleService : IAnnualScheduleService
    {

        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ITieredTaskRepository _scheduleMainRepository;
        private readonly IAnnualScheduleDetailRepository _detailRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAnnualPlanMainRepository _annualPlanMainRepository;
        private readonly IMessageService _messageService;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;

        public AnnualScheduleService(
            ITieredTaskRepository mainRepository,
            IAnnualScheduleDetailRepository detailRepository,
            IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
            IAnnualPlanMainRepository annualPlanMainRepository,
            IUnitOfWork unitOfWork,
            IMessageService messageService,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IEmployeeStaffDataRepository employeeStaffDataRepository,
            IDepartmentListRepository departmentListRepository
            )
        {
            _scheduleMainRepository = mainRepository;
            _detailRepository = detailRepository;
            _annualPlanMainRepository = annualPlanMainRepository;
            _unitOfWork = unitOfWork;
            _messageService = messageService;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _departmentListRepository = departmentListRepository;
        }

        /// <summary>
        /// 护理部ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;
        /// <summary>
        /// 护理委员会
        /// </summary>
        private const int DEPARTMENT_ID_53 = 53;

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<(bool, string message)> DeleteAnnualScheduleAsync(string scheduleMainID, string employeeID)
        {
            var scheduleMainInfo = await _scheduleMainRepository.GetTieredTaskByID(scheduleMainID);
            if (scheduleMainInfo == null)
            {
                return (false, "查找不到需要删除的任务，请刷新页面查看一下。");
            }
            if (scheduleMainInfo.PerformDateTime is not null)
            {
                return (false, "任务已执行，不能删除！");
            }
            scheduleMainInfo.Delete(employeeID);
            var scheduleDetailInfos = await _detailRepository.GetAnnualScheduleDetailsAsync(scheduleMainID, false);
            scheduleDetailInfos?.ForEach(info => info.Delete(employeeID));
            return (await _unitOfWork.SaveChangesAsync() > 0, null);
        }
        /// <summary>
        /// 获取年度计划任务对应明细
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        public async Task<List<AnnualScheduleDetailView>> GetAnnualScheduleDetailsAsync(string scheduleMainID)
        {
            return await _detailRepository.GetAnnualScheduleDetailViewsAsync(scheduleMainID);
        }
        /// <summary>
        /// 获取年度计划主记录集合（根据执行人、年、月）
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="scheduleMonth">计划月份</param>
        /// <param name="scheduleYear">计划年度</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<AnnualScheduleMainView[]> GetAPSchedules(string employeeID, int? scheduleMonth, int scheduleYear, int departmentID)
        {
            if (!scheduleMonth.HasValue || scheduleMonth == 0)
            {
                throw new ArgumentException($"参数检查异常：scheduleMonth = {scheduleMonth}");
            }
            var tasks = await _scheduleMainRepository.GetTieredTaskViews(employeeID, scheduleMonth.Value, scheduleYear, departmentID);

            var upperDepartmentIDs = await GetUpperDepartmentIDs(departmentID);
            // 补充名称和要求，优先使用本部门的，没有再选择上级部门的
            foreach (var task in tasks)
            {
                var relation = task.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == departmentID);
                // 向上查找，从最近的上级部门开始
                foreach (var upperDepartmentID in upperDepartmentIDs)
                {
                    relation ??= task.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == upperDepartmentID);
                }
                task.InterventionName = relation.WorkContent;
                task.Requirement = relation.Requirement;
            }
            return tasks;
        }
        /// <summary>
        /// 获取任务统计数据
        /// </summary>
        /// <param name="schedulePerformer"></param>
        /// <param name="scheduleYear"></param>
        /// <returns></returns>
        public async Task<List<ScheduleStatisticsView>> GetAnnualScheduleStatisticsAsync(string schedulePerformer, int scheduleYear)
        {
            if (schedulePerformer == null)
            {
                _logger.Warn("请求GetAnnualScheduleStatisticsAsync方法传递参数schedulePerformer为空");
                return null;
            }

            var list = await _scheduleMainRepository.GetTasksMonthlyCountViews(schedulePerformer, scheduleYear);
            var existMonths = list.Select(m => m.Month).ToHashSet();
            // 补充没有数据月份的实例
            for (int index = 1; index <= 12; index++)
            {
                if (existMonths.Contains(index))
                {
                    continue; ;
                }
                list.Insert(index - 1, new ScheduleStatisticsView
                {
                    Month = index,
                    StatisticValue = 0,
                    StatisticTotalValue = 0
                });
            }
            return list;
        }
        /// <summary>
        /// 保存年度计划任务
        /// </summary>
        /// <param name="scheduleParamsView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> SaveAnnualScheduleAsync(AnnualScheduleParamsView scheduleParamsView, string employeeID)
        {
            if (scheduleParamsView == null)
            {
                _logger.Warn("SaveAnnualScheduleAsync方法参数异常,为空");
                return false;
            }
            var scheduleMainInfo = await _scheduleMainRepository.GetTieredTaskByID(scheduleParamsView.ScheduleMainID);
            if (scheduleMainInfo == null)
            {
                _logger.Error($"无法获取计划任务ScheduleMainID = {scheduleParamsView.ScheduleMainID}");
                return false;
            }
            scheduleMainInfo.Modify(employeeID);

            // 根据原因有无，判断是否是延迟执行
            scheduleMainInfo.Status = !string.IsNullOrEmpty(scheduleParamsView.Reason) ? APScheduleStatus.Delay : APScheduleStatus.Performed;
            scheduleMainInfo.Performer = employeeID;
            scheduleMainInfo.PerformComment = scheduleParamsView.PerformComment;
            scheduleMainInfo.PerformDateTime = scheduleParamsView.PerformDateTime;
            scheduleMainInfo.Reason = scheduleParamsView.Reason;
            scheduleMainInfo.DelayContent = scheduleParamsView.DelayContent;
            var scheduleDetails = CreateScheduleDetailInfos(scheduleParamsView, employeeID);

            await _unitOfWork.GetRepository<AnnualScheduleDetailInfo>().InsertAsync(scheduleDetails);

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 根据执行明细生成Model
        /// </summary>
        /// <param name="scheduleParamsView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private List<AnnualScheduleDetailInfo> CreateScheduleDetailInfos(AnnualScheduleParamsView scheduleParamsView, string employeeID)
        {
            var detailInfos = new List<AnnualScheduleDetailInfo>();
            scheduleParamsView.ScheduleDetails.ForEach(detail =>
            {
                var detailInfo = new AnnualScheduleDetailInfo
                {
                    InterventionDetailID = detail.InterventionDetailID,
                    InterventionID = detail.InterventionID,
                    AnnualScheduleMainID = scheduleParamsView.ScheduleMainID,
                    DeleteFlag = ""
                };
                // TODO 此处存在新增和修改字段，那么修改操作应该如何处理
                detailInfo.Add(employeeID);
                detailInfo.Modify(employeeID);
                detailInfo.AnnualScheduleDetailID = detailInfo.GetId();
                detailInfos.Add(detailInfo);
            });

            return detailInfos;

        }
        /// <summary>
        /// 展出下月任务
        /// </summary>
        /// <param name="cmd">命令</param>
        /// <returns></returns>
        public async Task<bool> CreateOrUpdateTasks(CreateOrUpdateTasksCommand cmd)
        {
            // 根据执行人、时间查询任务
            var tasks = await _scheduleMainRepository.GetMonthlyTasks(cmd.Year, cmd.Month, cmd.HospitalID);
            // 构造创建任务的方法
            var addTask = CreateFuncForAddTask(cmd, tasks);
            // 尝试将计划工作转为任务（或关系）并新增之
            cmd.Works.ForEach(work =>
            {
                work.PrincipalIDs.ForEach(async principal =>
                {
                    await addTask(work, principal);
                });
            });

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 为新增任务创建方法
        /// </summary>
        /// <param name="cmd">新增视图</param>
        /// <param name="tasks">任务列表</param>
        /// <param name="currentTime">当前时间</param>
        /// <returns></returns>
        private Func<MonthlyPlanWorkVo, string, Task> CreateFuncForAddTask(CreateOrUpdateTasksCommand cmd, List<AnnualScheduleMainInfo> tasks)
        {
            var currentTime = DateTime.Now;
            var scheduleDate = new DateTime(cmd.Year, cmd.Month, 1);
            async Task createScheduleOrRelationFunc(MonthlyPlanWorkVo work, string schedulePerformer)
            {
                // 从已有的任务中查找任务是否已被创建
                // 分两种情况：临时性工作、非临时性工作
                // 临时性工作：执行人+关系信息查询，若不存在则说明任务不存在
                // 非临时性工作：先根据字典ID+执行人查询，不存在说明任务不存在，否则再根据关系信息查询关系是否已存在
                var task = tasks.IfWhere(work.ApInterventionID.HasValue, m => m.InterventionID == work.ApInterventionID && m.SchedulePerformer == schedulePerformer)
                    .IfWhere(!work.ApInterventionID.HasValue, m => m.SchedulePerformer == schedulePerformer && m.MonthlyWorkToTasks.Any(n => n.MonthlyPlanDetailID == work.Key))
                    .FirstOrDefault();
                // 任务不存在，创建任务
                if (task is null)
                {
                    await CreateNewTask(cmd, currentTime, work, schedulePerformer, scheduleDate);
                    return;
                }
                // 任务存在，进一步检查是否存在关系
                var relation = task.MonthlyWorkToTasks.FirstOrDefault(m => m.MonthlyPlanDetailID == work.Key);
                if (relation is not null)
                {
                    return;
                }
                // 关系若为空，仅创建关系
                var newRelation = new MonthlyWorkToTaskInfo
                {
                    MonthlyPlanDetailID = work.Key,
                    ApScheduleMainID = task.AnnualScheduleMainID,
                    DepartmentID = cmd.DepartmentID,
                    WorkContent = work.WorkContent,
                    Requirement = work.Requirement
                };
                newRelation.Add(cmd.EmployeeID).Modify(cmd.EmployeeID);
                task.MonthlyWorkToTasks.Add(newRelation);
            }
            return createScheduleOrRelationFunc;
        }

        /// <summary>
        /// 创建新任务
        /// </summary>
        /// <param name="cmd">新增视图</param>
        /// <param name="currentTime">当前时间</param>
        /// <param name="work">待转换为任务的计划工作</param>
        /// <param name="schedulePerformer">预计执行人</param>
        /// <param name="scheduleDate">预计执行时间</param>
        /// <returns></returns>
        private async Task CreateNewTask(CreateOrUpdateTasksCommand cmd, DateTime currentTime, MonthlyPlanWorkVo work, string schedulePerformer, DateTime scheduleDate)
        {
            var guid = "".NewGuid();
            var monthlyWorkToTask = new MonthlyWorkToTaskInfo
            {
                MonthlyPlanDetailID = work.Key,
                ApScheduleMainID = guid,
                DepartmentID = cmd.DepartmentID,
                WorkContent = work.WorkContent,
                Requirement = work.Requirement
            }.Add(cmd.EmployeeID).Modify(cmd.EmployeeID) as MonthlyWorkToTaskInfo;
            var newTask = new AnnualScheduleMainInfo
            {
                AnnualScheduleMainID = guid,
                HospitalID = cmd.HospitalID,
                InterventionID = work.ApInterventionID,
                ScheduleYear = cmd.Year,
                ScheduleMonth = (byte)cmd.Month,
                ScheduleDateTime = scheduleDate,
                SchedulePerformer = schedulePerformer,
                Status = APScheduleStatus.UnPerform,
                AddDateTime = currentTime,
                AddEmployeeID = cmd.EmployeeID,
                ModifyDateTime = currentTime,
                ModifyEmployeeID = cmd.EmployeeID,
                DeleteFlag = "",
                MonthlyWorkToTasks = [monthlyWorkToTask]
            }.Add(cmd.EmployeeID).Modify(cmd.EmployeeID) as AnnualScheduleMainInfo;
            await _unitOfWork.GetRepository<AnnualScheduleMainInfo>().InsertAsync(newTask);
        }

        /// <summary>
        /// 某月未执行任务统计量
        /// </summary>
        /// <param name="scheduleYear">年度</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="schedulePerformer">预计执行人</param>
        /// <returns></returns>
        public async Task<int?> GetStatisticValueByMonthAsync(int scheduleYear, int scheduleMonth, string schedulePerformer)
        {
            return await _scheduleMainRepository.GetMonthlyUnPerformTasksCount(scheduleYear, scheduleMonth, schedulePerformer);
        }
        /// <summary>
        /// 发送年度计划钉钉消息提醒
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SendAnnualPlanRemindAsync()
        {
            var currentDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var unPerformTasks = await _scheduleMainRepository.GetDailyUnPerformTaskRelationWorks(currentDate);
            if (unPerformTasks.Count == 0)
            {
                _logger.Warn("没有需要提醒的年度计划执行");
                return false;
            }
            var employeeIDs = unPerformTasks.Select(m => m.SchedulePerformer).Distinct().ToList();
            var employeeList = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var staffData = await _employeeStaffDataRepository.GetAll<EmployeeStaffDataInfo>();

            foreach (var employeeID in employeeIDs)
            {
                var departmentID = staffData.Find(m => m.EmployeeID == employeeID)?.DepartmentID ?? 0;
                var employeeName = employeeList.TryGetValue(employeeID, out var name) ? $"{name}老师" : "老师";
                var works = unPerformTasks.Find(m => m.SchedulePerformer == employeeID)
                    ?.MonthlyWorkToTasks;
                var interventionsStr = BuildMessageContent(works, departmentID);
                var sendParam = new MessageView
                {
                    MessageTools = [MessageTool.Dingtalk],
                    EmployeeID = employeeID,
                    MessageCondition = new MessageConditionView()
                    {
                        Message = $"温馨提醒: {employeeName}, 您{currentDate.Year}年{currentDate.Month}月需要按照计划执行的措施：<br>{interventionsStr}"
                    }
                };

                if (!await _messageService.SendMessage(sendParam))
                {
                    _logger.Warn($"年度计划执行钉钉消息发送失败，sendParam：{sendParam}");
                }
            }
            return true;
        }
        /// <summary>
        /// 组装信息Content
        /// </summary>
        /// <param name="relationWorks">当前任务关联计划工作信息</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        private static string BuildMessageContent(ICollection<MonthlyWorkToTaskInfo> relationWorks, int departmentID)
        {
            var sort = 1;
            var messageContent = relationWorks.Aggregate(new StringBuilder(), (current, next) =>
            {
                // 优先取当前部门的任务数据
                var interventionName = relationWorks.FirstOrDefault(m => m.DepartmentID == departmentID)?.WorkContent;
                interventionName ??= relationWorks.FirstOrDefault()?.WorkContent;
                current.AppendLine($"{sort++}. {interventionName}<br>");
                return current;
            });
            return messageContent.ToString();
        }
        /// <summary>
        /// 优先获取当前部门计划对应的未执行任务数据
        /// </summary>
        /// <param name="employeeID">计划执行人</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="preOrNextFlag">获取计划月份之前OR之后，True：当月和当月之前；False：当月</param>
        /// <returns></returns>
        //public async Task<AnnualScheduleMainView[]> GetUnExecTasks(string employeeID, int departmentID, int? scheduleMonth, bool preOrNextFlag)
        //{
        //    // 获取本月之后未执行的年度计划任务
        //    var unPerformTaskViews = await _scheduleMainRepository.GetUnPerformTaskViews(employeeID, DateTime.Now.Year, scheduleMonth, departmentID, preOrNextFlag);

        //    var upperDepartmentIDs = await GetUpperDepartmentIDs(departmentID);
        //    // 补充名称，优先使用本部门的，没有再选择上级部门的
        //    foreach (var unPerformTaskView in unPerformTaskViews)
        //    {
        //        var relation = unPerformTaskView.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == departmentID);
        //        // 向上查找，从最近的上级部门开始
        //        foreach (var upperDepartmentID in upperDepartmentIDs)
        //        {
        //            relation ??= unPerformTaskView.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == upperDepartmentID);
        //        }
        //        if (relation is null)
        //        {
        //            _logger.Error($"未找到任务关联的月度计划工作信息，taskID={unPerformTaskView.AnnualScheduleMainID}");
        //            throw new CustomException("发生内部错误，请联系管理员");
        //        }
        //        unPerformTaskView.InterventionName = relation.WorkContent;
        //    }

        //    return [.. unPerformTaskViews.OrderBy(m => m.ScheduleDateTime)];
        //}

        /// <summary>
        /// 获取任务来源
        /// </summary>
        /// <param name="annualScheduleMainID">任务主表ID</param>
        /// <param name="interventionID">措施字典ID</param>
        /// <returns></returns>
        /// <exception cref="CustomException">查询不到任务异常</exception>
        public async Task<List<string>> GetTaskSource(string annualScheduleMainID, int interventionID)
        {
            var schedule = await _scheduleMainRepository.GetTieredTaskByID(annualScheduleMainID) ?? throw new CustomException("查询计划任务发生异常，请联系管理员！");
            if (interventionID == 0)
            {
                var employee = await _employeePersonalDataRepository.GetEmployeeNameByID(schedule.AddEmployeeID);
                var result = (string.IsNullOrEmpty(employee) ? "" : employee + "于") + schedule.AddDateTime.ToString("yyyy-MM-dd HH:mm") + "手动加入";
                return [result];
            }
            var departmentList = await _departmentListRepository.GetAllDictAsync();

            var taskSources = schedule.MonthlyWorkToTasks.Select(relationWork =>
            {
                var department = departmentList.Find(m => m.DepartmentID == relationWork.DepartmentID);
                var result = department != null ? department.DepartmentContent + "计划，" : "";
                result += relationWork.WorkContent + "，";
                return result;
            }).ToList();
            return taskSources;
        }
        /// <summary>
        /// 手动添加任务
        /// </summary>
        /// <param name="scheduleDate">预计执行日期</param>
        /// <param name="annualScheduleContent">措施内容</param>
        /// <param name="hospitalID">医院代码</param>
        /// <param name="employeID">用户ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<bool> AddAnnualScheduleManual(DateTime scheduleDate, string annualScheduleContent, string hospitalID, string employeID, int departmentID)
        {
            var planMainID = await _annualPlanMainRepository.GetMainIDByDeptIDAndYear(departmentID, scheduleDate.Year);
            if (string.IsNullOrEmpty(planMainID))
            {
                throw new Exception("该部门没有年度计划，无法添加年度计划");
            }
            var schedule = new AnnualScheduleMainInfo
            {
                AnnualScheduleMainID = "".NewGuid(),
                HospitalID = hospitalID,
                ScheduleYear = scheduleDate.Year,
                ScheduleMonth = (byte)scheduleDate.Month,
                ScheduleDateTime = scheduleDate,
                SchedulePerformer = employeID,
                Status = APScheduleStatus.UnPerform,
                DeleteFlag = ""
            }.Add(employeID).Modify(employeID) as AnnualScheduleMainInfo;
            await _unitOfWork.GetRepository<AnnualScheduleMainInfo>().InsertAsync(schedule);

            var monthlyWorkToTask = new MonthlyWorkToTaskInfo
            {
                MonthlyPlanDetailID = "",
                ApScheduleMainID = schedule.AnnualScheduleMainID,
                DepartmentID = departmentID,
                WorkContent = annualScheduleContent,
                Requirement = "",
                DeleteFlag = ""
            }.Add(employeID).Modify(employeID) as MonthlyWorkToTaskInfo;
            await _unitOfWork.GetRepository<MonthlyWorkToTaskInfo>().InsertAsync(monthlyWorkToTask);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 获取上级部门ID集合
        /// </summary>
        /// <param name="departmentID">当前部门ID</param>
        /// <returns></returns>
        private async Task<HashSet<int>> GetUpperDepartmentIDs(int departmentID)
        {
            var upperDepartmentIDs = await _departmentListRepository.GetUpperDepartmentRecursion<int, HashSet<int>>(departmentID, m => m.DepartmentID);
            var committeeDepartments = await _departmentListRepository.GetLowerDepartments(DEPARTMENT_ID_53);
            upperDepartmentIDs.Add(DEPARTMENT_ID_405);
            committeeDepartments.ForEach(m => upperDepartmentIDs.Add(m.DepartmentID));
            return upperDepartmentIDs;
        }
    }
}
