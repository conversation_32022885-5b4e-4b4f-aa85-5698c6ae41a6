﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员职务对照表
    /// </summary>
    [Table("EmployeeToJob")]
    public class EmployeeToJobInfo : MutiModifyInfo
    {
        /// <summary>
        /// 人员ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 岗位编号，主键，来源DepartmentToJob的JobCode
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string JobCode { get; set; }
        /// <summary>
        /// 护理管理部门编码,OrganizationType=1
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号，主键
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
