﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 考核预约记录操作Service
    /// </summary>
    public interface IExaminationAppointmentService
    {
        /// <summary>
        /// 获取可以预约的监考日信息
        /// </summary>
        /// <param name="examinationRecordID"></param>
        /// <returns></returns>
        Task<List<AppointmentExaminerScheduleView>> GetAvailableAppointmentList(string examinationRecordID);
        /// <summary>
        /// 保存考核预约记录
        /// </summary>
        /// <param name="appointmentView">包含完整预约信息的实体对象</param>
        /// <param name="employeeID">会话人工号</param>
        /// <returns></returns>
        Task<bool> SaveExaminationAppointment(ExaminationAppointmentView appointmentView, string employeeID);
        /// <summary>
        /// 获取预约信息
        /// </summary>
        /// <param name="examinationRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<AppointmentExaminerScheduleView> GetAppointmentByExaminationRecordID(string examinationRecordID, string employeeID);
        /// <summary>
        /// 取消指定考核预约记录
        /// </summary>
        /// <param name="cancelAppointmentParamView">取消相关参数</param>
        /// <returns></returns>
        Task<bool> CancelAppointment(CancelAppointmentParamView cancelAppointmentParamView);

        /// <summary>
        /// 获取考核预约信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<ExaminationAppointmentData>> GetAppointmentList(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 发布实操类考试通知
        /// </summary>
        /// <returns></returns>
        Task<bool> SendPracticalExamNotification();

    }
}