﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data
{
    public class MonthlyPlanMaintainRepository(NursingManagementDbContext dbContext) : IMonthlyPlanMaintainRepository
    {

        /// <summary>
        /// 获取月度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">部门ID</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        public async Task<string> GetMonthlyPlanMainID(string annualPlanMainID, int month)
        {
            return await dbContext.MonthlyPlanMainInfos.Where(m => m.AnnualPlanMainID == annualPlanMainID && m.Month == month && m.DeleteFlag != "*")
                .Select(m => m.MonthlyPlanMainID)
                .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取月度计划
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        public async Task<MonthlyPlanMainInfo> GetMonthlyPlanMain(string monthlyPlanMainID)
        {
            return await dbContext.MonthlyPlanMainInfos
                .FirstOrDefaultAsync(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 获取月度计划状态
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        public async Task<int> GetMonthlyPlanStatus(string monthlyPlanMainID)
        {
            return await dbContext.MonthlyPlanMainInfos.Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*")
                .Select(m => m.StatusCode)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取月度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, Dictionary<int, string>>> GetMonthToID(string[] annualPlanMainIDs)
        {
            return await dbContext.MonthlyPlanMainInfos.Where(m => annualPlanMainIDs.Contains(m.AnnualPlanMainID) && m.DeleteFlag != "*")
                .GroupBy(m => m.AnnualPlanMainID)
                .ToDictionaryAsync(m => m.Key, m => m.ToDictionary(n => n.Month, n => n.MonthlyPlanMainID));
        }
        /// <summary>
        /// 获取月度计划View
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        public async Task<TieredPlanWorksByType[]> GetMonthlyWorks(string monthlyPlanMainID)
        {
            var views = await dbContext.MonthlyPlanDetailInfos.Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*")
                .GroupBy(m => m.TypeID)
                .Select(m => new TieredPlanWorksByType
                {
                    TypeID = m.Key,
                    Children = m.Select(n => new TieredPlanWork
                    {
                        Key = n.MonthlyPlanDetailID,
                        APInterventionID = n.APInterventionID,
                        TypeID = n.TypeID,
                        Sort = n.Sort,
                        WorkContent = n.WorkContent,
                        WorkType = (byte)n.WorkType,
                        IsTemp = n.IsTemp,
                        Requirement = n.Requirement,
                        PrincipalIDs = n.PrincipalIDs,
                        PrincipalGroupName = n.PrincipalGroupName,
                    }).OrderBy(m => m.Sort.HasValue ? 0 : 1).ThenBy(m => m.Sort).ToArray()
                }).ToArrayAsync();
            return views;
        }

        /// <summary>
        /// 获取工作ID与工作内容
        /// </summary>
        /// <param name="workIDs">工作ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs)
        {
            return await dbContext.MonthlyPlanDetailInfos.Where(m => m.DeleteFlag != "*" && workIDs.Contains(m.MonthlyPlanDetailID))
                .ToDictionaryAsync(m => m.MonthlyPlanDetailID, m => m.WorkContent);
        }

        /// <summary>
        /// 获取多个部门的月度计划工作
        /// </summary>
        /// <param name="apMainIDs">年度计划主表ID</param>
        /// <param name="refInterventionIDs">已参考的执行项目字典ID</param>
        /// <param name="onlyPriority">是否仅获取重点工作</param>
        /// <returns></returns>
        public async Task<List<TieredPlanWorksByPlanThenType>> GetWorkViews(string[] apMainIDs, int? includeApInterventionID = null, int[] excludeApInterventionIDs = null, bool onlyPriority = false, bool includeTempWork = true)
        {
            // 表达式组装器，业务解释：
            // 零、以 true作为表达式开始，组装完结构大致为：(true && condition1 && condition2) || condition3
            // 一、若有 excludeApInterventionIDs，说明要依据 interventionID排除部分工作
            // 二、若有 includeApInterventionID，说明要依据 interventionID筛选指定字典工作
            // 三、前两个筛选完，临时标记（IsTemp）为 true的也要加进来
            var apInterventionPredicate = ExpBuilder.True<MonthlyPlanDetailInfo>()
                .IfAnd((excludeApInterventionIDs?.Length ?? 0) > 0, m => m.APInterventionID.HasValue && !excludeApInterventionIDs.Contains(m.APInterventionID.Value))
                .IfAnd(includeApInterventionID.HasValue, m => m.APInterventionID == includeApInterventionID.Value);
            var workPredicate = apInterventionPredicate.IfOr(includeTempWork, m => m.IsTemp);

            var workViews = await dbContext.MonthlyPlanDetailInfos.Where(m => m.DeleteFlag != "*").Where(workPredicate)
                .Join(dbContext.MonthlyPlanMainInfos.Where(m => m.DeleteFlag != "*" && apMainIDs.Contains(m.AnnualPlanMainID) && m.StatusCode == 1), m => m.MonthlyPlanMainID, m => m.MonthlyPlanMainID, (detail, main) => new
                {
                    main.AnnualPlanMainID,
                    detail,
                })
                .GroupBy(m => m.AnnualPlanMainID)
                .ToDictionaryAsync(m => m.Key, m => m.ToList().Select(o => new TieredPlanWork
                {
                    APInterventionID = o.detail.APInterventionID,
                    WorkContent = o.detail.WorkContent,
                    TypeID = o.detail.TypeID,
                    Requirement = o.detail.Requirement,
                    WorkType = (byte)o.detail.WorkType,
                    IsTemp = o.detail.IsTemp
                }));
            return workViews.Select(pair =>
            {
                var typeGroup = pair.Value.GroupBy(m => m.TypeID);
                return new TieredPlanWorksByPlanThenType
                {
                    PlanMainID = pair.Key,
                    Children = typeGroup.Select(m => new TieredPlanWorksByType
                    {
                        TypeID = m.Key,
                        Children = [.. m]
                    }).ToArray()
                };
            }).ToList();
        }
        /// <summary>
        /// 获取月度计划工作
        /// </summary>
        /// <param name="monthlyPlanDetailIDs">工作ID集合</param>
        /// <returns></returns>
        public async Task<List<MonthlyPlanDetailInfo>> GetMonthlyPlanWorks(string[] monthlyPlanDetailIDs)
        {
            return await dbContext.MonthlyPlanDetailInfos.Where(m => m.DeleteFlag != "*" && monthlyPlanDetailIDs.Contains(m.MonthlyPlanDetailID)).ToListAsync();
        }
        /// <summary>
        /// 获取月度计划工作
        /// </summary>
        /// <param name="monthlyPlanDetailID">主键</param>
        /// <returns></returns>
        public async Task<MonthlyPlanDetailInfo> GetMonthlyWork(string monthlyPlanDetailID)
        {
            return await dbContext.MonthlyPlanDetailInfos.Where(m => m.MonthlyPlanDetailID == monthlyPlanDetailID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取某月度计划工作关联字典ID集合
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主键</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        public async Task<int[]> GetMpWorkInterventionIDs(string monthlyPlanMainID, int month)
        {
            if (string.IsNullOrEmpty(monthlyPlanMainID))
            {
                return [];
            }
            return await dbContext.MonthlyPlanDetailInfos.Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.APInterventionID.HasValue && m.DeleteFlag != "*")
                .Select(m => m.APInterventionID.Value).Distinct().ToArrayAsync();
        }
        /// <summary>
        /// 获取月度计划工作关联字典ID集合
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="departmentIDs">病区集合</param>
        /// <returns></returns>
        public async Task<List<APMainView>> GetMonthlyPlanMainViewsByYearAndDepartmentIDs(int year, IEnumerable<int> departmentIDs)
        {
            return await (from a in dbContext.AnnualPlanMainInfos
                          join b in dbContext.MonthlyPlanMainInfos
                          on a.AnnualPlanMainID equals b.AnnualPlanMainID
                          where a.Year == year && departmentIDs.Contains(a.DepartmentID) && a.DeleteFlag != "*" && b.DeleteFlag != "*"
                          select new APMainView
                          {
                              MainID = a.AnnualPlanMainID,
                              DepartmentID = a.DepartmentID,
                              Planner = a.Planner,
                              Year = a.Year,
                              AddDateTime = a.AddDateTime,
                              StatusCode = a.StatusCode,
                              MonthlyPlanMainID = b.MonthlyPlanMainID,
                              Month = b.Month
                          }).ToListAsync();
        }
    }
}
