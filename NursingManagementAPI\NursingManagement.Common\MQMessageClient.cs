﻿//using RabbitMQ.Client;
//using System.Text;

//namespace NursingManagement.Common
//{
//    /// <summary>
//    /// MQ消息客户端
//    /// 使用说明：
//    /// 1. 定义交换机名称（ExchangeName）
//    /// 2. 设置连接工厂 （调用 SetConFactory）
//    /// 3. 初始化客户端 （调用 InitClient）
//    /// 4. 发布消息  （调用 PublishMessage）
//    /// 5. 关闭连接 （调用 Close）
//    /// </summary>
//    public class MQMessageClient
//    {
//        #region -- 构造函数

//        /// <summary>
//        /// 无参构造函数
//        /// </summary>
//        public MQMessageClient() { }

//        /// <summary>
//        /// 有参构造函数
//        /// </summary>
//        /// <param name="_exchangeName">交换机名称</param>
//        public MQMessageClient(string _exchangeName)
//        {
//            ExchangeName = _exchangeName;
//        }
//        #endregion
//        #region -- 公共字段区域
//        /// <summary>
//        /// 交换机名
//        /// </summary>
//        public string ExchangeName { get; set; }
//        /// <summary>
//        /// 连接工厂
//        /// </summary>
//        private ConnectionFactory ConFactory { get; set; }
//        /// <summary>
//        /// 创建频道
//        /// </summary>
//        private IModel Channel { get; set; }
//        /// <summary>
//        /// 连接
//        /// </summary>
//        private IConnection Connection { get; set; }
//        #endregion

//        public void SetConFactory(string userName, string passward, string hostName)
//        {
//            ConFactory = new ConnectionFactory()
//            {
//                UserName = userName,
//                Password = passward,
//                HostName = hostName
//            };
//        }

//        /// <summary>
//        /// 初始化消息客户端
//        /// </summary>
//        public void InitClient()
//        {
//            InitChannel();
//            ExchangeDeclare(ExchangeName);
//        }
//        /// <summary>
//        /// 初始化频道
//        /// </summary>
//        public void InitChannel()
//        {
//            //频道
//            Connection = ConFactory.CreateConnection();
//            //创建频道
//            Channel = Connection.CreateModel();
//        }

//        /// <summary>
//        /// 声明交换机
//        /// </summary>
//        /// <param name="_exchangeName"></param>
//        public void ExchangeDeclare(string _exchangeName)
//        {
//            ExchangeName = _exchangeName;
//            Channel.ExchangeDeclare(_exchangeName, ExchangeType.Direct, false, false, null);
//        }

//        /// <summary>
//        /// 发布消息
//        /// </summary>
//        /// <param name="jsonContent">json字符串的消息内容</param>
//        /// <param name="routeKey"></param>
//        public void PublishMessage(string jsonContent, string routeKey)
//        {
//            var sendBytes = Encoding.UTF8.GetBytes(jsonContent);
//            //发布消息
//            Channel.BasicPublish(ExchangeName, routeKey, null, sendBytes);
//        }

//        /// <summary>
//        /// 发布消息
//        /// </summary>
//        /// <param name="jsonContent"></param>
//        /// <param name="routeKey"></param>
//        public void PublishMessage(byte[] content, string routeKey)
//        {
//            //发布消息
//            Channel.BasicPublish(ExchangeName, routeKey, null, content);
//        }

//        /// <summary>
//        /// 关闭连接
//        /// </summary>
//        /// <returns></returns>
//        public bool Close()
//        {
//            try
//            {
//                Channel.Close();
//                Connection.Close();
//                return true;
//            }
//            catch
//            {
//                return false;
//            }
//        }
//    }
//}
