using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 内容明细字典表
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCObject")]
    public class HierarchicalQCObjectInfo : MutiModifyInfo
    {
        /// <summary>
        /// 质控对象明细表主键
        /// </summary>
        [Key]
        public int HierarchicalQCObjectID { get; set; }
        /// <summary>
        /// 质控主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCRecordID { get; set; }
        /// <summary>
        /// 质控维护记录明细ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCDetailID { get; set; }
        /// <summary>
        /// 1：被考核人员 2:考核人员 3：病区
        /// </summary>
        public int ObjectType { get; set; }
        /// <summary>
        /// 质控值
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string ObjectValue { get; set; }
    }
}