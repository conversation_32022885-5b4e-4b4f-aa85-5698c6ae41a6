﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class ApproveProcess
    {
        /// <summary>
        /// 审批流程唯一码，Guid
        /// </summary>
        public string ApproveProcessID { get; set; }
        /// <summary>
        /// 审批流程名称
        /// </summary>
        public string ProcessName { get; set; }
        /// <summary>
        /// 审批流程描述
        /// </summary>
        public string ProcessDescription { get; set; }
        /// <summary>
        /// 流程当前状态
        /// </summary>
        public ApproveProcessStatusCode StatusCode { get; set; }
        /// <summary>
        /// 申请模板
        /// </summary>
        public string ContentTemplate { get; set; }
        /// <summary>
        /// 适用科室ID
        /// </summary>
        public int[] DepartmentIDs { get; set; }
        /// <summary>
        /// 适用科室名称
        /// </summary>
        public string DepartmentNames { get; set; }
        /// <summary>
        /// 所属审批分类码
        /// </summary>
        public string ProveCategory { get; set; }
        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 新增人员工号
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增人员姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
    }
}
