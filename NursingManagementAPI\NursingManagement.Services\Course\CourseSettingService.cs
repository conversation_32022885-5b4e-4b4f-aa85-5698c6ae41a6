﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.AspNetCore.Http;
using NLog;
using NursingManagement.Data;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 课程字典Service
    /// </summary>
    public class CourseSettingService : ICourseSettingService
    {
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly ICourseSettingRepository _courseSettingRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IFileService _fileService;
        private readonly ICommonFileRepository _commonFileRepository;
        /// <summary>
        /// 培训考核文件标签ID 
        /// </summary>
        private const string TRAINING_EXAMINE_FILE = "55";

        public CourseSettingService(
            ICourseSettingRepository courseSettingRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            ISettingDictionaryRepository settingDictionaryRepository,
            IUnitOfWork unitOfWork,
            IFileService fileService,
            ICommonFileRepository commonFileRepository)
        {
            _courseSettingRepository = courseSettingRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _unitOfWork = unitOfWork;
            _fileService = fileService;
            _commonFileRepository = commonFileRepository;
        }

        /// <summary>
        /// 根据课程类别获取对应的课程字典
        /// </summary>
        /// <param name="courseTypeID"></param>
        /// <returns></returns>
        public async Task<List<CourseSettingView>> GetCourseSettingListAsync(string courseTypeID)
        {
            //获取数据
            var cacheCourseSettings = await _courseSettingRepository.GetCourseSettingsAsync(courseTypeID);
            if (cacheCourseSettings == null && cacheCourseSettings.Count <= 0)
            {
                return null;
            }
            var employeeIDs = cacheCourseSettings.Select(m => m.AddEmployeeID).Union(cacheCourseSettings.Select(m => m.ModifyEmployeeID));
            var cacheEmployeePersonDatas = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            //组装前端需要处理的数据集合
            var topParentCourseSettings = cacheCourseSettings.Where(m => m.ParentID == null).OrderBy(m => m.Year).ToList();
            var childCourseSettings = cacheCourseSettings.Except(topParentCourseSettings).ToList();
            var parasm = new SettingDictionaryParams
            {
                SettingType = "TrainingManagement",
                SettingTypeCode = "TrainingClassification"
            };
            var settingDictionaryLits = await _settingDictionaryRepository.GetSettingDictionary(parasm);
            return await GetCourseSettingViews(cacheEmployeePersonDatas, topParentCourseSettings, childCourseSettings, settingDictionaryLits);
        }
        /// <summary>
        /// 根据课程配置信息，组装前端呈现的数据结构
        /// </summary>
        /// <param name="cacheEmployeePersonDatas"></param>
        /// <param name="topParentCourseSettings"></param>
        /// <param name="childCourseSettings"></param>
        /// <param name="settingDictionaryLits"></param>
        /// <returns></returns>
        private async Task<List<CourseSettingView>> GetCourseSettingViews(Dictionary<string, string> cacheEmployeePersonDatas, List<CourseSettingInfo> topParentCourseSettings, List<CourseSettingInfo> childCourseSettings, List<SettingDictionaryInfo> settingDictionaryLits)
        {

            var courseSettingViews = new List<CourseSettingView>();
            CourseSettingView iterateView = null;
            foreach (var courseSettingItem in topParentCourseSettings)
            {
                //转换子节点Model to View
                var childCourses = childCourseSettings
                    .Where(m => m.ParentID == courseSettingItem.CourseSettingID)
                    .Select(CourseModelTransforView()).OrderBy(m => m.Year).ToList();
                //转换父节点Model to View
                iterateView = CourseModelTransforView()(courseSettingItem);
                iterateView.ChildCourses = childCourses;
                iterateView.FileID = courseSettingItem.FileID != null ? courseSettingItem.FileID.Split(new string[] { "||" }, StringSplitOptions.RemoveEmptyEntries).ToList() : null;
                if (iterateView.FileID != null && iterateView.FileID.Count > 0)
                {
                    iterateView.FileInfoList = await GetCourseFileList(iterateView.FileID);
                }
                courseSettingViews.Add(iterateView);
                //处理子节点
                if (iterateView.ChildCourses != null && iterateView.ChildCourses.Count >= 0)
                {
                    //处理子节点文件
                    await GetDetaileFileListByViews(iterateView.ChildCourses);
                }
            }

            return courseSettingViews;
            // Model 转 View
            Func<CourseSettingInfo, CourseSettingView> CourseModelTransforView()
            {
                return m => new CourseSettingView
                {
                    CourseSettingID = m.CourseSettingID,
                    CourseTypeID = m.CourseTypeID,
                    CourseTypeName = settingDictionaryLits.Find(n => n.SettingValue == m.CourseTypeID)?.Description,
                    CourseName = m.CourseName,
                    CourseIntroduction = m.CourseIntroduction,
                    Year = m.Year,
                    AddEmployeeName = cacheEmployeePersonDatas.TryGetValue(m.AddEmployeeID, out var addName) ? addName : m.AddEmployeeID,
                    ModifyEmployeeName = cacheEmployeePersonDatas.TryGetValue(m.ModifyEmployeeID, out var modifyName) ? addName : m.ModifyEmployeeID,
                    ParentID = m.ParentID,
                    AddDateTime = m.AddDateTime,
                    ModifyDateTime = m.ModifyDateTime,
                    AddDepartmentID = m.AddDepartmentID,
                    Level = m.Level,
                    AddEmployeeID = m.AddEmployeeID,
                    FileID = m.FileID != null ? m.FileID.Split(new string[] { "||" }, StringSplitOptions.RemoveEmptyEntries).ToList() : null,
                };
            }
        }

        /// <summary>
        /// 处理子节点文件
        /// </summary>
        /// <param name="childCourses"></param>
        /// <returns></returns>
        private async Task GetDetaileFileListByViews(List<CourseSettingView> childCourses)
        {
            foreach (var courseSettingView in childCourses)
            {
                if (courseSettingView.FileID == null || courseSettingView.FileID.Count <= 0)
                {
                    continue;
                }
                courseSettingView.FileInfoList = await GetCourseFileList(courseSettingView.FileID);
            }
        }
        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="fileIDList">commonFileInfo表主键ID集合</param>
        /// <returns></returns>
        private async Task<List<FileUploadReturnView>> GetCourseFileList(List<string> fileIDList)
        {
            var datas = new List<FileUploadReturnView>();
            var commonFileInfos = await _commonFileRepository.GetPartFileInfosByIDsAsync(fileIDList);
            var remoteFileIDs = commonFileInfos.Select(m => m.SourceID).ToList();
            var documentMainViews = await _fileService.GetFileAccessInfosFromFileSystemAsync(remoteFileIDs);
            if (documentMainViews == null || documentMainViews.Count < 0)
            {
                return datas;
            }
            foreach (var item in commonFileInfos)
            {
                var documentView = documentMainViews.FirstOrDefault(n => n.DocumentMainID == item.SourceID);
                if (documentView != null)
                {
                    datas.Add(new FileUploadReturnView
                    {
                        Url = documentView.DocumentUrl,
                        FileName = documentView.DocumentTitle,
                        FileID = item.CommonFileID,
                        ExtensionName = documentView.DocumentType
                    });
                }
                else
                {
                    datas.Add(new FileUploadReturnView
                    {
                        Url = "",
                        FileName = item.Content,
                        FileID = item.CommonFileID,
                        ExtensionName = ""
                    });
                }
            }
            return datas;
        }
        /// <summary>
        /// 删除培训课程记录
        /// </summary>
        /// <param name="courseSettingID">课程配置主键ID</param>
        /// <param name="employeeID">执行人员</param>
        /// <returns></returns>
        public async Task<bool> DeleteCourseSettingAsync(string courseSettingID, string employeeID)
        {
            var courseSettingInfo = await _courseSettingRepository.GetCourseSettingAsNoCacheAsync(courseSettingID);
            if (courseSettingInfo == null)
            {
                _logger.Error($"删除失败,已经没有对应的记录CourseSettingID={courseSettingID}");
                return false;
            }
            courseSettingInfo.Delete(employeeID);
            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            if (success)
            {
                await _courseSettingRepository.UpdateCache();
            }
            return success;
        }
        /// <summary>
        /// 保存课程配置信息
        /// </summary>
        /// <param name="courseSettingView"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<bool> SaveCourseSettingAsync(CourseSettingView courseSettingView, string employeeID, string hospitalID, int departmentID)
        {
            var courseSettingInfo = new CourseSettingInfo
            {
                CourseSettingID = courseSettingView.CourseSettingID,
                CourseTypeID = courseSettingView.CourseTypeID,
                CourseName = courseSettingView.CourseName,
                CourseIntroduction = courseSettingView.CourseIntroduction,
                Year = courseSettingView.Year,
                AddEmployeeID = employeeID,
                ModifyEmployeeID = employeeID,
                HospitalID = hospitalID,
                AddDateTime = courseSettingView.AddDateTime,
                ParentID = courseSettingView.ParentID,
                AddDepartmentID = departmentID
            };

            // 处理文件ID
            courseSettingInfo.FileID = await GetFileListAsync(courseSettingView.Files, courseSettingView.FileID, employeeID);

            // 统一设置修改人ID
            courseSettingInfo.Modify(employeeID);

            // 判断是插入还是更新
            if (string.IsNullOrEmpty(courseSettingInfo.CourseSettingID))
            {
                courseSettingInfo.CourseSettingID = courseSettingInfo.GetId();
                courseSettingInfo.Add(employeeID);
                await _unitOfWork.GetRepository<CourseSettingInfo>().InsertAsync(courseSettingInfo);
            }
            else
            {
                _unitOfWork.GetRepository<CourseSettingInfo>().Update(courseSettingInfo);
            }

            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            if (success)
            {
                await _courseSettingRepository.UpdateCache();
            }

            return success;
        }

        /// <summary>
        /// 合并文件ID的逻辑
        /// </summary>
        /// <param name="uploadedFiles">新上传ID</param>
        /// <param name="existingFileIDs">已存在ID</param>
        /// <param name="employeeID">操作人员工号</param>
        /// <returns></returns>
        private async Task<string> GetFileListAsync(List<IFormFile> uploadedFiles, List<string> existingFileIDs, string employeeID)
        {
            var fileIDs = await UpLoadFile(uploadedFiles, employeeID);
            var strList = fileIDs?.ToList() ?? [];

            if (existingFileIDs != null && existingFileIDs.Count > 0)
            {
                strList.AddRange(existingFileIDs);
            }

            return string.Join("||", strList);
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="fileList">文件集合</param>
        /// <param name="employeeID">操作人员工号</param>
        /// <returns></returns>
        private async Task<List<string>> UpLoadFile(List<IFormFile> fileList, string employeeID)
        {
            if (fileList == null || fileList.Count <= 0)
            {
                return null;
            }
            var fileIDList = new List<string>();
            // TODO：等文档管理的文件属性字典表整理好，这里替换为正常的
            var documentDetails = new List<DocumentDetailView>()
            {
                new (){ GroupID = 1, ItemID = 1, Value = "测试" },
            };
            var employeeData = await _employeePersonalDataRepository.GetDataByEmployeeID(employeeID);
            foreach (var file in fileList)
            {
                // 组装保存文档接口参数
                var document = new DocumentView()
                {
                    DocumentMainView = new DocumentMainView()
                    {
                        SourceSystem = "NursingManagement",
                        DocumentTypeID = 1,
                        UserID = employeeID,
                        UserName = employeeData.EmployeeName,
                        DocumentTitle = file.FileName,
                        DocumentType = file.FileName.Substring(file.FileName.LastIndexOf(".") + 1),
                        DocumentStatus = 20
                    },
                    DocumentDetailViews = documentDetails,
                    DocumentTagViews = [new DocumentTagView
                    {
                        DocumentTagListID = TRAINING_EXAMINE_FILE
                    }]
                };
                // 调用文档管理系统的接口保存文件
                var view = await _fileService.UpLoadFile(file, document);
                if (view != null && !string.IsNullOrEmpty(view.FileID))
                {
                    fileIDList.Add(view.FileID);
                }
            }
            return fileIDList;
        }
        /// <summary>
        /// 根据课程名称模糊查询课程信息
        /// </summary>
        /// <param name="courseName"></param>
        /// <param name="courseTypeID"></param>
        /// <returns></returns>
        public async Task<List<CourseSettingView>> SearchCourseSettingAsync(string courseName, string courseTypeID)
        {
            var courseSettingViews = await GetCourseSettingListAsync(courseTypeID);
            if (string.IsNullOrWhiteSpace(courseName))
            {
                return courseSettingViews;
            }
            var resultSettings = new List<CourseSettingView>();
            foreach (var viewItem in courseSettingViews)
            {
                //匹配子节点课程
                var match = MatchCourseByCourseName(viewItem.ChildCourses, courseName);
                if (match || viewItem.CourseName.Contains(courseName))
                {
                    viewItem.ExpandTree = match;
                    resultSettings.Add(viewItem);
                }
            }
            return resultSettings;
        }
        /// <summary>
        /// 判断子节点命中（课程名称模糊匹配）
        /// </summary>
        /// <param name="courseSettingViews"></param>
        /// <param name="courseName"></param>
        /// <returns></returns>
        private bool MatchCourseByCourseName(List<CourseSettingView> courseSettingViews, string courseName)
        {
            if (courseSettingViews == null || courseSettingViews.Count <= 0)
            {
                return false;
            }
            var matchSuccess = false;
            foreach (var viewItem in courseSettingViews)
            {
                if (viewItem.CourseName.Contains(courseName))
                {
                    return true;
                }
                if (viewItem.ChildCourses != null && viewItem.ChildCourses.Count >= 0)
                {
                    viewItem.ExpandTree = MatchCourseByCourseName(viewItem.ChildCourses, courseName);
                    matchSuccess |= viewItem.ExpandTree;
                }
            }
            return matchSuccess;
        }


    }
}
