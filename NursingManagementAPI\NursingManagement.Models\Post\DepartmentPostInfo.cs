﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 部门岗位字典
    /// </summary>
    [Table("DepartmentPost")]
    public class DepartmentPostInfo : MutiModifyInfo
    {
        /// <summary>
        /// 部门岗位表主键
        /// </summary>
        public int DepartmentPostID { get; set; }

        /// <summary>
        /// 岗位序号，主键，PostDescription的主键，如白班责护岗
        /// </summary>
        public int PostID { get; set; }

        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 医院序号，主键
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言序号，主键
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 部门岗位名称
        /// </summary>
        [Column(TypeName = "nvarchar(30)")]
        public string DepartmentPostName { get; set; }

        /// <summary>
        /// 部门岗位名称简写，如：责1
        /// </summary>
        [Column(TypeName = "nvarchar(10)")]
        public string ShortName { get; set; }

        /// <summary>
        /// 状态 0：停用、1：启用
        /// </summary>
        [Column(TypeName ="varchar(50")]
        public string StatusCode { get; set; }

        /// <summary>
        /// 排班时是否参加日统计标记
        /// </summary>
        public bool? DailyStatisticalMark { get; set; }

        /// <summary>
        /// 排班时是否参加月统计标记
        /// </summary>
        public bool? MonthlyStatisticalMark { get; set; }

        /// <summary>
        /// 临床岗标记，同步给CCC，供CCC做岗床配置及排班
        /// </summary>
        public bool? ClinicalFlag { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 考勤天数
        /// </summary>
        public decimal AttendanceDays { get; set; }
        /// <summary>
        /// 岗位班别编码
        /// </summary>
        [Column(TypeName = "varchar(50")]
        public string PostShiftID { get; set; }
        /// <summary>
        /// 前景色
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Color { get; set; }
        /// <summary>
        /// 背景颜色
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string BackGroundColor { get; set; }
        /// <summary>
        /// 半天岗计算考勤方式编码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HalfDayAttendanceCalc { get; set; }
    }
}
