﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ITrainingEvaluationDetailRepository
    {
        /// <summary>
        /// 根据培训评价主表ID获取明细数据
        /// </summary>
        /// <param name="trainingEvaluationMainID"></param>
        /// <returns></returns>
        Task<List<TrainingEvaluationDetailInfo>> GetDetailListByMainID(string trainingEvaluationMainID);
    }
}
