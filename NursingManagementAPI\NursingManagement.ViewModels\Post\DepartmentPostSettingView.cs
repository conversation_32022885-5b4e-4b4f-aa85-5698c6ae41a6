﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class DepartmentPostSettingView : DepartmentPostSettingInfo
    {
        /// <summary>
        /// 全院岗位名称
        /// </summary>
        public string PostName { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyPerson { get; set; }
        /// <summary>
        /// 新增人
        /// </summary>
        public string AddPerson { get; set; }
        /// <summary>
        /// 类型描述
        /// </summary>
        public string TypeDesc { get; set; }

        public DepartmentPostSettingView(DepartmentPostSettingInfo dp)
        {
            CopyParentPropertiesToChild.SynchronizationProperties(dp, this);
        }
    }
}
