﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 注记图示仓储层
    /// </summary>
    public class AdministrationIconRepository : IAdministrationIconRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public AdministrationIconRepository(
            NursingManagementDbContext db,
            IRedisService redisService,
            SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<AdministrationIconInfo>> GetIconData(string moduleType, string[] groupIDs = null)
        {
            var iconList = await GetCacheAsync() as List<AdministrationIconInfo>;
            iconList = iconList.Where(m => m.ModuleType == moduleType).ToList();
            if (groupIDs != null && groupIDs.Length > 0)
            {
                iconList = iconList.Where(m => groupIDs.Contains(m.GroupID)).ToList();
            }
            return iconList;
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AdministrationIconInfos.Where(m => m.HospitalID == hospitalID
                && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.AdministrationIcon.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        /// <summary>
        /// 获取全表数据包含未删除
        /// </summary>
        /// <returns></returns>
        public async Task<List<AdministrationIconInfo>> GetAllIconInfos()
        {
            return await _nursingManagementDbContext.AdministrationIconInfos.ToListAsync();
        }
    }
}
