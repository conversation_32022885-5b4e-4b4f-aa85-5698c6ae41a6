﻿namespace NursingManagement.ViewModels
{
    public class AdjustScheduleRecordView
    {
        /// <summary>
        /// 换班记录ID
        /// </summary>
        public string AdjustScheduleRecordID { get; set; }

        /// <summary>
        /// 部门编码，DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 申请调班日期
        /// </summary>
        public DateTime AdjustDate { get; set; }

        /// <summary>
        /// 申请调整岗位，根据申请人和申请日期自动提取已经排好的值班岗
        /// </summary>
        public string AdjustDepartmentPost { get; set; }

        /// <summary>
        /// 调班目标人员ID
        /// </summary>
        public string TargetEmployeeID { get; set; }

        /// <summary>
        /// 调班目标人员
        /// </summary>
        public string TargetEmployee { get; set; }

        /// <summary>
        /// 调班目标日期
        /// </summary>
        public DateTime TargetDate { get; set; }

        /// <summary>
        /// 目标岗位，根据目标人和目标日期自动提取已经排好的值班岗
        /// </summary>
        public string TargetDepartmentPost { get; set; }

        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 提交审批记录序号
        /// </summary>
        public string ApproveRecordID { get; set; }

        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployee { get; set; }

        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }

        /// <summary>
        /// 申请原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 申请午别
        /// </summary>
        public string AdjustNoonType { get; set; }

        /// <summary>
        /// 申请午别名称
        /// </summary>
        public string AdjustNoonTypeName { get; set; }

        /// <summary>
        /// 换班午别
        /// </summary>
        public string TargetNoonType { get; set; }

        /// <summary>
        /// 换班午别名称
        /// </summary>
        public string TargetNoonTypeName { get; set; }

        /// <summary>
        /// 是否自动派班
        /// </summary>
        public bool AutoSchedule { get; set; }

        /// <summary>
        /// 撤销原因
        /// </summary>
        public string RevokeReason { get; set; }

        /// <summary>
        /// 审批标记
        /// </summary>
        public bool ApproveFlag { get; set; }
    }
}
