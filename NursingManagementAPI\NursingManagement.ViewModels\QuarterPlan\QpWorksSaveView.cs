﻿namespace NursingManagement.ViewModels
{
    public class QpWorksSaveView
    {
        /// <summary>
        /// 年度计划主表ID
        /// </summary>
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 季度计划主表ID
        /// </summary>
        public string QuarterPlanMainID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 工作内容
        /// </summary>
        public TieredPlanWork[] WorkViews { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        public int Annual { get; set; }
        /// <summary>
        /// 工作所属季度
        /// </summary>
        public int Quarter { get; set; }

        /// <summary>
        /// 是否是首次导入
        /// </summary>
        public bool IsFirstImport { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeID { get; set; }
    }
}
