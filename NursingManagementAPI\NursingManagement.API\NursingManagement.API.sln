﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33213.308
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.API", "NursingManagement.API\NursingManagement.API.csproj", "{37A3334B-2CA5-428E-A858-0F216D2B5DEC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.Common", "..\NursingManagement.Common\NursingManagement.Common.csproj", "{1AC173F4-286E-465A-A374-789D191B9F31}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.Data", "..\NursingManagement.Data\NursingManagement.Data.csproj", "{EAB9F6F1-3B6A-4D47-B42A-99C99CA1DB3E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.Data.Interface", "..\NursingManagement.Data.Interface\NursingManagement.Data.Interface.csproj", "{3E2E6154-7FCD-4A5C-B419-E7BC4490F595}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.Models", "..\NursingManagement.Models\NursingManagement.Models.csproj", "{9E6E385E-5504-4001-922F-E4218C97F7E0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.Services", "..\NursingManagement.Services\NursingManagement.Services.csproj", "{FD6D932A-640D-4254-A065-BF76CD79FC7D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.Services.Interface", "..\NursingManagement.Services.Interface\NursingManagement.Services.Interface.csproj", "{DF27EDB3-EA13-4C5D-AE1C-868DF6181D72}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.ViewModels", "..\NursingManagement.ViewModels\NursingManagement.ViewModels.csproj", "{3DB5062D-25DF-40BA-AC5F-9C91BA9804D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NursingManagement.UnitTest", "UnitTest\NursingManagement.UnitTest.csproj", "{EBD802EB-7894-4CB6-BD9D-2B78CEAAF3D8}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{37A3334B-2CA5-428E-A858-0F216D2B5DEC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37A3334B-2CA5-428E-A858-0F216D2B5DEC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37A3334B-2CA5-428E-A858-0F216D2B5DEC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37A3334B-2CA5-428E-A858-0F216D2B5DEC}.Release|Any CPU.Build.0 = Release|Any CPU
		{1AC173F4-286E-465A-A374-789D191B9F31}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1AC173F4-286E-465A-A374-789D191B9F31}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1AC173F4-286E-465A-A374-789D191B9F31}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1AC173F4-286E-465A-A374-789D191B9F31}.Release|Any CPU.Build.0 = Release|Any CPU
		{EAB9F6F1-3B6A-4D47-B42A-99C99CA1DB3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EAB9F6F1-3B6A-4D47-B42A-99C99CA1DB3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EAB9F6F1-3B6A-4D47-B42A-99C99CA1DB3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EAB9F6F1-3B6A-4D47-B42A-99C99CA1DB3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E2E6154-7FCD-4A5C-B419-E7BC4490F595}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E2E6154-7FCD-4A5C-B419-E7BC4490F595}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E2E6154-7FCD-4A5C-B419-E7BC4490F595}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E2E6154-7FCD-4A5C-B419-E7BC4490F595}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E6E385E-5504-4001-922F-E4218C97F7E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E6E385E-5504-4001-922F-E4218C97F7E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E6E385E-5504-4001-922F-E4218C97F7E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E6E385E-5504-4001-922F-E4218C97F7E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD6D932A-640D-4254-A065-BF76CD79FC7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD6D932A-640D-4254-A065-BF76CD79FC7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD6D932A-640D-4254-A065-BF76CD79FC7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD6D932A-640D-4254-A065-BF76CD79FC7D}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF27EDB3-EA13-4C5D-AE1C-868DF6181D72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF27EDB3-EA13-4C5D-AE1C-868DF6181D72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF27EDB3-EA13-4C5D-AE1C-868DF6181D72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF27EDB3-EA13-4C5D-AE1C-868DF6181D72}.Release|Any CPU.Build.0 = Release|Any CPU
		{3DB5062D-25DF-40BA-AC5F-9C91BA9804D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3DB5062D-25DF-40BA-AC5F-9C91BA9804D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3DB5062D-25DF-40BA-AC5F-9C91BA9804D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3DB5062D-25DF-40BA-AC5F-9C91BA9804D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{EBD802EB-7894-4CB6-BD9D-2B78CEAAF3D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBD802EB-7894-4CB6-BD9D-2B78CEAAF3D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBD802EB-7894-4CB6-BD9D-2B78CEAAF3D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBD802EB-7894-4CB6-BD9D-2B78CEAAF3D8}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C7290DB2-27FD-452A-ADCD-0379EF41F3C1}
	EndGlobalSection
EndGlobal
