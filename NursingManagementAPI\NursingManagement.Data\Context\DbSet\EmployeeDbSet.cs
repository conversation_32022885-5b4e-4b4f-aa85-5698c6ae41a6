﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 人员服装尺码
        /// </summary>
        public DbSet<EmployeeClothingSizesInfo> EmployeeClothingSizesInfos { get; set; }

        /// <summary>
        /// 人员联系信息表
        /// </summary>
        public DbSet<EmployeeContactInfo> EmployeeContactInfos { get; set; }

        /// <summary>
        /// 人员教育经历表
        /// </summary>
        public DbSet<EmployeeEducationalExperienceInfo> EmployeeEducationalExperienceInfos { get; set; }

        /// <summary>
        /// 人员任职记录（本公司）
        /// </summary>
        public DbSet<EmployeeEmploymentRecordInfo> EmployeeEmploymentRecordInfos { get; set; }

        /// <summary>
        /// 人员护士层级
        /// </summary>
        public DbSet<EmployeeCapabilityLevelInfo> EmployeeCapabilityLevelInfos { get; set; }

        /// <summary>
        /// 人员个人信息表
        /// </summary>
        public DbSet<EmployeePersonalDataInfo> EmployeePersonalDataInfos { get; set; }

        /// <summary>
        /// 人员职称
        /// </summary>
        public DbSet<EmployeeProfessionalPositionInfo> EmployeeProfessionalPositionInfos { get; set; }

        /// <summary>
        /// 人员家庭信息表
        /// </summary>
        public DbSet<EmployeeRelativesInfo> EmployeeRelativesInfos { get; set; }

        /// <summary>
        /// 人员奖惩
        /// </summary>
        public DbSet<EmployeeRewardInfo> EmployeeRewardInfos { get; set; }

        /// <summary>
        /// 人员技能
        /// </summary>
        public DbSet<EmployeeSkillInfo> EmployeeSkillInfos { get; set; }

        /// <summary>
        /// 人员在职信息表
        /// </summary>
        public DbSet<EmployeeStaffDataInfo> EmployeeStaffDataInfos { get; set; }

        /// <summary>
        /// 人员工作经历（非本公司）
        /// </summary>
        public DbSet<EmployeeWorkExperienceInfo> EmployeeWorkExperienceInfos { get; set; }

        /// <summary>
        /// 人员借调表
        /// </summary>
        public DbSet<EmployeeSecondmentRecordInfo> EmployeeSecondmentRecordInfo { get; set; }

        /// <summary>
        /// 员工带教关系表
        /// </summary>
        public DbSet<EmployeeTeachingRelationInfo> EmployeeTeachingRelationInfos { get; set; }

        /// <summary>
        /// 人员部门变动申请表
        /// </summary>
        public DbSet<EmployeeDepartmentChangeRequestInfo> EmployeeDepartmentChangeRequestInfos { get; set; }

        /// <summary>
        /// 人员多组织架构部门表
        /// </summary>
        public DbSet<EmployeeToDepartmentInfo> EmployeeToDepartmentInfos { get; set; }

        public DbSet<EmployeeStrengthInfo> EmployeeStrengthInfos { get; set; }

        /// <summary>
        /// 用户组聚合根
        /// </summary>
        public DbSet<EmployeeGroupInfo> EmployeeGroupInfos { get; set; }
    }
}
