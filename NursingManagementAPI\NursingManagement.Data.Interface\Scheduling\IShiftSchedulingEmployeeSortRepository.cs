﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IShiftSchedulingEmployeeSortRepository
    {
        /// <summary>
        /// 根据排班顺序获取人员
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingEmployeeSortInfo>> GetEmployeeListByScheduling(string shiftSchedulingRecordID);
    }
}
