﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.0.2" />
    <PackageReference Include="Hangfire" Version="1.8.12" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.UnitOfWork" Version="3.1.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.5" />
    <PackageReference Include="NLog" Version="5.3.2" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.156" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\NursingManagement.Common\NursingManagement.Common.csproj" />
    <ProjectReference Include="..\..\NursingManagement.Data.Interface\NursingManagement.Data.Interface.csproj" />
    <ProjectReference Include="..\..\NursingManagement.Data\NursingManagement.Data.csproj" />
    <ProjectReference Include="..\..\NursingManagement.Services.Interface\NursingManagement.Services.Interface.csproj" />
    <ProjectReference Include="..\..\NursingManagement.Services\NursingManagement.Services.csproj" />
    <ProjectReference Include="..\..\NursingManagement.ViewModels\NursingManagement.ViewModels.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="image\logo.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
