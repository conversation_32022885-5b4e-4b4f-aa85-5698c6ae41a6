﻿using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class APISettingService : IAPISettingService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAPISettingRepository _apiSettingRepository;
        private readonly IOptions<SystemConfig> _config;
        public APISettingService(
            IAPISettingRepository apiSettingRepository
            , IOptions<SystemConfig> config
        )
        {
            _apiSettingRepository = apiSettingRepository;
            _config = config;
        }

        public async Task<ApiSettingView> GetAPIAddressByCode(string settingCode)
        {
            if (string.IsNullOrEmpty(settingCode))
            {
                return null;
            }
            var apiSettingList = await _apiSettingRepository.GetAll<APISettingInfo>();
            var apiSetting = apiSettingList.Where(m => m.SettingCode == settingCode).FirstOrDefault();
            if (apiSetting == null)
            {
                _logger.Error("获取配置失败！settingCode:" + settingCode);
                return null;
            }
            var serverAddress = GeServers(apiSetting.ServerCode, apiSettingList);
            if (string.IsNullOrEmpty(serverAddress))
            {
                _logger.Error("获取服务器配置失败！settingCode:" + apiSetting.ServerCode);
                return null;
            }
            var apiStr = serverAddress + apiSetting.SettingValue;
            var apiUrl = new ApiSettingView
            {
                ApiUrl = apiStr,
                CallType = apiSetting.CallType,
                Description = apiSetting.Description
            };
            return apiUrl;
        }

        /// <summary>
        /// 获取服务器地址
        /// </summary>
        /// <param name="serverCode">服务器码</param>
        /// <returns></returns>
        public async Task<string> GetServerURL(string serverCode)
        {
            var serverType = _config.Value.ServerType == 0 ? 1 : _config.Value.ServerType;
            var apiSettingList = await _apiSettingRepository.GetAll<APISettingInfo>();
            var server = apiSettingList.FirstOrDefault(m => m.SettingType == 1 && m.SettingCode == serverCode);
            return server?.SettingValue;
        }

        /// <summary>
        /// 获取服务器地址,如果找不到，会找settingType=1的服务器
        /// </summary>
        /// <param name="serverCode"> 服务器的编码 SettingCode</param>
        /// <returns></returns>
        private string GeServers(string serverCode, List<APISettingInfo> apiSettingList)
        {
            var serverType = _config.Value.ServerType;
            // 如果取不到，说明appsettings.json文件中没有配置。默认1
            if (serverType == 0)
            {
                serverType = 1;
            }
            var apiSetting = apiSettingList.Where(m => m.SettingType == serverType && m.SettingCode == serverCode).FirstOrDefault();
            //找到服务器地址，返回
            if (apiSetting != null)
            {
                return apiSetting.SettingValue;
            }
            return "";
        }
    }
}
