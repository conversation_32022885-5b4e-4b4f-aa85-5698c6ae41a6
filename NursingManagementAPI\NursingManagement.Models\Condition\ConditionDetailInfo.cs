﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 条件明细表
    /// </summary>
    [Serializable]
    [Table("ConditionDetail")]
    public class ConditionDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 条件明细记录ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ConditionDetailID { get; set; }
        /// <summary>
        /// 条件主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ConditionMainID { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        public int ItemID { get; set; }
        /// <summary>
        /// 条件：EQUALS:等于; NOT_EQUALS:不等于; GREATER_THAN:大于; LESS_THAN:小于;GREATER_THAN_OR_EQUALS:
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Condition { get; set; }
        /// <summary>
        /// 条件表达式
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string ConditionValue { get; set; }
        /// <summary>
        /// 条件类型：and、or
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ConditionType { get; set; }
        /// <summary>
        /// 父ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ParentID { get; set; } = "";
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
