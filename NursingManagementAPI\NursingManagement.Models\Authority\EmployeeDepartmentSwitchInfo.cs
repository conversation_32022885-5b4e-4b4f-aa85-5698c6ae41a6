﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using NursingManagement.Models;

namespace NursingManagement.Data
{

    /// <summary>
    /// 员工部门切换信息实体类
    /// </summary>
    [Table("EmployeeDepartmentSwitch")] 
    public class EmployeeDepartmentSwitchInfo : ModifyInfo
    {
        /// <summary>
        /// 员工部门切换的唯一标识符
        /// </summary>
        [Key]
        [Column("EmployeeDepartmentSwitchID")]
        public int EmployeeDepartmentSwitchID { get; set; }

        /// <summary>
        /// 医院ID
        /// </summary>
        [Column("HospitalID")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 员工的工号，最大长度为20
        /// </summary>
        [Column("EmployeeID", TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 部门的ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门 
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string OrganizationType { get; set; }
    }
}