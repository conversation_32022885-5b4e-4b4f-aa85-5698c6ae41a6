﻿namespace NursingManagement.Services
{
    public class FormExtendFactory
    {
        private readonly FormExtendByHierarchicalQC _getByHierarchicalQCAssessList;

        public FormExtendFactory(
           FormExtendByHierarchicalQC getByHierarchicalQCAssessList
        )
        {
            _getByHierarchicalQCAssessList = getByHierarchicalQCAssessList;
        }

        public FormExtend SwitchCondition(string sourceType)
        {
            FormExtend formExtend = null;

            switch (sourceType)
            {
                // 三级质控评估字典表
                case "HierarchicalQCAssessList":
                    formExtend = _getByHierarchicalQCAssessList;
                    break;
            }
            return formExtend;
        }
    }
}
