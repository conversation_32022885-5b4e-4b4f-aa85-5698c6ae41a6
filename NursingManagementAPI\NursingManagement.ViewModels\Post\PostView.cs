﻿namespace NursingManagement.ViewModels.Post
{
   public class PostView
    {
        /// <summary>
        /// 岗位ID
        /// </summary>
        public int PostID { get; set; }
        /// <summary>
        /// 岗位名称
        /// </summary>
        public string PostName { get; set; }
        /// <summary>
        /// 岗位类型ID
        /// </summary>
        public string PostTypeID { get; set; }
        /// <summary>
        /// 岗位类型ID
        /// </summary>
        public string PostType { get; set; }
        /// <summary>
        /// 岗位性质ID
        /// </summary>
        public string PostNatureID { get; set; }
        /// <summary>
        /// 岗位性质ID
        /// </summary>
        public string PostNature { get; set; }
        /// <summary>
        /// 新增人ID
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增人姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 修改人ID
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }
    }
}
