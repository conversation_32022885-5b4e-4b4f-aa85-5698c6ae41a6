﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 岗位查询仓储|查询删除数据添加.IgnoreQueryFilters() 
    /// </summary>
    public interface IPostRepository : ICacheRepository
    {
        /// <summary>
        /// 根据序号获取岗位信息
        /// </summary>
        /// <param name="postID"></param>
        /// <returns></returns>
        Task<PostInfo> GetByPostID(int postID);
        /// <summary>
        /// 从缓存中获取岗位字典集合
        /// </summary>
        /// <returns>List集合</returns>
        Task<List<PostInfo>> GetByCacheAsync();
        /// <summary>
        /// 根据岗位类型获取岗位
        /// </summary>
        /// <param name="postTypeID"></param>
        /// <returns></returns>
        Task<List<PostInfo>> GetPostList(string postTypeID = null);
        /// <summary>
        /// 根据postID集合获取数据
        /// </summary>
        /// <param name="postIDs"></param>
        /// <returns></returns>
        Task<List<PostInfo>> GetByPostIDs(List<int> postIDs);
        /// <summary>
        /// 根据postID获取数据不走缓存
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <returns></returns>
        Task<PostInfo> GetByPostIDNoCache(int postID);
    }
}