﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        ///质控表单字典表
        /// </summary>
        public DbSet<HierarchicalQCFormInfo> HierarchicalQCFormInfos { get; set; }
        /// <summary>
        /// 质控主题表
        /// </summary>
        public DbSet<HierarchicalQCSubjectInfo> HierarchicalQCSubjectInfos { get; set; }
        /// <summary>
        /// 质控内容明细字典表
        /// </summary>
        public DbSet<HierarchicalQCAssessListInfo> HierarchicalQCAssessListInfos { get; set; }
        /// <summary>
        /// 质控内容备注字典表
        /// </summary>
        public DbSet<HierarchicalQCRemarkInfo> HierarchicalQCRemarkInfos { get; set; }
        /// <summary>
        /// 质控主记录
        /// </summary>
        public DbSet<HierarchicalQCRecordInfo> HierarchicalQCRecordInfos { get; set; }
        /// <summary>
        /// 质控维护记录
        /// </summary>
        public DbSet<HierarchicalQCMainInfo> HierarchicalQCMainInfos { get; set; }
        /// <summary>
        /// 质控明细记录 
        /// </summary>
        public DbSet<HierarchicalQCDetailInfo> HierarchicalQCDetailInfos { get; set; }
        /// <summary>
        /// 质控对象明细记录
        /// </summary>
        public DbSet<HierarchicalQCObjectInfo> HierarchicalQCObjectInfos { get; set; }
        /// <summary>
        ///问题整改表
        /// </summary>
        public DbSet<ProblemRectificationInfo> ProblemRectificationInfos { get; set; }
    }
}
