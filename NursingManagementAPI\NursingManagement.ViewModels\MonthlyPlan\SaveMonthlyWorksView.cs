﻿using static NursingManagement.Models.AnnualPlanEnums;

namespace NursingManagement.ViewModels.MonthlyPlan;

/// <summary>
/// 保存月度计划工作参数
/// </summary>
/// <param name="MonthlyPlanMainID">月度计划主键</param>
/// <param name="WorkViews">待保存月度计划工作列表</param>
public record SaveMonthlyWorksView(string MonthlyPlanMainID, SaveMonthlyWorksView.MonthlyWork[] WorkViews)
{
    /// <summary>
    /// 工作View
    /// </summary>
    /// <param name="Key">工作主键</param>
    /// <param name="TypeID">所属分类</param>
    /// <param name="ApInterventionID">分解目标任务字典ID</param>
    /// <param name="WorkType">工作类型</param>
    /// <param name="Sort">序号</param>
    /// <param name="Requirement">要求</param>
    /// <param name="WorkContent">工作内容</param>
    /// <param name="IsTemp">是否临时新增</param>
    /// <param name="PrincipalIDs">负责人列表</param>
    /// <param name="PrincipalName">负责人姓名</param>
    /// <param name="PrincipalGroupName">负责人组名</param>
    public record MonthlyWork(string Key,
        int TypeID,
        int? ApInterventionID,
        WorkType WorkType,
        int? Sort,
        string Requirement,
        string WorkContent,
        bool IsTemp,
        string[] PrincipalIDs,
        string PrincipalName,
        string PrincipalGroupName
        );
}

