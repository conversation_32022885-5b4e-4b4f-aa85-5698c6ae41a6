﻿﻿namespace NursingManagement.ViewModels.NormalWorkingReminder
{
    /// <summary>
    /// 待提醒问题视图
    /// </summary>
    public class ReminderProblemView
    {
        /// <summary>
        /// 质控维护记录ID
        /// </summary>
        public string HierarchicalQCMainID { get; set; }

        /// <summary>
        /// 质控主记录ID
        /// </summary>
        public string HierarchicalQCRecordID { get; set; }

        /// <summary>
        /// 质控主记录ID
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }

        /// <summary>
        /// 考核日期（发现问题日期）
        /// </summary>
        public DateTime ExamineDate { get; set; }

        /// <summary>
        /// 被质控病区
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 被质控病区名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 考核主题名称
        /// </summary>
        public string SubjectName { get; set; }

        /// <summary>
        /// 质控指导意见
        /// </summary>
        public string Guidance { get; set; }

        /// <summary>
        /// 科室改进内容
        /// </summary>
        public string Improvement { get; set; }

        /// <summary>
        /// 被考核人
        /// </summary>
        public string QcEmployeeName { get; set; }

        /// <summary>
        /// 未整改天数
        /// </summary>
        public int UnrectifiedDays { get; set; }

        /// <summary>
        /// 整改时间（如果已整改）
        /// </summary>
        public DateTime? RectificationDateTime { get; set; }

        /// <summary>
        /// 是否已整改
        /// </summary>
        public bool IsRectified { get; set; }

        /// <summary>
        /// 提醒类型：3天提醒护士长，6天提醒片区主任
        /// </summary>
        public int ReminderType { get; set; }

        /// <summary>
        /// 提醒类型描述
        /// </summary>
        public string ReminderTypeDescription { get; set; }
    }
}
