﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Repository
{
    /// <summary>
    /// 考核预约仓储实现类
    /// </summary>
    public class ExaminationAppointmentRepository : IExaminationAppointmentRepository
    {
        // 注入DbContext
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ExaminationAppointmentRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        #region 接口方法实现

        /// <summary>
        /// 根据条件查询考核预约记录列表
        /// </summary>
        public async Task<List<ExaminationAppointmentInfo>> GetListByConditionAsync(
            Expression<Func<ExaminationAppointmentInfo, bool>> predicate, Expression<Func<ExaminationAppointmentInfo, ExaminationAppointmentInfo>> selectPredicate = null)
        {
            var query = _nursingManagementDbContext.ExaminationAppointmentInfos.Where(m => m.DeleteFlag != "*").IfWhere(predicate != null, predicate);
            if (selectPredicate != null)
            {
                return await query.Select(selectPredicate).ToListAsync();
            }
            return await query.ToListAsync();
        }

        /// <summary>
        /// 根据主键获取单条考核预约记录
        /// </summary>
        public async Task<ExaminationAppointmentInfo> GetByIDAsync(string examinationAppointmentId)
        {
            return await _nursingManagementDbContext.ExaminationAppointmentInfos
                .FirstOrDefaultAsync(e => e.ExaminationAppointmentID == examinationAppointmentId && e.DeleteFlag != "*");
        }

        public async Task<List<ExaminationAppointmentInfo>> GetByExaminerScheduleIDAsync(string examinerScheduleID)
        {
            return await _nursingManagementDbContext.ExaminationAppointmentInfos
                .Where(e => e.ExaminerScheduleID == examinerScheduleID && e.DeleteFlag != "*")
                .ToListAsync();
        }

        #endregion
    }
}
