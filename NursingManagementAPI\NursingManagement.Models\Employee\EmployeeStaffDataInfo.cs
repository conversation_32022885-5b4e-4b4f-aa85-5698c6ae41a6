﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员在职信息表
    /// </summary>
    [Serializable]
    [Table("EmployeeStaffData")]
    public class EmployeeStaffDataInfo : MutiModifyInfo
    {
        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// HIS编号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HisEmployeeID { get; set; }

        /// <summary>
        /// HRP编号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HrpEmployeeID { get; set; }

        /// <summary>
        /// 档案编号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string FileID { get; set; }

        /// <summary>
        /// 工作性质(10长期聘用;20正式;30享正;40临时聘用)
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string JobCategoryCode { get; set; }

        /// <summary>
        /// 员工状态(1、在职、0、离职,2离职申请中)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }

        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? EntryDate { get; set; }

        /// <summary>
        /// 试用期（1、2、3、4、5、6月）
        /// </summary>
        public int? Probation { get; set; }

        /// <summary>
        /// 转正日期
        /// </summary>
        public DateTime? TurnRegularDate { get; set; }

        /// <summary>
        /// 当前部门，护理管理组织架构的DepartmentID
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 当前岗位
        /// </summary>
        [Column(TypeName = "nvarchar(20)")]
        public string PostType { get; set; }

        /// <summary>
        /// 当前职务
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string Title { get; set; }

        /// <summary>
        /// 当前级别
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string EmployeeLevelCode { get; set; }

        /// <summary>
        /// 护理能级ID
        /// </summary>
        public int? CapabilityLevelID { get; set; }

        /// <summary>
        /// 无证护士带教老师
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string TeacherEmployeeID { get; set; }

        /// <summary>
        /// 人员特殊标记。1:表示系统无需显示人员
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string SpecialFlag { get; set; }
        /// <summary>
        /// 离职申请日期
        /// </summary>
        public DateTime? ResignationApplyDate { get; set; }
        /// <summary>
        /// 离职日期
        /// </summary>
        public DateTime? ResignationDate { get; set; }
        /// <summary>
        /// 病案号
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string ChartNo { get; set; }
    }
}
