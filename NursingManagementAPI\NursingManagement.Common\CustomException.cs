﻿namespace NursingManagement.Common
{
    /// <summary>
    /// 自定义错误
    /// </summary>
    public class CustomException : Exception
    {
        public bool WriteLog { get; set; }
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="message"></param>
        /// <param name="writeLog"></param>
        public CustomException(string message, bool writeLog = false) : base(message) { WriteLog = writeLog; }
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="message"></param>
        /// <param name="inner"></param>
        /// <param name="writeLog"></param>
        public CustomException(string message, Exception inner, bool writeLog = false) : base(message, inner) { WriteLog = writeLog; }

        public override string ToString()
        {
            return $"{base.Message} {base.StackTrace}";
        }
    }
}