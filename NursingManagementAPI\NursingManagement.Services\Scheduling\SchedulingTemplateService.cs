﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Data;

namespace NursingManagement.Services
{
    /// <summary>
    /// 排班模板相关业务服务层
    /// </summary>
    public class SchedulingTemplateService : ISchedulingTemplateService
    {
        #region 定义变量
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISchedulingTemplateRecordRepository _schedulingTemplateRecordRepository;
        private readonly ISchedulingTemplateDetailRepository _schedulingTemplateDetailRepository;
        private readonly ISchedulingTemplateDetailMarkRepository _schedulingTemplateDetailMarkRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDictionaryService _dictionaryService;
        #endregion


        public SchedulingTemplateService(
            IUnitOfWork unitOfWork
            , ISchedulingTemplateRecordRepository schedulingTemplateRecordRepository
            , ISchedulingTemplateDetailRepository schedulingTemplateDetailRepository
            , ISchedulingTemplateDetailMarkRepository schedulingTemplateDetailMarkRepository
            , IDepartmentPostRepository departmentPostRepository
            , ISettingDictionaryService settingDictionaryService
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IDictionaryService dictionaryService
        )
        {
            _unitOfWork = unitOfWork;
            _schedulingTemplateRecordRepository = schedulingTemplateRecordRepository;
            _schedulingTemplateDetailRepository = schedulingTemplateDetailRepository;
            _schedulingTemplateDetailMarkRepository = schedulingTemplateDetailMarkRepository;
            _departmentPostRepository = departmentPostRepository;
            _settingDictionaryService = settingDictionaryService;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _dictionaryService = dictionaryService;
        }
        public async Task<List<SchedulingTemplateRecordView>> GetTemplateRecords(int departmentID)
        {
            var schedulingTemplateRecords = await _schedulingTemplateRecordRepository.GetRecordListByDepartmentID(departmentID);
            var employeeIDs = schedulingTemplateRecords.Select(m => m.ModifyEmployeeID).ToList();
            var employeeDict = await _employeePersonalDataRepository.GetEmployOptionViewByEmployeeIDs(employeeIDs);
            return schedulingTemplateRecords.Select(m => new SchedulingTemplateRecordView()
                                            {
                                                SchedulingTemplateRecordID = m.SchedulingTemplateRecordID,
                                                TemplateName = m.TemplateName,
                                                TemplateDescription = m.TemplateDescription,
                                                DepartmentID = m.DepartmentID,
                                                StatusCode = m.StatusCode,
                                                ModifyDateTime = m.ModifyDateTime,
                                                ModifyEmployeeName = employeeDict.Find(n => n.Value.ToString() == m.ModifyEmployeeID)?.Label ?? ""
                                            }).ToList();
        }

        #region 获取排班模板数据

        public async Task<List<ShiftSchedulingMarkView>> GetShiftSchedulingMarks(int departmentID)
        {
            var groups = new string[] { departmentID.ToString(), "999999" };
            var administrationIconList = await _dictionaryService.GetIconsByModuleType("SchedulingMark", groups);
            return administrationIconList.Select(m =>
                new ShiftSchedulingMarkView()
                {
                    MarkID = m.AdministrationIconID,
                    Text = m.Text,
                    Icon = m.Icon,
                    Remark = m.Remark,
                    Color = m.Color,
                    BackGroundColor = m.BackGroundColor,
                    Sort = m.Sort,
                }
            ).ToList();
        }
        public async Task<SchedulingTemplateData> GetTemplateData(string schedulingTemplateRecordID)
        {
            var schedulingTemplateRecord = await _schedulingTemplateRecordRepository.GetRecordByID(schedulingTemplateRecordID);
            if (schedulingTemplateRecord == null)
            {
                return null;
            }
            var columns = new List<TableColumn>();
            for (var columnIndex = 0; columnIndex < schedulingTemplateRecord.ColumnCount; columnIndex++)
            {
                var name = (columnIndex + 1).ToString();
                var column = new TableColumn(columnIndex, name, columnIndex.ToString(), 60, columnIndex, "center")
                {
                    Value = name,
                };
                columns.Add(column);
            }
            // 根据人员清单组装表格行
            var schedulingTemplateTable = await CreateTemplateTable(columns, schedulingTemplateRecord);
            return new SchedulingTemplateData
            {
                RowCount = schedulingTemplateRecord.RowCount,
                ColumnCount = schedulingTemplateRecord.ColumnCount,
                SchedulingTemplateTable = schedulingTemplateTable
            };
        }
        /// <summary>
        /// 组装排班模板表格数据
        /// </summary>
        /// <param name="columns"></param>
        /// <param name="schedulingTemplateRecord"></param>
        /// <returns></returns>
        private async Task<TableView> CreateTemplateTable(List<TableColumn> columns, SchedulingTemplateRecordInfo schedulingTemplateRecord)
        {
            var rows = new List<Dictionary<string, object>>();
            // 获取排班标记
            var shiftSchedulingMarks = await GetShiftSchedulingMarks(schedulingTemplateRecord.DepartmentID);
            // 取午别字典表
            var param = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var noonTypeList = await _settingDictionaryService.GetSettingDictionaryDict(param);
            // 获取部门岗位字典
            var departmentPostList = await _dictionaryService.GetDepartmentPostDict(schedulingTemplateRecord.DepartmentID, true);
            var schedulingTemplateDetails = await _schedulingTemplateDetailRepository.GetDetailByRecordID(schedulingTemplateRecord.SchedulingTemplateRecordID);
            var schedulingTemplateDetailMarks = await _schedulingTemplateDetailMarkRepository.GetMarkByRecordID(schedulingTemplateRecord.SchedulingTemplateRecordID);
            // 第一行是标题，从第二行开始
            for (int rowIndex = 1; rowIndex < schedulingTemplateRecord.RowCount; rowIndex++)
            {
                var row = new Dictionary<string, object>();
                // 组装排班信息
                foreach (var column in columns)
                {
                    var columnIndex = column.Index;
                    // 获取排班明细标记
                    var markList = GetDetailMarkList(schedulingTemplateDetailMarks, shiftSchedulingMarks, rowIndex, columnIndex);
                    var data = new SchedulingTemplateDetailView()
                    {
                        RowIndex = rowIndex,
                        ColumnIndex = columnIndex,
                        MarkList = markList
                    };
                    var templateDetails = schedulingTemplateDetails.Where(m => m.RowIndex == rowIndex && m.ColumnIndex == columnIndex).ToList();
                    data.NoonPost = await GetNoonPost(templateDetails, noonTypeList, departmentPostList, schedulingTemplateRecord.DepartmentID);
                    row.Add(column.Key, data);
                }
                rows.Add(row);
            }
            return new TableView() { Columns = columns, Rows = rows };
        }
        /// <summary>
        /// 获取明细标记列表
        /// </summary>
        /// <param name="schedulingTemplateDetailMarks"></param>
        /// <param name="shiftSchedulingMarks"></param>
        /// <param name="rowIndex"></param>
        /// <param name="columnIndex"></param>
        /// <returns></returns>
        private List<ShiftSchedulingMarkView> GetDetailMarkList(List<SchedulingTemplateDetailMarkInfo> schedulingTemplateDetailMarks, List<ShiftSchedulingMarkView> shiftSchedulingMarks, int rowIndex, int columnIndex)
        {
            var markList = new List<ShiftSchedulingMarkView>();
            // 获取排班标记
            var schedulingTemplateDetailMarkList = schedulingTemplateDetailMarks.Where(m => m.RowIndex == rowIndex && m.ColumnIndex == columnIndex).ToList();
            foreach (var schedulingTemplateDetailMark in schedulingTemplateDetailMarkList)
            {
                var shiftSchedulingMark = shiftSchedulingMarks.Find(m => m.MarkID == schedulingTemplateDetailMark.MarkID);
                if (shiftSchedulingMark == null)
                {
                    continue;
                }
                var newShiftSchedulingMark = CloneData.CloneObj(shiftSchedulingMark);
                newShiftSchedulingMark.MarkValue = schedulingTemplateDetailMark.MarkValue;
                markList.Add(newShiftSchedulingMark);
            }
            return markList.OrderBy(m => m.Sort).ToList();
        }

        /// <summary>
        /// 获取午别对应的排班岗位
        /// </summary>
        /// <param name="schedulingTemplateDetails"></param>
        /// <param name="noonTypeList"></param>
        /// <param name="departmentPostList"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, ShiftSchedulingPost>> GetNoonPost(List<SchedulingTemplateDetailInfo> schedulingTemplateDetails, List<SelectOptionsView> noonTypeList, List<PostSelectOptionsView> departmentPostList, int departmentID)
        {
            var noonPost = new Dictionary<string, ShiftSchedulingPost>();
            if (schedulingTemplateDetails.Count <= 0)
            {
                return noonPost;
            }
            // 组装上午和下午的排班明细
            foreach (var noonType in noonTypeList)
            {
                var shiftSchedulingDetail = schedulingTemplateDetails.Find(m => m.NoonType == noonType.Value.ToString());
                if (shiftSchedulingDetail == null)
                {
                    continue;
                }
                // 获取岗位名称
                var departmentPost = departmentPostList.Find(m => m.Value.ToString() == shiftSchedulingDetail.DepartmentPostID.ToString());
                // 如果找不到岗位，可能该岗位状态关闭或删除了，根据部门岗位ID查找
                departmentPost ??= await _dictionaryService.GetDepartmentPostDictByID(shiftSchedulingDetail.DepartmentPostID, departmentID);
                // 如果根据部门岗位ID查找也查不到 直接跳过
                if (departmentPost == null)
                {
                    continue;
                }
                var departmentPostName = departmentPost?.LocalLabel.ToString();
                var shiftSchedulingPost = new ShiftSchedulingPost()
                {
                    DepartmentPostID = shiftSchedulingDetail.DepartmentPostID,
                    DepartmentPostName = departmentPostName,
                    PostType = departmentPost.Type,
                    AttendanceDays = departmentPost?.AttendanceDays ?? 1,
                    Color = departmentPost.Color,
                    BackGroundColor = departmentPost.BackGroundColor,
                };
                noonPost.Add(noonType.Value.ToString(), shiftSchedulingPost);
            }
            return noonPost;
        }
        #endregion

        #region 保存排班模板
        public async Task<bool> SaveTemplateRecord(SchedulingTemplateView schedulingTemplateView)
        {
            if (schedulingTemplateView == null)
            {
                return false;
            }
            SchedulingTemplateRecordInfo schedulingTemplateRecord = null;
            // 新增
            if (string.IsNullOrWhiteSpace(schedulingTemplateView.SchedulingTemplateRecordID))
            {
                schedulingTemplateRecord = new SchedulingTemplateRecordInfo()
                {
                    DepartmentID = schedulingTemplateView.DepartmentID,
                    HospitalID = schedulingTemplateView.HospitalID,
                    StatusCode = "1"
                };
                schedulingTemplateRecord.SchedulingTemplateRecordID = schedulingTemplateRecord.GetId();
                schedulingTemplateRecord.Add(schedulingTemplateView.EmployeeID);
                await _unitOfWork.GetRepository<SchedulingTemplateRecordInfo>().InsertAsync(schedulingTemplateRecord);
            }
            else
            {
                schedulingTemplateRecord = await _schedulingTemplateRecordRepository.GetRecordByID(schedulingTemplateView.SchedulingTemplateRecordID);
                // 删除旧的明细
                var oldSchedulingDetails = await _schedulingTemplateDetailRepository.GetDetailByRecordID(schedulingTemplateView.SchedulingTemplateRecordID);
                if (oldSchedulingDetails.Count > 0)
                {
                    oldSchedulingDetails.ForEach(detail => detail.Delete(schedulingTemplateView.EmployeeID));
                }
                // 删除旧的mark
                var oldMarkList = await _schedulingTemplateDetailMarkRepository.GetMarkByRecordID(schedulingTemplateView.SchedulingTemplateRecordID);
                if (oldMarkList.Count > 0)
                {
                    oldMarkList.ForEach(mark => mark.Delete(schedulingTemplateView.EmployeeID));
                }
            }
            schedulingTemplateRecord.TemplateName = schedulingTemplateView.TemplateName;
            schedulingTemplateRecord.TemplateDescription = schedulingTemplateView.TemplateDescription;
            schedulingTemplateRecord.RowCount = schedulingTemplateView.RowCount;
            schedulingTemplateRecord.ColumnCount = schedulingTemplateView.ColumnCount;
            schedulingTemplateRecord.Modify(schedulingTemplateView.EmployeeID);
            await SaveSchedulingTemplateDeatil(schedulingTemplateView, schedulingTemplateRecord.SchedulingTemplateRecordID);

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 插入排班模板明细数据
        /// </summary>
        /// <param name="schedulingTemplateView"></param>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <returns></returns>
        private async Task SaveSchedulingTemplateDeatil(SchedulingTemplateView schedulingTemplateView, string schedulingTemplateRecordID)
        {
            if (schedulingTemplateView.SchedulingTemplateDetails?.Count <= 0)
            {
                return;
            }
            foreach (var schedulingTemplateDetail in schedulingTemplateView.SchedulingTemplateDetails)
            {
                // 插入岗位
                if (schedulingTemplateDetail.NoonPost?.Count > 0)
                {
                    foreach (var noonType in schedulingTemplateDetail.NoonPost.Keys)
                    {
                        var schedulingTemplateDetailInfo = new SchedulingTemplateDetailInfo()
                        {
                            SchedulingTemplateRecordID = schedulingTemplateRecordID,
                            DepartmentPostID = schedulingTemplateDetail.NoonPost[noonType].DepartmentPostID,
                            RowIndex = schedulingTemplateDetail.RowIndex,
                            ColumnIndex = schedulingTemplateDetail.ColumnIndex,
                            NoonType = noonType
                        };
                        schedulingTemplateDetailInfo.SchedulingTemplateDetailID = schedulingTemplateDetailInfo.GetId();
                        schedulingTemplateDetailInfo.Add(schedulingTemplateView.EmployeeID);
                        schedulingTemplateDetailInfo.Modify(schedulingTemplateView.EmployeeID);
                        await _unitOfWork.GetRepository<SchedulingTemplateDetailInfo>().InsertAsync(schedulingTemplateDetailInfo);
                    }
                }

                // 插入标记
                if (schedulingTemplateDetail.MarkList?.Count > 0)
                {
                    foreach (var mark in schedulingTemplateDetail.MarkList)
                    {
                        var schedulingTemplateDetailMarkInfo = new SchedulingTemplateDetailMarkInfo()
                        {
                            SchedulingTemplateRecordID = schedulingTemplateRecordID,
                            RowIndex = schedulingTemplateDetail.RowIndex,
                            ColumnIndex = schedulingTemplateDetail.ColumnIndex,
                            MarkID = mark.MarkID,
                            MarkValue = mark.MarkValue
                        };
                        schedulingTemplateDetailMarkInfo.SchedulingTemplateDetailMarkID = schedulingTemplateDetailMarkInfo.GetId();
                        schedulingTemplateDetailMarkInfo.Add(schedulingTemplateView.EmployeeID);
                        schedulingTemplateDetailMarkInfo.Modify(schedulingTemplateView.EmployeeID);
                        await _unitOfWork.GetRepository<SchedulingTemplateDetailMarkInfo>().InsertAsync(schedulingTemplateDetailMarkInfo);
                    }
                }
            }
        }
        #endregion

        public async Task<bool> CopyTemplate(string schedulingTemplateRecordID, string employeeID)
        {
            var template = await _schedulingTemplateRecordRepository.GetRecordByID(schedulingTemplateRecordID);
            // 复制排班模板主表
            var newTemplate = CloneData.CloneObj(template);
            newTemplate.SchedulingTemplateRecordID = newTemplate.GetId();
            newTemplate.TemplateName = $"{template.TemplateName} 副本";
            newTemplate.Add(employeeID);
            newTemplate.Modify(employeeID);
            await _unitOfWork.GetRepository<SchedulingTemplateRecordInfo>().InsertAsync(newTemplate);
            // 复制排班模板明细表
            var schedulingTemplateDetails = await _schedulingTemplateDetailRepository.GetDetailByRecordID(schedulingTemplateRecordID);
            if (schedulingTemplateDetails?.Count > 0)
            {
                foreach (var schedulingTemplateDetail in schedulingTemplateDetails)
                {
                    var newSchedulingTemplateDetail = CloneData.CloneObj(schedulingTemplateDetail);
                    newSchedulingTemplateDetail.SchedulingTemplateDetailID = newSchedulingTemplateDetail.GetId();
                    newSchedulingTemplateDetail.SchedulingTemplateRecordID = newTemplate.SchedulingTemplateRecordID;
                    newSchedulingTemplateDetail.Add(employeeID);
                    newSchedulingTemplateDetail.Modify(employeeID);
                    await _unitOfWork.GetRepository<SchedulingTemplateDetailInfo>().InsertAsync(newSchedulingTemplateDetail);
                }
            }
            // 复制排班模板明细标记表
            var schedulingTemplateDetailMarks = await _schedulingTemplateDetailMarkRepository.GetMarkByRecordID(schedulingTemplateRecordID);
            if (schedulingTemplateDetailMarks?.Count > 0)
            {
                foreach (var schedulingTemplateDetailMark in schedulingTemplateDetailMarks)
                {
                    var newSchedulingTemplateDetailMark = CloneData.CloneObj(schedulingTemplateDetailMark);
                    newSchedulingTemplateDetailMark.SchedulingTemplateDetailMarkID = newSchedulingTemplateDetailMark.GetId();
                    newSchedulingTemplateDetailMark.SchedulingTemplateRecordID = newTemplate.SchedulingTemplateRecordID;
                    newSchedulingTemplateDetailMark.Add(employeeID);
                    newSchedulingTemplateDetailMark.Modify(employeeID);
                    await _unitOfWork.GetRepository<SchedulingTemplateDetailMarkInfo>().InsertAsync(newSchedulingTemplateDetailMark);
                }
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        public async Task<bool> DeleteTemplate(string schedulingTemplateRecordID, string employeeID)
        {
            var template = await _schedulingTemplateRecordRepository.GetRecordByID(schedulingTemplateRecordID);
            template.Delete(employeeID);
            // 明细处理
            var templateDetails = await _schedulingTemplateDetailRepository.GetDetailByRecordID(schedulingTemplateRecordID);
            if (templateDetails?.Count > 0)
            {
                templateDetails.ForEach(m => m.Delete(employeeID));
            }
            var templateDetailMarks = await _schedulingTemplateDetailMarkRepository.GetMarkByRecordID(schedulingTemplateRecordID);
            if (templateDetailMarks?.Count > 0)
            {
                templateDetailMarks.ForEach(m => m.Delete(employeeID));
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        public async Task<bool> UpdateTemplateStatus(string schedulingTemplateRecordID, string statusCode, string employeeID)
        {
            var template = await _schedulingTemplateRecordRepository.GetRecordByID(schedulingTemplateRecordID);
            template.StatusCode = statusCode;
            template.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
    }
}
