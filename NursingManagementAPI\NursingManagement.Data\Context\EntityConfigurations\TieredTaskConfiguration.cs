﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NursingManagement.Models;

namespace NursingManagement.Data.Context.EntityConfigurations
{
    /// <summary>
    /// 主表字段映射
    /// </summary>
    public class TieredTaskMainConfiguration : IEntityTypeConfiguration<AnnualScheduleMainInfo>
    {
        public void Configure(EntityTypeBuilder<AnnualScheduleMainInfo> builder)
        {
            builder.ToTable("AnnualScheduleMain");
            builder.HasKey(m => m.AnnualScheduleMainID);
            builder.Property(m => m.AnnualScheduleMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.HospitalID).HasColumnType("varchar(20)").HasMaxLength(20);
            builder.Property(m => m.SchedulePerformer).HasColumnType("varchar(40)").HasMaxLength(40);
            builder.Property(m => m.Performer).HasColumnType("varchar(20)").HasMaxLength(20);
            builder.Property(m => m.PerformComment).HasColumnType("nvarchar(1000)").HasMaxLength(1000);
            builder.Property(m => m.DelayContent).HasColumnType("nvarchar(1000)").HasMaxLength(1000);
            builder.Property(m => m.Content).HasColumnType("nvarchar(800)").HasMaxLength(800);
            // 一个Task对应多个WorkToTask，一个WorkToTask对应一个Task
            builder.HasMany(m => m.MonthlyWorkToTasks).WithOne(m => m.AnnualScheduleMainInfo)
                .HasForeignKey(m => m.ApScheduleMainID);
        }
    }

    /// <summary>
    /// 明细表字段映射
    /// </summary>
    public class TieredTaskDetailConfiguration : IEntityTypeConfiguration<AnnualScheduleDetailInfo>
    {
        public void Configure(EntityTypeBuilder<AnnualScheduleDetailInfo> builder)
        {
            builder.ToTable("AnnualScheduleDetail");
            builder.HasKey(m => m.AnnualScheduleMainID);
            builder.Property(m => m.AnnualScheduleDetailID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.AnnualScheduleMainID).HasColumnType("varchar(32)").HasMaxLength(32);
        }
    }

    /// <summary>
    /// 关系表字段映射
    /// </summary>
    public class ApScheduleToMonthlyWorksConfiguration : IEntityTypeConfiguration<MonthlyWorkToTaskInfo>
    {
        public void Configure(EntityTypeBuilder<MonthlyWorkToTaskInfo> builder)
        {
            builder.ToTable("ApScheduleToMonthlyWorks");
            builder.HasKey(m => new { m.MonthlyPlanDetailID, m.ApScheduleMainID });
            builder.Property(m => m.MonthlyPlanDetailID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.ApScheduleMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.WorkContent).HasColumnType("nvarchar(400)").HasMaxLength(400);
            builder.Property(m => m.Requirement).HasColumnType("nvarchar(400)").HasMaxLength(400);
        }
    }
}
