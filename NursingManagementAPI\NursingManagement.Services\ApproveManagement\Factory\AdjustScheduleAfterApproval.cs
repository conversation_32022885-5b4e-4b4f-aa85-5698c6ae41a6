﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    public class AdjustScheduleAfterApproval(
        IUnitOfWork _unitOfWork
        , IAdjustScheduleRecordRepository _adjustScheduleRecordRepository
        , IShiftSchedulingRecordRepository _shiftSchedulingRecordRepository
        , IShiftSchedulingDetailRepository _shiftSchedulingDetailRepository
        , IShiftSchedulingService _shiftSchedulingService
        , IRouterListRepository _routerListRepository
        , ISettingDictionaryRepository _settingDictionaryRepository
        ) : ICommonProcessingAfterApproval
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public override async Task<(string, int)[]> ProcessAfterApprovalAsync(ProcessAfterApprovalView view)
        {
            var adjustScheduleRecord = await _adjustScheduleRecordRepository.GetRecordByIDAsync(view.SourceID);
            if (adjustScheduleRecord == null)
            {
                _logger.Error("未找到审批记录来源，回写审批结果失败");
                return null;
            }
            if (adjustScheduleRecord.StatusCode != view.ApprovalResult)
            {
                //当审批状态改变的时候 回写状态
                adjustScheduleRecord.StatusCode = view.ApprovalResult;
            }
            if (!adjustScheduleRecord.AutoScheduleFlag.HasValue || !adjustScheduleRecord.AutoScheduleFlag.Value)
            {
                _logger.Info("自动排班开关未打开，不调整排班");
                return null;
            }
            //审批通过，更新排班信息
            if (view.ApprovalResult == ApprovalStatus.Completed.ToString("d"))
            {
                await UpdateShiftScheduling(adjustScheduleRecord);
            }
            //审批撤销,取消原本调班
            if (view.ApprovalResult == ApprovalStatus.Revoke.ToString("d"))
            {
                await RevokeShiftSchedulingByAdjustRecord(adjustScheduleRecord);
            }
            if (await _unitOfWork.SaveChangesAsync() <= 0)
            {
                throw new Exception("更新排班信息失败，请联系管理员。");
            }
            var settingParsms = new SettingDictionaryParams
            { 
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ProveCategoryToRoute",
                SettingTypeValue = "AA-020"
            };
            var settingList =await _settingDictionaryRepository.GetSettingDictionary(settingParsms);
            var setting = settingList.FirstOrDefault();
            if (setting == null || !int.TryParse(setting.SettingValue, out var routerListID))
            {
                _logger.Error($"发送完成审批通知失败，找不到SettingType=ProveCategoryToRoute, SettingTypeCode=AA-020的配置");
                return null;
            }
            var routers = await _routerListRepository.GetInfosByRouterListID(routerListID);
            // 获取跳转 path，PC端、移动端
            var paths = routers.Select(r => (r.Path, r.ClientType)).ToArray();
            return paths;
        }
        /// <summary>
        /// 撤销原本调班记录
        /// </summary>
        /// <param name="recordInfo">调班对应的主记录</param>
        /// <returns></returns>
        private async Task RevokeShiftSchedulingByAdjustRecord(AdjustScheduleRecordInfo recordInfo)
        {
            var targetDetails = await _shiftSchedulingService.GetShiftSchedulingDetailsByEmployeeID(recordInfo.DepartmentID, recordInfo.AdjustDate, recordInfo.AddEmployeeID);
            var adjustDetails = await _shiftSchedulingService.GetShiftSchedulingDetailsByEmployeeID(recordInfo.DepartmentID, recordInfo.TargetDate, recordInfo.TargetEmployeeID);
            if (targetDetails == null || adjustDetails == null)
            {
                _logger.Error($"取消调班申请时，获取调班记录失败，adjustRecord={ListToJson.ToJson(recordInfo)}");
                return;
            }
            // 筛选出已经被调班的记录
            targetDetails = targetDetails.Where(m => m.AdjustScheduleRecordID == recordInfo.AdjustScheduleRecordID).ToList();
            adjustDetails = adjustDetails.Where(m => m.AdjustScheduleRecordID == recordInfo.AdjustScheduleRecordID).ToList();
            if (targetDetails.Count <= 0 || adjustDetails.Count <= 0)
            {
                _logger.Error($"取消调班申请时，获取已经调班的前后记录失败，adjustRecord={ListToJson.ToJson(recordInfo)}");
                return;
            }
            //还原调班记录
            UpdateShiftSchedulingDetail(recordInfo, targetDetails, adjustDetails);
        }

        /// <summary>
        /// 审批通过后更新派班
        /// </summary>
        /// <param name="adjustScheduleRecord">调班记录</param>
        /// <returns></returns>
        private async Task UpdateShiftScheduling(AdjustScheduleRecordInfo adjustScheduleRecord)
        {
            List<ShiftSchedulingDetailInfo> adjustSchedulingDetails = null;
            List<ShiftSchedulingDetailInfo> targetSchedulingDetails = null;
            // 获取所在月的开始时间和结束时间
            var startTime = DateHelper.GetFirstDayOfMonth(adjustScheduleRecord.AdjustDate);
            var endTime = DateHelper.GetLastDayOfMonth(adjustScheduleRecord.AdjustDate);
            var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(adjustScheduleRecord.DepartmentID, startTime, endTime);
            if (shiftSchedulingRecord == null)
            {
                _logger.Error($"未找到部门ID：{adjustScheduleRecord.DepartmentID}的ShiftSchedulingRecord，开始时间：{startTime}，结束时间：{endTime}");
                return;
            }
            var shiftSchedulingDetailList = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startTime, endTime);
            if (shiftSchedulingDetailList.Count <= 0)
            {
                _logger.Error($"未找到ShiftSchedulingRecordID：{adjustScheduleRecord.DepartmentID}的ShiftSchedulingDetail排班信息，开始时间：{startTime}，结束时间：{endTime}");
                return;
            }
            adjustSchedulingDetails = shiftSchedulingDetailList.Where(m => m.SchedulingDate.Date == adjustScheduleRecord.AdjustDate.Date && m.EmployeeID == adjustScheduleRecord.AddEmployeeID).ToList();
            // 如果是月份相同，直接取目标排班明细
            if (adjustScheduleRecord.AdjustDate.Month == adjustScheduleRecord.TargetDate.Month)
            {
                targetSchedulingDetails = shiftSchedulingDetailList.Where(m => m.SchedulingDate.Date == adjustScheduleRecord.TargetDate.Date && m.EmployeeID == adjustScheduleRecord.TargetEmployeeID).ToList();
            }
            else
            {
                // 非同月，依据目标时间获取目标排班明细
                targetSchedulingDetails = await _shiftSchedulingService.GetShiftSchedulingDetailsByEmployeeID(adjustScheduleRecord.DepartmentID, adjustScheduleRecord.TargetDate, adjustScheduleRecord.TargetEmployeeID);
            }
            if (adjustSchedulingDetails.Count <= 0 || targetSchedulingDetails.Count <= 0)
            {
                _logger.Error($"未找到对应时间的排班信息，申请人ID：{adjustScheduleRecord.AddEmployeeID}，调整日期：{adjustScheduleRecord.AdjustDate.Date:yyyy-MM-dd}，" +
                    $"调班人：{adjustScheduleRecord.TargetEmployeeID}，调班日期：{adjustScheduleRecord.TargetDate.Date:yyyy-MM-dd}");
                return;
            }
            UpdateShiftSchedulingDetail(adjustScheduleRecord, adjustSchedulingDetails, targetSchedulingDetails);
            return;
        }
        /// <summary>
        /// 调换排班数据
        /// </summary>
        /// <param name="adjustScheduleRecord">调班申请记录</param>
        /// <param name="adjustSchedulingDetails">申请日期排班数据</param>
        /// <param name="targetSchedulingDetails">调班日期排班数据</param>
        private static void UpdateShiftSchedulingDetail(AdjustScheduleRecordInfo adjustScheduleRecord, List<ShiftSchedulingDetailInfo> adjustSchedulingDetails, List<ShiftSchedulingDetailInfo> targetSchedulingDetails)
        {
            // 排班互换
            var adjustEmployeeID = adjustSchedulingDetails[0].EmployeeID;
            //交换RecordID，处理跨月调班情况
            var adjustSchedulingRecordID = adjustSchedulingDetails[0].ShiftSchedulingRecordID;
            // 判断排班午别
            if (string.IsNullOrEmpty(adjustScheduleRecord.AdjustNoonType) || string.IsNullOrEmpty(adjustScheduleRecord.TargetNoonType))
            {
                adjustSchedulingDetails.ForEach(adjustSchedulingDetail =>
                {
                    adjustSchedulingDetail.ShiftSchedulingRecordID = targetSchedulingDetails[0].ShiftSchedulingRecordID;
                    adjustSchedulingDetail.EmployeeID = targetSchedulingDetails[0].EmployeeID;
                    adjustSchedulingDetail.SchedulingDate = adjustScheduleRecord.TargetDate;
                    adjustSchedulingDetail.AdjustScheduleRecordID = adjustScheduleRecord.AdjustScheduleRecordID;
                    adjustSchedulingDetail.Modify(adjustScheduleRecord.AddEmployeeID);
                });
                targetSchedulingDetails.ForEach(targetSchedulingDetail =>
                {
                    targetSchedulingDetail.ShiftSchedulingRecordID = adjustSchedulingRecordID;
                    targetSchedulingDetail.EmployeeID = adjustEmployeeID;
                    targetSchedulingDetail.SchedulingDate = adjustScheduleRecord.AdjustDate;
                    targetSchedulingDetail.AdjustScheduleRecordID = adjustScheduleRecord.AdjustScheduleRecordID;
                    targetSchedulingDetail.Modify(adjustScheduleRecord.AddEmployeeID);
                });
                return;
            }
            //排班有午别，对应得的午别替换
            var adjustSchedulingDetail = adjustSchedulingDetails.FirstOrDefault(m => m.NoonType == adjustScheduleRecord.AdjustNoonType);
            if (adjustSchedulingDetail != null)
            {
                adjustSchedulingDetail.ShiftSchedulingRecordID = targetSchedulingDetails[0].ShiftSchedulingRecordID;
                adjustSchedulingDetail.EmployeeID = targetSchedulingDetails[0].EmployeeID;
                adjustSchedulingDetail.SchedulingDate = adjustScheduleRecord.TargetDate;
                adjustSchedulingDetail.AdjustScheduleRecordID = adjustScheduleRecord.AdjustScheduleRecordID;
                adjustSchedulingDetail.NoonType = adjustScheduleRecord.TargetNoonType;
                adjustSchedulingDetail.Modify(adjustScheduleRecord.AddEmployeeID);
            }
            var targetSchedulingDetail = targetSchedulingDetails.FirstOrDefault(m => m.NoonType == adjustScheduleRecord.TargetNoonType);
            if (targetSchedulingDetail != null)
            {
                targetSchedulingDetail.ShiftSchedulingRecordID = adjustSchedulingRecordID;
                targetSchedulingDetail.EmployeeID = adjustEmployeeID;
                targetSchedulingDetail.SchedulingDate = adjustScheduleRecord.AdjustDate;
                targetSchedulingDetail.AdjustScheduleRecordID = adjustScheduleRecord.AdjustScheduleRecordID;
                targetSchedulingDetail.NoonType = adjustScheduleRecord.AdjustNoonType;
                targetSchedulingDetail.Modify(adjustScheduleRecord.AddEmployeeID);
            }
            return;
        }

    }
}
