﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 注记图示表
    /// </summary>
    [Table("AdministrationIcon")]
    [Serializable]
    public class AdministrationIconInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int AdministrationIconID { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 标识来源表
        /// </summary>
        [Column(TypeName = "varchar(40)")]
        public string SourceTable { get; set; }
        /// <summary>
        /// 标识来源表的编号
        /// </summary>
        public int? SourceID { get; set; }
        /// <summary>
        /// 标识类型（如：人员标识、年度计划、文档管理）
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ModuleType { get; set; }
        /// <summary>
        /// 群组号
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string GroupID { get; set; }
        /// <summary>
        /// 标记图标
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Icon { get; set; }
        /// <summary>
        /// 标记文本
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Text { get; set; }
        /// <summary>
        /// 标识说明
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Remark { get; set; }
        /// <summary>
        /// 前景色
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Color { get; set; }
        /// <summary>
        /// 背景颜色
        /// </summary>
        [Column(TypeName = "varchar(11)")]
        public string BackGroundColor { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// API名称
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string API { get; set; }
    }
}
