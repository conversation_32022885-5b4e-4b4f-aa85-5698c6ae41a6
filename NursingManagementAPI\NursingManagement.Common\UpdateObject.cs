﻿using System.Linq.Expressions;

namespace NursingManagement.Common
{
    /// <summary>
    /// 遍历T1中的所有属性与T2中同名、同类型属性进行比较，如果不同则更新T1中的属性值
    /// </summary>
    /// <typeparam name="T1">待更新对象</typeparam>
    /// <typeparam name="T2">新数据来源</typeparam>
    public static class ObjUpdater<T1, T2>
    {
        private static readonly Func<T1, T2, bool> cache = GetFunc();

        /// <summary>
        /// target的属性值
        /// </summary>
        /// <param name="target">待更新对象</param>
        /// <param name="source">新数据来源</param>
        public static bool UpdateWithSource(T1 target, T2 source)
        {
            return cache(target, source);
        }

        /// <summary>
        /// 表达式树构建方法
        /// </summary>
        /// <returns></returns>
        private static Func<T1, T2, bool> GetFunc()
        {
            // TODO：反射获取属性对象需要进行缓存优化
            var t1Type = typeof(T1);
            var t1Properties = t1Type.GetProperties();
            var t2Properties = typeof(T2).GetProperties();
            var t2Type = typeof(T2);
            var paramM = Expression.Parameter(t1Type, "m");
            var paramN = Expression.Parameter(t2Type, "n");
            var conditionExpList = new List<ConditionalExpression>();

            var isUpdate = Expression.Variable(typeof(bool), "isUpdate");

            foreach (var propertyName in t1Properties.Select(m => m.Name))
            {
                if (propertyName == "HospitalID" || propertyName == "Language")
                {
                    continue;
                }
                var t1Prop = t1Properties.FirstOrDefault(m => m.Name == propertyName);
                var t2Prop = t2Properties.FirstOrDefault(m => m.Name == propertyName);
                if (t1Prop is null || t2Prop is null)
                {
                    continue;
                }
                if (t1Prop?.PropertyType != t2Prop?.PropertyType)
                {
                    throw new Exception($"【更新器更新异常】T1与T2的{propertyName}属性类型不一致");
                }
                var t1CannotWrite = !t1Prop?.CanWrite ?? true;
                var t2CannotRead = !t2Prop?.CanRead ?? true;
                // t1需要可写，t2需要可读
                if (t1CannotWrite || t2CannotRead)
                {
                    continue;
                }
                // m.prop
                var left = Expression.Property(paramM, t1Prop);
                var right = Expression.Property(paramN, t2Prop);

                var notEqual = Expression.NotEqual(left, right);

                // m.prop = n.prop
                var updatePropExp = Expression.Assign(left, right);
                // isUpdate = true
                var updateFlagExp = Expression.Assign(isUpdate, Expression.Constant(true));
                var ifTrueExp = Expression.Block(updatePropExp, updateFlagExp);

                var ifThen = Expression.IfThen(notEqual, ifTrueExp);

                conditionExpList.Add(ifThen);
            }
            var updateActions = Expression.Block(conditionExpList);
            var block = Expression.Block(new[] { isUpdate }, updateActions, isUpdate);
            var lambda = Expression.Lambda<Func<T1, T2, bool>>(block, paramM, paramN);

            return lambda.Compile();
        }


    }
}
