﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 审批明细表仓储接口
    /// </summary>
    public interface IApproveDetailRepository
    {
        /// <summary>
        /// 根据业务主表ID集合获取多个业务的审批明细记录
        /// </summary>
        /// <param name="approveMainIDs"></param>
        /// <returns></returns>
        Task<List<ApproveDetailInfo>> GetApproveDetailsByMainIDAsync(params string[] approveMainIDs);
        /// <summary>
        /// 根据主键获取明细数据
        /// </summary>
        /// <param name="detailID"></param>
        /// <returns></returns>
        Task<ApproveDetailInfo> GetApproveDetailByDetailIDAsync(string detailID);
        /// <summary>
        /// 根据主表ID判断单签审批节点是否存在没有被审批的明细（主要用于会签的逻辑判断）
        /// </summary>
        /// <param name="approveMainID"></param>
        /// <returns></returns>
        Task<bool> CheckUnApproveDetailsExistOrNotByMainIDAsync(string approveMainID);

        /// <summary>
        /// 获取多个节点中 完成的审批明细
        /// </summary>
        /// <param name="approveMainIDs"></param>
        /// <returns></returns>
        Task<List<ApproveDetailInfo>> GetCompletedDetailViewsByMainIDAsync(params string[] approveMainIDs);
        /// <summary>
        /// 获取下一个节点的所有可能审批人
        /// </summary>
        /// <param name="approveMainID"></param>
        /// <returns></returns>
        Task<List<string>> GetPreApproveEmployeeIDAsNoTrackAsync(string approveMainID);

        /// <summary>
        /// 获取整个审批流程中人员未完成审批的记录ID
        /// </summary>
        /// <param name="approveMainIDs">审批维护记录ID</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        Task<List<string>> GetNotApproveMainIDsAsync(List<string> approveMainIDs, string employeeID);

        /// <summary>
        /// 获取整个审批流程中已经完成节点审批的所有审批人员工号
        /// </summary>
        /// <param name="approveMainIDs"></param>
        /// <returns></returns>
        Task<List<string>> GetApproveEmployeeIDAsync(List<string> approveMainIDs);
        /// <summary>
        /// 根据业务主表ID集合获取多个业务的审批明细记录(部分字段)
        /// </summary>
        /// <param name="approveMainIDs">审批节点记录ID集合</param>
        /// <returns>ApproveMainID、StatusCode、PreApproveEmployeeID</returns>
        Task<List<ApproveDetailInfo>> GetPartDetailsByMainIDAsync(params string[] approveMainIDs);
        /// <summary>
        /// 根据审批记录获取审批人
        /// </summary>
        /// <param name="approveRecordIDList"></param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetApproveEmployeeIDByRecordIDsAsync(List<string> approveRecordIDList);
    }
}
