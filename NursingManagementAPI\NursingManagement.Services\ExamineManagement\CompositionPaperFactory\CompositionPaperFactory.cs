﻿using NLog;
using NursingManagement.Models;
using NursingManagement.Services.Examine;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services
{
    /// <summary>
    /// 组卷工厂
    /// </summary>
    public class CompositionPaperFactory
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly GetPaperDetailByChoiceQuestion _getPaperDetailByChoiceQuestion;
        private readonly GetPaperDetailByType _getPaperDetailByType;

        public CompositionPaperFactory(
            GetPaperDetailByChoiceQuestion getPaperDetailByChoiceQuestion,
            GetPaperDetailByType getPaperDetailByType)
        {
            _getPaperDetailByChoiceQuestion = getPaperDetailByChoiceQuestion;
            _getPaperDetailByType = getPaperDetailByType;
        }

        /// <summary>
        /// 根据组卷规则工厂产生试卷明细数据
        /// </summary>
        /// <param name="examinationQuestionList">题库中题目</param>
        /// <param name="paperCompositionRuleView"></param>
        /// <param name="examinationPaperMainData"></param>
        /// <returns></returns>
        public async Task<List<PaperQuestionView>> GetCompositionPaperAPI(List<ExaminationQuestionInfo> examinationQuestionList, PaperCompositionRuleView paperCompositionRuleView, ExaminationPaperMainInfo examinationPaperMainData)
        {
            // 计算组卷参数
            CalcCompositePaperCondition(paperCompositionRuleView);
            var returnPaperQuestionList = new List<PaperQuestionView>();
            var questionTypes = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.QustionTypes;
            var newQuestionTypes = questionTypes.ToArray().Prepend(ExamineConstant.RULE_CODE_CHOICE_QUESTION);
            foreach (var questionTypeItem in newQuestionTypes)
            {
                //切换API方法
                var factory = SwitchFactoryByAPI(questionTypeItem);
                if (factory == null)
                {
                    _logger.Warn("组卷配置没有匹配到对应的工厂，SettingTypeValue='ExaminationQuestionType' and SettingValue =" + questionTypeItem);
                    continue;
                }
                var paperQuestionList = await factory.FilterExaminationQuestion(questionTypeItem, examinationQuestionList, paperCompositionRuleView, returnPaperQuestionList, examinationPaperMainData, paperCompositionRuleView.ModifyEmployeeID);
                if (paperQuestionList == null || paperQuestionList.Count <= 0)
                {
                    _logger.Info("没有筛选到题目");
                    continue;
                }
                returnPaperQuestionList.AddRange(paperQuestionList);
            }
            return SetSortByQuestionType(returnPaperQuestionList);
        }

        /// <summary>
        /// 计算组卷时使用的数据
        /// </summary>
        /// <param name="paperCompositionRuleView"></param>
        private static void CalcCompositePaperCondition(PaperCompositionRuleView paperCompositionRuleView)
        {
            var returnDynamicFormDetailList = new List<DynamicFormDetailInfo>();
            var questionFilterConditionRules = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.QuestionFilterConditionRules;
            // 分题库处理必选题
            var paperQuestionCount = 0;
            foreach (var questionFilterRuleItem in questionFilterConditionRules)
            {
                var questionFilters = questionFilterRuleItem.QuestionFilters;
                // 题型分类
                var selectBankQuestionCount = 0;
                foreach (var questionFilterItem in questionFilters)
                {
                    // 题目数量
                    var questionCountRule = questionFilterItem.RuleKeyValues.Find(m => m.RuleCode == $"{questionFilterItem.QuestionType}Count");
                    if (questionCountRule != null)
                    {
                        selectBankQuestionCount += questionCountRule.Value;
                        questionFilterItem.QuestionCount = questionCountRule.Value;
                        questionFilterItem.DynamicFilterCount = questionCountRule.Value;
                    }
                    // 题目分数
                    var questionScoreRule = questionFilterItem.RuleKeyValues.Find(m => m.RuleCode == $"{questionFilterItem.QuestionType}Score");
                    if (questionScoreRule != null)
                    {
                        questionFilterItem.Score = questionScoreRule.Value;
                    }
                }
                // 当前题库中选取的题目总数
                questionFilterRuleItem.SelectBankQuestionCount = selectBankQuestionCount;
                paperQuestionCount += selectBankQuestionCount;
            }
            // 试卷总共题目数量
            paperCompositionRuleView.PaperFilterQuestionConditionRuleView.PaperQuestionCount = paperQuestionCount;
            // 题目难度处理
            var difficultyRule = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.DifficultyRule;
            var denominatorValue = difficultyRule.Sum(m => m.Value);
            Dictionary<string, decimal> keyValuePairs = [];
            // 计算比例
            foreach (var item in difficultyRule)
            {
                if(denominatorValue == 0)
                {
                    continue;
                }
                var rate = Math.Round((decimal)item.Value / denominatorValue, 2);
                keyValuePairs.Add(item.RuleCode, rate);
            }
            paperCompositionRuleView.PaperFilterQuestionConditionRuleView.DifficultyPercentage = keyValuePairs;
        }

        private static List<PaperQuestionView> SetSortByQuestionType(List<PaperQuestionView> returnPaperQuestionList)
        {
            var returnView = new List<PaperQuestionView>();
            foreach (var questionView in returnPaperQuestionList)
            {
                var typeCount = returnView.Where(m => m.QuestionType == questionView.QuestionType).Count();
                questionView.Sort = typeCount + 1;
                returnView.Add(questionView);
            }
            return returnView;
        }

        /// <summary>
        /// 动态获取api相关实例
        /// </summary>
        /// <param name="apiName"></param>
        /// <returns></returns>
        internal CompositionPaper SwitchFactoryByAPI(string apiName)
        {
            if (string.IsNullOrWhiteSpace(apiName))
            {
                return null;
            }
            CompositionPaper apiClass = null;
            switch (apiName)
            {
                case "ChoiceQuestion":
                    apiClass = _getPaperDetailByChoiceQuestion;
                    break;
                case "Judgment":
                case "MultipleChoice":
                case "SingleChoice":
                case "ShortAnswer":
                    apiClass = _getPaperDetailByType;
                    break;
            }
            return apiClass;
        }
    }
}
