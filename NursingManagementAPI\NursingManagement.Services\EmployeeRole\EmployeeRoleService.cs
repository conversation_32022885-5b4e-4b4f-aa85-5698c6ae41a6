﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 人员角色功能管理
    /// </summary>
    public class EmployeeRoleService : IEmployeeRoleService
    {
        private readonly IEmployeeRoleRepository _employeeRoleRepository;
        private readonly IAuthorityRoleRepository _authorityRoleRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;

        public EmployeeRoleService(
            IEmployeeRoleRepository employeeRoleRepository,
            IAuthorityRoleRepository authorityRoleRepository,
            IUnitOfWork unitOfWork,
            IEmployeeStaffDataRepository employeeStaffDataRepository,
            IDepartmentListRepository departmentListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository)
        {
            _employeeRoleRepository = employeeRoleRepository;
            _authorityRoleRepository = authorityRoleRepository;
            _unitOfWork = unitOfWork;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentListRepository = departmentListRepository;
        }

        #region 常量
        /// <summary>
        /// 片区主任角色ID
        /// </summary>
        private const int AUTHORITYROLEID_60 = 60;
        #endregion

        /// <summary>
        /// 根据科室ID获取对应的员工角色列表
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<List<EmployeeRoleView>> GetEmployeeRoleListAsync(int? departmentID)
        {
            List<EmployeeRoleInfo> cacheEmployeeRoleInfos = await _employeeRoleRepository.GetAll<EmployeeRoleInfo>();
            var cacheEmployeePersonalDataInfos = await _employeePersonalDataRepository.GetEmployeePersonalDataView();
            var employeeIDs = await _employeeStaffDataRepository.GetEmployeeIDsByDepartmentIDAsync(departmentID, true);
            var personalDatas = cacheEmployeePersonalDataInfos.Where(m => employeeIDs.Contains(m.EmployeeID)).OrderBy(m => m.EmployeeID).ToDictionary(m => m.EmployeeID, m => m.EmployeeName);
            //获取在职信息
            var staffDatas = await _employeeStaffDataRepository.GetEmployeeListByDeptIDOrEmployeeIDs(1,null, employeeIDs.ToArray());
            var cacheAuthorityRoles = await _authorityRoleRepository.GetAll<AuthorityRoleInfo>();
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();

            List<EmployeeRoleView> employeeRoleViews = new List<EmployeeRoleView>();
            foreach (var view in personalDatas)
            {
                var employee = staffDatas.FirstOrDefault(m => m.EmployeeID == view.Key);
                //没有角色权限不显示
                var employeeRoles = cacheEmployeeRoleInfos.Where(m => m.EmployeeID == view.Key).ToList();
                if (employee == null || employeeRoles.Count <= 0)
                {
                    continue;
                }
                var department = departmentList.FirstOrDefault(m => m.DepartmentID == employee.DepartmentID);
                if (department == null)
                {
                    continue;
                }
                var lastModifyRecord = employeeRoles.OrderBy(m => m.ModifyDateTime).LastOrDefault();
                var employeeRoleView = new EmployeeRoleView
                {
                    EmployeeID = view.Key,
                    EmployeeName = view.Value,
                    RoleNames = SetRoleNameList(cacheAuthorityRoles, employeeRoles),
                    AuthorityRoleIDs = GetEmployeeAuthorityRoleIDs(cacheAuthorityRoles, employeeRoles),
                    ModifyDateTime = lastModifyRecord?.ModifyDateTime,
                    ModifyEmployeeName = cacheEmployeePersonalDataInfos.FirstOrDefault(m =>lastModifyRecord?.ModifyEmployeeID == m.EmployeeID)?.EmployeeName ?? "",
                    DepartmentName = department.LocalShowName,
                };
                employeeRoleViews.Add(employeeRoleView);
            }
            return employeeRoleViews.OrderBy(m=>m.DepartmentName).ToList();

        }
        /// <summary>
        /// 拼接角色名称
        /// </summary>
        /// <param name="cacheAuthorityRoles"></param>
        /// <param name="employeeRoles"></param>
        /// <returns></returns>
        private List<string> SetRoleNameList(List<AuthorityRoleInfo> cacheAuthorityRoles, List<EmployeeRoleInfo> employeeRoles)
        {
            var authorityRoles = cacheAuthorityRoles.Where(m =>
                employeeRoles.Exists(n => n.AuthorityRoleID == m.AuthorityRoleID)).ToList();

            return authorityRoles.Select(m => m.RoleName).ToList();
        }

        /// <summary>
        /// 拼接角色名称
        /// </summary>
        /// <param name="cacheAuthorityRoles"></param>
        /// <param name="employeeRoles"></param>
        /// <returns></returns>
        private static List<int> GetEmployeeAuthorityRoleIDs(List<AuthorityRoleInfo> cacheAuthorityRoles, List<EmployeeRoleInfo> employeeRoles)
        {
            var authorityRoles = cacheAuthorityRoles.Where(m =>
                employeeRoles.Exists(n => n.AuthorityRoleID == m.AuthorityRoleID)).ToList();

            return authorityRoles.Select(m => m.AuthorityRoleID).Distinct().ToList();
        }
        /// <summary>
        /// 保存人员角色
        /// </summary>
        /// <param name="employeeRoleView">前端传递参数</param>
        /// <param name="userID">用户工号</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        public async Task<bool> SaveEmployeeRoleAsync(EmployeeRoleView employeeRoleView, string userID, string hospitalID)
        {
            var employeeRoleInfos = await _employeeRoleRepository.GetAllEmployeeRoleByEmployeeIDAsync(employeeRoleView.EmployeeID);
            // 不包含 -删除
            foreach (var employeeRole in employeeRoleInfos)
            {
                if (!employeeRoleView.AuthorityRoleIDs.Contains(employeeRole.AuthorityRoleID) && employeeRole.DeleteFlag != "*")
                {
                    employeeRole.Delete(userID);
                }
            }
            //新增角色
            foreach (var authorityRoleID in employeeRoleView.AuthorityRoleIDs)
            {
                var existRole = employeeRoleInfos.Find(m => m.AuthorityRoleID == authorityRoleID);
                if (existRole == null)
                {
                    await InsertEmployeeRoleInfo(employeeRoleView.EmployeeID, authorityRoleID, userID, hospitalID);
                    continue;
                }
                existRole.Modify(userID);
                existRole.DeleteFlag = "";
            }
            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            await _employeeRoleRepository.UpdateCache();
            return true;
        }
        /// <summary>
        /// 插入一条新的角色
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="authorityRoleID"></param>
        /// <param name="userID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task InsertEmployeeRoleInfo(string employeeID, int authorityRoleID, string userID, string hospitalID)
        {
            var employeeRoleInfo = new EmployeeRoleInfo()
            {
                EmployeeID = employeeID,
                AuthorityRoleID = authorityRoleID,
                HospitalID = hospitalID,
                DeleteFlag = "",
            };
            employeeRoleInfo.Modify(userID);
            employeeRoleInfo.Add(userID);
            await _unitOfWork.GetRepository<EmployeeRoleInfo>().InsertAsync(employeeRoleInfo);
        }

        /// <summary>
        /// 删除人员角色
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteEmployeeRoleAsync(string employeeID, string userID)
        {
            var roleList = await _employeeRoleRepository.GetEmployeeRoleInfoByEmployeeID(employeeID);
            if (roleList.Count <= 0)
            {
                return true;
            }
            foreach (EmployeeRoleInfo roleInfo in roleList)
            {
                roleInfo.Delete(userID);
            }
            _unitOfWork.GetRepository<EmployeeRoleInfo>().Update(roleList);
            var success = await _unitOfWork.SaveChangesAsync() > 0;
            await _employeeRoleRepository.UpdateCache();
            return success;
        }
        /// <summary>
        /// 获取片区权限
        /// </summary>
        /// <param name="employeeStrengthID"></param>
        /// <returns></returns>
        public async Task<bool> GetEmployeeRoleAsync(string employeeID)
        {
            var roleList = await _employeeRoleRepository.GetEmployeeRoleInfoByEmployeeID(employeeID);
            if (roleList.Count <= 0)
            {
                return false;
            }
            return roleList.Any(m => m.AuthorityRoleID ==  AUTHORITYROLEID_60);
        }
    }
}
