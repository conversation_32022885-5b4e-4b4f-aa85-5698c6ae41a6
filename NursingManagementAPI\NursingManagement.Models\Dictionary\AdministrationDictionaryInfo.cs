﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 政府编码字典表
    /// </summary>
    [Serializable]
    [Table("AdministrationDictionary")]
    public class AdministrationDictionaryInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int AdministrationDictionaryID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 类别码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingTypeCode { get; set; }
        /// <summary>
        /// 类别内容
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string SettingValue { get; set; }
        /// <summary>
        /// 参照码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ReferenceTypeCode { get; set; }
        /// <summary>
        /// 参照内容
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string ReferenceValue { get; set; }
        /// <summary>
        /// 国标码的值
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string TypeCode { get; set; }
        /// <summary>
        /// 值(编码)，系统内部唯一码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string TypeValue { get; set; }
        /// <summary>
        /// 说明描述
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string Description { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 院内显示名称
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string LocalShowName { get; set; }
        /// <summary>
        /// 院内系统使用码
        /// </summary>
        [Column(TypeName = "varchar(255)")]
        public string LocalCode { get; set; }
        /// <summary>
        /// 性别限制
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string Gender { get; set; }
    }
}
