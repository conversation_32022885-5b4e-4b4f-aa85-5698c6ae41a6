﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IDepartmentListRepository :ICacheRepository
    {       
        /// <summary>
        /// 根据ID获取科室部门信息
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<DepartmentListInfo> GetByIDAsync(int departmentID);

        /// <summary>
        /// 获取所有缓存
        /// </summary>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetByCacheAsync();
        /// <summary>
        /// 根据部门名字获取数据
        /// </summary>
        /// <param name="departmentName"></param>
        /// <returns></returns>
        Task<DepartmentListInfo> GetByName(string departmentName);
        /// <summary>
        /// 获取部门字典数据（ID，code，name）
        /// </summary>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetAllDictAsync();
        /// <summary>
        /// 根据组织架构类型获取部门配置
        /// </summary>
        /// <param name="organizationType"></param>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetByOrganizationType(string organizationType);
        /// <summary>
        /// 获取上级部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<DepartmentListInfo> GetUpperDepartment(int departmentID);
        /// <summary>
        /// 递归查找所有上级部门
        /// </summary>
        /// <typeparam name="T">返回元素类型</typeparam>
        /// <typeparam name="V">返回集合类型</typeparam>
        /// <param name="departmentID">部门ID</param>
        /// <param name="func">要返回的数据格式</param>
        /// <returns></returns>
        Task<V> GetUpperDepartmentRecursion<T, V>(int departmentID, Func<DepartmentListInfo, T> func) where V : ICollection<T>, new();
        /// <summary>
        /// 获取下级部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetLowerDepartments(int departmentID);
        /// <summary>
        /// 获取部门ID最大值
        /// </summary>
        /// <param name="hospitalID">医院编号</param>
        /// <param name="language">语言编号</param>
        /// <returns></returns>
        Task<int> GetMaxDepartmentID(string hospitalID, int language);

        /// <summary>
        /// 获取部门配置(包括删除的)
        /// </summary>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetDepartmentListNoCache();
        /// <summary>
        /// 根据部门名称集合获取数据
        /// </summary>
        /// <param name="nameList"></param>
        /// <param name="organizationType"></param>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetByNameList(List<string> nameList, string organizationType);
        /// <summary>
        /// 获取View
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentView>> GetViewsByOrganizationType(string organizationType);
        /// <summary>
        /// 根据his部门与护理管理部门关系
        /// </summary>
        /// <param name="organizationType1"></param>
        /// <param name="organizationType2"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<Dictionary<string, object>>> GetNMDepartmentByHisDepartment(string organizationType1, string organizationType2, string hospitalID);
        /// <summary>
        /// 根据组织类别和部门编码获取对应的部门ID
        /// </summary>
        /// <param name="organizationType">组织类别</param>
        /// <param name="departmentCode">部门编码</param>
        /// <returns></returns>
        Task<int> GetDepartmentIDByOrganizationAndDepartmentCodeAsync(string organizationType, string departmentCode);
        /// <summary>
        /// 根据主键集合获取数据
        /// </summary>
        /// <param name="deparmentIDs"></param>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetDepartmentIDByIDsAsync(List<int> deparmentIDs);
    }
}
