﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeStrengthRepository
    {
        /// <summary>
        /// 根据ID获取人员数据
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<List<EmployeeStrengthInfo>> GetListByEmployeeID(string employeeID);
        /// <summary>
        /// 根据个人特长主键获取数据
        /// </summary>
        /// <param name="employeeStrengthID"></param>
        /// <returns></returns>
        Task<EmployeeStrengthInfo> GetDataByStrengthID(string employeeStrengthID);
    }
}
