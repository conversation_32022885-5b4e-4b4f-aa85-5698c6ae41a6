﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeClothingSizesRepository : IEmployeeClothingSizesRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeClothingSizesRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<EmployeeClothingSizesInfo> GetDataByEmployeeID(string employeeID) 
        {
            return await _dbContext.EmployeeClothingSizesInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}