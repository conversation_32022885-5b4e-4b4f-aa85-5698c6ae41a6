﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 动态表单主表
        /// </summary>
        public DbSet<DynamicFormRecordInfo> DynamicFormRecordInfos { get; set; }
        /// <summary>
        /// 动态表单明细表
        /// </summary>
        public DbSet<DynamicFormDetailInfo> DynamicFormDetailInfos { get; set; }
        /// <summary>
        /// 动态表单明细项目属性表
        /// </summary>
        public DbSet<DynamicFormDetailAttributeInfo> DynamicFormDetailAttributeInfos { get; set; }
        /// <summary>
        /// 动态表单明细联动条件表
        /// </summary>
        public DbSet<DynamicFormDetailConditionInfo> DynamicFormDetailConditionInfos { get; set; }

    }
}
