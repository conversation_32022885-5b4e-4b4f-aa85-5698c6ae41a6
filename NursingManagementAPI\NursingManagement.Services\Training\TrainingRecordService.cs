﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.AspNetCore.Http;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class TrainingRecordService : ITrainingRecordService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ITrainingRecordRepository _trainingRecordRepository;
        private readonly ICourseSettingRepository _courseSettingRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISignUpRecordRepository _signUpRecordRepository;
        private readonly IDynamicFormService _dynamicFormService;
        private readonly IConditionMainRepository _conditionMainRepository;
        private readonly IConditionDetaiRepository _conditionDetaiRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IFileService _fileService;
        private readonly IDynamicFormRecordRepository _dynamicFormRecordRepository;
        private readonly IDynamicFormDetailRepository _dynamicFormDetailRepository;
        private readonly ICommonFileRepository _commonFileRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly IConditionService _conditionService;
        private readonly ITrainingLearnerRepository _trainingLearnerRepository;
        private readonly ITrainingEvaluationDetailRepository _trainingEvaluationDetailRepository;
        private readonly ITrainingClassDetailRepository _trainingClassDetailRepository;

        public TrainingRecordService(
            ITrainingRecordRepository trainingRecordRepository
            , ICourseSettingRepository courseSettingRepository
            , IEmployeePersonalDataRepository EmployeePersonalDataRepository
            , IUnitOfWork unitOfWork
            , ISignUpRecordRepository signUpRecordRepository
            , IDynamicFormService dynamicFormService
            , IConditionMainRepository conditionMainRepository
            , IConditionDetaiRepository conditionDetaiRepository
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , IFileService fileService
            , IDynamicFormRecordRepository dynamicFormRecordRepository
            , IDynamicFormDetailRepository dynamicFormDetailRepository
            , ICommonFileRepository commonFileRepository
            , ISettingDictionaryService settingDictionaryService
            , IConditionService conditionService
            , ITrainingLearnerRepository trainingLearnerRepository
            , ITrainingEvaluationDetailRepository trainingEvaluationDetailRepository
            , ITrainingClassDetailRepository trainingClassDetailRepository
            )
        {
            _trainingRecordRepository = trainingRecordRepository;
            _courseSettingRepository = courseSettingRepository;
            _employeePersonalDataRepository = EmployeePersonalDataRepository;
            _unitOfWork = unitOfWork;
            _signUpRecordRepository = signUpRecordRepository;
            _dynamicFormService = dynamicFormService;
            _conditionMainRepository = conditionMainRepository;
            _conditionDetaiRepository = conditionDetaiRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _fileService = fileService;
            _dynamicFormRecordRepository = dynamicFormRecordRepository;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
            _commonFileRepository = commonFileRepository;
            _settingDictionaryService = settingDictionaryService;
            _conditionService = conditionService;
            _trainingLearnerRepository = trainingLearnerRepository;
            _trainingEvaluationDetailRepository = trainingEvaluationDetailRepository;
            _trainingClassDetailRepository = trainingClassDetailRepository;
        }

        /// <summary>
        /// 培训考核文件标签ID
        /// </summary>
        private const string TRAINING_EXAMINE_FILE = "44";

        /// <summary>
        /// 查询培训记录数据
        /// </summary>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        public async Task<List<TrainingRecordView>> GetTrainingRecord(List<int> departmentIDs, string hospitalID)
        {
            var returnView = new List<TrainingRecordView>();
            var trainingList = await _trainingRecordRepository.GetListByDepartMentID(departmentIDs, hospitalID);
            if (trainingList.Count <= 0)
            {
                return returnView;
            }
            var lecturerEmployeeIDList = trainingList.Select(m => m.TrainingLecturer).ToList();
            var hostEmployeeIDList = trainingList.Select(m => m.TrainingHost).ToList();
            var trainingRecordIDList = trainingList.Select(m => m.TrainingRecordID).ToList();
            lecturerEmployeeIDList.AddRange(hostEmployeeIDList);
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(lecturerEmployeeIDList);
            var conditionMainList = await _conditionMainRepository.GetListBySourceIDs(trainingList.Select(m => m.TrainingRecordID).ToList(), "Training");
            var conditionDetailList = await _conditionDetaiRepository.GetListByMainIDs(conditionMainList.Select(m => m.ConditionMainID).ToList());
            var settingDictionaryList = await _settingDictionaryService.GetSettingDictionaryByCodeValue("TrainingRecord", new List<string> { "TrainingLocation", "TrainingMethod", "StatusCode" });
            var locationSetting = settingDictionaryList["TrainingLocation"];
            var methodSetting = settingDictionaryList["TrainingMethod"];
            var statusSetting = settingDictionaryList["StatusCode"];
            foreach (var item in trainingList)
            {
                var courseSettingIDs = ListToJson.ToList<List<string>>(item.CourseSettingIDs);
                var courseSettingList = await _courseSettingRepository.GetCourseListAsync(courseSettingIDs);
                var conditionMainData = conditionMainList.Find(m => m.SourceID == item.TrainingRecordID);
                var view = new TrainingRecordView
                {
                    TrainingRecordID = item.TrainingRecordID,
                    CourseSettingIDArr = courseSettingIDs,
                    CourseSettingName = string.Join(", ", courseSettingList.Select(p => p.CourseName)),
                    TrainingLocation = item.TrainingLocation,
                    TrainingLocationName = locationSetting.FirstOrDefault(m => m.Key == item.TrainingLocation).Value,
                    TrainingMethod = item.TrainingMethod,
                    TrainingMethodName = methodSetting.FirstOrDefault(m => m.Key == item.TrainingMethod).Value,
                    TrainingContent = item.TrainingContent,
                    TrainingTarget = item.TrainingTarget,
                    TrainingLecturer = item.TrainingLecturer,
                    TrainingLecturerName = employeeList.FirstOrDefault(m => m.EmployeeID == item.TrainingLecturer)?.EmployeeName,
                    TrainingHost = item.TrainingHost,
                    TrainingHostName = employeeList.FirstOrDefault(m => m.EmployeeID == item.TrainingHost)?.EmployeeName,
                    StartDateTime = item.StartDateTime,
                    EndDateTime = item.EndDateTime,
                    StatusCode = item.StatusCode,
                    StatusName = statusSetting.FirstOrDefault(m => m.Key == item.StatusCode).Value,
                    ExaminationRecordID = item.ExaminationRecordID,
                    EvaluationID = item.EvaluationID,
                    HeadNurseEvaluationID = item.HeadNurseEvaluationID,
                    DepartmentID = item.DepartmentID,
                    HospitalID = item.HospitalID,
                    SignUpConditions = conditionDetailList.Where(m => conditionMainData != null && m.ConditionMainID == conditionMainData.ConditionMainID && string.IsNullOrEmpty(m.ParentID))
                                   .OrderBy(m => m.Sort)
                                   .Select(m => new FormDetailConditionView()
                                   {
                                       ItemID = m.ItemID.ToString(),
                                       Condition = m.Condition,
                                       Value = m.ConditionValue,
                                       ConditionType = m.ConditionType,
                                       Children = GenerateCascaderView(conditionDetailList, m.ConditionDetailID)
                                   }).ToList(),
                    SignUpConditionContent = conditionMainData?.ConditionContent,
                    SignUpConditionExpression = conditionMainData?.ConditionExpression,
                    TrainingClassMainID = item.TrainingClassMainID,
                    SignInFlag = item.SignInFlag,
                    QRCodeRefreshTime = item.QRCodeRefreshTime,
                };
                if (!string.IsNullOrEmpty(item.FileIDs))
                {
                    view.FileInfoList = await GetTrainingRecordFileList(ListToJson.ToList<List<string>>(item.FileIDs));
                }
                returnView.Add(view);
            }
            return returnView;
        }

        /// <summary>
        /// 生成级联选择器所需的option
        /// </summary>
        /// <param name="conditionDetailList">条件明细数据</param>
        /// <param name="parentID">上级ID</param>
        /// <returns></returns>
        private List<FormDetailConditionView> GenerateCascaderView(List<ConditionDetailInfo> conditionDetailList, string parentID)
        {
            var views = new List<FormDetailConditionView>();
            var detailList = conditionDetailList.Where(m => m.ParentID == parentID).ToList();
            foreach (var detail in detailList)
            {
                var view = new FormDetailConditionView
                {
                    ItemID = detail.ItemID.ToString(),
                    Condition = detail.Condition,
                    Value = detail.ConditionValue,
                    ConditionType = detail.ConditionType,
                    // 递归获取子级部门
                    Children = GenerateCascaderView(conditionDetailList, detail.ConditionDetailID)
                };
                views.Add(view);
            }
            return views;
        }

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="fileIdList">commonFileInfo表主键ID集合</param>
        /// <returns></returns>
        private async Task<List<FileUploadReturnView>> GetTrainingRecordFileList(List<string> fileIdList)
        {
            var fileInfoViews = new List<FileUploadReturnView>();
            var commonFileInfos = await _commonFileRepository.GetPartFileInfosByIDsAsync(fileIdList);
            // 根据文档主键获取文档信息
            var documentMainIDs = commonFileInfos.Select(m => m.SourceID).ToList();
            var documentMainViews = await _fileService.GetFileAccessInfosFromFileSystemAsync(documentMainIDs);
            if (documentMainViews == null || documentMainViews.Count < 0)
            {
                return fileInfoViews;
            }
            foreach (var item in commonFileInfos)
            {
                var documentView = documentMainViews.FirstOrDefault(n => n.DocumentMainID == item.SourceID);
                fileInfoViews.Add(new FileUploadReturnView
                {
                    Url = documentView?.DocumentUrl,
                    FileID = item.CommonFileID,
                    FileName = documentView?.DocumentTitle ?? item.Content,
                    ExtensionName = documentView?.DocumentType
                });
            }
            return fileInfoViews;
        }

        /// <summary>
        /// 删除培训记录
        /// </summary>
        /// <param name="trainingRecordID">培训记录ID</param>
        /// <param name="modifyEmployeeID">修改人</param>
        /// <returns></returns>
        public async Task<bool> DeleteTrainingRecord(string trainingRecordID, string modifyEmployeeID)
        {
            var data = await _trainingRecordRepository.GetDataByID(trainingRecordID);
            if (data == null)
            {
                return false;
            }
            var signUpList = await _signUpRecordRepository.GetListByRecordID(trainingRecordID, "CourseTraining");
            foreach (var item in signUpList)
            {
                item.Delete(modifyEmployeeID);
            }
            data.Delete(modifyEmployeeID);
            var conditionMain = await _conditionMainRepository.GetDataBySourceID(trainingRecordID, "Training");
            if (conditionMain != null)
            {
                conditionMain.Delete(modifyEmployeeID);
                var conditionDetailList = await _conditionDetaiRepository.GetListByMainID(conditionMain.ConditionMainID);
                foreach (var detail in conditionDetailList)
                {
                    detail.Delete(modifyEmployeeID);
                }
            }
            var formIDs = new List<string>()
            {
                data.EvaluationID,data.HeadNurseEvaluationID
            };
            var formDatas = await _dynamicFormRecordRepository.GetFormByFormRecordIDs(formIDs);
            if (formDatas.Count > 0)
            {
                formDatas.ForEach(record => record.Delete(modifyEmployeeID));
                var detailList = await _dynamicFormDetailRepository.GetFormDetailListByRecordIDList(formIDs);
                detailList.ForEach(detail => detail.Delete(modifyEmployeeID));
                _unitOfWork.GetRepository<DynamicFormRecordInfo>().Update(formDatas);
                _unitOfWork.GetRepository<DynamicFormDetailInfo>().Update(detailList);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 保存培训记录
        /// </summary>
        /// <param name="view">保存参数view</param>
        /// <returns></returns>
        public async Task<bool> SaveTrainingRecord(TrainingRecordView view)
        {
            var data = await _trainingRecordRepository.GetDataByID(view.TrainingRecordID);
            var addFlag = data == null;
            // 新增或修改培训记录
            await SaveOrUpdateTrainingRecord(view, data, addFlag);
            // 根据是否选择培训群组 判断如何处理人员培训记录和报名记录 选择培训群组，处理人员培训记录
            if (!string.IsNullOrEmpty(view.TrainingClassMainID))
            {
                // 根据群组报名情况 - 生成人员培训记录
                var passEmployeeIDs = await _signUpRecordRepository.GetSignUpEmployeeIDsBySourceID(view.TrainingClassMainID);
                if (passEmployeeIDs.Count > 0)
                {
                    var emloyeeList = await _employeeStaffDataRepository.GetEmployeeStaffDataView();
                    List<(string, int?)> employeeTuple = passEmployeeIDs.Select(employeeID => (employeeID, emloyeeList.Find(n => n.EmployeeID == employeeID)?.DepartmentID)).ToList();
                    await HandleTrainingLearnerDatas(view.TrainingRecordID, employeeTuple, view.ModifyEmployeeID, addFlag, view.HospitalID);
                }
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            // 设置了培训报名条件（将符合条件的人员进行人员报名和人员培训信息记录登记）
            var conditionMainID = "";
            if (!string.IsNullOrEmpty(view.SignUpConditionContent))
            {
                conditionMainID = await SetConditionView(view, false);
            }
            if (!string.IsNullOrEmpty(conditionMainID))
            {
                await HandleSignDataByCondtion(conditionMainID, view.TrainingRecordID, view.ModifyEmployeeID, false, view.HospitalID);
            }

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 新增或修改培训记录
        /// </summary>
        /// <param name="view"></param>
        /// <param name="trainingRecord">数据库中的培训记录</param>
        /// <param name="addFlag">新增状态，用于判断是否新增</param>
        /// <returns></returns>
        private async Task SaveOrUpdateTrainingRecord(TrainingRecordView view, TrainingRecordInfo trainingRecord, bool addFlag)
        {
            // 获取培训相关课程集合
            var relatedCourseSettingIDs = view.CourseSettingIDArr;
            if (!string.IsNullOrEmpty(view.TrainingClassMainID))
            {
                relatedCourseSettingIDs = await _trainingClassDetailRepository.GetCourseSettingIDs(view.TrainingClassMainID);
            }
            var courseSettingIDStr = relatedCourseSettingIDs.Count > 0 ? ListToJson.ToJson(relatedCourseSettingIDs) : "";
            if (addFlag)
            {
                var recordInfo = CreateTrainingRecordByView(view, courseSettingIDStr);
                recordInfo.Add(view.ModifyEmployeeID).Modify(view.ModifyEmployeeID);
                var addFileIDs = await UpLoadFile(view.Files, view.ModifyEmployeeID);
                recordInfo.FileIDs = ListToJson.ToJson(addFileIDs);
                view.TrainingRecordID = recordInfo.TrainingRecordID;
                await _unitOfWork.GetRepository<TrainingRecordInfo>().InsertAsync(recordInfo);
                return;
            }
            // 修改
            trainingRecord.CourseSettingIDs = courseSettingIDStr;
            trainingRecord.TrainingLocation = view.TrainingLocation;
            trainingRecord.TrainingMethod = view.TrainingMethod;
            trainingRecord.TrainingContent = view.TrainingContent;
            trainingRecord.TrainingTarget = view.TrainingTarget;
            trainingRecord.TrainingLecturer = view.TrainingLecturer;
            trainingRecord.TrainingHost = view.TrainingHost;
            trainingRecord.StartDateTime = view.StartDateTime;
            trainingRecord.EndDateTime = view.EndDateTime;
            trainingRecord.StatusCode = view.StatusCode;
            trainingRecord.ExaminationRecordID = view.ExaminationRecordID ?? "";
            trainingRecord.EvaluationID = view.EvaluationID ?? "";
            trainingRecord.HeadNurseEvaluationID = view.HeadNurseEvaluationID ?? "";
            trainingRecord.DepartmentID = view.DepartmentID;
            trainingRecord.SignInFlag = view.SignInFlag;
            trainingRecord.QRCodeRefreshTime = view.QRCodeRefreshTime;
            trainingRecord.Modify(view.ModifyEmployeeID);
            var uploadedFileIDs = await UpLoadFile(view.Files, view.ModifyEmployeeID);
            if (uploadedFileIDs.Count <= 0)
            {
                return;
            }
            if (trainingRecord.FileIDs != null)
            {
                var existedFileIDs = ListToJson.ToList<List<string>>(trainingRecord.FileIDs);
                uploadedFileIDs.AddRange(existedFileIDs);
            }
            trainingRecord.FileIDs = ListToJson.ToJson(uploadedFileIDs);
        }

        /// <summary>
        /// 根据参数构造培训记录Model对象
        /// </summary>
        /// <param name="view">前端传入的培训记录保存参数</param>
        /// <param name="courseSettingIDStr">课程ID集合格式化后的字符串</param>
        /// <returns></returns>
        private static TrainingRecordInfo CreateTrainingRecordByView(TrainingRecordView view, string courseSettingIDStr)
        {
            return new TrainingRecordInfo()
            {
                TrainingRecordID = Guid.NewGuid().ToString("N"),
                CourseSettingIDs = courseSettingIDStr,
                TrainingLocation = view.TrainingLocation,
                TrainingMethod = view.TrainingMethod,
                TrainingContent = view.TrainingContent,
                TrainingTarget = view.TrainingTarget,
                TrainingLecturer = view.TrainingLecturer,
                TrainingHost = view.TrainingHost,
                StartDateTime = view.StartDateTime,
                EndDateTime = view.EndDateTime,
                StatusCode = view.StatusCode,
                ExaminationRecordID = view.ExaminationRecordID ?? "",
                EvaluationID = view.EvaluationID,
                HeadNurseEvaluationID = view.HeadNurseEvaluationID,
                DepartmentID = view.DepartmentID,
                HospitalID = view.HospitalID,
                TrainingClassMainID = view.TrainingClassMainID,
                SignInFlag = view.SignInFlag,
                QRCodeRefreshTime = view.QRCodeRefreshTime,
                DeleteFlag = ""
            };
        }

        /// <summary>
        /// 获取课程级联下拉框数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<CascaderView<string>>> GetCourseSetting()
        {
            var returnView = new List<CascaderView<string>>();
            var courseSettingList = await _courseSettingRepository.GetAllCourseSetting();
            if (courseSettingList.Count <= 0)
            {
                return returnView;
            }
            var onLevelSetting = courseSettingList.Where(m => m.Level == 1).ToList();
            foreach (var item in onLevelSetting)
            {
                var cascaderView = new CascaderView<string>
                {
                    Label = item.CourseName,
                    Value = item.CourseSettingID,
                    Children = GenerateCascaderView(courseSettingList, 2, item.CourseSettingID),
                };
                returnView.Add(cascaderView);
            }
            return returnView;
        }

        /// <summary>
        /// 递归获取级联子数据
        /// </summary>
        /// <param name="courseSettingList">课程信息集合</param>
        /// <param name="level">级联选择器中的选项所在层级</param>
        /// <param name="parentID">父级ID</param>
        /// <returns></returns>
        private List<CascaderView<string>> GenerateCascaderView(List<CourseSettingInfo> courseSettingList, int level, string parentID)
        {
            var cascaderViews = new List<CascaderView<string>>();
            var currentLevelList = courseSettingList.Where(m => m.Level == level && m.ParentID == parentID).ToList();

            foreach (var item in currentLevelList)
            {
                var cascaderView = new CascaderView<string>
                {
                    Label = item.CourseName,
                    Value = item.CourseSettingID,
                    // 递归获取子级部门
                    Children = GenerateCascaderView(courseSettingList, level + 1, item.CourseSettingID)
                };
                cascaderViews.Add(cascaderView);
            }
            return cascaderViews;
        }

        /// <summary>
        /// 保存培训问卷模版
        /// </summary>
        /// <param name="fromView">培训问卷模版</param>
        /// <param name="employeeID">执行人</param>
        /// <returns></returns>
        public async Task<string> SaveEvaluationForm(FormTemplateView fromView, string employeeID)
        {
            await _dynamicFormService.DeleteFormTemplate(fromView.Props.FormID, employeeID);
            // 先保存表单模板，然后将表单模板ID写入HierarchicalQCSubject表中
            var dynamicFormID = await _dynamicFormService.SaveFormTemplate(fromView, employeeID);
            if (string.IsNullOrWhiteSpace(dynamicFormID))
            {
                _logger.Error("SaveQCForm方法中调用_dynamicFormService.SaveFormTemplate保存表单失败");
                return null;
            }
            var result = await _unitOfWork.SaveChangesAsync() >= 0;
            if (result)
            {
                await _dynamicFormService.UpdateFormCache();
            }
            return dynamicFormID;
        }

        /// <summary>
        /// 根据条件和培训记录产生培训人员的信息记录
        /// </summary>
        /// <param name="trainingRecordID">培训主记录ID</param>
        /// <param name="employeeList">人员工号和科室ID</param>
        /// <param name="modifyEmployeeID">新增或修改培训记录的人员工号ID</param>
        /// <param name="addFlag">标记 是新增还是修改培训记录</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        private async Task HandleTrainingLearnerDatas(string trainingRecordID, List<(string, int?)> employeeList, string modifyEmployeeID, bool addFlag, string hospitalID)
        {
            if (!addFlag)
            {
                var existedTrainLearnerInfos = await _trainingLearnerRepository.GetByTrainingRecordIdAsync(trainingRecordID);
                if (existedTrainLearnerInfos.Count > 0)
                {
                    // 排除已经存在的不新增
                    employeeList = employeeList.Where(m => !existedTrainLearnerInfos.Exists(n => n.EmployeeID == m.Item1)).ToList();
                    //重新调整培训记录时，发现群组中没有的人员培训记录删除
                    var notExistedLearnerRecords = existedTrainLearnerInfos.Where(m => !employeeList.Exists(n => n.Item1 == m.EmployeeID)).ToList();
                    notExistedLearnerRecords.ForEach(m => m.Delete(modifyEmployeeID));
                }
            }
            // 新增人员培训信息记录
            foreach (var (employeeID, departmentID) in employeeList)
            {
                var trainLearnerInfo = new TrainingLearnerInfo
                {
                    TrainingRecordID = trainingRecordID,
                    DepartmentID = departmentID ?? 0,
                    EmployeeID = employeeID,
                    HospitalID = hospitalID,
                    LastTrainingTime = DateTime.Now,
                    MonitorFlag = false,
                };
                trainLearnerInfo.TrainingLearnerID = trainLearnerInfo.GetId();
                trainLearnerInfo.Add(modifyEmployeeID);
                trainLearnerInfo.Modify(modifyEmployeeID);
                await _unitOfWork.GetRepository<TrainingLearnerInfo>().InsertAsync(trainLearnerInfo);
            }
        }

        /// <summary>
        /// 保存培训记录报名条件
        /// </summary>
        /// <param name="view">培训记录保存相关参数</param>
        /// <param name="addFlag">培训记录（新增/修改）</param>
        /// <returns>报名条件主记录ID - ConditionMainID</returns>
        private async Task<string> SetConditionView(TrainingRecordView view, bool addFlag)
        {
            var conditionView = new HandleConditionView
            {
                Conditions = view.SignUpConditions,
                ConditionContent = view.SignUpConditionContent,
                ConditionExpression = view.SignUpConditionExpression,
                SourceID = view.TrainingRecordID,
                SourceType = "Training",
                ModifyEmployeeID = view.ModifyEmployeeID,
                AddFlag = addFlag,
            };
            return await _conditionService.HandleConditionData(conditionView);
        }

        /// <summary>
        /// 根据报名条件生成报名记录和人员培训记录
        /// </summary>
        /// <param name="conditionMainID">条件主记录ID</param>
        /// <param name="trainingRecordID">培训记录ID</param>
        /// <param name="modifyEmployeeID">操作修改人</param>
        /// <param name="addFlag">培训记录（新增/修改）</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        private async Task HandleSignDataByCondtion(string conditionMainID, string trainingRecordID, string modifyEmployeeID, bool addFlag, string hospitalID)
        {
            var learnEmployeeList = new List<EmployeeStaffDataInfo>();
            var emloyeeList = await _employeeStaffDataRepository.GetEmployeeStaffDataView();
            var conditionDetailList = await _conditionDetaiRepository.GetListByMainID(conditionMainID);
            //EQUALS: 等于; NOT_EQUALS: 不等于; GREATER_THAN: 大于; LESS_THAN: 小于;
            //GREATER_THAN_OR_EQUALS:大于等于; LESS_THAN_OR_EQUALS:小于等于;
            //EMPTY:为空; INCLUDES:包含; EXCLUDE:不包含; RANGE:范围
            var onlevelConditionDetailList = conditionDetailList.Where(m => string.IsNullOrEmpty(m.ParentID)).ToList();
            learnEmployeeList = await _conditionService.FilterConditionDetailList(onlevelConditionDetailList, emloyeeList, conditionDetailList);
            var employeeIDList = learnEmployeeList.Select(m => (m.EmployeeID, m.DepartmentID)).ToList();
            if (employeeIDList.Count > 0)
            {
                await HandleSignUpData(trainingRecordID, employeeIDList, modifyEmployeeID, addFlag);
                // 处理人员培训记录
                await HandleTrainingLearnerDatas(trainingRecordID, employeeIDList, modifyEmployeeID, addFlag, hospitalID);
            }
        }

        /// <summary>
        /// 根据条件和培训记录产生培训人员的信息记录
        /// </summary>
        /// <param name="trainingRecordID">培训主记录ID</param>
        /// <param name="employeeList">人员工号和科室ID</param>
        /// <param name="modifyEmployeeID">新增或修改培训记录的人员工号ID</param>
        /// <param name="addFlag">标记 是新增还是修改培训记录</param>
        /// <returns></returns>
        private async Task HandleTrainingLearnerDatas(string trainingRecordID, List<(string, int?)> employeeList, string modifyEmployeeID, bool addFlag)
        {
            var employeeDict = await _employeePersonalDataRepository.GetIDAndNameData();
            if (!addFlag)
            {
                var existedTrainLearnerInfos = await _trainingLearnerRepository.GetByTrainingRecordIdAsync(trainingRecordID);
                if (existedTrainLearnerInfos.Count > 0)
                {
                    existedTrainLearnerInfos.ForEach(m => m.Delete(modifyEmployeeID));
                }
            }
            // 新增人员培训信息记录
            foreach (var (employeeID, departmentID) in employeeList)
            {
                var trainLearnerInfo = new TrainingLearnerInfo
                {
                    TrainingRecordID = trainingRecordID,
                    DepartmentID = departmentID ?? 0,
                    EmployeeID = employeeID,
                    HospitalID = "1",
                    LastTrainingTime = DateTime.Now,
                    MonitorFlag = false,
                };
                trainLearnerInfo.TrainingLearnerID = trainLearnerInfo.GetId();
                trainLearnerInfo.Add(modifyEmployeeID);
                trainLearnerInfo.Modify(modifyEmployeeID);
                await _unitOfWork.GetRepository<TrainingLearnerInfo>().InsertAsync(trainLearnerInfo);
            }
        }

        /// <summary>
        /// 处理培训人员数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="learnerIDs"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <param name="addFlag"></param>
        /// <returns></returns>
        private async Task HandleSignUpData(string recordID, List<(string, int?)> learnerIDs, string modifyEmployeeID, bool addFlag)
        {
            if (addFlag)
            {
                await AddSignUpInfo(recordID, learnerIDs, modifyEmployeeID);
                return;
            }
            var signUpList = await _signUpRecordRepository.GetListByRecordID(recordID, "CourseTraining");
            foreach (var item in signUpList)
            {
                item.Delete(modifyEmployeeID);
            }
            await AddSignUpInfo(recordID, learnerIDs, modifyEmployeeID);
        }

        /// <summary> 新增培训人员数据 </summary> <param name="recordID">课程培训记录的ID</param> <param
        /// name="learnerIDs">List<(string EmployeeID, int?
        /// DepartmentID)>:培训人员的EmployeeID和所属部门</param> <param name="modifyEmployeeID">修改培训记录的操作人工号</param>
        private async Task AddSignUpInfo(string recordID, List<(string EmployeeID, int? DepartmentID)> learnerIDs, string modifyEmployeeID)
        {
            foreach (var (employeeID, _) in learnerIDs)
            {
                var info = new SignUpRecordInfo()
                {
                    SignUpRecordID = Guid.NewGuid().ToString("N"),
                    SourceID = recordID,
                    SourceType = "1",
                    StatusCode = "1",
                    SignUpType = "2",
                    EmployeeID = employeeID,
                };
                info.Add(modifyEmployeeID);
                info.Modify(modifyEmployeeID);
                await _unitOfWork.GetRepository<SignUpRecordInfo>().InsertAsync(info);
            }
        }

        /// <summary>
        /// 上传附件逻辑
        /// </summary>
        /// <param name="fileList">文件信息</param>
        /// <param name="employeeID">操作人</param>
        /// <returns></returns>
        private async Task<List<string>> UpLoadFile(List<IFormFile> fileList, string employeeID)
        {
            if (fileList == null || fileList.Count <= 0)
            {
                return null;
            }
            var fileIDList = new List<string>();
            // TODO：等文档管理的文件属性字典表整理好，这里替换为正常的
            var documentDetails = new List<DocumentDetailView>()
            {
                new (){ GroupID = 1, ItemID = 1, Value = "测试" },
            };
            var employeeData = await _employeePersonalDataRepository.GetDataByEmployeeID(employeeID);
            foreach (var file in fileList)
            {
                // 组装保存文档接口参数
                var document = new DocumentView()
                {
                    DocumentMainView = new DocumentMainView()
                    {
                        SourceSystem = "NursingManagement",
                        DocumentTypeID = 1,
                        UserID = employeeID,
                        UserName = employeeData.EmployeeName,
                        DocumentTitle = file.FileName,
                        DocumentType = file.FileName.Substring(file.FileName.LastIndexOf(".") + 1),
                        DocumentStatus = 20
                    },
                    DocumentDetailViews = documentDetails,
                    DocumentTagViews = [new DocumentTagView
                    {
                        DocumentTagListID = TRAINING_EXAMINE_FILE
                    }]
                };
                // 调用文档管理系统的接口保存文件
                var view = await _fileService.UpLoadFile(file, document);
                if (view != null && !string.IsNullOrEmpty(view.FileID))
                {
                    fileIDList.Add(view.FileID);
                }
            }
            return fileIDList;
        }

        /// <summary>
        /// 保存培训评价记录
        /// </summary>
        /// <param name="mainAndDetailView"></param>
        /// <returns></returns>
        public async Task<bool> SaveEvaluationData(TrainingEvaluationMainAndDetailView mainAndDetailView)
        {
            var trainingRecord = await _trainingRecordRepository.GetDataByID(mainAndDetailView.TrainingRecordID);
            var dynamicFormRecordID = mainAndDetailView.EvaluationType == "1" ? trainingRecord?.EvaluationID : trainingRecord?.HeadNurseEvaluationID;
            if (string.IsNullOrEmpty(mainAndDetailView.BeEvaluationEmployeeID))
            {
                mainAndDetailView.BeEvaluationEmployeeID = trainingRecord.TrainingLecturer;
            }
            var evaluationMainInfo = new TrainingEvaluationMainInfo
            {
                TrainingEvaluationMainID = Guid.NewGuid().ToString("N"),
                TrainingLearnerID = mainAndDetailView.TrainingLearnerID,
                EvaluationType = mainAndDetailView.EvaluationType,
                BeEvaluationEmployeeID = mainAndDetailView.BeEvaluationEmployeeID,
                DynamicFormRecordID = dynamicFormRecordID,
                DeleteFlag = ""
            };
            evaluationMainInfo.Add(mainAndDetailView.ModifyEmployeeID);
            evaluationMainInfo.Modify(mainAndDetailView.ModifyEmployeeID);
            await _unitOfWork.GetRepository<TrainingEvaluationMainInfo>().InsertAsync(evaluationMainInfo);
            HandleEvaluationDetailList(mainAndDetailView, evaluationMainInfo.TrainingEvaluationMainID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 保存培训评价明细记录
        /// </summary>
        /// <param name="mainAndDetailView">保存培训评价视图</param>
        /// <param name="trainingEvaluationMainID">培训评价主表ID</param>
        private void HandleEvaluationDetailList(TrainingEvaluationMainAndDetailView mainAndDetailView, string trainingEvaluationMainID)
        {
            foreach (var detailView in mainAndDetailView.DetailList)
            {
                var trainingEvaluationDetail = new TrainingEvaluationDetailInfo
                {
                    TrainingEvaluationDetailID = Guid.NewGuid().ToString("N"),
                    TrainingEvaluationMainID = trainingEvaluationMainID,
                    ItemID = detailView.ItemID,
                    Value = detailView.Value,
                    GroupID = detailView.GroupID,
                    ParentID = detailView.ParentID,
                    DeleteFlag = ""
                };
                trainingEvaluationDetail.Add(mainAndDetailView.ModifyEmployeeID);
                trainingEvaluationDetail.Modify(mainAndDetailView.ModifyEmployeeID);
                _unitOfWork.GetRepository<TrainingEvaluationDetailInfo>().Insert(trainingEvaluationDetail);
            }
        }

        /// <summary>
        /// 获取培训评价模板数据
        /// </summary>
        /// <param name="evaluationMainID">培训评价主表ID</param>
        /// <param name="trainingRecordID">培训记录ID</param>
        /// <param name="evaluationType">评价类别</param>
        /// <returns></returns>
        public async Task<FormTemplateView> GetEvaluationFormView(string evaluationMainID, string trainingRecordID, string evaluationType)
        {
            var view = new FormTemplateView();
            if (string.IsNullOrEmpty(trainingRecordID))
            {
                return view;
            }
            var trainingRecord = await _trainingRecordRepository.GetDataByID(trainingRecordID);
            if (trainingRecord == null)
            {
                return view;
            }
            var dynamicFormRecordID = evaluationType == "1" ? trainingRecord?.EvaluationID : trainingRecord?.HeadNurseEvaluationID;
            var details = string.IsNullOrEmpty(evaluationMainID) ? [] : await _trainingEvaluationDetailRepository.GetDetailListByMainID(evaluationMainID);
            var datas = new List<FormValueView>();
            foreach (var item in details)
            {
                var data = new FormValueView()
                {
                    ID = item.ItemID.ToString(),
                    GroupID = item.GroupID.ToString(),
                    ParentID = item.ParentID.ToString(),
                    Value = ListToJson.ToList<object>(item.Value ?? "")
                };
                datas.Add(data);
            }
            view = await _dynamicFormService.GetFormTemplateByRecordID(dynamicFormRecordID, [], null, null);
            return view;
        }
    }
}
