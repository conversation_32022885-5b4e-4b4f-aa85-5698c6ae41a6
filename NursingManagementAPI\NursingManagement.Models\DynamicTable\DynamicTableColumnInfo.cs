﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    ///动态表格字段配置
    /// </summary>
    [Serializable]
    [Table("DynamicTableColumn")]
    public class DynamicTableColumnInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int ColumnID { get; set; }
        /// <summary>
        /// 对应表格ID
        /// </summary>
        public int DynamicTableListID { get; set; }
        /// <summary>
        /// 字段名称
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string ColumnShowName { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 是否默认显示
        /// </summary>
        public int DefaultShowFlag { get; set; }
    }
}
