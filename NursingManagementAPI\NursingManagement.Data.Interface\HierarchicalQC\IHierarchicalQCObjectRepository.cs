﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCObjectRepository
    {
        /// <summary>
        /// 根据主记录集合和质控对象类型获取View
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <param name="objectType"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCObjectInfo>> GetHierarchicalQCObjectViewByRecordID(List<string> recordIDs, int objectType);
        /// <summary>
        /// 根据主记录ID和质控对象类型获取View
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="objectType"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCObjectInfo>> GetHierarchicalQCObjectViewByRecordID(string recordID, int objectType);
        /// <summary>
        /// 根据主记录ID和质控对象类型获取Model 删除使用
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="objectType"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCObjectInfo>> GetHierarchicalQCObjectInfoByRecordID(string recordID, int objectType);
    }
}
