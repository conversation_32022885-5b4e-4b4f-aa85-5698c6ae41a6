﻿using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Data;

namespace NursingManagement.Services
{
    public class RouterService : IRouterService
    {
        private readonly IAuthorityCommonService _authorityCommonService;
        private readonly IAuthorityComponentListRepository _authorityComponentListRepository;
        private readonly IComponentListRepository _componentListRepository;

        public RouterService(
            IAuthorityCommonService authorityCommonService
            , IAuthorityComponentListRepository authorityComponentListRepository
            , IComponentListRepository componentListRepository
        )
        {
            _authorityCommonService = authorityCommonService;
            _authorityComponentListRepository = authorityComponentListRepository;
            _componentListRepository = componentListRepository;
        }

        public async Task<RouterAndComponentView> GetRouterAndComponentListByRoles(List<int> roles, int clientType)
        {
            var routerAndButtonView = new RouterAndComponentView();
            var ret = await _authorityCommonService.GetMenuAndRouterList(roles, clientType);
            if (ret == null)
            {
                return routerAndButtonView;
            }
            var routerList = ret.Item2;
            // 组装路由
            routerAndButtonView.RouterList = CreateRouterList(routerList);
            routerAndButtonView.ComponentList = await CreateComponentList(roles, routerList, clientType);
            return routerAndButtonView;
        }

        public async Task<List<ComponentView>> GetComponentListByRoles(List<int> roles, int clientType)
        {
            var buttonViewList = new List<ComponentView>();
            var ret = await _authorityCommonService.GetMenuAndRouterList(roles, clientType);
            if (ret == null)
            {
                return buttonViewList;
            }
            var routerList = ret.Item2;
            buttonViewList = await CreateComponentList(roles, routerList, clientType);
            return buttonViewList;
        }

        /// <summary>
        /// 组装路由清单
        /// </summary>
        /// <param name="routerList"></param>
        /// <returns></returns>
        private List<RouterView> CreateRouterList(List<RouterListInfo> routerList)
        {
            var routerViewList = new List<RouterView>();
            var oneLevelRouterList = routerList.Where(m => m.RouterLevel == 1).OrderBy(m => m.Sort).ToList();
            foreach (var router in oneLevelRouterList)
            {
                routerViewList.Add(CreateRouterView(routerList, router));
            }
            return routerViewList;
        }
        /// <summary>
        /// 组装RouterView
        /// </summary>
        /// <param name="routerList"></param>
        /// <param name="router"></param>
        /// <returns></returns>
        private RouterView CreateRouterView(List<RouterListInfo> routerList, RouterListInfo router)
        {
            var children = CreateChildRouterList(routerList, router.RouterListID);
            var meta = new Dictionary<string, object> {
                    { "auth", router.Auth ?? false },
                    { "hasTopMenu", router.HasTopMenu ?? false },
                    { "keepAlive", router.KeepAlive ?? false },
                    { "refreshFlag", router.KeepAlive ?? false },
                    { "routerListID", router.RouterListID }
                };
            // 如果有父层，把父层的路由地址填上
            if (router.ParentID != null)
            {
                var parentRouter = routerList.Find(m => m.RouterListID == router.ParentID.Value);
                if (parentRouter != null)
                {
                    // 如果是头部菜单画面，继续找父画面
                    if ((parentRouter.HasTopMenu ?? false) && parentRouter.ParentID != null)
                    {
                        parentRouter = routerList.Find(m => m.RouterListID == parentRouter.ParentID.Value);
                        if (parentRouter != null)
                        {
                            meta.Add("parentPath", parentRouter.Path);
                        }
                    }
                    else
                    {
                        meta.Add("parentPath", parentRouter.Path);
                    }
                }
            }
            var routerPath = _authorityCommonService.AssemblePath(router?.Path?.Trim(), true);
            return new RouterView()
            {
                Path = routerPath,
                Name = router.Name,
                Component = router.Component,
                Children = children,
                Meta = meta
            };
        }
        /// <summary>
        /// 组装子路由清单
        /// </summary>
        /// <param name="routerList"></param>
        /// <param name="routerListID"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private List<RouterView> CreateChildRouterList(List<RouterListInfo> routerList, int routerListID)
        {
            var childRouterList = routerList.Where(m => m.ParentID == routerListID).OrderBy(m => m.Sort).ToList();
            if (childRouterList.Count <= 0)
            {
                return null;
            }
            var children = new List<RouterView>();
            foreach (var childRouter in childRouterList)
            {
                children.Add(CreateRouterView(routerList, childRouter));
            }
            return children;
        }

        private async Task<List<ComponentView>> CreateComponentList(List<int> roles, List<RouterListInfo> routerList, int clientType)
        {
            var buttonViewList = new List<ComponentView>();
            var routerListIDs = routerList.Select(m => m.RouterListID).Distinct().ToList();
            var componentList = await _componentListRepository.GetComponentListByType("Authority");
            if (componentList.Count <= 0)
            {
                return buttonViewList;
            }
            var authorityComponentList = await _authorityComponentListRepository.GetComponentListByRoleIDs(roles, routerListIDs, clientType);
            // 依据路由、按钮、客户端分组取权限最高的数据
            authorityComponentList = authorityComponentList.GroupBy(m=> new { m.RouterListID,m.ComponentListID,m.ClientType }).Select(m=>m.OrderByDescending(m=>m.AuthorityRoleID).First()).ToList();
            if (authorityComponentList.Count <= 0)
            {
                return buttonViewList;
            }
            buttonViewList = (from a in authorityComponentList                                                                                                                                                                                                                              
                              join c in componentList on a.ComponentListID equals c.ComponentListID
                              join r in routerList on new { a.RouterListID, a.ClientType } equals new { r.RouterListID, r.ClientType }
                              select new ComponentView()
                              {
                                  ComponentListID = c.ComponentListID,
                                  RouterListID = a.RouterListID,
                                  RouterPath = r.Path,
                                  ControlerType = c.ControlerType,
                                  Status = a.Status,
                                  Description = a.Description,
                              }).ToList();
            return buttonViewList;
        }
    }
}
