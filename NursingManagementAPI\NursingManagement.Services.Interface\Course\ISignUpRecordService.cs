﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 培训报名服务接口
    /// </summary>
    public interface ISignUpRecordService
    {
        /// <summary>
        /// 根据来源类型查询报名记录
        /// </summary>
        /// <param name="sourceType">来源类别</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns>报名信息视图模型列表</returns>
        Task<List<SignUpRecordView>> GetSignUpRecordListAsync(string sourceType, string sourceID);

        /// <summary>
        /// 保存报名信息
        /// </summary>
        /// <param name="signUpRecordInfo">报名信息</param>
        /// <param name="employeeID">操作人员ID</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveSignUpRecordAsync(SignUpRecordInfo signUpRecordInfo, string employeeID);

        /// <summary>
        /// 删除报名信息
        /// </summary>
        /// <param name="signUpRecordID">报名记录ID</param>
        /// <param name="employeeID">操作人员ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteSignUpRcordAsync(string signUpRecordID, string employeeID);
    }
}