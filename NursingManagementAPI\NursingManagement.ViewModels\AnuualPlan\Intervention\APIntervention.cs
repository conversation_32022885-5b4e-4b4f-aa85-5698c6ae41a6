﻿namespace NursingManagement.ViewModels
{
    public class APIntervention
    {
        /// <summary>
        /// 制定序号
        /// </summary>
        public string AnnualPlanInterventionMainID { get; set; }
        /// <summary>
        /// 计划目标GUID
        /// </summary>
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 工作项目明细ID
        /// </summary>
        public string ProjectDetailID { get; set; }
        /// <summary>
        /// 执行项目字典ID
        /// </summary>
        public int? InterventionID { get; set; }
        /// <summary>
        /// 自定义执行项目名称
        /// </summary>
        public string LocalShowName { get; set; }
        /// <summary>
        /// 计划月份
        /// </summary>
        public int[] PlanMonths { get; set; }
        /// <summary>
        /// 负责人ID集合
        /// </summary>
        public string[] PrincipalIDs { get; set; } = [];
        /// <summary>
        /// 当分组名称为空时，前端列呈现此字段值
        /// </summary>
        public string PrincipalName { get; set; }
        /// <summary>
        /// 分组名称
        /// </summary>
        public string PrincipalGroupName { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
    }

}
