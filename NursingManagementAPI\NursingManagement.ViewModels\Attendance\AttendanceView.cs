﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 考勤记录
    /// </summary>
    public class AttendanceView
    {
        /// <summary>
        /// 语言序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门编码
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 人员编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 考勤年份
        /// </summary>
        public int AttendanceYear { get; set; }
        /// <summary>
        /// 考勤月份
        /// </summary>
        public int AttendanceMonth { get; set; }
        /// <summary>
        /// 来源
        /// </summary>
        public string SourceID { get; set; }
        /// <summary>
        /// 人员考勤
        /// </summary>
        public List<EmployeeAttendanceView> EmployeeAttendanceList { get; set; }
        /// <summary>
        /// 考勤数据
        /// </summary>
        public TableView AttendanceTable { get; set; }
        /// <summary>
        /// 审批标记
        /// </summary>
        public bool ApproveFlag { get; set; }
        /// <summary>
        /// 排班审核主记录ID
        /// </summary>
        public string AttendanceApproveRecordID { get; set; }
    }
}
