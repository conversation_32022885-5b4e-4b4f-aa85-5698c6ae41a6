﻿namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划目标
    /// </summary>
    [Serializable]
    public class AnnualPlanMainGoalInfo : MutiModifyInfo
    {
        /// <summary>
        /// 部门年度计划目标GuID,主键
        /// </summary>
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 年度计划主表主键Guid，对应各部门年度计划主表
        /// </summary>
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 年度计划目标，从目标字典获取
        /// </summary>
        public int AnnualGoalID { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 年度计划目标类别，对应类别字典
        /// </summary>
        public int AnnualPlanTypeID { get; set; }
        /// <summary>
        /// 目标序号,呈现顺序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 导航属性，关联到分组表
        /// </summary>
        public ICollection<AnnualPlanGoalGroupInfo> PlanGroups { get; set; }
    }
}
