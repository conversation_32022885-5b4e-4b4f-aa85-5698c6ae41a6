﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels.CacheView;

namespace NursingManagement.Data.Repository
{
    public class DynamicFormDetailAttributeRepository : IDynamicFormDetailAttributeRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        private readonly IDynamicFormDetailRepository _dynamicFormDetailRepository;
        public DynamicFormDetailAttributeRepository(
            NursingManagementDbContext db,
            IRedisService redisService
            , SessionCommonServer sessionCommonServer
            , IDynamicFormDetailRepository dynamicFormDetailRepository)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
        }


        public async Task<List<DynamicFormDetailAttributeInfo>> GetDetailAttributeListByDetailID(string dynamicFormRecordID, string dynamicFormDetailID)
        {
            var result = await GetDetailAttributeListByRecordID(dynamicFormRecordID);
            return result.Where(m => m.DynamicFormDetailID == dynamicFormDetailID).ToList();
        }

        public async Task<List<DynamicFormDetailAttributeInfo>> GetDetailAttributeListByRecordID(string dynamicFormRecordID)
        {
            dynamic cacheQuery = new DynamicDictionary();
            cacheQuery.DynamicFormRecordID = dynamicFormRecordID;
            return await GetCacheAsync(cacheQuery) as List<DynamicFormDetailAttributeInfo>;
        }

        /// <summary>
        /// 根据dynamicFormDetailIDList获取数据
        /// </summary>
        /// <param name="dynamicFormDetailIDs"></param>
        /// <returns></returns>
        public async Task<List<DynamicFormDetailAttributeInfo>> GetDetailAttributeListByDetailIDList(List<string> dynamicFormDetailIDs)
        {
            var datas = new List<DynamicFormDetailAttributeInfo>();
            foreach (var item in dynamicFormDetailIDs)
            {
                dynamic cacheQuery = new DynamicDictionary();
                cacheQuery.DynamicFormDetailID = item;
                var datasTemp = await GetCacheAsync(cacheQuery) as List<DynamicFormDetailAttributeInfo>;
                datas.AddRange(datasTemp);
            }
            return datas;
        }

        /// <summary>
        /// 获取评估模板中最大分数（追踪考核使用）
        /// </summary>
        /// <param name="dynamicFormDetailIDs"></param>
        /// <returns></returns>
        public async Task<int?> GetDynamicFormMaxScoreByDynamicFormDetailIDs(List<string> dynamicFormDetailIDs)
        {
            var datas = new List<DynamicFormDetailAttributeInfo>();
            foreach (var item in dynamicFormDetailIDs)
            {
                dynamic cacheQuery = new DynamicDictionary();
                cacheQuery.DynamicFormDetailID = item;
                var datasTemp = await GetCacheAsync(cacheQuery) as List<DynamicFormDetailAttributeInfo>;
                datas.AddRange(datasTemp);
            }
            //走在索引后再二次查询
            var maxScoreAttribute = datas.Where(m => dynamicFormDetailIDs.Contains(m.DynamicFormDetailID) && m.ComponentAttributeID == 36 && !string.IsNullOrEmpty(m.AttributeValue));
            if (!maxScoreAttribute.Any())
            {
                return null;
            }
            var maxScore = maxScoreAttribute.Select(m =>
            {
                bool success = int.TryParse(m.AttributeValue, out int score);
                return new { Score = score, Success = success };
            }).Where(m => m.Success).Max(m => m.Score);
            return maxScore;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var dict = new Dictionary<string, object>();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            if (query != null)
            {
                if (query is DynamicDictionary queryTemp && queryTemp.HasMember("DynamicFormRecordID"))
                {
                    dict = new Dictionary<string, object>
                    {
                        { "DynamicFormRecordID",query.DynamicFormRecordID }
                    };
                    key = key+"_" + query.DynamicFormRecordID;
                }
            }
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await GetDataBaseListData(dict);
                return result;

            });
            return datas;
        }

        private async Task<List<DynamicFormDetailAttributeInfo>> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("DynamicFormRecordID", out var DynamicFormRecordID);

            if (DynamicFormRecordID == null)
            {
                return await _nursingManagementDbContext.DynamicFormDetailAttributeInfos.Where(m => m.DeleteFlag != "*").Select(m =>
                new DynamicFormDetailAttributeInfo
                {
                    DynamicFormDetailID = m.DynamicFormDetailID,
                    DynamicFormDetailAttributeID = m.DynamicFormDetailAttributeID,
                    ComponentAttributeID = m.ComponentAttributeID,
                    AttributeValue = m.AttributeValue,
                    AddEmployeeID = m.AddEmployeeID,
                    AddDateTime = m.AddDateTime
                }).ToListAsync();
            }
            else
            {
                var dynamicFormDetailIDs = await _dynamicFormDetailRepository.GetFormDetailIDByFormRecordID(DynamicFormRecordID.ToString());
                return await _nursingManagementDbContext.DynamicFormDetailAttributeInfos.Where(m => dynamicFormDetailIDs.Contains(m.DynamicFormDetailID) && m.DeleteFlag != "*").Select(m =>
                 new DynamicFormDetailAttributeInfo
                 {
                     DynamicFormDetailID = m.DynamicFormDetailID,
                     DynamicFormDetailAttributeID = m.DynamicFormDetailAttributeID,
                     ComponentAttributeID = m.ComponentAttributeID,
                     AttributeValue = m.AttributeValue,
                     AddEmployeeID = m.AddEmployeeID,
                     AddDateTime = m.AddDateTime
                 }).ToListAsync();
            }
        }


        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.DynamicFormDetailAttribute.GetKey(_sessionCommonServer);
        }
        public async Task UpdateCacheByQuery(Dictionary<string, object> query)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var cacheName = "";
            if (query != null)
            {
                cacheName = GetDictionaryValue(query, "CacheName");
            }
            if (!string.IsNullOrEmpty(cacheName))
            {
                key = key+"_" + cacheName;
                await _redisService.RemoveCacheByNameAsync(key, hospitalID, language);
            }
        }
        /// <summary>
        /// 获取字典值
        /// </summary>
        /// <param name="query"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        private static string GetDictionaryValue(Dictionary<string, object> query, string key)
        {
            if (query.ContainsKey(key))
            {
                return query[key].ToString();
            }
            return "";
        }
    }
}
