﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 排班控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/scheduling")]
    [EnableCors("any")]
    public class SchedulingController : Controller
    {
        /// <summary>
        /// 引用
        /// </summary>
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IShiftSchedulingService _shiftSchedulingService;
        private readonly ISchedulingRequestService _schedulingRequestService;
        private readonly IAdjustScheduleService _adjustScheduleService;
        private readonly IRemainingRestDaysService _remainingRestDaysService;
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="session"></param>
        /// <param name="shiftSchedulingService"></param>
        /// <param name="schedulingRequestService"></param>
        /// <param name="adjustScheduleService"></param>
        /// <param name="remainingRestDaysService"></param>
        /// <returns></returns>
        public SchedulingController(
            ISessionService session
            , IShiftSchedulingService shiftSchedulingService
            , ISchedulingRequestService schedulingRequestService
            , IAdjustScheduleService adjustScheduleService
            , IRemainingRestDaysService remainingRestDaysService
        )
        {
            _session = session;
            _shiftSchedulingService = shiftSchedulingService;
            _schedulingRequestService = schedulingRequestService;
            _adjustScheduleService = adjustScheduleService;
            _remainingRestDaysService = remainingRestDaysService;
        }

        #region 排班预约相关接口
        /// <summary>
        /// 获取单人排班预约数据
        /// </summary>
        [HttpGet]
        [Route("GetSingleSchedulingRequest")]
        public async Task<IActionResult> GetSingleSchedulingRequest(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingRequestService.GetSingleSchedulingData(employeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取科室排班预约数据
        /// </summary>
        [HttpGet]
        [Route("GetDepartmentSchedulingRequest")]
        public async Task<IActionResult> GetDepartmentSchedulingRequest(int? departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            departmentID ??= session.DepartmentID;
            result.Data = await _schedulingRequestService.GetDepartmentSchedulingData(departmentID.Value);
            return result.ToJson();
        }
        /// <summary>
        /// 保存排班预约数据
        /// </summary>
        /// <param name="saveData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveSchedulingRequestRecord")]
        public async Task<IActionResult> SaveSchedulingRequestRecord([FromBody] SchedulingRequestRecordView saveData)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(saveData, session);
            result.Data = await _schedulingRequestService.SaveSchedulingRequestRecord(saveData);
            return result.ToJson();
        }
        /// <summary>
        /// 删除排班预约数据
        /// </summary>
        /// <param name="schedulingRequestRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteScheduleRequestRecord")]
        public async Task<IActionResult> DeleteScheduleRequestRecord(string schedulingRequestRecordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingRequestService.DeleteScheduleRequestRecord(schedulingRequestRecordID, session.EmployeeID);
            return result.ToJson();
        }
        #endregion

        #region 调班申请相关接口
        /// <summary>
        /// 获取调班申请记录
        /// </summary>
        /// <param name="employeeID">员工工号</param>
        /// <param name="departmentID">部门科室ID</param>
        /// <param name="deptSwitch">是否获取全科数据</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAdjustScheduleRecord")]
        public async Task<IActionResult> GetAdjustScheduleRecord(string employeeID, int? departmentID, bool deptSwitch)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _adjustScheduleService.GetAdjustScheduleRecordsAsync(employeeID, departmentID, deptSwitch);
            result.Code = 1;
            return result.ToJson();
        }
        /// <summary>
        /// 新增保存调班申请记录
        /// </summary>
        /// <param name="paramView">调班申请前端请求参数</param>
        /// <returns>布尔值：成功标记</returns>
        [HttpPost]
        [Route("SaveAdjustScheduleRecord")]
        public async Task<IActionResult> SaveAdjustScheduleRecord([FromBody] AdjustScheduleParamView paramView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _adjustScheduleService.SaveAdjustScheduleRecordAsync(paramView, session.HospitalID, session.EmployeeID, session.DepartmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除调班申请记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteAdjustScheduleRecord")]
        public async Task<IActionResult> DeleteAdjustScheduleRecord([FromBody] DeleteAdjustScheduleRecordView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            var success = await _adjustScheduleService.DeleteAdjustScheduleRecordAsync(view.adjustScheduleRecordID, session.EmployeeID);
            if (success)
            {
                result.Code = 1;
                result.Data = success;
                return result.ToJson();
            }
            result.Data = success;
            return result.ToJson();
        }
        #endregion

        #region 排班相关接口
        /// <summary>
        /// 获取部门时间段内的排班数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="schedulingType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="statusCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetShiftSchedulingData")]
        public async Task<IActionResult> GetShiftSchedulingData(int departmentID, string schedulingType, DateTime startDate, DateTime endDate, string statusCode)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.GetShiftSchedulingData(departmentID, schedulingType, startDate, endDate, statusCode);
            return result.ToJson();
        }

        /// <summary>
        ///  获取个人排班信息
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetShiftSchedulingByEmployeeID")]
        public async Task<IActionResult> GetShiftSchedulingByEmployeeID(int departmentID, DateTime startDate, DateTime endDate, string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.GetShiftSchedulingByEmployeeID(departmentID, startDate, endDate, employeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存排班数据
        /// </summary>
        /// <param name="shiftSchedulingView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveShiftSchedulingData")]
        public async Task<IActionResult> SaveShiftSchedulingData([FromBody] ShiftSchedulingView shiftSchedulingView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            shiftSchedulingView.HospitalID = session.HospitalID;
            shiftSchedulingView.EmployeeID = session.EmployeeID;
            result.Data = await _shiftSchedulingService.SaveShiftSchedulingData(shiftSchedulingView);
            return result.ToJson();
        }

        /// <summary>
        /// 更新排班人员排序
        /// </summary>
        /// <param name="shiftSchedulingEmployeeSortInfos"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateSchedulingEmployeeSort")]
        public async Task<IActionResult> UpdateSchedulingEmployeeSort([FromBody] List<ShiftSchedulingEmployeeSortInfo> shiftSchedulingEmployeeSortInfos)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.UpdateSchedulingEmployeeSort(shiftSchedulingEmployeeSortInfos, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取智能排班相关参数
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetShiftSchedulingParameter")]
        public async Task<IActionResult> GetShiftSchedulingParameter(int departmentID, DateTime startDate, DateTime endDate)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.GetShiftSchedulingParameter(departmentID, startDate, endDate);
            return result.ToJson();
        }

        /// <summary>
        /// 获取员工指定日期的排班
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="datetime"></param>
        /// <param name="employeeID"></param>
        /// <param name="noonType"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSchedulingPostByEmployeeID")]
        public async Task<IActionResult> GetSchedulingPostByEmployeeID(int departmentID, DateTime datetime, string employeeID, string noonType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.GetSchedulingPostByEmployeeID(departmentID, datetime, employeeID, noonType);
            return result.ToJson();
        }
        /// <summary>
        /// 获取部门时间段内的排班数据【同步数据使用】
        /// </summary>
        /// <param name="schedulingRecordID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetShiftSchedulingDataByRecordID")]
        public async Task<IActionResult> GetShiftSchedulingDataByRecordID(string schedulingRecordID)
        {
            var result = new ResponseResult();

            result.Data = await _shiftSchedulingService.GetShiftSchedulingDataByRecordID(schedulingRecordID);
            return result.ToJson();
        }

        /// <summary>
        /// 复制上个月/周排班
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="schedulingType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CopyScheduling")]
        public async Task<IActionResult> CopyScheduling(int departmentID, string schedulingType, DateTime startDate, DateTime endDate)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var ret = await _shiftSchedulingService.CopyScheduling(departmentID, schedulingType, startDate, endDate);
            if (string.IsNullOrWhiteSpace(ret))
            {
                result.Data = "复制成功";
            }
            else
            {
                result.Error(ret);
            }
            return result.ToJson();
        }
        #endregion
        /// <summary>
        /// 获取部门人员剩余休假天数数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRemainingRestDaysData")]
        public async Task<IActionResult> GetRemainingRestDaysData(int year, int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _remainingRestDaysService.GetRemainingRestDaysData(year, departmentID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存部门人员剩余休假天数数据
        /// </summary>
        /// <param name="saveViews">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveRemainingRestDaysData")]
        public async Task<IActionResult> SaveRemainingRestDaysData([FromBody] List<RemainingRestDaysView> saveViews)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _remainingRestDaysService.SaveRemainingRestDaysData(saveViews, session);
            return result.ToJson();
        }
        /// <summary>
        /// 获取排班标记设定
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSchedulingMarkSettings")]
        public async Task<IActionResult> GetSchedulingMarkSettings(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.GetSchedulingMarkSettings(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取排班标记设定
        /// </summary>
        /// <param name="schedulingMarks">标记集合</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveSchedulingMarkSettings")]
        public async Task<IActionResult> SaveSchedulingMarkSettings([FromBody] SchedulingMarkSettingsView schedulingMarks)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.SaveSchedulingMarkSettings(schedulingMarks);
            return result.ToJson();
        }
        /// <summary>
        /// 删除排班标记设定
        /// </summary>
        /// <param name="deleteScheduling">标记主键</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteSchedulingMarkSettings")]
        public async Task<IActionResult> DeleteSchedulingMarkSettings([FromBody] DeleteSchedulingMarkSettingsView deleteScheduling)
        {
            var result = new ResponseResult();

            result.Data = await _shiftSchedulingService.DeleteSchedulingMarkSettings(deleteScheduling);
            return result.ToJson();
        }
        /// <summary>
        /// 查询排班规则
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetShiftSchedulingRules")]
        public async Task<IActionResult> GetShiftSchedulingRules(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _shiftSchedulingService.GetShiftSchedulingRules(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存排班规则
        /// </summary>
        /// <param name="shiftSchedulingRule"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveShiftSchedulingRule")]
        public async Task<IActionResult> SaveShiftSchedulingRule([FromBody] ShiftSchedulingRuleView shiftSchedulingRule)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var data = await _shiftSchedulingService.SaveShiftSchedulingRule(shiftSchedulingRule);
            if (data)
            {
                result.Data = data;
            }
            else
            {
                result.Error("保存失败！");
            }
            return result.ToJson();
        }
    }
}
