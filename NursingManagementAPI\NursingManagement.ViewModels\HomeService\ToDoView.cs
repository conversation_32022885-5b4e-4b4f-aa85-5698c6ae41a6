﻿namespace NursingManagement.ViewModels.HomeService
{
    public class ToDoView
    {
        /// <summary>
        /// 待办项目名称
        /// </summary>
        public string ToDoContent { get; set; }
        /// <summary>
        /// 待办路由ID
        /// </summary>
        public int RouterListID { get; set; }
        /// <summary>
        /// 待办项目数量
        /// </summary>
        public int Counts { get; set; }
        /// <summary>
        /// 待办内容集合
        /// </summary>
        public List<ToDoDetailView> ToDoList { get; set; }
        /// 待办项目跳转路径
        /// </summary>
        public string RouterPath { get; set; }
    }
    /// <summary>
    /// 待办明细内容
    /// </summary>
    public class ToDoDetailView
    {
        /// <summary>
        /// 待办的计划执行时间
        /// </summary>
        public DateTime ScheduleDateTime { get; set; }
        /// <summary>
        /// 措施ID
        /// </summary>
        public int? InterventionID { get; set; }
        /// <summary>
        ///  待办项目标题
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 审批类别码
        /// </summary>
        public string ProveCategory { get; set; }
    }
}
