﻿namespace NursingManagement.ViewModels
{
    public class AnnualPlanTypeListView
    {
        /// <summary>
        /// 年度计划类别序号，非自增
        /// </summary>
        public int AnnualPlanTypeID { get; set; }
        /// <summary>
        /// 年度计划类别名称
        /// </summary>
        public string AnnualPlanTypeContent { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 制定部门
        /// </summary>
        public string AddDepartmentName { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
    }
}
