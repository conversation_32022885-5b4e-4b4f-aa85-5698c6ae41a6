﻿using Microsoft.AspNetCore.Http;

namespace NursingManagement.ViewModels
{
    public class CourseSettingView
    {
        /// <summary>
        /// 课程主键ID
        /// </summary>
        public string CourseSettingID { get; set; }

        /// <summary>
        /// 课程分类ID
        /// </summary>
        public string CourseTypeID { get; set; }

        /// <summary>
        /// 课程分类名称
        /// </summary>
        public string CourseTypeName { get; set; }

        /// <summary>
        /// 课程名称
        /// </summary>
        public string CourseName { get; set; }

        /// <summary>
        /// 课程简介
        /// </summary>
        public string CourseIntroduction { get; set; }

        /// <summary>
        /// 新增人员姓名
        /// </summary>
        public string AddEmployeeName { get; set; }

        /// <summary>
        /// 修改人员姓名
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 父课程ID
        /// </summary>
        public string ParentID { get; set; }

        /// <summary>
        /// 子课程集合
        /// </summary>
        public List<CourseSettingView> ChildCourses { get; set; } = new List<CourseSettingView>();

        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }

        /// <summary>
        /// 课程制定的年份
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 新增人员的部门ID
        /// </summary>
        public int AddDepartmentID { get; set; }

        /// <summary>
        /// 展开子树的标识
        /// </summary>
        public bool ExpandTree { get; set; }

        /// <summary>
        /// 课程的级别
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 新增人员的工号
        /// </summary>
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 文件内容
        /// </summary>
        public List<FileUploadReturnView> FileInfoList { get; set; }

        /// <summary>
        /// 上传的文件集合（IFormFile 类型）
        /// </summary>
        public List<IFormFile> Files { get; set; } = new List<IFormFile>();
        /// <summary>
        /// 上传文件ID集合
        /// </summary>
        public List<string> FileID  { get; set; }
    }

}
