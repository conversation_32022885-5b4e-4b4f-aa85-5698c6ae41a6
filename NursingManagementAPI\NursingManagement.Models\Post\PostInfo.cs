﻿using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  Post    
    /// </summary>
    [Table("Post")]
    public class PostInfo : ModifyInfo
    {
        /// <summary>
        /// 岗位编号，主键    
        /// </summary>
        public int PostID { get; set; }

        /// <summary>
        /// 医院序号，主键    
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言序号，主键    
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 岗位名称    
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string PostName { get; set; }

        /// <summary>
        /// 岗位类别    
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string PostTypeID { get; set; }

        /// <summary>
        /// 岗位性质    
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string PostNatureID { get; set; }
        
        /// <summary>
        /// 创建人，HREmployeeID    
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 创建时间    
        /// </summary>
        public DateTime AddDateTime { get; set; }

        /// <summary>
        /// 排序    
        /// </summary>
        public int Sort { get; set; }
    }
}
