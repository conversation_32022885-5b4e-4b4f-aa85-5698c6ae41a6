﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IAttendanceApproveRecordRepository
    {
        /// <summary>
        ///  获取部门审核状态表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        Task<List<AttendanceApproveRecordInfo>> GetRecordByDepartmentID(int departmentID, int attendanceYear, int attendanceMonth);
        /// <summary>
        /// 根据ID获取部门审核状态表
        /// </summary>
        /// <param name="attendanceApproveRecordID"></param>
        /// <returns></returns>
        Task<AttendanceApproveRecordInfo> GetRecordByRecordID(string attendanceApproveRecordID);
        /// <summary>
        /// 获取审核最新状态
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        Task<AttendanceApproveRecordInfo> GetRecord(int departmentID, int attendanceYear, int attendanceMonth);
        /// <summary>
        /// 根据主键获取记录
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<AttendanceApproveRecordInfo>> GetRecordsByIDAsNoTrackAsync(List<string> recordIDs);
    }
}
