﻿namespace NursingManagement.ViewModels
{
    public class SchedulingRequestRecordView
    {
        /// <summary>
        /// 预约排班开始日期，必须晚于当月时间
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 预约排班结束日期，必须晚于当月时间
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 预约排班开始日期，必须晚于当月时间
        /// </summary>
        public string StartDateFormat { get; set; }
        /// <summary>
        /// 预约排班结束日期，必须晚于当月时间
        /// </summary>
        public string EndDateFormat { get; set; }
        /// <summary>
        /// 预约类型
        /// </summary>
        public string PostType { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public string AddDateTime { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 部门岗位ID
        /// </summary>
        public int DepartmentPostID { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 添加人ID
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 修改人ID
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 开始午别
        /// </summary>
        public string StartNoon { get; set; }
        /// <summary>
        /// 开始午别
        /// </summary>
        public string StartNoonName { get; set; }
        /// <summary>
        /// 结束午别
        /// </summary>
        public string EndNoon { get; set; }
        /// <summary>
        /// 结束午别
        /// </summary>
        public string EndNoonName { get; set; }
        /// <summary>
        /// 表主键
        /// </summary>
        public string SchedulingRequestRecordID { get; set; }
        /// <summary>
        /// 天数
        /// </summary>
        public decimal? Days { get; set; }
        /// <summary>
        /// 申请原因
        /// </summary>
        public string Reason { get; set; }
        /// <summary>
        /// 审批记录ID
        /// </summary>
        public string ApproveRecordID { get; set; }
        /// <summary>
        /// 撤销原因
        /// </summary>
        public string RevokeReason { get; set; }
        /// <summary>
        /// 审批标记
        /// </summary>
        public bool ApproveFlag { get; set; }
    }
}
