﻿using NursingManagement.Data.Interface;

namespace NursingManagement.Services
{
    public class AttendanceDynamicColumn
    {
        private readonly IAttendanceApproveRecordRepository _attendanceApproveRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;

        public AttendanceDynamicColumn(
            IAttendanceApproveRecordRepository attendanceApproveRecordRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IDepartmentListRepository departmentListRepository)
        {
            _attendanceApproveRecordRepository = attendanceApproveRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentListRepository = departmentListRepository;
        }
        /// <summary>
        /// 根据主记录获取对应的审批动态列数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetDynamicColumnListByRecordIDAsync(List<string> recordIDs, string proveCategory)
        {
            var recordLists = await _attendanceApproveRecordRepository.GetRecordsByIDAsNoTrackAsync(recordIDs);
            if (recordLists.Count <= 0)
            {
                return null;
            }
            List<Dictionary<string, object>> resultList = new();
            var employeeDict = await _employeePersonalDataRepository.GetDataByEmployeeIDs(recordLists.Select(m => m.AddEmployeeID).ToList());
            var department = await _departmentListRepository.GetAllDictAsync();
            foreach (var item in recordLists)
            {
                var view = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase)
                {
                    { "SourceID", item.AttendanceApproveRecordID },
                    { "DepartmentName", department.Find(m => m.DepartmentID == item.DepartmentID)?.DepartmentContent },
                    { "ProveCategory", proveCategory },
                    { "AddEmployeeID", item.AddEmployeeID },
                    { "AttendanceYear", item.AttendanceYear },
                    { "AttendanceMonth", item.AttendanceMonth },
                    { "SubmitEmployeeName", employeeDict.TryGetValue(item.AddEmployeeID, out var name) ? name : item.AddEmployeeID }
                };

                resultList.Add(view);
            }
            return resultList;
        }
    }
}
