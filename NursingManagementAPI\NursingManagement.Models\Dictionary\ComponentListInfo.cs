using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 前端页面组件表
    /// </summary>
    [Serializable]
    [Table("ComponentList")]
    public class ComponentListInfo : MutiModifyInfo
    {

        /// <summary>
        /// 路由使用组件ID
        /// </summary>
        public int ComponentListID { get; set; }
        /// <summary>
        /// 件类型Template：模板类，Authority：权限控制类，DynamicForm：动态表单组件
        /// </summary>
        [Column(TypeName = "verchar(30)")]
        public string ComponentType { get; set; }
        /// <summary>
        /// 前端呈现控件类型
        /// </summary>
        [Column(TypeName = "verchar(30)")]
        public string ControlerType { get;set;}

        /// <summary>
        /// 说明
        /// </summary>
        [Column(TypeName = "nverchar(200)")]
        public string Description { get; set; }
    }
}
