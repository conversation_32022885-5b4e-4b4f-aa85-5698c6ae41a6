﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IRouterService
    {
        /// <summary>
        ///  根据角色获取权限路由和组件
        /// </summary>
        /// <returns></returns>
        Task<RouterAndComponentView> GetRouterAndComponentListByRoles(List<int> roles, int clientType);

        // <summary>
        ///  根据角色获取权限路由的组件
        /// </summary>
        /// <returns></returns>
        Task<List<ComponentView>> GetComponentListByRoles(List<int> roles, int clientType);
    }
}