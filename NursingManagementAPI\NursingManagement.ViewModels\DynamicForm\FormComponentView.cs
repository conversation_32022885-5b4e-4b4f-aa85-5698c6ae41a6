﻿namespace NursingManagement.ViewModels
{
    public class FormComponentView
    {
        /// <summary>
        /// 表单属性
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 组件项目ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 组件项目ID
        /// </summary>
        public string ItemID { get; set; }
        /// <summary>
        /// 项目来源字典，这里放字典表名
        /// </summary>
        public string ItemSourceType { get; set; }        
        /// <summary>
        /// 组件属性
        /// </summary>
        public Dictionary<string, object> Props { get; set; }
        /// <summary>
        /// 是否为布局组件
        /// </summary>
        public bool IsLayout { get; set; }
        /// <summary>
        /// 子组件集合
        /// </summary>
        public List<FormComponentView> Children { get; set; }
        /// <summary>
        /// 业务需求的固定项目ID
        /// </summary>
        public string FixedItemID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        
    }
}
