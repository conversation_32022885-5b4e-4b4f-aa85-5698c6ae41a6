﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeCapabilityLevelRepository
    {
        /// <summary>
        /// 获取工号 护理岗位层级
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeCapabilityLevelInfo>> GetRecordListAsync(string employeeID);

        /// <summary>
        /// 获取人员当前能力层级
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="capabilityIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeCapabilityLevelInfo>> GetCurrentCapabilityLevelView(string hospitalID, string[] employeeIDs, int[] capabilityIDs);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeCapabilityLevelInfo>> GetRecordListByEmployeeIDs(List<string> employeeIDs);
    }
}