﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IApproveRecordService
    {
        /// <summary>
        /// 根据审批主记录获取审批呈现内容
        /// </summary>
        /// <param name="approveMainRecordID"></param>     
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        Task<List<ApproveView>> GetApproveDetailView(string approveMainRecordID, string employeeID);
        /// <summary>
        /// 添加审批记录（主记录和明细记录）
        /// </summary>
        /// <param name="view"></param>
        /// <param name="approveProcessID">流程ID</param>
        /// <returns></returns>
        Task<bool> AddApproveRecordAndDetailAsync(ApproveMainAndDetailParamView view, string approveProcessID);

        /// <summary>
        /// 获取需要当前登录账号审批的审批记录
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="departmentID">人员所在科室ID</param>
        /// <param name="proveCategory">审批类别码</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        Task<List<dynamic>> GetApproveRecordViewAsync(string employeeID, int departmentID, string proveCategory, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 保存审批结果
        /// </summary>
        /// <param name="approveParams"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SaveApprovalAsync(ApproveDetailInfo approveParams, string employeeID);
        /// <summary>
        /// 获取审批过的历史申请
        /// </summary>
        /// <param name="employeeID"></param>   
        /// <param name="proveCategory">审批类别码</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        Task<List<dynamic>> GetHistoryApproveRecordByEmployeeIDAsync(string employeeID, string proveCategory, int departmentID, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 根据所传对象，替换申请模板，返回申请内容
        /// </summary>
        /// <param name="contentTemplate">流程存储的申请模板</param>
        /// <param name="view">业务View，存储了相关数据用以替换</param>
        /// <returns></returns>
        Task<string> GetApproveContentByBizView(string contentTemplate, dynamic view);

        /// <summary>
        /// 停止未开始的审批
        /// </summary>
        /// <param name="sourceID">审批来源ID</param>
        /// <param name="employeeID">操作人工号</param>
        /// <param name="saveChange">是否保存</param>
        /// <returns></returns>
        Task<bool> StopApprovalAsync(string sourceID, string employeeID, bool saveChange = false);
        /// <summary>
        /// 撤销审批
        /// </summary>
        /// <param cref="ApprovalRevokeParamsView">审批撤销参数</param>
        /// <param name="employeeID">执行人工号</param>
        /// <returns></returns
        Task<bool> RevokeApprovalAsync(ApprovalRevokeParamsView view, string employeeID);
        /// <summary>
        /// 根据工号获取对方没有审批的记录
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<object> GetUnApprovalRecordAsync(string employeeID);
        /// <summary>
        /// 获取审批类型及相关审批记录数量
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="isCompleteFlag">是否已审批</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<MessageCountView<string>>> GetApproveCategoryAndCount(string employeeID, int departmentID,bool isCompleteFlag, DateTime? startDate, DateTime? endDate);
    }
}