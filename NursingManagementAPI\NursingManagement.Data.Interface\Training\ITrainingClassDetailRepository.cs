﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ITrainingClassDetailRepository
    {
        /// <summary>
        /// 根据trainingClassMainID获取培训群组明细集合
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        Task<List<TrainingClassDetailInfo>> GetListByMainID(string trainingClassMainID);

        /// <summary>
        /// 根据trainingClassMainIDs获取培训群组明细集合
        /// </summary>
        /// <param name="trainingClassMainIDs"></param>
        /// <returns></returns>
        Task<List<TrainingClassDetailInfo>> GetListByMainIDs(List<string> trainingClassMainIDs);
        /// <summary>
        /// 根据培训群组主记录ID获取对应的课程ID集合
        /// </summary>
        /// <param name="trainClassMainID">培训群组主记录ID</param>
        /// <returns></returns>
        Task<List<string>> GetCourseSettingIDs(string trainClassMainID);
    }
}