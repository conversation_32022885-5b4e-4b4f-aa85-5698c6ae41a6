using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 排班明细标记表
    /// </summary>
    [Serializable]
    [Table("ShiftSchedulingDetailMark")]
    public class ShiftSchedulingDetailMarkInfo : MutiModifyInfo
    {

        /// <summary>
        /// 排班明细标记记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ShiftSchedulingDetailMarkID { get; set; }
        /// <summary>
        /// 排班主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ShiftSchedulingRecordID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime SchedulingDate { get; set; }
        /// <summary>
        /// 排班人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 标记编码，对应AdministrationIcon表中的唯一码
        /// </summary>
        public int MarkID { get; set; }
        /// <summary>
        /// 标记值
        /// </summary>
        [Column(TypeName = "nvarchar(500)")]
        public string MarkValue { get; set; }
    }
}
