﻿using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class ScheduleRequestDynamicColumn
    {
        private readonly ISchedulingRequestRepository _schedulingRequestRepository;
        private readonly IAdministrationDictionaryRepository _administrationDictionaryRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        public ScheduleRequestDynamicColumn(
            ISchedulingRequestRepository schedulingRequestRepository,
            IAdministrationDictionaryRepository administrationDictionaryRepository,
            IDepartmentPostRepository departmentPostRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            ISettingDictionaryRepository settingDictionaryRepository)
        {
            _schedulingRequestRepository = schedulingRequestRepository;
            _administrationDictionaryRepository = administrationDictionaryRepository;
            _departmentPostRepository = departmentPostRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
        }


        /// <summary>
        /// 根据主键集合获取对应的排班申请记录
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetDynamicColumnListByRecordIDAsync(List<string> recordIDs)
        {
            var schedulingInfos = await _schedulingRequestRepository.GetRecordsByIDAsNoTrackAsync(recordIDs);
            if (schedulingInfos.Count <= 0)
            {
                return null;
            }
            var noonParam = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var noonSetting = await _settingDictionaryRepository.GetSettingDictionary(noonParam);
            var noonPairs = noonSetting.Select(m => new KeyValueString { Key = m.SettingValue.ToString(), Value = m.Description }).ToList();

            var deptPostSetting = await _departmentPostRepository.GetAll<DepartmentPostInfo>();
            var personalDatas = await _employeePersonalDataRepository.GetIDAndNameData();
            List<Dictionary<string, object>> resultList = new();
            foreach (var item in schedulingInfos)
            {
                var view = CreateDynamicViewAsync(item, deptPostSetting, noonPairs, personalDatas);
                resultList.Add(view);
            }
            return resultList;
        }

        /// <summary>
        /// 创建请求参数
        /// </summary>
        /// <param name="requestRecordInfo">申请记录实例</param>
        /// <param name="deptPostSetting"></param>
        /// <param name="noonPairs"></param>
        /// <param name="personalDatas"></param>
        /// <returns></returns>
        private Dictionary<string, object> CreateDynamicViewAsync(SchedulingRequestRecordInfo requestRecordInfo
            , List<DepartmentPostInfo> deptPostSetting, List<KeyValueString> noonPairs, List<EmployeePersonalDataListView> personalDatas)
        {
            return new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase)
            {
                { "sourceID", requestRecordInfo.SchedulingRequestRecordID },
                { "approveRecordID", requestRecordInfo.ApproveRecordID },
                { "proveCategory", "AA-021" },
                { "departmentID", requestRecordInfo.DepartmentID },
                { "addEmployeeID", requestRecordInfo.AddEmployeeID },
                { "startDate", requestRecordInfo.StartDate.ToString("yyyy-MM-dd") },
                { "endDate", requestRecordInfo.EndDate.ToString("yyyy-MM-dd") },
                { "startNoon", noonPairs.Find(m => m.Key == requestRecordInfo.StartNoon)?.Value },
                { "endNoon", noonPairs.Find(m => m.Key == requestRecordInfo.EndNoon)?.Value },
                { "reason", string.IsNullOrEmpty(requestRecordInfo.Reason) ? "无" : requestRecordInfo.Reason },
                { "days",requestRecordInfo.Days },
                { "deptPostName",deptPostSetting.Find(m=>m.DepartmentPostID == requestRecordInfo.DepartmentPostID)?.DepartmentPostName ?? ""},
                { "applicantName",personalDatas.Find(m=>m.EmployeeID == requestRecordInfo.AddEmployeeID)?.EmployeeName ??""}
            };

        }
    }
}
