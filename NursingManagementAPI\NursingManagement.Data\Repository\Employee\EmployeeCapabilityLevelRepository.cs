﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeCapabilityLevelRepository : IEmployeeCapabilityLevelRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeCapabilityLevelRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<List<EmployeeCapabilityLevelInfo>> GetRecordListAsync(string employeeID)
        {
            return await _dbContext.EmployeeCapabilityLevelInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").OrderByDescending(m => m.PromotionDate).ToListAsync();
        }

        public async Task<List<EmployeeCapabilityLevelInfo>> GetCurrentCapabilityLevelView(string hospitalID, string[] employeeIDs, int[] capabilityIDs)
        {
            var result = _dbContext.EmployeeCapabilityLevelInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*" && employeeIDs.Contains(m.EmployeeID));
            if (capabilityIDs != null && capabilityIDs.Length > 0)
            {
                result = result.Where(m => m.CapabilityLevelID != null && capabilityIDs.Contains(m.CapabilityLevelID.Value));
            }
            return await result.Select(m => new EmployeeCapabilityLevelInfo
            {
                EmployeeID = m.EmployeeID,
                CapabilityLevelID = m.CapabilityLevelID,
                PromotionDate = m.PromotionDate
            }
            ).ToListAsync();
        }

        public async Task<List<EmployeeCapabilityLevelInfo>> GetRecordListByEmployeeIDs(List<string> employeeIDs)
        {
            return await _dbContext.EmployeeCapabilityLevelInfos.Where(m => employeeIDs.Contains(m.EmployeeID) && m.DeleteFlag != "*").OrderByDescending(m => m.PromotionDate).ToListAsync();
        }
    }
}