﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    /// 科室对应审批流程表，记录审批流程都在哪些科室启用
    /// </summary>
    [Table("DepartmentToApproveProcess")]
    public class DepartmentToApproveProcessInfo : MutiModifyInfo
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int DepartmentToApproveProcessID { get; set; }
        /// <summary>
        /// 审批流程ID，审批流程表主键GUID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveProcessID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 启用时间
        /// </summary>
        public DateTime? EnableDateTime { get; set; }
        /// <summary>
        /// 禁用时间
        /// </summary>
        public DateTime? DisableDateTime { get; set; }
    }

}
