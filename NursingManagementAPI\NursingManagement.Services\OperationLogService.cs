﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class OperationLogService : IOperationLogService
    {
        private readonly IUnitOfWork _unitOfWork;

        public OperationLogService(
            IUnitOfWork unitOfWork
        )
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> SaveLog(List<OperationLog> operationLogList, Session session)
        {
            if (operationLogList == null || operationLogList.Count <= 0)
            {
                return false;
            }
            var userOperationLogList = new List<OperationLogInfo>();
            var loginType = new Dictionary<string, string>() {
                { "oa", "OAUserID" }, // OA登录
                { "his", "HisUserID" }, // HIS登录
                { "wechat2", "WechatWebOpenID" }, // H5微信登录
                { "wechat3", "WechatMiniProgramOpenID" }, // 小程序登录
            };
            foreach (var operationLog in operationLogList)
            {
                var userOperationLog = new OperationLogInfo()
                {
                    HospitalID = session.HospitalID,
                    LoginType = session.LoginType,
                    ClientType = session.ClientType,
                    Token = session.Token,
                    Title = operationLog.Title,
                    Content = operationLog.Content,
                    Page = operationLog.Page,
                    Api = operationLog.Api,
                    Steps = operationLog.Steps,
                    AddEmployeeID = session.EmployeeID,
                    AddDateTime = operationLog.Time
                };
                // 取session的字段名
                string sessionFields;
                if (session.LoginType == "wechat")
                {
                    sessionFields = loginType[session.LoginType + session.ClientType.ToString()];
                }
                else
                {
                    sessionFields = loginType[session.LoginType];
                }
                if (!string.IsNullOrWhiteSpace(sessionFields))
                {
                    // 通过反射取值
                    userOperationLog.LoginUserID = session.GetType().GetProperty(sessionFields).GetValue(session, null).ToString();
                }
                userOperationLogList.Add(userOperationLog);
            }
            if (operationLogList.Count > 0)
            {
                await _unitOfWork.GetRepository<OperationLogInfo>().InsertAsync(userOperationLogList);
                return await _unitOfWork.SaveChangesAsync() >= 0;
            }
            return false;
        }
    }
}