﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface ISchedulingRequestRepository
    {
        /// <summary>
        /// 获取单人所有排班预约数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<SchedulingRequestRecordInfo>> GetSingleSchedulingAsync(string employeeID);
        /// <summary>
        /// 获取科室派班预约数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<SchedulingRequestRecordInfo>> GetDepartmentSchedulingAsync(int departmentID);
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="schedulingRequestRrecordID"></param>
        /// <returns></returns>
        Task<SchedulingRequestRecordInfo> GetDataByID(string schedulingRequestRecordID);
        /// <summary>
        /// 获取时间段内的申请
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<SchedulingRequestRecordInfo>> GetSchedulingRequestByDate(int departmentID, DateTime startDate, DateTime endDate);
        /// <summary>
        /// 获取该部门某日的排班预约数据View
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startDate">开始日期</param>
        /// <returns></returns>
        Task<ApproveReqView[]> GetViewsByDepartmentIDAndStartDate(int departmentID, DateTime startDate);
        /// <summary>
        /// 获取该员工的排班预约记录
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <returns></returns>
        Task<ApproveReqView[]> GetViewsByEmployee(string employeeID);
        /// <summary>
        /// 获取该时间点之后的所有排班预约记录
        /// </summary>
        /// <param name="addDateTime">添加时间</param>
        /// <returns></returns>
        Task<List<SchedulingRequestRecordInfo>> GetRecordsByAddDate(DateTime addDateTime);
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<SchedulingRequestRecordInfo>> GetRecordsByIDAsNoTrackAsync(List<string> recordIDs);
    }
}
