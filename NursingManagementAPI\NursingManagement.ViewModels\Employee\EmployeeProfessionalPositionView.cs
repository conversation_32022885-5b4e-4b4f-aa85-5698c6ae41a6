﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NursingManagement.ViewModels.Employee
{
    /// <summary>
    /// 人员职称视图
    /// </summary>
    public class EmployeeProfessionalPositionView
    {
        public string EmployeeProfessionalPositionID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 专业分类(GB/T 8561-2001)职务系列名称(250:卫生技术人员(护理)转换后)
        /// </summary>
        public string ProfessionalType { get; set; }

        /// <summary>
        /// 专业等级(Professional)系列等级名称(251主任护师、252副主任、253主管、254护师、255护士转换后)
        /// </summary>
        public string ProfessionalLevel { get; set; }

        /// <summary>
        /// 获得职称时间
        /// </summary>
        public string ObtainingDate { get; set; }

        /// <summary>
        /// 证书编号
        /// </summary>
        public string CertificateNo { get; set; }
    }
}
