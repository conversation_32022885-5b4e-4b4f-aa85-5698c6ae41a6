﻿namespace NursingManagement.ViewModels.Examine
{
    public class PaperCompositionRuleView
    {
        /// <summary>
        /// 试卷主记录ID
        /// </summary>
        public string ExaminationPaperMainID { get; set; }

        /// <summary>
        /// 选择题库
        /// </summary>
        public List<string> QuestionBankIDList { get; set; }

        /// <summary>
        /// 选择规则内容
        /// </summary>
        public List<KeyValueString> RuleKeyValueList { get; set; }

        /// <summary>
        /// 试卷说明
        /// </summary>
        public string PaperTitle { get; set; }

        /// <summary>
        /// 异动人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 及格分
        /// </summary>
        public decimal? PassingScore { get; set; }

        /// <summary>
        /// 组卷规则
        /// </summary>
        public PaperFilterQuestionConditionRuleView PaperFilterQuestionConditionRuleView { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 组卷条件记录ID
        /// </summary>
        public string ExaminationConditionRecordID { get; set; }

        /// <summary>
        /// 试卷组卷模式
        /// </summary>
        public string PaperQuestionMode { get; set; }
    }
}
