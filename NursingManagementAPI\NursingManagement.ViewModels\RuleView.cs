﻿namespace NursingManagement.ViewModels
{
    public class RuleView
    {
        /// <summary>
        /// 规则属性主表ID（非自增）
        /// </summary>
        public int RuleListID { get; set; }

        /// <summary>
        /// 规则呈现名称
        /// </summary>
        /// s
        public string ShowName { get; set; }

        /// <summary>
        /// 规则属性表述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 规则前端值呈现控件类型ID
        /// </summary>
        public int ComponentListID { get; set; }

        /// <summary>
        /// 规则前端值呈现控件类型(配置在ComponentList)
        /// </summary>
        public string ComponentType { get; set; }

        /// <summary>
        /// 默认值（当默认值非文本类型时，需要判断字典明细表是否存在）
        /// </summary>
        public string DefaultValue { get; set; }

        /// <summary>
        /// 使用系统（培训、考核、排班）
        /// </summary>
        public string SystemType { get; set; }

        /// <summary>
        /// 使用系统（培训、考核、排班）
        /// </summary>
        public string SystemTypeName { get; set; }

        /// <summary>
        /// 使用的条件组（使用逗号拼接）
        /// </summary>
        public List<string> ConditionCode { get; set; }

        /// <summary>
        /// 使用的条件组（使用逗号拼接）
        /// </summary>
        public string Condition { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }

        /// <summary>
        /// 答案集合
        /// </summary>
        public List<ItemDetailListView> RuleDetail { get; set; }

        /// <summary>
        /// 规则属性code
        /// </summary>
        public string RuleCode { get; set; }
    }
}
