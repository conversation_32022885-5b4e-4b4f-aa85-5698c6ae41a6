﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 人员多组织架构部门表仓储接口
    /// </summary>
    public interface IEmployeeToDepartmentRepository : ICacheRepository
    {
        /// <summary>
        /// 获取所有人员多组织架构部门数据
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, EmployeeToDepartmentInfo[]>> GetEmployeeToDepartments();
        /// <summary>
        /// 根据员工ID获取部门信息
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<EmployeeToDepartmentInfo[]> GetDepartmentsByEmployeeID(string employeeID);
        /// <summary>
        /// 根据员工ID获取多组织架构部门信息
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<EmployeeToDepartmentInfo[]> GetNoCacheInfosByEmployeeID(string employeeID);
        /// <summary>
        /// 根据员工编号和组织架构类型获取员工部门信息
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        Task<List<EmployeeToDepartmentInfo>> GetDepartmentByEmployeeIDAndType(string employeeID,string organizationType);
        /// <summary>
        /// 根据部门编号和组织架构类型获取员工信息
        /// </summary>
        /// <param name="departmentID">部门编号</param>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        Task<List<string>> GetEmployeeByDepartmentIDAndType(int departmentID, string organizationType);
        /// <summary>
        /// 根据人员工号和组织架构类型获取部门信息
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns>人员部门信息列表</returns>
        Task<List<EmployeeToDepartmentInfo>> GetByOrganizationTypeAndEmployeeIDAsync(string employeeID, string organizationType);
    }
}
