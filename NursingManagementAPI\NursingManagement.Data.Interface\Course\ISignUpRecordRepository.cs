﻿using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 培训报名记录仓储接口
    /// </summary>
    public interface ISignUpRecordRepository 
    {
        /// <summary>
        /// 根据来源ID和来源类别获取签到集合
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <param name="sourceType">来源类别(1：培训清单，2：培训群组，3：考核签到，4、培训签到)</param>
        /// <returns></returns>
        Task<List<SignUpRecordInfo>> GetListByRecordID(string sourceID, string sourceType);
        /// <summary>
        /// 根据主键获取报名信息
        /// </summary>
        /// <param name="signUpRecordID">主键ID</param>
        /// <returns>报名信息</returns>
        Task<SignUpRecordInfo> GetByIDAsync(string signUpRecordID);

        /// <summary>
        /// 根据 SourceID 获取报名信息
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <returns>报名信息</returns>
        Task<SignUpRecordInfo> GetBySourceIDAsNoTrackAsync(string sourceID);

        /// <summary>
        /// 根据 EmployeeID 获取 EmployeeID 和 StatusCode
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <returns>包含 EmployeeID 和 StatusCode 的报名信息列表</returns>
        Task<List<SignUpRecordInfo>> GetEmployeeIDAndStatusCodeAsNoTrackAsync(string employeeID);
        /// <summary>
        /// 根据条件表达式获取报名信息列表 (完整字段，跟踪)
        /// </summary>
        /// <param name="predicate">查询条件表达式</param>
        /// <returns>报名信息列表</returns>
        Task<List<SignUpRecordInfo>> GetListByConditionAsync(Expression<Func<SignUpRecordInfo, bool>> predicate);

        /// <summary>
        /// 根据条件表达式获取报名信息列表 (部分字段，不跟踪)
        /// </summary>
        /// <param name="predicate">查询条件表达式</param>
        /// <returns>报名信息列表</returns>
        Task<List<SignUpRecordInfo>> GetListByConditionAsNoTrackAsync(Expression<Func<SignUpRecordInfo, bool>> predicate);
        /// <summary>
        /// 根据来源ID获取对应的报名人员工号（报名通过的人员）
        /// </summary>
        /// <param name="sourceID">报名来源</param>
        /// <returns></returns>
        Task<List<string>> GetSignUpEmployeeIDsBySourceID(string sourceID);
        /// <summary>
        /// 根据来源id集合,来源类别获取数据
        /// </summary>
        /// <param name="sourceIDs">来源id集合</param>
        /// <param name="sourceType">来源类别</param>
        /// <returns></returns>
        Task<List<SignUpRecordInfo>> GetListBySourceIDAsNoTrackAsync(List<string> sourceIDs, string sourceType);
    }
}
