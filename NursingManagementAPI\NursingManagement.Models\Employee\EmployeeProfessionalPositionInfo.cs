﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员职称
    /// </summary>
    [Serializable]
    [Table("EmployeeProfessionalPosition")]
    public class EmployeeProfessionalPositionInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeProfessionalPositionID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 专业分类(GB/T 8561-2001)职务系列名称(250:卫生技术人员(护理))
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ProfessionalTypeCode { get; set; }

        /// <summary>
        /// 专业等级(Professional)系列等级名称(251主任护师、252副主任、253主管、254护师、255护士)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ProfessionalLevelCode { get; set; }

        /// <summary>
        /// 获得职称时间
        /// </summary>
        public DateTime? ObtainingDate { get; set; }

        /// <summary>
        /// 证书编号
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string CertificateNo { get; set; }

        /// <summary>
        /// 专业职称特殊标记，H表示同步人事系统维护的最高职称
        /// </summary>
        [Column(TypeName = "varchar(1)")]

        public string SpecialFlag { get; set; }
    }
}