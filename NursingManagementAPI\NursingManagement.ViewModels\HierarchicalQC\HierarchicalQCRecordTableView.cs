﻿namespace NursingManagement.ViewModels.HierarchicalQC
{
    public class HierarchicalQCRecordTableView
    {
        /// <summary>
        /// 主题ID
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }
        /// <summary>
        /// 主记录主键
        /// </summary>
        public string HierarchicalQCRecordID { get; set; }
        /// <summary>
        /// 质控主题
        /// </summary>
        public string FormName { get; set; }
        /// <summary>
        /// 考核日期
        /// </summary>
        public DateTime? ExamineDate { get; set; }
        /// <summary>
        /// 考核人
        /// </summary>
        public string ExamineEmployee { get; set; }
        /// <summary>
        /// 考核人ID
        /// </summary>
        public List<string> ExamineEmployeeIDList { get; set; }
        /// <summary>
        /// 考核病区
        /// </summary>
        public string ExamineObject { get; set; }
        /// <summary>
        /// 末次分数
        /// </summary>
        public string LastPoint { get; set; }
        /// <summary>
        /// 末次考核日期
        /// </summary>
        public DateTime? LastQCDateTime { get; set; }
        /// <summary>
        /// 首次分数
        /// </summary>
        public string FirstPoint { get; set; }
        /// <summary>
        /// 首次日期
        /// </summary>
        public DateTime? FirstQCDateTime { get; set; }
        /// <summary>
        /// 考核次数
        /// </summary>
        public string ExamineNumbers { get; set; }
        /// <summary>
        /// 审核人
        /// </summary>
        public string AuditNurse { get; set; }
        /// <summary>
        /// 审核人工号
        /// </summary>
        public string VerifierEmployeeID { get; set; }
        /// <summary>
        /// 状态 
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 获取QualityControlFormContent明细数据的编码
        /// </summary>
        public string TemplateCode { get; set; }

        /// <summary>
        /// 质控类型
        /// </summary>
        public string FormType { get; set; }

        /// <summary>
        ///  质控部门ID
        /// </summary>
        public int ExamineDepartmentID { get; set; }

        /// <summary>
        ///  质控对象ID
        /// </summary>
        public int QcObjectID { get; set; }

        /// <summary>
        /// 质控主题ID
        /// </summary>
        public string QcSubjectID { get; set; }
        /// <summary>
        /// 质控开始时间
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 主题最大分
        /// </summary>
        public int? ScoreThreshold {  get; set; }
        /// <summary>
        /// 达标分数
        /// </summary>
        public int? MinPassingScore { get; set; }
        /// <summary>
        /// 质控结束时间
        /// </summary>
        public DateTime EndDate { get; set; }
    }
}