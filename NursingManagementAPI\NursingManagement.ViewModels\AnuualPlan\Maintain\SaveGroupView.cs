﻿namespace NursingManagement.ViewModels
{
    public class SaveGroupView
    {
        /// <summary>
        /// 分组ID
        /// </summary>
        public string GroupID { get; set; }
        /// <summary>
        /// 主表ID
        /// </summary>
        public string MainID { get; set; }
        /// <summary>
        /// 目标业务ID
        /// </summary>
        public string MainGoalID { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 负责部门
        /// </summary>
        public string[] ResponsibleDepartments { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 转换指标、项目明细保存View为分组新增View
        /// </summary>
        /// <param name="view"></param>
        public static SaveGroupView Convert(dynamic view)
        {
            try
            {
                return new SaveGroupView
                {
                    GroupID = view.GroupID,
                    MainID = view.MainID,
                    MainGoalID = view.MainGoalID,
                    Year = view.Year,
                    HospitalID = view.HospitalID,
                    EmployeeID = view.EmployeeID,
                    Sort = view.GroupSort,
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"转换指标、项目明细保存View为分组新增View失败，异常：{ex}");
            }

        }
    }
}
