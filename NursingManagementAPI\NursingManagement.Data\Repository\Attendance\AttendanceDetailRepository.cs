﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AttendanceDetailRepository : IAttendanceDetailRepository
    {
        private readonly NursingManagementDbContext _context;

        public AttendanceDetailRepository(NursingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<List<AttendanceDetailInfo>> GetDetailByRecordID(string attendanceRecordID)
        {
            return await _context.AttendanceDetailInfos.Where(m => m.AttendanceRecordID == attendanceRecordID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<AttendanceDetailInfo>> GetDetailByRecordIDs(List<string> attendanceRecordIDs)
        {
            return await _context.AttendanceDetailInfos.Where(m => attendanceRecordIDs.Contains(m.AttendanceRecordID) && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
