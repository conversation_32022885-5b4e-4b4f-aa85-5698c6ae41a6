﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IQuarterPlanMaintainService
    {
        #region 季度计划表增删改查
        /// <summary>
        /// 保存季度计划工作内容
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        Task<bool> SaveQuarterWorks(QpWorksSaveView view);

        /// <summary>
        /// 删除季度计划工作
        /// </summary>
        /// <param name="quarterPlanDetailID">主键</param>
        /// <returns></returns>
        Task<bool> DeleteQuarterWork(string quarterPlanDetailID, string employeeID);

        /// <summary>
        /// 更新一条季度计划工作
        /// </summary>
        /// <param name="workView">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> UpdateQuarterWork(TieredPlanWork workView, string employeeID);

        /// <summary>
        /// 发布季度计划
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        Task<bool> PublishQuarterPlan(string quarterPlanMainID, string employeeID);

        /// <summary>
        /// 获取季度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        Task<string> GetQuarterPlanMainID(string annualPlanMainID, int quarter);

        /// <summary>
        /// 获取季度计划状态
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主键</param>
        /// <returns></returns>
        Task<bool> GetQuarterPlanStatus(string quarterPlanMainID);

        /// <summary>
        /// 查询某科室的季度计划
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        Task<TieredPlanWorksByType[]> GetQuarterWorks(string annualPlanMainID, string quarterPlanMainID);
        #endregion

        #region 参考导入逻辑
        /// <summary>
        /// 查询可导入的工作
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <param name="departmentID">当前部门</param>
        /// <param name="quarter">季度</param>
        /// <param name="annual">年度</param>
        /// <returns></returns>
        Task<List<TieredPlanWorksByPlanThenType>> GetCanImportQpWorksGroupByPlanThenType(string annualPlanMainID, string quarterPlanMainID, int departmentID, int annual, int quarter);
        /// <summary>
        /// 获取上级部门季度计划可选工作
        /// </summary>
        /// <param name="annual">年度</param>
        /// <param name="departmentID">部门</param>
        /// <param name="includeApInterventionID">需包含的执行项目字典ID</param>
        /// <param name="excludeApInterventionIDs">需排除的执行项目字典ID集合</param>
        /// <param name="typeList">分类字典</param>
        /// <returns></returns>
        /// <exception cref="Exception">未找到上级部门季度计划所属的年度计划</exception>
        Task<List<TieredPlanWorksByPlanThenType>> GetUpperDeptQpWorksByPlanThenTypeList(int annual, int departmentID, int? includeApInterventionID, int[] excludeApInterventionIDs = null, List<AnnualPlanTypeListInfo> typeList = null);
        /// <summary>
        /// 批量导入保存
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        Task<bool> SaveImportWorks(QpWorksSaveView view);
        #endregion

        /// <summary>
        /// 查询本人及上下级已制定的季度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<List<QuarterPlanBrowseView>> GetBrowseQPViews(int year, string employeeID);
        /// <summary>
        /// 获取季度计划预览数据
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划ID</param>
        /// <returns></returns>
        Task<QuarterPlanPreview> GetQuarterPlanPreview(string quarterPlanMainID);
    }
}
