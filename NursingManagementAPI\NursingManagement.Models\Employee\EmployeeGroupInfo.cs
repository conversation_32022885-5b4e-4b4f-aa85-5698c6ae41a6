﻿namespace NursingManagement.Models;

/// <summary>
/// 用户组聚合根
/// </summary>
public class EmployeeGroupInfo : ModifyInfo
{
    /// <summary>
    /// 用户组ID
    /// </summary>
    public int EmployeeGroupID { get; set; }
    /// <summary>
    /// 用户组名称
    /// </summary>
    public string GroupName { get; set; }
    /// <summary>
    /// 用户组成员工号集合
    /// </summary>
    public string[] EmployeeIDs { get; set; }


    /// <summary>
    /// 新建用户组
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <param name="employeeIDs">组员</param>
    /// <param name="employeeID">新增人</param>
    public EmployeeGroupInfo(string groupName, string[] employeeIDs, string employeeID)
    {
        GroupName = groupName;
        EmployeeIDs = employeeIDs ?? [];
        Modify(employeeID);
    }

    public EmployeeGroupInfo()
    {
    }

    /// <summary>
    /// 修改用户组
    /// </summary>
    /// <param name="groupName">用户组名称</param>
    /// <param name="employeeIDs">工号集合</param>
    /// <param name="employeeID">修改人</param>
    /// <returns></returns>
    public bool UpdateEmployeeGroup(string groupName, string[] employeeIDs, string employeeID)
    {
        if (GroupName != groupName)
        {
            GroupName = groupName;
        }
        var addEmployeeIDs = employeeIDs.Except(EmployeeIDs);
        var removeEmployeeIDs = EmployeeIDs.Except(employeeIDs);
        if (addEmployeeIDs.Any() || removeEmployeeIDs.Any())
        {
            IEnumerable<string> modifiedEmployeeIDs = null;
            if (addEmployeeIDs.Any())
            {
                modifiedEmployeeIDs = EmployeeIDs.Concat(addEmployeeIDs);
            }
            if (removeEmployeeIDs.Any())
            {
                modifiedEmployeeIDs = modifiedEmployeeIDs?.Except(removeEmployeeIDs) ?? EmployeeIDs.Except(removeEmployeeIDs);
            }
            EmployeeIDs = [.. modifiedEmployeeIDs];
        }
        Modify(employeeID);
        return true;
    }
    /// <summary>
    /// 删除用户组
    /// </summary>
    /// <param name="employeeID"></param>
    public void DeleteEmployeeGroup(string employeeID) => Delete(employeeID);
}
