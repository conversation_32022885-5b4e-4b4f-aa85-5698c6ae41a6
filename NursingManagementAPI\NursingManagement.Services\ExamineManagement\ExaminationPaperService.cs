﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Newtonsoft.Json.Linq;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Models.Examine;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services
{
    public class ExaminationPaperService : IExaminationPaperService
    {
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly NursingManagementDbContext _nursingManagementDbContext;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IExaminationQuestionRepository _examinationQuestionRepository;
        private readonly IExaminationQuestionDetailRepository _examinationQuestionDetailRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly CompositionPaperFactory _compositionPaperFactory;
        private readonly IExaminationPaperMainRepository _examinationPaperMainRepository;
        private readonly IDynamicFormService _dynamicFormService;
        private readonly IDynamicFormDetailRepository _dynamicFormDetailRepository;
        private readonly IDynamicFormDetailAttributeRepository _dynamicFormDetailAttributeRepository;
        private readonly IDynamicFormRecordRepository _dynamicFormRecordRepository;
        private readonly IConditionMainRepository _conditionMainRepository;
        private readonly IConditionDetaiRepository _conditionDetaiRepository;
        private readonly IExaminationMainRepository _examinationMainRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IRuleListRepository _ruleListRepository;
        private readonly IExaminationDetailRepository _examinationDetailRepository;
        private readonly IExaminationConditionRecordRepository _examinationConditionRecordRepository;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IExamineService _examineService;
        private readonly IExaminationPaperTemplateService _examinationPaperTemplateService;

        /// <summary>
        /// 必选题的RuleCode
        /// </summary>
        private const string RULE_CODE_CHOICE_QUESTION = "ChoiceQuestion";

        /// <summary>
        /// 组卷条件中 题库类别
        /// </summary>
        private const string GROUP_TYPE_QUESTION_BANK = "QuestionBank";

        /// <summary>
        /// 组卷规则 -难易程度的systemType分类
        /// </summary>
        private const string RULE_CODE_DEFFICULT_LEVEL_SYSTEM_TYPE = "CompositionPaperByDifficultyLevel";

        /// <summary>
        /// 理论类考核
        /// </summary>
        private static readonly string PAPER_TYPE_1 = "1";

        /// <summary>
        /// 模拟考核（理论试卷）
        /// </summary>
        private static readonly string PAPER_TYPE_3 = "3";

        /// <summary>
        /// 考核作答状态：正确
        /// </summary>
        private static readonly string EXAMINE_CORRECT_STATUSCODE = "2";

        /// <summary>
        /// 考核中状态
        /// </summary>
        private static readonly string EXAMINEING_STATUS = "3";

        /// <summary>
        /// 未开始考试的状态集合
        /// </summary>
        private static readonly string[] NOT_COMPLATE_STATUS_LIST = ["1", "2"];

        /// <summary>
        /// 考核结束的状态集合
        /// </summary>
        private static readonly string[] COMPLATE_STATUS_LIST = ["4", "5", "6"];

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="nursingManagementDbContext"></param>
        /// <param name="employeePersonalDataRepository"></param>
        /// <param name="examinationQuestionRepository"></param>
        /// <param name="examinationQuestionDetailRepository"></param>
        /// <param name="settingDictionaryRepository"></param>
        /// <param name="compositionPaperFactory"></param>
        /// <param name="examinationPaperMainRepository"></param>
        /// <param name="dynamicFormService"></param>
        /// <param name="dynamicFormDetailRepository"></param>
        /// <param name="dynamicFormDetailAttributeRepository"></param>
        /// <param name="dynamicFormRecordRepository"></param>
        /// <param name="conditionMainRepository"></param>
        /// <param name="conditionDetaiRepository"></param>
        /// <param name="examinationMainRepository"></param>
        /// <param name="departmentListRepository"></param>
        /// <param name="ruleListRepository"></param>
        /// <param name="examinationDetailRepository"></param>
        /// <param name="examinationConditionRecordRepository"></param>
        /// <param name="employeeDepartmentSwitchRepository"></param>
        /// <param name="examineService"></param>
        /// <param name="examinationPaperTemplateService"></param>
        public ExaminationPaperService(
            IUnitOfWork unitOfWork
            , NursingManagementDbContext nursingManagementDbContext
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IExaminationQuestionRepository examinationQuestionRepository
            , IExaminationQuestionDetailRepository examinationQuestionDetailRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , CompositionPaperFactory compositionPaperFactory
            , IExaminationPaperMainRepository examinationPaperMainRepository
            , IDynamicFormService dynamicFormService
            , IDynamicFormDetailRepository dynamicFormDetailRepository
            , IDynamicFormDetailAttributeRepository dynamicFormDetailAttributeRepository
            , IDynamicFormRecordRepository dynamicFormRecordRepository
            , IConditionMainRepository conditionMainRepository
            , IConditionDetaiRepository conditionDetaiRepository
            , IExaminationMainRepository examinationMainRepository
            , IDepartmentListRepository departmentListRepository
            , IRuleListRepository ruleListRepository
            , IExaminationDetailRepository examinationDetailRepository
            , IExaminationConditionRecordRepository examinationConditionRecordRepository
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IExamineService examineService
            , IExaminationPaperTemplateService examinationPaperTemplateService
        )
        {
            _unitOfWork = unitOfWork;
            _nursingManagementDbContext = nursingManagementDbContext;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _examinationQuestionRepository = examinationQuestionRepository;
            _examinationQuestionDetailRepository = examinationQuestionDetailRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _compositionPaperFactory = compositionPaperFactory;
            _examinationPaperMainRepository = examinationPaperMainRepository;
            _dynamicFormService = dynamicFormService;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
            _dynamicFormDetailAttributeRepository = dynamicFormDetailAttributeRepository;
            _dynamicFormRecordRepository = dynamicFormRecordRepository;
            _conditionMainRepository = conditionMainRepository;
            _conditionDetaiRepository = conditionDetaiRepository;
            _examinationMainRepository = examinationMainRepository;
            _departmentListRepository = departmentListRepository;
            _ruleListRepository = ruleListRepository;
            _examinationDetailRepository = examinationDetailRepository;
            _examinationConditionRecordRepository = examinationConditionRecordRepository;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _examineService = examineService;
            _examinationPaperTemplateService = examinationPaperTemplateService;
        }

        public async Task<List<ExaminationPaperMainView>> GetExaminationPaperMainList(DateTime startDate, DateTime endDate, string paperType, int? departmentID, string employeeID)
        {
            List<ExaminationPaperMainInfo> examinationPaperMainList =
             await GetPaperMainInfoByCondition(startDate, endDate, paperType, departmentID, employeeID);
            // 获取人员工号
            var employeeIDs = new List<string>();
            var addEmployeeIDs = examinationPaperMainList.Select(m => m.AddEmployeeID).Distinct().ToList();
            var modifyEmployeeIDs = examinationPaperMainList.Select(m => m.ModifyEmployeeID).Distinct().ToList();
            employeeIDs.AddRange(addEmployeeIDs);
            employeeIDs.AddRange(modifyEmployeeIDs);
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            var ruleList = await _ruleListRepository.GetListByContainsType("CompositionPaper");
            var conditionRecordIDs = examinationPaperMainList.Select(m => m.ExaminationConditionRecordID).ToList();
            var conditionDict = await _examinationConditionRecordRepository.GetConditionNameAsync(conditionRecordIDs);
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            // 组装数据
            var examinationPaperMainViewList = new List<ExaminationPaperMainView>();
            foreach (var item in examinationPaperMainList)
            {
                var difficultyLevelValue = ruleList.FirstOrDefault(m => m.RuleListID == item.DifficultyLevel)?.ShowName;
                var view = new ExaminationPaperMainView
                {
                    ExaminationPaperMainID = item.ExaminationPaperMainID,
                    PaperTitle = item.PaperTitle,
                    DifficultyLevel = difficultyLevelValue,
                    TotalPoints = item.TotalScore,
                    QuestionCount = item.QuestionCount,
                    PassingScore = item.PassingScore,
                    AddDateTime = item.AddDateTime,
                    AddEmployeeName = employeeList.FirstOrDefault(m => m.EmployeeID == item.AddEmployeeID)?.EmployeeName,
                    ModifyDateTime = item.ModifyDateTime,
                    ModifyEmployeeID = item.ModifyEmployeeID,
                    ModifyEmployeeName = employeeList.FirstOrDefault(m => m.EmployeeID == item.ModifyEmployeeID)?.EmployeeName,
                    DepartmentID = item.DepartmentID,
                    QuestionBankID = item.QuestionBankID,
                    DepartmentName = departmentList.Find(m => m.DepartmentID == item.DepartmentID)?.LocalShowName,
                    ExaminationConditionRecordID = item.ExaminationConditionRecordID,
                    ConditionName = item.ExaminationConditionRecordID != null ? conditionDict.TryGetValue(item.ExaminationConditionRecordID, out var conditionName) ? conditionName : "" : "",
                    PaperQuestionMode = item.PaperQuestionMode
                };
                examinationPaperMainViewList.Add(view);
            }
            return examinationPaperMainViewList.OrderByDescending(m => m.ModifyDateTime).ToList();
        }

        /// <summary>
        /// 根据条件获取需要呈现的试卷记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="paperType">试卷类别</param>
        /// <param name="departmentID">前端筛选的部门ID</param>
        /// <param name="employeeID">会话登录人</param>
        /// <returns></returns>
        private async Task<List<ExaminationPaperMainInfo>> GetPaperMainInfoByCondition(DateTime startDate, DateTime endDate, string paperType, int? departmentID, string employeeID)
        {
            List<int> switchDeptIDs = null;

            if (departmentID == null)
            {
                var departmentSwitchList = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeID, true);
                switchDeptIDs = departmentSwitchList.Select(m => m.DepartmentID).ToList();
            }
            else
            {
                switchDeptIDs = [departmentID.Value];
            }
            var examinationPaperMainList = await _examinationPaperMainRepository.GetListByDate(startDate, endDate, switchDeptIDs);
            if (!string.IsNullOrEmpty(paperType))
            {
                examinationPaperMainList = examinationPaperMainList.Where(m => m.PaperType == paperType).ToList();
            }

            return examinationPaperMainList;
        }

        public async Task<string> DeleteExaminationPaperMainData(string examinationPaperMainID, string employeeID)
        {
            var examintionPaperMain = await _examinationPaperMainRepository.GetDataByID(examinationPaperMainID);
            if (examintionPaperMain == null)
            {
                return "没有对应的试卷记录";
            }
            examintionPaperMain.Delete(employeeID);
            var dynamicFormDetail = await _dynamicFormDetailRepository.GetFormDetailListByFormRecordID(examintionPaperMain?.PaperID);
            if (dynamicFormDetail.Count > 0)
            {
                foreach (var detail in dynamicFormDetail)
                {
                    detail.Delete(employeeID);
                }
                _nursingManagementDbContext.UpdateRange(dynamicFormDetail, e => e.DeleteFlag, e => e.ModifyEmployeeID, e => e.ModifyDateTime);
                var dynamicFormDetailAttribute = await _dynamicFormDetailAttributeRepository.GetDetailAttributeListByRecordID(examintionPaperMain.PaperID);
                foreach (var attribute in dynamicFormDetailAttribute)
                {
                    attribute.Delete(employeeID);
                }
                _nursingManagementDbContext.UpdateRange(dynamicFormDetailAttribute, e => e.DeleteFlag, e => e.ModifyEmployeeID, e => e.ModifyDateTime);
            }
            var conditionMainData = await _conditionMainRepository.GetDataBySourceID(examintionPaperMain.ExaminationPaperMainID, "ExaminationPaperComposition");
            conditionMainData?.Delete(employeeID);
            var conditionDetailList = await _conditionDetaiRepository.GetListByMainID(conditionMainData?.ConditionMainID);
            foreach (var detail in conditionDetailList)
            {
                detail.Delete(employeeID);
            }
            var message = await _examineService.DeleteExamineByPaperMainID([examinationPaperMainID], employeeID);
            if (string.IsNullOrEmpty(message))
            {
                await _unitOfWork.SaveChangesAsync();
                return null;
            }
            return message;
        }

        public async Task<List<string>> DeletePaperByQuestionBank(List<string> questionBankIDs, string employeeID)
        {
            var questionList = await _examinationQuestionRepository.GetListByQuestionBankIDList(questionBankIDs);
            if (questionList.Count <= 0)
            {
                return [];
            }
            var questionIDList = questionList.Select(m => m.ExaminationQuestionID.ToString()).ToList();
            var dynamicFormDetailList = await _dynamicFormDetailRepository.GetFormDetailListByItemIDList(questionIDList, "ExaminationManagement");
            if (dynamicFormDetailList.Count <= 0)
            {
                var onlyTheoryPaperMainList = await DeleteTheoryPaperMain(questionBankIDs, employeeID);
                return onlyTheoryPaperMainList.Select(m => m.ExaminationPaperMainID).ToList();
            }
            var dynamicFormRecordIDList = dynamicFormDetailList.Select(m => m.DynamicFormRecordID).Distinct().ToList();
            foreach (var detail in dynamicFormDetailList)
            {
                detail.Delete(employeeID);
            }
            var dynamicRecordList = await _dynamicFormRecordRepository.GetFormByFormRecordIDs(dynamicFormRecordIDList);
            foreach (var record in dynamicRecordList)
            {
                record.Delete(employeeID);
            }
            var paperMainList = await _examinationPaperMainRepository.GetDataByPaperIDList(dynamicFormRecordIDList);
            foreach (var paper in paperMainList)
            {
                paper.Delete(employeeID);
            }
            var theoryPaperMainList = await DeleteTheoryPaperMain(questionBankIDs, employeeID);
            paperMainList.AddRange(theoryPaperMainList);
            return paperMainList.Select(m => m.ExaminationPaperMainID).ToList();
        }

        /// <summary>
        /// 获取理论试卷记录
        /// </summary>
        /// <param name="questionBankIDs"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<List<ExaminationPaperMainInfo>> DeleteTheoryPaperMain(List<string> questionBankIDs, string employeeID)
        {
            var examinationConditionRecordIDList = await _conditionMainRepository.GetSourceIDsByGroupTypeValue(questionBankIDs, "QuestionBank");
            if (examinationConditionRecordIDList.Count <= 0)
            {
                return [];
            }
            var paperMainList = await _examinationPaperMainRepository.GetDataByExaminationConditionRecordIDList(examinationConditionRecordIDList);
            if (paperMainList.Count <= 0)
            {
                return [];
            }
            foreach (var paper in paperMainList)
            {
                paper.Delete(employeeID);
            }
            return paperMainList;
        }

        #region 保存理论类和实操类试卷

        #region 保存理论类试卷

        public async Task<bool> SaveTheoryPaper(ExaminationPaperMainView view)
        {
            var conditionRule = await GetConditionDetailByPaperMainView(view);
            if ((conditionRule == null) && !view.ExaminationPaperComposition)
            {
                return false;
            }
            // 生成试卷
            var paperCompositionRuleView = new PaperCompositionRuleView
            {
                ExaminationPaperMainID = view.ExaminationPaperMainID,
                QuestionBankIDList = view.QuestionBankIDs,
                PaperTitle = view.PaperTitle,
                ModifyEmployeeID = view.ModifyEmployeeID,
                RuleKeyValueList = [],
                PassingScore = view.PassingScore,
                PaperFilterQuestionConditionRuleView = conditionRule,
                DepartmentID = view.DepartmentID,
                ExaminationConditionRecordID = view.ExaminationConditionRecordID,
                PaperQuestionMode = view.PaperQuestionMode
            };
            return await CompositionExaminePaperByRule(paperCompositionRuleView, view.PaperType);
        }

        /// <summary>
        /// 根据组卷规则产生试卷
        /// </summary>
        /// <param name="paperCompositionRuleView">组卷规则view</param>
        /// <param name="paperType">试卷类型</param>
        /// <returns></returns>
        private async Task<bool> CompositionExaminePaperByRule(PaperCompositionRuleView paperCompositionRuleView, string paperType)
        {
            // 提前返回条件检查
            if (paperCompositionRuleView == null || paperCompositionRuleView.QuestionBankIDList == null || paperCompositionRuleView.QuestionBankIDList.Count == 0)
            {
                return false;
            }
            // 获取规则配置
            var ruleSettingList = await _ruleListRepository.GetListByContainsType("CompositionPaper");
            // 获取录入难度等级规则
            var difficultyLevelValue = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.DifficultyRule.OrderBy(m => m.Value).LastOrDefault();
            // 获取录入难度等级规则最大值配置
            var difficultyLevelSetting = ruleSettingList.FirstOrDefault(m => m.RuleListID == difficultyLevelValue?.RuleListID);
            // 处理试卷信息（新增、修改）
            var examinationPaperMainData = await HandlePaperMainData(paperCompositionRuleView, paperType, difficultyLevelSetting);
            if (string.IsNullOrEmpty(examinationPaperMainData.ExaminationConditionRecordID))
            {
                examinationPaperMainData.QuestionCount = 0;
                await _unitOfWork.SaveChangesAsync();
                return true;
            }
            // 查询试卷绑定的组卷规则
            var conditionMains = await _conditionMainRepository.GetMainIDsBySourceID(examinationPaperMainData.ExaminationConditionRecordID);
            if (conditionMains.Count == 0)
            {
                examinationPaperMainData.QuestionCount = 0;
                await _unitOfWork.SaveChangesAsync();
                return true;
            }
            // 补充按照条件调整的字段值
            examinationPaperMainData = await HandlePaperMainFieldValue(paperCompositionRuleView, ruleSettingList, examinationPaperMainData, conditionMains);
            // 1、定题 2、不定题但是定题目
            if (PaperDynamicGeneratorUtils.IsFixedQuestion(paperCompositionRuleView.PaperQuestionMode) || PaperDynamicGeneratorUtils.CheckTheSameQuestion(paperCompositionRuleView.PaperQuestionMode))
            {
                // 根据规则产生试卷明细数据
                var examinationQuestionList = await _examinationQuestionRepository.GetListByQuestionBankIDList(paperCompositionRuleView.QuestionBankIDList, true);
                var paperQuestionViewList = await _compositionPaperFactory.GetCompositionPaperAPI(examinationQuestionList, paperCompositionRuleView, examinationPaperMainData);
                if (PaperDynamicGeneratorUtils.IsFixedQuestion(paperCompositionRuleView.PaperQuestionMode))
                {
                    var paperGroup = await _examinationPaperTemplateService.GetTheoryPaperPaperGroup(paperQuestionViewList, paperType);
                    // 将试卷分组和试卷题目转换为试卷模板
                    var paperFormTemplate = await _examinationPaperTemplateService.ConvertToFormTemplate(paperGroup, paperQuestionViewList, examinationPaperMainData.PaperTitle, false, true, paperCompositionRuleView.PaperQuestionMode);
                    examinationPaperMainData.PaperID = await _dynamicFormService.SaveFormTemplate(paperFormTemplate, examinationPaperMainData.ModifyEmployeeID);
                }
                else
                {
                    examinationPaperMainData.FixedQuestionList = paperQuestionViewList.Select(m => m.QuestionID).ToList();
                }
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 补充按照条件调整的字段值
        /// </summary>
        /// <param name="paperCompositionRuleView"></param>
        /// <param name="ruleSettingList"></param>
        /// <param name="examinationPaperMainData"></param>
        /// <param name="conditionMains"></param>
        /// <returns></returns>
        private async Task<ExaminationPaperMainInfo> HandlePaperMainFieldValue(PaperCompositionRuleView paperCompositionRuleView, List<RuleListInfo> ruleSettingList, ExaminationPaperMainInfo examinationPaperMainData, List<ConditionMainInfo> conditionMains)
        {
            // 查询组卷规则明细
            var mainIds = conditionMains.Select(m => m.ConditionMainID).ToList();
            var conditionDetails = await _conditionDetaiRepository.GetListByMainIDs(mainIds);
            // 使用 HashSet 加速集合查询题目数量组卷规则配置
            var countRules = ruleSettingList
                .Where(m => m.SystemType == "CompositionPaperByCount")
                .Select(m => m.RuleListID)
                .ToHashSet();
            // 计算题目数量
            examinationPaperMainData.QuestionCount = conditionDetails
                .Where(detail => countRules.Contains(detail.ItemID))
                .Sum(detail =>
                {
                    if (int.TryParse(detail.ConditionValue, out var value)) return value; return 0;
                });
            // 如果调整试卷题目组织模式，从 定题到不定题，删除定题模板内容
            if (!PaperDynamicGeneratorUtils.IsFixedQuestion(paperCompositionRuleView.PaperQuestionMode) && !string.IsNullOrEmpty(examinationPaperMainData.PaperID))
            {
                await _dynamicFormService.DeleteFormTemplate(examinationPaperMainData.PaperID, paperCompositionRuleView.ModifyEmployeeID);
                examinationPaperMainData.PaperID = null;
            }
            //试卷不完成相同 、同时题目不相同时 ，删除定题列表
            if (!PaperDynamicGeneratorUtils.IsFixedQuestion(paperCompositionRuleView.PaperQuestionMode) && !PaperDynamicGeneratorUtils.CheckTheSameQuestion(paperCompositionRuleView.PaperQuestionMode))
            {
                examinationPaperMainData.FixedQuestionList = null;
            }
            return examinationPaperMainData;
        }

        /// <summary>
        /// 处理试卷主表数据
        /// </summary>
        /// <param name="paperCompositionRuleView"></param>
        /// <param name="paperType"></param>
        /// <param name="difficultyLevelSetting"></param>
        /// <returns></returns>
        private async Task<ExaminationPaperMainInfo> HandlePaperMainData(PaperCompositionRuleView paperCompositionRuleView, string paperType, RuleListInfo difficultyLevelSetting)
        {
            // 困难程度
            int difficultyLevelRuleListID = difficultyLevelSetting?.RuleListID ?? 0;
            ExaminationPaperMainInfo paperMainData = null;
            if (!string.IsNullOrWhiteSpace(paperCompositionRuleView.ExaminationPaperMainID))
            {
                paperMainData = await _examinationPaperMainRepository.GetDataByID(paperCompositionRuleView.ExaminationPaperMainID);
            }
            // 更新试卷信息
            if (paperMainData != null)
            {
                paperMainData.PaperTitle = paperCompositionRuleView.PaperTitle;
                paperMainData.PaperType = paperType;
                paperMainData.TotalScore = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.Score;
                paperMainData.PassingScore = paperCompositionRuleView.PassingScore;
                paperMainData.DifficultyLevel = difficultyLevelRuleListID;
                paperMainData.DepartmentID = paperCompositionRuleView.DepartmentID;
                paperMainData.ExaminationConditionRecordID = paperCompositionRuleView.ExaminationConditionRecordID;
                paperMainData.Add(paperCompositionRuleView.ModifyEmployeeID).Modify(paperCompositionRuleView.ModifyEmployeeID);
                paperMainData.PaperQuestionMode = paperCompositionRuleView.PaperQuestionMode;
                return paperMainData;
            }
            // 新增试卷记录
            var insertPaperMainData = new ExaminationPaperMainInfo
            {
                PaperTitle = paperCompositionRuleView.PaperTitle,
                PaperType = paperType,
                TotalScore = paperCompositionRuleView.PaperFilterQuestionConditionRuleView.Score,
                PassingScore = paperCompositionRuleView.PassingScore,
                DifficultyLevel = difficultyLevelRuleListID,
                ExaminationConditionRecordID = paperCompositionRuleView.ExaminationConditionRecordID,
                DepartmentID = paperCompositionRuleView.DepartmentID,
                PaperQuestionMode = paperCompositionRuleView.PaperQuestionMode
            };
            insertPaperMainData.ExaminationPaperMainID = insertPaperMainData.GetId();
            insertPaperMainData.Add(paperCompositionRuleView.ModifyEmployeeID);
            insertPaperMainData.Modify(paperCompositionRuleView.ModifyEmployeeID);
            await _unitOfWork.GetRepository<ExaminationPaperMainInfo>().InsertAsync(insertPaperMainData);
            return insertPaperMainData;
        }

        #endregion

        /// <summary>
        /// 保存实操类试卷
        /// </summary>
        /// <param name="view">入参</param>
        /// <returns></returns>
        public async Task<bool> SavePracticalPaper(ExaminationPaperMainView view)
        {
            ExaminationPaperMainInfo examinationPaperMain = null;
            var needCreatTemplate = false;
            var oldPaperID = "";
            // 新增
            if (string.IsNullOrWhiteSpace(view.ExaminationPaperMainID))
            {
                needCreatTemplate = true;
                examinationPaperMain = new ExaminationPaperMainInfo
                {
                    PaperType = view.PaperType
                };
                examinationPaperMain.ExaminationPaperMainID = examinationPaperMain.GetId();
                examinationPaperMain.Add(view.ModifyEmployeeID);
                await _unitOfWork.GetRepository<ExaminationPaperMainInfo>().InsertAsync(examinationPaperMain);
            }
            else
            {
                // 修改
                examinationPaperMain = await _examinationPaperMainRepository.GetDataByID(view.ExaminationPaperMainID);
                if (examinationPaperMain.QuestionBankID != view.QuestionBankID)
                {
                    needCreatTemplate = true;
                    oldPaperID = examinationPaperMain.QuestionBankID;
                }
            }
            examinationPaperMain.PaperTitle = view.PaperTitle;
            examinationPaperMain.DepartmentID = view.DepartmentID;
            examinationPaperMain.QuestionBankID = view.QuestionBankID;
            examinationPaperMain.Modify(view.ModifyEmployeeID);
            if (needCreatTemplate)
            {
                // 根据题库ID获取试卷模板和试卷题目数量
                (var paperFormTemplate, examinationPaperMain.QuestionCount) = await _examinationPaperTemplateService.GetPaperTemplateByBankID(examinationPaperMain.QuestionBankID, examinationPaperMain.PaperTitle, view.IsPractical);
                examinationPaperMain.PaperID = await _dynamicFormService.SaveFormTemplate(paperFormTemplate, examinationPaperMain.ModifyEmployeeID);
                if (string.IsNullOrWhiteSpace(examinationPaperMain.PaperID))
                {
                    throw new CustomException("保存失败：生成试卷失败！");
                }
            }
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                // 如果题库换了 删除历史题库模板
                if (!string.IsNullOrWhiteSpace(oldPaperID))
                {
                    await _dynamicFormService.DeleteFormTemplate(examinationPaperMain.QuestionBankID, view.ModifyEmployeeID);
                }
                return true;
            }
            return false;
        }

        #endregion

        /// <summary>
        /// 根据规则记录ID获取组卷的规则内容
        /// </summary>
        /// <param name="view">前端保存参数</param>
        /// <returns></returns>
        private async Task<PaperFilterQuestionConditionRuleView> GetConditionDetailByPaperMainView(ExaminationPaperMainView view)
        {
            var conditionRecord = await _examinationConditionRecordRepository.GetRecordByIdAsync(view.ExaminationConditionRecordID);
            if (conditionRecord == null)
            {
                _logger.Error($"找不到对应的组卷规则,ExaminationConditionRecordID：{view.ExaminationConditionRecordID}");
                return null;
            }
            var conditionMains = await _conditionMainRepository.GetMainIDsBySourceID(view.ExaminationConditionRecordID);
            if (conditionMains.Count <= 0)
            {
                return null;
            }
            var conditionDetails = await _conditionDetaiRepository.GetListByMainIDs(conditionMains.Select(m => m.ConditionMainID).ToList());
            if (conditionDetails.Count <= 0)
            {
                return null;
            }
            var compositePaperRules = await GetPaperRule();
            var questionTypeSettings = await GetQuestionTypeSetting();
            var questionTypes = questionTypeSettings.Select(m => m.SettingValue).ToList();
            //按照题库分组
            var groupQuestionBankCondition = conditionMains.Where(m => m.GroupType == GROUP_TYPE_QUESTION_BANK).GroupBy(m => m.GroupTypeValue);
            if (groupQuestionBankCondition.Count() == 0)
            {
                return null;
            }
            // 设置关联的题库
            view.QuestionBankIDs = groupQuestionBankCondition.Select(m => m.Key).ToList();
            var paperFilterQuestionConditionRule = new PaperFilterQuestionConditionRuleView
            {
                Score = conditionRecord.Score,
                QuestionFilterConditionRules = [],
                DifficultyRule = [],
                QustionTypes = questionTypes
            };
            // 题型和题目数量
            foreach (var groupItem in groupQuestionBankCondition)
            {
                var questionBankID = groupItem.Key;
                var groupConditionMainIDs = groupItem.Select(m => m.ConditionMainID).ToList();
                var questionFilters = new List<QuestionFilter>();
                var groupConditionDetails = conditionDetails.Where(m => groupConditionMainIDs.Contains(m.ConditionMainID)).ToList();
                // 按照题型处理 组卷规则
                foreach (var questionType in questionTypes)
                {
                    var questionTypeRules = compositePaperRules.Where(m => m.RuleCode.StartsWith(questionType));
                    var countRule = questionTypeRules.Where(m => m.RuleCode.Contains("Count")).ToList();
                    var scoreRule = questionTypeRules.FirstOrDefault(m => m.RuleCode.Contains("Score"));
                    _ = Decimal.TryParse(groupConditionDetails.Find(detail => detail.ItemID == scoreRule?.RuleListID)?.ConditionValue, out Decimal score);
                    var questionFilter = new QuestionFilter
                    {
                        QuestionType = questionType,
                        RuleKeyValues = countRule.Select(m =>
                        {
                            var value = groupConditionDetails.Find(detail => detail.ItemID == m.RuleListID)?.ConditionValue;
                            return new RuleKeyValue
                            {
                                RuleListID = m.RuleListID,
                                RuleCode = m.RuleCode,
                                Value = int.TryParse(value, out var count) ? count : 0
                            };
                        }).ToList(),
                        Score = score
                    };
                    questionFilters.Add(questionFilter);
                }
                paperFilterQuestionConditionRule.QuestionFilterConditionRules.Add(new QuestionFilterConditionRule
                {
                    QuestionBankID = questionBankID,
                    QuestionFilters = questionFilters
                });
            }
            // 必选题
            var requiredQuestionRuleListID = compositePaperRules.Find(m => m.RuleCode == RULE_CODE_CHOICE_QUESTION)?.RuleListID;
            if (requiredQuestionRuleListID != null)
            {
                var questionIDs = conditionDetails.Where(m => m.ItemID == requiredQuestionRuleListID).SelectMany(m =>
                {
                    var tokens = JToken.Parse(m.ConditionValue);
                    return tokens.Type == JTokenType.Array
                    ? tokens.Values<string>()
                    : new[] { tokens.Value<string>() };
                }).Where(s => !string.IsNullOrWhiteSpace(s) && s.All(char.IsDigit) && s.TrimStart('0').Length > 0).Select(int.Parse).Distinct().ToList();
                paperFilterQuestionConditionRule.RequireQuestion = questionIDs;
            }
            // 难易程度
            var difficultLevelRules = compositePaperRules.Where(m => m.SystemType == RULE_CODE_DEFFICULT_LEVEL_SYSTEM_TYPE).ToList();
            foreach (var ruleItem in difficultLevelRules)
            {
                var difficulty = conditionDetails.Find(m => ruleItem.RuleListID == m.ItemID);
                var difficultyValue = !string.IsNullOrWhiteSpace(difficulty?.ConditionValue) ? ListToJson.ToList<string>(difficulty.ConditionValue) ?? "0" : "0";
                paperFilterQuestionConditionRule.DifficultyRule.Add(new RuleKeyValue
                {
                    RuleCode = ruleItem.RuleCode,
                    RuleListID = ruleItem.RuleListID,
                    Value = int.TryParse(difficultyValue, out var value) ? value : 0
                });
            }

            return paperFilterQuestionConditionRule;
        }

        /// <summary>
        /// 获取题目类型字典配置
        /// </summary>
        /// <returns></returns>
        private async Task<List<SettingDictionaryInfo>> GetQuestionTypeSetting()
        {
            var questionTypeSettingParams = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationQuestion",
                SettingTypeValue = "ExaminationQuestionType"
            };
            var questionTypeSettings = await _settingDictionaryRepository.GetSettingDictionary(questionTypeSettingParams);
            return questionTypeSettings;
        }

        /// <summary>
        /// 获取组卷规则
        /// </summary>
        /// <returns></returns>
        private async Task<List<RuleListInfo>> GetPaperRule()
        {
            var ruleTypeList = new List<string>
            {
                "CompositionPaperByCount",
                "CompositionPaperByDifficultyLevel",
                "CompositionPaperBySingleScore",
                "CompositionPaperByRequiredChoice"
            };
            var ruleList = await _ruleListRepository.GetListByTypeList(ruleTypeList);
            return ruleList;
        }

        public async Task<ExamineFormTemplateView> GetPaperFormTemplate(string examinationPaperMainID, bool isPreview, string employeeID, string examinationMainID = null)
        {
            var returnView = new ExamineFormTemplateView();
            var formTemplateData = new List<FormValueView>();
            var examinationMain = new ExaminationMainInfo();
            var paperMain = await _examinationPaperMainRepository.GetDataByID(examinationPaperMainID);
            if (paperMain == null)
            {
                return null;
            }
            var paperID = paperMain.PaperID;
            if (!string.IsNullOrEmpty(examinationMainID))
            {
                examinationMain = await _examinationMainRepository.GetDataByMainID(examinationMainID);
                paperID = examinationMain.PaperID;
                var examinationDetailList = await _examinationDetailRepository.GetListByMainIDAsNoTracking(examinationMainID);
                formTemplateData = await GetPaperFromTmeplateData(examinationDetailList);
            }
            // 实操类、刷题练习、定题试卷、考核结果查看
            if (!string.IsNullOrWhiteSpace(paperID))
            {
                returnView.FormTemplateView = await _dynamicFormService.GetFormTemplateByRecordID(paperID, formTemplateData, null, null);
                return returnView;
            }
            // 没有paperID说明是不定题试卷，根据组卷规则获取试卷模板
            returnView.FormTemplateView = await GetDynamicFormInformationByRule(paperMain, employeeID);
            if (returnView.FormTemplateView == null)
            {
                return returnView;
            }
            if (!isPreview)
            {
                var saveFormTemplateView = CloneData.CloneObj(returnView.FormTemplateView);
                // 将试卷分组和试卷题目转换为试卷模板
                examinationMain.PaperID = await _dynamicFormService.SaveFormTemplate(saveFormTemplateView, employeeID);
            }
            return returnView;
        }

        /// <summary>
        /// 根据组卷规则获取试卷明细
        /// </summary>
        /// <param name="paperMainInfo"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        private async Task<FormTemplateView> GetDynamicFormInformationByRule(ExaminationPaperMainInfo paperMainInfo, string modifyEmployeeID)
        {
            var view = new ExaminationPaperMainView()
            {
                ExaminationConditionRecordID = paperMainInfo.ExaminationConditionRecordID,
            };
            // 获取组卷规则
            var conditionRule = await GetConditionDetailByPaperMainView(view);
            if (conditionRule == null)
            {
                return null;
            }
            // 构造组卷View
            var paperCompositionRuleView = new PaperCompositionRuleView
            {
                ExaminationPaperMainID = paperMainInfo.ExaminationPaperMainID,
                QuestionBankIDList = view.QuestionBankIDs,
                PaperTitle = paperMainInfo.PaperTitle,
                ModifyEmployeeID = modifyEmployeeID,
                RuleKeyValueList = [],
                PassingScore = paperMainInfo.PassingScore,
                PaperFilterQuestionConditionRuleView = conditionRule,
                DepartmentID = paperMainInfo.DepartmentID,
                ExaminationConditionRecordID = paperMainInfo.ExaminationConditionRecordID,
                PaperQuestionMode = paperMainInfo.PaperQuestionMode
            };
            List<PaperQuestionView> paperQuestionViewList = null;
            // 根据规则产生试卷明细数据 不定题-固定题目
            if (paperMainInfo.FixedQuestionList != null)
            {
                var questionIDs = paperMainInfo.FixedQuestionList.Select(m => int.Parse(m)).ToList();
                var examinationQuestionList = await _examinationQuestionRepository.GetListByIDList(questionIDs);

                paperQuestionViewList = await _compositionPaperFactory.GetCompositionPaperAPI(examinationQuestionList, paperCompositionRuleView, paperMainInfo);
                // 调整试卷 -不定题-固定题目排序
                if (PaperDynamicGeneratorUtils.CheckQuestionHasOrder(paperMainInfo.PaperQuestionMode))
                {
                    var orderDict = paperMainInfo.FixedQuestionList.Select((id, index) => new { id, index }).ToDictionary(x => x.id, x => x.index);
                    paperQuestionViewList = paperQuestionViewList.OrderBy(m => orderDict[m.QuestionID]).ToList();
                }
            }
            else
            {
                var examinationQuestionList = await _examinationQuestionRepository.GetListByQuestionBankIDList(paperCompositionRuleView.QuestionBankIDList, true);
                paperQuestionViewList = await _compositionPaperFactory.GetCompositionPaperAPI(examinationQuestionList, paperCompositionRuleView, paperMainInfo);
            }
            var paperGroup = await _examinationPaperTemplateService.GetTheoryPaperPaperGroup(paperQuestionViewList, paperMainInfo.PaperType);
            return await _examinationPaperTemplateService.ConvertToFormTemplate(paperGroup, paperQuestionViewList, paperMainInfo.PaperTitle, false, true, paperCompositionRuleView.PaperQuestionMode);
        }

        /// <summary>
        /// 获取考核记录明细内容，组装模版data数据
        /// </summary>
        /// <param name="examinationDetailList">考核主记录ID</param>
        /// <returns></returns>
        private async Task<List<FormValueView>> GetPaperFromTmeplateData(List<ExaminationDetailInfo> examinationDetailList)
        {
            var returnView = new List<FormValueView>();
            if (examinationDetailList.Count <= 0)
            {
                return returnView;
            }
            var examinationQuestionIDs = examinationDetailList.Select(m => m.ExaminationQuestionID).ToList();
            var examintionQuestionList = await _examinationQuestionRepository.GetListByIDList(examinationQuestionIDs);
            var multipleQuestionIDs = examintionQuestionList.Where(m => m.ExaminationQuestionType == "MultipleChoice").Select(m => m.ExaminationQuestionID).ToList();
            var questionDetails = await _examinationQuestionDetailRepository.GetListByQuestionIDs(multipleQuestionIDs);
            foreach (var detail in examinationDetailList)
            {
                var examintionQuestionData = examintionQuestionList.Find(m => m.ExaminationQuestionID == detail.ExaminationQuestionID);
                if (examintionQuestionData == null)
                {
                    continue;
                }
                var view = new FormValueView()
                {
                    ID = detail.ExaminationQuestionDetailID,
                    ParentID = detail.ExaminationQuestionID.ToString(),
                    ExaminationStatus = detail.StatusCode == EXAMINE_CORRECT_STATUSCODE ? ExaminationStatusEnum.Correct : ExaminationStatusEnum.Error
                };
                switch (examintionQuestionData.ExaminationQuestionType)
                {
                    case "MultipleChoice":
                        view.Value = detail.ExaminationQuestionDetailID;
                        var correctDetails = questionDetails.Where(m => m.ExaminationQuestionID == detail.ExaminationQuestionID && m.AnswerFlag).ToList();
                        var answerCorrectDetails = examinationDetailList.Where(m => m.ExaminationQuestionID == detail.ExaminationQuestionID && m.StatusCode == EXAMINE_CORRECT_STATUSCODE).ToList();
                        view.ExaminationStatus = correctDetails.Count == answerCorrectDetails.Count ? ExaminationStatusEnum.Correct : ExaminationStatusEnum.Error;
                        break;
                    case "SingleChoice":
                    case "Judgment":
                        view.Value = detail.ExaminationQuestionDetailID;
                        break;
                    case "ShortAnswer":
                        view.Value = detail.Value;
                        break;
                    case "Scoring":
                        var valueDictionary = new Dictionary<string, object>()
                        {
                            { "score", detail.Value },
                            { "remark", detail.Remark }
                        };
                        view.Value = valueDictionary;
                        break;
                };
                returnView.Add(view);
            }
            return returnView;
        }

        /// <summary>
        /// 获取试卷下拉框数据
        /// </summary>
        /// <param name="paperType"></param>
        /// <param name="employeeID"></param>
        /// <param name="departmentID">当前用户登录查看的部门</param>
        public async Task<List<SelectOptionsView>> GetPaperMainSelectList(string paperType, string employeeID, int departmentID)
        {
            // TODO: 暂时屏蔽，后续确认最终做法再调整
            //var departmentSwitchList = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeID, true);
            //var switchDeptIDs = departmentSwitchList.Select(m => m.DepartmentID).ToList();
            //if (switchDeptIDs.Count <= 0)
            //{
            //    switchDeptIDs.Add(departmentID);
            //}
            // 模拟考核 实际使用的是理论试卷进行的模拟
            if (paperType == PAPER_TYPE_3)
            {
                paperType = PAPER_TYPE_1;
            }
            // TODO: 暂时屏蔽，后续确认最终做法再调整 return await
            // _examinationPaperMainRepository.GetDataByPaperType(paperType, switchDeptIDs);
            return await _examinationPaperMainRepository.GetDataByPaperType(paperType, null);
        }

        /// <summary>
        /// 获取组卷规则下拉框数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetPaperRuleSelectList()
        {
            var paperConditionList = await _conditionMainRepository.GetListBySourceType("ExaminationPaperComposition");
            if (paperConditionList.Count <= 0)
            {
                return [];
            }
            paperConditionList = paperConditionList.DistinctBy(m => m.SourceID).ToList();
            return paperConditionList.Select(m => new SelectOptionsView
            {
                Label = m.Description,
                Value = m.SourceID,
            }).ToList();
        }

        /// <summary>
        /// 根据考核记录MainID获取试卷模版内容
        /// </summary>
        /// <param name="examinationMainID">考核主记录ID</param>
        /// <param name="employeeID">人员工号</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns></returns>
        public async Task<ExamineFormTemplateView> GetPaperFormTemplateByMainID(string examinationMainID, string employeeID, string hospitalID)
        {
            try
            {
                var examinaMainData = await _examinationMainRepository.GetDataByMainID(examinationMainID);
                if (examinaMainData == null)
                {
                    return null;
                }
                if (NOT_COMPLATE_STATUS_LIST.Contains(examinaMainData.StatusCode))
                {
                    examinaMainData.StatusCode = EXAMINEING_STATUS;
                    examinaMainData.StartDateTime = DateTime.Now;
                    examinaMainData.Modify(employeeID);
                }

                var hasCompleteExamination = COMPLATE_STATUS_LIST.Contains(examinaMainData.StatusCode);
                var formTemplateView = await GetPaperFormTemplate(examinaMainData.ExaminationPaperMainID, hasCompleteExamination, employeeID, examinationMainID);
                if (formTemplateView != null && formTemplateView.FormTemplateView != null)
                {
                    formTemplateView.CurrentQuestionID = examinaMainData.CurrentQuestionID;
                }
                if (!hasCompleteExamination)
                {
                    await _unitOfWork.SaveChangesAsync();
                }
                return formTemplateView;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取试卷模板内容失败，异常信息：{ex}");
                return null;
            }
        }

        /// <summary>
        /// 根据试卷主记录ID复制一份新的试卷
        /// </summary>
        /// <param name="examinationPaperMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> CopyExamPaperMain(string examinationPaperMainID, string employeeID)
        {
            var sourceExamPaper = await _examinationPaperMainRepository.GetDataByID(examinationPaperMainID);
            var examinationPaperMain = new ExaminationPaperMainInfo
            {
                ExaminationPaperMainID = Guid.NewGuid().ToString("N"),
                PaperTitle = sourceExamPaper.PaperTitle,
                DifficultyLevel = sourceExamPaper.DifficultyLevel,
                TotalScore = sourceExamPaper.TotalScore,
                QuestionCount = sourceExamPaper.QuestionCount,
                PassingScore = sourceExamPaper.PassingScore,
                PaperID = sourceExamPaper.PaperID,
                PaperType = sourceExamPaper.PaperType,
                DepartmentID = sourceExamPaper.DepartmentID,
                ExaminationConditionRecordID = await CopyExaminationCondition(sourceExamPaper.ExaminationConditionRecordID, employeeID)
            };
            examinationPaperMain.Add(employeeID).Modify(employeeID);
            await _unitOfWork.GetRepository<ExaminationPaperMainInfo>().InsertAsync(examinationPaperMain);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 复制组卷规则
        /// </summary>
        /// <param name="conditionRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<string> CopyExaminationCondition(string conditionRecordID, string employeeID)
        {
            var sourceConditionRecord = await _examinationConditionRecordRepository.GetRecordByIdAsync(conditionRecordID);
            if (sourceConditionRecord == null)
            {
                return "";
            }
            var addConditionRecord = new ExaminationConditionRecordInfo
            {
                ConditionName = sourceConditionRecord.ConditionName,
                ConditionContent = sourceConditionRecord.ConditionContent,
                Score = sourceConditionRecord.Score,
            };
            addConditionRecord.Add(employeeID).Modify(employeeID);
            addConditionRecord.ExaminationConditionRecordID = addConditionRecord.GetId();
            await _unitOfWork.GetRepository<ExaminationConditionRecordInfo>().InsertAsync(addConditionRecord);
            // 复制规则主表和明细
            await CopyConditionMainAndDetail(conditionRecordID, employeeID, addConditionRecord);
            return addConditionRecord.ExaminationConditionRecordID;
        }

        /// <summary>
        /// 复制规则主表和明细
        /// </summary>
        /// <param name="conditionRecordID"></param>
        /// <param name="employeeID"></param>
        /// <param name="addConditionRecord"></param>
        /// <returns></returns>
        private async Task CopyConditionMainAndDetail(string conditionRecordID, string employeeID, ExaminationConditionRecordInfo addConditionRecord)
        {
            var sourceConditionMains = await _conditionMainRepository.GetMainIDsBySourceID(conditionRecordID);
            if (sourceConditionMains.Count == 0)
            {
                return;
            }
            var conditionMainIDs = sourceConditionMains.Select(m => m.ConditionMainID).ToList();
            var sourceConditionDetails = await _conditionDetaiRepository.GetListByMainIDs(conditionMainIDs);
            var addConditionDetails = new List<ConditionDetailInfo>();
            var addConditionMains = new List<ConditionMainInfo>();
            foreach (var mainItem in sourceConditionMains)
            {
                var addConditionMain = new ConditionMainInfo
                {
                    ConditionContent = mainItem.ConditionContent,
                    ConditionExpression = mainItem.ConditionExpression,
                    DataType = mainItem.DataType,
                    DataTypeValue = mainItem.DataTypeValue,
                    Description = mainItem.Description,
                    GroupType = mainItem.GroupType,
                    GroupTypeValue = mainItem.GroupTypeValue,
                    SourceID = addConditionRecord.ExaminationConditionRecordID,
                    SourceType = mainItem.SourceType,
                };
                addConditionMain.ConditionMainID = addConditionMain.GetId();
                addConditionMain.Add(employeeID).Modify(employeeID);
                addConditionMains.Add(addConditionMain);
                var conditionDetails = sourceConditionDetails.Where(m => m.ConditionMainID == mainItem.ConditionMainID);
                if (!conditionDetails.Any())
                {
                    continue;
                }
                foreach (var detailItem in conditionDetails)
                {
                    var addConditonDetail = new ConditionDetailInfo
                    {
                        ConditionMainID = addConditionMain.ConditionMainID,
                        Condition = detailItem.Condition,
                        ConditionType = detailItem.ConditionType,
                        ConditionValue = detailItem.ConditionValue,
                        ParentID = detailItem.ParentID,
                        ItemID = detailItem.ItemID,
                        Sort = detailItem.Sort
                    };
                    addConditonDetail.Add(employeeID).Modify(employeeID);
                    addConditonDetail.ConditionDetailID = addConditonDetail.GetId();
                    addConditionDetails.Add(addConditonDetail);
                }
            }
            await _unitOfWork.GetRepository<ConditionMainInfo>().InsertAsync(addConditionMains);
            await _unitOfWork.GetRepository<ConditionDetailInfo>().InsertAsync(addConditionDetails);
        }
    }
}
