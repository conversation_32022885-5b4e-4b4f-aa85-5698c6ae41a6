﻿namespace NursingManagement.ViewModels
{
    public class ApproveDetailView
    {
        /// <summary>
        /// 审批明细记录唯一码，Guid
        /// </summary>
        public string ApproveDetailID { get; set; }

        /// <summary>
        /// 关联审批主记录ID
        /// </summary>
        public string ApproveRecordID { get; set; }

        /// <summary>
        /// 审批部门ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 审批部门类型ID
        /// </summary>
        public int OrganizationType { get; set; }

        /// <summary>
        /// 岗位ID
        /// </summary>
        public int PostID { get; set; }

        /// <summary>
        /// 岗位类别
        /// </summary>
        public int PostTypeID { get; set; }

        /// <summary>
        /// 审批人员ID
        /// 如果人员岗位发生改变，则根据实际情况，进行业务交接（系统建立交接功能，改变审批链），或自己进行审签
        /// </summary>
        public string ApproveEmployeeID { get; set; }
        /// <summary>
        /// 审批人员名称
        /// </summary>
        public string ApproveEmployeeName { get; set; }

        /// <summary>
        /// 后一审批节点ID，空字符表示末尾节点
        /// </summary>
        public string NextNodeID { get; set; }

        /// <summary>
        /// 审批方式（【默认】1表示顺序签、2表示会签、3表示或签）
        /// </summary>
        public string ApproveModel { get; set; }

        /// <summary>
        /// 审批结果（1：同意、2：拒绝）
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// 审批建议
        /// </summary>
        public string ApproveSuggestions { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime? ApproveDateTime { get; set; }
        /// <summary>
        /// 预审批人集合
        /// </summary>
        public List<string> PreApproveEmployeeIDs { get; set; }
    }

}
