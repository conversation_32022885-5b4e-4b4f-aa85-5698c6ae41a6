﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class AnnualInterventionMainPrincipalRepository : IAnnualInterventionMainPrincipalRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public AnnualInterventionMainPrincipalRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 计划执行项目主表
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionMainPrincipalInfo>> GetInfosByPlanMainIDAsNoTracking(string planMainID)
        {
            return await _dbContext.AnnualInterventionMainPrincipalInfos.Where(m => planMainID == m.AnnualPlanMainID && m.DeleteFlag != "*")
                .AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 获取负责人ID列表
        /// </summary>
        /// <param name="interventionMainIDs"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, string[]>> GetPrincipalIDsByMainIDs(List<string> interventionMainIDs)
        {
            return await _dbContext.AnnualInterventionMainPrincipalInfos.Where(m => interventionMainIDs.Contains(m.AnnualInterventionMainID) && m.DeleteFlag != "*")
                .Select(m => new
                {
                    m.AnnualInterventionMainID,
                    m.EmployeeID
                })
                .GroupBy(m => m.AnnualInterventionMainID)
                .ToDictionaryAsync(m => m.Key, m => m.Select(n => n.EmployeeID).ToArray());
        }
        /// <summary>
        /// 获取负责人ID列表
        /// </summary>
        /// <param name="planMainID"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, string[]>> GetPrincipalIDsByPlanMainID(string planMainID)
        {
            return await _dbContext.AnnualInterventionMainPrincipalInfos.Where(m => planMainID == m.AnnualPlanMainID && m.DeleteFlag != "*")
                .Select(m => new
                {
                    m.AnnualInterventionMainID,
                    m.EmployeeID
                })
                .GroupBy(m => m.AnnualInterventionMainID)
                .ToDictionaryAsync(m => m.Key, m => m.Select(n => n.EmployeeID).ToArray());
        }
        /// <summary>
        /// 计划执行项目主表
        /// </summary>
        /// <param name="interventionMainIDs">计划执行项目主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionMainPrincipalInfo>> GetInfosByInterventionMainIDs(params string[] interventionMainIDs)
        {
            return await _dbContext.AnnualInterventionMainPrincipalInfos.Where(m => interventionMainIDs.Contains(m.AnnualInterventionMainID) && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 依据年度计划ID查找措施对应负责人数据
        /// </summary>
        /// <param name="planMainID">年度计划ID</param>
        /// <returns></returns>
        public async Task<List<KeyValueString>> GetByPlanMainID(string planMainID)
        {
            var list = await _dbContext.AnnualInterventionMainPrincipalInfos.Where(m => m.AnnualPlanMainID == planMainID && m.DeleteFlag!="*")
                .Select(m => new KeyValueString {Key= m.AnnualInterventionMainID, Value =m.EmployeeID }).ToListAsync();
            return list.GroupBy(m => new { m.Key, m.Value }).Select(m => m.First()).ToList();
        }

        /// <summary>
        /// 获取执行项目负责人
        /// </summary>
        /// <param name="apInterventionMainIDs">年度计划执行项目表ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string[]>> GetPrincipalIDsByAPInterventionMainIDs(string[] apInterventionMainIDs)
        {
            return await _dbContext.AnnualInterventionMainPrincipalInfos.Where(m => apInterventionMainIDs.Contains(m.AnnualInterventionMainID) && m.DeleteFlag != "*")
                .Select(m => new
                {
                    m.AnnualInterventionMainID,
                    m.EmployeeID
                })
                .GroupBy(m => m.AnnualInterventionMainID)
                .ToDictionaryAsync(m => m.Key, m => m.Select(n => n.EmployeeID).ToArray());
        }
    }
}
