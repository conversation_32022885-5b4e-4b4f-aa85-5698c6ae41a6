﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 问题整改表
    /// </summary>
    [Serializable]
    [Table("ProblemRectification")]
    public class ProblemRectificationInfo : MutiModifyInfo
    {
        /// <summary>
        /// 问题整改表主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ProblemRectificationID { get; set; }
        /// <summary>
        /// 整改人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 整改时间
        /// </summary>
        public DateTime RectificationDateTime { get; set; }
        /// <summary>
        /// 整改备注
        /// </summary>
        [Column(TypeName = "nvarchar(500)")]
        public string RectificationRemarks { get; set; }
        /// <summary>
        /// 质控表主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCMainID { get; set; }
        /// <summary>
        /// 字典分类
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string FormType { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 来源类型
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SourceType { get; set; }
        /// <summary>
        /// 来源序列号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }
    }
}
