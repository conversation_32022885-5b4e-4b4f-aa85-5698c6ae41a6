﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    /// 审批业务主表ID
    /// </summary>
    [Serializable]
    [Table("ApproveMain")]
    public class ApproveMainInfo :MutiModifyInfo
    {
        /// <summary>
        /// 审批业务所属节点ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ApproveMainID { get; set; }

        /// <summary>
        /// 审批业务记录表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveRecordID { get; set; }

        /// <summary>
        /// 下一个需要审批的业务节点主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string NextApproveMainID { get; set; }

        /// <summary>
        /// 当前审批业务进行到的节点，对应审批流程表中的NodeID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveNodeID { get; set; }

        /// <summary>
        /// 审批方式（【默认】1表示顺序签、2表示会签、3表示或签）
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string ApproveModel { get; set; }

        /// <summary>
        /// 对应ApproveProcessNode中节点的实际审批完成时间，完成后进行到下一个审批节点
        /// </summary>
        public DateTime? ApproveDateTime { get; set; }

        /// <summary>
        /// 审批结果（1：同意、2：拒绝，空为未审批）
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string StatusCode { get; set; }

    }

}
