﻿using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 直接从数据库中获取组装好的人员信息
    /// </summary>
    public interface IEmployeeRepository
    {
        /// <summary>
        /// 获取部门员工信息
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="includingResignedFlag">是否包含离职人员</param>
        /// <returns></returns>
        Task<List<EmployeeForSchedulingView>> GetEmployeeDataByDepartmentID(int departmentID, bool includingResignedFlag = false);

        /// <summary>
        /// 根据人员编号获取员工信息
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeForSchedulingView>> GetEmployeeDataByEmployeeIDs(List<string> employeeIDs);
        /// <summary>
        /// 根据部门ID获取所有员工的工号
        /// </summary>
        /// <param name="departmentID">部门ID（可空）</param>
        /// <returns></returns>
        Task<List<EmployeeRoleView>> GetEmployeeIDsByDepartmentIDAsync(int? departmentID);
    }
}
