﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class ExaminationPaperMainRepository(NursingManagementDbContext context) : IExaminationPaperMainRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = context;

        /// <summary>
        /// 根据新增时间获取数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="departmentIDs">部门ID,为空时不限制部门条件</param>
        /// <returns></returns>
        public async Task<List<ExaminationPaperMainInfo>> GetListByDate(DateTime startDate, DateTime endDate, List<int> departmentIDs)
        {
            return await _nursingManagementDbContext.ExaminationPaperMainInfos
                .Where(m => m.AddDateTime >= startDate.Date && m.AddDateTime < endDate.Date.AddDays(1) && m.DeleteFlag != "*")
                .IfWhere(departmentIDs != null && departmentIDs.Count > 0, m =>
                    //TODO： 补充m.DepartmentID == null 的条件，解决试用环境旧数据不显示问题，正式上线时删除m.DepartmentID == null
                    (m.DepartmentID.HasValue && departmentIDs.Contains(m.DepartmentID.Value)) || m.DepartmentID == null)
                .ToListAsync();

        }
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="mainID">主键</param>
        /// <returns></returns>
        public async Task<ExaminationPaperMainInfo> GetDataByID(string mainID)
        {
            return await _nursingManagementDbContext.ExaminationPaperMainInfos.FirstOrDefaultAsync(m => m.ExaminationPaperMainID == mainID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="mainIDs">试卷主表ID</param>
        /// <returns></returns>
        public async Task<List<ExaminationPaperMainInfo>> GetDataByIDList(List<string> mainIDs)
        {
            return await _nursingManagementDbContext.ExaminationPaperMainInfos.Where(m => mainIDs.Any(n => n == m.ExaminationPaperMainID) && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<SelectOptionsView>> GetDataByPaperType(string paperType, List<int> switchDeptIDs)
        {
            return await _nursingManagementDbContext.ExaminationPaperMainInfos.Where(m => m.PaperType == paperType && m.DeleteFlag != "*")
                .IfWhere(switchDeptIDs != null && switchDeptIDs.Count > 0, m => m.DepartmentID.HasValue && switchDeptIDs.Contains(m.DepartmentID.Value))
                .Select(m => new SelectOptionsView
                {
                    Label = m.PaperTitle,
                    Value = m.ExaminationPaperMainID,
                }).ToListAsync();
        }
        /// <summary>
        /// 根据模版ID获取试卷数据
        /// </summary>
        /// <param name="paperIDs"></param>
        /// <returns></returns>
        public async Task<List<ExaminationPaperMainInfo>> GetDataByPaperIDList(List<string> paperIDs)
        {
            return await _nursingManagementDbContext.ExaminationPaperMainInfos.Where(m => paperIDs.Any(n => n == m.PaperID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据组卷条件记录ID获取数据
        /// </summary>
        /// <param name="examinationConditionRecordIDs"></param>
        /// <returns></returns>
        public async Task<List<ExaminationPaperMainInfo>> GetDataByExaminationConditionRecordIDList(List<string> examinationConditionRecordIDs)
        {
            return await _nursingManagementDbContext.ExaminationPaperMainInfos.Where(m => examinationConditionRecordIDs.Any(n => n == m.ExaminationConditionRecordID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据试卷获取对应的题库
        /// </summary>
        /// <param name="paperIDs"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetBankIdDictByPaperIds(List<string> paperIDs)
        {
            return await _nursingManagementDbContext.ExaminationPaperMainInfos.Where(m => paperIDs.Contains(m.ExaminationPaperMainID) && m.DeleteFlag != "*")
                .ToDictionaryAsync(m => m.ExaminationPaperMainID, m => m.QuestionBankID);

        }
    }
}
