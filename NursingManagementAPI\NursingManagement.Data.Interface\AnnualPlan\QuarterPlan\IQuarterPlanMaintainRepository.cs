﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IQuarterPlanMaintainRepository
    {

        /// <summary>
        /// 获取季度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表iD</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        Task<string> GetQuarterPlanMainID(string annualPlanMainID, int quarter);

        /// <summary>
        /// 获取季度计划
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        Task<QuarterPlanMainInfo> GetQuarterPlanMain(string quarterPlanMainID);

        /// <summary>
        /// 获取季度计划状态
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        Task<int> GetQuarterPlanStatus(string quarterPlanMainID);

        /// <summary>
        /// 获取季度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <returns></returns>
        Task<Dictionary<string, Dictionary<int, string>>> GetQuarterToID(string[] annualPlanMainIDs);
        /// <summary>
        /// 获取季度计划
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        Task<TieredPlanWorksByType[]> GetQuarterWorks(string quarterPlanMainID);
        /// <summary>
        /// 获取工作ID与工作内容
        /// </summary>
        /// <param name="workIDs">工作ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs);

        /// <summary>
        /// 获取选择工作视图
        /// </summary>
        /// <param name="apMainIDs">年度计划主表ID</param>
        /// <returns></returns>
        Task<List<TieredPlanWorksByPlanThenType>> GetWorkViews(string[] apMainIDs, int? includeApInterventionID = null, int[] excludeApInterventionIDs = null, bool onlyPriority = false, bool includeTempWork = true);
        /// <summary>
        /// 获取季度计划工作（供月度计划使用）
        /// </summary>
        /// <param name="annualPlanMainID">年度计划</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        Task<TieredPlanWorksByPlanThenType> GetQuarterPlanWorksForMonthlyPlan(string annualPlanMainID, int quarter);
        /// <summary>
        /// 获取季度计划工作
        /// </summary>
        /// <param name="workIDs">工作ID集合</param>
        /// <returns></returns>
        Task<List<QuarterPlanDetailInfo>> GetQuarterPlanWorks(string[] workIDs);
        /// <summary>
        /// 获取季度计划工作
        /// </summary>
        /// <param name="quarterWorkID">主键</param>
        /// <returns></returns>
        Task<QuarterPlanDetailInfo> GetQuarterWork(string quarterWorkID);

        /// <summary>
        /// 获取某季度计划工作关联字典ID集合
        /// </summary>
        /// <param name="qpMainID">季度计划主表ID</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        Task<int[]> GetQpWorkInterventionIDs(string qpMainID, int quarter);
        /// <summary>
        /// 获取季度计划工作关联字典ID集合
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="departmentIDs">病区集合</param>
        /// <returns></returns>
        Task<List<QuarterPlanBrowseView>> GetQuarterPlanMainViewsByYearAndDepartmentIDs(int year, IEnumerable<int> departmentIDs);
        /// <summary>
        /// 获取导出视图
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        Task<QuarterPlanExportView> GetExportView(string quarterPlanMainID);
        /// <summary>
        /// 获取季度计划预览视图
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划ID</param>
        /// <returns></returns>
        Task<QuarterPlanPreview> GetQuarterPlanPreview(string quarterPlanMainID);
    }
}
