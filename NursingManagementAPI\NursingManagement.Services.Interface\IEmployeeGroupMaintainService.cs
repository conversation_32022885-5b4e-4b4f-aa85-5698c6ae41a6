﻿using NursingManagement.ViewModels.EmployeeGroup;

namespace NursingManagement.Services.Interface;

public interface IEmployeeGroupMaintainService
{
    #region 查询
    /// <summary>
    /// 获取用户组列表
    /// </summary>
    /// <param name="employeeID">当前用户</param>
    /// <returns></returns>
    Task<EmployeeGroupVo[]> GetEmployeeGroups(string employeeID);
    #endregion

    #region 命令
    /// <summary>
    /// 新建用户组
    /// </summary>
    /// <param name="dto">参数</param>
    /// <param name="employeeID">新增人</param>
    /// <returns></returns>
    Task<int> AddEmployeeGroup(AddEmployeeGroupDto dto, string employeeID);
    /// <summary>
    /// 更新用户组
    /// </summary>
    /// <param name="dto">参数</param>
    /// <returns></returns>
    Task<bool> UpdateEmployeeGroup(UpdateEmployeeGroupDto dto, string employeeID);
    /// <summary>
    /// 删除用户组
    /// </summary>
    /// <param name="groupID">主键</param>
    /// <param name="employeeID">修改人</param>
    /// <returns></returns>
    Task<bool> DeleteEmployeeGroup(int groupID, string employeeID);
    #endregion
}
