﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class ApproveProcessNodeDetailRepository : IApproveProcessNodeDetailRepository
    {
        private readonly NursingManagementDbContext _context;

        public ApproveProcessNodeDetailRepository(NursingManagementDbContext context)
        {
            _context = context;
        }
        /// <summary>
        /// 根据审批流程ID获取审批流程节点明细
        /// </summary>
        /// <param name="processID">流程ID</param>
        /// <returns></returns>
        public async Task<List<ApproveProcessNodeDetailInfo>> GetInfosByProcessID(string processID)
        {
            return await _context.ApproveProcessNodeDetailInfos.Where(m => m.ApproveProcessID == processID && m.DeleteFlag != "*")
                .ToListAsync();
        }
        /// <summary>
        /// 根据审批流程ID获取审批流程节点明细View
        /// </summary>
        /// <param name="processID">流程ID</param>
        /// <returns></returns>
        public async Task<List<ApproveProcessNodeDetail>> GetViewsByProcessID(string processID)
        {
            return await _context.ApproveProcessNodeDetailInfos.Where(m => m.ApproveProcessID == processID && m.DeleteFlag != "*")
                .Select(m => new ApproveProcessNodeDetail
                {
                    ApproveNodeID = m.ApproveNodeID,
                    NodeDetailType = m.NodeDetailType,
                    DataValue = m.DataValue,
                    DepartmentID = m.DepartmentID
                }).ToListAsync();
        }
        /// <summary>
        /// 根据审批流程节点ID获取当前节点下的明细数据
        /// </summary>
        /// <param name="approveNodeID">审批节点ID</param>
        /// <returns></returns>
        public async Task<List<ApproveProcessNodeDetailInfo>> GetNodeDetailsAsync(string approveNodeID)
        {
            return await _context.ApproveProcessNodeDetailInfos.Where(m => m.ApproveNodeID == approveNodeID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取审批流程中指定到岗的的岗位集合
        /// </summary>
        /// <param name="processID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetJobCodesByProcessIDAndNodeTypeAsync(string processID)
        {
            return await _context.ApproveProcessNodeDetailInfos.Where(m => m.DeleteFlag != "*" && m.NodeDetailType == ApproveProcessNodeDetailType.Position)
                .IfWhere(!string.IsNullOrEmpty(processID), m => m.ApproveProcessID == processID)
                .Select(m=>m.DataValue).ToListAsync();
        }
    }
}
