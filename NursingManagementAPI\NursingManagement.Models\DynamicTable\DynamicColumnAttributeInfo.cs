﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 表格列属性配置
    /// </summary>
    [Serializable]
    [Table("DynamicColumnAttribute")]
    public class DynamicColumnAttributeInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int DynamicColumnAttributeID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 表格ID
        /// </summary>
        public int DynamicTableListID { get; set; }
        /// <summary>
        ///列ID,对应动态表格字段字典
        /// </summary>
        public int ColumnID { get; set; }
        /// <summary>
        /// 字段阶层
        /// </summary>
        [Column(TypeName = "TINYINT")]
        public int Level { get; set; }
        /// <summary>
        /// 列属性Code
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AttributeCode { get; set; }
        /// <summary>
        /// 属性值
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AttributeValue { get; set; }
        /// <summary>
        /// 使用人员,999999表示系统配置
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string UserID { get; set; }
    }
}
