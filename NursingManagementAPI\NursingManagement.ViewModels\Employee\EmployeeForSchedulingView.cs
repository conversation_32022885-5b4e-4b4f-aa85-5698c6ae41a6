﻿namespace NursingManagement.ViewModels
{
    public class EmployeeForSchedulingView
    {
        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        /// 护理能级ID
        /// </summary>
        public int? CapabilityLevelID { get; set; }

        /// <summary>
        /// 护理能级名称
        /// </summary>
        public string CapabilityLevel { get; set; }

        /// <summary>
        /// 带教老师
        /// </summary>
        public string TeacherEmployeeID { get; set; }

        /// <summary>
        /// 在职状态 1在职，0离职
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// 人员借调提示信息
        /// </summary>
        public string TipContent { get; set; }

        /// <summary>
        /// 被借调到其他部门的借调记录
        /// </summary>
        public List<EmployeeSecondmentView> SecondedList { get; set; }

        /// <summary>
        /// 借调到本部门的借调记录
        /// </summary>
        public List<EmployeeSecondmentView> SecondmentList { get; set; }

        /// <summary>
        /// 根据人员能级允许排班的岗位ID集合
        /// </summary>
        public List<int> DepartmentPostIDs { get; set; }

        /// <summary>
        /// 转入部门日期
        /// </summary>
        public DateTime? TransferInDate { get; set; }

        /// <summary>
        /// 转出部门日期
        /// </summary>
        public DateTime? TransferOutDate { get; set; }

        /// <summary>
        /// 已休产假天数
        /// </summary>
        public int MaternityLeaveDays { get; set; }

        /// <summary>
        /// 已休年休假天数
        /// </summary>
        public int AnnualLeaveDays { get; set; }
    }
}
