﻿using NursingManagement.Common;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IUserLoginService
    {
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="loginParam"></param>
        /// <returns></returns>
        Task<Session> Login(LoginParam loginParam);
        /// <summary>
        /// 根据Token获取Session
        /// </summary>
        /// <returns></returns>
        Task<Session> GetSessionByToken();
        /// <summary>
        /// 绑定微信
        /// </summary>
        /// <param name="loginParam"></param>
        /// <returns></returns>
        Task<bool> BindWechat(BindWechatParam bindWechatParam);
        /// <summary>
        /// 退出登录
        /// </summary>
        /// <returns></returns>
        Task<bool> LogOut();
        /// <summary>
        /// 更新session中的部门ID
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<bool> UpdateDepartmentIDOfSessionAsync(int departmentID);
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="loginParam">登录信息</param>
        /// <returns></returns>
        Task<bool> ChangePassword(LoginParam loginParam);
        /// <summary>
        /// 转换其他系统的部门ID为OrganizationType=1的部门ID
        /// </summary>
        /// <param name="departmentID">其他系统部门ID,CCC对应OrganizationType=3</param>
        /// <param name="sourceSystem">来源系统，如CCC</param>
        /// <returns>转换后的部门ID或者null</returns>
        Task<int?> TransformDepartmentIDAsync(int departmentID, string sourceSystem);
        /// <summary>
        /// 单点登录验证
        /// </summary>
        /// <param name="token"></param>
        /// <param name="clientType"></param>
        /// <param name="loginType"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        Task<Session> SSOUserCheck(string token, int clientType, string loginType, string hospitalID, int language);
    }
}