﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 文件管理
    /// </summary>
    [Produces("application/json")]
    [Route("api/file")]
    [EnableCors("any")]
    public class FileController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IFileService _fileService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="session"></param>
        /// <param name="fileService"></param>
        public FileController(
            ISessionService session,
            IFileService fileService)
        {
            _session = session;
            _fileService = fileService;
        }
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="file">file参数不可改变，改变要一同改变前端</param>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UploadFile")]
        public async Task<IActionResult> UploadFile(IFormFile file, [FromForm] DocumentView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var data = await _fileService.UpLoadFile(file, view);
            // 根据返回信息判断是否成功
            if (data == null)
            {
                result.Error("文件上传失败！");
            }
            result.Data = data;
            return result.ToJson();
        }
        /// <summary>
        /// 上传富文本相关的文件（图片，文件、视频）
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UploadRichTextFile")]
        public async Task<IActionResult> UploadRichTextFile(IFormFile file)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var data = await _fileService.UploadRichTextFile(file, session.EmployeeID, session.UserName);
            result.Data = data;
            // 根据返回信息判断是否成功
            return result.ToJson();
        }
        /// <summary>
        /// 上传文件(旧的文件上传，后续要换成UploadFile串到档案管理系统)
        /// </summary>
        /// <param name="file">file参数不可改变，改变要一同改变前端</param>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Upload")]
        public async Task<IActionResult> Upload(IFormFile file, [FromForm] FileUploadView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var (fileID, errorMessage) = await _fileService.UpLoadFileAsync(file, view);
            // 根据返回信息判断是否成功
            if (!string.IsNullOrEmpty(fileID))
            {
                result.Sucess();
                result.Data = fileID;
            }
            if (!string.IsNullOrEmpty(errorMessage))
            {
                result.Message = errorMessage;
            }
            return result.ToJson();
        }
        /// <summary>
        /// 根据分类获取文件集合
        /// </summary>
        /// <param name="fileClass">文件分类；1：公共文件，2：消息附件</param>
        /// <param name="sourceID">文件来源</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetFileListByClassAndSource")]
        public async Task<IActionResult> GetFileListByClassAndSource(string fileClass, string sourceID)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _fileService.GetFileListByClassAndSourceAsync(fileClass, sourceID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="fileID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteFile")]
        public async Task<IActionResult> DeleteFile(string fileID)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _fileService.DeleteFileAsync(fileID, session.EmployeeID);
            return result.ToJson();
        }
    }

}
