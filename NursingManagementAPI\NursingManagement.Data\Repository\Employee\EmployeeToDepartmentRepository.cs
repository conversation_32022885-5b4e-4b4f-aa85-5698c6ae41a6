﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeToDepartmentRepository : IEmployeeToDepartmentRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public EmployeeToDepartmentRepository(
            NursingManagementDbContext dbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService
            )
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }
        /// <summary>
        /// 获取所有人员多组织架构部门数据
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, EmployeeToDepartmentInfo[]>> GetEmployeeToDepartments()
        {
            var data = await this.GetAll<EmployeeToDepartmentInfo>();
            return data.Where(m => m.DeleteFlag != "*").GroupBy(m => m.EmployeeID)
                .ToDictionary(m => m.Key, m => m.ToArray());
        }
        /// <summary>
        /// 根据员工ID获取部门信息
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<EmployeeToDepartmentInfo[]> GetDepartmentsByEmployeeID(string employeeID)
        {
            var data = await this.GetAll<EmployeeToDepartmentInfo>();
            return data.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToArray();
        }

        /// <summary>
        /// 根据员工ID获取多组织架构部门信息
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<EmployeeToDepartmentInfo[]> GetNoCacheInfosByEmployeeID(string employeeID)
        {
            string key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _dbContext.EmployeeToDepartmentInfos.Where(m => m.EmployeeID == employeeID && m.HospitalID == hospitalID && m.DeleteFlag != "*").ToArrayAsync();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var data = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _dbContext.EmployeeToDepartmentInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return data;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeToDepartment.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 根据员工编号和组织架构类型获取员工部门信息
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        public async Task<List<EmployeeToDepartmentInfo>> GetDepartmentByEmployeeIDAndType(string employeeID, string organizationType)
        {
            var data = await this.GetAll<EmployeeToDepartmentInfo>();
            return data.Where(m => m.EmployeeID == employeeID && m.OrganizationType == organizationType).ToList();
        }

        /// <summary>
        /// 根据部门编号和组织架构类型获取员工信息
        /// </summary>
        /// <param name="departmentID">部门编号</param>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        public async Task<List<string>> GetEmployeeByDepartmentIDAndType(int departmentID, string organizationType)
        {
            var data = await this.GetAll<EmployeeToDepartmentInfo>();
            return data.Where(m => m.DepartmentID == departmentID && m.OrganizationType == organizationType).Select(m=>m.EmployeeID).ToList();
        }

        /// <summary>
        /// 根据人员工号和组织架构类型获取部门信息
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns>人员部门信息列表</returns>
        public async Task<List<EmployeeToDepartmentInfo>> GetByOrganizationTypeAndEmployeeIDAsync(string employeeID, string organizationType)
        {
            var data = await this.GetAll<EmployeeToDepartmentInfo>();
            return data.Where(m => m.EmployeeID == employeeID && m.OrganizationType == organizationType && m.DeleteFlag != "*")
                .ToList();
        }
    }
}
