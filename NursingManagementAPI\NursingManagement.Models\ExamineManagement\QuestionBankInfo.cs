﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 考核题库
    /// </summary>
    [Serializable]
    [Table("QuestionBank")]
    public class QuestionBankInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考核题库ID(主键ID)
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string QuestionBankID { get; set; }
        /// <summary>
        /// 考核题库名称
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Content { get; set; }
        /// <summary>
        /// 考核题库分类(配置在AdministrationDictionary中)
        /// </summary>
        public int QuestionBankType { get; set; }
        /// <summary>
        /// 来源类别(培训：Training)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SourceType { get; set; }
        /// <summary>
        /// 来源ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }
        /// <summary>
        ///  年份(版本)
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 是否是实操类标记
        /// </summary>
        public bool IsPractical { get; set; }
        /// <summary>
        /// 组织部门编码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string OrganizationalDepartmentCode { get; set; }
        /// <summary>
        /// 父层级ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ParentID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
