﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class DepartmentPostView : DepartmentPostInfo
    {
        /// <summary>
        /// 全院岗位名称
        /// </summary>
        public string PostName { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyPerson { get; set; }

        /// <summary>
        /// 新增人
        /// </summary>
        public string AddPerson { get; set; }

        /// <summary>
        /// 夏季时间范围
        /// </summary>
        public string SummerTimeRange { get; set; }

        /// <summary>
        /// 冬季时间范围
        /// </summary>
        public string WinterTimeRange { get; set; }

        /// <summary>
        /// 岗位类型
        /// </summary>
        public string PostType { get; set; }

        /// <summary>
        /// 岗位性质
        /// </summary>
        public string PostNature { get; set; }

        /// <summary>
        /// 岗位班别
        /// </summary>
        public string PostShift { get; set; }

        /// <summary>
        /// 半天岗计算考勤方式描述
        /// </summary>
        public string HalfDayAttendanceCalcDescription { get; set; }

        public DepartmentPostView(DepartmentPostInfo dp)
        {
            CopyParentPropertiesToChild.SynchronizationProperties(dp, this);
        }
    }
}
