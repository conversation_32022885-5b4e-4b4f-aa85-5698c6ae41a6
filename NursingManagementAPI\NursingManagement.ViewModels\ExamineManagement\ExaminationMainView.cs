﻿namespace NursingManagement.ViewModels
{
    public class ExaminationMainView
    {
        /// <summary>
        /// 考核主表ID
        /// </summary>
        public string ExaminationMainID { get; set; }

        /// <summary>
        /// 试卷主记录ID
        /// </summary>
        public string ExaminationPaperMainID { get; set; }

        /// <summary>
        /// 考核记录ID
        /// </summary>
        public string ExaminationRecordID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 员工编号（被考核人）
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 被考核人
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        /// 考试状态（7 待预约、1待考核，2 签到，3 考核中、4 已交卷、5 作弊交卷、6 强制交卷）(配置在SettingDictionary)
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }

        /// <summary>
        /// 主考人（考察老师）
        /// </summary>
        public List<string> Examiners { get; set; }

        /// <summary>
        /// 主考人姓名（考察老师）
        /// </summary>
        public string ExaminerName { get; set; }

        /// <summary>
        /// 分数
        /// </summary>
        public decimal? Score { get; set; }

        /// <summary>
        /// 是否是补考
        /// </summary>
        public bool RetakeFlag { get; set; }

        /// <summary>
        /// 作弊次数
        /// </summary>
        public int? CheatedCount { get; set; }

        /// <summary>
        /// 考试开始时间（进入考试时间）
        /// </summary>
        public DateTime? StartDateTime { get; set; }

        /// <summary>
        /// 考试结束时间（交卷时间）
        /// </summary>
        public DateTime? EndDateTime { get; set; }

        /// <summary>
        /// 考核名称
        /// </summary>
        public string ExamineName { get; set; }

        /// <summary>
        /// 考核时长
        /// </summary>
        public decimal? Duration { get; set; }

        /// <summary>
        /// 考试开始时间
        /// </summary>
        public DateTime ExamineStartDateTime { get; set; }

        /// <summary>
        /// 考试截止时间
        /// </summary>
        public DateTime ExamineEndDateTime { get; set; }

        /// <summary>
        /// 考核类型（1、试卷考核 2、操作考核 3、个人练习）
        /// </summary>
        public string ExamineType { get; set; }

        /// <summary>
        /// 被考核人层级
        /// </summary>
        public string CapabilityLevelName { get; set; }

        /// <summary>
        /// 被考核人层级
        /// </summary>
        public int? CapabilityLevelID { get; set; }

        /// <summary>
        /// 补考分数
        /// </summary>
        public decimal? RetakeScore { get; set; }

        /// <summary>
        /// 及格分数
        /// </summary>
        public decimal? PassingScore { get; set; }

        /// <summary>
        /// 最低答卷时长
        /// </summary>
        public short? MinAnswerTime { get; set; }

        /// <summary>
        /// 考核明细作答记录
        /// </summary>
        public string DetailResponseRecord { get; set; }

        /// <summary>
        /// 是否需要签到标记
        /// </summary>
        public bool SignInFlag { get; set; }

        /// <summary>
        /// 一页一题标记
        /// </summary>
        public bool? OnePageQuestionFlag { get; set; }

        /// <summary>
        /// 考核记录修改人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 考核级别
        /// </summary>
        public string ExaminationLevel { get; set; }

        /// <summary>
        /// 考核级别名称
        /// </summary>
        public string ExaminationLevelName { get; set; }
    }
}
