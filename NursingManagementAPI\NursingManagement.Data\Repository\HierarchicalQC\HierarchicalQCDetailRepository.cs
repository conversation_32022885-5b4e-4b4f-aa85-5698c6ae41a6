﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCDetailRepository : IHierarchicalQCDetailRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly IHierarchicalQCAssessListRepository _hierarchicalQCAssessListRepository;

        public HierarchicalQCDetailRepository(NursingManagementDbContext dbContext, IHierarchicalQCAssessListRepository hierarchicalQCAssessListRepository)
        {
            _dbContext = dbContext;
            _hierarchicalQCAssessListRepository = hierarchicalQCAssessListRepository;
        }

        /// <summary>
        /// 根据质控主记录获取明细数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCDetailInfo>> GetQCDetailByQCRecordID(string id)
        {
            return await _dbContext.HierarchicalQCDetailInfos.Where(m => m.HierarchicalQCRecordID == id && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取质控明细内容
        /// </summary>
        /// <param name="careMainID">质控维护记录主键ID</param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCDetailView>> GetQCDetailByQCMainID(string careMainID)
        {
            return await _dbContext.HierarchicalQCDetailInfos.Where(m => m.HierarchicalQCMainID == careMainID && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCDetailView
                {
                    ItemID = m.HierarchicalQCAssessListID,
                    GroupID = m.GroupID ?? 0,
                    Value = m.Result,
                    Problem = m.Problem,
                    BrightSpot = m.BrightSpot,
                    HierarchicalQCDetailID = m.HierarchicalQCDetailID,
                    Remark = m.Remark,
                    ParentID = m.ParentID
                }).ToListAsync();
        }

        /// <summary>
        /// 获取质控明细内容
        /// </summary>
        /// <param name="careMainID">质控维护记录主键ID</param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfoByQCMainID(string careMainID)
        {
            return await _dbContext.HierarchicalQCDetailInfos.Where(m => m.HierarchicalQCMainID == careMainID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取需追踪考核明细
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCDetailInfo>> GetQCDetailByQCMainIDs(List<string> careMainIDs)
        {
            return await _dbContext.HierarchicalQCDetailInfos.Where(m => careMainIDs.Contains(m.HierarchicalQCMainID) && m.DeleteFlag != "*"
            && !string.IsNullOrEmpty(m.Result)).ToListAsync();
        }

        /// <summary>
        /// 获取考核内容
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        public async Task<List<NormalWorkingDetail>> GetQCDetailsByQCMainIDs(List<string> careMainIDs)
        {
            var data = await (from a in _dbContext.HierarchicalQCDetailInfos
                              join b in _dbContext.HierarchicalQCAssessListInfos on a.HierarchicalQCAssessListID equals b.HierarchicalQCAssessListID
                              where careMainIDs.Contains(a.HierarchicalQCMainID) && a.DeleteFlag != "*" && !string.IsNullOrEmpty(a.Result)
                              select new NormalWorkingDetail
                              {
                                  HierarchicalQCMainID = a.HierarchicalQCMainID,
                                  HierarchicalQCDetailID = a.HierarchicalQCAssessListID,
                                  Name = b.ContentName,
                              }).ToListAsync();
            return data;
        }

        /// <summary>
        /// 根据质控主记录获取明细数据
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfosByQCMainIDs(List<string> careMainIDs)
        {
            var data = await _dbContext.HierarchicalQCDetailInfos.Where(m => careMainIDs.Contains(m.HierarchicalQCMainID) && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCDetailInfo
                {
                    HierarchicalQCMainID = m.HierarchicalQCMainID,
                    HierarchicalQCAssessListID = m.HierarchicalQCAssessListID,
                    Result = m.Result,
                    GroupID = m.GroupID,
                    ParentID = m.ParentID,
                }).ToListAsync();
            return data;
        }

        /// <summary>
        /// 根据维护记录ID集合和组ID集合获取数据
        /// </summary>
        /// <param name="careMainIDs"></param>
        /// <param name="groupIDList"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfosByQCMainIDs(List<string> careMainIDs, List<int> groupIDList)
        {
            var data = await _dbContext.HierarchicalQCDetailInfos.Where(m => careMainIDs.Contains(m.HierarchicalQCMainID) && groupIDList.Contains(m.HierarchicalQCAssessListID) && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCDetailInfo
                {
                    HierarchicalQCMainID = m.HierarchicalQCMainID,
                    HierarchicalQCAssessListID = m.HierarchicalQCAssessListID,
                    Result = m.Result,
                    ParentID = m.ParentID,
                    GroupID = m.GroupID,
                }).ToListAsync();
            return data;
        }

        /// <summary>
        /// 根据SubjectId获取ParentId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<int>> GetParentIdBySubjectId(string id)
        {
            return await (from a in _dbContext.HierarchicalQCRecordInfos
                          join b in _dbContext.HierarchicalQCDetailInfos
                          on a.HierarchicalQCRecordID equals b.HierarchicalQCRecordID
                          where a.DeleteFlag != "*"
                                && b.DeleteFlag != "*"
                                && b.ParentID != null
                                && a.HierarchicalQCSubjectID == id
                          select b.ParentID.Value).Distinct().ToListAsync();
        }

        /// 根据质控主记录获取明细数据 </summary> <param name="careMainID">主记录表主键</param> <returns></returns>
        public async Task<List<HierarchicalQCDetailInfo>> GetQCDetailInfosByQCMainID(string careMainID)
        {
            return await _dbContext.HierarchicalQCDetailInfos.Where(m => careMainID == m.HierarchicalQCMainID && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCDetailInfo
                {
                    HierarchicalQCMainID = m.HierarchicalQCMainID,
                    HierarchicalQCAssessListID = m.HierarchicalQCAssessListID,
                    Result = m.Result,
                    GroupID = m.GroupID,
                    ParentID = m.ParentID,
                    Problem = m.Problem,
                    BrightSpot = m.BrightSpot
                }).ToListAsync();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCDetailView>> GetQCExportDatas(string dynamicFormRecordID)
        {
            return await (from a in _dbContext.DynamicFormDetailInfos
                          join b in _dbContext.DynamicFormDetailAttributeInfos
                              on a.DynamicFormDetailID equals b.DynamicFormDetailID
                          where a.DynamicFormRecordID == dynamicFormRecordID
                                && a.DeleteFlag != "*"
                                && b.DeleteFlag != "*"
                                && b.ComponentAttributeID == 4
                          select new HierarchicalQCDetailView
                          {
                              AssessListID = a.ItemID,
                              ComponentListID = a.ComponentListID,
                              Sort = a.Sort,
                              AttributeValue = b.AttributeValue
                          }).ToListAsync();
        }
    }
}
