﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class RuleDetailListRepository : IRuleDetailListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public RuleDetailListRepository(
            NursingManagementDbContext nursingManagementDbContext
            , IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 根据主表ID获取数据
        /// </summary>
        /// <param name="ruleListID"></param>
        /// <returns></returns>
        public async Task<List<RuleDetailListInfo>> GetListByID(int ruleListID)
        {
            var datas = await GetAllCacheAsync();
            return datas.Where(m => m.RuleListID == ruleListID).ToList();
        }
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<RuleDetailListInfo>> GetAllCacheAsync()
        {
            return (List<RuleDetailListInfo>)await GetCacheAsync();
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.RuleDetailListInfos.Where(m => m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.RuleDetailListInfo.GetKey(_sessionCommonServer);
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }
        /// <summary>
        /// 根据主表ID获取数据
        /// </summary>
        /// <param name="ruleListIDs"></param>
        /// <returns></returns>
        public async Task<List<RuleDetailListInfo>> GetListByIDs(List<int> ruleListIDs)
        {
            var datas = await GetAllCacheAsync();
            return datas.Where(m => ruleListIDs.Contains(m.RuleListID)).Select(m => new RuleDetailListInfo
            {
                RuleDetailListID = m.RuleDetailListID,
                RuleListID = m.RuleListID,
                Value = m.Value,
                Sort = m.Sort,
            }).OrderBy(m=>m.Sort).ToList();
        }
        /// <summary>
        /// 获取最大值（主键）
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxID()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var IDList = await _nursingManagementDbContext.RuleDetailListInfos.Where(m => m.Language == language)
                            .MaxAsync(m => (int?)m.RuleDetailListID);
            return IDList ?? 0;
        }

    }
}
