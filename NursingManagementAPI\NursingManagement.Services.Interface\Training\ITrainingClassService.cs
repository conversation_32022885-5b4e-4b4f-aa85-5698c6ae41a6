﻿using NursingManagement.Common;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface ITrainingClassService
    {
        /// <summary>
        /// 获取培训群组列表
        /// </summary>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<List<TrainingClassView>> GetTrainingClassList(Session session);

        /// <summary>
        /// 保存培训群组
        /// </summary>
        /// <param name="trainingClassView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SaveTrainingClass(TrainingClassView trainingClassView, string employeeID);

        /// <summary>
        /// 删除培训群组
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteTrainingClassByID(string trainingClassMainID, string employeeID);

        /// <summary>
        /// 根据培训群组ID获取培训群组课程列表
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        Task<List<TrainingClassCourseView>> GetTrainingClassCourseListByMainID(string trainingClassMainID);
        /// <summary>
        /// 获取佩璇群组下拉选项集合
        /// </summary>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetTrainClassOptions();
    }
}