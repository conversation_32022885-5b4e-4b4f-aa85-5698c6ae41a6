﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 排班Service
    /// </summary>
    public class RemainingRestDaysService : IRemainingRestDaysService
    {
        private readonly IUnitOfWork _unitOfWork;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IEmployeeRepository _employeeRepository;
        private readonly IRemainingRestDaysRepository _remainingRestDaysRepository;
        private readonly SessionCommonServer _sessionCommonServer;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        public RemainingRestDaysService(IUnitOfWork unitOfWork
            , IEmployeeRepository employeeRepository
            , IRemainingRestDaysRepository remainingRestDaysRepository
            , SessionCommonServer sessionCommonServer
        )
        {
            _unitOfWork = unitOfWork;
            _employeeRepository = employeeRepository;
            _remainingRestDaysRepository = remainingRestDaysRepository;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 获取剩余休假天数集合
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<TableView> GetRemainingRestDaysData(int year,int departmentID)
        {
            var tableView = new TableView();
            // 组装第一列（人员）
            var employeeColumn = CreateEmployeeColumns();
            // 组装日期列
            var dateColumns = CreateColumnsByDate(year);
            tableView.Columns.Add(employeeColumn);
            tableView.Columns.AddRange(dateColumns);
            //获取部门人员
            var employeeList = await _employeeRepository.GetEmployeeDataByDepartmentID(departmentID);
            var employeeRemainingRestDays = await _remainingRestDaysRepository.GetRemainingRestDaysByDepartmentID(departmentID,year);
            // 根据人员清单组装表格行
            tableView.Rows = GetRowsByEmployeeList(employeeList, employeeRemainingRestDays, dateColumns, departmentID);

            return tableView;
        }
        /// <summary>
        /// 构建人员列
        /// </summary>
        /// <returns></returns>
        private TableColumn CreateEmployeeColumns()
        {
            // 所有列都有一个子标题，先组件子标题
            var childColumns = new List<TableColumn>() {
                // 层级列
                new TableColumn{
                    Index = 0,
                    Name = "层级",
                    Key = "capabilityLevel",
                    Width = 80,
                    Sort = 0
                },
                // 姓名列
                new TableColumn{
                    Index = 1,
                    Name = "姓名",
                    Key = "employeeName",
                    Width = 90,
                    Sort = 1
                }
            };
            // 人员列
            var employeeColumns = new TableColumn()
            {
                Index = 0,
                Name = "人员",
                Key = "employee",
                Width = 160,
                Sort = 0,
                ChildColumns = childColumns,
            };
            return employeeColumns;
        }
        /// <summary>
        /// 根据时间段构建列
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns></returns>
        private List<TableColumn> CreateColumnsByDate(int year)
        {
            var columns = new List<TableColumn>();
            // 第一列是姓名和能级，这里下标从2开始
            int index = 2;
            var startDate = new DateTime(year,1,1);
            var endDate = new DateTime(year, 12, 1);
            for (var date = startDate; date <= endDate; date = date.AddMonths(1))
            {
                var column = new TableColumn()
                {
                    Index = index,
                    Name = DateHelper.GetMonthByDate(date),
                    Key = date.ToString("yyyyMM"),
                    Value = date,
                    Width = 50,
                    Sort = index,
                    ChildColumns = new List<TableColumn>(),
                };
                index++;
                columns.Add(column);
            }
            return columns;
        }
        /// <summary>
        /// 组装表格行
        /// </summary>
        /// <param name="employeeList">员工集合</param>
        /// <param name="employeeRemainingRestDays">当前员工剩余休息日数据集合</param>
        /// <param name="dateColumns">数据列</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        private List<Dictionary<string, object>> GetRowsByEmployeeList(List<EmployeeForSchedulingView> employeeList, List<RemainingRestDaysView> employeeRemainingRestDays, List<TableColumn> dateColumns, int departmentID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            var tableRows = new List<Dictionary<string, object>>();
            for (var i = 0; i < employeeList.Count; i++)
            {
                var row = new Dictionary<string, object>();
                var employeeInfo = employeeList[i];
                row.Add("employee", employeeInfo);
                row.Add("employeeName", employeeInfo.EmployeeName);
                row.Add("capabilityLevel", employeeInfo.CapabilityLevel);
                // 组装信息
                foreach (var dateColumn in dateColumns)
                {
                    var queryDate = (DateTime)dateColumn.Value;
                    var restDaysInfo = employeeRemainingRestDays.FirstOrDefault(m => m.EmployeeID == employeeInfo.EmployeeID && m.Year == queryDate.Year && m.Month == queryDate.Month);
                    var data = restDaysInfo == null ? new RemainingRestDaysView()
                    {
                        HospitalID = session.HospitalID,
                        Year = queryDate.Year,
                        Month = queryDate.Month,
                        EmployeeID = employeeInfo.EmployeeID,
                        DepartmentID = departmentID,
                    } : restDaysInfo;
                    row.Add(dateColumn.Key, data);
                }
                tableRows.Add(row);
            }
            return tableRows;
        }

        /// <summary>
        /// 保存剩余休息日数据
        /// </summary>
        /// <param name="saveViews">保存数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> SaveRemainingRestDaysData(List<RemainingRestDaysView> saveViews,Session session)
        {
            if (saveViews== null || saveViews.Count<=0)
            {
                _logger.Error("保存剩余休假天数失败，前端传递参数为空！");
                return false;
            }
            var departmentID = saveViews[0].DepartmentID;
            var employeeRemainingRestDays = await _remainingRestDaysRepository.GetAllRemainingRestDaysByDepartmentID(departmentID);
            var employeeRemainingRestDaysInfos = new List<RemainingRestDaysInfo>();
            var employeeRemainingRestDaysLogInfos = new List<RemainingRestDaysLogInfo>();
            foreach (var saveView in saveViews)
            {
                //新增
                if (saveView.RemainingRestDaysID == 0)
                {
                    //业务表
                    var employeeRemainingRestDaysInfo = CreateRemainingRestDaysInfo(saveView,session);
                    //日志表
                    var employeeRestDaysLogInfo = CreateRemainingRestDaysLogInfo(employeeRemainingRestDaysInfo);
                    employeeRemainingRestDaysInfos.Add(employeeRemainingRestDaysInfo);
                    employeeRemainingRestDaysLogInfos.Add(employeeRestDaysLogInfo);
                    continue;
                }
                //修改
                var employeeDays = employeeRemainingRestDays.FirstOrDefault(m => m.RemainingRestDaysID == saveView.RemainingRestDaysID);
                if (employeeDays == null || employeeDays.Days == saveView.Days)
                {
                    continue;
                }
                employeeDays.Days = saveView.Days;
                employeeDays.Modify(session.EmployeeID);
                var employeeDaysLogInfo = CreateRemainingRestDaysLogInfo(employeeDays);
                employeeRemainingRestDaysLogInfos.Add(employeeDaysLogInfo);
            }
            if (employeeRemainingRestDaysInfos.Count > 0) await _unitOfWork.GetRepository<RemainingRestDaysInfo>().InsertAsync(employeeRemainingRestDaysInfos);
            if (employeeRemainingRestDaysLogInfos.Count > 0) await _unitOfWork.GetRepository<RemainingRestDaysLogInfo>().InsertAsync(employeeRemainingRestDaysLogInfos);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 创建RemainingRestDays
        /// </summary>
        /// <param name="saveView">保存参数</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        private RemainingRestDaysInfo CreateRemainingRestDaysInfo(RemainingRestDaysView saveView, Session session)
        {
            var employeeRemainingRestDaysInfo = new RemainingRestDaysInfo()
            {
                HospitalID = saveView.HospitalID,
                EmployeeID = saveView.EmployeeID,
                DepartmentID = saveView.DepartmentID,
                Year = saveView.Year,
                Month = saveView.Month,
                Days = saveView.Days,
            };
            employeeRemainingRestDaysInfo.Add(session.EmployeeID);
            employeeRemainingRestDaysInfo.Modify(session.EmployeeID);
            return employeeRemainingRestDaysInfo;
        }
        /// <summary>
        /// 创建RemainingRestDaysLog
        /// </summary>
        /// <param name="remainingRestDaysInfo">剩余休息日实例</param>
        /// <returns></returns>
        private RemainingRestDaysLogInfo CreateRemainingRestDaysLogInfo(RemainingRestDaysInfo remainingRestDaysInfo)
        {
            var employeeRestDaysLogInfo = new RemainingRestDaysLogInfo()
            {
                HospitalID = remainingRestDaysInfo.HospitalID,
                EmployeeID = remainingRestDaysInfo.EmployeeID,
                DepartmentID = remainingRestDaysInfo.DepartmentID,
                Year = remainingRestDaysInfo.Year,
                Month = remainingRestDaysInfo.Month,
                Days = remainingRestDaysInfo.Days,
                AddDateTime = remainingRestDaysInfo.ModifyDateTime,
                AddEmployeeID = remainingRestDaysInfo.ModifyEmployeeID
            };
            return employeeRestDaysLogInfo;
        }
    }
}
