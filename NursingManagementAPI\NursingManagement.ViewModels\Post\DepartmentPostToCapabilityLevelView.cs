﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class DepartmentPostToCapabilityLevelView : DepartmentPostToCapabilityLevelInfo
    {
        /// <summary>
        /// 全院岗位名称
        /// </summary>
        public string PostName { get; set; }
        /// <summary>
        /// 全院部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyPerson { get; set; }
        /// <summary>
        /// 新增人
        /// </summary>
        public string AddPerson { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 能级名称
        /// </summary>
        public string CapabilityLevel { get; set; }

        public DepartmentPostToCapabilityLevelView(DepartmentPostToCapabilityLevelInfo dp)
        {
            CopyParentPropertiesToChild.SynchronizationProperties(dp, this);
        }
    }
}
