﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 首页部分service
    /// </summary>
    [Produces("application/json")]
    [Route("api/Home")]
    [EnableCors("any")]
    public class HomeController : Controller
    {
        private readonly ISessionService _session;
        private readonly IHomeService _homeService;
        /// <summary>
        /// service注入
        /// </summary>
        /// <param name="session"></param>
        /// <param name="homeService"></param>
        public HomeController(
            ISessionService session,
            IHomeService homeService)
        {
            _session = session;
            _homeService = homeService;
        }
        /// <summary>
        /// 获取待办列表
        /// </summary>
        /// <param name="todoType">待办项目类别</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetToDoList")]
        public async Task<IActionResult> GetToDoList(string todoType)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _homeService.GetToDoListAsync(session.EmployeeID, session.DepartmentID, todoType, session.ClientType);
            return result.ToJson();
        }
        /// <summary>
        /// 优先获取当前部门计划对应的未执行排程数据）
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="preOrNextFlag">获取计划月份之前OR之后，True：当月和当月之前；False：之后</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUnExecAnnualSchedule")]
        public async Task<IActionResult> GetUnExecAnnualSchedule(int? departmentID, int? scheduleMonth, bool preOrNextFlag)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            // TODO：年度计划还未完成，数据依赖关系不完整，暂不上线，待上线后打开
            //result.Data = await _homeService.GetUnExecAnnualScheduleAsync(session.EmployeeID, departmentID ?? session.DepartmentID, scheduleMonth, preOrNextFlag, session.ClientType);
            return result.ToJson();
        }
        /// <summary>
        /// 优先获取当前部门计划对应的未执行排程数据）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHomeMessageList")]
        public async Task<IActionResult> GetHomeMessageList()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _homeService.GetHomeMessageList(session.DepartmentID);
            return result.ToJson();
        }
    }
}
