﻿using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeStaffDataRepository : ICacheRepository
    {
        /// <summary>
        /// 根据工号获取在职信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<EmployeeStaffDataInfo> GetEmployeeStaffDataByID(string employeeID);
        /// <summary>
        /// 获取指定字段信息
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="employeeID"></param>
        /// <param name="predicate"></param>
        /// <returns></returns>
        Task<T> GetFieldValueByEmployeeIDAsync<T>(string employeeID, [DisallowNull] Expression<Func<EmployeeStaffDataInfo, T>> predicate);
        /// <summary>
        /// 根据查询条件获取在职人员
        /// </summary>
        /// <param name="employeeQueryView"></param>
        /// <returns></returns>
        Task<List<EmployeeListView>> GetStaffByEmployeeQueryView(EmployeeQueryView employeeQueryView);
        /// <summary>
        /// 根据科室ID获取护士信息
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        Task<List<EmployeeListView>> GetEmployeeViewByDepartmentIDAsync(int departmentID);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="nurseFlag"></param>
        /// <returns></returns>
        Task<List<string>> GetEmployeeIDsByDepartmentIDAsync(int? departmentID, bool nurseFlag = true);
        /// <summary>
        /// 获取员工信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startFileIDs">档案编号</param>
        /// <returns></returns>
        Task<List<EmployeeStaffDataInfo>> GetStaffsByDepartmentID(int? departmentID, string[] startFileIDs = null);
        /// <summary>
        /// 根据科室ID获取护士信息
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeStaffDataInfo>> GetEmployeeStaffDataView();
        /// <summary>
        /// 获取人员信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="employeeIDs">档案编号</param>
        /// <returns></returns>
        Task<List<EmployeeStaffDataInfo>> GetEmployeeListByDeptIDOrEmployeeIDs(int staffStatusCode, int? departmentID, string[] employeeIDs = null);
        /// <summary>
        /// 获取有护理层级的人员数据
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeStaffDataInfo>> GetHaveCapabilityLevelList();
        /// <summary>
        /// 获取所有在职人员
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeStaffDataInfo>> GetOnJobEmployeeStaffDataView();
    }
}
