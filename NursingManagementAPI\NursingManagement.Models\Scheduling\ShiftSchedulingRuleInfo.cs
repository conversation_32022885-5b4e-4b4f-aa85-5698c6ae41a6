using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 排班规则表
    /// </summary>
    [Serializable]
    [Table("ShiftSchedulingRule")]
    public class ShiftSchedulingRuleInfo : MutiModifyInfo
    {

        /// <summary>
        /// 排班规则配置记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ShiftSchedulingRuleID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门编号
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 规则ID，关联SettingDictionary表的SettingValue字段
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string RuleID { get; set; }
        /// <summary>
        /// 规则值
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string RuleValue { get; set; }
    }
}
