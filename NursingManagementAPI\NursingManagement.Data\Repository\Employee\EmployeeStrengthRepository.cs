﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeStrengthRepository : IEmployeeStrengthRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeStrengthRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据ID获取人员数据
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<List<EmployeeStrengthInfo>> GetListByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeStrengthInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据个人特长主键获取数据
        /// </summary>
        /// <param name="employeeStrengthID"></param>
        /// <returns></returns>
        public async Task<EmployeeStrengthInfo> GetDataByStrengthID(string employeeStrengthID)
        {
            return await _dbContext.EmployeeStrengthInfos.Where(m => m.EmployeeStrengthID == employeeStrengthID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}
