﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Employee;

namespace NursingManagement.Services.Interface
{
    public interface IEmployeeService
    {
        /// <summary>
        /// 获取员工信息
        /// </summary>
        /// <param name="employeeQueryView"></param>
        /// <returns></returns>
        Task<EmployeeStatisticListView> GetEmployeeList(EmployeeQueryView employeeQueryView);

        /// <summary>
        /// 获取在职信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<object> GetOnJobInfoAsync(string employeeID);

        /// <summary>
        /// 保存在职相关信息
        /// </summary>
        /// <param name="onJobView"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        Task<object> SaveOnJobInfoAsync(EmployeeOnJobView onJobView, string userID);

        /// <summary>
        /// 获取个人档案头呈现信息信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<PersonalFileHeader> GetPersonalFileHeaderInfoAsync(string employeeID);

        /// <summary>
        /// 获取员工个人信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<CollapseView>> GetEmployeePersonalData(string employeeID);

        /// <summary>
        /// 获取人员借调数据
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="effectiveRecordSwitch">有效记录开关， True:显示有效记录，结束时间在当前时间之后的数据并且没有撤销审批的数据 False:显示历史记录，结束时间在当前时间之前的数据或者已经撤销审批的数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordView>> GetEmployeeSecondmenRecordList(string employeeID, bool effectiveRecordSwitch, Session session);

        /// <summary>
        /// 删除人员借调数据
        /// </summary>
        /// <param name="employeeSecondmentRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteEmployeeSecondmenRecord(string employeeSecondmentRecordID, string employeeID);

        /// <summary>
        /// 保存人员借调数据
        /// </summary>
        /// <param name="recordView"></param>
        /// <returns></returns>
        Task<SaveReponseView> SaveEmployeeSecondmenRecord(EmployeeSecondmentRecordView recordView);

        /// <summary>
        /// 根据类型获取借调记录
        /// </summary>
        /// <param name="type">1：借调到本部门人员的借调记录；2: 本部门借调出去人员的借调记录</param>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetSecondmenRecordByDepartmentIDAndDate(int type, int departmentID, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 获取带教关系及员工信息
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        Task<Dictionary<string, List<EmployeeTeachingRelationView>>> GetEmployeeTeachingRelationView(int departmentID);

        /// <summary>
        /// 新增保存带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationView">保存信息</param>
        /// <param name="userID">护士工号</param>
        /// <param name="saveFlag">保存标记</param>
        /// <returns></returns>
        Task<bool> SaveTeachRelation(EmployeeTeachingRelationView employeeTeachingRelationView, string userID, bool saveFlag = true);

        /// <summary>
        /// 停止带教
        /// </summary>
        /// <param name="employeeTeachingRelationID">带教关系主键ID</param>
        /// <param name="userID">护士工号</param>
        /// <returns></returns>
        Task<bool> StopTeachRelation(int employeeTeachingRelationID, string userID);

        /// <summary>
        /// 删除带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationID">带教关系主键ID</param>
        /// <param name="userID">护士工号</param>
        /// <returns></returns>
        Task<bool> DeleteTeachRelation(int employeeTeachingRelationID, string userID);

        /// <summary>
        /// 批量保存数据
        /// </summary>
        /// <param name="employeeTeachingRelationView">保存信息</param>
        /// <param name="userID">护士工号</param>
        /// <returns></returns>
        Task<bool> BachSaveTeachRelation(List<EmployeeTeachingRelationView> employeeTeachingRelationView, string userID);

        /// <summary>
        /// 获取部门可以变更的人员
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        Task<List<EmployeeListView>> GetDepartmentChangeRequestEmployeeByDepartmentID(int departmentID, string hospitalID);

        /// <summary>
        /// 保存人员部门变更申请
        /// </summary>
        /// <param name="employeeListView">保存人员信息</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> SaveEmployeeDepartmentChangeRequest(List<EmployeeListView> employeeListView, int departmentID, Session session);

        /// <summary>
        /// 获取人员部门变更申请记录
        /// </summary>
        /// <param name="requestStartDate">申请开始时间</param>
        /// <param name="requestEndDate">申请结束时间</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentChangeView>> GetEmployeeDepartmentChangeRequestView(DateTime requestStartDate, DateTime requestEndDate, Session session);

        /// <summary>
        /// 根据主键ID删除人员部门变更申请记录（未审核的）
        /// </summary>
        /// <param name="dataID">主键ID</param>
        /// <param name="employeeID">人员工号</param>
        /// <returns></returns>
        Task<bool> DeleteEmployeeDepartmentChangeRequest(string dataID, string employeeID);

        /// <summary>
        /// 保存员工服饰尺码信息
        /// </summary>
        /// <param name="employeeClothingSize">员工服饰尺码信息</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        Task<bool> SaveEmployeeClothingSizes(EmployeeClothingSizesView employeeClothingSize, string hospitalID);

        #region 人员多组织架构部门

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        Task<EmployeeToDepartmentView[]> GetEmpToDeptViews();

        /// <summary>
        /// 根据工号获取人员多组织架构部门
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<EmployeeDepartmentView[]> GetEmpDepartmentsByEmployeeID(string employeeID);

        /// <summary>
        /// 保存人员多组织架构部门
        /// </summary>
        /// <param name="employeeToDepartmentView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SaveEmployeeToDepartments(EmployeeToDepartmentView employeeToDepartmentView, string employeeID);

        /// <summary>
        /// 删除人员多组织架构部门
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> DeleteEmployeeToDepartmentByEmployeeID(string employeeID);

        #endregion

        /// <summary>
        /// 获取离职人员
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="employeeIDs">档案编号</param>
        /// <returns></returns>
        Task<List<EmployeeStaffDataInfo>> GetDimissionEmployeeList(int? departmentID, string[] employeeIDs = null);

        /// <summary>
        /// 保存个人特长数据
        /// </summary>
        /// <param name="employeeStrengths">前端传进来的需要保存的数据</param>
        /// <returns></returns>
        Task<bool> SaveEmployeeStrengthsAsync(List<EmployeeStrengthView> employeeStrengths);

        /// 根据员工ID和组织类型获取员工所属部门的信息。 </summary> <param name="employeeID">员工的ID。</param> <param
        /// name="organizationType">组织类型。</param> <returns>包含员工组织部门信息</returns>
        Task<List<EmployeeDepartmentView>> GetEmployeeToDeptByEmployeeIDAndOrganizationTypeAsync(string employeeID, string organizationType);

        /// <summary>
        /// 获取员工体检信息列表
        /// </summary>
        /// <param name="employeeID">员工明细</param>
        /// <returns></returns>
        Task<object> GetPhyExamList(string employeeID);

        /// <summary>
        /// 根据检验号和体检次数获取检验明细数据
        /// </summary>
        /// <param name="phyExamID">体检号</param>
        /// <param name="visitID">体检次数</param>
        /// <returns></returns>
        Task<object> GetPhyExamRecord(string phyExamID, int visitID);

        /// 删除个人特长数据 </summary> <param name="employeeStrengthID"></param> <returns></returns>
        Task<bool> DeleteEmployeeStrengthsAsync(string employeeStrengthID, string userID);

        /// <summary>
        /// 删除个人任职记录数据
        /// </summary>
        /// <param name="employeeStrengthID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        Task<bool> DeleteEmployeePositionAsync(string employeeStrengthID, string userID);

        /// <summary>
        /// 创建对应的审批记录
        /// </summary>
        /// <param name="employeeSecondmentRecordInfo">人员借调信息</param>
        /// <param name="updateSecondmentTypeFlag">是否修改借调类型</param>
        /// <returns>是否创建成功</returns>
        Task<bool> CreateOrUpdateEmployeeSecondmentApprove(EmployeeSecondmentRecordInfo employeeSecondmentRecordInfo, bool updateSecondmentTypeFlag);

        /// <summary>
        /// 获取片区权限
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> GetEmployeeToDepartmentAsync(string employeeID);

        /// <summary>
        /// 获取用户权限科室
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<Dictionary<int, string>> GetEmployeeDepartmentIDAndNames(string employeeID);

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <returns></returns>
        Task<EmployeeInformation[]> GetEmployeesInformation();

        /// 获取员工离职列表
        /// </summary>
        /// <param name="queryView"></param>
        /// <returns></returns>
        Task<List<EmployeeResignationView>> GetEmployeeResignationList(EmployeeQueryView queryView);
    }
}
