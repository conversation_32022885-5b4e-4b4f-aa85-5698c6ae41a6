﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels;

/// <summary>
/// 季度计划查询参数
/// </summary>
public class QuarterPlanQueryView
{
    /// <summary>
    /// 主键
    /// </summary>
    public string Key { get; set; }
    /// <summary>
    /// 季度
    /// </summary>
    public int Quarter { get; set; }
    /// <summary>
    /// 是否是维护权限科室
    /// </summary>
    public bool IsMainDepartment { get; set; }
    /// <summary>
    /// 科室ID
    /// </summary>
    public int DepartmentID { get; set; }
    /// <summary>
    /// 科室名称
    /// </summary>
    public string DepartmentName { get; set; }
    /// <summary>
    /// 制定人名称
    /// </summary>
    public string PlannerName { get; set; }
    /// <summary>
    /// 制定人工号
    /// </summary>
    public string Planner { get; set; }
    /// <summary>
    /// 状态码
    /// </summary>
    public AnnualPlanEnums.PlanStatus StatusCode { get; set; }
    /// <summary>
    /// 新增时间
    /// </summary>
    public DateTime ModifyDateTime { get; set; }
}
