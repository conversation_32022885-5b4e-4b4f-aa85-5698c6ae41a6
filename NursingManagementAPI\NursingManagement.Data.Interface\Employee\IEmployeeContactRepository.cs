﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeContactRepository
    {
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeContactInfo>> GetDataByEmployeeID(string employeeID);

        /// <summary>
        /// 获取手机号
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<string> GetPhoneNumberByEmployeeID(string employeeID);
    }
}