﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using NursingManagement.API.Infrastructure.Filters;
using NursingManagement.Data;
using NursingManagement.Data.Context;
using NursingManagement.ViewModels;
using StackExchange.Redis;
using System.Reflection;

namespace NursingManagement.API
{
    public class Startup
    {
        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";
        /// <summary>
        /// Startup
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }
        /// <summary>
        /// Configuration
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// 注册依赖关系
        /// </summary>
        /// <param name="services"></param>
        public void RegisterServices(IServiceCollection services)
        {
    
            //配置数据库连接 UseMySql使用MySQL  UseSqlServer使用SQL Server
            services.AddDbContext<NursingManagementDbContext>(options => 
            options.UseSqlServer(Configuration.GetConnectionString("Connection"), c => c.UseCompatibilityLevel(100))
            .AddInterceptors(new QueryWithNoLockDbCommandInterceptor()));
            services.AddScoped<NursingManagementDbContext>();
            services.AddUnitOfWork<NursingManagementDbContext>();           

            //消息数据库
            services.AddDbContext<MessageDBContext>(options => 
            options.UseSqlServer(Configuration.GetConnectionString("MessageConnection"), c => c.UseCompatibilityLevel(100))
            .AddInterceptors(new QueryWithNoLockDbCommandInterceptor()));
            services.AddScoped<MessageDBContext>();
            //hangfire定时任务数据库
            services.AddDbContext<HangfireDBConnect>(options => options.UseSqlServer(Configuration.GetConnectionString("HangFireConnection"), c => c.UseCompatibilityLevel(100)));
            services.AddScoped<NursingManagementDbContext>();
            services.AddUnitOfWork<NursingManagementDbContext>();

            string str = Configuration.GetConnectionString("HangFireConnection");
            var configurationOptions = new ConfigurationOptions
            {
                DefaultDatabase = 1
            };
            //连接Redis数据库
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = Configuration.GetConnectionString("RedisConnection");
                options.InstanceName = "";              
            });
            services.Configure<SystemConfig>(Configuration.GetSection("ConnectionStrings"));
            services.Configure<SystemConfig>(Configuration.GetSection("Configs"));
            services.AddHttpContextAccessor();
            services.AddEndpointsApiExplorer();
            //增加服务端缓存
            services.AddMemoryCache();
            services.AddMvcCore(options =>
            {
                //添加过滤器
                options.Filters.Add(typeof(AuthorizationAttribute));
                options.Filters.Add(typeof(CommonFilterAttribute));
                options.Filters.Add<GlobalExceptionsFilter>();

            }).AddNewtonsoftJson(options =>
            {
                //设置时间格式
                options.SerializerSettings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.MicrosoftDateFormat;
                options.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
            });
            services.AddHangfireServer();
            AutomaticRetryAttribute automaticRetryAttribute = new AutomaticRetryAttribute
            {
                Attempts = 3
            };
            // 设置请求体大小
            services.Configure<IISServerOptions>(options =>
            {
                options.MaxRequestBodySize = int.MaxValue;
            });
            services.AddHangfire((provider, configuration) =>
            {
                configuration.UseFilter(automaticRetryAttribute);
                configuration.UseSqlServerStorage(str);
            });

            services.AddCors(options =>
            {
                options.AddPolicy("any", builder =>
                {
                    builder                
                     .AllowAnyHeader() //允许所有请求头
                           .AllowAnyMethod() //允许任何方法
                           .AllowCredentials() //允许跨源凭据----服务器必须允许凭据
                           .SetIsOriginAllowed(origin => true)
                           .SetPreflightMaxAge(TimeSpan.FromSeconds(36000));//指定可以缓存预检请求的响应的时间;
                });
            });
            if (Configuration.GetSection("UseSwagger").Value != "false")
            {
                services.AddSwaggerGen(options =>
                {
                    options.SwaggerDoc("v1", new OpenApiInfo { Title = "NursingManagement.API", Version = "V1.0" });
                    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                    options.IncludeXmlComments(xmlPath, true);
                    options.AddSecurityRequirement(new OpenApiSecurityRequirement
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                                }
                            },
                            Array.Empty<string>()
                        }
                    });
                    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                    {
                        Description = "请输入 Bearer {token}：",
                        Name = "Authorization",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.ApiKey
                    });
                });
            }
            services.AddControllers();
        }
        /// <summary>
        /// 设置中间件
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        public void SetupMiddleware(WebApplication app, IWebHostEnvironment env)
        {
            app.UseCors("any");           
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                // 显示请求持续时间（以毫秒为单位）的显示
                c.DisplayRequestDuration();
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "NursingManagement");
                // 设置为空字符串来让Swagger UI直接在根路径下进行访问
                c.RoutePrefix = string.Empty;
            });
            //app.UseMiddleware<ReqResLogMiddle>();
            app.UseHttpsRedirection();
            app.UseAuthorization();
            app.MapControllers();
            app.Run();
            app.UseHangfireDashboard("/hangfire", new DashboardOptions
            {
                AppPath = "/swagger",
                Authorization = new[] { new MyAuthorizationFilter() }
            });
        }
    }
}