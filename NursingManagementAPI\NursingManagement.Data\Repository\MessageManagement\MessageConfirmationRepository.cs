﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 消息确认记录的仓储实现类，提供对消息确认记录的查询操作。
    /// </summary>
    public class MessageConfirmationRepository : IMessageConfirmationRepository
    {
        private readonly NursingManagementDbContext _context;

        public MessageConfirmationRepository(NursingManagementDbContext context)
        {
            _context = context;
        }
        /// <summary>
        /// 根据查询条件获取消息确认记录
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>返回满足条件的消息确认记录列表</returns>
        public async Task<List<MessageConfirmationInfo>> FindAsync(Expression<Func<MessageConfirmationInfo, bool>> predicate)
        {
            return await _context.MessageConfirmationInfos.Where(predicate).ToListAsync();
        }
        /// <summary>
        /// 根据查询条件获取消息确认记录
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>返回满足条件的消息确认记录</returns>
        public async Task<MessageConfirmationInfo> SingleRecordAsync(Expression<Func<MessageConfirmationInfo, bool>> predicate)
        {
            return await _context.MessageConfirmationInfos.Where(predicate).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据消息记录ID查询消息确认记录
        /// </summary>
        /// <param name="messageRecordId">消息记录的ID</param>
        /// <returns>返回满足条件的消息确认记录列表</returns>
        public async Task<List<MessageConfirmationInfo>> GetByMessageRecordIdAsync(string messageRecordId)
        {
            return await _context.MessageConfirmationInfos
                .Where(m => m.MessageRecordID == messageRecordId)
                .ToListAsync();
        }

        /// <summary>
        /// 根据员工ID获取 指定状态 消息记录ID集合
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <param name="isConfirmed">是否确认，true为已确认，false为未确认</param>
        /// <returns>返回满足条件的消息记录ID集合</returns>
        public async Task<List<string>> GetMessageRecordIDsByEmployeeIdAsync(string employeeId, bool isConfirmed)
        {
            return await _context.MessageConfirmationInfos
                .Where(m => m.ConfirmEmployeeID == employeeId && m.DeleteFlag !="*" && m.ConfirmationStatus == isConfirmed)
                .Select(m=>m.MessageRecordID).ToListAsync();
        }

        /// <summary>
        /// 获取某条信息某人是否已经确认
        /// </summary>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>如果该员工已经确认，返回true，否则返回false</returns>
        public async Task<bool> IsConfirmedAsync(string messageRecordId, string employeeId)
        {
            var status = await _context.MessageConfirmationInfos.Where(m=> m.MessageRecordID == messageRecordId && m.ConfirmEmployeeID == employeeId)
                .Select(m=>m.ConfirmationStatus)
                .FirstOrDefaultAsync();

            return status;
        }
    }
}
