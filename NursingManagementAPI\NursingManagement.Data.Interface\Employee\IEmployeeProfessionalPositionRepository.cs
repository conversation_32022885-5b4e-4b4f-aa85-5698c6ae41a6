﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeProfessionalPositionRepository
    {
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeProfessionalPositionInfo>> GetListByEmployeeID(string employeeID);

        /// <summary>
        /// 获取人员最高职称
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="positionIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeProfessionalPositionInfo>> GetHighestCurrentProfessionalPositionView(string hospitalID, string[] employeeIDs, string[] positionIDs);
        
        /// <summary>
        /// 获取当前最高职称
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<EmployeeProfessionalPositionInfo> GetCurrentByEmployeeID(string employeeID);
    }
}