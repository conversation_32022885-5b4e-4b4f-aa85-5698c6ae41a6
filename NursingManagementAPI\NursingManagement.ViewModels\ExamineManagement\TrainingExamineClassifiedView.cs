﻿namespace NursingManagement.ViewModels
{
    public class TrainingExamineClassifiedView
    {
        /// <summary>
        /// 分类Code（Examine：考核；Training：培训）
        /// </summary>
        public string SettingType { get; set; }
        /// <summary>
        /// 分类名称
        /// </summary>
        public string LocalShowName { get; set; }
        /// <summary>
        /// 分类value，唯一缺点
        /// </summary>
        public string SettingValue { get; set; }
        /// <summary>
        /// 父ID
        /// </summary>
        public string ParentID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 层级
        /// </summary>
        public int Level { get; set; }
        /// <summary>
        /// 记录人员
        /// </summary>
        public string ModifyEmployee { get; set; }
        /// <summary>
        /// 记录人员ID
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 记录时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 人员HR编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言序号
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 类别码
        /// </summary>
        public string SettingTypeCode { get; set; }
        /// <summary>
        /// 子数据
        /// </summary>
        public List<TrainingExamineClassifiedView> Children { get; set; }
    }
}
