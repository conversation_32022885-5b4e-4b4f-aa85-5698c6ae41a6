﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class PostRepository : IPostRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public PostRepository(
            NursingManagementDbContext dbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<PostInfo> GetByPostID(int postID)
        {
            var list = await GetCacheAsync() as List<PostInfo>;
            return list.FirstOrDefault(m=>m.PostID == postID);
        }
        /// <summary>
        /// 根据postID获取数据不走缓存
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <returns></returns>
        public async Task<PostInfo> GetByPostIDNoCache(int postID)
        {
            return await _dbContext.PostInfos.FirstOrDefaultAsync(m => m.PostID == postID);
        }
        public async Task<List<PostInfo>> GetPostList(string postTypeID = null)
        {
            var list = await GetByCacheAsync();
            // 没传类型，默认把休假类型岗过滤掉
            if (string.IsNullOrEmpty(postTypeID))
            {
                list = list.Where(m => m.PostTypeID != "4").ToList();
            }
            else
            {
                list = list.Where(m => m.PostTypeID == postTypeID).ToList();
            }
            return list;
        }
        /// <summary>
        /// 根据postID集合获取数据
        /// </summary>
        /// <param name="postIDs"></param>
        /// <returns></returns>
        public async Task<List<PostInfo>> GetByPostIDs(List<int> postIDs)
        {
            var list = await GetCacheAsync() as List<PostInfo>;
            return list.Where(m => postIDs.Contains(m.PostID)).ToList();
        }

        public async Task<List<PostInfo>> GetByCacheAsync()
        {
            var list = await GetCacheAsync() as List<PostInfo>;

            return list;
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _dbContext.PostInfos.Where(m => m.HospitalID == hospitalID && m.Language == language).OrderBy(m => m.Sort).ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public bool HasCache(string cacheType) => GetCacheType() == cacheType;

        public string GetCacheType() => CacheType.Post.GetKey(_sessionCommonServer);
    }
}
