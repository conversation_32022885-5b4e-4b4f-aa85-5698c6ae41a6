﻿using DocumentFormat.OpenXml.Wordprocessing;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 常用文件清单表，OA首页底部文件常用链接清单
    /// </summary>
    [Serializable]
    [Table("CommonFile")]
    public class CommonFileInfo : MutiModifyInfo
    {
        /// <summary>
        /// 记录序号
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string CommonFileID { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 文件分类 1：公共文件，2：消息附件
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Class { get; set; }

        /// <summary>
        /// 文件类型 1：通知；2、：公告，3：红头文件等，暂时可以为空
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Type { get; set; } = "";

        /// <summary>
        /// 来源
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }

        /// <summary>
        /// 文件说明
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string Content { get; set; }

        /// <summary>
        /// 文件链接
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Link { get; set; }

        /// <summary>
        /// 发布日期时间
        /// </summary>
        public DateTime PublishDateTime { get; set; }
    }
}
