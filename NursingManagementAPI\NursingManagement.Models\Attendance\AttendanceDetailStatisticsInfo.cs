﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  考勤表   
    /// </summary>
    [Table("AttendanceDetailStatistics")]
    public class AttendanceDetailStatisticsInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考勤明细统计记录号，主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string AttendanceSheetDetailStatisticsID { get; set; }
        /// <summary>
        /// 考勤主表记录号，主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AttendanceRecordID { get; set; }
        /// <summary>
        /// 休假岗 ID
        /// </summary>
        public int RestPostID { get; set; }
        /// <summary>
        /// 统计天数
        /// </summary>
        public decimal StatisticsDays { get; set; }
    }
}