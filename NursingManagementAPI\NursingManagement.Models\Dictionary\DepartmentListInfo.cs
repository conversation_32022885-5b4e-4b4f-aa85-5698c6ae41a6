﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Table("DepartmentList")]
    public class DepartmentListInfo: MutiModifyInfo
    {
        /// <summary>
        /// 部门ID,护理管理内部流转，非自增	
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院ID	
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言	
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string DepartmentContent { get; set; }
        /// <summary>
        /// 部门呈现名称，默认与部门名称一致，但根据护理上需求，可以根据需要调整
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string LocalShowName { get; set; }
        /// <summary>
        /// 组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门 
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string OrganizationType { get; set; }
        /// <summary>
        /// 部门类别，0：无分类，1:行政、2：后勤、3：临床 4：医技	
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string DepartmentType { get; set; }
        /// <summary>
        /// 部门来源类别 1:HR、2:HIS、3:护理管理系统新增	
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SourceType { get; set; }
        /// <summary>
        /// 原始部门Code	
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 部门层级，院级别为1级，后续根据组织架构进行分级（1，2，3）	
        /// </summary>
        public int Level { get; set; }
        /// <summary>
        /// 部门上一级DepartmentID	
        /// </summary>
        public int UpperLevelDepartmentID { get; set; }
        /// <summary>
        /// 呈现顺序	
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 上一层级Code
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string UpperLevelDepartmentCode { get; set; }
    }
}
