﻿
using NursingManagement.Models;
namespace NursingManagement.Data.Interface
{
    public interface IUserLoginRepository : ICacheRepository
    {

        /// <summary>
        /// 检核OA用户
        /// </summary>
        /// <param name="oaUserID"></param>
        /// <param name="oaPassword"></param>
        /// <returns></returns>
        Task<UserLoginInfo> CheckOAUser(string oaUserID, string oaPassword);
       /// <summary>
       /// 根据OA账号获取
       /// </summary>
       /// <param name="oaUserID"></param>
       /// <returns></returns>
        Task<UserLoginInfo> GetByOAUserID(string oaUserID);
        /// <summary>
        /// 根据HIS账号获取
        /// </summary>
        /// <param name="hisUserID"></param>
        /// <returns></returns>
        Task<UserLoginInfo> GetByHISUserID(string hisUserID);
        /// <summary>
        /// 根据微信web端唯一键获取
        /// </summary>
        /// <param name="wechatWebOpenID"></param>
        /// <returns></returns>
        Task<UserLoginInfo> GetByWechatWebOpenID(string wechatWebOpenID);
        /// <summary>
        /// 根据微信小程序唯一码获取
        /// </summary>
        /// <param name="wechatMiniProgramOpenID"></param>
        /// <returns></returns>
        Task<UserLoginInfo> GetByWechatMiniProgramOpenID(string wechatMiniProgramOpenID);
        /// <summary>
        /// 根据微信唯一码获取
        /// </summary>
        /// <param name="wechatUnionID"></param>
        /// <returns></returns>
        Task<UserLoginInfo> GetByWechatUnionID(string wechatUnionID);
        /// <summary>
        /// 根据员工工号获取
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<UserLoginInfo> GetByEmployeeID(string employeeID);
        /// <summary>
        ///  获取绑定微信服务号的员工ID结果集
        /// </summary>
        /// <returns></returns>
        Task<List<string>> GetEmployeeIDsByWechatUnionIDIsNotNull();

    }
}
