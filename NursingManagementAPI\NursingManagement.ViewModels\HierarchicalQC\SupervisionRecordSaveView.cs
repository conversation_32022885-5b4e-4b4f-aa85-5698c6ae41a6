﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class SupervisionRecordSaveView
    {
        /// <summary>
        /// 质控数据
        /// </summary>
        public HierarchicalQCMainAndDetailView QCMainAndDetail { get; set; }

        /// <summary>
        /// 访视内容模板ID
        /// </summary>
        public int SupervisionFormID { get; set; }

        /// <summary>
        /// 片区ID
        /// </summary>
        public int UpNMDepartmetID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int NmDepartmetID { get; set; }

        /// <summary>
        /// CCC患者主记录数据
        /// </summary>
        public PatientProfileRecordInfo SupervisionRecord { get; set; }

        /// <summary>
        /// CCC患者明细记录数据
        /// </summary>
        public List<PatientProfileDetailInfo> SupervisionDetails { get; set; }

        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 登陆人
        /// </summary>
        public string EmployID { get; set; }

        /// <summary>
        /// 责任护士
        /// </summary>
        public string NurseEmployeeID { get; set; }
    }
}
