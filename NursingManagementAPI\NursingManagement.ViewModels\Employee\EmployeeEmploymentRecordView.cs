﻿namespace NursingManagement.ViewModels
{
    public class EmployeeEmploymentView
    {
        public string Title { get; set; }
        public List<EmployeeEmploymentRecordView> Children { get; set; }
    }
    public class EmployeeEmploymentRecordView
    {
        public string EmployeeEmploymentRecordID { get; set; }
        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 任职开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 任职结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 任职部门
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 任职岗位
        /// </summary>
        public int? Post { get; set; }

        /// <summary>
        /// 任职职务
        /// </summary>
        public int? Title { get; set; }

        /// <summary>
        /// 当前部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 当前岗位名称
        /// </summary>
        public string PostName { get; set; }
        /// <summary>
        /// 当前职称名
        /// </summary>
        public string TitleName { get; set; }
        /// <summary>
        /// 岗位类型
        /// </summary>
        public int PostType { get; set; }
    }
}
