﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ShiftSchedulingRecordRepository : IShiftSchedulingRecordRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        public ShiftSchedulingRecordRepository(
            NursingManagementDbContext db
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<ShiftSchedulingRecordInfo> GetRecordByDepartmentID(int departmentID, DateTime startDate, DateTime endDate, string statusCode = "1")
        {
            var session = _sessionCommonServer.GetSessionByCache();
            var list = await _nursingManagementDbContext.ShiftSchedulingRecordInfos.Where(m => m.DepartmentID == departmentID
                && m.StartDate.Date == startDate.Date && m.EndDate.Date == endDate.Date && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                .OrderByDescending(m => m.ModifyDateTime).ToListAsync();
            if (string.IsNullOrWhiteSpace(statusCode))
            {
                return list.FirstOrDefault();
            }
            return list.Find(m => m.StatusCode == statusCode);
        }

        public async Task<ShiftSchedulingRecordInfo> GetRecordByID(string shiftSchedulingRecordID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContext.ShiftSchedulingRecordInfos.Where(m => m.ShiftSchedulingRecordID == shiftSchedulingRecordID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}
