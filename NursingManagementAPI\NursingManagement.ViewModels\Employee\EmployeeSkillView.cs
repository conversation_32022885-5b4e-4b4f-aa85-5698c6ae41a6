﻿namespace NursingManagement.ViewModels.Employee
{
    /// <summary>
    /// 人员技能语言视图
    /// </summary>
    public class EmployeeSkillView
    {
        public string EmployeeSkillID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 技能分类(语言、计算机)
        /// </summary>s
        public string SkillClassification { get; set; }

        /// <summary>
        /// 技能名称(英语、计算机)
        /// </summary>
        public string Skill { get; set; }

        /// <summary>
        /// 熟练程度(一般、良好、熟练、精通)
        /// </summary>
        public string ProficiencyLevel { get; set; }

        /// <summary>
        /// 证书(10:英语四级)
        /// </summary>
        public string Certificate { get; set; }
        /// <summary>
        /// 备注
        /// </summary>

        public string Remark { get; set; }
    }
}
