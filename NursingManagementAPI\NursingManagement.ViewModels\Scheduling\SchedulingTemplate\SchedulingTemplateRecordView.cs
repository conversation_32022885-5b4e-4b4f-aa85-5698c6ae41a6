﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班模板记录
    /// </summary>
    public class SchedulingTemplateRecordView
    {
        /// <summary>
        /// 排班模板主记录序号
        /// </summary>
        public string SchedulingTemplateRecordID { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板说明
        /// </summary>
        public string TemplateDescription { get; set; }

        /// <summary>
        /// 部门序号
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 状态 0：停用、1：启用
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
    }
}
