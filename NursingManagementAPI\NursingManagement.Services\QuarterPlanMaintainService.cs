﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class QuarterPlanMaintainService : IQuarterPlanMaintainService
    {
        private readonly IAnnualPlanInterventionService _annualPlanInterventionService;
        private readonly IQuarterPlanMaintainRepository _quarterPlanMaintainRepository;
        private readonly IAnnualPlanMainRepository _annualPlanMainRepository;
        private readonly IAnnualPlanTypeListRepository _typeListRepository;
        private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;
        private readonly IDictionaryService _dictionaryService;

        public QuarterPlanMaintainService(
            IAnnualPlanInterventionService annualPlanInterventionService,
            IQuarterPlanMaintainRepository quarterPlanMaintainRepository,
            IAnnualPlanMainRepository annualPlanMainRepository,
            IAnnualPlanTypeListRepository typeListRepository,
            IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
            IDepartmentListRepository departmentListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IUnitOfWork unitOfWork,
            IEmployeeToDepartmentRepository employeeToDepartmentRepository,
            IDictionaryService dictionaryService
            )
        {
            _annualPlanInterventionService = annualPlanInterventionService;
            _quarterPlanMaintainRepository = quarterPlanMaintainRepository;
            _annualPlanMainRepository = annualPlanMainRepository;
            _typeListRepository = typeListRepository;
            _annualPlanInterventionMainRepository = annualPlanInterventionMainRepository;
            _departmentListRepository = departmentListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _unitOfWork = unitOfWork;
            _employeeToDepartmentRepository = employeeToDepartmentRepository;
            _dictionaryService = dictionaryService;
        }


        #region 常量
        /// <summary>
        /// 护理部ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;
        /// <summary>
        /// 护理委员会
        /// </summary>
        private const int DEPARTMENT_ID_53 = 53;
        private readonly int[][] QUARTER_MONTH_MAP = [
            [1, 2, 3],
            [4, 5, 6],
            [7, 8, 9],
            [10, 11, 12]
            ];
        #endregion

        #region 季度计划表增删改查
        /// <summary>
        /// 保存季度计划工作内容
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        public async Task<bool> SaveQuarterWorks(QpWorksSaveView view)
        {
            var lookup = view.WorkViews.ToLookup(m => string.IsNullOrEmpty(m.Key));
            var newWorks = lookup[true];
            await AddNewWorks(view.QuarterPlanMainID, newWorks, view.EmployeeID);

            var modifyWorks = lookup[false];
            await UpdateWorks(modifyWorks, view.EmployeeID);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #region 保存方法私有方法
        /// <summary>
        /// 新增季度计划工作
        /// </summary>
        /// <param name="qpMainID">季度计划主表ID</param>
        /// <param name="workViews">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task AddNewWorks(string qpMainID, IEnumerable<TieredPlanWork> workViews, string employeeID)
        {
            var newEntities = workViews.Select(m => new QuarterPlanDetailInfo
            {
                QuarterPlanDetailID = "".NewGuid(),
                QuarterPlanMainID = qpMainID,
                APInterventionID = m.APInterventionID,
                TypeID = m.TypeID,
                Sort = m.Sort,
                WorkContent = m.WorkContent,
                Requirement = m.Requirement,
                WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(m.WorkType.ToString()),
                IsTemp = m.IsTemp,
                PrincipalIDs = m.PrincipalIDs ?? [],
                PrincipalGroupName = m.PrincipalGroupName
            }).Select(m => m.Add(employeeID).Modify(employeeID) as QuarterPlanDetailInfo).ToList();
            await _unitOfWork.GetRepository<QuarterPlanDetailInfo>().InsertAsync(newEntities);
        }

        /// <summary>
        /// 更新季度计划工作
        /// </summary>
        /// <param name="workViews">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task UpdateWorks(IEnumerable<TieredPlanWork> workViews, string employeeID)
        {

            var workIDs = workViews.Select(m => m.Key).ToArray();
            var workInfos = await _quarterPlanMaintainRepository.GetQuarterPlanWorks(workIDs);
            foreach (var workInfo in workInfos)
            {
                var modifyView = workViews.FirstOrDefault(m => m.Key == workInfo.QuarterPlanDetailID);
                if (modifyView is null)
                {
                    continue;
                }
                workInfo.WorkContent = modifyView.WorkContent;
                workInfo.Requirement = modifyView.Requirement;
                workInfo.WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(modifyView.WorkType.ToString());
                workInfo.PrincipalIDs = modifyView.PrincipalIDs;
                workInfo.PrincipalGroupName = modifyView.PrincipalGroupName;
                workInfo.Sort = modifyView.Sort;
                workInfo.Modify(employeeID);
            }
        }
        #endregion

        /// <summary>
        /// 删除季度计划工作
        /// </summary>
        /// <param name="quarterPlanDetailInfo">主键</param>
        /// <returns></returns>
        public async Task<bool> DeleteQuarterWork(string quarterPlanDetailInfo, string employeeID)
        {
            if (string.IsNullOrEmpty(quarterPlanDetailInfo))
            {
                return false;
            }
            var quarterWorkInfo = await _quarterPlanMaintainRepository.GetQuarterWork(quarterPlanDetailInfo);
            if (quarterWorkInfo == null)
            {
                return false;
            }
            quarterWorkInfo.Delete(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 更新季度计划工作
        /// </summary>
        /// <param name="workView">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> UpdateQuarterWork(TieredPlanWork workView, string employeeID)
        {
            var workInfos = await _quarterPlanMaintainRepository.GetQuarterPlanWorks([workView.Key]);
            if (workInfos.FirstOrDefault() is QuarterPlanDetailInfo workInfo && workInfo != null)
            {
                workInfo.WorkContent = workView.WorkContent;
                workInfo.Requirement = workView.Requirement;
                workInfo.WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(workView.WorkType.ToString());
                workInfo.PrincipalIDs = workView.PrincipalIDs;
                workInfo.PrincipalGroupName = workView.PrincipalGroupName;
                workInfo.Sort = workView.Sort;
                workInfo.Modify(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 发布季度计划
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        public async Task<bool> PublishQuarterPlan(string quarterPlanMainID, string employeeID)
        {
            var quarterPlanMainInfo = await _quarterPlanMaintainRepository.GetQuarterPlanMain(quarterPlanMainID);
            quarterPlanMainInfo.StatusCode = 1;
            quarterPlanMainInfo.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取季度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        public async Task<string> GetQuarterPlanMainID(string annualPlanMainID, int quarter)
        {
            return await _quarterPlanMaintainRepository.GetQuarterPlanMainID(annualPlanMainID, quarter);
        }

        /// <summary>
        /// 获取季度计划状态
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主键</param>
        /// <returns></returns>
        public async Task<bool> GetQuarterPlanStatus(string quarterPlanMainID)
        {
            var statusCode = await _quarterPlanMaintainRepository.GetQuarterPlanStatus(quarterPlanMainID);
            return statusCode == 1;
        }

        /// <summary>
        /// 查询某科室的季度计划
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        public async Task<TieredPlanWorksByType[]> GetQuarterWorks(string annualPlanMainID, string quarterPlanMainID)
        {
            // 获取当前季度工作
            var quarterWorks = await _quarterPlanMaintainRepository.GetQuarterWorks(quarterPlanMainID);

            if (quarterWorks.Length == 0)
            {
                return [];
            }
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var interventionIDs = quarterWorks.SelectMany(m => m.Children).Where(m => !m.IsTemp && m.APInterventionID.HasValue).Select(m => m.APInterventionID.Value).ToArray();
            var interventionIDAndLocalShowName = await _annualPlanInterventionMainRepository.GetInterventionIDAndLocalShowName(interventionIDs, annualPlanMainID);
            foreach (var quarterWork in quarterWorks)
            {
                // 补充分类名称
                quarterWork.TypeName = typeList.Find(m => m.AnnualPlanTypeID == quarterWork.TypeID)?.AnnualPlanTypeContent;
                foreach (var work in quarterWork.Children)
                {
                    // 补充所引用执行项目的自定义名称
                    if (!work.IsTemp && work.APInterventionID.HasValue)
                    {
                        interventionIDAndLocalShowName.TryGetValue(work.APInterventionID.Value, out var localShowName);
                        work.APInterventionLocalShowName = localShowName;
                    }
                    // 若没有自定义分组名称，则拼接人员姓名作前端呈现
                    if (string.IsNullOrEmpty(work.PrincipalGroupName) && work.PrincipalIDs.Length > 0)
                    {
                        var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(work.PrincipalIDs);
                        work.PrincipalName = string.Join("，", employees.Select(m => m.Value));
                    }
                }
            }
            return quarterWorks;
        }
        #endregion

        #region 参考导入逻辑
        /// <summary>
        /// 获取可导入的工作
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <param name="departmentID">当前部门</param>
        /// <param name="annual">年度</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        public async Task<List<TieredPlanWorksByPlanThenType>> GetCanImportQpWorksGroupByPlanThenType(string annualPlanMainID, string quarterPlanMainID, int departmentID, int annual, int quarter)
        {
            // 当前部门季度计划工作已参考过的执行项目ID，重复进行导入时，需要根据此集合排除掉已参考的工作
            var refInterventionIDs = await _quarterPlanMaintainRepository.GetQpWorkInterventionIDs(quarterPlanMainID, quarter);
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            // 来源一：本部门年度计划，仅首次有，因首次导入后所有年度计划执行项目数据均已转换为季度计划工作
            var currentDeptQpWorksByPlan = refInterventionIDs.Length == 0
                ? await GetCurrentDeptQpWorksByPlanThenType(annualPlanMainID, departmentID, quarter, typeList)
                : null;

            // 来源二：上级部门季度计划
            var upperDeptQpWorksByPlanThenTypeList = await GetUpperDeptQpWorksByPlanThenTypeList(annual, departmentID, null, refInterventionIDs, typeList);

            if (currentDeptQpWorksByPlan is null)
            {
                return upperDeptQpWorksByPlanThenTypeList;
            }
            return [.. upperDeptQpWorksByPlanThenTypeList, currentDeptQpWorksByPlan];
        }

        /// <summary>
        /// 获取当前部门年度计划可选工作，非首次时 children为空集合
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="quarter">季度</param>
        /// <param name="typeList">分类字典</param>
        /// <returns></returns>
        private async Task<TieredPlanWorksByPlanThenType> GetCurrentDeptQpWorksByPlanThenType(string annualPlanMainID, int departmentID, int quarter, List<AnnualPlanTypeListInfo> typeList)
        {
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            List<APInterventionConvertView> convertViews;
            var months = QUARTER_MONTH_MAP[quarter - 1];
            convertViews = await _annualPlanInterventionService.GetAPInterventionConvertViews(annualPlanMainID, months);
            var currentDeptQpTypesByPlan = new TieredPlanWorksByPlanThenType
            {
                DepartmentID = departmentID,
                PlanMainID = annualPlanMainID,
                PlanName = departmentList.Find(m => m.DepartmentID == departmentID)?.LocalShowName + "年度计划",
                Children = convertViews.GroupBy(m => m.TypeID)
                .Select(m => new TieredPlanWorksByType
                {
                    TypeID = m.Key,
                    TypeName = typeList.Find(n => n.AnnualPlanTypeID == m.Key)?.AnnualPlanTypeContent,
                    Children = m.Select(n => new TieredPlanWork
                    {
                        APInterventionID = n.InterventionID,
                        TypeID = n.TypeID,
                        WorkContent = n.LocalShowName,
                    }).ToArray()
                }).ToArray()
            };
            return currentDeptQpTypesByPlan;
        }

        /// <summary>
        /// 获取上级部门季度计划可选工作
        /// </summary>
        /// <param name="annual">年度</param>
        /// <param name="departmentID">部门</param>
        /// <param name="includeApInterventionID">需包含的执行项目字典ID</param>
        /// <param name="excludeApInterventionIDs">需排除的执行项目字典ID集合</param>
        /// <param name="typeList">分类字典</param>
        /// <returns></returns>
        /// <exception cref="Exception">未找到上级部门季度计划所属的年度计划</exception>
        public async Task<List<TieredPlanWorksByPlanThenType>> GetUpperDeptQpWorksByPlanThenTypeList(int annual, int departmentID, int? includeApInterventionID, int[] excludeApInterventionIDs = null, List<AnnualPlanTypeListInfo> typeList = null)
        {
            typeList ??= await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var nursingDepartmentName = (await _departmentListRepository.GetByIDAsync(DEPARTMENT_ID_405))?.LocalShowName;
            var committeeDepartments = await _departmentListRepository.GetLowerDepartments(DEPARTMENT_ID_53);
            var upperDeptIDs = committeeDepartments.Select(m => m.DepartmentID)
                .Append(DEPARTMENT_ID_405)
                // 排除当前部门
                .Where(m => m != departmentID);
            var planMainViews = await _annualPlanMainRepository.GetPlanMainViewsByYearAndDepartmentIDs(annual, upperDeptIDs);
            var planMainIDs = planMainViews.Select(m => m.MainID).ToArray();
            // 若指定了执行项目ID，说明是快捷参考，按业务只包含对应字典的上级计划工作，故查询数据时不再包含临时性工作
            var upperDeptQpWorksByPlanThenTypeList = await _quarterPlanMaintainRepository.GetWorkViews(planMainIDs, includeApInterventionID, excludeApInterventionIDs, true, !includeApInterventionID.HasValue);
            upperDeptQpWorksByPlanThenTypeList.ForEach(planGroup =>
            {
                if (planMainViews.FirstOrDefault(m => m.MainID == planGroup.PlanMainID)?.DepartmentID is not int currentPlanDeptID)
                {
                    throw new Exception($"未找到当前季度计划所属年度计划！planMainID = {planGroup.PlanMainID}");
                }
                planGroup.IsCommittee = currentPlanDeptID != DEPARTMENT_ID_405;
                planGroup.DepartmentID = currentPlanDeptID;
                planGroup.PlanName = currentPlanDeptID == DEPARTMENT_ID_405
                ? $"{nursingDepartmentName}季度计划"
                : committeeDepartments.Find(m => m.DepartmentID == currentPlanDeptID)?.LocalShowName + "季度计划";
                foreach (var typeGroup in planGroup.Children)
                {
                    typeGroup.TypeName = typeList.Find(m => m.AnnualPlanTypeID == typeGroup.TypeID)?.AnnualPlanTypeContent;
                }
            });
            return upperDeptQpWorksByPlanThenTypeList;
        }

        /// <summary>
        /// 批量导入保存
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        public async Task<bool> SaveImportWorks(QpWorksSaveView view)
        {
            // 规则一：数据来源是导入弹窗，而导入弹窗中的数据是过滤后的，不会和季度计划现有数据重叠。所以前端传递的始终认为是新数据
            // 规则二：本部门年度计划执行项目中有但未传的，若此时是首次（即季度计划表中无数据）导入，则新增为常规工作。
            if (view.WorkViews.Length == 0)
            {
                return false;
            }
            var mainID = view.IsFirstImport ? "".NewGuid() : view.QuarterPlanMainID;
            var priorityWorks = view.WorkViews.Select(workView => new QuarterPlanDetailInfo
            {
                QuarterPlanDetailID = "".NewGuid(),
                QuarterPlanMainID = mainID,
                TypeID = workView.TypeID,
                APInterventionID = workView.APInterventionID,
                Sort = workView.Sort,
                WorkType = AnnualPlanEnums.WorkType.Key,
                WorkContent = workView.WorkContent,
                Requirement = workView.Requirement,
                IsTemp = workView.IsTemp,
                PrincipalIDs = []
            }).Select(m => m.Add(view.EmployeeID).Modify(view.EmployeeID) as QuarterPlanDetailInfo).ToList();
            if (view.IsFirstImport)
            {
                var quarterMainInfo = CreateMainInfo(view, mainID);
                await _unitOfWork.GetRepository<QuarterPlanMainInfo>().InsertAsync(quarterMainInfo);
                var normalWorks = await CreateNormalWorks(view, mainID);
                await _unitOfWork.GetRepository<QuarterPlanDetailInfo>().InsertAsync([.. priorityWorks, .. normalWorks]);
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            await _unitOfWork.GetRepository<QuarterPlanDetailInfo>().InsertAsync(priorityWorks);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 创建季度计划主表
        /// </summary>
        /// <param name="view">导入参数</param>
        /// <returns></returns>
        private static QuarterPlanMainInfo CreateMainInfo(QpWorksSaveView view, string mainID)
        {
            var quarterMainInfo = new QuarterPlanMainInfo
            {
                QuarterPlanMainID = mainID,
                AnnualPlanMainID = view.AnnualPlanMainID,
                Year = view.Annual,
                DepartmentID = view.DepartmentID,
                Quarter = view.Quarter,
                StatusCode = 0,
                DeleteFlag = ""
            };
            quarterMainInfo.Add(view.EmployeeID).Modify(view.EmployeeID);
            return quarterMainInfo;
        }

        /// <summary>
        /// 创建常规工作
        /// </summary>
        /// <param name="view">导入参数</param>
        /// <returns></returns>
        private async Task<List<QuarterPlanDetailInfo>> CreateNormalWorks(QpWorksSaveView view, string mainID)
        {
            // 未经前端传递过来的本部门年度计划执行项目，视为常规工作
            var months = QUARTER_MONTH_MAP[view.Quarter - 1];
            // 查询年度计划执行项目表
            var convertViews = await _annualPlanInterventionService.GetAPInterventionConvertViews(view.AnnualPlanMainID, months);
            var filteredAPInterventions = convertViews.ExceptBy(view.WorkViews.Select(m => m.APInterventionID), m => m.InterventionID);
            var normalWorks = filteredAPInterventions.Select(m => new QuarterPlanDetailInfo
            {
                QuarterPlanDetailID = "".NewGuid(),
                QuarterPlanMainID = mainID,
                TypeID = m.TypeID,
                APInterventionID = m.InterventionID,
                Sort = null,
                WorkType = AnnualPlanEnums.WorkType.Routine,
                WorkContent = m.LocalShowName,
                Requirement = "",
                PrincipalIDs = [],
                PrincipalGroupName = ""
            }).Select(m => m.Add(view.EmployeeID).Modify(view.EmployeeID) as QuarterPlanDetailInfo).ToList();
            return normalWorks;
        }
        #endregion

        /// <summary>
        /// 查询本人及上下级已制定的季度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<List<QuarterPlanBrowseView>> GetBrowseQPViews(int year, string employeeID)
        {
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var mainDepartment = employeeToDepartments.FirstOrDefault(m => m.IsMainDepartment ?? false)?.DepartmentID;
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var departIDs = await GetDepartmentIDs(employeeToDepartments, departmentList);

            var plans = await _quarterPlanMaintainRepository.GetQuarterPlanMainViewsByYearAndDepartmentIDs(year, departIDs);
            foreach (var plan in plans)
            {
                plan.IsMainDepartment = plan.DepartmentID == mainDepartment;
                var departmentName = departmentList.Find(m => m.DepartmentID == plan.DepartmentID)?.LocalShowName;
                plan.PlanName = $"{plan.Year}年{departmentName}第{plan.Quarter}季度季度计划";
                plan.PlannerName = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(plan.Planner, m => m.EmployeeName);
            }
            return [.. plans.OrderBy(m => departmentList.Find(n => n.DepartmentID == m.DepartmentID)?.Level)];
        }
        /// <summary>
        /// 获取季度计划预览数据
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划ID</param>
        /// <returns></returns>
        public async Task<QuarterPlanPreview> GetQuarterPlanPreview(string quarterPlanMainID)
        {
            var quarterPlan = await _quarterPlanMaintainRepository.GetQuarterPlanPreview(quarterPlanMainID);
            foreach (var planType in quarterPlan.PlanTypes)
            {
                foreach (var planWork in planType.PlanWorks)
                {
                    if (string.IsNullOrEmpty(planWork.PrincipalName))
                    {
                        var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(planWork.PrincipalIDs);
                        planWork.PrincipalName = string.Join("，", employees.Select(m => m.Value));
                    }
                }
            }
            return quarterPlan;
        }
        /// <summary>
        /// 获取科室集合
        /// </summary>
        /// <param name="employeeToDepartments">当前用户的权限科室</param>
        /// <param name="departmentList">科室字典</param>
        /// <returns></returns>
        private async Task<IEnumerable<int>> GetDepartmentIDs(EmployeeToDepartmentInfo[] employeeToDepartments, List<DepartmentListInfo> departmentList)
        {
            if (employeeToDepartments.Any(m => (m.IsMainDepartment ?? false) && m.DepartmentID == DEPARTMENT_ID_405))
            {
                return departmentList.Where(m => m.OrganizationType == "1").Select(m => m.DepartmentID);
            }

            var departIDs = new HashSet<int>() { DEPARTMENT_ID_405 };
            foreach (var empToDepart in employeeToDepartments)
            {
                // 获取此部门的连续上下级部门ID集合
                var departments = await _dictionaryService.GetSuperAndSubDepartmentsByID(empToDepart.DepartmentID, false);
                departments.ForEach(m => departIDs.Add(m.DepartmentID));
            }
            return departIDs;
        }
    }
}
