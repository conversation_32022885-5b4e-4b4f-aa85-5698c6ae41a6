﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 部门岗位
    /// </summary>
    public class PostSelectOptionsView
    {
        /// <summary>
        /// 选项的值 | 可以是非基本类型变量
        /// </summary>
        public object Value { get; set; }

        /// <summary>
        /// 选项标签
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 选项标签别名
        /// </summary>
        public string LocalLabel { get; set; }

        /// <summary>
        /// 岗位名称
        /// </summary>
        public string PostName { get; set; }

        /// <summary>
        /// 岗位类
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 工作时间区间
        /// </summary>
        public string WorkingTimeRange { get; set; }

        /// <summary>
        /// 考勤天数
        /// </summary>
        public decimal AttendanceDays { get; set; }

        /// <summary>
        /// 考勤班别编码
        /// </summary>
        public string PostShiftID { get; set; }

        /// <summary>
        /// 前景色
        /// </summary>
        public string Color { get; set; }

        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackGroundColor { get; set; }

        /// <summary>
        /// 半天考勤计算方法
        /// </summary>
        public string HalfDayAttendanceCalc { get; set; }
    }
}
