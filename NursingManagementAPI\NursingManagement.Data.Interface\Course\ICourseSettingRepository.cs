﻿using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data
{
    public interface ICourseSettingRepository :ICacheRepository
    {
        /// <summary>
        /// 根据主键获取培训课程记录（非缓存）
        /// </summary>
        /// <param name="courseSettingID"></param>
        /// <returns></returns>
        Task<CourseSettingInfo> GetCourseSettingAsNoCacheAsync(string courseSettingID);

        /// <summary>
        /// 根据课程分类获取对应课程配置
        /// </summary>
        /// <param name="courseTypeID">课程类别</param>
        /// <returns></returns>
        Task<List<CourseSettingInfo>> GetCourseSettingsAsync(string courseTypeID);
        /// <summary>
        /// 根据课程ID集合获取数据
        /// </summary>
        /// <param name="courseSettingIDs"></param>
        /// <returns></returns>
        Task<List<CourseSettingInfo>> GetCourseListAsync(List<string> courseSettingIDs);
        /// <summary>
        /// 获取所有课程分类
        /// </summary>
        /// <returns></returns>
        Task<List<CourseSettingInfo>> GetAllCourseSetting();
    }
}
