﻿namespace NursingManagement.ViewModels.Employee
{
    /// <summary>
    /// 人员个人信息视图
    /// </summary>
    public class EmployeePersonalDataView
    {
        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        /// 姓名拼音码
        /// </summary>
        public string NamePinyin { get; set; }

        /// <summary>
        /// 性别(GB/T 2261.1-2003转码后)
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 国籍(GB/T 2659.1-2022转码后)
        /// </summary>
        public string Nationality { get; set; }

        /// <summary>
        /// 民族(GB/T 3304-1991)
        /// </summary>
        public string Nation { get; set; }

        /// <summary>
        /// 身份证号码
        /// </summary>
        public string IDCardNo { get; set; }

        /// <summary>
        /// 公历生日
        /// </summary>
        public string Birthdate { get; set; }

        /// <summary>
        /// 农历生日
        /// </summary>
        public string LunarBirthdate { get; set; }

        /// <summary>
        /// 家庭地址(身份证)
        /// </summary>
        public string HomeAddress { get; set; }

        /// <summary>
        /// 实际住址
        /// </summary>
        public string ActualAddress { get; set; }

        /// <summary>
        /// 籍贯
        /// </summary>
        public string NativePlace { get; set; }

        /// <summary>
        /// 婚姻状况(GB/T 2261.2-2003转码后)
        /// </summary>
        public string Marriage { get; set; }

        /// <summary>
        /// 生育状态(00:未育、10：已育)
        /// </summary>
        public string DeliverCode { get; set; }

        /// <summary>
        /// 政治面貌(GB/T 4762-1984转码后)
        /// </summary>
        public string Polity { get; set; }
    }
}
