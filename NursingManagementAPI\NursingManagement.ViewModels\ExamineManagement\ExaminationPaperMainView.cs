﻿namespace NursingManagement.ViewModels.Examine
{
    /// <summary>
    /// 试卷视图
    /// </summary>
    public class ExaminationPaperMainView
    {
        /// <summary>
        /// </summary>
        public string ExaminationPaperMainID { get; set; }

        /// <summary>
        /// 试卷名称
        /// </summary>
        public string PaperTitle { get; set; }

        /// <summary>
        /// 难度等级
        /// </summary>
        public string DifficultyLevel { get; set; }

        /// <summary>
        /// 题目数量
        /// </summary>
        public int QuestionCount { get; set; }

        /// <summary>
        /// 总分
        /// </summary>
        public decimal TotalPoints { get; set; }

        /// <summary>
        /// 及格分数
        /// </summary>
        public decimal? PassingScore { get; set; }

        /// <summary>
        /// 题库来源
        /// </summary>
        public List<string> QuestionBankIDs { get; set; }

        /// <summary>
        /// 新增人
        /// </summary>
        public string AddEmployeeName { get; set; }

        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime? AddDateTime { get; set; }

        /// <summary>
        /// 异动人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string ModifyEmployeeName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyDateTime { get; set; }

        /// <summary>
        /// 是否是实操类
        /// </summary>
        public bool IsPractical { get; set; }

        /// <summary>
        /// 动态组卷标记
        /// </summary>
        public bool ExaminationPaperComposition { get; set; } = false;

        /// <summary>
        /// 规则记录ID
        /// </summary>
        public string ExaminationConditionRecordID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 组卷条件
        /// </summary>
        public string ConditionName { get; set; }

        /// <summary>
        /// 题库ID 实操类试卷需要存储
        /// </summary>
        public string QuestionBankID { get; set; }

        /// <summary>
        /// 试卷类型
        /// </summary>
        public string PaperType { get; set; }

        /// <summary>
        /// 试卷题目组织模式
        /// </summary>
        public string PaperQuestionMode { get; set; }
    }
}
