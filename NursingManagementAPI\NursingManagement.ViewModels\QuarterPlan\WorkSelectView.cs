﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 工作计划选择View
    /// </summary>
    public class WorkSelectView
    {
        /// <summary>
        /// 计划名称
        /// </summary>
        public string PlanName { get; set; }

        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 计划主表ID
        /// </summary>
        public string PlanMainID { get; set; }

        /// <summary>
        /// 分类分组View集合
        /// </summary>
        public TypeView[] TypeViews { get; set; }

        /// <summary>
        /// 分类分组View
        /// </summary>
        public class TypeView
        {
            /// <summary>
            /// 分类ID
            /// </summary>
            public int TypeID { get; set; }

            /// <summary>
            /// 分类名称
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// 工作集合
            /// </summary>
            public WorkView[] WorkViews { get; set; }
        }

        /// <summary>
        /// 工作View
        /// </summary>
        public class WorkView
        {
            /// <summary>
            /// 执行项目ID
            /// </summary>
            public int? InterventionID { get; set; }

            /// <summary>
            /// 分类ID
            /// </summary>
            public int TypeID { get; set; }

            /// <summary>
            /// 工作内容
            /// </summary>
            public string WorkContent { get; set; }

            /// <summary>
            /// 要求
            /// </summary>
            public string Requirement { get; set; }

            /// <summary>
            /// 是否为重点
            /// </summary>
            public bool IsPriority { get; set; }
        }
    }
}
