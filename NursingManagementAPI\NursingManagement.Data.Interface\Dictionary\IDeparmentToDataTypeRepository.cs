﻿
using NursingManagement.Models;
namespace NursingManagement.Data.Interface
{
    public interface IDeparmentToDataTypeRepository : ICacheRepository
    {
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        Task<List<DeparmentToDataTypeInfo>> GetAllDataAsync();
        /// <summary>
        /// 根据tableName和dataTypeKey获取对应数据
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="dataTypeKey"></param>
        /// <returns></returns>
        Task<List<Dictionary<string, object>>> GetDataByTableNameAndKey(string tableName, string dataTypeKey);
    }
}
