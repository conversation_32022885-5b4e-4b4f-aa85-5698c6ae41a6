﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IMessageRecordRepository
    {
        /// <summary>
        /// 根据ID异步获取消息记录
        /// </summary>
        /// <param name="id">消息ID</param>
        /// <returns>消息记录</returns>
        Task<MessageRecordInfo> GetRecordByIdAsync(string id);
        /// <summary>
        /// 获取消息集合
        /// </summary>
        /// <param name="messageType"></param>
        /// <returns></returns>
        Task<List<MessageRecordInfo>> GetListAsNoTrack(string messageType);
        /// <summary>
        /// 获取最新一次消息记录
        /// </summary>
        /// <param name="messageRecordIDs">消息ID集合</param>
        /// <param name="messageRecordType">消息记录类型</param>
        /// <returns></returns>
        Task<MessageRecordInfo> GetLastRecordByIDsAndTypeAsync(IEnumerable<string> messageRecordIDs, string messageRecordType);
        /// <summary>
        /// 获取消息内容
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <returns></returns>
        Task<string> GetContentByID(string messageRecordID);
        /// <summary>
        /// 获取从开始日期到现在的消息集合
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <returns></returns>
        Task<List<MessageRecordInfo>> GetListByStartDateAsNoTrack(DateTime startDate);
        /// <summary>
        /// 获取最后一条系统更新记录
        /// </summary>
        /// <returns></returns>
        Task<MessageRecordView> GetLastSystemUpdateRecord();
    }
}
