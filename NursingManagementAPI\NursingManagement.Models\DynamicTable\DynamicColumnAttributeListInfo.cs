﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 表格列属性字典
    /// </summary>
    [Serializable]
    [Table("DynamicColumnAttributeList")]
    public class DynamicColumnAttributeListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column(TypeName = "varchar(20)")]
        public string AttributeCode { get; set; }
        /// <summary>
        /// 属性说明
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Description { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 默认值
        /// </summary>
        [Column(TypeName = "VARCHAR(20)")]
        public string DefaultValue { get; set; }
        /// <summary>
        /// 是否提供用户修改
        /// </summary>
        public int UserModifyFlag { get; set; }
    }
}
