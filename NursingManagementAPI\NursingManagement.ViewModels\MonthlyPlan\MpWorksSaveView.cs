﻿namespace NursingManagement.ViewModels;

public class MpWorksSaveView
{
    /// <summary>
    /// 年度计划主表ID
    /// </summary>
    public string AnnualPlanMainID { get; set; }
    /// <summary>
    /// 月度计划主表ID
    /// </summary>
    public string MonthlyPlanMainID { get; set; }
    /// <summary>
    /// 年份
    /// </summary>
    public int Annual { get; set; }
    /// <summary>
    /// 工作所属月度
    /// </summary>
    public int Month { get; set; }
    /// <summary>
    /// 科室ID
    /// </summary>
    public int DepartmentID { get; set; }
    /// <summary>
    /// 工作内容
    /// </summary>
    public TieredPlanWork[] WorkViews { get; set; }
    /// <summary>
    /// 是否是首次导入
    /// </summary>
    public bool IsFirstImport { get; set; }
    /// <summary>
    /// 工号
    /// </summary>
    public string EmployeeID { get; set; }
}
