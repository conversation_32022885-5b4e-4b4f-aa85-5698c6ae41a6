﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 审批明细记录
    /// </summary>
    [Serializable]
    [Table("ApproveDetail")]
    public class ApproveDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 审批明细记录唯一码，Guid
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ApproveDetailID { get; set; }

        /// <summary>
        /// 关联审批主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveMainID { get; set; }

        /// <summary>
        /// 预审批人，可以审批当前业务节点的人员工号集合，多个工号使用||分割
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string PreApproveEmployeeID { get; set; }

        /// <summary>
        /// 实际审批人员ID,如果人员岗位发生改变，则根据实际情况，进行业务交接（系统建立交接功能，改变审批链），或自己进行审签
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ApproveEmployeeID { get; set; }

        /// <summary>
        /// 当前审批业务进行到的节点，对应审批流程表中的NodeID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveNodeDetailID { get; set; }

        /// <summary>
        /// 审批方式（【默认】1表示顺序签、2表示会签、3表示或签）
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string ApproveModel { get; set; }

        /// <summary>
        /// 实际审批时间
        /// </summary>
        [Column(TypeName = "datetime")]
        public DateTime? ApproveDateTime { get; set; }

        /// <summary>
        /// 审批结果（空表示为进行审批，1：同意、2：拒绝）
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string StatusCode { get; set; }

        /// <summary>
        /// 审批建议
        /// </summary>
        [Column(TypeName = "nvarchar(400)")]
        public string ApproveSuggestions { get; set; }
    }

}
