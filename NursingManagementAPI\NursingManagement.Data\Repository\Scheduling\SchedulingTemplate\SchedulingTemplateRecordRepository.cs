﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class SchedulingTemplateRecordRepository : ISchedulingTemplateRecordRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        public SchedulingTemplateRecordRepository(
            NursingManagementDbContext db
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<SchedulingTemplateRecordInfo>> GetRecordListByDepartmentID(int departmentID, string statusCode = "1")
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContext.SchedulingTemplateRecordInfos.Where(m => m.DepartmentID == departmentID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                        .IfWhere(string.IsNullOrWhiteSpace(statusCode),m=>m.StatusCode == statusCode)
                        .OrderBy(m => m.AddDateTime).ToListAsync();
        }

        public async Task<SchedulingTemplateRecordInfo> GetRecordByID(string schedulingTemplateRecordID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContext.SchedulingTemplateRecordInfos.Where(m => m.SchedulingTemplateRecordID == schedulingTemplateRecordID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}
