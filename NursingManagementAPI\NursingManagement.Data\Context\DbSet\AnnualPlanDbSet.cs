﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {

        /// <summary>
        /// 年度计划类别字典
        /// </summary>
        public DbSet<AnnualPlanTypeListInfo> AnnualPlanTypeListInfos { get; set; }
        /// <summary>
        /// 年度计划目标字典
        /// </summary>
        public DbSet<AnnualGoalListInfo> AnnualGoalListInfos { get; set; }
        /// <summary>
        /// 执行项目字典
        /// </summary>
        public DbSet<InterventionListInfo> InterventionListInfos { get; set; }
        /// <summary>
        /// 年度计划指标字典
        /// </summary>
        public DbSet<AnnualIndicatorListInfo> AnnualIndicatorListInfos { get; set; }
        /// <summary>
        /// 年度计划主表
        /// </summary>
        public DbSet<AnnualPlanMainInfo> AnnualPlanMainInfos { get; set; }
        /// <summary>
        /// 年度计划目标
        /// </summary>
        public DbSet<AnnualPlanMainGoalInfo> AnnualPlanMainGoalInfos { get; set; }
        /// <summary>
        /// 年度计划目标分组
        /// </summary>
        public DbSet<AnnualPlanGoalGroupInfo> AnnualPlanGoalGroupInfos { get; set; }
        /// <summary>
        /// 年度计划项目明细
        /// </summary>
        public DbSet<AnnualPlanProjectDetailInfo> AnnualPlanProjectDetailInfos { get; set; }
        /// <summary>
        /// 年度计划指标明细
        /// </summary>
        public DbSet<AnnualPlanIndicatorDetailInfo> AnnualPlanIndicatorDetailInfos { get; set; }
        /// <summary>
        /// 年度计划制定主表
        /// </summary>
        public DbSet<AnnualInterventionMainInfo> AnnualPlanInterventionMainInfos { get; set; }
        /// <summary>
        /// 年度计划制定明细表
        /// </summary>
        public DbSet<AnnualInterventionDetailInfo> AnnualPlanInterventionDetailInfos { get; set; }
        /// <summary>
        /// 年度计划排程主表
        /// </summary>
        public DbSet<AnnualScheduleMainInfo> AnnualScheduleMainInfos { get; set; }
        /// <summary>
        /// 年度计划排程明细
        /// </summary>
        public DbSet<AnnualScheduleDetailInfo> AnnualScheduleDetailInfos { get; set; }
        /// <summary>
        /// 年度计划执行项目负责人表
        /// </summary>
        public DbSet<AnnualInterventionMainPrincipalInfo> AnnualInterventionMainPrincipalInfos { get; set; }
        /// <summary>
        /// 执行项目明细对排程关系表
        /// </summary>
        public DbSet<APInterventionDetailToScheduleInfo> APInterventionDetailToScheduleInfos { get; set; }
        /// <summary>
        /// 季度计划主表
        /// </summary>
        public DbSet<QuarterPlanMainInfo> QuarterPlanMainInfos { get; set; }
        /// <summary>
        /// 季度计划明细表
        /// </summary>
        public DbSet<QuarterPlanDetailInfo> QuarterPlanDetailInfos { get; set; }
        /// <summary>
        /// 月度计划主表
        /// </summary>
        public DbSet<MonthlyPlanMainInfo> MonthlyPlanMainInfos { get; set; }
        /// <summary>
        /// 月度计划明细表
        /// </summary>
        public DbSet<MonthlyPlanDetailInfo> MonthlyPlanDetailInfos { get; set; }
        /// <summary>
        /// 计划执行任务对计划工作关系表
        /// </summary>
        public DbSet<MonthlyWorkToTaskInfo> MonthlyWorkToTaskInfos { get; set; }
    }
}
