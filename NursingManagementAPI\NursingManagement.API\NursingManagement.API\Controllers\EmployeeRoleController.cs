﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 员工角色控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/EmployeeRole")]
    [EnableCors("any")]
    public class EmployeeRoleController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IEmployeeRoleService _employeeRoleService;

        /// <summary>
        /// 构造注入
        /// </summary>
        /// <param name="session"></param>
        /// <param name="employeeRoleService"></param>
        public EmployeeRoleController(
            ISessionService session,
            IEmployeeRoleService employeeRoleService)
        {
            _session = session;
            _employeeRoleService = employeeRoleService;
        }



        /// <summary>
        /// 根据部门ID获取对应的员工角色列表
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeRoleList")]
        public async Task<IActionResult> GetEmployeeRoleList(int? departmentID)
        {
            var result = new ResponseResult();
            
                var session = await _session.GetSession();
                if (session == null)
                {
                    result.TimeOut();
                    return result.ToJson();
                }
                result.Data = await _employeeRoleService.GetEmployeeRoleListAsync(departmentID);
                        return result.ToJson();
        }
        /// <summary>
        /// 保存人员角色
        /// </summary>
        /// <param name="employeeRoleView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEmployeeRole")]
        public async Task<IActionResult> SaveEmployeeRole([FromBody] EmployeeRoleView employeeRoleView)
        {
            var result = new ResponseResult();
            
                var session = await _session.GetSession();
                if (session == null)
                {
                    result.TimeOut();
                    return result.ToJson();
                }
                result.Data = await _employeeRoleService.SaveEmployeeRoleAsync(employeeRoleView, session.EmployeeID, session.HospitalID);
                        return result.ToJson();
        }
        /// <summary>
        /// 删除人员角色
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteEmployeeRole")]
        public async Task<IActionResult> DeleteEmployeeRole(string employeeID)
        {
            var result = new ResponseResult();
            
                var session = await _session.GetSession();
                if (session == null)
                {
                    result.TimeOut();
                    return result.ToJson();
                }
                result.Data = await _employeeRoleService.DeleteEmployeeRoleAsync(employeeID, session.EmployeeID);
                        return result.ToJson();
        }
    }
}
