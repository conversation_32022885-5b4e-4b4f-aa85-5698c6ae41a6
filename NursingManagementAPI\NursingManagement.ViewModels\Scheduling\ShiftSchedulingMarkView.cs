﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班明细标记
    /// </summary>
    public class ShiftSchedulingMarkView
    {
        /// <summary>
        /// 排班明细标记ID
        /// </summary>
        public int MarkID { get; set; }
        /// <summary>
        /// 标记文字
        /// </summary>
        public string Text { get; set; }
        /// <summary>
        /// 标记符号
        /// </summary>
        public string Icon { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 前景色
        /// </summary>
        public string Color { get; set; }
        /// <summary>
        /// 背景色
        /// </summary>
        public string BackGroundColor { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 标记值
        public string MarkValue { get; set; }
    }
}
