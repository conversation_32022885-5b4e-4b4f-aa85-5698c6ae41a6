﻿using NursingManagement.ViewModels;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划明细
    /// </summary>
    [Serializable]
    [Table("AnnualPlanProjectDetail")]
    public class AnnualPlanProjectDetailInfo : MutiModifyInfo, IBaseAPDetail
    {
        /// <summary>
        /// 年度明细表GUID
        /// </summary>
        [Key]
        [Column("AnnualPlanProjectDetailID", TypeName = "varchar(32)")]
        public string DetailID { get; set; }
        /// <summary>
        /// 年度计划主表主键Guid，对应各部门年度计划主表
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 年度计划目标GuID,主键，对应年度计划目标表
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 年度计划目标分组主键ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanGoalGroupID { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 工作项目说明
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Content { get; set; }
        /// <summary>
        /// 特别标记
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string MarkID { get; set; }
        /// <summary>
        /// 呈现顺序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 导航属性，一个Project对应一个Group
        /// </summary>
        public AnnualPlanGoalGroupInfo AnnualPlanGoalGroup { get; set; }
    }
}
