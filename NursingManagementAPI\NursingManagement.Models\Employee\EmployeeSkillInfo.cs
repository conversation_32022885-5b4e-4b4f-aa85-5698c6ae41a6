﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员技能
    /// </summary>
    [Serializable]
    [Table("EmployeeSkill")]
    public class EmployeeSkillInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeSkillID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 技能分类(10:语言、20:计算机)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SkillClassification { get; set; }

        /// <summary>
        /// 技能名称(10:英语、20:计算机)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SkillCode { get; set; }

        /// <summary>
        /// 熟练程度(10、一般/20、良好/30、熟练/40、精通)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ProficiencyLevel { get; set; }

        /// <summary>
        /// 证书(10:英语四级)
        /// </summary>
        [Column(TypeName = "nvarchar(50)")]
        public string CertificateCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string Remark { get; set; }
    }
}