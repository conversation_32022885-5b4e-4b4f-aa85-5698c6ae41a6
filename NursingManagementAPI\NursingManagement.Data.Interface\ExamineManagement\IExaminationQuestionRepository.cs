﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IExaminationQuestionRepository
    {
        /// <summary>
        /// 根据问题ID获取问题记录。
        /// </summary>
        /// <param name="examinationQuestionID">考试问题列表的ID。</param>
        /// <returns>考试问题列表信息。</returns>
        Task<ExaminationQuestionInfo> GetDataByID(int examinationQuestionID);
        /// <summary>
        /// 根据题库ID获取题目列表
        /// </summary>
        /// <param name="questionBankID">问题的ID</param>
        /// <param name="asNoTrack">是否跟踪实体</param>
        /// <returns>考试问题集合。</returns>
        Task<List<ExaminationQuestionInfo>> GetListByQuestionBankID(string questionBankID, bool asNoTrack = false);
        /// <summary>
        /// 根据题库ID集合获取题目数据
        /// </summary>
        /// <param name="questionBankIDs"></param>
        /// <param name="asNoTrack">是否跟踪实体</param>
        /// <returns></returns>
        Task<List<ExaminationQuestionInfo>> GetListByQuestionBankIDList(List<string> questionBankIDs, bool asNoTrack = false);
        /// <summary>
        /// 根据题库ID，按组获取问题ID与描述的键值对
        /// </summary>
        /// <param name="questionBankIDs">题库ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string, (int questionID, string questionContent)[]>> GetQuestionIDAndContent(IEnumerable<string> questionBankIDs);
        /// <summary>
        /// 根据题目ID集合获取记录
        /// </summary>
        /// <param name="examinationQuestionListIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationQuestionInfo>> GetListByIDList(List<int> examinationQuestionListIDs);

        /// <summary>
        /// 根据问题ID获取所属题库
        /// </summary>
        /// <param name="examinationQuestionIDs">考试问题列表的ID。</param>
        /// <returns></returns>
        Task<List<(int, string)>> GetQuestionBanksByQuestionIDs(List<int> examinationQuestionIDs);

        /// <summary>
        /// 获取问题类型和问题ID集合
        /// </summary>
        /// <param name="questionBankIDs">题库主键ID集合</param>
        /// <returns></returns>
        Task<List<ExaminationQuestionInfo>> GetQuestionTypeAndIDByBankIDs(List<string> questionBankIDs);

        /// <summary>
        /// 获取主键最大值
        /// </summary>
        /// <returns></returns>
        Task<int> GetMaxID();
    }
}
