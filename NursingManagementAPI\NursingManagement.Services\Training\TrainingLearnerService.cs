﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class TrainingLearnerService : ITrainingLearnerService
    {
        private Logger _logger = LogManager.GetCurrentClassLogger();
        private IUnitOfWork _unitOfWork;
        private readonly ITrainingLearnerRepository _trainingLearnerRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly ITrainingRecordRepository _trainingRecordRepository;
        private readonly ISignUpRecordRepository _signUpRecordRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly ISignUpRecordService _signUpRecordService;

        public TrainingLearnerService(
            IUnitOfWork unitOfWork,
            ITrainingLearnerRepository trainingLearnerRepository,
            IDepartmentListRepository departmentListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IEmployeeStaffDataRepository employeeStaffDataRepository,
            ICapabilityLevelRepository capabilityLevelRepository,
            ITrainingRecordRepository trainingRecordRepository,
            ISignUpRecordRepository signUpRecordRepository,
            ISettingDictionaryRepository settingDictionaryRepository,
            ISignUpRecordService signUpRecordService
            )
        {
            _unitOfWork = unitOfWork;
            _trainingLearnerRepository = trainingLearnerRepository;
            _departmentListRepository = departmentListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
            _trainingRecordRepository = trainingRecordRepository;
            _signUpRecordRepository = signUpRecordRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _signUpRecordService = signUpRecordService;
        }

        /// <summary>
        /// 获取学员列表
        /// </summary>
        /// <param name="trainingRecordID">查询参数</param>
        /// <param name="session">缓存</param>
        /// <returns>学员列表</returns>
        public async Task<List<TrainingLearnerView>> GetTraineeList(string trainingRecordID, Session session)
        {
            var trainingLearnerViews = await GetTrainingLearnerView(trainingRecordID, session);
            if (trainingLearnerViews.Count() <= 0)
            {
                return [];
            }
            var departmentDict = await _departmentListRepository.GetByOrganizationType("1");
            var employeeDict = await _employeePersonalDataRepository.GetIDAndNameData();
            var staffDatas = await _employeeStaffDataRepository.GetHaveCapabilityLevelList();
            var staffDict = staffDatas.ToLookup(m => m.EmployeeID);
            //能力层级字典
            var capabilityLevelList = await _capabilityLevelRepository.GetByCacheAsync();
            foreach (TrainingLearnerView view in trainingLearnerViews)
            {
                view.EmployeeName = employeeDict.Find(m => m.EmployeeID == view.EmployeeID)?.EmployeeName;
                view.DepartmentName = departmentDict.Find(m => m.DepartmentID == view.DepartmentID)?.DepartmentContent;
                view.LearningCount ??= 0;
                view.TrainingDuration ??= 0;
                // 护理层级
                var staffData = staffDict[view.EmployeeID]?.FirstOrDefault();
                if (staffData != null)
                {
                    view.CapabilityLevel = capabilityLevelList.Find(m => m.CapabilityLevelID == staffData.CapabilityLevelID)?.CapabilityLevelName;
                }
            }
            return [.. trainingLearnerViews.OrderByDescending(m => m.AddDateTime)];
        }
        /// <summary>
        /// 根据人员权限和是否跳转获取培训记录
        /// </summary>
        /// <param name="trainingRecordID">培训主记录ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        private async Task<List<TrainingLearnerView>> GetTrainingLearnerView(string trainingRecordID, Session session)
        {
            var trainingLearnerViews = new List<TrainingLearnerView>();
            if (!string.IsNullOrEmpty(trainingRecordID))
            {
                //培训清单跳转获取培训记录
                trainingLearnerViews = await _trainingLearnerRepository.GetViewListByTrainingRecordIDsAsync(new List<string> { trainingRecordID });
                return trainingLearnerViews;
            }
            //个人培训页面获取培训记录
            trainingLearnerViews = await _trainingLearnerRepository.GetByEmployeeIdAsync(session.EmployeeID);
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "TrainingManagement",
                SettingTypeCode = "TrainingLearner",
                SettingTypeValue = "SearchDataRoleID",
            };
            var authoritySetting = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var authorityRoles = authoritySetting.Select(m => m.SettingValue).ToList();
            //教研组长获取培训讲师/培训主持人是自己的记录
            if (authorityRoles.Any(m => session.Roles.Contains(int.Parse(m))))
            {
                var recordIDs = await _trainingRecordRepository.GetTrainingRecordIDsByEmployeeID(session.EmployeeID, session.HospitalID);
                var trainingLearners = await _trainingLearnerRepository.GetViewListByTrainingRecordIDsAsync(recordIDs);
                trainingLearnerViews.AddRange(trainingLearners);
            }
            return trainingLearnerViews.GroupBy(m=>m.TrainingLearnerID).Select(m=>m.FirstOrDefault()).ToList();
        }

        /// <summary>
        /// 更新学员记录
        /// </summary>
        /// <param name="trainingLearnerInfo">学员信息</param>
        /// <param name="employeeID">操作员工ID</param>
        /// <returns>更新是否成功</returns>
        public async Task<bool> UpdateTraineeRecordAsync(TrainingLearnerInfo trainingLearnerInfo, string employeeID)
        {
            trainingLearnerInfo.Modify(employeeID);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 删除学员记录
        /// </summary>
        /// <param name="trainingLearnerID">学员记录ID</param>
        /// <param name="employeeID">操作员工ID</param>
        /// <returns>删除是否成功</returns>
        public async Task<bool> DeleteTraineeAsync(string trainingLearnerID, string employeeID)
        {
            var deleteTrainingLearnerInfo = await _trainingLearnerRepository.GetByIdAsync(trainingLearnerID);
            if (deleteTrainingLearnerInfo == null)
            {
                _logger.Warn($"找不到需要删除的学员培训记录!!,trainingLearnerID={trainingLearnerID}");
                return false;
            }

            deleteTrainingLearnerInfo.DeleteFlag = "*";
            deleteTrainingLearnerInfo.Modify(employeeID);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 设置学员为班长
        /// </summary>
        /// <param name="setMonitorParamsView">设置班长参数</param>
        /// <param name="employeeID">操作员工ID</param>
        /// <returns>设置是否成功</returns>
        public async Task<bool> SetTraineeMonitorAsync(TrainingLearnerParamView setMonitorParamsView, string employeeID)
        {
            var trainingLearner = await _trainingLearnerRepository.GetByIdAsync(setMonitorParamsView.TrainingLearnerID);
            if (trainingLearner == null)
            {
                _logger.Warn($"找不到需要设置班长的学员培训记录!!,setMonitorParamsView={ListToJson.ToJson(setMonitorParamsView)}");
                return false;
            }

            trainingLearner.MonitorFlag = setMonitorParamsView.MonitorFlag;
            trainingLearner.ModifyEmployeeID = employeeID;
            trainingLearner.ModifyDateTime = DateTime.Now;

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 获取培训时间线
        /// </summary>
        /// <param name="timelineSearchParamsView">时间线搜索参数</param>
        /// <returns>培训时间线数据</returns>
        public async Task<List<TimelineItemView>> GetTrainTimelineAsync(TrainingLearnerParamView timelineSearchParamsView)
        {
            var learnerRecord = await _trainingLearnerRepository.GetByIdAsync(timelineSearchParamsView.TrainingLearnerID);
            if (learnerRecord == null)
            {
                return [];
            }

            var trainingRecord = await _trainingRecordRepository.GetDataByID(learnerRecord.TrainingRecordID);
            if (trainingRecord == null)
            {
                return [];
            }
            var signUpRecord = await _signUpRecordRepository.GetBySourceIDAsNoTrackAsync(trainingRecord.TrainingRecordID);
            if (signUpRecord == null)
            {
                return [];
            }
            List<TimelineItemView> timelineItems = [];
            var trainingTimeLine = new TimelineItemView
            {
                Timestamp = trainingRecord.AddDateTime.ToString("yyyy-MM-dd HH:mm"),
                Title = "新增培训",
                Description = $" 开办培训，培训内容：{trainingRecord.TrainingContent}",
            };
            var signUpTimeLine = new TimelineItemView
            {
                Timestamp = signUpRecord.AddDateTime.ToString("yyyy-MM-dd HH:mm"),
                Title = "培训报名",
                Description = "",
            };
            timelineItems.Add(signUpTimeLine);
            timelineItems.Add(trainingTimeLine);
            return timelineItems;
        }

        /// <summary>
        /// 保存课程评价及课程建议
        /// </summary>
        /// <param name="recommendationsView">培训记录建议及满意度信息</param>
        /// <returns></returns>
        public async Task<bool> SaveCourseRecommendations(TrainingLearnerRecommendationsParamsView recommendationsView,string employeeID)
        {
            if (recommendationsView == null || string.IsNullOrEmpty(recommendationsView.TrainingLearnerID)) 
            {
                _logger.Error("保存培训满意度和建议失败，前端传递参数为空");
                return false; 
            }
            var learnerRecord = await _trainingLearnerRepository.GetByIdAsync(recommendationsView.TrainingLearnerID);
            if (learnerRecord == null)
            {
                _logger.Error($"保存培训满意度和建议失败，未找到相应培训记录，TrainingLearnerID：{recommendationsView.TrainingLearnerID}");
                return false;
            }
            learnerRecord.CourseSatisfaction = recommendationsView.CourseSatisfaction;
            learnerRecord.CourseRecommendations = recommendationsView.CourseRecommendations;
            learnerRecord.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 培训扫码签到
        /// </summary>
        /// <param name="trainingRecordID">培训主表记录</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<(bool, string)> TrainingLearnerSignIn(string trainingRecordID, long timeStamp, string employeeID)
        {
            var (flag, returnMessage, trainingLearnerID) = await CheckTrainingLearnerSignInData(trainingRecordID, timeStamp, employeeID);
            if (!string.IsNullOrEmpty(returnMessage))
            {
                return (flag, returnMessage);
            }
            var signUpRecord = new SignUpRecordInfo()
            {
                SourceID = trainingLearnerID,
                SourceType = "4",
                StatusCode = "1",
                SignUpType = "2",
            };
            returnMessage = await _signUpRecordService.SaveSignUpRecordAsync(signUpRecord, employeeID) ? "签到成功" : "签到失败";
            return (true, returnMessage);
        }
        /// <summary>
        /// 检核签到信息
        /// </summary>
        /// <param name="trainingRecordID">培训主表记录</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        private async Task<(bool, string, string)> CheckTrainingLearnerSignInData(string trainingRecordID, long timeStamp, string employeeID)
        {
            var returnMessage = "";
            var nowDateTime = DateTime.Now;
            var trainingRecord = await _trainingRecordRepository.GetDataByID(trainingRecordID);
            if (trainingRecord == null)
            {
                returnMessage = "签到失败，未查到本场培训";
                _logger.Error($"{returnMessage},TrainingRecordID:{trainingRecordID}");
                return (false, returnMessage, null);
            }
            if (trainingRecord.SignInFlag && trainingRecord.QRCodeRefreshTime > 0)
            {
                // 获取当前时间戳（秒）,比较当前时间戳和传入的二维码时间戳
                var currentTimeStamp = ((DateTimeOffset) nowDateTime).ToUnixTimeSeconds();
                if (currentTimeStamp - (timeStamp/1000) > trainingRecord.QRCodeRefreshTime)
                {
                    returnMessage = "签到码已过期";
                    return (false, returnMessage, null);
                }
            }
            if (trainingRecord.EndDateTime <= nowDateTime)
            {
                return (false, $"签到失败，【{trainingRecord.TrainingContent}】培训已结束", null);
            }
            //先找signUpRecord是否同意，没有或拒绝：签到失败，【】培训中没有您的报名记录
            //①群组ID为空，使用trainingRecordID和人员ID找到同意的记录，sourcetype是1   ②群组ID不为空，使用群组ID和人员ID找到同意的记录，sourcetype是2
            var recordID = string.IsNullOrEmpty(trainingRecord.TrainingClassMainID) ? trainingRecordID : trainingRecord.TrainingClassMainID;
            var sourceType = string.IsNullOrEmpty(trainingRecord.TrainingClassMainID) ? "1" : "2";
            var records = await _signUpRecordRepository.GetListByRecordID(recordID, sourceType);
            if (!records.Any(m => m.StatusCode == "1"))
            {
                returnMessage = $"签到失败，【{trainingRecord.TrainingContent}】培训中没有您的报名记录";
                _logger.Error($"{returnMessage},TrainingRecordID:{trainingRecordID}，EmployeeID：{employeeID}");
                return (false, returnMessage, null);
            }
            var trainingLearnerInfo = await _trainingLearnerRepository.GetByRecordIdAndEmployeeId(trainingRecordID, employeeID);
            if (trainingLearnerInfo == null)
            {
                returnMessage = $"签到失败，【{trainingRecord.TrainingContent}】没有您的培训记录";
                _logger.Error($"{returnMessage},TrainingRecordID:{trainingRecordID}，EmployeeID：{employeeID}");
                return (false, returnMessage, null);
            }
            //找sourcetype是4的，代表培训签到
            var signUpRecordInfo = await _signUpRecordRepository.GetListByRecordID(trainingLearnerInfo.TrainingLearnerID,"4");
            if (signUpRecordInfo.Count()>0)
            {
                return (true, $"您在{signUpRecordInfo.FirstOrDefault().AddDateTime:yyyy-MM-dd HH:mm}已签到", null);
            }
            return (true, returnMessage, trainingLearnerInfo.TrainingLearnerID);
        }

    }
}