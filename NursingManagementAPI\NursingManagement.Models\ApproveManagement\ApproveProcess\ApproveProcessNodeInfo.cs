﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 审批流程节点表
    /// </summary>
    [Serializable]
    [Table("ApproveProcessNode")]
    public class ApproveProcessNodeInfo : MutiModifyInfo
    {
        /// <summary>
        /// 审批流程节点唯一码，Guid
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ApproveNodeID { get; set; }
        /// <summary>
        /// 审批流程设置表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveProcessID { get; set; }
        /// <summary>
        /// 审批节点名称,默认：审批人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ApproveNodeName { get; set; }
        /// <summary>
        /// 审批时限，0表示无时限,使用分钟
        /// </summary>
        public int ApproveTimeLimit { get; set; }
        /// <summary>
        /// 后一审批节点ID，为空串表示末尾节点
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string NextNodeID { get; set; }
        /// <summary>
        /// 审批方式（【默认】1表示顺序签、2表示会签、3表示或签）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ApproveModel { get; set; }
    }
}
