﻿using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 消息确认记录仓储接口，提供消息确认记录的查询操作。
    /// </summary>
    public interface IMessageConfirmationRepository
    {
        /// <summary>
        /// 根据查询条件获取消息确认记录
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>返回满足条件的消息确认记录列表</returns>
        Task<List<MessageConfirmationInfo>> FindAsync(Expression<Func<MessageConfirmationInfo, bool>> predicate);
        /// <summary>
        /// 根据查询条件获取消息确认记录
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>返回满足条件的消息确认记录</returns>
        Task<MessageConfirmationInfo> SingleRecordAsync(Expression<Func<MessageConfirmationInfo, bool>> predicate);
        /// <summary>
        /// 根据消息记录ID查询消息确认记录
        /// </summary>
        /// <param name="messageRecordId">消息记录的ID</param>
        /// <returns>返回满足条件的消息确认记录列表</returns>
        Task<List<MessageConfirmationInfo>> GetByMessageRecordIdAsync(string messageRecordId);

        /// <summary>
        /// 根据员工ID获取 指定状态 消息记录ID集合
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <param name="isConfirmed">是否确认，true为已确认，false为未确认</param>
        /// <returns>返回满足条件的消息记录ID集合</returns>
        Task<List<string>> GetMessageRecordIDsByEmployeeIdAsync(string employeeId, bool isConfirmed);

        /// <summary>
        /// 获取某条信息某人是否已经确认
        /// </summary>
        /// <param name="messageRecordId">消息记录ID</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>如果该员工已经确认，返回true，否则返回false</returns>
        Task<bool> IsConfirmedAsync(string messageRecordId, string employeeId);
    }
}
