﻿using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Repository;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class AdjustScheduleDynamicColumn
    {
        private readonly IAdjustScheduleRecordRepository _adjustScheduleRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;

        public AdjustScheduleDynamicColumn(IAdjustScheduleRecordRepository adjustScheduleRecordRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IDepartmentPostRepository departmentPostRepository)
        {
            _adjustScheduleRecordRepository = adjustScheduleRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentPostRepository = departmentPostRepository;
        }



        /// <summary>
        /// 根据主键来源表动态列
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetDynamicColumnListByRecordIDAsync(List<string> recordIDs)
        {
            var recordLists = await _adjustScheduleRecordRepository.GetRecordsByIDsAsNoTrackAsync(recordIDs);
            if (recordLists.Count <= 0)
            {
                return null;
            }
            List<Dictionary<string, object>> resultList = new();
            foreach (var item in recordLists)
            {
                var view = await CreateDynamicViewAsync(item);
                resultList.Add(view);
            }
            return resultList;
        }

        /// <summary>
        /// 创建请求审批所需参数
        /// </summary>
        /// <param name="adjustScheduleRecordInfo">调班申请记录</param>
        /// <returns></returns>
        private async Task<Dictionary<string, object>> CreateDynamicViewAsync(AdjustScheduleRecordInfo adjustScheduleRecordInfo)
        {
            var employeeData = await _employeePersonalDataRepository.GetDataByEmployeeIDs(new List<string> { adjustScheduleRecordInfo.AddEmployeeID, adjustScheduleRecordInfo.TargetEmployeeID });
            var autoSchedule = "否";
            if (adjustScheduleRecordInfo.AutoScheduleFlag.HasValue)
            {
                autoSchedule = adjustScheduleRecordInfo.AutoScheduleFlag.Value ? "是" : "否";
            }
            return new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase)
            {
                { "SourceID", adjustScheduleRecordInfo.AdjustScheduleRecordID },
                { "ApproveRecordID", adjustScheduleRecordInfo.ApproveRecordID },
                { "ProveCategory", "AA-020" },
                { "DepartmentID", adjustScheduleRecordInfo.DepartmentID },
                { "AddEmployeeID", adjustScheduleRecordInfo.AddEmployeeID },
                { "StartDate", adjustScheduleRecordInfo.AdjustDate.ToString("yyyy-MM-dd") },
                { "EndDate", adjustScheduleRecordInfo.TargetDate.ToString("yyyy-MM-dd") },
                { "AdjustEmployeeName", employeeData.FirstOrDefault(m => m.Key == adjustScheduleRecordInfo.AddEmployeeID).Value },
                { "TargetEmployeeName", employeeData.FirstOrDefault(m => m.Key == adjustScheduleRecordInfo.TargetEmployeeID).Value },
                { "Reason", adjustScheduleRecordInfo.Reason },
                { "AdjustTimeOfDay", adjustScheduleRecordInfo.AdjustNoonType == "1" ? "上午" : (adjustScheduleRecordInfo.AdjustNoonType == "2" ? "下午" : "整班") },
                { "TargetNoonType", adjustScheduleRecordInfo.TargetNoonType == "1" ? "上午" : (adjustScheduleRecordInfo.TargetNoonType == "2" ? "下午" : "整班") },
                { "AutoSchedule", autoSchedule },
                { "AdjustDeptPostName", adjustScheduleRecordInfo.AdjustDepartmentPost },
                { "TargetDeptPostName", adjustScheduleRecordInfo.TargetDepartmentPost }

            };

        }

    }
}
