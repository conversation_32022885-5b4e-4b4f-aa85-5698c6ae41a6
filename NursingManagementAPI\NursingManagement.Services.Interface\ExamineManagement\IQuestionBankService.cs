﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IQuestionBankService
    {
        /// <summary>
        ///  获取考核题库数据
        /// </summary>
        /// <param name="employeeID">当前会话登录人</param>
        /// <returns></returns>
        Task<List<QuestionBankView>> GetQuestionBankList(string employeeID);
        /// <summary>
        /// 保存维护考核题库数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveQuestionBank(QuestionBankView view);
        /// <summary>
        /// 删除考核题库维护数据
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<string> DeleteQuestionBank(string questionBankID, string employeeID);

        /// <summary>
        /// 获取题库key-value数据
        /// </summary>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetQuestionBankDictAsync();

        /// <summary>
        /// 获取题库下拉框数据内容
        /// </summary>
        /// <param name="isPractical"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetQuestionBankSelectList(bool? isPractical);

        /// <summary>
        /// 获取题库级联选项数据
        /// </summary>
        /// <param name="isPractical">是否过滤掉实操类</param>
        /// <param name="employeeID">当前会话登录人</param>
        /// <returns></returns>
        Task<List<QuestionBankCascadeView>> GetQuestionBankCascaderAsync(bool? isPractical, string employeeID);

        #region 题目
        /// <summary>
        /// 根据题库ID获取题库内容
        /// </summary>
        /// <param name="questionBankID">题库ID</param>
        /// <returns>题目列表</returns>
        Task<List<ExaminationQuestionView>> GetQuestionsByBankIDAsync(string questionBankID);

        /// <summary>
        /// 根据题目ID删除题目
        /// </summary>
        /// <param name="questionID">题目ID</param>
        /// <param name="employeeID">操作人ID</param>
        /// <returns>是否成功删除</returns>
        Task<bool> DeleteQuestionAsync(int questionID, string employeeID);

        /// <summary>
        /// 将所有题目导入到题库
        /// </summary>
        /// <param name="examinationQuestionOuterView">前端导入的题目题库等信息View</param>
        /// <param name="employeeID">操作人ID</param>
        /// <returns>是否成功导入</returns>
        Task<bool> ImportQuestionsAsync(ExaminationQuestionOuterView examinationQuestionOuterView, string employeeID);
        /// <summary>
        /// 保存题目和题目明细数据
        /// </summary>
        /// <param name="questionSaveParamsView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SaveQuestionData(ExaminationQuestionView questionSaveParamsView, string employeeID);
       
        #endregion
        /// <summary>
        /// 根据题库ID集合获取题目下拉框数据
        /// </summary>
        /// <param name="questionBankIDs"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetQuestionOptionList(List<string> questionBankIDs);
       
        /// <summary>
        /// 根据题库ID集合获取每个题库中各个题型的题目数量
        /// </summary>
        /// <param name="questionBankIDs">题库ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string, Dictionary<int, int>>> GetQuestionTypeCount(List<string> questionBankIDs);
        /// <summary>
        /// 更新题库排序
        /// </summary>
        /// <param name="bankSortView">排序信息</param>
        /// <returns></returns>
        Task<bool> UpdateBankSort(List<BankSortView> bankSortView);
        /// <summary>
        /// 更新题目排序
        /// </summary>
        /// <param name="questionSortView">排序信息</param>
        /// <returns></returns>
        Task<bool> UpdateQuestionSort(List<BankSortView> questionSortView);
        /// <summary>
        /// 克隆题库
        /// </summary>
        /// <param name="bankId">题库ID</param>
        /// <param name="bankIds">被复制题库ID集合</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        Task<bool> CloneQuestionBankAsync(string bankId, List<string> bankIds, string employeeID);

        /// <summary>
        /// 复制题库
        /// </summary>
        /// <param name="questionBank">前端View</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        Task<bool> CopyQuestionBankAsync(QuestionBankView questionBank, string employeeID);
    }
}
