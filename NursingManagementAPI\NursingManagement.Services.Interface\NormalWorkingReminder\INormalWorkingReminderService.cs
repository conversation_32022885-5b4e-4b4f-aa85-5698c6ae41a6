﻿﻿using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.Services.Interface.NormalWorkingReminder
{
    /// <summary>
    /// 常态工作控制提醒服务接口
    /// </summary>
    public interface INormalWorkingReminderService
    {
        /// <summary>
        /// 执行常态工作控制提醒
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        Task<ReminderResultView> ExecuteReminderAsync(ReminderRequestView requestView);


    }
}
