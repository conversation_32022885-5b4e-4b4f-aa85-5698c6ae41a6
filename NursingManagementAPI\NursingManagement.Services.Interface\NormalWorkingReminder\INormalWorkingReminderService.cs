﻿﻿using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.Services.Interface.NormalWorkingReminder
{
    /// <summary>
    /// 常态工作控制提醒服务接口
    /// </summary>
    public interface INormalWorkingReminderService
    {
        /// <summary>
        /// 执行常态工作控制提醒
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        Task<ReminderResultView> ExecuteReminderAsync(ReminderRequestView requestView);

        /// <summary>
        /// 执行三天未整改提醒（提醒护士长）
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <param name="forceRemind">是否强制提醒</param>
        /// <returns>提醒执行结果</returns>
        Task<ReminderResultView> ExecuteThreeDayReminderAsync(string hospitalID, int? departmentID = null, bool forceRemind = false);

        /// <summary>
        /// 执行六天未整改提醒（提醒片区主任）
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <param name="forceRemind">是否强制提醒</param>
        /// <returns>提醒执行结果</returns>
        Task<ReminderResultView> ExecuteSixDayReminderAsync(string hospitalID, int? departmentID = null, bool forceRemind = false);

        /// <summary>
        /// 查询未整改问题列表
        /// </summary>
        /// <param name="requestView">查询请求参数</param>
        /// <returns>未整改问题列表</returns>
        Task<List<ReminderProblemView>> QueryUnrectifiedProblemsAsync(QueryUnrectifiedProblemsRequestView requestView);

        /// <summary>
        /// 获取需要三天提醒的问题列表
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>需要提醒的问题列表</returns>
        Task<List<ReminderProblemView>> GetProblemsNeedThreeDayReminderAsync(string hospitalID, int? departmentID = null);

        /// <summary>
        /// 获取需要六天提醒的问题列表
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>需要提醒的问题列表</returns>
        Task<List<ReminderProblemView>> GetProblemsNeedSixDayReminderAsync(string hospitalID, int? departmentID = null);

        /// <summary>
        /// 发送提醒消息给护士长
        /// </summary>
        /// <param name="problems">需要提醒的问题列表</param>
        /// <returns>提醒详情列表</returns>
        Task<List<ReminderDetailView>> SendReminderToHeadNursesAsync(List<ReminderProblemView> problems);

        /// <summary>
        /// 发送提醒消息给片区主任
        /// </summary>
        /// <param name="problems">需要提醒的问题列表</param>
        /// <returns>提醒详情列表</returns>
        Task<List<ReminderDetailView>> SendReminderToDistrictDirectorsAsync(List<ReminderProblemView> problems);
    }
}
