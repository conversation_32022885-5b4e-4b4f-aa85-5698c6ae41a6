﻿﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NursingManagement.Services.Interface.NormalWorkingReminder;
using NursingManagement.ViewModels.NormalWorkingReminder;
using Xunit;

namespace NursingManagement.UnitTest
{
    /// <summary>
    /// 常态工作控制提醒服务单元测试
    /// </summary>
    public class NormalWorkingReminderServiceTest : IClassFixture<TestFixture>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly INormalWorkingReminderService _normalWorkingReminderService;

        public NormalWorkingReminderServiceTest(TestFixture fixture)
        {
            _serviceProvider = fixture.ServiceProvider;
            _normalWorkingReminderService = _serviceProvider.GetRequiredService<INormalWorkingReminderService>();
        }

        /// <summary>
        /// 测试查询未整改问题 - 参数验证
        /// </summary>
        [Fact]
        public async Task QueryUnrectifiedProblemsAsync_WithNullRequest_ShouldReturnEmptyList()
        {
            // Arrange
            QueryUnrectifiedProblemsRequestView request = null;

            // Act
            var result = await _normalWorkingReminderService.QueryUnrectifiedProblemsAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        /// <summary>
        /// 测试查询未整改问题 - 空医院ID
        /// </summary>
        [Fact]
        public async Task QueryUnrectifiedProblemsAsync_WithEmptyHospitalID_ShouldReturnEmptyList()
        {
            // Arrange
            var request = new QueryUnrectifiedProblemsRequestView
            {
                HospitalID = "",
                MinUnrectifiedDays = 3
            };

            // Act
            var result = await _normalWorkingReminderService.QueryUnrectifiedProblemsAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        /// <summary>
        /// 测试查询未整改问题 - 正常参数
        /// </summary>
        [Fact]
        public async Task QueryUnrectifiedProblemsAsync_WithValidRequest_ShouldReturnList()
        {
            // Arrange
            var request = new QueryUnrectifiedProblemsRequestView
            {
                HospitalID = "1",
                MinUnrectifiedDays = 3,
                MaxUnrectifiedDays = 10
            };

            // Act
            var result = await _normalWorkingReminderService.QueryUnrectifiedProblemsAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证具体数量，因为依赖于数据库中的实际数据
        }



        /// <summary>
        /// 测试执行提醒 - 参数验证
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithNullRequest_ShouldReturnFailure()
        {
            // Arrange
            ReminderRequestView request = null;

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("参数不能为空", result.Message);
        }

        /// <summary>
        /// 测试执行提醒 - 空医院ID
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithEmptyHospitalID_ShouldReturnFailure()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "",
                ReminderType = 3
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("医院ID不能为空", result.Message);
        }

        /// <summary>
        /// 测试执行提醒 - 不支持的提醒类型
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithInvalidReminderType_ShouldReturnFailure()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "1",
                ReminderType = 999 // 不支持的类型
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("不支持的提醒类型", result.Message);
        }

        /// <summary>
        /// 测试执行三天提醒
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithThreeDayType_ShouldReturnResult()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "1",
                ReminderType = 3
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证Success，因为可能没有需要提醒的问题
        }

        /// <summary>
        /// 测试执行六天提醒
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithSixDayType_ShouldReturnResult()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = "1",
                ReminderType = 6
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证Success，因为可能没有需要提醒的问题
        }

        /// <summary>
        /// 测试HospitalID为空时从配置获取
        /// </summary>
        [Fact]
        public async Task ExecuteReminderAsync_WithEmptyHospitalID_ShouldUseConfigValue()
        {
            // Arrange
            var request = new ReminderRequestView
            {
                HospitalID = null, // 空值，应该从配置获取
                ReminderType = 3
            };

            // Act
            var result = await _normalWorkingReminderService.ExecuteReminderAsync(request);

            // Assert
            Assert.NotNull(result);
            // 注意：这里不验证Success，因为依赖于配置中是否有HospitalID
        }
    }
}
