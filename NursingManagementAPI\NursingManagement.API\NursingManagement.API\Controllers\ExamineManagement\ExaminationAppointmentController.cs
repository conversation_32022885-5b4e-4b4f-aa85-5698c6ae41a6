﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.API;
using NursingManagement.Common;
using NursingManagement.Services;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Controllers
{
    /// <summary>
    /// 考核预约控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/ExaminationAppointment")]
    [EnableCors("any")]
    public class ExaminationAppointmentController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IExaminationAppointmentService _examinationAppointmentService;
        /// <summary>
        /// 构造注入
        /// </summary>
        /// <param name="session"></param>
        /// <param name="examinationAppointmentService"></param>
        public ExaminationAppointmentController(
            ISessionService session,
            IExaminationAppointmentService examinationAppointmentService)
        {
            _session = session;
            _examinationAppointmentService = examinationAppointmentService;
        }


        #region 查询预约
        /// <summary>
        /// 获取可以可以预约的监考日
        /// </summary>
        /// <param name="examinationRecordID">考核计划ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAvailableAppointmentList")]
        public async Task<IActionResult> GetAvailableAppointmentList(string examinationRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            // 构建查询条件
            result.Data = await _examinationAppointmentService.GetAvailableAppointmentList(examinationRecordID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据考核计划ID和预约人ID获取考核预约记录
        /// </summary>
        /// <param name="examinationRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAppointmentByExaminationRecordID")]
        public async Task<IActionResult> GetAppointmentByExaminationRecordID(string examinationRecordID, string employeeID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationAppointmentService.GetAppointmentByExaminationRecordID(examinationRecordID, employeeID);
            return result.ToJson();
        }
        #endregion

        #region 新增考核预约
        /// <summary>
        /// 新增考核预约记录
        /// </summary>
        /// <param name="appointmentView">完整的预约信息实体</param>
        /// <returns>新增记录的唯一标识ID</returns>
        [HttpPost]
        [Route("SaveExaminationAppointment")]
        public async Task<IActionResult> SaveExaminationAppointment([FromBody] ExaminationAppointmentView appointmentView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationAppointmentService.SaveExaminationAppointment(appointmentView, session.EmployeeID);
            return result.ToJson();
        }
        #endregion

        #region 取消预约并记录原因
        /// <summary>
        /// 取消指定预约，并记录取消原因
        /// </summary>
        /// <param name="cancelAppointmentParamView">取消参数</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("CancelAppointment")]
        public async Task<IActionResult> CancelAppointment([FromBody] CancelAppointmentParamView cancelAppointmentParamView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            cancelAppointmentParamView.EmployeeID ??= session.EmployeeID;
            result.Data = await _examinationAppointmentService.CancelAppointment(cancelAppointmentParamView);
            return result.ToJson();
        }
        #endregion

        #region 获取考核预约信息
        /// <summary>
        /// 获取考核预约信息
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAppointmentList")]
        public async Task<IActionResult> GetAppointmentList(DateTime startDate, DateTime endDate)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationAppointmentService.GetAppointmentList(startDate, endDate);
            return result.ToJson();
        }
        #endregion
    }
}