﻿
using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 部门岗位能级对照
    /// </summary>
    public interface IDepartmentPostToCapabilityLevelRepository: ICacheRepository
    {
        /// <summary>
        /// 根据科室ID获取岗位能级对照记录
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        Task<List<DepartmentPostToCapabilityLevelInfo>> GetByDepartmentIDAsync(int departmentID);

        /// <summary>
        /// 根据科室ID和岗位ID获取岗位能级对照记录
        /// 如果PostID为空的话，获取到当前科室所有的数据
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="postID">岗位ID</param>
        /// <returns></returns>
        Task<List<DepartmentPostToCapabilityLevelInfo>> GetByDepartmentIDAndPostIDAsync(int departmentID, int? postID);
        /// <summary>
        /// 根据联合主键，获取唯一的记录
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="postID">岗位ID</param>
        /// <param name="capabilityLevelID">能级ID</param>
        /// <returns></returns>
        Task<DepartmentPostToCapabilityLevelInfo> GetRecordByAllKeysAsync(int departmentID, int postID, int capabilityLevelID);
        /// <summary>
        /// 根据主键获取能级对照记录
        /// </summary>
        /// <param name="departmentPostToCapabilityLevelIDs">主键ID</param>
        /// <returns></returns>
        Task<List<DepartmentPostToCapabilityLevelInfo>> GetByIDsAsync( List<int> departmentPostToCapabilityLevelIDs);
        /// <summary>
        /// 根据主键获取能级对照记录
        /// </summary>
        /// <param name="departmentPostToCapabilityLevelID">主键ID</param>
        /// <returns></returns>
        Task<DepartmentPostToCapabilityLevelInfo> GetByIDAsync(int departmentPostToCapabilityLevelID);
    }
}
