﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class MessageRecordRepository : IMessageRecordRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public MessageRecordRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 根据ID异步获取消息记录
        /// </summary>
        /// <param name="id">消息ID</param>
        /// <returns>消息记录</returns>
        public async Task<MessageRecordInfo> GetRecordByIdAsync(string id)
        {
            return await _dbContext.MessageRecordInfos.FirstOrDefaultAsync(m => m.MessageRecordID == id && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 获取消息集合
        /// </summary>
        /// <param name="messageType">null，获取所有消息，否则获取指定消息类型</param>
        /// <returns></returns>
        public async Task<List<MessageRecordInfo>> GetListAsNoTrack(string messageType)
        {
            return await _dbContext.MessageRecordInfos.Where(m => m.DeleteFlag != "*")
                .IfWhere(!string.IsNullOrEmpty(messageType), m => m.MessageType == messageType)
                .Select(m => new MessageRecordInfo
                {
                    MessageRecordID = m.MessageRecordID,
                    MessageTitle = m.MessageTitle,
                    MessageType = m.MessageType,
                    TopDays = m.TopDays,
                    MessageStatus = m.MessageStatus,
                    PublishTime = m.PublishTime,
                    AddDateTime = m.AddDateTime,
                    AddEmployeeID = m.AddEmployeeID,
                    ModifyDateTime = m.ModifyDateTime,
                    ModifyEmployeeID = m.ModifyEmployeeID,
                }).ToListAsync();
        }
        /// <summary>
        /// 获取最后一条消息记录
        /// </summary>
        /// <param name="messageRecordIDs"></param>
        /// <param name="messageRecordType"></param>
        /// <returns></returns>
        public async Task<MessageRecordInfo> GetLastRecordByIDsAndTypeAsync(IEnumerable<string> messageRecordIDs, string messageRecordType)
        {
            return await _dbContext.MessageRecordInfos.Where(m => messageRecordIDs.Contains(m.MessageRecordID) && m.MessageType == messageRecordType && m.DeleteFlag != "*")
                .OrderByDescending(m => m.AddDateTime).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取消息内容
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <returns></returns>
        public async Task<string> GetContentByID(string messageRecordID)
        {
            return await _dbContext.MessageRecordInfos.Where(m => m.MessageRecordID == messageRecordID && m.DeleteFlag != "*").Select(m => m.MessageContent).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取从开始日期到现在的消息集合
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <returns></returns>
        public async Task<List<MessageRecordInfo>> GetListByStartDateAsNoTrack(DateTime startDate)
        {
            return await _dbContext.MessageRecordInfos.Where(m => m.DeleteFlag != "*" && m.PublishTime.HasValue && m.PublishTime.Value >= startDate)
                .Select(m => new MessageRecordInfo
                {
                    MessageRecordID = m.MessageRecordID,
                    MessageType = m.MessageType,
                    PublishTime = m.PublishTime,
                    TopDays = m.TopDays,
                    MessageTitle = m.MessageTitle,
                    MessageContent = m.MessageContent
                }).ToListAsync();
        }
        public async Task<MessageRecordView> GetLastSystemUpdateRecord()
        {
            return await _dbContext.MessageRecordInfos.Where(m => m.MessageType == "99" && m.MessageStatus == "1" && m.DeleteFlag != "*")
                .OrderByDescending(m => m.PublishTime)
                .Select(m => new MessageRecordView
                {
                    MessageRecordID = m.MessageRecordID,
                    MessageTitle = m.MessageTitle,
                    MessageType = m.MessageType,
                    MessageContent = m.MessageContent
                }).FirstOrDefaultAsync();
        }
    }
}
