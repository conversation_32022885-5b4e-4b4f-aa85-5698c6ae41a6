﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ConditionMainRepository : IConditionMainRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;
        public ConditionMainRepository
            (
            NursingManagementDbContext nursingManagementDbContext
            )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }
        /// <summary>
        /// 根据来源ID集合,来源类别获取数据
        /// </summary>
        /// <param name="sourceIDs"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        public async Task<List<ConditionMainInfo>> GetListBySourceIDs(List<string> sourceIDs, string sourceType)
        {
            return await _nursingManagementDbContext.ConditionMainInfos.Where(m => sourceIDs.Any(n => m.SourceID == n) && m.SourceType.Contains(sourceType) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据来源ID,来源类别获取数据
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        public async Task<ConditionMainInfo> GetDataBySourceID(string sourceID, string sourceType)
        {
            return await _nursingManagementDbContext.ConditionMainInfos.FirstOrDefaultAsync(m => sourceID == m.SourceID && m.SourceType == sourceType && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        public async Task<List<ConditionMainInfo>> GetListByMainIDs(List<string> mainIDs)
        {
            return await _nursingManagementDbContext.ConditionMainInfos.Where(m => mainIDs.Any(n => m.ConditionMainID == n) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据来源类别获取数据
        /// </summary>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        public async Task<List<ConditionMainInfo>> GetListBySourceType(string sourceType)
        {
            return await _nursingManagementDbContext.ConditionMainInfos.Where(m => m.SourceType.Contains(sourceType) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        ///  根据mainID获取数据
        /// </summary>
        /// <param name="mainD"></param>
        /// <returns></returns>
        public async Task<ConditionMainInfo> GetDateByMainID(string mainD)
        {
            return await _nursingManagementDbContext.ConditionMainInfos.FirstOrDefaultAsync(m => mainD == m.ConditionMainID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据来源获取对应的规则主记录
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        public async Task<List<ConditionMainInfo>> GetMainIDsBySourceID(string sourceID)
        {
            return await _nursingManagementDbContext.ConditionMainInfos.
                Where(m => sourceID == m.SourceID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据分类下的明细值集合获取数据
        /// </summary>
        /// <param name="groupTypeValues"></param>
        /// <param name="groupType"></param>
        /// <returns></returns>
        public async Task<List<string>> GetSourceIDsByGroupTypeValue(List<string> groupTypeValues,string groupType)
        { 
            return await _nursingManagementDbContext.ConditionMainInfos.Where(m => m.GroupType == groupType && groupTypeValues.Any(n => n == m.GroupTypeValue))
                .Select(m => m.SourceID).Distinct().ToListAsync();
        }
        /// <summary>
        /// 根据来源ID,来源类别判断规则是否存在
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        public async Task<bool> ExistBySource(string sourceID, string sourceType)
        {
            var existCount = await _nursingManagementDbContext.ConditionMainInfos.
                CountAsync(m => sourceID == m.SourceID && m.SourceType == sourceType && m.DeleteFlag != "*");
            return existCount > 0;
        }
    }
}
