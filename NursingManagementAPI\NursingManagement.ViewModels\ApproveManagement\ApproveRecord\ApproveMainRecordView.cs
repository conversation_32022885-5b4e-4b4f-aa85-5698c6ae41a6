﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    public class ApproveRecordView : MutiModifyInfo
    {
        /// <summary>
        /// 审批主记录唯一码，Guid
        /// </summary>
        public string ApproveRecordID { get; set; }

        /// <summary>
        /// 审批类型
        /// </summary>
        public string ApproveCategoryName { get; set; }

        /// <summary>
        /// 审批状态码
        /// </summary>
        public string ApproveStatusCode { get; set; }
        /// <summary>
        /// 审批状态
        /// </summary>
        public string ApproveStatus { get; set; }

        /// <summary>
        /// 审批终止，或者完成时间
        /// </summary>
        public DateTime? CompleteDateTime { get; set; }

        /// <summary>
        /// 新增员工姓名
        /// </summary>
        public string AddEmployeeName { get; set; }

        /// <summary>
        /// 发起审批的业务内容
        /// </summary>
        public string Content { get;set; }
    }
}

