﻿using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 消息发布到部门
    /// </summary>
    public interface IMessageToDepartmentRepository
    {
        /// <summary>
        /// 根据消息序号获取该消息发布到的部门
        /// </summary>
        /// <param name="messageRecordID"></param>
        /// <returns></returns>
        Task<List<MessageToDepartmentInfo>> GetMessageToDepartmentByMessageID(string messageRecordID);
        /// <summary>
        /// 根据部门获取发布到该部门的数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<MessageToDepartmentInfo>> GetMessageToDepartmentByDepartmentID(int departmentID);
        /// <summary>
        /// 根据部门集合获取发布到的部门集合
        /// </summary>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        Task<List<string>> GetMessageRecordIDsByDepartmentIDs(List<int> departmentIDs);
    }
}
