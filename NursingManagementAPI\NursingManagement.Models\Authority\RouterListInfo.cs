using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 前端画面路由表
    /// </summary>
    [Serializable]
    [Table("RouterList")]
    public class RouterListInfo : MutiModifyInfo
    {

        /// <summary>
        /// 路由序号
        /// </summary>
        public int RouterListID { get; set; }

        /// <summary>
        /// 客户端类型 （1:PC , 2:移动端）
        /// </summary>
        public int ClientType { get; set; }

        /// <summary>
        /// 路由地址
        /// </summary>
        [Column(TypeName = "verchar(200)")]
        public string Path { get; set; }

        /// <summary>
        /// 路由名称
        /// </summary>
        [Column(TypeName = "verchar(50)")]
        public string Name { get; set; }

        /// <summary>
        /// 路由组件(前端画面地址)
        /// </summary>
        [Column(TypeName = "verchar(50)")]
        public string Component { get; set; }

        /// <summary>
        /// 是否需要身份验证(前端路由meta需要的参数)
        /// </summary>
        public bool? Auth { get; set; }

        /// <summary>
        /// 页面是否缓存(前端路由meta需要的参数)若为1则前端路由meta的refreshFlag为true
        /// </summary>
        public bool? KeepAlive { get; set; }

        /// <summary>
        /// 空表示为1阶(根据此组队组前端路由meta中的isParent及parentPath)
        /// </summary>
        public int? ParentID { get; set; }

        /// <summary>
        /// 阶层
        /// </summary>
        public int RouterLevel { get; set; }

        /// <summary>
        /// 是否有顶部菜单
        /// </summary>
        public bool? HasTopMenu { get; set; }

        /// <summary>
        /// 路由说明
        /// </summary>
        [Column(TypeName = "nverchar(100)")]
        public string Content { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
