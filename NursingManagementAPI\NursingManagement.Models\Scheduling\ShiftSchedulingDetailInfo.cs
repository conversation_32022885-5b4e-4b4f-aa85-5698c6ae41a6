using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 排班明细表
    /// </summary>
    [Serializable]
    [Table("ShiftSchedulingDetail")]
    public class ShiftSchedulingDetailInfo : MutiModifyInfo
    {

        /// <summary>
        /// 排班明细记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ShiftSchedulingDetailID { get; set; }

        /// <summary>
        /// 排班主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ShiftSchedulingRecordID { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 部门岗位编号，DepartmentPost表的主键
        /// </summary>
        public int DepartmentPostID { get; set; }

        /// <summary>
        /// 排班人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime SchedulingDate { get; set; }

        /// <summary>
        /// 午别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string NoonType { get; set; }

        /// <summary>
        /// 调班记录ID，AdjustScheduleRecord表的主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AdjustScheduleRecordID { get; set; }
    }
}
