﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划执行项目字典
    /// </summary>
    public class InterventionListRepository : IInterventionListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        private readonly IOptions<SystemConfig> _config;

        public InterventionListRepository(
            NursingManagementDbContext nursingManagementDbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService,
            IOptions<SystemConfig> options)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
            _config = options;
        }
        /// <summary>
        /// 获取年度计划项目字典
        /// </summary>
        /// <param name="interventionID">执行项目字典序号</param>
        /// <returns></returns>
        public async Task<InterventionListInfo> GetInfoByIDNoCache(int interventionID)
        {
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(GetCacheType());
            return  await _nursingManagementDbContext.InterventionListInfos.Where(m =>m.InterventionID == interventionID
            && m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取多条数据
        /// </summary>
        /// <param name="interventionIDs">字典ID集合</param>
        /// <returns></returns>
        public async Task<List<InterventionListInfo>> GetInfosByIDsNoCache(List<int> interventionIDs)
        {
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(GetCacheType());
            return await _nursingManagementDbContext.InterventionListInfos.Where(m => interventionIDs.Contains(m.InterventionID)
            && m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取最大项目ID
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxID()
        {
            var maxID = await _nursingManagementDbContext.InterventionListInfos
                .Where(m => _config.Value.HospitalID == m.HospitalID && m.Language == _config.Value.Language)
                .Select(m => m.InterventionID).DefaultIfEmpty().MaxAsync();
            return maxID;
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var data = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.InterventionListInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return data;
        }

        public string GetCacheType()
        {
            return CacheType.InterventionList.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
           await _redisService.Remove(key);
            
        }
    }
}
