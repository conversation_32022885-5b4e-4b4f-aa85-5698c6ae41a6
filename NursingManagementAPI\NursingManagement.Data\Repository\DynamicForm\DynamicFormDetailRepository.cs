﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;


namespace NursingManagement.Data.Repository
{
    public class DynamicFormDetailRepository : IDynamicFormDetailRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public DynamicFormDetailRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<DynamicFormDetailInfo>> GetFormDetailListByFormRecordID(string dynamicFormRecordID)
        {
            dynamic cacheQuery = new DynamicDictionary();
            cacheQuery.DynamicFormRecordID = dynamicFormRecordID;
            return await GetCacheAsync(cacheQuery) as List<DynamicFormDetailInfo>;
        }
        /// <summary>
        /// 根据FormRecordID获取FormDetailID集合
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetFormDetailIDByFormRecordID(string dynamicFormRecordID)
        {
            dynamic cacheQuery = new DynamicDictionary();
            cacheQuery.DynamicFormRecordID = dynamicFormRecordID;
            var datas = await GetCacheAsync(cacheQuery) as List<DynamicFormDetailInfo>;
            return datas.Where(m => m.DynamicFormRecordID == dynamicFormRecordID).Select(m => m.DynamicFormDetailID).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var dict = new Dictionary<string, object>();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            if (query != null)
            {
                if (query is DynamicDictionary queryTemp && queryTemp.HasMember("DynamicFormRecordID"))
                {
                    dict = new Dictionary<string, object>
                    {
                        { "DynamicFormRecordID",query.DynamicFormRecordID }
                    };
                    key = key + "_" + query.DynamicFormRecordID;
                }
                //缓存预加载
                if (query is DynamicDictionary query1 && query1.HasMember("PreloadingFlag"))
                {
                    return await GetCachePreloading(key, dict);
                }
            }
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await GetDataBaseListData(dict);
                return result;

            });
            return datas;
        }

        /// 缓存预热
        /// </summary>
        /// <param name="key"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<bool> GetCachePreloading(string key, Dictionary<string, object> dict = null)
        {
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var recordsCodes = await _nursingManagementDbContext.DynamicFormRecordInfos.Where(m => m.DeleteFlag != "*").Select(m => m.DynamicFormRecordID).Distinct().ToListAsync();
            foreach (var item in recordsCodes)
            {
                if (string.IsNullOrEmpty(item))
                {
                    continue;
                }
                dict = new Dictionary<string, object>
                    {
                        { "DynamicFormRecordID",item }
                    };
                var keyTemp = key + "_" + item;
                var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
                {
                    var result = await GetDataBaseListData(dict); ;
                    return result;

                });
            }
            return true;
        }

        /// <summary>
        /// 从表中获取数据
        /// </summary>
        /// <param name="dict"></param>
        /// <returns></returns>
        private async Task<List<DynamicFormDetailInfo>> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("DynamicFormRecordID", out var dynamicFormRecordID);
            if (dynamicFormRecordID == null)
            {
                return await _nursingManagementDbContext.DynamicFormDetailInfos.Where(m => m.DeleteFlag != "*").Select(m =>
                new DynamicFormDetailInfo
                {
                    DynamicFormDetailID = m.DynamicFormDetailID,
                    DynamicFormRecordID = m.DynamicFormRecordID,
                    ParentID = m.ParentID,
                    ItemID = m.ItemID,
                    ItemSourceType = m.ItemSourceType,
                    ComponentListID = m.ComponentListID,
                    Sort = m.Sort,
                    AddDateTime = m.AddDateTime,
                    AddEmployeeID = m.AddEmployeeID
                }).ToListAsync();
            }
            else
            {
                return await _nursingManagementDbContext.DynamicFormDetailInfos.Where(m => m.DynamicFormRecordID == dynamicFormRecordID.ToString()
                && m.DeleteFlag != "*").Select(m =>
                new DynamicFormDetailInfo
                {
                    DynamicFormDetailID = m.DynamicFormDetailID,
                    DynamicFormRecordID = m.DynamicFormRecordID,
                    ParentID = m.ParentID,
                    ItemID = m.ItemID,
                    ItemSourceType = m.ItemSourceType,
                    ComponentListID = m.ComponentListID,
                    Sort = m.Sort,
                    AddDateTime = m.AddDateTime,
                    AddEmployeeID = m.AddEmployeeID
                }).ToListAsync(); ;
            }
        }
        public async Task<List<DynamicFormDetailInfo>> GetAllDynamicFormDetailByTest()
        {
            return await _nursingManagementDbContext.DynamicFormDetailInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public async Task UpdateCacheByQuery(Dictionary<string, object> query)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var cacheName = "";
            if (query != null)
            {
                cacheName = GetDictionaryValue(query, "CacheName");
            }
            if (!string.IsNullOrEmpty(cacheName))
            {
                key = key +"_"+ cacheName;
                await _redisService.RemoveCacheByNameAsync(key, hospitalID, language);
            }
        }
        /// <summary>
        /// 获取字典值
        /// </summary>
        /// <param name="query"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        private static string GetDictionaryValue(Dictionary<string, object> query, string key)
        {
            if (query.ContainsKey(key))
            {
                return query[key].ToString();
            }
            return "";
        }
        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.DynamicFormDetail.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 根据主记录ID和组件类型ID获取数据
        /// </summary>
        /// <param name="dynamicFormRecordID">动态表单ID</param>
        /// <param name="componentListID">组件ID</param>
        /// <param name="orderBy">排序规则</param>
        /// <returns></returns>
        /// 
        public async Task<List<string>> GetFormItemIDByFormRecordIDAndComponentListID(string dynamicFormRecordID,int componentListID, Func<IQueryable<DynamicFormDetailInfo>, IOrderedQueryable<DynamicFormDetailInfo>> orderBy = null)
        {
            var query = _nursingManagementDbContext.DynamicFormDetailInfos.Where(m => m.DeleteFlag != "*" && m.DynamicFormRecordID == dynamicFormRecordID && m.ComponentListID == componentListID);
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            return await query.Select(m => m.ItemID).ToListAsync();
        }
        /// <summary>
        /// 根据FormRecordID获取FormDetailID集合
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <param name="attributeID"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetFormSettingsByFormRecordID(string dynamicFormRecordID, int attributeID)
        {
            var query =
               await ((from detail in _nursingManagementDbContext.DynamicFormDetailInfos
                    .Where(m => m.DeleteFlag != "*" && m.DynamicFormRecordID == dynamicFormRecordID)
                       join attribute in _nursingManagementDbContext.DynamicFormDetailAttributeInfos
                           .Where(m => m.DeleteFlag != "*" && m.ComponentAttributeID == attributeID && !string.IsNullOrEmpty(m.AttributeValue))
                           on detail.DynamicFormDetailID equals attribute.DynamicFormDetailID into attributes
                       from attribute in attributes.DefaultIfEmpty()
                       group new { detail.ItemID, attribute.AttributeValue } by detail.ItemID into g
                       select new
                       {
                           g.Key,
                           MaxValue = g.Max(x => x.AttributeValue)
                       })).ToDictionaryAsync(m => m.Key, m => m.MaxValue);
            return query;
        }
        /// <summary>
        /// 根据主记录ID集合模板明细数据
        /// </summary>
        /// <param name="dynamicFormRecordID">模板主记录ID</param>
        /// <returns></returns>
        public async Task<List<DynamicFormView>> GetDynamicFormDetailViewByFormRecordID(string dynamicFormRecordID)
        {
            dynamic cacheQuery = new DynamicDictionary();
            cacheQuery.DynamicFormRecordID = dynamicFormRecordID;
            var datas = await GetCacheAsync(cacheQuery) as List<DynamicFormDetailInfo>;
            return datas.Where(m => m.DynamicFormRecordID == dynamicFormRecordID).Select(m => new DynamicFormView
            {
                ItemID = m.ItemID,
                ComponentListID = m.ComponentListID
            }).ToList();
        }
        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="dynamicFormRecordIDs"></param>
        /// <returns></returns>
        public async Task<List<DynamicFormDetailInfo>> GetFormDetailListByRecordIDList(List<string> dynamicFormRecordIDs)
        {

            var datas = new List<DynamicFormDetailInfo>();
            foreach (var item in dynamicFormRecordIDs)
            {
                dynamic cacheQuery = new DynamicDictionary();
                cacheQuery.DynamicFormRecordID = item;
                var datasTemp = await GetCacheAsync(cacheQuery) as List<DynamicFormDetailInfo>;
                datas.AddRange(datasTemp);
            }
            return datas;
        }
        /// <summary>
        /// 根据itemID获取数据
        /// </summary>
        /// <param name="itemIDs"></param>
        /// <param name="itemSourceType"></param>
        /// <returns></returns>
        public async Task<List<DynamicFormDetailInfo>> GetFormDetailListByItemIDList(List<string> itemIDs, string itemSourceType)
        {
            var datas = new List<DynamicFormDetailInfo>();
            foreach (var item in itemIDs)
            {
                dynamic cacheQuery = new DynamicDictionary();
                cacheQuery.DynamicFormRecordID = item;
                var datasTemp = await GetCacheAsync(cacheQuery) as List<DynamicFormDetailInfo>;
                datas.AddRange(datasTemp);
            }
            return datas.Where(m => itemIDs.Any(n => n == m.ItemID) && m.ItemSourceType == itemSourceType).ToList();
        }

        /// <summary>
        /// 根据主记录ID查询ItemIDs
        /// </summary>
        /// <param name="recordIDs">动态表单主记录</param>
        /// <returns></returns>
        public async Task<List<Tuple<string, string>>> GetItemIDsByFormRecordID(List<string> recordIDs)
        {
            return await _nursingManagementDbContext.DynamicFormDetailInfos.Where(m => recordIDs.Contains(m.DynamicFormRecordID) && m.DeleteFlag != "*")
                .Select(m => Tuple.Create(m.DynamicFormRecordID, m.ItemID))
                .ToListAsync();
        }
    }
}
