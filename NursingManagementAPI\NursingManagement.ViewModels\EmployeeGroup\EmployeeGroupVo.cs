﻿using static NursingManagement.ViewModels.EmployeeGroup.EmployeeGroupVo;

namespace NursingManagement.ViewModels.EmployeeGroup;

/// <summary>
/// 用户组视图模型
/// </summary>
/// <param name="GroupID">主键</param>
/// <param name="GroupName">组名</param>
/// <param name="Members">成员</param>
/// <param name="ModifyEmployeeName">维护人</param>
/// <param name="ModifyDateTime">维护时间</param>
public record EmployeeGroupVo(int GroupID, string GroupName, MemberInformation[] Members, string ModifyEmployeeName, DateTime ModifyDateTime)
{
    public record MemberInformation(string EmployeeID, string EmployeeName, string DepartmentName);
};
