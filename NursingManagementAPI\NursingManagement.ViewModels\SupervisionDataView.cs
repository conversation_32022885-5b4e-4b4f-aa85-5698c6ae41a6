﻿namespace NursingManagement.ViewModels
{
    public class SupervisionDataView
    {
        /// <summary>
        /// 住院病人序号
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病历号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 病区
        /// </summary>
        public int StationID { get; set; }
        /// <summary>
        /// 病人姓名
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 床号
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public string Age { get; set; }
        /// <summary>
        /// 入院日期
        /// </summary>
        public DateTime AdmissionDateTime { get; set; }
        /// <summary>
        /// 主责护士编号
        /// </summary>
        public string NurseEmployeeID { get; set; }
        /// <summary>
        /// 主责护士姓名
        /// </summary>
        public string NurseName { get; set; }
        /// <summary>
        /// 相关风险等级
        /// </summary>
        public string RiskContent { get; set; }
        /// <summary>
        /// 相关风险评估时间
        /// </summary>
        public DateTime? RiskAssessTime { get; set; }
        /// <summary>
        /// 风险等级颜色
        /// </summary>
        public string RiskColor { get; set; }
        /// <summary>
        /// 导管ID
        /// </summary>
        public int TubeID { get; set; }
        /// <summary>
        /// 导管类型
        /// </summary>
        public string TubeType { get; set; }
        /// <summary>
        /// 导管名称
        /// </summary>
        public string TubeName { get; set; }
        /// <summary>
        /// 导管置管时间
        /// </summary>
        public DateTime TubeStartDateTime { get; set; }
        /// <summary>
        /// 来源类型
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 状况来源ID
        /// </summary>
        public string SourceID { get; set; }
    }
}
