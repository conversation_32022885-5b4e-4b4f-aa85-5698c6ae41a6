namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 人员培训主键以及班长记录 ViewModel
    /// </summary>
    public class TrainingLearnerParamView
    {
        /// <summary>
        /// 人员培训记录ID
        /// </summary>
        public string TrainingLearnerID { get; set; }

        /// <summary>
        /// 培训人员工号ID
        /// </summary>
        public string TrainEmployeeID { get; set; }

        /// <summary>
        /// 本次培训的班长标志
        /// </summary>
        public bool MonitorFlag { get; set; }
    }

    /// <summary>
    /// 人员培训记录查询条件
    /// </summary>
    public class TraineeQueryParamsView
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public List<int> DepartmentIDs { get; set; }

        /// <summary>
        /// 培训人员工号ID
        /// </summary>
        public string TrainEmployeeID { get; set; }

        /// <summary>
        /// 课程培训记录ID
        /// </summary>
        public string TrainingRecordID { get; set; }
    }

    /// <summary>
    /// 人员培训记录课程满意度及课程建议记录
    /// </summary>
    public class TrainingLearnerRecommendationsParamsView
    {
        /// <summary>
        /// 人员培训记录主键ID
        /// </summary>
        public string TrainingLearnerID { get; set; }

        /// <summary>
        /// 课程满意度（分）
        /// </summary>
        public int? CourseSatisfaction { get; set; }

        /// <summary>
        /// 课程建议
        /// </summary>
        public string CourseRecommendations { get; set; }
    }
}
