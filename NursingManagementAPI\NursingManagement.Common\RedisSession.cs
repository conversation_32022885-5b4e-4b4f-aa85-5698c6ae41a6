﻿using System;

namespace NursingManagement.Common
{
    /// <summary>
    /// RedisSession
    /// </summary>
    //public class RedisSession : ISession
    //{
    //    public bool Contain(string token)
    //    {
    //        throw new NotImplementedException();
    //    }

    //    public Session Get(string token)
    //    {
    //        throw new NotImplementedException();
    //    }

    //    public bool Remove(Session session)
    //    {
    //        throw new NotImplementedException();
    //    }

    //    public bool Set(Session session)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}