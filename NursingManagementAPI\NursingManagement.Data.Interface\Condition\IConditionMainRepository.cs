﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IConditionMainRepository
    {
        /// <summary>
        /// 根据来源ID集合,来源类别获取数据
        /// </summary>
        /// <param name="sourceIDs"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        Task<List<ConditionMainInfo>> GetListBySourceIDs(List<string> sourceIDs, string sourceType);
        /// <summary>
        /// 根据来源ID,来源类别获取数据
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        Task<ConditionMainInfo> GetDataBySourceID(string sourceID, string sourceType);
        /// <summary>
        ///  根据主记录ID集合获取数据
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <returns></returns>
        Task<List<ConditionMainInfo>> GetListByMainIDs(List<string> mainIDs);
        /// <summary>
        /// 根据来源类别获取数据
        /// </summary>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        Task<List<ConditionMainInfo>> GetListBySourceType(string sourceType);
        /// <summary>
        /// 根据mainID获取数据
        /// </summary>
        /// <param name="mainD"></param>
        /// <returns></returns>
        Task<ConditionMainInfo> GetDateByMainID(string mainD);
        /// <summary>
        /// 根据来源获取对应的规则主记录
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        Task<List<ConditionMainInfo>> GetMainIDsBySourceID(string sourceID);
        /// <summary>
        /// 根据分类下的明细值集合获取数据
        /// </summary>
        /// <param name="groupTypeValues"></param>
        /// <param name="groupType"></param>
        /// <returns></returns>
        Task<List<string>> GetSourceIDsByGroupTypeValue(List<string> groupTypeValues, string groupType);

        /// <summary>
        /// 根据来源ID,来源类别判断规则是否存在
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <returns></returns>
        Task<bool> ExistBySource(string sourceID, string sourceType);
    }
}
