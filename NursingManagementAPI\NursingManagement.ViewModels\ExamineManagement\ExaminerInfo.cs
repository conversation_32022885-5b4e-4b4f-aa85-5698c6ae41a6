﻿using NursingManagement.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.ViewModels.Examine
{
    /// <summary>
    /// 考核考官信息表，用于存储考核记录对应的主要人信息
    /// </summary>
    [Serializable]
    [Table("Examiner")]
    public class ExaminerInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主考官记录ID，自动增长
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ExaminerID { get; set; }

        /// <summary>
        /// 员工ID，用于关联员工信息
        /// </summary>
        [Column("EmployeeID", TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 数据来源类型，例如ExaminationRecordID、ExaminationMainID等
        /// </summary>
        [Column("SourceType", TypeName = "varchar(20)")]
        public string SourceType { get; set; }

        /// <summary>
        /// 数据来源的唯一标识符
        /// </summary>
        [Column("SourceID", TypeName = "nvarchar(32)")]
        public string SourceID { get; set; }
    }
}
