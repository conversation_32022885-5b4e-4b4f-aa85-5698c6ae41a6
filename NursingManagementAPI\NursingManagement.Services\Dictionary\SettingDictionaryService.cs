﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Services
{
    public class SettingDictionaryService : ISettingDictionaryService
    {
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IUnitOfWork _unitOfWork;
        public SettingDictionaryService(
            ISettingDictionaryRepository settingDictionaryRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IUnitOfWork unitOfWork
            )
        {
            _settingDictionaryRepository = settingDictionaryRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _unitOfWork = unitOfWork;
        }
        /// <summary>
        /// 获取 质控表单类型
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <param name="qcType"></param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetQCFormType(string settingType, string settingTypeCode, QCTypeEnum? qcType)
        {
            var settingParams = new SettingDictionaryParams
            {
                SettingType = settingType,
                SettingTypeCode = settingTypeCode,
            };
            var settings = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            if (settings.Count == 0)
            {
                return new List<SelectOptionsView>();
            };
            if (!qcType.HasValue)
            {
                return settings.Select(m => new SelectOptionsView() { Label = m.Description, Value = m.SettingValue }).ToList();
            }
            var typeView = new QCFormTypeView();
            if (typeView.FormTypes.TryGetValue(qcType.Value, out var formTypeSet))
            {
                return settings.Where(m => formTypeSet.Contains(m.SettingValue)).Select(m => new SelectOptionsView() { Label = m.Description, Value = m.SettingValue }).ToList();
            };
            return settings.Select(m => new SelectOptionsView() { Label = m.Description, Value = m.SettingValue }).ToList();
        }
        /// <summary>
        /// 获取下拉框数据
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetSettingDictionaryDict(SettingDictionaryParams settingDictionaryParams)
        {
            if (settingDictionaryParams == null)
            {
                return new List<SelectOptionsView>();
            }
            var datas = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            return datas.OrderBy(m => m.Sort)
                        .Select(m => new SelectOptionsView() { Label = m.Description, Value = m.SettingValue, Type = m.SettingTypeValue })
                        .ToList();
        }
        /// <summary>
        /// 获取部门岗位工作时间配置
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="currentDate"></param>
        /// <returns></returns>
        public async Task<string> GetCurrentPostSeason(int departmentID, DateTime? currentDate)
        {
            var season = "Summer";
            if (currentDate == null)
            {
                currentDate = DateTime.Now;
            }
            var year = currentDate.Value.Year.ToString();
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "DepartmentPostWorkingSeason"
            };
            var settings = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var settingTypes = (from setting in settings
                                let settingTypeArrr = setting.SettingTypeValue.Split('-')
                                where settingTypeArrr.Length > 1
                                select settingTypeArrr[0]).Distinct().ToList();
            foreach (var settingType in settingTypes)
            {
                var startDate = settings.Find(m => m.SettingTypeValue.Contains(settingType) && m.SettingTypeValue.Contains("StartDate"))?.SettingValue;
                var endDate = settings.Find(m => m.SettingTypeValue.Contains(settingType) && m.SettingTypeValue.Contains("EndDate"))?.SettingValue;
                if (!string.IsNullOrWhiteSpace(startDate) && startDate.Contains("-") && !string.IsNullOrWhiteSpace(endDate) && endDate.Contains("-"))
                {
                    DateTime.TryParse($"{year}-{startDate}", out var start);
                    DateTime.TryParse($"{year}-{endDate}", out var end);
                    // 开始时间大于结束时间，表示跨年了，结束时间加12 保证条件判断正常
                    if (start > end)
                    {
                        if (currentDate >= start)
                        {
                            end = end.AddYears(1);
                        }
                        else
                        {
                            start = start.AddYears(-1);
                        }
                    }
                    if (start <= currentDate && currentDate <= end)
                    {
                        season = settingType;
                        break;
                    }
                }
            }
            return season;
        }
        /// <summary>
        /// 获取消息通知工具开关配置
        /// </summary>
        /// <param name="settingTypeCode"></param>
        /// <returns></returns>
        public async Task<List<(string settingTypeCode, bool switchFlag)>> GetMessageNotifySettingAsync(string settingTypeCode)
        {
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "Common",
                SettingTypeCode = settingTypeCode,
            };
            var settingList = await _settingDictionaryRepository.GetSettingSwitchList(settingParams);
            return settingList.Select(m =>
            (m.SettingTypeValue, !string.IsNullOrEmpty(m.SettingValue) && m.SettingValue.Equals("TRUE", StringComparison.CurrentCultureIgnoreCase))).ToList();
        }
        /// <summary>
        /// 获取语言配置
        /// </summary>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetLanguageList()
        {
            var setingPrams = new SettingDictionaryParams
            {
                SettingType = "Common",
                SettingTypeCode = "Language",
                SettingTypeValue = "Language"
            };
            var languageList = await _settingDictionaryRepository.GetSettingDictionary(setingPrams);
            return languageList.Select(m => new Dictionary<string, object>
            {
                { "language",m.Language},
                { "languageName",m.Description},
                { "languageCode",m.SettingValue}
            }).ToList();
        }
        /// <summary>
        /// 获取审批页面中那些审批项目具有跳转到源业务页面的按钮和逻辑
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, bool>> GetApprovalJumpSettingAsync()
        {
            var setingPrams = new SettingDictionaryParams
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApprovalJumpSourceSetting",
            };
            var settingInfos = await _settingDictionaryRepository.GetSettingDictionary(setingPrams);
            return settingInfos.ToDictionary(m => m.SettingTypeValue, m => m.SettingValue.ToUpper() == "TRUE");
        }
        /// <summary>
        /// 获取开关类配置
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        public async Task<bool> GetSettingSwitchAsync(SettingDictionaryParams settingDictionaryParams)
        {
            return await _settingDictionaryRepository.GetSettingSwitch(settingDictionaryParams);
        }

        /// <summary>
        /// 获取审批分类级联选择器数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<CascaderView<string>>> GetProveCategoryCascaderList()
        {
            var groupProveCategoriesSetting = await _settingDictionaryRepository.GetSettingDictionary(new SettingDictionaryParams
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveProcess",
                SettingTypeValue = "ProveCategoryGroup"
            });
            var proveCategories = await _settingDictionaryRepository.GetSettingDictionary(new SettingDictionaryParams
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveProcess",
                FilterSettingTypeValue = "ProveCategoryGroup"
            });
            var grouped = proveCategories.GroupBy(m => m.SettingTypeValue);
            var returnView = new List<CascaderView<string>>(grouped.Count());
            foreach (var group in grouped)
            {
                var settingTypeValue = group.FirstOrDefault()?.SettingTypeValue;
                var label = groupProveCategoriesSetting.FirstOrDefault(m => settingTypeValue.Contains(m.SettingValue))?.Description;
                var firstLevelCategory = new CascaderView<string>
                {
                    Label = label,
                    Value = group.Key,
                    Children = group.Select(m => new CascaderView<string>
                    {
                        Label = m.Description,
                        Value = m.SettingValue
                    }).ToList()
                };
                returnView.Add(firstLevelCategory);
            }
            return returnView;
        }
        /// <summary>
        /// 获取组织架构类型
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetOrganizationTypes()
        {
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "Common",
                SettingTypeCode = "DepartmentList",
                SettingTypeValue = "OrganizationType"
            };
            var organizationTypes = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);

            return organizationTypes.ToDictionary(m => m.SettingValue, m => m.Description);
        }
        /// <summary>
        /// 获取考核/培训分类数据
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <returns></returns>
        public async Task<List<TrainingExamineClassifiedView>> GetSettingDictionaryMaintain(string settingType, string settingTypeCode)
        {
            var classifiedView = new List<TrainingExamineClassifiedView>();
            var parasm = new SettingDictionaryParams
            {
                SettingType = settingType,
                SettingTypeCode = settingTypeCode
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(parasm);
            settingList = settingList.Where(m => m.ModifyFlag).ToList();
            if (settingList == null || settingList.Count <= 0)
            {
                return classifiedView;
            }
            var employeeList = await _employeePersonalDataRepository.GetIDAndNameData();
            var oneLvelSetting = from m in settingList
                                 where m.SettingTypeValue != null
                                 let settingTypeValueArr = m.SettingTypeValue.Split("_")
                                 where settingTypeValueArr.Length > 1 && settingTypeValueArr[1] == "1"
                                 select m;
            foreach (var setting in oneLvelSetting)
            {
                var view = new TrainingExamineClassifiedView
                {
                    LocalShowName = setting.Description,
                    SettingValue = setting.SettingValue,
                    Level = 1,
                    ParentID = "0",
                    ModifyDateTime = setting.ModifyDateTime,
                    ModifyEmployee = employeeList.Find(m => m.EmployeeID == setting.ModifyEmployeeID)?.EmployeeName,
                    SettingType = settingType,
                    Sort = setting.Sort,
                    ModifyEmployeeID = setting.ModifyEmployeeID,
                    HospitalID = setting.HospitalID,
                    Language = setting.Language,
                    SettingTypeCode = setting.SettingTypeCode,
                    Children = GetSettingDictionaryMaintainView(settingList, 2, setting.SettingValue, employeeList, settingType)
                };
                classifiedView.Add(view);
            }
            return classifiedView.OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        /// 递归获取分类子集
        /// </summary>
        /// <param name="settingList"></param>
        /// <param name="level"></param>
        /// <param name="upValue"></param>
        /// <param name="employeeList"></param>
        /// <param name="settingValue"></param>
        /// <returns></returns>
        private List<TrainingExamineClassifiedView> GetSettingDictionaryMaintainView(List<SettingDictionaryInfo> settingList, int level, string upValue, List<EmployeePersonalDataListView> employeeList, string settingValue)
        {
            var classifiedView = new List<TrainingExamineClassifiedView>();
            var currentLevelList = from m in settingList
                                   where m.SettingTypeValue != null
                                   let settingTypeValueArr = m.SettingTypeValue.Split("_")
                                   where settingTypeValueArr.Length > 2 && settingTypeValueArr[1] == level.ToString() && settingTypeValueArr[2] == upValue.ToString()
                                   select m;
            foreach (var setting in currentLevelList)
            {
                var view = new TrainingExamineClassifiedView
                {
                    LocalShowName = setting.Description,
                    SettingValue = setting.SettingValue,
                    Level = level,
                    ParentID = upValue,
                    ModifyDateTime = setting.ModifyDateTime,
                    ModifyEmployee = employeeList.Find(m => m.EmployeeID == setting.ModifyEmployeeID)?.EmployeeName,
                    SettingType = setting.SettingType,
                    Sort = setting.Sort,
                    ModifyEmployeeID = setting.ModifyEmployeeID,
                    HospitalID = setting.HospitalID,
                    Language = setting.Language,
                    SettingTypeCode = setting.SettingTypeCode,
                    Children = GetSettingDictionaryMaintainView(settingList, level + 1, setting.SettingValue, employeeList, settingValue)
                };
                classifiedView.Add(view);
            }
            return classifiedView.OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        ///  删除分类数据
        /// </summary>
        /// <param name="settingValue"></param>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteSettingDictionaryMaintain(string settingValue, string settingType, string settingTypeCode, string employeeID)
        {
            var parasm = new SettingDictionaryParams
            {
                SettingType = settingType,
                SettingTypeCode = settingTypeCode
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(parasm);
            if (settingList == null || settingList.Count <= 0)
            {
                return false;
            }
            var deleteList = settingList.Where(m => m.SettingValue == settingValue || (m.SettingTypeValue != null &&
                                         m.SettingTypeValue.Split("_").Length > 2 &&
                                         m.SettingTypeValue.Split("_")[2] == settingValue.ToString()));
            foreach (var data in deleteList)
            {
                data.Delete(employeeID);
                _unitOfWork.GetRepository<SettingDictionaryInfo>().Update(data);
            }
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await _settingDictionaryRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        ///  保存分类数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<bool> SaveSettingDictionaryMaintain(TrainingExamineClassifiedView view)
        {
            var parasm = new SettingDictionaryParams
            {
                SettingType = view.SettingType,
                SettingTypeCode = view.SettingTypeCode,
            };
            var settingDictionaryList = await _settingDictionaryRepository.GetSettingDictionary(parasm);
            await RecursionSettingDictionaryMaintain(view, settingDictionaryList);
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await _settingDictionaryRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 递归保存数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task RecursionSettingDictionaryMaintain(TrainingExamineClassifiedView view, List<SettingDictionaryInfo> settingDictionaryList)
        {
            int maxSettingValueID = 1;
            int sort = 1;
            var maxSettingValue = settingDictionaryList.OrderByDescending(m => m.SettingValue.ToString().PadLeft(5, '0')).FirstOrDefault()?.SettingValue;
            if (!string.IsNullOrEmpty(maxSettingValue) && int.TryParse(maxSettingValue, out maxSettingValueID))
            {
                maxSettingValueID += 1;
                sort = settingDictionaryList.Count + 1;
            }
            if (string.IsNullOrEmpty(view.SettingValue))
            {
                var partentID = string.IsNullOrEmpty(view.ParentID) ? "0" : view.ParentID;
                var classifiedInfo = new SettingDictionaryInfo()
                {
                    HospitalID = view.HospitalID,
                    Language = view.Language,
                    DataType = "1",
                    SettingType = view.SettingType,
                    SettingTypeCode = view.SettingTypeCode,
                    SettingTypeValue = $"{view.SettingType}_{view.Level.ToString()}_{partentID}",
                    SettingValue = maxSettingValueID.ToString(),
                    Description = view.LocalShowName,
                    Sort = sort,
                    ModifyFlag = true,
                    DeleteFlag = "",                   
                };
                classifiedInfo.Add(view.ModifyEmployeeID ?? view.EmployeeID);
                classifiedInfo.Modify(view.ModifyEmployeeID ?? view.EmployeeID);
                classifiedInfo.ModifyDateTime = view.ModifyDateTime;
                await _unitOfWork.GetRepository<SettingDictionaryInfo>().InsertAsync(classifiedInfo);
                foreach (var item in view.Children)
                {
                    sort += 1;
                    await RecursionSettingDictionaryMaintain(item, settingDictionaryList);
                }
                return;
            }
            var settingDictionaryDate = settingDictionaryList.Find(m => m.SettingValue == view.SettingValue);
            if (settingDictionaryDate != null)
            {
                settingDictionaryDate.Description = view.LocalShowName;
                settingDictionaryDate.Modify(view.ModifyEmployeeID ?? view.EmployeeID);
                settingDictionaryDate.ModifyDateTime = view.ModifyDateTime;
                _unitOfWork.GetRepository<SettingDictionaryInfo>().Update(settingDictionaryDate);
                foreach (var item in view.Children)
                {
                    sort += 1;
                    await RecursionSettingDictionaryMaintain(item, settingDictionaryList);
                }
            }
        }
        /// <summary>
        ///  根据SettingTypeCode和SettingTypeValue获取配置键值对配置
        /// </summary>
        /// <param name="settingTypeCode"></param>
        /// <param name="settingTypeValueList"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, List<KeyValuePair<string, string>>>> GetSettingDictionaryByCodeValue(string settingTypeCode, List<string> settingTypeValueList)
        {
            if (settingTypeValueList == null || settingTypeValueList.Count <= 0)
            {
                return [];
            }
            var settingParams = new SettingDictionaryParams
            {
                SettingTypeCode = settingTypeCode,
            };
            var settingDictionarys = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            Dictionary<string, List<KeyValuePair<string, string>>> result = new();
            foreach (var settingTypeValue in settingTypeValueList)
            {
                var tempDict = settingDictionarys.Where(m => m.SettingTypeValue.Contains(settingTypeValue)).Select(m => new KeyValuePair<string, string>(m.SettingValue, m.Description)).ToList();
                result.Add(settingTypeValue, tempDict);
            }
            return result;
        } /// <summary>
          /// 根据SettingTypeCode和SettingTypeValue获取配置级联数据
          /// </summary>
          /// <param name="settingTypeCode"></param>
          /// <param name="settingTypeValueList"></param>
          /// <returns></returns>
        public async Task<Dictionary<string, List<CascaderView<string>>>> GetCascaderSettingDictionary(string settingTypeCode, List<string> settingTypeValueList)
        {
            if (settingTypeValueList == null || settingTypeValueList.Count <= 0)
            {
                return [];
            }
            var settingParams = new SettingDictionaryParams
            {
                SettingTypeCode = settingTypeCode,
            };
            var settingDictionarys = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            Dictionary<string, List<CascaderView<string>>> result = new();
            foreach (var settingTypeValue in settingTypeValueList)
            {
                var cascaderView = new List<CascaderView<string>>();
                var settingList = settingDictionarys.Where(m => m.SettingTypeValue.Contains(settingTypeValue)).ToList();
                var settingTypeValueData = settingList.Select(m => m.SettingTypeValue).Distinct().ToList();
                List<string> levelList = new List<string>();
                List<string> groupList = new List<string>();

                foreach (var parts in settingTypeValueData)
                {
                    var splitArray = parts.Split("_");
                    if (splitArray.Length > 1)
                    {
                        var levelPart = splitArray[1].ToString();
                        if (!string.IsNullOrEmpty(levelPart))
                        {
                            levelList.Add(levelPart);
                        }
                    }
                    if (splitArray.Length > 2)
                    {
                        var groupPart = splitArray[2].ToString();
                        if (!string.IsNullOrEmpty(groupPart))
                        {
                            groupList.Add(groupPart);
                        }
                    }
                }
                var levelArr = levelList.OrderBy(m => m).Distinct().ToArray();
                var groupArr = groupList.Distinct().ToArray();
                foreach (var group in groupArr)
                {
                    var oneLeverSetting = settingList.FirstOrDefault(m => m.SettingTypeValue.Contains(group) && m.SettingTypeValue.Contains(levelArr[0]));
                    var view = new CascaderView<string>()
                    {
                        Label = oneLeverSetting.Description,
                        Value = oneLeverSetting.SettingValue,
                        Children = GenerateSettingDictionaryCascaderView(settingList, 1, group, levelArr)
                    };
                    cascaderView.Add(view);
                }
                result.Add(settingTypeValue, cascaderView);
            }
            return result;
        }
        /// <summary>
        /// 获取配置级联子数据
        /// </summary>
        /// <param name="settingDictionaryList"></param>
        /// <param name="level"></param>
        /// <param name="group"></param>
        /// <param name="levelArr"></param>
        /// <returns></returns>
        private List<CascaderView<string>> GenerateSettingDictionaryCascaderView(List<SettingDictionaryInfo> settingDictionaryList, int level, string group, string[] levelArr)
        {
            var cascaderViews = new List<CascaderView<string>>();
            if (levelArr.Length < level + 1)
            {
                return cascaderViews;
            }

            var currentLevelList = settingDictionaryList.Where(m => m.SettingTypeValue.Contains(group) && m.SettingTypeValue.Contains(levelArr[level])).ToList();
            foreach (var setting in currentLevelList)
            {
                var cascaderView = new CascaderView<string>
                {
                    Label = setting.Description,
                    Value = setting.SettingValue,
                    Children = GenerateSettingDictionaryCascaderView(settingDictionaryList, level + 1, group, levelArr)
                };
                cascaderViews.Add(cascaderView);
            }
            return cascaderViews;
        }
    }
}
