﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class CommonFileRepository : ICommonFileRepository
    {
        private readonly NursingManagementDbContext _context;
        private readonly SessionCommonServer _sessionCommonServer;

        public CommonFileRepository(
            NursingManagementDbContext context
            , SessionCommonServer sessionCommonServer
        )
        {
            _context = context;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 根据分类和来源获取文件集合
        /// </summary>
        /// <param name="fileClass"></param>
        /// <param name="sourceID"></param>
        /// <returns></returns>
        public async Task<List<CommonFileInfo>> GetFileListByClassAndSourceAsync(string fileClass, string sourceID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            if (string.IsNullOrEmpty(fileClass))
            {
                return await _context.CommonFileInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == session.HospitalID && m.SourceID == sourceID).ToListAsync();
            }
            return await _context.CommonFileInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == session.HospitalID
                        && m.Class == fileClass && m.SourceID == sourceID).ToListAsync();
        }
        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="fileID"></param>
        /// <returns></returns>
        public async Task<CommonFileInfo> GetFileByFileIDAsync(string fileID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.CommonFileInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == session.HospitalID
                        && m.CommonFileID == fileID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主键获取link
        /// </summary>
        /// <param name="fileID"></param>
        /// <returns></returns>
        public async Task<string> GetFileUrlByIDAsync(string fileID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.CommonFileInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == session.HospitalID
                        && m.CommonFileID == fileID).Select(m => m.Link).FirstOrDefaultAsync();
        }

        public async Task<List<CommonFileInfo>> GetPartFileInfosByIDsAsync(List<string> fileIDs)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.CommonFileInfos.Where(m => m.DeleteFlag != "*" && m.HospitalID == session.HospitalID
                        && fileIDs.Contains(m.CommonFileID)).Select(m => new CommonFileInfo
                        {
                            SourceID = m.SourceID,
                            CommonFileID = m.CommonFileID,
                            Content = m.Content
                        }).ToListAsync();
        }
    }
}
