﻿using Microsoft.AspNetCore.Http;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;

namespace NursingManagement.Services
{
    public class SessionService : ISessionService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IRedisService _redisService;

        public SessionService(
            IHttpContextAccessor httpContextAccessor
            , IRedisService redisService
        )
        {
            _httpContextAccessor = httpContextAccessor;
            _redisService = redisService;
        }

        public async Task<string> SetUserLoginSession(string hospitalID, int language)
        {
            var token = "UserLoginHospitalLanguage";
            var session = new Session()
            {
                Token = token,
                LoginTime = DateTime.Now,
                Language = language,
                HospitalID = hospitalID,
                Roles = new List<int>()
            };
            await SetAsync(session);
            return token;
        }

        public async Task<Session> GetSession()
        {
            var token = "";
            try
            {
                token = _httpContextAccessor.HttpContext.GetCommonToken();
                if (string.IsNullOrEmpty(token))
                {
                    token = "UserLoginHospitalLanguage";
                }
                token = token.Trim();
            }
            catch (Exception)
            {
                return null;
            }
            var session = await GetAsync(token);
            return await Task.Run(() => session);
        }

        public async Task<bool> ContainAsync(string token)
        {
            if (token == null)
            {
                return false;
            }
            return await _redisService.Exists(token);
        }

        public async Task<Session> GetAsync(string token)
        {
            if (await ContainAsync(token))
            {
                return await _redisService.GetAsync<Session>(token);

            }
            return null;
        }

        public async Task<bool> RemoveAsync(Session session)
        {
            return await _redisService.Remove(session.Token);
        }

        public async Task SetAsync(Session session)
        {
            //session过期时间设置为8小时
            await _redisService.Add(session.Token, 8 * 60 * 60, session);
        }

        /// <summary>
        /// 获取缓存集合
        /// </summary>
        /// <param name="keys">缓存Key集合</param>
        /// <returns></returns>
        public async Task<IDictionary<string, object>> GetAll<T>(IEnumerable<string> keys)
        {
            if (keys == null)
            {
                throw new ArgumentNullException(nameof(keys));
            }
            var dict = new Dictionary<string, object>();
            try
            {
                foreach (var key in keys)
                {
                    var sessionObj = await _redisService.GetAsync<Object>(key);
                    if (sessionObj is T temp)
                    {
                        dict.Add(key, temp);
                    }
                }
                return dict;
            }
            catch { }
            return dict;
        }
    }
}