﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IMonthlyPlanMaintainService
    {
        #region 月度计划表增删改查
        /// <summary>
        /// 保存月度计划工作内容
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        Task<bool> SaveMonthlyWorks(MpWorksSaveView view);

        /// <summary>
        /// 删除月度计划工作
        /// </summary>
        /// <param name="quarterPlanDetailID">主键</param>
        /// <returns></returns>
        Task<bool> DeleteMonthlyWork(string quarterPlanDetailID, string employeeID);

        /// <summary>
        /// 更新一条月度计划工作
        /// </summary>
        /// <param name="workView">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<bool> UpdateMonthlyWork(TieredPlanWork workView, string employeeID);

        /// <summary>
        /// 发布月度计划
        /// </summary>
        /// <param name="quarterPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        Task<bool> PublishMonthlyPlan(string quarterPlanMainID, string employeeID);

        /// <summary>
        /// 获取月度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarter">月度</param>
        /// <returns></returns>
        Task<string> GetMonthlyPlanMainID(string annualPlanMainID, int quarter);

        /// <summary>
        /// 获取月度计划状态
        /// </summary>
        /// <param name="quarterPlanMainID">月度计划主键</param>
        /// <returns></returns>
        Task<bool> GetMonthlyPlanStatus(string quarterPlanMainID);

        /// <summary>
        /// 查询某科室的月度计划
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        Task<TieredPlanWorksByType[]> GetMonthlyWorks(string annualPlanMainID, string quarterPlanMainID);
        #endregion

        #region 参考导入逻辑
        /// <summary>
        /// 查询可导入的工作
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="quarterPlanMainID">月度计划主表ID</param>
        /// <param name="departmentID">当前部门</param>
        /// <param name="annual">年度</param>
        /// <param name="month">月度</param>
        /// <returns></returns>
        Task<List<TieredPlanWorksByPlanThenType>> GetCanImportMpWorksGroupByPlanThenType(string annualPlanMainID, string quarterPlanMainID, int departmentID, int annual, int month);
        /// <summary>
        /// 获取上级部门月度计划可选工作
        /// </summary>
        /// <param name="annual">年度</param>
        /// <param name="departmentID">部门</param>
        /// <param name="includeApInterventionID">需包含的执行项目字典ID</param>
        /// <param name="excludeApInterventionIDs">需排除的执行项目字典ID集合</param>
        /// <param name="typeList">分类字典</param>
        /// <returns></returns>
        /// <exception cref="Exception">未找到上级部门月度计划所属的年度计划</exception>
        Task<List<TieredPlanWorksByPlanThenType>> GetUpperDeptMpWorksByPlanThenTypeList(int annual, int departmentID, int? includeApInterventionID, int[] excludeApInterventionIDs = null, List<AnnualPlanTypeListInfo> typeList = null);
        /// <summary>
        /// 批量导入保存
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        Task<bool> SaveImportWorks(MpWorksSaveView view);
        #endregion

        /// <summary>
        /// 查询本人及上下级已制定的月度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        Task<List<APMainView>> GetBrowseMPViews(int year, string employeeID);

    }
}
