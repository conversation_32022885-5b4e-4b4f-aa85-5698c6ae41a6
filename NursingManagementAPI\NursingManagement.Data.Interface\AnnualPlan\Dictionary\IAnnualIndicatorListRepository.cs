﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划指标字典
    /// </summary>
    public interface IAnnualIndicatorListRepository : ICacheRepository
    {
        /// <summary>
        /// 获取最大指标字典ID
        /// </summary>
        /// <returns></returns>
        Task<int> GetMaxID();
        /// <summary>
        /// 根据ID获取一条数据，更新使用
        /// </summary>
        /// <param name="annualIndicatorID"></param>
        /// <param name="noCache">是否走缓存，默认走缓存</param>
        /// <returns></returns>
        Task<AnnualIndicatorListInfo> GetInfoByID(int annualIndicatorID, bool noCache = false);
    }
}
