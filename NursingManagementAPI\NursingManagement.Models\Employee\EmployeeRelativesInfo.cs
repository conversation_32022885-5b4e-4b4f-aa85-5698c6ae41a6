﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员家庭信息表
    /// </summary>
    [Serializable]
    [Table("EmployeeRelatives")]
    public class EmployeeRelativesInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeRelativesID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 家庭关系(GB/T 4761-2008)
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string RelationshipCode { get; set; }

        /// <summary>
        /// 亲属姓名
        /// </summary>
        [Column(TypeName = "nvarchar(30)")]
        public string RelativesName { get; set; }

        /// <summary>
        /// 亲属手机号码
        /// </summary>
        [Column(TypeName = "varchar(11)")]
        public string RelativesPhoneNumber { get; set; }

        /// <summary>
        /// 亲属身份证
        /// </summary>
        [Column(TypeName = "varchar(18)")]
        public string RelativesIDCardNo { get; set; }

        /// <summary>
        /// 亲属籍贯
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string RelativesNativePlace { get; set; }

        /// <summary>
        /// 亲属学历
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string RelativesEducation { get; set; }

        /// <summary>
        /// 亲属学校
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string RelativesSchool { get; set; }

        /// <summary>
        /// 亲属任职单位
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string RelativesCompany { get; set; }

        /// <summary>
        /// 亲属担任职务
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string RelativesPost { get; set; }
    }
}