﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeEducationalExperienceRepository
    {
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeEducationalExperienceInfo>> GetListByEmployeeID(string employeeID);

        /// <summary>
        /// 获取第一学历及最高学历信息
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="eucationalIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeEducationalExperienceInfo>> GetFirstAndHighestEducationalView(string hospitalID, string[] employeeIDs, string[] eucationalIDs);
    }
}