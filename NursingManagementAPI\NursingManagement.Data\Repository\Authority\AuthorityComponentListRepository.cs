﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AuthorityComponentListRepository : IAuthorityComponentListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public AuthorityComponentListRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AuthorityComponentListInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AuthorityComponentList.GetKey(_sessionCommonServer);
        }

        public async Task<List<AuthorityComponentListInfo>> GetComponentListByRoleIDs(List<int> roleIDs, List<int> routerListIDs, int clientType)
        {
            var componentLists = await GetCacheAsync() as List<AuthorityComponentListInfo>;
            if (componentLists.Count <= 0)
            {
                return new List<AuthorityComponentListInfo>();
            }
            return componentLists.Where(m => roleIDs.Contains(m.AuthorityRoleID) && routerListIDs.Contains(m.RouterListID) && m.ClientType == clientType).ToList();
        }
    }
}