﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 部门维护控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/department")]
    [EnableCors("any")]
    public class DepartmentMaintenanceController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IDepartmentVSDepartmentService _departmentVSDepartmentService;

        /// <summary>
        /// 部门维护
        /// </summary>
        public DepartmentMaintenanceController(
            ISessionService session
            , IDepartmentVSDepartmentService departmentVSDepartmentService
        )
        {
            _session = session;
            _departmentVSDepartmentService = departmentVSDepartmentService;
        }
        /// <summary>
        /// 获取部门对照关系视图
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentVSDepartmentListView")]
        public async Task<IActionResult> GetDepartmentVSDepartmentListView(string organizationType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _departmentVSDepartmentService.GetDepartmentVSDepartmentView(organizationType);
            return result.ToJson();
        }
        /// <summary>
        /// 批量保存
        /// </summary>
        /// <param name="saveViews">保存数据</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveDepartmentVSDepartmentListViews")]
        public async Task<IActionResult> SaveDepartmentVSDepartmentListViews([FromBody] List<DepartmentMaintenanceView> saveViews)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _departmentVSDepartmentService.SaveDepartmentVSDepartmentViews(saveViews, session);
            return result.ToJson();
        }

        /// <summary>
        /// 单个保存
        /// </summary>
        /// <param name="saveView">保存数据</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveDepartmentVSDepartmentListView")]
        public async Task<IActionResult> SaveDepartmentVSDepartmentListView([FromBody] DepartmentMaintenanceView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _departmentVSDepartmentService.SaveDepartmentVSDepartmentView(saveView, session);
            return result.ToJson();
        }

        /// <summary>
        /// 启用或停用该部门
        /// </summary>
        /// <param name="view">组织信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("EnableOrDisableDepartment")]
        public async Task<IActionResult> EnableOrDisableDepartment([FromBody] DepartmentMaintenanceView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _departmentVSDepartmentService.EnableOrDisableDepartment(view.ManagementDepartmentID, session.EmployeeID, view.IsActived);
            return result.ToJson();
        }
    }
}
