﻿using DocumentFormat.OpenXml.Bibliography;
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Linq;

namespace NursingManagement.Data.Repository
{
    public class DepartmentListRepository : IDepartmentListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public DepartmentListRepository(NursingManagementDbContext db,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID
                && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.DepartmentList.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        /// <summary>
        /// 根据ID获取科室部门信息
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        public async Task<DepartmentListInfo> GetByIDAsync(int departmentID)
        {
            var list = (List<DepartmentListInfo>)await GetCacheAsync();
            return list.Where(m => m.DepartmentID == departmentID).FirstOrDefault();
        }

        /// <summary>
        /// 获取所有缓存
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetByCacheAsync()
        {
            var caches = await GetCacheAsync() as List<DepartmentListInfo>;
            return caches;
        }
        /// <summary>
        /// 根据部门名字获取数据
        /// </summary>
        /// <param name="departmentName"></param>
        /// <returns></returns>
        public async Task<DepartmentListInfo> GetByName(string departmentName)
        {
            if (string.IsNullOrEmpty(departmentName))
            {
                return null;
            }
            var list = await GetCacheAsync() as List<DepartmentListInfo>;
            return list.FirstOrDefault(m => m.DepartmentContent.Contains(departmentName));
        }
        /// <summary>
        /// 获取部门字典数据（ID，code，name）
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetAllDictAsync()
        {
            var list = (List<DepartmentListInfo>)await GetCacheAsync();
            return list.Select(m => new DepartmentListInfo
            {
                DepartmentID = m.DepartmentID,
                DepartmentCode = m.DepartmentCode,
                DepartmentContent = m.DepartmentContent,
                LocalShowName= m.LocalShowName,
                UpperLevelDepartmentID = m.UpperLevelDepartmentID
            }).ToList();
        }
        /// <summary>
        /// 根据组织架构类型获取部门配置
        /// </summary>
        /// <param name="organizationType"></param>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetByOrganizationType(string organizationType)
        {
            var list = await GetCacheAsync() as List<DepartmentListInfo>;
            if (string.IsNullOrWhiteSpace(organizationType) || organizationType == "0")
            {
                return list;
            }
            return list.Where(m => m.OrganizationType == organizationType).ToList();
        }
        /// <summary>
        /// 获取上级部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<DepartmentListInfo> GetUpperDepartment(int departmentID)
        {
            var list = (List<DepartmentListInfo>)await GetCacheAsync();
            // 查找当前部门的直接与间接上级部门
            var upperDepartment = list.FirstOrDefault(m => m.DepartmentID == departmentID);
            if (upperDepartment?.UpperLevelDepartmentID == 0)
            {
                return null;
            }
            return list.FirstOrDefault(m => m.DepartmentID == upperDepartment.UpperLevelDepartmentID);
        }
        /// <summary>
        /// 递归查找所有上级部门
        /// </summary>
        /// <typeparam name="T">返回元素类型</typeparam>
        /// <typeparam name="V">返回集合类型</typeparam>
        /// <param name="departmentID">部门ID</param>
        /// <param name="func">要返回的数据格式</param>
        /// <returns></returns>
        public async Task<V> GetUpperDepartmentRecursion<T, V>(int departmentID, Func<DepartmentListInfo, T> func) where V : ICollection<T>, new()
        {
            var upperDepts = new V();
            var currentDeptID = departmentID;
            while (currentDeptID != 0)
            {
                var upperDepartment = await GetUpperDepartment(currentDeptID);
                currentDeptID = upperDepartment?.DepartmentID ?? 0;
                if (currentDeptID != 0)
                {
                    var res = func(upperDepartment);
                    upperDepts.Add(res);
                }
            }
            return upperDepts;
        }
        /// <summary>
        /// 获取下级部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetLowerDepartments(int departmentID)
        {
            var list = (List<DepartmentListInfo>)await GetCacheAsync();
            return list.FindAll(m => m.UpperLevelDepartmentID == departmentID);
        }
        /// <summary>
        /// 获取部门ID最大值
        /// </summary>
        /// <param name="hospitalID">医院编号</param>
        /// <param name="language">语言编号</param>
        /// <returns></returns>
        public async Task<int> GetMaxDepartmentID(string hospitalID,int language)
        {
            return await _nursingManagementDbContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID
                && m.Language == language).Select(m=>m.DepartmentID).MaxAsync();
        }

        /// <summary>
        /// 根据组织架构类型获取部门配置(包括删除的)
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetDepartmentListNoCache()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var result = await _nursingManagementDbContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language).ToListAsync();
            return result;
        }
        /// <summary>
        /// 根据部门名称集合获取数据
        /// </summary>
        /// <param name="nameList"></param>
        /// <param name="organizationType"></param>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetByNameList(List<string> nameList, string organizationType)
        {
            if (nameList.Count <= 0)
            {
                return null;
            }
            var list = await GetByOrganizationType(organizationType);
            return list.Where(m => nameList.Any(name => m.DepartmentContent.Contains(name))).ToList();
        }
        /// <summary>
        /// 获取View
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentView>> GetViewsByOrganizationType(string organizationType)
        {
            var list = (List<DepartmentListInfo>)await GetCacheAsync();
            return list.IfWhere(!string.IsNullOrEmpty(organizationType), m => organizationType == m.OrganizationType)
                .Select(m => new EmployeeDepartmentView
                {
                    DepartmentID = m.DepartmentID,
                    LocalShowName = m.LocalShowName,
                    OrganizationType = m.OrganizationType
                }).ToList();
        }
        /// <summary>
        /// 根据his部门与护理管理部门关系
        /// </summary>
        /// <param name="organizationType1"></param>
        /// <param name="organizationType2"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string,object>>>GetNMDepartmentByHisDepartment(string organizationType1,string organizationType2, string hospitalID)
        {
          var mappingDeparmentList = await (from a in _nursingManagementDbContext.DepartmentVSDepartmentInfos
                 join b in _nursingManagementDbContext.DepartmentListInfos on new { OrganizationType = a.OrganizationType1 , DepartmentID= a.DepartmentID1  } equals  new { b.OrganizationType, b.DepartmentID }
                 join c in _nursingManagementDbContext.DepartmentListInfos on new { OrganizationType = a.OrganizationType2, DepartmentID = a.DepartmentID2 } equals new { c.OrganizationType, c.DepartmentID }
                                            where a.OrganizationType1 == organizationType1 && a.OrganizationType2 == organizationType2 && a.HospitalID == hospitalID&& a.DeleteFlag!="*"
                 && b.DeleteFlag !="*" && c.DeleteFlag !="*" select new Dictionary<string, object>()
                 {
                     {"stationID",b.DepartmentCode },
                     {"hisDepartmentID",b.DepartmentID },
                     {"hisDepartmentName",b.LocalShowName },
                     {"nmDepartmentID",c.DepartmentID },
                     {"nmDepartmentName",c.LocalShowName },
                     {"upNmDepartmentID", c.UpperLevelDepartmentID}
                 }).ToListAsync();
            return mappingDeparmentList;
        }

        /// <summary>
        /// 根据组织类别和部门编码获取对应的部门ID
        /// </summary>
        /// <param name="organizationType">组织类别</param>
        /// <param name="departmentCode">部门编码</param>
        /// <returns></returns>
        public async Task<int> GetDepartmentIDByOrganizationAndDepartmentCodeAsync(string organizationType, string departmentCode)
        {
            var list = (List<DepartmentListInfo>)await GetCacheAsync();
            return list.Where(m=>m.OrganizationType == organizationType && m.DepartmentCode == departmentCode).Select(m=>m.DepartmentID).FirstOrDefault();
        }
        /// <summary>
        /// 根据主键集合获取数据
        /// </summary>
        /// <param name="deparmentIDs"></param>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetDepartmentIDByIDsAsync(List<int>deparmentIDs)
        {
            var list = (List<DepartmentListInfo>)await GetCacheAsync();
            return list.Where(m => deparmentIDs.Contains(m.DepartmentID)).ToList();
        }
    }
}
