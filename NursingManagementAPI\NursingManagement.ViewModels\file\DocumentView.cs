﻿namespace NursingManagement.ViewModels
{
    public class DocumentView
    {
        /// <summary>
        /// 文档主内容
        /// </summary>
        public DocumentMainView DocumentMainView { get; set; }
        /// <summary>
        /// 文档明细属性数据集合
        /// </summary>
        public List<DocumentDetailView> DocumentDetailViews { get; set; }
        /// <summary>
        /// 文档标签集合
        /// </summary>
        public List<DocumentTagView> DocumentTagViews { get; set; }

      
    }
    /// <summary>
    /// 文档主内容
    /// </summary>
    public class DocumentMainView
    {
        /// <summary>
        /// 文件主记录ID
        /// </summary>
        public string DocumentMainID { set; get; }
        /// <summary>
        /// 来源系统
        /// </summary>
        public string SourceSystem { get; set; }
        /// <summary>
        /// 文档类型序号
        /// </summary>
        public int DocumentTypeID { get; set; }
        /// <summary>
        /// 上传人序号
        /// </summary>
        public string UserID { get; set; }
        /// <summary>
        /// 上传人姓名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 文档标题
        /// </summary>
        public string DocumentTitle { get; set; }
        /// <summary>
        /// 文档封面图片地址
        /// </summary>
        public string DocumentCover { get; set; }
        /// <summary>
        /// 文档摘要
        /// </summary>
        public string DocumentAbstract { get; set; }
        /// <summary>
        /// 文件类型扩展名
        /// </summary>
        public string DocumentType { get; set; }
        /// <summary>
        ///文件地址
        /// </summary>
        public string DocumentUrl { get; set; }
        /// <summary>
        ///文件状态（10：暂存；20：保存；30：待审核；40：已审核）
        ///</summary>
        public int DocumentStatus { get; set; }
        /// <summary>
        ///文件大小(KB)
        ///</summary>
        public int? DocumentSize { get; set; }
    }
    /// <summary>
    /// 文档明细属性数据
    /// </summary>
    public class DocumentDetailView
    {
        /// <summary>
        /// 动态模板组ID
        /// </summary>
        public int GroupID { get; set; }
        /// <summary>
        /// 动态模板项目ID
        /// </summary>
        public int ItemID { get; set; }
        /// <summary>
        /// 动态模板项目值
        /// </summary>
        public string Value { get; set; }

    }
    /// <summary>
    /// 文档标签
    /// </summary>
    public class DocumentTagView
    {
        /// <summary>
        /// 文档标签序号
        /// </summary>
        public string DocumentTagListID { get; set; }
        /// <summary>
        /// 标签类型
        /// </summary>
        public string TagType { get; set; }
        /// <summary>
        /// 标签内容
        /// </summary>
        public string TagContent { get; set; }
        /// <summary>
        /// 标签说明
        /// </summary>
        public string TagDescription { get; set; }
        /// <summary>
        /// 标签颜色
        /// </summary>
        public string TagColor { get; set; }
        /// <summary>
        /// 是否为自定义标签
        /// </summary>
        public bool? CustomFlag { get; set; }
    }
}