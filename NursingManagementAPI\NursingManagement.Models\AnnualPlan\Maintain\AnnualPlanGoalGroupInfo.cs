﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划目标分组
    /// </summary>
    [Serializable]
    public class AnnualPlanGoalGroupInfo : MutiModifyInfo
    {
        /// <summary>
        /// 部门年度计划目标分组主键ID
        /// </summary>
        public string AnnualPlanGoalGroupID { get; set; }
        /// <summary>
        /// 年度计划主表主键Guid，对应各部门年度计划主表
        /// </summary>
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 部门年度计划目标GuID,主键，对应年度计划目标表
        /// </summary>
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 呈现顺序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 负责部门
        /// </summary>
        public string ResponsibleDepartments { get; set; }
        /// <summary>
        /// 导航属性，一个Group对应一个MainGoal
        /// </summary>
        public AnnualPlanMainGoalInfo AnnualPlanMainGoal { get; set; }

        /// <summary>
        /// 导航属性，一个Group对应多个PlanIndicator
        /// </summary>
        public ICollection<AnnualPlanIndicatorDetailInfo> PlanIndicators { get; set; }

        /// <summary>
        /// 导航属性，一个Group对应多个PlanProject
        /// </summary>
        public ICollection<AnnualPlanProjectDetailInfo> PlanProjects { get; set; }
    }
}
