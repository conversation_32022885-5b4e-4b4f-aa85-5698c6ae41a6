﻿namespace NursingManagement.Data.Interface
{
    public interface IDepartmentToJobRepository : ICacheRepository
    {
        /// <summary>
        /// 根据职务编号获取科室+职务的拼接名称
        /// </summary>
        /// <param name="jobCode">职务编号</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<string> GetJobAndDepartmentNameByJobCode(string jobCode, int? departmentID);
    }
}
