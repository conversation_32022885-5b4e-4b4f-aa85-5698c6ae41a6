# 常态工作控制提醒 API 文档

## 概述

本 API 提供常态工作过程控制问题的自动提醒功能，支持以下功能：

- 3 天未整改问题提醒护士长
- 6 天未整改问题提醒片区行政主任
- 查询未整改问题列表
- 支持微信和电脑端同步提醒

## API 接口列表

### 1. 执行常态工作控制提醒

**接口地址：** `POST /api/NormalWorkingReminder/ExecuteReminder`

**请求参数：**

```json
{
  "hospitalID": "1",
  "reminderType": 0,
  "departmentID": null,
  "startDate": null,
  "endDate": null,
  "onlyQuery": false,
  "forceRemind": false
}
```

**参数说明：**

- `hospitalID`: 医院 ID（必填）
- `reminderType`: 提醒类型（0-全部提醒，3-3 天提醒，6-6 天提醒）
- `departmentID`: 指定部门 ID（可选）
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）
- `onlyQuery`: 是否只查询不发送提醒（可选）
- `forceRemind`: 是否强制提醒（可选）

**响应示例：**

```json
{
  "success": true,
  "message": "执行成功",
  "data": {
    "success": true,
    "message": "全部提醒执行完成",
    "totalProblemsChecked": 10,
    "problemsNeedReminder": 5,
    "successfulReminders": 5,
    "failedReminders": 0,
    "reminderDetails": [
      {
        "hierarchicalQCMainID": "xxx",
        "departmentID": 422,
        "departmentName": "1-1病区",
        "reminderType": 3,
        "receiverEmployeeIDs": ["EMP001"],
        "receiverEmployeeNames": ["张护士长"],
        "success": true,
        "message": "提醒发送成功"
      }
    ],
    "executionTime": "2024-06-26T10:00:00"
  }
}
```

### 2. 执行三天未整改提醒

**接口地址：** `POST /api/NormalWorkingReminder/ExecuteThreeDayReminder`

**请求参数：**

- `departmentID`: 部门 ID（可选，Query 参数）
- `forceRemind`: 是否强制提醒（可选，Query 参数）

**示例：** `POST /api/NormalWorkingReminder/ExecuteThreeDayReminder?departmentID=422&forceRemind=false`

### 3. 执行六天未整改提醒

**接口地址：** `POST /api/NormalWorkingReminder/ExecuteSixDayReminder`

**请求参数：**

- `departmentID`: 部门 ID（可选，Query 参数）
- `forceRemind`: 是否强制提醒（可选，Query 参数）

**示例：** `POST /api/NormalWorkingReminder/ExecuteSixDayReminder?departmentID=422&forceRemind=false`

### 4. 查询未整改问题列表

**接口地址：** `POST /api/NormalWorkingReminder/QueryUnrectifiedProblems`

**请求参数：**

```json
{
  "hospitalID": "1",
  "departmentID": null,
  "minUnrectifiedDays": 3,
  "maxUnrectifiedDays": null,
  "startDate": null,
  "endDate": null
}
```

**响应示例：**

```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "hierarchicalQCMainID": "xxx",
      "hierarchicalQCRecordID": "xxx",
      "hierarchicalQCSubjectID": "xxx",
      "examineDate": "2024-06-20T00:00:00",
      "departmentID": 422,
      "departmentName": "1-1病区",
      "subjectName": "护理质量检查",
      "guidance": "需要改进护理记录",
      "improvement": "",
      "qcEmployeeName": "张三",
      "unrectifiedDays": 6,
      "rectificationDateTime": null,
      "isRectified": false,
      "reminderType": 6,
      "reminderTypeDescription": "6天提醒片区主任"
    }
  ]
}
```

### 5. 获取需要三天提醒的问题列表

**接口地址：** `GET /api/NormalWorkingReminder/GetProblemsNeedThreeDayReminder`

**请求参数：**

- `departmentID`: 部门 ID（可选，Query 参数）

**示例：** `GET /api/NormalWorkingReminder/GetProblemsNeedThreeDayReminder?departmentID=422`

### 6. 获取需要六天提醒的问题列表

**接口地址：** `GET /api/NormalWorkingReminder/GetProblemsNeedSixDayReminder`

**请求参数：**

- `departmentID`: 部门 ID（可选，Query 参数）

**示例：** `GET /api/NormalWorkingReminder/GetProblemsNeedSixDayReminder?departmentID=422`

## 业务逻辑说明

### 提醒规则

1. **3 天提醒规则：** 问题发现日期距今 ≥3 天且<6 天，且未整改的问题，提醒给所属病区护士长
2. **6 天提醒规则：** 问题发现日期距今 ≥6 天，且未整改的问题，提醒给所属片区行政主任

### 责任人查找逻辑

1. **护士长查找：** 使用 JobCode=975 在 EmployeeToJob 表中查找对应部门的护士长
2. **片区主任查找：**
   - 通过 DepartmentList 表的 UpperLevelDepartmentID 字段递归查找上级部门
   - 找到 OrganizationType=3 且 Level=1 的片区部门
   - 在片区部门中查找行政主任

### 数据来源

- **问题数据：** 直接从 HierarchicalQCMainInfo 表查询指定天数前的质控维护记录
- **发现日期：** HierarchicalQCMainInfo.AssessDate（考核日期）
- **整改状态：** 通过 ProblemRectificationInfo 表判断是否已整改

### 消息发送

- 使用现有的 SendMessage 接口发送提醒消息
- 支持微信和电脑端同步提醒
- 消息内容包含问题详情、未整改天数等信息

## 错误处理

所有接口都包含完善的错误处理和参数验证：

- 空指针检查
- 参数有效性验证
- 异常捕获和日志记录
- 统一的错误响应格式

## 使用建议

1. **定时调用：** 建议每日上午 9 点调用 ExecuteReminder 接口进行自动提醒
2. **参数设置：** 首次使用时建议设置 onlyQuery=true 先查看需要提醒的问题
3. **监控日志：** 关注日志输出，及时发现和处理异常情况
4. **测试验证：** 在生产环境使用前，建议在测试环境充分验证功能

## 注意事项

1. 接口需要有效的用户会话（Session）
2. 确保相关的基础数据（部门、员工、职务）配置正确
3. 消息发送依赖于现有的消息服务配置
4. 建议在低峰期执行大批量提醒操作
