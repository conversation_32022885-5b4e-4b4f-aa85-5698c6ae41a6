# 常态工作控制提醒 API 文档

## 概述

本 API 提供常态工作过程控制问题的自动提醒功能，专为外部定时任务调用设计：

- 三天未整改问题提醒护士长
- 六天未整改问题提醒片区行政主任
- 支持微信和电脑端同步提醒
- 医院 ID 可空，自动从配置获取

## API 接口列表

### 1. 执行三天未整改提醒（提醒护士长）

**接口地址：** `POST /api/NormalWorkingReminder/ExecuteThreeDayReminder`

**请求参数：** 无需参数

**功能说明：** 自动查找发现问题 ≥3 天未整改的记录，提醒到所属病区护士长

### 2. 执行六天未整改提醒（提醒片区主任）

**接口地址：** `POST /api/NormalWorkingReminder/ExecuteSixDayReminder`

**请求参数：** 无需参数

**功能说明：** 自动查找发现问题 ≥6 天仍未整改的记录，提醒到所属片区行政主任处，微信、电脑同步有提醒

## 响应格式

所有接口都返回统一的响应格式：

```json
{
  "success": true,
  "message": "执行成功",
  "data": {
    "success": true,
    "message": "三天提醒执行完成，成功：2，失败：0",
    "totalProblemsChecked": 5,
    "problemsNeedReminder": 2,
    "successfulReminders": 2,
    "failedReminders": 0,
    "reminderDetails": [
      {
        "hierarchicalQCMainID": "xxx",
        "departmentID": 422,
        "departmentName": "1-1病区",
        "reminderType": 3,
        "receiverEmployeeIDs": ["EMP001"],
        "receiverEmployeeNames": ["张护士长"],
        "success": true,
        "message": "提醒发送成功"
      }
    ],
    "executionTime": "2024-06-29T10:00:00"
  }
}
```

## 业务逻辑说明

### 提醒规则

1. **三天提醒规则：** 问题发现日期距今 ≥3 天且<6 天，且未整改的问题，提醒给所属病区护士长
2. **六天提醒规则：** 问题发现日期距今 ≥6 天，且未整改的问题，提醒给所属片区行政主任

### 责任人查找逻辑

1. **护士长查找：** 使用 JobCode=975 在 EmployeeToJob 表中查找对应部门的护士长
2. **片区主任查找：**
   - 通过 DepartmentList 表的 UpperLevelDepartmentID 字段递归查找上级部门
   - 找到 OrganizationType=3 且 Level=1 的片区部门
   - 在片区部门中查找行政主任

### 数据来源

- **问题数据：** 直接从 HierarchicalQCMainInfo 表查询指定天数前的质控维护记录
- **发现日期：** HierarchicalQCMainInfo.AssessDate（考核日期）
- **整改状态：** 通过 ProblemRectificationInfo 表判断是否已整改
- **医院配置：** 从 appsettings.json 的 Configs.HospitalID 获取默认医院 ID

### 消息发送

- 使用现有的 SendMessage 接口发送提醒消息
- 支持微信和电脑端同步提醒
- 消息内容包含问题详情、未整改天数等信息

## 错误处理

所有接口都包含完善的错误处理和参数验证：

- 空指针检查
- 参数有效性验证
- 异常捕获和日志记录
- 统一的错误响应格式

## 使用建议

1. **定时调用：** 建议每日上午 9 点分别调用两个提醒接口
2. **调用顺序：** 先调用三天提醒，再调用六天提醒
3. **监控日志：** 关注日志输出，及时发现和处理异常情况
4. **测试验证：** 在生产环境使用前，建议在测试环境充分验证功能

## 注意事项

1. **无需 Session：** 接口专为外部定时任务设计，无需用户会话
2. **全局异常处理：** 接口使用全局异常处理，无需额外的错误处理
3. **配置依赖：** 确保 appsettings.json 中配置了正确的 HospitalID
4. **基础数据：** 确保相关的基础数据（部门、员工、职务）配置正确
5. **消息服务：** 消息发送依赖于现有的消息服务配置
6. **执行时机：** 建议在低峰期执行大批量提醒操作
