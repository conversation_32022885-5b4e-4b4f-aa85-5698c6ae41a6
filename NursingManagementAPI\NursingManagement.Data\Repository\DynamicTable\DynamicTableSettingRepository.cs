﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class DynamicTableSettingRepository : IDynamicTableSettingRepository
    {
        private NursingManagementDbContext _dbContext;
        private readonly SessionCommonServer _sessionCommonServer;

        public DynamicTableSettingRepository(
            NursingManagementDbContext dbContext
            , SessionCommonServer sessionCommonServer
            , IMemoryCache memoryCache
            )
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 根据表格ID获取表格列配置
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        public async Task<List<DynamicTableSettingView>> GetViewListByID(int dynamicTableListID)
        {
            var session =  _sessionCommonServer.GetSessionByCache();
            var list =await (from setting in _dbContext.DynamicTableSettingInfos
                        join column in _dbContext.DynamicTableColumnInfos
                        on setting.ColumnID equals column.ColumnID
                        where setting.DynamicTableListID == dynamicTableListID 
                        && setting.HospitalID == session.HospitalID && setting.DeleteFlag != "*"
                        && column.DefaultShowFlag == 1 && column.DeleteFlag != "*"
                        select new DynamicTableSettingView
                        {
                            DynamicTableListID = setting.DynamicTableListID,
                            ColumnID = setting.ColumnID,
                            Level = setting.Level,
                            ColumnShowName = column.ColumnShowName,
                            DefaultShowFlag = column.DefaultShowFlag == 1
                        }).ToListAsync();
            return list;
        }
        /// <summary>
        /// 根据表格ID获取所有表格列配置
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        public async Task<List<DynamicTableSettingView>> GetCommonViewByID(int dynamicTableListID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await (from setting in _dbContext.DynamicTableSettingInfos
                    join column in _dbContext.DynamicTableColumnInfos
                    on setting.ColumnID equals column.ColumnID
                    where column.DynamicTableListID == dynamicTableListID && setting.HospitalID == session.HospitalID && column.DeleteFlag != "*" && setting.DynamicTableListID == dynamicTableListID
                    && setting.DeleteFlag != "*"
                    select new DynamicTableSettingView
                    {
                        DynamicTableListID = setting.DynamicTableListID,
                        ColumnID = setting.ColumnID,
                        Level = setting.Level,
                        ColumnShowName = column.ColumnShowName,
                        DefaultShowFlag = column.DefaultShowFlag == 1
                    }).ToListAsync();
        }

    }
}
