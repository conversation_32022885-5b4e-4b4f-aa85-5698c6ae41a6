
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IExaminerScheduleService
    {
        /// <summary>
        /// 获取监考计划列表
        /// </summary>
        /// <param name="employeeID">监考人</param>
        /// <param name="examinationRecordID">考核计划ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        Task<List<ExaminerScheduleView>> GetExaminerScheduleList(string employeeID, string examinationRecordID, DateTime startDate, DateTime endDate);
        /// <summary>
        /// 保存监考计划
        /// </summary>
        /// <param name="saveExaminerScheduleView">保存的监考计划信息</param>
        /// <param name="employeeID">操作人工号</param>
        /// <returns></returns>
        Task<bool> SaveExaminerSchedule(SaveExaminerScheduleView saveExaminerScheduleView, string employeeID);
        /// <summary>
        /// 删除监考计划
        /// </summary>
        /// <param name="examinerScheduleID">监考计划ID</param>
        /// <param name="employeeID">操作人工号</param>
        /// <returns></returns>
        Task<bool> DeleteExaminerSchedule(string examinerScheduleID, string employeeID);
        /// <summary>
        /// 删除(跟当前考核计划相关的所有) 监考计划
        /// </summary>
        /// <param name="examinationRecordID">考核记录ID</param>
        /// <param name="employeeID">操作人工号</param>
        /// <returns></returns>
        Task<bool> DeleteExaminerScheduleByRecordID(string examinationRecordID, string employeeID);
    }
}