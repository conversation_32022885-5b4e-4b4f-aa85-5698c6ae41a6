﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划类别字典
    /// </summary>
    public interface IAnnualPlanTypeListRepository : ICacheRepository
    {
        /// <summary>
        /// 获取部门制定的分类
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanTypeListView>> GetAnnualPlanTypeListView(int departmentID);
        /// <summary>
        /// 获取分类
        /// </summary>
        /// <param name="typeID">主键ID</param>
        /// <returns></returns>
        Task<AnnualPlanTypeListInfo> GetAnnualPlanTypeByID(int typeID);
        /// <summary>
        /// 获取最大主键ID
        /// </summary>
        /// <returns></returns>
        Task<int> GetAnnualPlanTypeMaxID();
    }
}
