﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Hangfire;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Data;

namespace NursingManagement.Services
{
    /// <summary>
    /// 排班Service
    /// </summary>
    public class ShiftSchedulingService : IShiftSchedulingService
    {
        #region 定义变量
        private readonly ILogger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IEmployeeRepository _employeeRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly IShiftSchedulingRecordRepository _shiftSchedulingRecordRepository;
        private readonly IShiftSchedulingDetailRepository _shiftSchedulingDetailRepository;
        private readonly ISchedulingRequestRepository _schedulingRequestRepository;
        private readonly IPostRepository _postRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;
        private readonly IPostService _postService;
        private readonly IShiftSchedulingDetailMarkRepository _shiftSchedulingDetailMarkRepository;
        private readonly IShiftSchedulingEmployeeSortRepository _shiftSchedulingEmployeeSortRepository;
        private readonly IEmployeeService _employeeService;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IDepartmentVSDepartmentRepository _departmentVSDepartmentRepository;
        private readonly IRemainingRestDaysRepository _remainingRestDaysRepository;
        private readonly IPerpetualCalendarRepository _perpetualCalendarRepository;
        private readonly IAdministrationIconRepository _administrationIconRepository;
        private readonly IShiftSchedulingRuleRepository _shiftSchedulingRuleRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IMessageService _messageService;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IRouterListRepository _routerListRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly ISchedulingTemplateRecordRepository _schedulingTemplateRecordRepository;
        private readonly IEmployeeCapabilityLevelRepository _employeeCapabilityLevelRepository;
        private readonly IEmployeeEmploymentRecordRepository _employeeEmploymentRecordRepository;

        /// <summary>
        /// 借调到本部门
        /// </summary>
        private const int SECONDMENT_TYPE_1 = 1;
        /// <summary>
        /// 本部门借调出去
        /// </summary>
        private const int SECONDMENT_TYPE_2 = 2;
        /// <summary>
        /// 全院
        /// </summary>
        private const int DEPARTMENT_ID_999999 = 999999;
        /// <summary>
        /// 休假岗类型
        /// </summary>
        private readonly static string POST_TYPE_ID_4 = "4";
        /// <summary>
        /// 周排班
        /// </summary>
        private readonly static string SCHEDUING_TYPE_2 = "2";
        /// <summary>
        /// 产假岗位编号
        /// </summary>
        private readonly static int DEPARTMENT_POST_ID_184 = 184;
        /// <summary>
        /// 年休假岗位编号
        /// </summary>
        private readonly static int DEPARTMENT_POST_ID_186 = 186;
        /// <summary>
        /// 消毒供应中心ID
        /// </summary>
        private readonly static int DEPARTMENT_ID_397 = 397;
        #endregion

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="sessionCommonServer"></param>
        /// <param name="shiftSchedulingRecordRepository"></param>
        /// <param name="shiftSchedulingDetailRepository"></param>
        /// <param name="employeeRepository"></param>
        /// <param name="dictionaryService"></param>
        /// <param name="schedulingRequestRepository"></param>
        /// <param name="postRepository"></param>
        /// <param name="departmentPostRepository"></param>
        /// <param name="postService"></param>
        /// <param name="shiftSchedulingDetailMarkRepository"></param>
        /// <param name="shiftSchedulingEmployeeSortRepository"></param>
        /// <param name="employeeService"></param>
        /// <param name="departmentListRepository"></param>
        /// <param name="requestApiService"></param>
        /// <param name="departmentVSDepartmentRepository"></param>
        /// <param name="remainingRestDaysRepository"></param>
        /// <param name="perpetualCalendarRepository"></param>
        /// <param name="administrationIconRepository"></param>
        /// <param name="shiftSchedulingRuleRepository"></param>
        /// <param name="messageService"></param>
        /// <param name="settingDictionaryRepository"></param>
        /// <param name="employeePersonalDataRepository"></param>
        /// <param name="routerListRepository"></param>
        /// <param name="settingDictionaryService"></param>
        /// <param name="schedulingTemplateRecordRepository"></param>
        /// <param name="employeeCapabilityLevelRepository"></param>
        /// <param name="employeeEmploymentRecordRepository"></param>
        public ShiftSchedulingService(
            IUnitOfWork unitOfWork
            , SessionCommonServer sessionCommonServer
            , IShiftSchedulingRecordRepository shiftSchedulingRecordRepository
            , IShiftSchedulingDetailRepository shiftSchedulingDetailRepository
            , IEmployeeRepository employeeRepository
            , IDictionaryService dictionaryService
            , ISchedulingRequestRepository schedulingRequestRepository
            , IPostRepository postRepository
            , IDepartmentPostRepository departmentPostRepository
            , IPostService postService
            , IShiftSchedulingDetailMarkRepository shiftSchedulingDetailMarkRepository
            , IShiftSchedulingEmployeeSortRepository shiftSchedulingEmployeeSortRepository
            , IEmployeeService employeeService
            , IDepartmentListRepository departmentListRepository
            , IRequestApiService requestApiService
            , IDepartmentVSDepartmentRepository departmentVSDepartmentRepository
            , IRemainingRestDaysRepository remainingRestDaysRepository
            , IPerpetualCalendarRepository perpetualCalendarRepository
            , IAdministrationIconRepository administrationIconRepository
            , IShiftSchedulingRuleRepository shiftSchedulingRuleRepository
            , IMessageService messageService
            , ISettingDictionaryRepository settingDictionaryRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IRouterListRepository routerListRepository
            , ISettingDictionaryService settingDictionaryService
            , ISchedulingTemplateRecordRepository schedulingTemplateRecordRepository
            , IEmployeeCapabilityLevelRepository employeeCapabilityLevelRepository
            , IEmployeeEmploymentRecordRepository employeeEmploymentRecordRepository
        )
        {
            _unitOfWork = unitOfWork;
            _sessionCommonServer = sessionCommonServer;
            _shiftSchedulingRecordRepository = shiftSchedulingRecordRepository;
            _shiftSchedulingDetailRepository = shiftSchedulingDetailRepository;
            _employeeRepository = employeeRepository;
            _dictionaryService = dictionaryService;
            _schedulingRequestRepository = schedulingRequestRepository;
            _postRepository = postRepository;
            _departmentPostRepository = departmentPostRepository;
            _postService = postService;
            _shiftSchedulingDetailMarkRepository = shiftSchedulingDetailMarkRepository;
            _shiftSchedulingEmployeeSortRepository = shiftSchedulingEmployeeSortRepository;
            _employeeService = employeeService;
            _departmentListRepository = departmentListRepository;
            _requestApiService = requestApiService;
            _departmentVSDepartmentRepository = departmentVSDepartmentRepository;
            _remainingRestDaysRepository = remainingRestDaysRepository;
            _perpetualCalendarRepository = perpetualCalendarRepository;
            _administrationIconRepository = administrationIconRepository;
            _shiftSchedulingRuleRepository = shiftSchedulingRuleRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _messageService = messageService;
            _settingDictionaryRepository = settingDictionaryRepository;
            _routerListRepository = routerListRepository;
            _settingDictionaryService = settingDictionaryService;
            _schedulingTemplateRecordRepository = schedulingTemplateRecordRepository;
            _employeeCapabilityLevelRepository = employeeCapabilityLevelRepository;
            _employeeEmploymentRecordRepository = employeeEmploymentRecordRepository;
        }

        public async Task<ShiftSchedulingParameter> GetShiftSchedulingParameter(int departmentID, DateTime startDate, DateTime endDate)
        {
            var shiftSchedulingParameter = new ShiftSchedulingParameter();

            #region 获取底部日实时统计参数和浮动的个人月统计参数
            // 默认统计岗位
            var postList = await _postRepository.GetByCacheAsync();
            var departmentPostList = await _departmentPostRepository.GetAsync(departmentID);
            departmentPostList = departmentPostList.Where(m => m.StatusCode == "1").ToList();
            var dailyStatisticalPostIDs = departmentPostList.Where(m => m.DailyStatisticalMark != null && m.DailyStatisticalMark.Value).Select(m => m.PostID).Distinct().ToList();
            postList = postList.Where(m => dailyStatisticalPostIDs.Contains(m.PostID)).OrderBy(m => m.Sort).ToList();
            var dailyStatisticsPostCondition = new List<ShiftSchedulingCondition>();
            foreach (var post in postList)
            {
                var shiftSchedulingCondition = new ShiftSchedulingCondition()
                {
                    ConditionKey = post.PostName,
                    ConditionValue = departmentPostList.Where(m => m.PostID == post.PostID).Select(m => m.DepartmentPostID).ToList()
                };
                dailyStatisticsPostCondition.Add(shiftSchedulingCondition);
            }
            shiftSchedulingParameter.DailyStatisticsPostCondition = dailyStatisticsPostCondition;
            // 浮动的个人月统计参数
            var monthlyStatisticsPostCondition = new List<ShiftSchedulingCondition>();
            var monthlyStatisticalPostList = departmentPostList.Where(m => m.MonthlyStatisticalMark != null && m.MonthlyStatisticalMark.Value).ToList();
            foreach (var departmentPost in monthlyStatisticalPostList)
            {
                var shiftSchedulingCondition = new ShiftSchedulingCondition()
                {
                    ConditionKey = departmentPost.ShortName,
                    ConditionValue = departmentPost.DepartmentPostID
                };
                monthlyStatisticsPostCondition.Add(shiftSchedulingCondition);
            }
            shiftSchedulingParameter.MonthlyStatisticsPostCondition = monthlyStatisticsPostCondition;
            #endregion

            #region 获取排班特殊标记集合
            var groups = new string[] { departmentID.ToString(), DEPARTMENT_ID_999999.ToString() };
            var administrationIconList = await _dictionaryService.GetIconsByModuleType("SchedulingMark", groups);
            var shiftSchedulingMarkList = administrationIconList.Select(m =>
                new ShiftSchedulingMarkView()
                {
                    MarkID = m.AdministrationIconID,
                    Text = m.Text,
                    Icon = m.Icon,
                    Remark = m.Remark,
                    Color = m.Color,
                    BackGroundColor = m.BackGroundColor,
                    Sort = m.Sort,
                }
            ).ToList();
            shiftSchedulingParameter.ShiftSchedulingMarkList = shiftSchedulingMarkList;
            #endregion
            // 获取人员可休假天数集合
            var employeeRemainingRestDaysDict = await _remainingRestDaysRepository.GetEmployeeRestDaysDict(departmentID, startDate.Year, startDate.Month);
            shiftSchedulingParameter.EmployeeRemainingRestDaysDict = employeeRemainingRestDaysDict ?? new Dictionary<string, decimal?>();
            // 获取部门排班规则
            var shiftSchedulingRules = await _shiftSchedulingRuleRepository.GetShiftSchedulingRule(departmentID);
            shiftSchedulingParameter.ShiftSchedulingRuleDict = shiftSchedulingRules.ToDictionary(m => m.RuleID, m => m.RuleValue);
            // 获取部门排班模板
            var schedulingTemplateList = await _schedulingTemplateRecordRepository.GetRecordListByDepartmentID(departmentID);
            shiftSchedulingParameter.SchedulingTemplateList = schedulingTemplateList.Select(m => new KeyValueString() { Key = m.SchedulingTemplateRecordID, Value = m.TemplateName }).ToList();
            return shiftSchedulingParameter;
        }

        #region 保存排班
        /// <summary>
        /// 保存排班数据
        /// </summary>
        /// <param name="shiftSchedulingView">保存的排班数据</param>
        /// <returns></returns>
        public async Task<bool> SaveShiftSchedulingData(ShiftSchedulingView shiftSchedulingView)
        {
            if (shiftSchedulingView == null)
            {
                return false;
            }
            var changeList = new List<List<ShiftSchedulingChangeView>>();
            var shiftSchedulingRecordList = new List<Dictionary<string, object>>();
            var startAndEndDateList = GetStartAndEndDate(shiftSchedulingView.SchedulingType, shiftSchedulingView.StartDate.Value, shiftSchedulingView.EndDate.Value);
            // 新增
            if (shiftSchedulingView.ShiftSchedulingRecordIDs == null || shiftSchedulingView.ShiftSchedulingRecordIDs.Count <= 0)
            {
                foreach (var startAndEndDate in startAndEndDateList)
                {
                    shiftSchedulingRecordList.Add(new()
                    {
                        { "shiftSchedulingRecord",new ShiftSchedulingRecordInfo(){ StartDate = startAndEndDate["startDate"], EndDate = startAndEndDate["endDate"] } },
                        { "isAdd", true }
                    });
                }
            }
            else
            {
                (shiftSchedulingRecordList, shiftSchedulingView, changeList) = await DealOldSchedulingData(shiftSchedulingView, startAndEndDateList);
            }
            var shiftSchedulingRecords = new List<ShiftSchedulingRecordInfo>();
            foreach (var shiftSchedulingRecordDict in shiftSchedulingRecordList)
            {
                // 新增
                var shiftSchedulingRecord = (ShiftSchedulingRecordInfo)shiftSchedulingRecordDict["shiftSchedulingRecord"];
                if (bool.TryParse(shiftSchedulingRecordDict["isAdd"].ToString(), out bool isAdd) && isAdd)
                {
                    shiftSchedulingRecord.ShiftSchedulingRecordID = shiftSchedulingRecord.GetId();
                    shiftSchedulingRecord.DepartmentID = shiftSchedulingView.DepartmentID;
                    shiftSchedulingRecord.HospitalID = shiftSchedulingView.HospitalID;
                    shiftSchedulingRecord.SchedulingType = shiftSchedulingView.SchedulingType;
                    shiftSchedulingRecord.AutoFlag = shiftSchedulingView.AutoFlag;
                    shiftSchedulingRecord.StatusCode = shiftSchedulingView.StatusCode;
                    shiftSchedulingRecord.Remark = shiftSchedulingView.Remark;
                    shiftSchedulingRecord.DeleteFlag = "";
                    shiftSchedulingRecord.Add(shiftSchedulingView.EmployeeID);
                    shiftSchedulingRecord.Modify(shiftSchedulingView.EmployeeID);
                    await _unitOfWork.GetRepository<ShiftSchedulingRecordInfo>().InsertAsync(shiftSchedulingRecord);
                    // 周排班 跨月时 第二个月为新增时 如果前端没有传人员排序，取上个月的人员排序
                    if (shiftSchedulingView.SchedulingEmployeeSortList?.Count <= 0)
                    {
                        await SetLastSchedulingEmployeeSortAsync(shiftSchedulingView, shiftSchedulingRecord);
                    }
                }
                shiftSchedulingRecords.Add(shiftSchedulingRecord);
                await InsertShiftSchedulingDeatil(shiftSchedulingView, shiftSchedulingRecord);
                await InsertShiftSchedulingEmployeeSort(shiftSchedulingView.SchedulingEmployeeSortList, shiftSchedulingRecord.ShiftSchedulingRecordID, shiftSchedulingView.EmployeeID);
            }
            var flag = await _unitOfWork.SaveChangesAsync() >= 0;
            if (flag)
            {
                await ProcessBackgroundJobAfterSaveChange(shiftSchedulingView, changeList, shiftSchedulingRecords);
            }
            return flag;
        }
        /// <summary>
        /// 使用上一次的排班表员工排序
        /// </summary>
        /// <param name="shiftSchedulingView">保存的排班数据</param>
        /// <param name="shiftSchedulingRecord">旧的排班主记录</param>
        /// <returns></returns>
        private async Task SetLastSchedulingEmployeeSortAsync(ShiftSchedulingView shiftSchedulingView, ShiftSchedulingRecordInfo shiftSchedulingRecord)
        {
            shiftSchedulingView.SchedulingEmployeeSortList = [];
            var schedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(shiftSchedulingView.DepartmentID, shiftSchedulingRecord.StartDate.AddMonths(-1), shiftSchedulingRecord.StartDate.AddDays(-1));
            if (schedulingRecord != null)
            {
                var sortEmployeeList = await _shiftSchedulingEmployeeSortRepository.GetEmployeeListByScheduling(schedulingRecord.ShiftSchedulingRecordID);
                foreach (var sortEmployee in sortEmployeeList)
                {
                    shiftSchedulingView.SchedulingEmployeeSortList.Add(
                        new ShiftSchedulingEmployeeSortInfo()
                        {
                            EmployeeID = sortEmployee.EmployeeID,
                            Sort = sortEmployee.Sort
                        }
                    );
                }
            }
        }

        /// <summary>
        /// 保存排班之后开始后台执行通知和调用
        /// </summary>
        /// <param name="shiftSchedulingView">保存的排班数据</param>
        /// <param name="changeList">排班明细发生变更的内容明细</param>
        /// <param name="shiftSchedulingRecords">新增或调整的排班主记录</param>
        private async Task ProcessBackgroundJobAfterSaveChange(ShiftSchedulingView shiftSchedulingView, List<List<ShiftSchedulingChangeView>> changeList, List<ShiftSchedulingRecordInfo> shiftSchedulingRecords)
        {
            try
            {
                foreach (var shiftSchedulingRecord in shiftSchedulingRecords)
                {
                    //调用同步程序接口，向中间库表存放消息(先暂停回传CCC)
                    //BackgroundJob.Enqueue(() => ShiftSchedulingMessageToCareDirectAsync("ShiftSchedulingMessageToCareDirect", shiftSchedulingRecord));
                    if (shiftSchedulingRecord.DepartmentID == 397)
                    {
                        //排班信息回传CSSD
                        BackgroundJob.Enqueue(() => ShiftSchedulingMessageToCareDirectAsync("SendShiftSchedulingToNM", shiftSchedulingRecord));
                        //await _requestApiService.RequestAPI("SendShiftSchedulingToNM", ListToJson.ToJson(shiftSchedulingRecord));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"发布排班调用护理管理失败{ex}");
            }
            try
            {
                foreach (var changeItems in changeList)
                {
                    if (changeItems.Count <= 0)
                    {
                        continue;
                    }
                    //发送排班改变信息
                    BackgroundJob.Enqueue(() => NotifyShiftSchedulingChangeAsync(changeItems, shiftSchedulingView.DepartmentID));
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"发布排班信息变更通知异常：{ex}");
            }
        }
        /// <summary>
        /// 处理旧的排班数据
        /// </summary>
        /// <param name="shiftSchedulingView">需要保存的排班数据</param>
        /// <param name="startAndEndDateList">排班的开始结束时间区间集合</param>
        /// <returns></returns>
        private async Task<Tuple<List<Dictionary<string, object>>, ShiftSchedulingView, List<List<ShiftSchedulingChangeView>>>> DealOldSchedulingData(ShiftSchedulingView shiftSchedulingView, List<Dictionary<string, DateTime>> startAndEndDateList)
        {
            var ret = new List<Dictionary<string, object>>();
            var changeList = new List<List<ShiftSchedulingChangeView>>();
            foreach (var shiftSchedulingRecordID in shiftSchedulingView.ShiftSchedulingRecordIDs)
            {
                var isAdd = false;
                var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByID(shiftSchedulingRecordID);

                // 如果是从发布状态改为暂存状态，走新增逻辑，保留上次已发布的排班信息
                if (shiftSchedulingRecord.StatusCode == "1" && shiftSchedulingView.StatusCode == "0")
                {
                    isAdd = true;
                    shiftSchedulingRecord.Modify(shiftSchedulingView.EmployeeID);
                    ret.Add(new Dictionary<string, object>() {
                        { "shiftSchedulingRecord",new ShiftSchedulingRecordInfo(){ StartDate = shiftSchedulingRecord.StartDate, EndDate = shiftSchedulingRecord.EndDate } },
                        { "isAdd", isAdd }
                    });
                    shiftSchedulingView = await CopySchedulingDataToShiftSchedulingView(shiftSchedulingView, shiftSchedulingRecordID);
                    continue;
                }
                // 如果是暂存改已发布 将上一个已发布的删除
                if (shiftSchedulingRecord.StatusCode == "0" && shiftSchedulingView.StatusCode == "1")
                {
                    await DeleteOldSchedulingData(shiftSchedulingView, shiftSchedulingRecord);
                }
                // 如果已发布，但是又有细微调整后重新发布，获取通知员工 排班变更明细
                if (shiftSchedulingRecord.StatusCode == "1" && shiftSchedulingView.StatusCode == "1")
                {
                    var changeInnerList = await GetShiftSchedulingChangeDatasAsync(shiftSchedulingView, shiftSchedulingRecordID);
                    changeList.Add(changeInnerList);
                }
                // 修改
                shiftSchedulingRecord.SchedulingType = shiftSchedulingView.SchedulingType;
                shiftSchedulingRecord.AutoFlag = shiftSchedulingView.AutoFlag;
                shiftSchedulingRecord.StatusCode = shiftSchedulingView.StatusCode;
                shiftSchedulingRecord.Remark = shiftSchedulingView.Remark;
                shiftSchedulingRecord.Modify(shiftSchedulingView.EmployeeID);
                await DeleteSchedulingDetailAndDetailMark(shiftSchedulingRecordID, shiftSchedulingView.StartDate, shiftSchedulingView.EndDate, shiftSchedulingView.EmployeeID);
                ret.Add(new Dictionary<string, object>() {
                    { "shiftSchedulingRecord",shiftSchedulingRecord },
                    { "isAdd", isAdd }
                });
                var startAndEndDate = startAndEndDateList.Find(m => m["startDate"] == shiftSchedulingRecord.StartDate);
                if (startAndEndDate != null)
                {
                    startAndEndDateList.Remove(startAndEndDate);
                }
            }
            // 如果跨月，且有一个月是新增，则添加
            if (shiftSchedulingView.SchedulingType == SCHEDUING_TYPE_2 && startAndEndDateList.Count == 1)
            {
                ret.Add(new()
                {
                    {
                        "shiftSchedulingRecord",new ShiftSchedulingRecordInfo()
                        {
                            StartDate = startAndEndDateList[0]["startDate"],
                            EndDate = startAndEndDateList[0]["endDate"]
                        }
                    },
                    { "isAdd", true }
                });
            }
            return Tuple.Create(ret, shiftSchedulingView, changeList);
        }
        /// <summary>
        /// 删除旧的排班表数据
        /// </summary>
        /// <param name="shiftSchedulingView">保存的排班数据</param>
        /// <param name="shiftSchedulingRecord">旧的排班主记录</param>
        /// <returns></returns>
        private async Task DeleteOldSchedulingData(ShiftSchedulingView shiftSchedulingView, ShiftSchedulingRecordInfo shiftSchedulingRecord)
        {
            var oldSchedulingData = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(shiftSchedulingView.DepartmentID, shiftSchedulingRecord.StartDate, shiftSchedulingRecord.EndDate);
            if (oldSchedulingData != null)
            {
                oldSchedulingData.Delete(shiftSchedulingView.EmployeeID);
                await DeleteSchedulingDetailAndDetailMark(oldSchedulingData.ShiftSchedulingRecordID, shiftSchedulingView.StartDate, shiftSchedulingView.EndDate, shiftSchedulingView.EmployeeID);
            }
        }
        /// <summary>
        /// 获取排班表改变的信息
        /// </summary>
        /// <param name="shiftSchedulingView">保存的排班数据</param>
        /// <param name="shiftSchedulingRecordID">旧的排班主记录的主键ID</param>
        /// <returns>List<List<ShiftSchedulingChangeView>> 多个排班表中（每个人的排班信息变更明细）</returns>
        private async Task<List<ShiftSchedulingChangeView>> GetShiftSchedulingChangeDatasAsync(ShiftSchedulingView shiftSchedulingView, string shiftSchedulingRecordID)
        {
            var schedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecordID, null, null);
            if (schedulingDetails.Count <= 0)
            {
                return new List<ShiftSchedulingChangeView>();
            }
            // 周排班的话，对比明细数据的时候进行旧的明细数据的过滤
            if (shiftSchedulingView.SchedulingType == SCHEDUING_TYPE_2)
            {
                schedulingDetails = schedulingDetails.Where(m => m.SchedulingDate >= shiftSchedulingView.StartDate && m.SchedulingDate <= shiftSchedulingView.EndDate).ToList();
            }
            //修改的排班明细信息
            var newShiftSchedulingDetails = shiftSchedulingView.ShiftSchedulingDetails.Where(m => m.NoonPost != null)
                        .SelectMany(detail =>
                            detail.NoonPost.Select(kv => new ShiftSchedulingDetailInfo
                            {
                                EmployeeID = detail.EmployeeID,
                                NoonType = kv.Key,
                                DepartmentPostID = kv.Value.DepartmentPostID,
                                SchedulingDate = detail.SchedulingDate.Value,
                            }
                            )).ToList();
            // 可能修改的排班信息（非新增和删除）
            var changeList = (from oldDetail in schedulingDetails
                              join newDetail in newShiftSchedulingDetails
                              on new { oldDetail.EmployeeID, oldDetail.SchedulingDate, oldDetail.NoonType } equals new { newDetail.EmployeeID, newDetail.SchedulingDate, newDetail.NoonType }
                              select new ShiftSchedulingChangeView
                              {
                                  EmployeeID = newDetail.EmployeeID,
                                  NoonType = newDetail.NoonType,
                                  OldDepartmentPostID = oldDetail.DepartmentPostID,
                                  NewDepartmentPostID = newDetail.DepartmentPostID,
                                  SchedulingDate = newDetail.SchedulingDate,
                              }).ToList();
            //新增的部分排班
            if (newShiftSchedulingDetails.Count > schedulingDetails.Count)
            {
                changeList = GetChangeSchedulingDetail(newShiftSchedulingDetails, changeList);
            }
            // 删除的部分派班
            if (newShiftSchedulingDetails.Count < schedulingDetails.Count)
            {
                changeList = GetChangeSchedulingDetail(schedulingDetails, changeList);
            }
            //筛选出发生岗位变更的记录
            return changeList.Where(m => m.OldDepartmentPostID != m.NewDepartmentPostID).ToList();
        }
        /// <summary>
        /// 获取发生排班改变的数据
        /// </summary>
        /// <param name="schedulingDetails">新旧排班明细</param>
        /// <param name="changeList">新旧排班对比集合</param>
        /// <returns>新旧排班对比集合</returns>
        private static List<ShiftSchedulingChangeView> GetChangeSchedulingDetail(List<ShiftSchedulingDetailInfo> schedulingDetails, List<ShiftSchedulingChangeView> changeList)
        {
            var addSchedulingDetails = schedulingDetails.Where(m => !changeList.Exists(n => m.EmployeeID == n.EmployeeID && m.SchedulingDate == n.SchedulingDate && m.NoonType == n.NoonType)).Select(m =>
                new ShiftSchedulingChangeView
                {
                    EmployeeID = m.EmployeeID,
                    NoonType = m.NoonType,
                    OldDepartmentPostID = m.DepartmentPostID,
                    NewDepartmentPostID = null,
                    SchedulingDate = m.SchedulingDate,
                }
                ).ToList();
            changeList.AddRange(addSchedulingDetails);
            return changeList;
        }

        /// <summary>
        /// 发送消息通知排班信息发生改变
        /// </summary>
        /// <param name="changeItems">发生改变的排班详细数据</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<bool> NotifyShiftSchedulingChangeAsync(List<ShiftSchedulingChangeView> changeItems, int departmentID)
        {
            #region 获取字典配置
            var deptPosts = await _departmentPostRepository.GetAsync(departmentID);
            deptPosts = deptPosts.Where(m => m.StatusCode == "1").ToList();
            var param = new AdministrationParams()
            {
                SettingTypeCode = "JobPositions",
                ReferenceTypeCode = "B0108"
            };
            var noonTypeList = await _dictionaryService.GetAdministrationDict(param);
            //获取人员信息
            var employeeIDs = changeItems.Select(m => m.EmployeeID).ToList();
            var employeeList = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            #endregion
            var group = changeItems.GroupBy(m => m.EmployeeID).ToList();
            foreach (var item in group)
            {
                string message = GetMessageContent(deptPosts, noonTypeList, employeeList, item);
                var pathAndClientList = await GetRouterPathAsync();
                if (pathAndClientList == null)
                {
                    continue;
                }
                //根据配置分别向PC和Mobile段发送通知
                foreach (var pathAndClient in pathAndClientList)
                {
                    await _messageService.SendMessage(CreateMessageView(item.Key, message, pathAndClient.Path, pathAndClient.ClientType));
                }
            }
            return true;

        }
        /// <summary>
        /// 根据配置获取消息通知的跳转地址
        /// </summary>
        /// <returns></returns>
        private async Task<List<(string Path, int ClientType)>> GetRouterPathAsync()
        {
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "ShiftManagement",
                SettingTypeCode = "MessageSkipPathToRoute",
                SettingTypeValue = "SchedulingTable",
            };
            var routerListIDSetting = await _settingDictionaryRepository.GetSettingValue(settingParams);
            // 取第一个
            if (!int.TryParse(routerListIDSetting, out var routerListID))
            {
                _logger.Error($"发送审批继续通知失败，找不到SettingType=ProveCategoryToRoute配置");
                return null;
            }
            var routers = await _routerListRepository.GetInfosByRouterListID(routerListID);
            // 获取跳转 path，PC端、移动端
            var routerPaths = routers.Select(r => (r.Path, r.ClientType)).ToList();

            return routerPaths;
        }
        /// <summary>
        /// 拼接获取通知文本内容
        /// </summary>
        /// <param name="deptPosts">部门岗位字典</param>
        /// <param name="noonTypeList">午别字典</param>
        /// <param name="employeeList">使用到的人员字典</param>
        /// <param name="item">分组后的排班改变数据</param>
        /// <returns></returns>
        private string GetMessageContent(List<DepartmentPostInfo> deptPosts, List<SelectOptionsView> noonTypeList, Dictionary<string, string> employeeList, IGrouping<string, ShiftSchedulingChangeView> item)
        {
            var viewList = item.OrderBy(m => m.SchedulingDate).ThenBy(m => m.NoonType).ToList();

            var employeeName = employeeList.TryGetValue(item.Key, out var name) ? $"{name}老师" : "老师";
            var notifyContent = viewList.Select(m =>
                $"{m.SchedulingDate:yyyy-MM-dd} {GetNoonName(m.NoonType)}：{GetAdjustContent(m.OldDepartmentPostID, m.NewDepartmentPostID)}；")
                .Aggregate((a, b) => a + b);
            var message = $"温馨提醒:{employeeName}，您的排班发生改变，具体为:{notifyContent}";

            return message;

            //获取岗位名称
            string GetPostName(int deptPostID) => deptPosts.Find(post => post.DepartmentPostID == deptPostID)?.ShortName ?? deptPostID.ToString();
            //获取午别名称
            string GetNoonName(string noonType) => noonTypeList.Find(n => n.Value.Equals(noonType))?.Label ?? "";
            //獲取崗位調整信息
            string GetAdjustContent(int? oldDeptPostID, int? newDeptPostID)
            {
                return (newDeptPostID.HasValue, oldDeptPostID.HasValue) switch
                {
                    (true, false) => $"新增【{GetPostName(newDeptPostID.Value)}】",
                    (false, true) => $"取消【{GetPostName(oldDeptPostID.Value)}】",
                    _ => $"【{GetPostName(oldDeptPostID.Value)}】=>【{GetPostName(newDeptPostID.Value)}】"
                };
            }
        }
        /// <summary>
        /// 創建messageView對象(发送mq和公众号通知）
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="message">通知的消息内容</param>
        /// <param name="path">跳转路由地址</param>
        /// <param name="clientType">客户端类型</param>
        /// <returns></returns>
        private static MessageView CreateMessageView(string employeeID, string message, string path = null, int clientType = 1)
        {
            var messageView = new MessageView
            {
                MessageTools = [MessageTool.MQ, MessageTool.Wechat],
                EmployeeID = employeeID,
                ClientType = clientType,
                MessageCondition = new MessageConditionView
                {
                    // 移动端与PC端使用不同的交换机
                    MQExchangeName = "MQNotification",
                    MQRoutingKey = employeeID,
                    Message = message,
                    Url = path,
                    ClientType = clientType,
                }
            };
            return messageView;
        }
        /// <summary>
        ///  复制排班数据到ShiftSchedulingView 方便后续保存
        /// </summary>
        /// <param name="shiftSchedulingView"></param>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <returns></returns>
        private async Task<ShiftSchedulingView> CopySchedulingDataToShiftSchedulingView(ShiftSchedulingView shiftSchedulingView, string shiftSchedulingRecordID)
        {
            // 如果前端没有传人员排序，复制一份排序
            if (shiftSchedulingView.SchedulingEmployeeSortList?.Count <= 0)
            {
                shiftSchedulingView.SchedulingEmployeeSortList = [];
                var sortEmployeeList = await _shiftSchedulingEmployeeSortRepository.GetEmployeeListByScheduling(shiftSchedulingRecordID);
                foreach (var sortEmployee in sortEmployeeList)
                {
                    shiftSchedulingView.SchedulingEmployeeSortList.Add(
                        new ShiftSchedulingEmployeeSortInfo()
                        {
                            EmployeeID = sortEmployee.EmployeeID,
                            Sort = sortEmployee.Sort
                        }
                    );
                }
            }
            if (shiftSchedulingView.SchedulingType != SCHEDUING_TYPE_2)
            {
                return shiftSchedulingView;
            }
            // 如果是周排班，需要把本月的明细复制一份(除前端传的排班明细）
            var schedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecordID, null, null);
            if (schedulingDetails.Count <= 0)
            {
                return shiftSchedulingView;
            }
            var markList = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(shiftSchedulingRecordID);
            var schedulingDates = schedulingDetails.Select(m => m.SchedulingDate).Distinct().ToList();
            var employeeIDs = schedulingDetails.Select(m => m.EmployeeID).Distinct().ToList();
            foreach (var schedulingDate in schedulingDates)
            {
                if (shiftSchedulingView.ShiftSchedulingDetails.Find(m => m.SchedulingDate.Value.Date == schedulingDate.Date) != null)
                {
                    continue;
                }
                foreach (var employeeID in employeeIDs)
                {
                    var details = schedulingDetails.Where(m => m.SchedulingDate.Date == schedulingDate.Date && m.EmployeeID == employeeID).ToList();
                    if (details.Count <= 0)
                    {
                        continue;
                    }
                    var noonPost = details.ToDictionary(m => m.NoonType, m => new ShiftSchedulingPost() { DepartmentPostID = m.DepartmentPostID });
                    var mark = markList.Where(m => m.SchedulingDate.Date == schedulingDate.Date && m.EmployeeID == employeeID)
                                        .Select(m => new ShiftSchedulingMarkView() { MarkID = m.MarkID }).ToList();
                    shiftSchedulingView.ShiftSchedulingDetails.Add(new ShiftSchedulingDetailView()
                    {
                        EmployeeID = employeeID,
                        MarkList = mark,
                        NoonPost = noonPost,
                        SchedulingDate = schedulingDate,
                        ShiftSchedulingRecordID = shiftSchedulingRecordID
                    });
                }
            }
            return shiftSchedulingView;
        }
        /// <summary>
        /// 删除排班明细和标记
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task DeleteSchedulingDetailAndDetailMark(string shiftSchedulingRecordID, DateTime? startDate, DateTime? endDate, string employeeID)
        {
            // 删除旧的明细
            var oldSchedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecordID, startDate, endDate);
            if (oldSchedulingDetails.Count > 0)
            {
                oldSchedulingDetails.ForEach(detail => detail.Delete(employeeID));
            }
            // 删除旧的mark
            var oldMarkList = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(shiftSchedulingRecordID, startDate, endDate);
            if (oldMarkList.Count > 0)
            {
                oldMarkList.ForEach(mark => mark.Delete(employeeID));
            }
        }

        /// <summary>
        /// 发送信息到中间程序
        /// </summary>
        /// <param name="shiftSchedulingRecord"></param>
        /// <returns></returns>
        public async Task ShiftSchedulingMessageToCareDirectAsync(string settingCode, ShiftSchedulingRecordInfo shiftSchedulingRecord)
        {
            await _requestApiService.RequestAPI(settingCode, ListToJson.ToJson(shiftSchedulingRecord));
        }

        public async Task<bool> UpdateSchedulingEmployeeSort(List<ShiftSchedulingEmployeeSortInfo> shiftSchedulingEmployeeSortInfos, string employeeID)
        {
            await InsertShiftSchedulingEmployeeSort(shiftSchedulingEmployeeSortInfos, shiftSchedulingEmployeeSortInfos[0].ShiftSchedulingRecordID, employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 插入排班明细数据
        /// </summary>
        /// <param name="shiftSchedulingView"></param>
        /// <param name="shiftSchedulingRecord"></param>
        /// <returns></returns>
        private async Task InsertShiftSchedulingDeatil(ShiftSchedulingView shiftSchedulingView, ShiftSchedulingRecordInfo shiftSchedulingRecord)
        {
            if (shiftSchedulingView.ShiftSchedulingDetails?.Count <= 0)
            {
                return;
            }
            var shiftSchedulingDetails = new List<ShiftSchedulingDetailInfo>();
            var details = shiftSchedulingView.ShiftSchedulingDetails.Where(m => m.SchedulingDate >= shiftSchedulingRecord.StartDate && m.SchedulingDate <= shiftSchedulingRecord.EndDate).ToList();
            foreach (var shiftSchedulingDetail in details)
            {
                if (shiftSchedulingDetail.NoonPost?.Count > 0)
                {
                    foreach (var noonType in shiftSchedulingDetail.NoonPost.Keys)
                    {
                        var shiftSchedulingDetailInfo = new ShiftSchedulingDetailInfo()
                        {
                            ShiftSchedulingRecordID = shiftSchedulingRecord.ShiftSchedulingRecordID,
                            HospitalID = shiftSchedulingView.HospitalID,
                            DepartmentPostID = shiftSchedulingDetail.NoonPost[noonType].DepartmentPostID,
                            EmployeeID = shiftSchedulingDetail.EmployeeID,
                            SchedulingDate = shiftSchedulingDetail.SchedulingDate.Value,
                            NoonType = noonType
                        };
                        shiftSchedulingDetailInfo.ShiftSchedulingDetailID = shiftSchedulingDetailInfo.GetId();
                        shiftSchedulingDetailInfo.Add(shiftSchedulingView.EmployeeID);
                        shiftSchedulingDetailInfo.Modify(shiftSchedulingView.EmployeeID);
                        shiftSchedulingDetails.Add(shiftSchedulingDetailInfo);
                    }
                }

                // 插入marks
                if (shiftSchedulingDetail.MarkList?.Count > 0)
                {
                    var shiftSchedulingDetailMarkList = new List<ShiftSchedulingDetailMarkInfo>();
                    foreach (var mark in shiftSchedulingDetail.MarkList)
                    {
                        var shiftSchedulingDetailMarkInfo = new ShiftSchedulingDetailMarkInfo()
                        {
                            ShiftSchedulingRecordID = shiftSchedulingRecord.ShiftSchedulingRecordID,
                            HospitalID = shiftSchedulingView.HospitalID,
                            EmployeeID = shiftSchedulingDetail.EmployeeID,
                            SchedulingDate = shiftSchedulingDetail.SchedulingDate.Value,
                            MarkID = mark.MarkID,
                            MarkValue = mark.MarkValue,
                            AddDateTime = DateTime.Now,
                            AddEmployeeID = shiftSchedulingView.EmployeeID,
                            ModifyDateTime = DateTime.Now,
                            ModifyEmployeeID = shiftSchedulingView.EmployeeID,
                            DeleteFlag = ""
                        };
                        shiftSchedulingDetailMarkInfo.ShiftSchedulingDetailMarkID = shiftSchedulingDetailMarkInfo.GetId();
                        shiftSchedulingDetailMarkList.Add(shiftSchedulingDetailMarkInfo);
                    }
                    // 去重，前端标记会重复，找不到原因，这里保存前去重，保证数据库不会重复
                    shiftSchedulingDetailMarkList = shiftSchedulingDetailMarkList.GroupBy(m => new { m.EmployeeID, m.SchedulingDate, m.MarkID }).Select(m => m.FirstOrDefault()).ToList();
                    await _unitOfWork.GetRepository<ShiftSchedulingDetailMarkInfo>().InsertAsync(shiftSchedulingDetailMarkList);
                }
            }
            await _unitOfWork.GetRepository<ShiftSchedulingDetailInfo>().InsertAsync(shiftSchedulingDetails);
        }

        /// <summary>
        /// 插入排班人员排序
        /// </summary>
        /// <param name="shiftSchedulingView"></param>
        /// <returns></returns>
        private async Task InsertShiftSchedulingEmployeeSort(List<ShiftSchedulingEmployeeSortInfo> schedulingEmployeeSortList, string shiftSchedulingRecordID, string employeeID)
        {
            // 顺序不变时 前端不传SchedulingEmployeeSortList
            if (schedulingEmployeeSortList == null || schedulingEmployeeSortList.Count <= 0)
            {
                return;
            }
            // 删除旧的人员排序
            var schedulingEmployeeList = await _shiftSchedulingEmployeeSortRepository.GetEmployeeListByScheduling(shiftSchedulingRecordID);
            if (schedulingEmployeeList.Count > 0)
            {
                schedulingEmployeeList.ForEach(employee => employee.Delete(employeeID));
            }
            var newSchedulingEmployeeSortList = new List<ShiftSchedulingEmployeeSortInfo>();
            foreach (var schedulingEmployeeSort in schedulingEmployeeSortList)
            {
                schedulingEmployeeSort.ShiftSchedulingRecordID = shiftSchedulingRecordID;
                schedulingEmployeeSort.Add(employeeID);
                schedulingEmployeeSort.Modify(employeeID);
                schedulingEmployeeSort.DeleteFlag = "";
                newSchedulingEmployeeSortList.Add(schedulingEmployeeSort);
            }
            await _unitOfWork.GetRepository<ShiftSchedulingEmployeeSortInfo>().InsertAsync(newSchedulingEmployeeSortList);
        }
        #endregion

        #region 查询排班
        public async Task<Dictionary<string, object>> GetShiftSchedulingByEmployeeID(int departmentID, DateTime startDate, DateTime endDate, string employeeID)
        {
            var shiftSchedulingData = new Dictionary<string, object>();
            (var departmentPostList, var shiftSchedulingDetails, var shiftSchedulingDetailMarks) = await GetShiftSchedulingInfoByEmployeeID(departmentID, startDate, endDate, employeeID);
            if (shiftSchedulingDetails.Count <= 0)
            {
                return shiftSchedulingData;
            }
            var schedulingRequestList = await _schedulingRequestRepository.GetSchedulingRequestByDate(departmentID, startDate, endDate);
            var groups = new string[] { departmentID.ToString(), DEPARTMENT_ID_999999.ToString() };
            var administrationIconList = await _dictionaryService.GetIconsByModuleType("SchedulingMark", groups);
            // 按时间分组
            var schedulingDates = shiftSchedulingDetails.Select(m => m.SchedulingDate).Distinct().ToList();
            foreach (var schedulingDate in schedulingDates)
            {
                var schedulingDetails = shiftSchedulingDetails.Where(m => m.SchedulingDate == schedulingDate).OrderBy(m => m.NoonType).ToList();
                var schedulingData = await CreateShiftSchedulingData(schedulingDetails, shiftSchedulingDetailMarks, administrationIconList, departmentPostList, departmentID, schedulingRequestList);
                shiftSchedulingData.Add(schedulingDate.ToString("yyyy-MM-dd"), schedulingData);
            }
            return shiftSchedulingData;
        }

        /// <summary>
        /// 获取个人排班相关信息
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<Tuple<List<PostSelectOptionsView>, List<ShiftSchedulingDetailInfo>, List<ShiftSchedulingDetailMarkInfo>>> GetShiftSchedulingInfoByEmployeeID(int departmentID, DateTime startDate, DateTime endDate, string employeeID)
        {
            var departmentPostList = await _dictionaryService.GetDepartmentPostDict(departmentID, true);
            // 只查看已发布的排班
            var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startDate, endDate);
            var shiftSchedulingDetails = new List<ShiftSchedulingDetailInfo>();
            var shiftSchedulingDetailMarks = new List<ShiftSchedulingDetailMarkInfo>();
            if (shiftSchedulingRecord != null)
            {
                // 获取本部门的排班
                var schedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startDate, endDate);
                schedulingDetails = schedulingDetails.Where(m => m.EmployeeID == employeeID).ToList();
                if (schedulingDetails.Count > 0)
                {
                    shiftSchedulingDetails.AddRange(schedulingDetails);
                }
                var shiftSchedulingMarks = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID);
                shiftSchedulingMarks = shiftSchedulingMarks.Where(m => m.EmployeeID == employeeID).ToList();
                if (shiftSchedulingMarks.Count > 0)
                {
                    shiftSchedulingDetailMarks.AddRange(shiftSchedulingMarks);
                }
            }
            #region 获取借调部门已发布的排班信息和借调部门的部门岗位字典
            // 获取借调部门的排班
            var secondedList = await _employeeService.GetSecondmenRecordByDepartmentIDAndDate(SECONDMENT_TYPE_2, departmentID, startDate, endDate);
            secondedList = secondedList.Where(m => m.EmployeeID == employeeID).ToList();
            if (secondedList.Count > 0)
            {
                var tuple = await GetSchedulingDetailsAndDepartmentPostList(secondedList, startDate, endDate, "SecondmentDepartmentID");
                if (tuple.Item1.Count > 0)
                {
                    shiftSchedulingDetails.AddRange(tuple.Item1);
                }
                if (tuple.Item2.Count > 0)
                {
                    shiftSchedulingDetailMarks.AddRange(tuple.Item2);
                }
                if (tuple.Item3.Count > 0)
                {
                    departmentPostList.AddRange(tuple.Item3);
                }
            }
            #endregion
            return Tuple.Create(departmentPostList, shiftSchedulingDetails, shiftSchedulingDetailMarks);
        }

        /// <summary>
        /// 获取排班信息
        /// </summary>
        /// <param name="schedulingDetails"></param>
        /// <param name="shiftSchedulingDetailMarks"></param>
        /// <param name="administrationIconList"></param>
        /// <param name="departmentPostList"></param>
        /// <param name="departmentID"></param>
        /// <param name="schedulingRequestList"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, object>> CreateShiftSchedulingData(List<ShiftSchedulingDetailInfo> schedulingDetails, List<ShiftSchedulingDetailMarkInfo> shiftSchedulingDetailMarks, List<AdministrationIconInfo> administrationIconList, List<PostSelectOptionsView> departmentPostList, int departmentID, List<SchedulingRequestRecordInfo> schedulingRequestList)
        {
            var markList = GetDetailMarkList(shiftSchedulingDetailMarks, administrationIconList, schedulingDetails[0].SchedulingDate.Date, schedulingDetails[0].EmployeeID);

            var schedulingData = new Dictionary<string, object>()
            {
                { "markList" ,markList },
            };
            var noonPost = new Dictionary<string, ShiftSchedulingPost>();
            foreach (var schedulingDetail in schedulingDetails)
            {
                var departmentPost = departmentPostList.Find(m => m.Value.ToString() == schedulingDetail.DepartmentPostID.ToString());
                // 如果找不到岗位，可能该岗位状态关闭或删除了，根据部门岗位ID查找
                departmentPost ??= await _dictionaryService.GetDepartmentPostDictByID(schedulingDetail.DepartmentPostID, departmentID);
                // 如果根据部门岗位ID查找也查不到 直接跳过
                if (departmentPost == null)
                {
                    continue;
                }
                var shiftSchedulingPost = new ShiftSchedulingPost()
                {
                    PostType = departmentPost.Type,
                    DepartmentPostID = (int)departmentPost.Value,
                    DepartmentPostName = departmentPost.Label,
                    DepartmentPostShortName = departmentPost.LocalLabel,
                    SchedulingRequestFlag = GetSchedulingRequestFlag(schedulingRequestList, schedulingDetail, departmentPost.Type),
                    Color = departmentPost.Color,
                    BackGroundColor = departmentPost.BackGroundColor,
                };
                noonPost.TryAdd(schedulingDetail.NoonType, shiftSchedulingPost);
            }
            schedulingData.Add("noonPost", noonPost);
            return schedulingData;
        }
        /// <summary>
        /// 获取明细标记列表
        /// </summary>
        /// <param name="shiftSchedulingDetailMarks"></param>
        /// <param name="administrationIconList"></param>
        /// <param name="schedulingDate"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private List<ShiftSchedulingMarkView> GetDetailMarkList(List<ShiftSchedulingDetailMarkInfo> shiftSchedulingDetailMarks, List<AdministrationIconInfo> administrationIconList, DateTime schedulingDate, string employeeID)
        {
            var markList = new List<ShiftSchedulingMarkView>();
            // 获取排班标记
            var shiftSchedulingDetailMarkList = shiftSchedulingDetailMarks.Where(m => m.SchedulingDate.Date == schedulingDate && m.EmployeeID == employeeID).ToList();
            // 去重，前端标记会重复，找不到原因，这里返回前端时去重，保证数据库历史重复数据不会到前端重复
            shiftSchedulingDetailMarkList = shiftSchedulingDetailMarkList.GroupBy(m => new { m.EmployeeID, m.SchedulingDate, m.MarkID }).Select(m => m.FirstOrDefault()).ToList();
            foreach (var shiftSchedulingDetailMark in shiftSchedulingDetailMarkList)
            {
                var administrationIcon = administrationIconList.Find(m => m.AdministrationIconID == shiftSchedulingDetailMark.MarkID);
                if (administrationIcon == null)
                {
                    continue;
                }
                markList.Add(new ShiftSchedulingMarkView()
                {
                    MarkID = shiftSchedulingDetailMark.MarkID,
                    Text = administrationIcon.Text,
                    Icon = administrationIcon.Icon,
                    Remark = administrationIcon.Remark,
                    Color = administrationIcon.Color,
                    BackGroundColor = administrationIcon.BackGroundColor,
                    Sort = administrationIcon.Sort,
                    MarkValue = shiftSchedulingDetailMark.MarkValue
                });
            }
            return markList.OrderBy(m => m.Sort).ToList();
        }
        /// <summary>
        /// 获取排班表的排班预约标记
        /// </summary>
        /// <param name="schedulingRequestList"></param>
        /// <param name="schedulingDetail"></param>
        /// <returns></returns>
        private bool GetSchedulingRequestFlag(List<SchedulingRequestRecordInfo> schedulingRequestList, ShiftSchedulingDetailInfo schedulingDetail, string postType)
        {
            // 目前排班预约 是指休假，非休假排班直接返回
            if (string.IsNullOrWhiteSpace(postType) || postType != POST_TYPE_ID_4)
            {
                return false;
            }
            var schedulingRequest = schedulingRequestList.Find(m => m.StartDate <= schedulingDetail.SchedulingDate && schedulingDetail.SchedulingDate <= m.EndDate && m.AddEmployeeID == schedulingDetail.EmployeeID);
            // 找不到直接返回
            if (schedulingRequest == null)
            {
                return false;
            }
            // 将午别code转换为int类型比较，上午1，下午2
            if (!int.TryParse(schedulingDetail.NoonType, out int schedulingDetailNoon))
            {
                return false;
            }
            if (!int.TryParse(schedulingRequest.StartNoon, out int schedulingRequestStartNoon))
            {
                return false;
            }
            if (!int.TryParse(schedulingRequest.EndNoon, out int schedulingRequestEndNoon))
            {
                return false;
            }
            // 如果排班日期等于排班预约开始日期且排班午别大于等于预约开始午别
            if (schedulingDetail.SchedulingDate == schedulingRequest.StartDate && schedulingDetailNoon >= schedulingRequestStartNoon)
            {
                return true;
            }
            // 如果排班日期等于排班预约结束日期且排班午别小于等于预约结束午别
            if (schedulingDetail.SchedulingDate == schedulingRequest.EndDate && schedulingDetailNoon <= schedulingRequestEndNoon)
            {
                return true;
            }
            // 如果排班日期在排班预约开始日期和结束日期之间
            if (schedulingDetail.SchedulingDate > schedulingRequest.StartDate && schedulingDetail.SchedulingDate < schedulingRequest.EndDate)
            {
                return true;
            }
            return false;
        }

        public async Task<ShiftSchedulingView> GetShiftSchedulingData(int departmentID, string schedulingType, DateTime startDate, DateTime endDate, string statusCode)
        {
            var shiftSchedulingView = new ShiftSchedulingView();
            var monthStartTime = DateHelper.GetFirstDayOfMonth(startDate);
            var monthEndTime = DateHelper.GetLastDayOfMonth(monthStartTime);
            var calendarList = await _perpetualCalendarRepository.GetCalendarByDate(monthStartTime, monthEndTime);
            var holidays = calendarList.Sum(m => m.Holiday) ?? 0;
            shiftSchedulingView.RequiredAttendanceDays = $"{monthStartTime:MMMM}标准应出勤{((monthEndTime - monthStartTime).Days + 1 - holidays):0.#}天";
            var endTime = monthEndTime;
            if (schedulingType == SCHEDUING_TYPE_2 && startDate.Month != endDate.Month)
            {
                endTime = endDate;
                calendarList = await _perpetualCalendarRepository.GetCalendarByDate(monthStartTime, endTime);
            }
            var tableView = new TableView();
            // 组装第一列（人员）
            var employeeColumns = CreateEmployeeColumns();
            tableView.Columns.AddRange(employeeColumns);
            // 组装日期列
            var dateColumns = CreateColumnsByDate(monthStartTime, endTime, calendarList);
            tableView.Columns.AddRange(dateColumns);
            var employeeList = await _employeeRepository.GetEmployeeDataByDepartmentID(departmentID);
            // 取午别字典表
            var param = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var noonTypeList = await _settingDictionaryService.GetSettingDictionaryDict(param);
            // 标记本部门被借调出去的人员 并 返回借调记录
            var secondmentTuple = await GetEmployeeSecondment(employeeList, departmentID, monthStartTime, monthEndTime, noonTypeList);
            employeeList = secondmentTuple.Item1;
            var employeeSecondmentList = secondmentTuple.Item2;
            // 补充借调到本部门的人员
            var secondedTuple = await GetEmployeeSeconded(employeeList, departmentID, monthStartTime, monthEndTime, noonTypeList);
            employeeList = secondedTuple.Item1;
            var employeeSecondedList = secondedTuple.Item2;
            if (employeeList.Count <= 0)
            {
                return shiftSchedulingView;
            }
            // 组装参数
            var shiftSchedulingParams = await GetShiftSchedulingParams(departmentID, schedulingType, startDate, endDate, statusCode, employeeSecondmentList, employeeSecondedList, employeeList, monthStartTime);
            if (shiftSchedulingParams.ShiftSchedulingRecord != null)
            {
                shiftSchedulingView.ShiftSchedulingRecordIDs = shiftSchedulingParams.ShiftSchedulingRecordIDs;
                shiftSchedulingView.DepartmentID = shiftSchedulingParams.ShiftSchedulingRecord.DepartmentID;
                shiftSchedulingView.SchedulingType = shiftSchedulingParams.ShiftSchedulingRecord.SchedulingType;
                shiftSchedulingView.StartDate = startDate;
                shiftSchedulingView.EndDate = endDate;
                shiftSchedulingView.AutoFlag = shiftSchedulingParams.ShiftSchedulingRecord.AutoFlag;
                shiftSchedulingView.StatusCode = shiftSchedulingParams.ShiftSchedulingRecord.StatusCode;
                shiftSchedulingView.Remark = shiftSchedulingParams.ShiftSchedulingRecord.Remark;
            }
            shiftSchedulingParams.NoonTypeList = noonTypeList;
            // 根据人员清单组装表格行
            tableView.Rows = await GetRowsByEmployeeList(shiftSchedulingParams, dateColumns);
            shiftSchedulingView.ShiftSchedulingTable = tableView;
            return shiftSchedulingView;
        }

        /// <summary>
        /// 获取本部门被借调出去的人员和借调记录
        /// </summary>
        /// <param name="employeeList"></param>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns>处理后的employeeList和借调记录</returns>
        private async Task<Tuple<List<EmployeeForSchedulingView>, List<EmployeeSecondmentRecordInfo>>> GetEmployeeSecondment(List<EmployeeForSchedulingView> employeeList, int departmentID, DateTime startDate, DateTime endDate, List<SelectOptionsView> noonTypeList)
        {
            // 获取当前部门人员的借调记录
            var secondedList = await _employeeService.GetSecondmenRecordByDepartmentIDAndDate(SECONDMENT_TYPE_2, departmentID, startDate, endDate);
            if (secondedList.Count <= 0)
            {
                return Tuple.Create(employeeList, secondedList);
            }
            foreach (var employee in employeeList)
            {
                (var list, var tipContent) = await GetSecondedListAndTipContent(SECONDMENT_TYPE_2, employee.EmployeeID, secondedList, noonTypeList);
                if (!string.IsNullOrWhiteSpace(tipContent))
                {
                    employee.TipContent = tipContent;
                }
                if (list.Count > 0)
                {
                    employee.SecondedList = list;
                }
            }
            return Tuple.Create(employeeList, secondedList);
        }

        /// <summary>
        /// 获取借调到本部门的人员和借调记录
        /// </summary>
        /// <param name="employeeList"></param>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns>处理后的employeeList和借调记录</returns>
        private async Task<Tuple<List<EmployeeForSchedulingView>, List<EmployeeSecondmentRecordInfo>>> GetEmployeeSeconded(List<EmployeeForSchedulingView> employeeList, int departmentID, DateTime startDate, DateTime endDate, List<SelectOptionsView> noonTypeList)
        {
            // 获取借调到当前部门的人员借调记录
            var secondmentList = await _employeeService.GetSecondmenRecordByDepartmentIDAndDate(SECONDMENT_TYPE_1, departmentID, startDate, endDate);
            if (secondmentList.Count <= 0)
            {
                return Tuple.Create(employeeList, secondmentList);
            }
            var secondmentEmployees = await _employeeRepository.GetEmployeeDataByEmployeeIDs(secondmentList.Select(m => m.EmployeeID).Distinct().ToList());
            if (secondmentEmployees.Count <= 0)
            {
                return Tuple.Create(employeeList, secondmentList);
            }
            foreach (var employee in secondmentEmployees)
            {
                (var list, var tipContent) = await GetSecondedListAndTipContent(SECONDMENT_TYPE_1, employee.EmployeeID, secondmentList, noonTypeList);
                if (!string.IsNullOrWhiteSpace(tipContent))
                {
                    employee.TipContent = tipContent;
                }
                if (list.Count > 0)
                {
                    employee.SecondmentList = list;
                }
                // 若该员工本来借调到本部门，在借调未结束时人事直接转到本部门，排班画面可能会出现两个该员工，这里排除下避免重复显示
                employeeList = employeeList.Where(m => m.EmployeeID != employee.EmployeeID).ToList();
            }
            employeeList.AddRange(secondmentEmployees);
            return Tuple.Create(employeeList, secondmentList);
        }

        /// <summary>
        /// 获取人员的借调信息及借调提示信息
        /// </summary>
        /// <param name="secondmentType"></param>
        /// <param name="employeeID"></param>
        /// <param name="secondedList"></param>
        /// <param name="noonTypeList"></param>
        /// <returns></returns>
        private async Task<Tuple<List<EmployeeSecondmentView>, string>> GetSecondedListAndTipContent(int secondmentType, string employeeID, List<EmployeeSecondmentRecordInfo> secondedList, List<SelectOptionsView> noonTypeList)
        {
            var list = new List<EmployeeSecondmentView>();
            var recordList = secondedList.Where(m => m.EmployeeID == employeeID.ToString()).ToList();
            var tipContent = "";
            foreach (var record in recordList)
            {
                var item = new EmployeeSecondmentView()
                {
                    StartDate = record.StartDate,
                    StartNoonTypeID = record.StartNoon,
                    EndDate = record.ActualEndDate ?? record.EndDate,
                    EndNoonTypeID = record.ActualEndNoon ?? record.EndNoon
                };
                var recordStartDate = item.StartDate.ToString("yyyy-MM-dd");
                var recordEndDate = item.EndDate.ToString("yyyy-MM-dd");
                var recordStartNoon = noonTypeList.Find(m => m.Value.ToString() == item.StartNoonTypeID)?.Label;
                var recordEndNoon = noonTypeList.Find(m => m.Value.ToString() == item.EndNoonTypeID)?.Label;
                var departmentName = (await _departmentListRepository.GetByIDAsync(record.SecondmentDepartmentID))?.DepartmentContent;
                var recordTipContent = "";
                if (secondmentType == SECONDMENT_TYPE_1)
                {
                    recordTipContent = $"{recordStartDate}{recordStartNoon} 至 {recordEndDate}{recordEndNoon} 借调到本部门";
                }
                if (secondmentType == SECONDMENT_TYPE_2)
                {
                    recordTipContent = $"{recordStartDate}{recordStartNoon} 至 {recordEndDate}{recordEndNoon} 借调到{departmentName}";
                }
                tipContent = StringExtension.ConcatString(tipContent, recordTipContent, "<br/>", false);
                item.Content = recordTipContent;
                list.Add(item);
            };
            return Tuple.Create(list, tipContent);
        }

        /// <summary>
        ///  组装参数
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="schedulingType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="statusCode"></param>
        /// <param name="employeeSecondmentList"></param>
        /// <param name="employeeSecondedList"></param>
        /// <param name="employeeList"></param>
        /// <param name="monthStartTime"></param>
        /// <returns></returns>
        private async Task<ShiftSchedulingParams> GetShiftSchedulingParams(int departmentID, string schedulingType, DateTime startDate, DateTime endDate, string statusCode, List<EmployeeSecondmentRecordInfo> employeeSecondmentList, List<EmployeeSecondmentRecordInfo> employeeSecondedList, List<EmployeeForSchedulingView> employeeList, DateTime monthStartTime)
        {
            // 取排班预约数据
            var schedulingRequestList = await _schedulingRequestRepository.GetSchedulingRequestByDate(departmentID, startDate, endDate);
            // 这里取所有岗，包含休假
            var departmentPostList = await _dictionaryService.GetDepartmentPostDict(departmentID, true);
            // 从AdministrationIcon表中获取
            var groups = new string[] { departmentID.ToString(), DEPARTMENT_ID_999999.ToString() };
            var administrationIconList = await _dictionaryService.GetIconsByModuleType("SchedulingMark", groups);
            // 标志新增，只有新增时才取预约申请的信息
            var isAdd = true;
            var shiftSchedulingDetails = new List<ShiftSchedulingDetailInfo>();
            var shiftSchedulingDetailMarks = new List<ShiftSchedulingDetailMarkInfo>();
            var shiftSchedulingRecordIDs = new List<string>();
            // 根据排班类型获取开始时间和结束时间，如果跨月 取两组
            var startAndEndDateList = GetStartAndEndDate(schedulingType, startDate, endDate);
            ShiftSchedulingRecordInfo shiftSchedulingRecord = null;
            foreach (var startAndEndDate in startAndEndDateList)
            {
                // 默认取月的明细记录
                var stratTime = startAndEndDate["startDate"];
                var endTime = startAndEndDate["endDate"];
                // 依据借调信息补充排班相关数据
                var tuple = await FillSchedulingDataBySecondment(shiftSchedulingDetails, shiftSchedulingDetailMarks, departmentPostList, employeeSecondmentList, employeeSecondedList, startAndEndDate["startDate"], startAndEndDate["endDate"]);
                shiftSchedulingDetails = tuple.Item1;
                shiftSchedulingDetailMarks = tuple.Item2;
                departmentPostList = tuple.Item3;
                var tempShiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startAndEndDate["startDate"], startAndEndDate["endDate"], statusCode);
                if (tempShiftSchedulingRecord != null)
                {
                    // 如果是修改，获取已排班信息
                    shiftSchedulingRecordIDs.Add(tempShiftSchedulingRecord.ShiftSchedulingRecordID);
                    shiftSchedulingRecord ??= tempShiftSchedulingRecord;
                    isAdd = false;
                    // 周排班 跨月 第二个月只取部分数据
                    if (startDate.Month != endDate.Month && endDate.Month == startAndEndDate["endDate"].Month)
                    {
                        endTime = endDate;
                    }
                    var schedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(tempShiftSchedulingRecord.ShiftSchedulingRecordID, stratTime, endTime);
                    if (schedulingDetails.Count > 0)
                    {
                        shiftSchedulingDetails.AddRange(schedulingDetails);
                    }
                    var schedulingDetailMarks = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(tempShiftSchedulingRecord.ShiftSchedulingRecordID, stratTime, endTime);
                    if (schedulingDetailMarks.Count > 0)
                    {
                        shiftSchedulingDetailMarks.AddRange(schedulingDetailMarks);
                    }
                }
                // 有排班数据 没人员信息  说明是已离职或已转科人员
                var notInDepartmentEmployeeIDs = shiftSchedulingDetails.Where(m => !employeeList.Any(n => n.EmployeeID == m.EmployeeID)).Select(m => m.EmployeeID).ToList();
                employeeList.AddRange(await _employeeRepository.GetEmployeeDataByEmployeeIDs(notInDepartmentEmployeeIDs));
                // 设置在排班日期内转科到本科人员在转科前的排班数据，并设置转入/转出日期，删除目前在本科室但传入的排班日期不在在本科室的人员
                (shiftSchedulingDetails, shiftSchedulingDetailMarks, employeeList) = await SetTransferBeforeData(shiftSchedulingDetails, shiftSchedulingDetailMarks, employeeList, stratTime, endTime, departmentID);
            }
            // 矫正人员的层级
            employeeList = await SetEmployeeCapabilityLevel(employeeList, startDate);
            // 人员排序
            employeeList = await SortEmployeeList(employeeList, shiftSchedulingRecord?.ShiftSchedulingRecordID, monthStartTime, departmentID);
            var employeeIDs = employeeList.Select(m => m.EmployeeID).ToList();
            // 取人员的产假天数，因产假为3个月，取最近4个月内的数据即可
            var employeeMaternityLeaveDays = await _shiftSchedulingDetailRepository.GetEmployeePostDays(employeeIDs, DEPARTMENT_POST_ID_184, monthStartTime.AddMonths(-4), monthStartTime);
            // 取人员的年休假，从当前日期年份的一月一号开始
            var employeeAnnualLeaveDays = await _shiftSchedulingDetailRepository.GetEmployeePostDays(employeeIDs, DEPARTMENT_POST_ID_186, new DateTime(monthStartTime.Year, 1, 1), monthStartTime);
            // 组装参数
            var shiftSchedulingParams = new ShiftSchedulingParams()
            {
                DepartmentID = departmentID,
                ShiftSchedulingRecordIDs = shiftSchedulingRecordIDs,
                IsAdd = isAdd,
                ShiftSchedulingDetails = shiftSchedulingDetails,
                ShiftSchedulingDetailMarks = shiftSchedulingDetailMarks,
                AdministrationIconList = administrationIconList,
                SchedulingRequestList = schedulingRequestList,
                DepartmentPostList = departmentPostList,
                IsQuery = !string.IsNullOrWhiteSpace(statusCode),
                ShiftSchedulingRecord = shiftSchedulingRecord,
                EmployeeList = employeeList,
                EmployeeMaternityLeaveDays = employeeMaternityLeaveDays,
                EmployeeAnnualLeaveDays = employeeAnnualLeaveDays
            };
            return shiftSchedulingParams;
        }
        /// <summary>
        /// 依据时间设置人员的正确层级
        /// </summary>
        /// <param name="employeeList"></param>
        /// <returns></returns>
        private async Task<List<EmployeeForSchedulingView>> SetEmployeeCapabilityLevel(List<EmployeeForSchedulingView> employeeList, DateTime startDate)
        {
            var capabilityLevelDict = await _dictionaryService.GetCapabilityLevelDict();
            var capabilityLevelList = await _employeeCapabilityLevelRepository.GetRecordListByEmployeeIDs(employeeList.Select(m => m.EmployeeID).ToList());
            foreach (var employee in employeeList)
            {
                // 取当前人员的小于startDate日期的最后一次层级记录
                var lastLevel = capabilityLevelList.Where(m => m.EmployeeID == employee.EmployeeID && m.PromotionDate <= startDate).OrderByDescending(m => m.PromotionDate).FirstOrDefault();
                if (lastLevel != null)
                {
                    employee.CapabilityLevelID = lastLevel.CapabilityLevelID;
                    employee.CapabilityLevel = capabilityLevelDict.Find(m => m.Value.ToString() == lastLevel.CapabilityLevelID.ToString())?.Label ?? "";
                }
            }
            return employeeList;
        }

        /// <summary>
        /// 设置在传入排班日期内转科到本科人员在转科前的排班数据，并设置转入/转出日期，删除目前在本科室但传入的排班日期不在在本科室的人员
        /// </summary>
        /// <param name="shiftSchedulingDetails"></param>
        /// <param name="shiftSchedulingDetailMarks"></param>
        /// <param name="employees"></param>
        /// <param name="stratTime"></param>
        /// <param name="endTime"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        private async Task<(List<ShiftSchedulingDetailInfo>, List<ShiftSchedulingDetailMarkInfo>, List<EmployeeForSchedulingView>)> SetTransferBeforeData(List<ShiftSchedulingDetailInfo> shiftSchedulingDetails, List<ShiftSchedulingDetailMarkInfo> shiftSchedulingDetailMarks, List<EmployeeForSchedulingView> employees, DateTime stratTime, DateTime endTime, int departmentID)
        {
            var needGetSchedulingEmployeeIDs = new List<string>();
            var employeeIDs = employees.Select(m => m.EmployeeID).ToList();
            // 获取转科记录最后一条记录且是本科室的记录
            var employeeEmploymentRecords = await _employeeEmploymentRecordRepository.GetLastRecordByEmployeeIDs(employeeIDs);
            if (employeeEmploymentRecords.Count > 0)
            {
                var deleteEmployeeIDs = new List<string>();
                foreach (var employee in employees)
                {
                    var employeeEmploymentRecord = employeeEmploymentRecords.Find(m => m.EmployeeID == employee.EmployeeID);
                    // 如果没有转科记录，或者转科记录没有开始日期，或者转科记录开始日期在排班开始日期之前
                    if (employeeEmploymentRecord == null || !employeeEmploymentRecord.StartDate.HasValue)
                    {
                        continue;
                    }
                    if (employeeEmploymentRecord.StartDate.Value.Date >= stratTime.Date && employeeEmploymentRecord.StartDate.Value.Date <= endTime.Date)
                    {
                        if (employeeEmploymentRecord.DepartmentID == departmentID)
                        {
                            employee.TransferInDate = employeeEmploymentRecord.StartDate.Value;
                            employee.TipContent = $"{employee.TransferInDate.Value:yyyy-MM-dd}转入到本部门";
                            needGetSchedulingEmployeeIDs.Add(employee.EmployeeID);
                        }
                        else
                        {
                            employee.TransferOutDate = employeeEmploymentRecord.StartDate.Value;
                            employee.TipContent = $"{employee.TransferOutDate.Value:yyyy-MM-dd}转出本部门";
                        }
                        continue;
                    }
                    // 最后一条转科记录是本科室，且开始日期在排班日期后，删除人员
                    if (employeeEmploymentRecord.DepartmentID == departmentID && employeeEmploymentRecord.StartDate.Value >= stratTime.Date)
                    {
                        deleteEmployeeIDs.Add(employee.EmployeeID);
                    }
                }
                if (deleteEmployeeIDs.Count > 0)
                {
                    employees = employees.Where(m => !deleteEmployeeIDs.Contains(m.EmployeeID)).ToList();
                    employeeIDs = employees.Select(m => m.EmployeeID).ToList();
                }
            }
            var excludeDepartmentDetails = await _shiftSchedulingDetailRepository.GetExcludeDepartmentDetailsByEmployeeIDs(needGetSchedulingEmployeeIDs, stratTime, endTime, departmentID);
            if (excludeDepartmentDetails.Count <= 0)
            {
                return (shiftSchedulingDetails, shiftSchedulingDetailMarks, employees);
            }
            shiftSchedulingDetails.AddRange(excludeDepartmentDetails);
            // 获取转科前的排班标记
            var excludeDepartmentMarks = await _shiftSchedulingDetailMarkRepository.GetExcludeDepartmentMarksByEmployeeIDs(needGetSchedulingEmployeeIDs, stratTime, endTime, departmentID);
            if (excludeDepartmentMarks.Count <= 0)
            {
                return (shiftSchedulingDetails, shiftSchedulingDetailMarks, employees);
            }
            shiftSchedulingDetailMarks.AddRange(excludeDepartmentMarks);
            return (shiftSchedulingDetails, shiftSchedulingDetailMarks, employees);
        }
        /// <summary>
        /// 获取开始日期和结束日期
        /// </summary>
        /// <param name="schedulingType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        private List<Dictionary<string, DateTime>> GetStartAndEndDate(string schedulingType, DateTime startDate, DateTime endDate)
        {
            var startAndEndDate = new List<Dictionary<string, DateTime>>()
            {
                new()
                {
                    { "startDate", DateHelper.GetFirstDayOfMonth(startDate) },
                    { "endDate", DateHelper.GetLastDayOfMonth(startDate) }
                }
            };
            // 如果是周排班 根据开始日期计算月开始和结束日期
            if (schedulingType == SCHEDUING_TYPE_2 && startDate.Month != endDate.Month)
            {
                startAndEndDate.Add(new()
                {
                    { "startDate", DateHelper.GetFirstDayOfMonth(endDate) },
                    { "endDate", DateHelper.GetLastDayOfMonth(endDate) }
                });
            }
            return startAndEndDate;
        }

        /// <summary>
        /// 依据借调信息补充排班相关数据
        /// </summary>
        /// <param name="shiftSchedulingDetails"></param>
        /// <param name="shiftSchedulingDetailMarks"></param>
        /// <param name="departmentPostList"></param>
        /// <param name="employeeSecondmentList"></param>
        /// <param name="employeeSecondedList"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        private async Task<Tuple<List<ShiftSchedulingDetailInfo>, List<ShiftSchedulingDetailMarkInfo>, List<PostSelectOptionsView>>> FillSchedulingDataBySecondment(List<ShiftSchedulingDetailInfo> shiftSchedulingDetails, List<ShiftSchedulingDetailMarkInfo> shiftSchedulingDetailMarks, List<PostSelectOptionsView> departmentPostList, List<EmployeeSecondmentRecordInfo> employeeSecondmentList, List<EmployeeSecondmentRecordInfo> employeeSecondedList, DateTime startDate, DateTime endDate)
        {
            // 获取被借调出去的人员在借调部门的已发布的排班信息和借调部门的部门岗位字典
            var secondmentTuple = await GetSchedulingDetailsAndDepartmentPostList(employeeSecondmentList, startDate, endDate, "SecondmentDepartmentID");
            if (secondmentTuple.Item1.Count > 0)
            {
                shiftSchedulingDetails.AddRange(secondmentTuple.Item1);
            }
            if (secondmentTuple.Item2.Count > 0)
            {
                shiftSchedulingDetailMarks.AddRange(secondmentTuple.Item2);
            }
            if (secondmentTuple.Item3.Count > 0)
            {
                departmentPostList.AddRange(secondmentTuple.Item3);
            }

            if (employeeSecondedList?.Count >= 0)
            {
                // 获取借调人员在原部门的已发布的排班信息和原部门的部门岗位字典
                var secondedTuple = await GetSchedulingDetailsAndDepartmentPostList(employeeSecondedList, startDate, endDate, "DepartmentID");
                if (secondedTuple.Item1.Count > 0)
                {
                    shiftSchedulingDetails.AddRange(secondedTuple.Item1);
                }
                if (secondedTuple.Item2.Count > 0)
                {
                    shiftSchedulingDetailMarks.AddRange(secondedTuple.Item2);
                }
                if (secondedTuple.Item3.Count > 0)
                {
                    departmentPostList.AddRange(secondedTuple.Item3);
                }
                // 获取借调到本部门的人员在非原部门的排班数据
                foreach (var employeeSeconded in employeeSecondedList)
                {
                    var secondmentList = await _employeeService.GetSecondmenRecordByDepartmentIDAndDate(SECONDMENT_TYPE_2, employeeSeconded.DepartmentID, startDate, endDate);
                    // 排除借调到本部门的记录
                    secondmentList = secondmentList.Where(m => m.SecondmentDepartmentID != employeeSecondedList[0].SecondmentDepartmentID).ToList();
                    var secondmentListTuple = await GetSchedulingDetailsAndDepartmentPostList(secondmentList, startDate, endDate, "SecondmentDepartmentID");
                    if (secondmentListTuple.Item1.Count > 0)
                    {
                        shiftSchedulingDetails.AddRange(secondmentListTuple.Item1);
                    }
                    if (secondmentListTuple.Item2.Count > 0)
                    {
                        shiftSchedulingDetailMarks.AddRange(secondmentListTuple.Item2);
                    }
                    if (secondmentListTuple.Item3.Count > 0)
                    {
                        departmentPostList.AddRange(secondmentListTuple.Item3);
                    }
                }
            }
            return Tuple.Create(shiftSchedulingDetails, shiftSchedulingDetailMarks, departmentPostList);
        }

        /// <summary>
        /// 依据借调记录，获取人员已发布的排班明细集合、排班标记集合和部门岗位集合
        /// </summary>
        /// <param name="employeeSecondmentList"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="departmentIDFields">SecondmentDepartmentID表示获取借调部门的排班信息，DepartmentID表示获取原部门排班信息</param>
        /// <returns></returns>
        private async Task<Tuple<List<ShiftSchedulingDetailInfo>, List<ShiftSchedulingDetailMarkInfo>, List<PostSelectOptionsView>>> GetSchedulingDetailsAndDepartmentPostList(List<EmployeeSecondmentRecordInfo> employeeSecondmentList, DateTime startDate, DateTime endDate, string departmentIDFields)
        {
            var schedulingDetails = new List<ShiftSchedulingDetailInfo>();
            var schedulingDetailMarks = new List<ShiftSchedulingDetailMarkInfo>();
            var departmentPostList = new List<PostSelectOptionsView>();
            if (employeeSecondmentList == null || employeeSecondmentList.Count <= 0)
            {
                return Tuple.Create(schedulingDetails, schedulingDetailMarks, departmentPostList);
            }
            foreach (var employeeSecondment in employeeSecondmentList)
            {
                // 通过反射取到部门ID
                var departmentID = (int)ReflexUtil.GetPropertyValue(employeeSecondment, departmentIDFields);
                // 只取已发布排班
                var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startDate, endDate);
                if (shiftSchedulingRecord == null)
                {
                    continue;
                }
                var startTime = startDate;
                var endTime = endDate;
                // 如果是借调科室 只取借调数据
                if (departmentIDFields == "SecondmentDepartmentID")
                {
                    startTime = employeeSecondment.StartDate;
                    endTime = employeeSecondment.ActualEndDate == null ? employeeSecondment.EndDate : employeeSecondment.ActualEndDate.Value;
                }
                var shiftSchedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startTime, endTime);
                shiftSchedulingDetails = shiftSchedulingDetails.Where(m => m.EmployeeID == employeeSecondment.EmployeeID).ToList();
                if (shiftSchedulingDetails.Count > 0)
                {
                    schedulingDetails.AddRange(shiftSchedulingDetails);
                }
                var shiftSchedulingMarks = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startTime, endTime);
                shiftSchedulingMarks = shiftSchedulingMarks.Where(m => m.EmployeeID == employeeSecondment.EmployeeID).ToList();
                if (shiftSchedulingMarks.Count > 0)
                {
                    schedulingDetailMarks.AddRange(shiftSchedulingMarks);
                }
                var departmentPostDict = await _dictionaryService.GetDepartmentPostDict(departmentID, false);
                departmentPostDict = departmentPostDict.Where(m => schedulingDetails.Any(n => n.DepartmentPostID.ToString() == m.Value.ToString())).ToList();
                if (departmentPostDict.Count > 0)
                {
                    departmentPostList.AddRange(departmentPostDict);
                }
            }
            return Tuple.Create(schedulingDetails, schedulingDetailMarks, departmentPostList);
        }

        /// <summary>
        /// 人员排序
        /// </summary>
        /// <param name="employeeList"></param>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        private async Task<List<EmployeeForSchedulingView>> SortEmployeeList(List<EmployeeForSchedulingView> employeeList, string shiftSchedulingRecordID, DateTime startDate, int departmentID)
        {
            var sortEmployeeList = new List<ShiftSchedulingEmployeeSortInfo>();
            // 如果shiftSchedulingRecordID不为空，取本次排班的人员顺序
            if (!string.IsNullOrWhiteSpace(shiftSchedulingRecordID))
            {
                sortEmployeeList = await _shiftSchedulingEmployeeSortRepository.GetEmployeeListByScheduling(shiftSchedulingRecordID);
            }
            // 如果本次排班取不到人员顺序，取上月排班人员顺序
            if (sortEmployeeList.Count <= 0)
            {

                var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startDate.AddMonths(-1), startDate.AddDays(-1));
                if (shiftSchedulingRecord != null)
                {
                    sortEmployeeList = await _shiftSchedulingEmployeeSortRepository.GetEmployeeListByScheduling(shiftSchedulingRecord.ShiftSchedulingRecordID);
                }
            }
            // 如果上月排班人员顺序取不到，按照正常顺序
            if (sortEmployeeList.Count <= 0)
            {
                return employeeList;
            }
            var newEmployeeList = new List<EmployeeForSchedulingView>();
            foreach (var sortEmployee in sortEmployeeList)
            {
                var index = employeeList.FindIndex(m => m.EmployeeID == sortEmployee.EmployeeID);
                // 如果找到，添加到新的集合，从旧集合中删除
                if (index != -1)
                {
                    newEmployeeList.Add(employeeList[index]);
                    employeeList.RemoveAt(index);
                }
            }
            // 循环完还留在employeeList中的，说明是新加人员，默认放最后
            if (employeeList.Count > 0)
            {
                return newEmployeeList.Union(employeeList).ToList();
            }
            return newEmployeeList;
        }

        /// <summary>
        /// 组装表格行
        /// </summary>
        /// <param name="shiftSchedulingParams"></param>
        /// <param name="dateColumns"></param>
        /// <returns></returns>
        private async Task<List<Dictionary<string, object>>> GetRowsByEmployeeList(ShiftSchedulingParams shiftSchedulingParams, List<TableColumn> dateColumns)
        {
            var tableRows = new List<Dictionary<string, object>>();
            // 循环人员
            foreach (var employee in shiftSchedulingParams.EmployeeList)
            {
                var row = new Dictionary<string, object>();
                // 设置人员的带教老师、能级限制、年休假、产假等信息并排序
                var employeeInfo = await SetEmployeeInfo(employee, shiftSchedulingParams);
                row.Add("employee", employeeInfo);
                row.Add("employeeName", employeeInfo.EmployeeName);
                row.Add("capabilityLevel", employeeInfo.CapabilityLevel);
                // 组装排班信息
                foreach (var dateColumn in dateColumns)
                {
                    shiftSchedulingParams.SchedulingDate = (DateTime)dateColumn.Value;
                    shiftSchedulingParams.EmployeeID = employeeInfo.EmployeeID;
                    shiftSchedulingParams.EmployeeInfo = employeeInfo;
                    var data = await GetSchedulingDataByDate(shiftSchedulingParams);
                    row.Add(dateColumn.Key, data);
                }
                tableRows.Add(row);
            }
            return tableRows;
        }
        /// <summary>
        /// 设置人员的带教老师、能级限制、已休年休假、已休产假等信息
        /// </summary>
        /// <param name="employeeInfo"></param>
        /// <param name="shiftSchedulingParams"></param>
        /// <returns></returns>
        private async Task<EmployeeForSchedulingView> SetEmployeeInfo(EmployeeForSchedulingView employeeInfo, ShiftSchedulingParams shiftSchedulingParams)
        {
            // 获取带教老师姓名
            if (employeeInfo.TeacherEmployeeID != null)
            {
                var teacher = shiftSchedulingParams.EmployeeList.Find(m => m.EmployeeID == employeeInfo.TeacherEmployeeID);
                if (teacher != null)
                {
                    employeeInfo.EmployeeName = $"{employeeInfo.EmployeeName}/{teacher.EmployeeName}";
                }
            }
            // 获取能级限制
            if (employeeInfo.CapabilityLevelID != null)
            {
                var departmentPostIDs = await _postService.GetDepartmentPostIDsByCapabilityLevel(shiftSchedulingParams.DepartmentID, employeeInfo.CapabilityLevelID.Value);
                employeeInfo.DepartmentPostIDs = departmentPostIDs;
            }
            // 取产假天数
            if (shiftSchedulingParams.EmployeeMaternityLeaveDays.TryGetValue(employeeInfo.EmployeeID, out var maternityLeaveDays))
            {
                employeeInfo.MaternityLeaveDays = maternityLeaveDays;
            }
            // 取年休假天数
            if (shiftSchedulingParams.EmployeeMaternityLeaveDays.TryGetValue(employeeInfo.EmployeeID, out var annualLeaveDays))
            {
                employeeInfo.AnnualLeaveDays = annualLeaveDays;
            }
            return employeeInfo;
        }
        /// <summary>
        /// 根据日期获取排班数据
        /// </summary>
        /// <param name="shiftSchedulingParams"></param>
        /// <returns></returns>
        private async Task<ShiftSchedulingDetailView> GetSchedulingDataByDate(ShiftSchedulingParams shiftSchedulingParams)
        {
            var data = new ShiftSchedulingDetailView();
            // 获取排班明细标记
            var markList = GetDetailMarkList(shiftSchedulingParams.ShiftSchedulingDetailMarks, shiftSchedulingParams.AdministrationIconList, shiftSchedulingParams.SchedulingDate.Date, shiftSchedulingParams.EmployeeID);
            data.MarkList = markList;
            // 填充当前日期内人员的被借调和借调标记及提示信息
            data = FillSecondedAndSecondmentMark(data, shiftSchedulingParams.EmployeeInfo, shiftSchedulingParams.SchedulingDate);
            var shiftSchedulingDetails = shiftSchedulingParams.ShiftSchedulingDetails.Where(m => m.EmployeeID == shiftSchedulingParams.EmployeeID && m.SchedulingDate.Date == shiftSchedulingParams.SchedulingDate.Date).ToList();
            if (shiftSchedulingDetails.Count <= 0)
            {
                // 如果当天没有排班数据且不是查看模式，再补充审核通过的预约排班数据
                if (!shiftSchedulingParams.IsQuery)
                {
                    data = GetShiftSchedulingView(data, shiftSchedulingParams);
                }
                return data;
            }
            // 获取午别对应的排班岗位
            var noonPost = await GetNoonPost(shiftSchedulingParams, shiftSchedulingDetails);
            data.ShiftSchedulingRecordID = shiftSchedulingDetails[0].ShiftSchedulingRecordID;
            data.EmployeeID = shiftSchedulingParams.EmployeeID;
            data.SchedulingDate = shiftSchedulingParams.SchedulingDate.Date;
            data.NoonPost = noonPost;
            return data;
        }

        /// <summary>
        /// 获取午别对应的排班岗位
        /// </summary>
        /// <param name="shiftSchedulingParams"></param>
        /// <param name="shiftSchedulingDetails"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, ShiftSchedulingPost>> GetNoonPost(ShiftSchedulingParams shiftSchedulingParams, List<ShiftSchedulingDetailInfo> shiftSchedulingDetails)
        {
            var noonPost = new Dictionary<string, ShiftSchedulingPost>();
            // 组装上午和下午的排班明细
            foreach (var noonType in shiftSchedulingParams.NoonTypeList)
            {
                var shiftSchedulingDetail = shiftSchedulingDetails.Find(m => m.NoonType == noonType.Value.ToString());
                if (shiftSchedulingDetail == null)
                {
                    continue;
                }
                // 获取岗位名称
                var departmentPost = shiftSchedulingParams.DepartmentPostList.Find(m => m.Value.ToString() == shiftSchedulingDetail.DepartmentPostID.ToString());
                // 如果找不到岗位，可能该岗位状态关闭或删除了，根据部门岗位ID查找
                departmentPost ??= await _dictionaryService.GetDepartmentPostDictByID(shiftSchedulingDetail.DepartmentPostID, shiftSchedulingParams.DepartmentID);
                // 如果根据部门岗位ID查找也查不到 直接跳过
                if (departmentPost == null)
                {
                    continue;
                }
                var departmentPostName = departmentPost?.LocalLabel.ToString();
                var shiftSchedulingPost = new ShiftSchedulingPost()
                {
                    DepartmentPostID = shiftSchedulingDetail.DepartmentPostID,
                    DepartmentPostName = departmentPostName,
                    PostType = departmentPost.Type,
                    AdjustScheduleRecordID = shiftSchedulingDetail.AdjustScheduleRecordID,
                    AttendanceDays = departmentPost?.AttendanceDays ?? 1,
                    SchedulingRequestFlag = GetSchedulingRequestFlag(shiftSchedulingParams.SchedulingRequestList, shiftSchedulingDetail, departmentPost.Type),
                    Color = departmentPost.Color,
                    BackGroundColor = departmentPost.BackGroundColor,
                };
                noonPost.Add(noonType.Value.ToString(), shiftSchedulingPost);
            }
            return noonPost;
        }
        /// <summary>
        /// 填充当前日期内人员的被借调和借调标记及提示信息
        /// </summary>
        /// <param name="data"></param>
        /// <param name="employeeInfo"></param>
        /// <param name="schedulingDate"></param>
        /// <returns></returns>
        private ShiftSchedulingDetailView FillSecondedAndSecondmentMark(ShiftSchedulingDetailView data, EmployeeForSchedulingView employeeInfo, DateTime schedulingDate)
        {
            // 判断当前人员当前日期是否为借调出去
            if (employeeInfo.SecondedList != null)
            {
                var seconded = employeeInfo.SecondedList.Find(m => m.StartDate <= schedulingDate && schedulingDate <= m.EndDate);
                if (seconded != null)
                {
                    data.SecondedFlag = true;
                }
            }
            // 判断当前日期当前人员是否为借调到本部门的人员
            if (employeeInfo.SecondmentList != null)
            {
                var secondment = employeeInfo.SecondmentList.Find(m => m.StartDate <= schedulingDate && schedulingDate <= m.EndDate);
                if (secondment != null)
                {
                    data.SecondmentFlag = true;
                }
            }
            return data;
        }

        /// <summary>
        /// 根据排班预约组装排班数据
        /// </summary>
        /// <param name="shiftSchedulingParams"></param>
        /// <returns></returns>
        private ShiftSchedulingDetailView GetShiftSchedulingView(ShiftSchedulingDetailView data, ShiftSchedulingParams shiftSchedulingParams)
        {
            if (shiftSchedulingParams.SchedulingRequestList.Count <= 0)
            {
                return data;
            }
            // 有可能一天两条预约记录
            var schedulingRequestList = shiftSchedulingParams.SchedulingRequestList.Where(m => m.AddEmployeeID == shiftSchedulingParams.EmployeeID
                        && m.StartDate.Date <= shiftSchedulingParams.SchedulingDate.Date && m.EndDate.Date >= shiftSchedulingParams.SchedulingDate)
                        .OrderBy(m => m.AddDateTime).ToList();
            // 没有排班预约直接返回
            if (schedulingRequestList.Count <= 0)
            {
                return data;
            }
            data.EmployeeID = shiftSchedulingParams.EmployeeID;
            data.SchedulingDate = shiftSchedulingParams.SchedulingDate;
            var noonPost = new Dictionary<string, ShiftSchedulingPost>();
            foreach (var schedulingRequest in schedulingRequestList)
            {
                // 获取预约岗位名称和午别
                var departmentPost = shiftSchedulingParams.DepartmentPostList.Find(m => m.Value.ToString() == schedulingRequest.DepartmentPostID.ToString());
                var startNoon = shiftSchedulingParams.NoonTypeList.Find(m => m.Value.ToString() == schedulingRequest.StartNoon);
                var endNoon = shiftSchedulingParams.NoonTypeList.Find(m => m.Value.ToString() == schedulingRequest.EndNoon);
                var requestDateTime = $"{schedulingRequest.StartDate:yyyy-MM-dd}{startNoon?.Label.ToString()} 至 {schedulingRequest.EndDate:yyyy-MM-dd}{endNoon?.Label.ToString()}";
                var departmentPostName = departmentPost?.LocalLabel.ToString();
                data.TipContent = StringExtension.ConcatString(data.TipContent, $"{departmentPost?.Label.ToString()}({requestDateTime})", "<br/>", false);
                var shiftSchedulingPost = new ShiftSchedulingPost()
                {
                    DepartmentPostID = schedulingRequest.DepartmentPostID,
                    DepartmentPostName = departmentPostName,
                    PostType = departmentPost?.Type,
                    SchedulingRequestFlag = true
                };
                // 如果申请开始日期 开始午别是下午，添加下午排班
                if (shiftSchedulingParams.SchedulingDate.Date == schedulingRequest.StartDate.Date && schedulingRequest.StartNoon == "2")
                {
                    noonPost = AddshiftSchedulingPost(noonPost, schedulingRequest.StartNoon, shiftSchedulingPost);
                    continue;
                }
                // 如果申请结束日期 结束午别是上午，添加上午标记
                if (shiftSchedulingParams.SchedulingDate.Date == schedulingRequest.EndDate.Date && schedulingRequest.EndNoon == "1")
                {
                    noonPost = AddshiftSchedulingPost(noonPost, schedulingRequest.EndNoon, shiftSchedulingPost);
                    continue;
                }
                // 全天休假
                foreach (var noonType in shiftSchedulingParams.NoonTypeList)
                {
                    noonPost = AddshiftSchedulingPost(noonPost, noonType.Value.ToString(), shiftSchedulingPost);
                }
            }
            data.NoonPost = noonPost;
            return data;
        }

        /// <summary>
        /// 向noonPost字典里添加数据，如果已有 覆盖
        /// </summary>
        /// <param name="noonPost"></param>
        /// <param name="noonKey"></param>
        /// <param name="shiftSchedulingPost"></param>
        /// <returns></returns>
        private Dictionary<string, ShiftSchedulingPost> AddshiftSchedulingPost(Dictionary<string, ShiftSchedulingPost> noonPost, string noonKey, ShiftSchedulingPost shiftSchedulingPost)
        {
            if (!noonPost.TryAdd(noonKey, shiftSchedulingPost))
            {
                noonPost[noonKey] = shiftSchedulingPost;
            }
            return noonPost;
        }
        /// <summary>
        /// 构建人员列
        /// </summary>
        /// <returns></returns>
        private TableColumn[] CreateEmployeeColumns()
        {
            var capabilityLevelChildColumn = new List<TableColumn>() { new TableColumn(0, "层级", "capabilityLevel", 50, 0, "center") };
            // 人员层级列
            var capabilityLevelColumn = new TableColumn(0, "人员", "capabilityLevel", 160, 0, "center", capabilityLevelChildColumn);
            capabilityLevelColumn.SetMerge();
            var nameChildColumn = new List<TableColumn>() { new TableColumn(0, "姓名", "employeeName", 90, 1, "center") };
            // 人员层级列
            var nameColumn = new TableColumn(1, "人员", "employeeName", 160, 0, "center", nameChildColumn);
            nameColumn.SetMerge();
            return [capabilityLevelColumn, nameColumn];
        }

        /// <summary>
        /// 根据时间段构建列
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private List<TableColumn> CreateColumnsByDate(DateTime startDate, DateTime endDate, List<PerpetualCalendarInfo> calendarList)
        {
            var columns = new List<TableColumn>();
            // 第一列是姓名和能级，这里下标从2开始
            int index = 2;
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                var dateKey = date.ToString("yyyyMMdd");
                // 所有列都有一个子标题，先组件子标题
                var childColumns = new List<TableColumn>() {
                    new TableColumn(index,DateHelper.GetWeekByDate(date),dateKey,50,index,"center")
                    {
                        Value = date.Date
                    }
                };
                var column = new TableColumn(index, date.Day.ToString(), dateKey, 50, index, "center", childColumns)
                {
                    Value = date.Date,
                };
                // 假期，用于标记假期，暂时获取周末，后续从万年历数据表中获取具体日期
                var calendar = calendarList.Find(m => m.Date == date);
                var specialContent = "";
                var isSunday = (int)date.DayOfWeek == 0;
                column.SetSunday(isSunday);
                if (calendar != null && calendar.PublicLeaveFlag != null && calendar.PublicLeaveFlag.Value)
                {
                    specialContent = calendar.Vacation ?? "";
                    if (!string.IsNullOrWhiteSpace(calendar.LunarFestival))
                    {
                        specialContent = StringExtension.ConcatString(specialContent, calendar.LunarFestival, "、", false);
                    }
                }
                if (!string.IsNullOrWhiteSpace(specialContent))
                {
                    column.SetSpecialContent(specialContent);
                }
                index++;
                columns.Add(column);
            }
            return columns;
        }

        public async Task<List<ShiftSchedulingDetailInfo>> GetShiftSchedulingDetailsByEmployeeID(int departmentID, DateTime datetime, string employeeID, string noonType)
        {
            // 获取所在月的开始时间和结束时间
            var startTime = DateHelper.GetFirstDayOfMonth(datetime);
            var endTime = DateHelper.GetLastDayOfMonth(datetime);
            var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startTime, endTime);
            if (shiftSchedulingRecord == null)
            {
                return null;
            }
            var shiftSchedulingDetailList = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startTime, endTime);
            if (shiftSchedulingDetailList.Count <= 0)
            {
                return null;
            }
            if (!string.IsNullOrEmpty(noonType))
            {
                return shiftSchedulingDetailList.Where(m => m.SchedulingDate.Date == datetime.Date && m.EmployeeID == employeeID && m.NoonType == noonType).ToList();
            }
            return shiftSchedulingDetailList.Where(m => m.SchedulingDate.Date == datetime.Date && m.EmployeeID == employeeID).OrderBy(m => m.NoonType).ToList();
        }
        #endregion

        public async Task<string> GetSchedulingPostByEmployeeID(int departmentID, DateTime datetime, string employeeID, string noonType)
        {
            var schedulingPost = "";
            // 获取所在月的开始时间和结束时间
            var schedulingDetails = await GetShiftSchedulingDetailsByEmployeeID(departmentID, datetime, employeeID, noonType);
            if (schedulingDetails == null || schedulingDetails.Count <= 0)
            {
                return schedulingPost;
            }
            var param = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            // 取午别字典表
            var noonTypeList = await _settingDictionaryService.GetSettingDictionaryDict(param);
            if (!string.IsNullOrEmpty(noonType))
            {
                noonTypeList = noonTypeList.Where(m => m.Value.ToString() == noonType).ToList();
            }
            // 取部门岗位字典
            var departmentPostList = await _dictionaryService.GetDepartmentPostDict(departmentID, true);
            foreach (var noonTypeitem in noonTypeList)
            {
                var schedulingDetail = schedulingDetails.Find(m => m.NoonType == noonTypeitem.Value.ToString());
                if (schedulingDetail != null)
                {
                    var departmentPost = departmentPostList.Find(m => m.Value.ToString() == schedulingDetail.DepartmentPostID.ToString());
                    var postName = departmentPost?.LocalLabel ?? "";
                    if (schedulingPost != postName)
                    {
                        if (string.IsNullOrWhiteSpace(schedulingPost))
                        {
                            schedulingPost = postName;
                        }
                        else
                        {
                            schedulingPost += (schedulingPost == "/" ? "" : "/") + postName;
                        }
                    }
                }
                else
                {
                    schedulingPost += "/";
                }
            }
            if (schedulingPost == "/")
            {
                return "";
            }
            return schedulingPost;
        }
        /// <summary>
        /// 根据排班记录ID获取排班数据
        /// </summary>
        /// <param name="schedulingRecordID"></param>
        /// <returns></returns>
        public async Task<ShiftSchedulingRecordView> GetShiftSchedulingDataByRecordID(string schedulingRecordID)
        {
            var shiftSchedulingView = new ShiftSchedulingRecordView();
            var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByID(schedulingRecordID);
            if (shiftSchedulingRecord == null)
            {
                return null;
            }
            if (shiftSchedulingRecord != null)
            {
                shiftSchedulingView.ShiftSchedulingRecordID = shiftSchedulingRecord.ShiftSchedulingRecordID;
                shiftSchedulingView.DepartmentID = shiftSchedulingRecord.DepartmentID;
                shiftSchedulingView.SchedulingType = shiftSchedulingRecord.SchedulingType;
                shiftSchedulingView.StartDate = shiftSchedulingRecord.StartDate;
                shiftSchedulingView.EndDate = shiftSchedulingRecord.EndDate;
                shiftSchedulingView.AutoFlag = shiftSchedulingRecord.AutoFlag;
                shiftSchedulingView.StatusCode = shiftSchedulingRecord.StatusCode;
                shiftSchedulingView.Remark = shiftSchedulingRecord.Remark;
            }
            var departToDepartList = await _departmentVSDepartmentRepository.GetByOrganizationTypeAndDepartmentID("1", shiftSchedulingRecord.DepartmentID, "3");
            if (departToDepartList != null && departToDepartList.Count > 0)
            {
                var departIDs = departToDepartList.Select(m => m.DepartmentID2).ToList();
                shiftSchedulingView.DepartmentID2s = departIDs;
            }
            var detailList = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID);
            if (detailList != null)
            {
                shiftSchedulingView.ShiftSchedulingDetails = detailList;
            }
            return shiftSchedulingView;
        }

        #region 复制上月/周排班数据
        public async Task<string> CopyScheduling(int departmentID, string schedulingType, DateTime startDate, DateTime endDate)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            var employeeID = session.EmployeeID;
            // 获取要复制的排班数据
            (var ret, var schedulingDetails, var schedulingDetailMarks, var previousStartDate) = await GetCopySchedulingDeta(departmentID, schedulingType, startDate);
            // 上月/周没排班，直接跳出
            if (!string.IsNullOrWhiteSpace(ret))
            {
                return ret;
            }

            // 获取当前月/周的时间区间集合
            var startAndEndDateList = GetStartAndEndDate(schedulingType, startDate, endDate);
            foreach (var startAndEndDate in startAndEndDateList)
            {
                var currentSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startAndEndDate["startDate"], startAndEndDate["endDate"], null);
                // 获取当前月/周是否已排班，如有则删除   
                if (currentSchedulingRecord != null)
                {
                    currentSchedulingRecord = await DeelOldSchedulingData(currentSchedulingRecord, employeeID, startAndEndDate, startDate, endDate);
                }
                else
                {
                    currentSchedulingRecord = new ShiftSchedulingRecordInfo()
                    {
                        DepartmentID = departmentID,
                        HospitalID = session.HospitalID,
                        SchedulingType = schedulingType,
                        StartDate = startAndEndDate["startDate"],
                        EndDate = startAndEndDate["endDate"],
                        StatusCode = "0",
                        AutoFlag = false,
                        Remark = null,
                        DeleteFlag = "",
                    };
                    currentSchedulingRecord.ShiftSchedulingRecordID = currentSchedulingRecord.GetId();
                    currentSchedulingRecord.Add(employeeID);
                    await _unitOfWork.GetRepository<ShiftSchedulingRecordInfo>().InsertAsync(currentSchedulingRecord);
                }
                currentSchedulingRecord.Modify(employeeID);
                await CopySchedulingDetails(currentSchedulingRecord.ShiftSchedulingRecordID, schedulingDetails, schedulingDetailMarks, employeeID, departmentID, startDate, endDate, previousStartDate);
            }
            // 复制排班明细数据
            await _unitOfWork.SaveChangesAsync();
            return ret;
        }
        /// <summary>
        ///  复制排班时 如已有排班 处理历史数据
        /// </summary>
        /// <param name="shiftSchedulingRecord"></param>
        /// <param name="employeeID"></param>
        /// <param name="startAndEndDate"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        private async Task<ShiftSchedulingRecordInfo> DeelOldSchedulingData(ShiftSchedulingRecordInfo shiftSchedulingRecord, string employeeID, Dictionary<string, DateTime> startAndEndDate, DateTime startDate, DateTime endDate)
        {
            ShiftSchedulingRecordInfo newSchedulingRecord = null;
            List<ShiftSchedulingDetailInfo> newSchedulingDetails = null;
            List<ShiftSchedulingDetailMarkInfo> newMarkList = null;
            // 如果是已发布的排班，先复制一份暂存，再处理
            if (shiftSchedulingRecord.StatusCode == "1")
            {
                newSchedulingRecord = CloneData.CloneObj(shiftSchedulingRecord);
                newSchedulingRecord.ShiftSchedulingRecordID = newSchedulingRecord.GetId();
                newSchedulingRecord.StatusCode = "0";
                newSchedulingRecord.Add(employeeID);
                newSchedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startAndEndDate["startDate"], startAndEndDate["endDate"]);
                newMarkList = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startAndEndDate["startDate"], startAndEndDate["endDate"]);
            }
            // 删除旧的明细
            var currentSchedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startDate, endDate);
            if (currentSchedulingDetails.Count > 0)
            {
                currentSchedulingDetails.ForEach(detail =>
                {
                    if (newSchedulingRecord != null && newSchedulingDetails?.Count > 0)
                    {
                        var removeDetail = newSchedulingDetails.Find(m => m.ShiftSchedulingDetailID == detail.ShiftSchedulingDetailID);
                        if (removeDetail != null)
                        {
                            newSchedulingDetails.Remove(removeDetail);
                        }
                    }
                    else
                    {
                        detail.Delete(employeeID);
                    }
                });
            }
            // 删除旧的mark
            var currentMarkList = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startDate, endDate);
            if (currentMarkList.Count > 0)
            {
                currentMarkList.ForEach(mark =>
                {
                    if (newSchedulingRecord != null && newMarkList?.Count > 0)
                    {
                        var removeMark = newMarkList.Find(m => m.ShiftSchedulingDetailMarkID == mark.ShiftSchedulingDetailMarkID);
                        if (removeMark != null)
                        {
                            newMarkList.Remove(removeMark);
                        }
                    }
                    else
                    {
                        mark.Delete(employeeID);
                    }
                });
            }
            // 如果是已发布的排班，复制排班明细
            if (newSchedulingRecord != null)
            {
                shiftSchedulingRecord = newSchedulingRecord;
                await _unitOfWork.GetRepository<ShiftSchedulingRecordInfo>().InsertAsync(shiftSchedulingRecord);
                if (newSchedulingDetails?.Count > 0)
                {
                    foreach (var detail in newSchedulingDetails)
                    {
                        detail.ShiftSchedulingDetailID = detail.GetId();
                        detail.ShiftSchedulingRecordID = shiftSchedulingRecord.ShiftSchedulingRecordID;
                        detail.Add(employeeID);
                        detail.Modify(employeeID);
                    };
                    await _unitOfWork.GetRepository<ShiftSchedulingDetailInfo>().InsertAsync(newSchedulingDetails);
                }
                if (newMarkList?.Count > 0)
                {
                    foreach (var mark in newMarkList)
                    {
                        mark.ShiftSchedulingDetailMarkID = mark.GetId();
                        mark.ShiftSchedulingRecordID = shiftSchedulingRecord.ShiftSchedulingRecordID;
                        mark.Add(employeeID);
                        mark.Modify(employeeID);
                    };
                    await _unitOfWork.GetRepository<ShiftSchedulingDetailMarkInfo>().InsertAsync(newMarkList);
                }
            }
            return shiftSchedulingRecord;
        }
        /// <summary>
        /// 获取要复制的排班数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="schedulingType"></param>
        /// <param name="startDate"></param>
        /// <returns>异常信息，排班明细，排班明细特殊标记</returns>
        private async Task<Tuple<string, List<ShiftSchedulingDetailInfo>, List<ShiftSchedulingDetailMarkInfo>, DateTime>> GetCopySchedulingDeta(int departmentID, string schedulingType, DateTime startDate)
        {
            var ret = "";
            // 月排班获取上月的开始和结束日期
            var previousStartDate = startDate.AddMonths(-1);
            var previousEndDate = startDate.AddDays(-1);
            // 周排班获取 上周的开始和结束日期
            if (schedulingType == SCHEDUING_TYPE_2)
            {
                var previousWeekDate = startDate.AddDays(-1);
                (previousStartDate, previousEndDate) = DateHelper.GetWeekStartAndEnd(previousWeekDate);
            }
            var startAndEndDateList = GetStartAndEndDate(schedulingType, previousStartDate, previousEndDate);
            var schedulingDetails = new List<ShiftSchedulingDetailInfo>();
            var schedulingDetailMarks = new List<ShiftSchedulingDetailMarkInfo>();
            foreach (var startAndEndDate in startAndEndDateList)
            {
                var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startAndEndDate["startDate"], startAndEndDate["endDate"]);
                if (shiftSchedulingRecord == null)
                {
                    var name = schedulingType == SCHEDUING_TYPE_2 ? "周" : "月";
                    ret = $"复制失败，上{name}尚未发布排班表！";
                    break;
                }
                // 获取符合条件的排班明细
                var previousSchedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, previousStartDate, previousEndDate);
                if (previousSchedulingDetails.Count > 0)
                {
                    schedulingDetails.AddRange(previousSchedulingDetails);
                }
                var previousSchedulingDetailMarks = await _shiftSchedulingDetailMarkRepository.GetMarkByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, previousStartDate, previousEndDate);
                if (previousSchedulingDetailMarks.Count > 0)
                {
                    schedulingDetailMarks.AddRange(previousSchedulingDetailMarks);
                }
            }
            if (string.IsNullOrWhiteSpace(ret))
            {
                var secondmentEmployeeIDs = new List<string>();
                // 借调到本部门人员的借调记录
                var secondmentList = await _employeeService.GetSecondmenRecordByDepartmentIDAndDate(SECONDMENT_TYPE_1, departmentID, previousStartDate, previousEndDate);
                secondmentEmployeeIDs.AddRange(secondmentList.Select(m => m.EmployeeID).ToList());
                // 本部门借调出去人员的借调记录
                var secondedList = await _employeeService.GetSecondmenRecordByDepartmentIDAndDate(SECONDMENT_TYPE_2, departmentID, previousStartDate, previousEndDate);
                secondmentEmployeeIDs.AddRange(secondedList.Select(m => m.EmployeeID).ToList());
                secondmentEmployeeIDs = secondmentEmployeeIDs.Distinct().ToList();
                // 排除借调人员和借调出去人员的排班
                schedulingDetails = schedulingDetails.Where(m => !secondmentEmployeeIDs.Contains(m.EmployeeID)).ToList();
                schedulingDetailMarks = schedulingDetailMarks.Where(m => !secondmentEmployeeIDs.Contains(m.EmployeeID)).ToList();
            }
            return Tuple.Create(ret, schedulingDetails, schedulingDetailMarks, previousStartDate);
        }

        /// <summary>
        /// 复制排班明细数据
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <param name="schedulingDetails"></param>
        /// <param name="schedulingDetailMarks"></param>
        /// <param name="employeeID"></param>
        /// <param name="copyStartDate"></param>
        private async Task CopySchedulingDetails(string shiftSchedulingRecordID, List<ShiftSchedulingDetailInfo> schedulingDetails, List<ShiftSchedulingDetailMarkInfo> schedulingDetailMarks, string employeeID, int departmentID, DateTime startDate, DateTime endDate, DateTime copyStartDate)
        {
            var dimissionEmployeeList = await _employeeService.GetDimissionEmployeeList(departmentID);
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                // 计算当天和开始日期的相差天数
                var diffDay = (date - startDate).Days;
                // 算出要复制排班的日期
                var schedulingDate = copyStartDate.AddDays(diffDay);
                // 循环复制排班明细
                foreach (var schedulingDetail in schedulingDetails)
                {
                    // 已离职人员不复制其排班
                    if (dimissionEmployeeList.Find(m => m.EmployeeID == schedulingDetail.EmployeeID) != null)
                    {
                        continue;
                    }
                    if (schedulingDetail.SchedulingDate != schedulingDate)
                    {
                        continue;
                    }
                    // 深度复制，避免污染原数据
                    var newSchedulingDetail = CloneData.CloneObj(schedulingDetail);
                    newSchedulingDetail.ShiftSchedulingDetailID = schedulingDetail.GetId();
                    newSchedulingDetail.ShiftSchedulingRecordID = shiftSchedulingRecordID;
                    newSchedulingDetail.SchedulingDate = date;
                    newSchedulingDetail.AdjustScheduleRecordID = null;
                    newSchedulingDetail.DeleteFlag = "";
                    newSchedulingDetail.Add(employeeID);
                    newSchedulingDetail.Modify(employeeID);
                    await _unitOfWork.GetRepository<ShiftSchedulingDetailInfo>().InsertAsync(newSchedulingDetail);

                }
                // 循环复制排班明细标记
                foreach (var schedulingDetailMark in schedulingDetailMarks)
                {
                    if (schedulingDetailMark.SchedulingDate != schedulingDate)
                    {
                        continue;
                    }
                    // 深度复制，避免污染原数据
                    var newSchedulingDetailMark = CloneData.CloneObj(schedulingDetailMark);
                    newSchedulingDetailMark.ShiftSchedulingDetailMarkID = schedulingDetailMark.GetId();
                    newSchedulingDetailMark.ShiftSchedulingRecordID = shiftSchedulingRecordID;
                    newSchedulingDetailMark.SchedulingDate = date;
                    newSchedulingDetailMark.DeleteFlag = "";
                    newSchedulingDetailMark.Add(employeeID);
                    newSchedulingDetailMark.Modify(employeeID);
                    await _unitOfWork.GetRepository<ShiftSchedulingDetailMarkInfo>().InsertAsync(newSchedulingDetailMark);
                }
            };
        }
        #endregion
        #region 排班标记设置相关逻辑
        /// <summary>
        /// 获取排班标记设定数据
        /// </summary>
        /// <param name="departmentID">病区ID</param>
        /// <returns></returns>
        public async Task<List<SchedulingMarkSettingsView>> GetSchedulingMarkSettings(int departmentID)
        {
            var icons = await _administrationIconRepository.GetAll<AdministrationIconInfo>();
            if (icons == null)
            {
                return null;
            }
            icons = icons.Where(m => m.GroupID.Trim() == departmentID.ToString()).ToList();
            if (icons.Count == 0)
            {
                return null;
            }
            return icons.Select(m => new SchedulingMarkSettingsView()
            {
                AdministrationIconID = m.AdministrationIconID,
                Text = m.Text,
                Icon = m.Icon,
                Remark = m.Remark,
                Color = m.Color,
                BackGroundColor = m.BackGroundColor,
                Sort = m.Sort,
                GroupID = m.GroupID,
                EditFlag = false
            }).ToList();
        }
        /// <summary>
        /// 保存排班标记设定
        /// </summary>
        /// <param name="schedulingMark">排班标记集合</param>
        /// <returns></returns>
        public async Task<bool> SaveSchedulingMarkSettings(SchedulingMarkSettingsView schedulingMark)
        {
            var icons = await _administrationIconRepository.GetAll<AdministrationIconInfo>();
            if (icons == null)
            {
                return false;
            }
            //修改
            if (schedulingMark.AdministrationIconID.HasValue)
            {
                var icon = icons.Find(m => m.AdministrationIconID == schedulingMark.AdministrationIconID);
                if (icon.Color == schedulingMark.Color && icon.BackGroundColor == schedulingMark.BackGroundColor && icon.Icon == schedulingMark.Icon && icon.Text == schedulingMark.Text)
                {
                    return true;
                }
                icon.Color = schedulingMark.Color;
                icon.BackGroundColor = schedulingMark.BackGroundColor;
                icon.Icon = schedulingMark.Icon;
                icon.Text = schedulingMark.Text;
                icon.Remark = schedulingMark.Icon + "：" + schedulingMark.Text;
                icon.ModifyEmployeeID = schedulingMark.UserID;
                icon.ModifyDateTime = DateTime.Now;
                _unitOfWork.GetRepository<AdministrationIconInfo>().Update(icon);
            }
            else
            {
                var inser = new AdministrationIconInfo()
                {
                    AdministrationIconID = icons.Max(m => m.AdministrationIconID) + 1,
                    HospitalID = _sessionCommonServer.GetSessionByCache()?.HospitalID,
                    ModuleType = "SchedulingMark",
                    GroupID = schedulingMark.GroupID,
                    Icon = schedulingMark.Icon,
                    Text = schedulingMark.Text,
                    Remark = schedulingMark.Icon + "：" + schedulingMark.Text,
                    Color = schedulingMark.Color,
                    BackGroundColor = schedulingMark.BackGroundColor,
                    AddEmployeeID = schedulingMark.UserID,
                    AddDateTime = DateTime.Now,
                    ModifyEmployeeID = schedulingMark.UserID,
                    ModifyDateTime = DateTime.Now,
                    DeleteFlag = ""
                };
                await _unitOfWork.GetRepository<AdministrationIconInfo>().InsertAsync(inser);
            }
            await _unitOfWork.SaveChangesAsync();
            await _administrationIconRepository.UpdateCache();
            return true;
        }
        /// <summary>
        /// 删除排班标记
        /// </summary>
        /// <param name="administrationIconID">标记ID</param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteSchedulingMarkSettings(DeleteSchedulingMarkSettingsView deleteScheduling)
        {
            var icons = await _administrationIconRepository.GetAllIconInfos();
            if (icons == null)
            {
                return false;
            }
            var icon = icons.Find(m => m.AdministrationIconID == deleteScheduling.AdministrationIconID);
            if (icon == null)
            {
                return false;
            }
            icon.DeleteFlag = "*";
            icon.ModifyEmployeeID = deleteScheduling.UserID;
            icon.ModifyDateTime = DateTime.Now;
            _unitOfWork.GetRepository<AdministrationIconInfo>().Update(icon);
            await _unitOfWork.SaveChangesAsync();
            await _administrationIconRepository.UpdateCache();
            return true;
        }
        #endregion
        /// <summary>
        /// 根据排序更新 MarkID
        /// </summary>
        /// <param name="markID">原始 MarkID</param>
        /// <param name="departmentIcons">部门图标字典</param>
        /// <param name="group">排班详情组</param>
        /// <returns>是否进行了更新</returns>
        private bool UpdateMarkID(int markID, Dictionary<int, int> departmentIcons, List<ShiftSchedulingDetailMarkInfo> group)
        {
            int? newMarkID = markID switch
            {
                6 => departmentIcons.GetValueOrDefault(1),
                7 => departmentIcons.GetValueOrDefault(2),
                9 => departmentIcons.GetValueOrDefault(3),
                10 => departmentIcons.GetValueOrDefault(4),
                _ => null
            };

            if (newMarkID.HasValue)
            {
                foreach (var item in group)
                {
                    item.MarkID = newMarkID.Value;
                }
                return true;
            }

            return false;
        }

        public async Task<List<ShiftSchedulingRuleView>> GetShiftSchedulingRules(int departmentID)
        {
            var shiftSchedulingRules = new List<ShiftSchedulingRuleView>();
            // 取出排班规则字典
            var settingParams = new SettingDictionaryParams()
            {
                SettingType = "ShiftManagement",
                SettingTypeCode = "SchedulingRule",
            };
            var ruleSettingDictionaryList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var shiftSchedulingRuleList = await _shiftSchedulingRuleRepository.GetShiftSchedulingRule(departmentID);
            foreach (var ruleSettingDictionary in ruleSettingDictionaryList)
            {
                var shiftSchedulingRule = shiftSchedulingRuleList.Find(m => m.RuleID == ruleSettingDictionary.SettingValue);
                var typeValue = ruleSettingDictionary.SettingTypeValue.Split("_");
                var ruleKey = typeValue[0];
                var componentType = "";
                if (typeValue.Length > 1)
                {
                    componentType = typeValue[1];
                }
                var shiftSchedulingRuleView = new ShiftSchedulingRuleView()
                {
                    DepartmentID = departmentID,
                    RuleID = ruleSettingDictionary.SettingValue,
                    Description = ruleSettingDictionary.Description,
                    ShiftSchedulingRuleID = shiftSchedulingRule?.ShiftSchedulingRuleID ?? "",
                    RuleValue = shiftSchedulingRule?.RuleValue ?? "",
                    ComponentType = componentType,
                    RuleKey = ruleKey,
                };
                shiftSchedulingRules.Add(shiftSchedulingRuleView);
            }
            return shiftSchedulingRules;
        }

        public async Task<bool> SaveShiftSchedulingRule(ShiftSchedulingRuleView shiftSchedulingRuleView)
        {
            if (shiftSchedulingRuleView == null)
            {
                return false;
            }
            var session = _sessionCommonServer.GetSessionByCache();
            if (!string.IsNullOrWhiteSpace(shiftSchedulingRuleView.ShiftSchedulingRuleID))
            {
                var shiftSchedulingRule = await _shiftSchedulingRuleRepository.GetShiftSchedulingRuleByID(shiftSchedulingRuleView.ShiftSchedulingRuleID);
                shiftSchedulingRule.RuleValue = shiftSchedulingRuleView.RuleValue;
                shiftSchedulingRule.Modify(session.EmployeeID);
            }
            else
            {
                var shiftSchedulingRule = new ShiftSchedulingRuleInfo()
                {
                    HospitalID = session.HospitalID,
                    DepartmentID = shiftSchedulingRuleView.DepartmentID,
                    RuleID = shiftSchedulingRuleView.RuleID,
                    RuleValue = shiftSchedulingRuleView.RuleValue,
                    DeleteFlag = ""
                };
                shiftSchedulingRule.ShiftSchedulingRuleID = shiftSchedulingRule.GetId();
                shiftSchedulingRule.Add(session.EmployeeID);
                shiftSchedulingRule.Modify(session.EmployeeID);
                await _unitOfWork.GetRepository<ShiftSchedulingRuleInfo>().InsertAsync(shiftSchedulingRule);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
    }
}
