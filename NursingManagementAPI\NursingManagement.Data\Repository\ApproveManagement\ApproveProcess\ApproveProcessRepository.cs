﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 审批流程仓储接口
    /// </summary>
    public class ApproveProcessRepository : IApproveProcessRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ApproveProcessRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 查询审批流程列表
        /// </summary>
        /// <param name="approveProcessID">主键</param>
        /// <returns></returns>
        public async Task<ApproveProcessInfo> GetApproveProcessInfo(string approveProcessID)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.FirstOrDefaultAsync(x => x.ApproveProcessID == approveProcessID && x.DeleteFlag != "*");
        }
        /// <summary>
        /// 查询Api
        /// </summary>
        /// <param name="approveProcessID">主键</param>
        /// <returns></returns>
        public async Task<string> GetProveCategoryByProcessID(string approveProcessID)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*")
                .Select(m => m.ProveCategory).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取流程ID对应分类码
        /// </summary>
        /// <param name="processIDs">流程ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetProcessTypeCodeDictByProcessIDs(params string[] processIDs)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m => processIDs.Contains(m.ApproveProcessID) && m.DeleteFlag != "*")
                .ToDictionaryAsync(m => m.ApproveProcessID, m => m.ProveCategory);
        }
        /// <summary>
        /// 查询未停用的审批流程列表
        /// </summary>
        /// <param name="employeeID">医院编号</param>
        /// <returns></returns>
        public async Task<List<ApproveProcess>> GetApproveProcessViews(string hospitalID)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(x => x.StatusCode != ApproveProcessStatusCode.Disable && x.DeleteFlag != "*" && x.HospitalID == hospitalID)
                .Select(m => new ApproveProcess
                {
                    ApproveProcessID = m.ApproveProcessID,
                    ProcessName = m.ProcessName,
                    ProcessDescription = m.ProcessDescription,
                    StatusCode = m.StatusCode,
                    ProveCategory = m.ProveCategory,
                    ContentTemplate = m.ContentTemplate,
                    AddDateTime = m.AddDateTime,
                    AddEmployeeID = m.AddEmployeeID
                }).ToListAsync();
        }
        /// <summary>
        /// 根据分类码查询审批流程ID列表
        /// </summary>
        /// <param name="proveCategory">分类码</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetProcessIDsByProveCategory(string proveCategory)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m => m.ProveCategory == proveCategory && m.StatusCode == ApproveProcessStatusCode.Enable && m.DeleteFlag != "*")
                .Select(m => new { m.ApproveProcessID, m.ContentTemplate }).ToDictionaryAsync(m => m.ApproveProcessID, m => m.ContentTemplate);
        }
        /// <summary>
        /// 更新审批流程表状态
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <param name="enable">启用/禁用</param>
        /// <param name="employeeID">工号</param>
        /// <param name="now">更新时间</param>
        /// <returns></returns>
        public async Task<bool> UpdateApproveProcessStatus(string approveProcessID, ApproveProcessStatusCode enable, string employeeID, DateTime now)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*")
                .ExecuteUpdateAsync(m => m.SetProperty(n => n.StatusCode, _ => enable)
                .SetProperty(n => n.ModifyEmployeeID, employeeID).SetProperty(n => n.ModifyDateTime, now)) > 0;
        }
        /// <summary>
        /// 获取审批内容模板
        /// </summary>
        /// <param name="approveProcessID">流程ID</param>
        /// <returns></returns>
        public async Task<string> GetContentTemplateByProcessID(string approveProcessID)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m => m.ApproveProcessID == approveProcessID && m.DeleteFlag != "*")
                .Select(m => m.ContentTemplate).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据审批类别码获取对应的审批流程ID
        /// </summary>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        public async Task<string> GetProcessIDByCategoryAsNoTrackAsync(string proveCategory)
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m =>
                m.ProveCategory == proveCategory && m.StatusCode == ApproveProcessStatusCode.Enable && m.DeleteFlag != "*")
               .Select(m => m.ApproveProcessID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取审批流程的名称
        /// </summary>
        /// <returns></returns>
        public async Task<List<Tuple<string, string>>> GetAllProcessNameAsync()
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m =>
                m.StatusCode == ApproveProcessStatusCode.Enable && m.DeleteFlag != "*")
              .Select(m => Tuple.Create(m.ProcessName, m.ApproveProcessID)).ToListAsync();
        }
        /// <summary>
        /// 获取所有已启用的审批流程
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetAllCategoryAndProcessID()
        {
            return await _nursingManagementDbContext.ApproveProcessInfos.Where(m => m.StatusCode == ApproveProcessStatusCode.Enable && m.DeleteFlag != "*")
                .Select(m => new { m.ApproveProcessID, m.ProveCategory }).ToDictionaryAsync(m => m.ApproveProcessID, m => m.ProveCategory);
        }
    }
}
