﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class SchedulingRequestService : ISchedulingRequestService
    {
        /// <summary>
        /// 引用
        /// </summary>
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISchedulingRequestRepository _schedulingRequestRepository;
        private readonly IOptions<SystemConfig> _config;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IAdministrationDictionaryRepository _administrationDictionaryRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;
        private readonly IServiceProvider _serviceProvider;
        private readonly IApproveProcessService _approveProcessService;
        private readonly IApproveProcessRepository _approveProcessRepository;
        private readonly IApproveRecordRepository _approveRecordRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        #region

        /// <summary>
        /// 公共部门
        /// </summary>
        private readonly static int DEPARTMENT_ID_999999 = 999999;
        /// <summary>
        /// 部门ID
        /// </summary>
        private readonly static int POST_ID_78 = 78;
        #endregion
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="schedulingRequestRepository"></param>
        public SchedulingRequestService(
            ISchedulingRequestRepository schedulingRequestRepository
            , IOptions<SystemConfig> config
            , IUnitOfWork unitOfWork
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IAdministrationDictionaryRepository administrationDictionaryRepository
            , IDepartmentPostRepository departmentPostRepository
            , IServiceProvider serviceProvider
            , IApproveProcessService approveProcessService
            , IApproveRecordRepository approveRecordRepository
            , IApproveProcessRepository approveProcessRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            )
        {
            _schedulingRequestRepository = schedulingRequestRepository;
            _config = config;
            _unitOfWork = unitOfWork;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _administrationDictionaryRepository = administrationDictionaryRepository;
            _departmentPostRepository = departmentPostRepository;
            _serviceProvider = serviceProvider;
            _approveProcessService = approveProcessService;
            _approveRecordRepository = approveRecordRepository;
            _approveProcessRepository = approveProcessRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
        }
        /// <summary>
        /// 获取单人排班预约数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<SchedulingRequestRecordView>> GetSingleSchedulingData(string employeeID)
        {
            var schedulingInfos = await _schedulingRequestRepository.GetSingleSchedulingAsync(employeeID);
            if (schedulingInfos.Count == 0)
            {
                return new List<SchedulingRequestRecordView>();
            }
            return await CreateSchedulingData(schedulingInfos);
        }
        /// <summary>
        /// 获取科室排班预约数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<SchedulingRequestRecordView>> GetDepartmentSchedulingData(int departmentID)
        {
            var schedulingInfos = await _schedulingRequestRepository.GetDepartmentSchedulingAsync(departmentID);
            if (schedulingInfos.Count == 0)
            {
                return [];
            }
            return await CreateSchedulingData(schedulingInfos);
        }
        /// <summary>
        /// 创建返回数据
        /// </summary>
        /// <param name="schedulingInfos"></param>
        /// <returns></returns>
        private async Task<List<SchedulingRequestRecordView>> CreateSchedulingData(List<SchedulingRequestRecordInfo> schedulingInfos)
        {
            schedulingInfos = schedulingInfos.OrderBy(m => m.StartDate).ToList();
            //获取配置数据
            var postSetting = await _departmentPostRepository.GetAsync(DEPARTMENT_ID_999999);
            postSetting = postSetting.Where(m => m.PostID == POST_ID_78).ToList();
            if (postSetting.Count == 0)
            {
                _logger.Error("获取Post公共配置失败");
                return new List<SchedulingRequestRecordView>();
            }
            var postPairs = postSetting.Select(m => new KeyValueString { Key = m.DepartmentPostID.ToString(), Value = m.DepartmentPostName }).ToList();

            return await AssembleSchedulingView(schedulingInfos, postPairs);
        }
        /// <summary>
        /// 数据进行组装排序
        /// </summary>
        /// <param name="schedulingInfos"></param>
        /// <param name="postPairs"></param>
        /// <returns></returns>
        private async Task<List<SchedulingRequestRecordView>> AssembleSchedulingView(List<SchedulingRequestRecordInfo> schedulingInfos, List<KeyValueString> postPairs)
        {
            List<SchedulingRequestRecordView> result = [];
            // 获取审批状态配置
            var approvalStatusParams = new SettingDictionaryParams()
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveRecord",
                SettingTypeValue = "StatusCode"
            };
            var approvalStatusSettings = await _settingDictionaryRepository.GetSettingDictionary(approvalStatusParams);
            // 获取午别配置
           var noonTypeParams = new SettingDictionaryParams()
            {
               SettingType = "PositionManagement",
               SettingTypeCode = "JobPositions",
               SettingTypeValue = "NoonType"
           };
            var noonTypeSettings = await _settingDictionaryRepository.GetSettingDictionary(noonTypeParams);
            var approveRecordIDs = schedulingInfos.Select(m => m.ApproveRecordID).ToList();
            var revokeResonDict = await _approveRecordRepository.GetRevokeReasonsByRecordIDsAsync(approveRecordIDs);
            foreach (var item in schedulingInfos)
            {
                var schedulingRequestRecordView = new SchedulingRequestRecordView
                {
                    PostType = postPairs.Find(m => m.Key == item.DepartmentPostID.ToString())?.Value,
                    AddDateTime = item.AddDateTime.ToString("yyyy-MM-dd HH:mm"),
                    AddEmployeeID = item.AddEmployeeID,
                    StatusCode = item.StatusCode
                };
                var status = approvalStatusSettings.Find(m => m.SettingValue == item.StatusCode);
                schedulingRequestRecordView.Status = status != null ? status.Description : item.StatusCode;
                var userInfo = await _employeePersonalDataRepository.GetDataByEmployeeID(item.AddEmployeeID);
                schedulingRequestRecordView.AddEmployeeName = userInfo?.EmployeeName;

                schedulingRequestRecordView.StartNoon = item.StartNoon;
                schedulingRequestRecordView.EndNoon = item.EndNoon;
                (schedulingRequestRecordView.StartDateFormat, schedulingRequestRecordView.StartNoonName) = GetFormattedDateAndTime(item.StartDate, item.StartNoon, noonTypeSettings);               
                (schedulingRequestRecordView.EndDateFormat, schedulingRequestRecordView.EndNoonName) = GetFormattedDateAndTime(item.EndDate, item.EndNoon, noonTypeSettings);
                schedulingRequestRecordView.DepartmentPostID = item.DepartmentPostID;
                schedulingRequestRecordView.StartDate = item.StartDate;
                schedulingRequestRecordView.EndDate = item.EndDate;
                schedulingRequestRecordView.SchedulingRequestRecordID = item.SchedulingRequestRecordID;
                schedulingRequestRecordView.Days = item.Days;
                schedulingRequestRecordView.Reason = item.Reason;
                schedulingRequestRecordView.ApproveRecordID = item.ApproveRecordID;
                schedulingRequestRecordView.RevokeReason = revokeResonDict.TryGetValue(item.ApproveRecordID, out var revokeReason) ? revokeReason : "";
                schedulingRequestRecordView.ApproveFlag = revokeResonDict.ContainsKey(item.ApproveRecordID);
                result.Add(schedulingRequestRecordView);
            }

            return result.OrderByDescending(m => m.AddDateTime).ToList();
        }

        /// <summary>
        /// 获取格式化后的时间
        /// </summary>
        /// <param name="dateTime">时间</param>
        /// <param name="noon">午别</param>
        /// <param name="settings">午别字典</param>
        /// <returns></returns>
        private Tuple<string,string> GetFormattedDateAndTime(DateTime dateTime, string noon, List<SettingDictionaryInfo> settings)
        {
            var noonSetting = settings.Find(m => m.SettingValue == noon);
            string formattedDateTime = dateTime.ToString("yyyy-MM-dd");
            return Tuple.Create($"{formattedDateTime} {noonSetting?.Description}", noonSetting?.Description);
        }
        /// <summary>
        /// 保存记录数据
        /// </summary>
        /// <param name="saveData"></param>
        /// <returns></returns>
        public async Task<SaveReponseView> SaveSchedulingRequestRecord(SchedulingRequestRecordView saveData)
        {
            var result = new SaveReponseView();
            if (saveData == null)
            {
                return result;
            }
            DateTime currentTime = DateTime.Now;
            SchedulingRequestRecordInfo requestRecordInfo;
            if (string.IsNullOrEmpty(saveData.SchedulingRequestRecordID))
            {
                requestRecordInfo = new SchedulingRequestRecordInfo
                {
                    DepartmentID = saveData.DepartmentID,
                    HospitalID = _config.Value.HospitalID,
                    DepartmentPostID = saveData.DepartmentPostID,
                    StatusCode = saveData.StatusCode,
                    AddEmployeeID = saveData.AddEmployeeID,
                    AddDateTime = currentTime,
                    ModifyEmployeeID = saveData.ModifyEmployeeID,
                    ModifyDateTime = currentTime,
                    DeleteFlag = "",
                    StartDate = saveData.StartDate,
                    EndDate = saveData.EndDate,
                    StartNoon = saveData.StartNoon,
                    EndNoon = saveData.EndNoon,
                    Days = saveData.Days,
                    Reason = saveData.Reason
                };
                requestRecordInfo.SchedulingRequestRecordID = requestRecordInfo.GetId();
                requestRecordInfo.ApproveRecordID = requestRecordInfo.GetId();
                await _unitOfWork.GetRepository<SchedulingRequestRecordInfo>().InsertAsync(requestRecordInfo);

            }
            else
            {
                requestRecordInfo = await _schedulingRequestRepository.GetDataByID(saveData.SchedulingRequestRecordID);
                if (requestRecordInfo == null)
                {
                    return result;
                }
                requestRecordInfo.DepartmentPostID = saveData.DepartmentPostID;
                requestRecordInfo.ModifyEmployeeID = saveData.ModifyEmployeeID;
                requestRecordInfo.ModifyDateTime = currentTime;
                requestRecordInfo.StartDate = saveData.StartDate;
                requestRecordInfo.EndDate = saveData.EndDate;
                requestRecordInfo.StartNoon = saveData.StartNoon;
                requestRecordInfo.EndNoon = saveData.EndNoon;
                requestRecordInfo.Days = saveData.Days;
                requestRecordInfo.Reason = saveData.Reason;
                requestRecordInfo.DeleteFlag = "";
            }
            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            result.RecordSaveFlag = success;
            if (!success)
            {
                requestRecordInfo.ApproveRecordID = null;
                await _unitOfWork.SaveChangesAsync();
                
                return result;
            }
            var approveResult = await CreateOrUpdateApprove(requestRecordInfo);
            result.ApproveSaveFlag = approveResult;
            return result;
        }
        /// <summary>
        /// 创建对应的审批记录
        /// </summary>
        /// <param name="requestRecordInfo">排班预约Model</param>
        /// <returns>是否创建成功</returns>
        public async Task<bool> CreateOrUpdateApprove(SchedulingRequestRecordInfo requestRecordInfo)
        {
            var approveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            //调用添加审批
            var view = await CreateApproveMainAndDetailParamViewAsync(requestRecordInfo);
            var (approveProcessID, contentTemplate) = await _approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(view.ProveCategory, view.DepartmentID);
            if (string.IsNullOrWhiteSpace(approveProcessID))
            {
                _logger.Warn($"未找到审批流程，ProveCategory={view.ProveCategory},DepartmentID={view.DepartmentID}");
                return false;
            }
            if (string.IsNullOrEmpty(requestRecordInfo.SchedulingRequestRecordID))
            {
                view.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                return await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID);
            }
            var approveRecordInfo = await _approveRecordRepository.GetApproveRecordByRecordIDAsync(requestRecordInfo.ApproveRecordID);
            if (approveRecordInfo == null)
            {
                view.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                if (!await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID))
                {
                    return false;
                }
            }
            else
            {
                approveRecordInfo.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                approveRecordInfo.Modify(view.AddEmployeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 创建请求参数
        /// </summary>
        /// <param name="requestRecordInfo">申请记录实例</param>
        /// <returns></returns>
        private async Task<ApproveMainAndDetailParamView> CreateApproveMainAndDetailParamViewAsync(SchedulingRequestRecordInfo requestRecordInfo)
        {
            var noonParam = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var noonSetting = await _settingDictionaryRepository.GetSettingDictionary(noonParam);
            var noonPairs = noonSetting.Select(m => new KeyValueString { Key = m.SettingValue.ToString(), Value = m.Description }).ToList();
            var todayRequests = await _schedulingRequestRepository.GetViewsByDepartmentIDAndStartDate(requestRecordInfo.DepartmentID, requestRecordInfo.StartDate);
            todayRequests = todayRequests.Where(m => m.AddDateTime < requestRecordInfo.AddDateTime && m.SchedulingRequestRecordID != requestRecordInfo.SchedulingRequestRecordID).ToArray();
            var employeeRequests = await _schedulingRequestRepository.GetViewsByEmployee(requestRecordInfo.AddEmployeeID);
            employeeRequests = employeeRequests.Where(m => m.AddDateTime < requestRecordInfo.AddDateTime && m.SchedulingRequestRecordID != requestRecordInfo.SchedulingRequestRecordID).ToArray();
            return new ApproveMainAndDetailParamView
            {
                SourceID = requestRecordInfo.SchedulingRequestRecordID,
                ApproveRecordID = requestRecordInfo.ApproveRecordID,
                ProveCategory = "AA-021",
                DepartmentID = requestRecordInfo.DepartmentID,
                AddEmployeeID = requestRecordInfo.AddEmployeeID,
                StartDate = requestRecordInfo.StartDate.ToLongDateString(),
                EndDate = requestRecordInfo.EndDate.ToLongDateString(),
                StartNoon = noonPairs.Find(m => m.Key == requestRecordInfo.StartNoon)?.Value,
                EndNoon = noonPairs.Find(m => m.Key == requestRecordInfo.EndNoon)?.Value,
                Reason = string.IsNullOrEmpty(requestRecordInfo.Reason) ? "无" : requestRecordInfo.Reason,
                DayCount = todayRequests.Length + 1,
                MonthCount = employeeRequests.Count(m => m.AddDateTime.Year == requestRecordInfo.AddDateTime.Year && m.AddDateTime.Month == requestRecordInfo.AddDateTime.Month) + 1,
                YearCount = employeeRequests.Count(m => m.AddDateTime.Year == requestRecordInfo.AddDateTime.Year) + 1
            };
        }

        /// <summary>
        /// 保存记录数据
        /// </summary>
        /// <param name="schedulingRequestRecordID">删除的记录ID</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteScheduleRequestRecord(string schedulingRequestRecordID, string employeeID)
        {
            var approveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            if (string.IsNullOrWhiteSpace(schedulingRequestRecordID))
            {
                return false;
            }
            var schedulingInfo = await _schedulingRequestRepository.GetDataByID(schedulingRequestRecordID);
            if (schedulingInfo == null)
            {
                return false;
            }
            schedulingInfo.Delete(employeeID);
            await approveRecordService.StopApprovalAsync(schedulingInfo.SchedulingRequestRecordID, employeeID, true);

            // 拉出该记录之后的所有预约记录、审批记录，重算预约次数
            var afterRequests = await _schedulingRequestRepository.GetRecordsByAddDate(schedulingInfo.AddDateTime);
            var sourceIDs = afterRequests.Select(m => m.SchedulingRequestRecordID);
            var approveRecords = await _approveRecordRepository.GetRecordsBySourceIDs(sourceIDs);
            foreach (var request in afterRequests)
            {
                var approveRecord = approveRecords.FirstOrDefault(m => m.SourceID == request.SchedulingRequestRecordID);
                if (approveRecord == null)
                {
                    continue;
                }
                var contentTemplate = await _approveProcessRepository.GetContentTemplateByProcessID(approveRecord.ApproveProcessID);
                var view = await CreateApproveMainAndDetailParamViewAsync(request);
                approveRecord.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                approveRecord.Modify(employeeID);
            }

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
    }

}
