﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员奖惩
    /// </summary>
    [Serializable]
    [Table("EmployeeReward")]
    public class EmployeeRewardInfo : MutiModifyInfo
    {
        [Key]
        public string EmployeeRewardID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 奖惩时间
        /// </summary>
        public DateTime? RewardDate { get; set; }

        /// <summary>
        /// 奖惩内容
        /// </summary>
        public string RewardContent { get; set; }

        /// <summary>
        /// 奖惩级别(1、国家级、2、省部级、3、市级、4、县区级，5、本单位)
        /// </summary>
        public string RewardLevelCode { get; set; }

        /// <summary>
        /// 奖惩类型(1、优秀员工、2、先进个人)
        /// </summary>
        public string RewardTypeCode { get; set; }
    }
}