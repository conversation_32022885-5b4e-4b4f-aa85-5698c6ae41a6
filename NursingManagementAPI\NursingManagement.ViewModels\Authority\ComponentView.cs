﻿namespace NursingManagement.ViewModels
{
    public class ComponentView
    {
        /// <summary>
        /// 路由组件序号
        /// </summary>
        public int ComponentListID { get; set; }
        /// <summary>
        /// 路由ID
        /// </summary>
        public int RouterListID { get; set; }
        /// <summary>
        /// 路由地址，因为移动端没有RouterListID，需要用路由判断
        /// </summary>
        public string RouterPath { get; set; }
        /// <summary>
        /// 组件类型，B：button、T：text、C：checkBox……
        /// </summary>
        public string ControlerType { get; set; }
        /// <summary>
        /// 状态，0不显示，1显示
        /// </summary>        
        public int Status { get; set; }
        /// <summary>
        /// 说明，鼠标悬浮提示,RouterUseButton中的Description
        /// </summary>        
        public string Description { get; set; }
    }
}
