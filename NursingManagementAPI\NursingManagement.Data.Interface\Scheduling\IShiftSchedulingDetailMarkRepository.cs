﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IShiftSchedulingDetailMarkRepository
    {
        /// <summary>
        /// 获取排班标记记录
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        Task<List<ShiftSchedulingDetailMarkInfo>> GetMarkByRecordID(string shiftSchedulingRecordID);
        /// <summary>
        ///  获取排班标记记录
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetailMarkInfo>> GetMarkByRecordID(string shiftSchedulingRecordID, DateTime? startDate, DateTime? endDate);
        /// <summary>
        ///  根据employeeID集合获取数据
        /// </summary>
        /// <param name="shiftSchedulingRecordIDs">排班主记录ID集合</param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetailMarkInfo>> GetMarkByEmployeeID(List<string> shiftSchedulingRecordIDs, string employeeID, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 获取人员除departmentID之外的排班标记
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="excludeDepartmentID"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetailMarkInfo>> GetExcludeDepartmentMarksByEmployeeIDs(List<string> employeeIDs, DateTime startDate, DateTime endDate, int excludeDepartmentID);
    }
}
