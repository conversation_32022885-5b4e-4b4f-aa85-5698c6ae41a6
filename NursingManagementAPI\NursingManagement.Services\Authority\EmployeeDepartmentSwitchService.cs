﻿using Arch.EntityFrameworkCore.UnitOfWork;
using DocumentFormat.OpenXml.Vml.Spreadsheet;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Repository;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Linq;

namespace NursingManagement.Services
{
    /// <summary>
    /// 人员多部门权限配置service
    /// </summary>
    public class EmployeeDepartmentSwitchService : IEmployeeDepartmentSwitchService
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;

        public EmployeeDepartmentSwitchService(
            IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IUnitOfWork unitOfWork,
            IDepartmentListRepository departmentListRepository,
            IEmployeeStaffDataRepository employeeStaffDataRepository,
            ISettingDictionaryService settingDictionaryService
            )
        {
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _unitOfWork = unitOfWork;
            _departmentListRepository = departmentListRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _settingDictionaryService = settingDictionaryService;
        }

        /// <summary>
        /// 获取人员多部门列表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentSwitchView>> GetEmployeeDepartmentSwitchListAsync(int? departmentID)
        {
            // 获取部门信息字典
            var cacheDepartments = await _departmentListRepository.GetAll<DepartmentListInfo>();
            // 获取个人信息字典
            var cachePersonDatas = await _employeePersonalDataRepository.GetEmployeePersonalDataView();
            //获取在职人员的多部门权限记录
            var groupDepartmentSwitchs = await GetDepartmentSwitchOfStaffEmployee(departmentID);
            var viewList = new List<EmployeeDepartmentSwitchView>();

            foreach (var groupItem in groupDepartmentSwitchs)
            {
                var departmentListIDs = new List<int>();
                var departmentListNames = new List<string>();
                var lastModifyDateTime = DateTime.MinValue;
                var lastEmployeeID = "";
                foreach (var item in groupItem)
                {
                    var name = cacheDepartments.Find(m => m.DepartmentID == item.DepartmentID)?.LocalShowName;
                    departmentListNames.Add(name);
                    departmentListIDs.Add(item.DepartmentID);
                    if (lastModifyDateTime < item.ModifyDateTime)
                    {
                        lastModifyDateTime = item.ModifyDateTime;
                        lastEmployeeID = item.ModifyEmployeeID;
                    }
                }
                var view = new EmployeeDepartmentSwitchView
                {
                    EmployeeID = groupItem.Key,
                    EmployeeName = cachePersonDatas.Find(m => m.EmployeeID == groupItem.Key)?.EmployeeName,
                    DepartmentIDs = departmentListIDs,
                    DepartmentNames = departmentListNames,
                    ModifyDateTime = lastModifyDateTime,
                    ModifyEmployeeName = cachePersonDatas.Find(m => m.EmployeeID == lastEmployeeID)?.EmployeeName,
                };
                viewList.Add(view);
            }
            return viewList;
        }
        /// <summary>
        /// 获取在职人员的多部门权限记录
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        private async Task<IEnumerable<IGrouping<string, EmployeeDepartmentSwitchInfo>>> GetDepartmentSwitchOfStaffEmployee(int? departmentID)
        {
            //根据科室获取所有的在职人员工号集合
            var staffEmployeeIDs = await _employeeStaffDataRepository.GetEmployeeIDsByDepartmentIDAsync(departmentID, false);
            //获取所有人员部门权限记录
            var cacheEmployeeDepartmentSwitchs = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync("", true);
            // 过滤所有在职人员的部门权限记录
            var groups = cacheEmployeeDepartmentSwitchs.Where(m => staffEmployeeIDs.Contains(m.EmployeeID)).GroupBy(m => m.EmployeeID);
            return groups;
        }

        /// <summary>
        /// 删除人员多部门权限
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteEmployeeDepartmentSwitchAsync(string employeeID, string modifyEmployeeID)
        {
            var employeeDepartmentSwitchInfos = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeID, false);

            employeeDepartmentSwitchInfos.ForEach(i => i.Delete(modifyEmployeeID));

            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            await _employeeDepartmentSwitchRepository.UpdateCache();
            return success;
        }
        /// <summary>
        /// 保存人员多部门权限
        /// </summary>
        /// <param name="employeeDepartmentSwitchParamView"></param>
        /// <param name="modifyEmployeeID"></param>
        /// <returns></returns>
        public async Task<bool> SaveEmployeeDepartmentSwitchAsync(EmployeeDepartmentSwitchParamView employeeDepartmentSwitchParamView, string modifyEmployeeID)
        {
            var cacheEmployeeDepartmentSwitchs = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeDepartmentSwitchParamView.EmployeeID,true);
            var existedDepartmentIDs = cacheEmployeeDepartmentSwitchs.Select(m=>m.DepartmentID).ToList();
            var departemntList = await _departmentListRepository.GetDepartmentIDByIDsAsync(employeeDepartmentSwitchParamView.DepartmentIDs);
            //从缓存中清理掉删除的记录
            var updateList = new List<EmployeeDepartmentSwitchInfo>();
            foreach (var cacheItem in cacheEmployeeDepartmentSwitchs)
            {
                if (!employeeDepartmentSwitchParamView.DepartmentIDs.Contains(cacheItem.DepartmentID))
                {
                    cacheItem.Delete(modifyEmployeeID);
                    employeeDepartmentSwitchParamView.DepartmentIDs.Remove(cacheItem.DepartmentID);
                    updateList.Add(cacheItem);
                }
            }
            //插入新增的记录
            foreach (var saveDepartmentID in employeeDepartmentSwitchParamView.DepartmentIDs)
            {
                if (!existedDepartmentIDs.Contains(saveDepartmentID))
                {
                    var successDepartemntList = departemntList.FirstOrDefault(m => m.DepartmentID == saveDepartmentID);
                    var newEmployeeDepartmentSwitchInfo = CreateEmployeeDepartmentSwitchInfo(employeeDepartmentSwitchParamView, modifyEmployeeID, saveDepartmentID, successDepartemntList?.OrganizationType);
                    await _unitOfWork.GetRepository<EmployeeDepartmentSwitchInfo>().InsertAsync(newEmployeeDepartmentSwitchInfo);
                }
            }
            if (updateList.Count > 0)
            {
                _unitOfWork.GetRepository<EmployeeDepartmentSwitchInfo>().Update(updateList);
            }
            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            await _employeeDepartmentSwitchRepository.UpdateCache();
            return success;
        }

        /// <summary>
        /// 创建人员对部门实体对象
        /// </summary>
        /// <param name="employeeDepartmentSwitchParamView"></param>
        /// <param name="modifyEmployeeID">修改操作人</param>
        /// <param name="saveDepartmentID">需要调整保存的科室ID</param>
        /// <returns></returns>
        private EmployeeDepartmentSwitchInfo CreateEmployeeDepartmentSwitchInfo(EmployeeDepartmentSwitchParamView employeeDepartmentSwitchParamView,
            string modifyEmployeeID, int saveDepartmentID,string organizationType)
        {
            var newEmployeeDepartmentSwitchInfo = new EmployeeDepartmentSwitchInfo
            {
                EmployeeID = employeeDepartmentSwitchParamView.EmployeeID,
                DepartmentID = saveDepartmentID,
                HospitalID = employeeDepartmentSwitchParamView.HospitalID,
                OrganizationType = organizationType
            };
            newEmployeeDepartmentSwitchInfo.Modify(modifyEmployeeID);

            return newEmployeeDepartmentSwitchInfo;
        }
        /// <summary>
        /// 获取用户拥有权限部门的级联Option
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<CascaderView<int>>> GetEmployeeSwitchCascader(string employeeID)
        {
            var options = new List<CascaderView<int>>();
            var employeeDepartmentSwitchInfos = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeID,false);
            if (employeeDepartmentSwitchInfos.Count == 0)
            {
                return options;
            }
            var organizationTypeIDs = employeeDepartmentSwitchInfos.Select(m=>m.OrganizationType).Distinct().ToList();
            var organizationTypes = await _settingDictionaryService.GetOrganizationTypes();
            var departmentIDs = employeeDepartmentSwitchInfos.Select(m=>m.DepartmentID).Distinct().ToList();
            var departmentList = await _departmentListRepository.GetDepartmentIDByIDsAsync(departmentIDs);
            foreach (var organizationTypeID in organizationTypeIDs)
            {
                var organizationType = organizationTypes.Keys.FirstOrDefault(m => m == organizationTypeID);
                if (organizationType == null)
                {
                    continue;
                }
                var sucEmployeeDepartmentSwitchInfos = employeeDepartmentSwitchInfos.Where(m => m.OrganizationType == organizationTypeID).ToList();
                if (sucEmployeeDepartmentSwitchInfos.Count == 0)
                {
                    continue;
                }
                var option = new CascaderView<int>()
                {
                    Label = organizationTypes[organizationTypeID],
                    Value=Convert.ToInt32(organizationTypeID.Trim()),
                    Disabled=false,
                    Children = sucEmployeeDepartmentSwitchInfos.Select(m=>new CascaderView<int>()
                    {
                        Label = departmentList.FirstOrDefault(n=>n.DepartmentID==m.DepartmentID)?.DepartmentContent??"",
                        Value =m.DepartmentID,
                        Disabled = false,
                        Children = []
                    }).ToList()
                };
                options.Add(option);
            }
            return options;
        }
    }
}
