﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 消息记录实体类
    /// </summary>
    [Serializable]
    [Table("MessageRecord")]
    public class MessageRecordInfo :MutiModifyInfo
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string MessageRecordID { get; set; }
        /// <summary>
        /// 消息标题
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(200)")]
        public string MessageTitle { get; set; }
        /// <summary>
        /// 消息类型
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(50)")]
        public string MessageType { get; set; }
        /// <summary>
        /// 消息内容
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(MAX)")]
        public string MessageContent { get; set; }
        /// <summary>
        /// 置顶天数
        /// </summary>
        [Required]
        public int TopDays { get; set; }
        /// <summary>
        /// 状态参考settingDictionary
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(50)")]
        public string MessageStatus { get; set; }
        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }
    }
}