﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 带教关系
    /// </summary>
    public interface IEmployeeTeachintRelationRepository
    {
        /// <summary>
        /// 获取带教关系信息
        /// </summary>
        /// <param name="employeeIDs">护士工号集合</param>
        /// <returns></returns>
        Task<List<EmployeeTeachingRelationInfo>> GetEmployeeTeachingRelation(List<string> employeeIDs);
        /// <summary>
        /// 通过employeeID获取带教关系
        /// </summary>
        /// <param name="employeeID">护士工号</param>
        /// <returns></returns>
        Task<List<EmployeeTeachingRelationInfo>> GetTeachRelationByEmployeeID(string employeeID);
        /// <summary>
        /// 通过主键ID获取带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationID">主键ID</param>
        /// <returns></returns>
        Task<EmployeeTeachingRelationInfo> GetTeachRelationByID(int employeeTeachingRelationID);
    }
}
