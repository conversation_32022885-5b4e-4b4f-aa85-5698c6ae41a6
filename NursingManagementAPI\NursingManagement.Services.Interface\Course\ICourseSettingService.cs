﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 培训课程字典IService
    /// </summary>
    public interface ICourseSettingService
    {
        /// <summary>
        /// 根据课程类别获取对应的课程字典
        /// </summary>
        /// <param name="courseTypeID"></param>
        /// <returns></returns>
        Task<List<CourseSettingView>> GetCourseSettingListAsync(string courseTypeID);
        /// <summary>
        /// 删除培训课程字典配置
        /// </summary>
        /// <param name="courseSettingID">课程字典主键ID</param>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        Task<bool> DeleteCourseSettingAsync(string courseSettingID,string employeeID);
        /// <summary>
        /// 保存课程字典
        /// </summary>
        /// <param name="courseSettingInfo"></param>
        /// <param name="employeeID"></param>
        /// <param name="hospitalID"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<bool> SaveCourseSettingAsync(CourseSettingView courseSettingInfo, string employeeID, string hospitalID, int departmentID); 
        /// <summary>
        /// 根据课程名称模糊查询课程信息
        /// </summary>
        /// <param name="courseName"></param>
        /// <param name="courseTypeID"></param>
        /// <returns></returns>
        Task<List<CourseSettingView>> SearchCourseSettingAsync(string courseName,string courseTypeID);
    }
}
