﻿namespace NursingManagement.Services.Interface
{
    public interface IMenuService
    {
        /// <summary>
        ///  根据menuType获取菜单清单
        /// </summary>
        /// <param name="roles"></param>
        /// <param name="menuType"></param>
        /// <param name="clientType"></param>
        /// <returns></returns>
        Task<object> GetMenuListByRole(List<int> roles, string menuType, int clientType);
    }
}
