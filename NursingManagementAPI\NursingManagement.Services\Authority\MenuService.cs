﻿using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class MenuService : IMenuService
    {
        private readonly IAuthorityCommonService _authorityCommonService;

        public MenuService(
             IAuthorityCommonService authorityCommonService
        )
        {
            _authorityCommonService = authorityCommonService;
        }

        public async Task<object> GetMenuListByRole(List<int> roles, string menuType, int clientType)
        {
            if (string.IsNullOrWhiteSpace(menuType))
            {
                return null;
            }
            var ret = await _authorityCommonService.GetMenuAndRouterList(roles, clientType);
            if (ret == null)
            {
                return null;
            }
            var menus = ret.Item1;
            // 只有PC端才需要过滤menuType
            if (clientType == 1)
            {
                menus = menus.Where(t => t.MenuType.Trim() == menuType.Trim()).ToList();
            }
            //组装菜单
            var menuList = CreateMenuList(menus, ret.Item2,clientType);
            // 非主菜单或非PC端，直接返回
            if (menuType != "Main" || clientType != 1)
            {
                return menuList;
            }
            // 主菜单需要组装顶部菜单
            var topMenuList = CreateTopMenuList(ret.Item1, ret.Item2);
            return new MenuView() { MainMenuList = menuList, TopMenuList = topMenuList };
        }

        /// <summary>
        /// 组装菜单清单
        /// </summary>
        /// <param name="menuList"></param>
        /// <param name="routerList"></param>
        /// <returns></returns>
        private List<MenuItem> CreateMenuList(List<MenuListInfo> menuList, List<RouterListInfo> routerList, int clientType)
        {
            var ret = new List<MenuItem>();
            var oneLevelMenuList = menuList.Where(t => t.MenuLevel == 1).ToList();
            foreach (var menu in oneLevelMenuList)
            {
                var menuItem = CreateMenuItem(menu, routerList, menuList, clientType);
                if (menuItem == null)
                {
                    continue;
                }
                ret.Add(menuItem);
            }
            return ret.OrderBy(t => t.Sort).ToList();
        }

        /// <summary>
        /// 创建MenuItem
        /// </summary>
        /// <param name="menu"></param>
        /// <param name="routerList"></param>
        /// <param name="menuList"></param>
        /// <returns></returns>
        private MenuItem CreateMenuItem(MenuListInfo menu, List<RouterListInfo> routerList, List<MenuListInfo> menuList, int clientType)
        {
            var router = routerList.Find(t => t.RouterListID == menu.RouterListID);
            var children = CreateChildMenuList(menuList, menu, routerList, clientType);
            // 没有路由且没有子菜单视为无效菜单
            if (router == null && (children == null || children.Count <= 0))
            {
                return null;
            }
            // PC端将带参数的路由地址转换为动态参数，移动端不转换
            var routerPath = clientType == 1 ? _authorityCommonService.AssemblePath(router?.Path?.Trim(), false) : router?.Path?.Trim();
            var menuItem = new MenuItem
            {
                MenuID = menu.MenuListID,
                ParentID = menu.ParentID,
                MenuName = menu.MenuName.Trim(),
                Router = routerPath,
                IconName = menu.IconName?.Trim(),
                Sort = menu.Sort,
                Children = children
            };
            return menuItem;
        }

        /// <summary>
        /// 递归获取子菜单
        /// </summary>
        /// <param name="menuList"></param>
        /// <param name="menu"></param>
        /// <param name="routerList"></param>
        /// <returns></returns>
        private List<MenuItem> CreateChildMenuList(List<MenuListInfo> menuList, MenuListInfo menu, List<RouterListInfo> routerList, int clientType)
        {
            var tempChildMenuList = menuList.Where(t => t.ParentID == menu.MenuListID && t.MenuLevel != menu.MenuLevel).ToList();
            if (tempChildMenuList.Count <= 0)
            {
                return null;
            }
            var children = new List<MenuItem>();
            var childMenuList = tempChildMenuList;
            // 移动端 需要将顶部菜单转换为正常的子菜单
            if (clientType == 2)
            {
                childMenuList = new List<MenuListInfo>();
                foreach (var childMenu in tempChildMenuList)
                {
                    var router = routerList.Find(t => t.RouterListID == childMenu.RouterListID);
                    // 判断子项是否为顶部菜单
                    if (router == null )
                    {
                        var tempMenuList = menuList.Where(t => t.ParentID == childMenu.MenuListID && t.MenuLevel != childMenu.MenuLevel).ToList();                        
                        if (tempMenuList.Count > 0 && tempMenuList[0].MenuType == "TopMenu")
                        {
                            // 借IconName字段返回父菜单名称，用于移动端前端呈现
                            tempMenuList.ForEach(m => m.IconName = childMenu.MenuName);
                            childMenuList.AddRange(tempMenuList);
                            continue;
                        }
                    }
                    // 正常的菜单配置 子菜单 的sort为父菜单sort*10这里需要将父菜单sort降级，保证菜单顺序和PC端一致
                    // 克隆一个对象，防止数据污染
                    var newChileMenu = childMenu.Clone() as MenuListInfo;
                    newChileMenu.Sort *= 10;
                    childMenuList.Add(newChileMenu);
                }
                if (childMenuList.Count <= 0)
                {
                    return null;
                }
            }
            foreach (var childMenu in childMenuList)
            {
                var menuItem = CreateMenuItem(childMenu, routerList, menuList, clientType);
                if (menuItem == null)
                {
                    continue;
                }
                children.Add(menuItem);
            }
            return children.OrderBy(t => t.Sort).ToList();
        }

        /// <summary>
        /// 组装顶部菜单
        /// </summary>
        /// <param name="menuList"></param>
        /// <param name="routerList"></param>
        /// <returns></returns>
        private Dictionary<string, List<TopMenu>> CreateTopMenuList(List<MenuListInfo> menuList, List<RouterListInfo> routerList)
        {
            var ret = new Dictionary<string, List<TopMenu>>();
            var topMenuList = menuList.Where(t => t.TopParentRouterID != null).ToList();
            var topParentRouterIDs = topMenuList.Select(t => t.TopParentRouterID).Distinct().ToList();
            foreach (var topParentRouterID in topParentRouterIDs)
            {
                var parentRouter = routerList.Find(t => t.RouterListID == topParentRouterID);
                if (parentRouter == null)
                {
                    continue;
                }
                var key = parentRouter?.Name?.Trim();
                var topMenus = topMenuList.Where(m => m.TopParentRouterID == topParentRouterID).ToList();
                var menuItems = new List<TopMenu>();
                foreach (var menu in topMenus)
                {
                    var router = routerList.Find(t => t.RouterListID == menu.RouterListID);
                    var routerPath = _authorityCommonService.AssemblePath(router?.Path?.Trim(), false);
                    var topMenu = new TopMenu
                    {
                        MenuName = menu.MenuName.Trim(),
                        RouterName = router?.Name?.Trim(),
                        RouterPath = routerPath,
                        ParentMenuListID = menu.ParentID ?? 0,
                        Sort = menu.Sort,
                    };
                    menuItems.Add(topMenu);
                }
                menuItems = menuItems.OrderBy(t => t.Sort).ToList();
                ret.Add(key, menuItems);
            }
            return ret;
        }
    }
}
