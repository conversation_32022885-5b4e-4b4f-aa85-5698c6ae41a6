﻿using System.Collections;
using System.Text;

namespace NursingManagement.Common
{
    public static class MultipartFormDataHelper
    {
        /// <summary>
        /// 将一个普通对象实体转成MultipartFormDataContent对象
        /// </summary>
        /// <param name="view">要转换的实例view</param>
        /// <returns>转换后的MultipartFormDataContent实例</returns>
        public static MultipartFormDataContent ConvertToMultipartFormDataContentAsync(object view)
        {
            var multipartContent = new MultipartFormDataContent();
            multipartContent = AddObjectToMultipartContent(multipartContent, view, string.Empty);
            return multipartContent;
        }

        /// <summary>
        /// 将实体对象转成FormData格式
        /// </summary>
        /// <param name="multipartContent">FormData实例</param>
        /// <param name="view">要转换的实例view</param>
        /// <param name="prefix">每个嵌套属性的前缀</param>
        /// <returns>MultipartFormDataContent实例</returns>
        private static MultipartFormDataContent AddObjectToMultipartContent(MultipartFormDataContent multipartContent, object view, string prefix)
        {
            if (view == null)
            {
                return multipartContent;
            }

            var objType = view.GetType();
            var properties = objType.GetProperties();

            foreach (var property in properties)
            {
                var propValue = property.GetValue(view);
                var propName = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}.{property.Name}";
                if (propValue == null)
                {
                    continue;
                }
                if (propValue is IEnumerable && !(propValue is string))
                {
                    int index = 0;
                    foreach (var item in (IEnumerable)propValue)
                    {
                        AddObjectToMultipartContent(multipartContent, item, $"{propName}[{index}]");
                        index++;
                    }
                    continue;
                }
                if (propValue.GetType().IsClass && propValue.GetType() != typeof(string))
                {
                    AddObjectToMultipartContent(multipartContent, propValue, propName);
                    continue;
                }
                var stringContent = new StringContent(propValue.ToString(), Encoding.UTF8, "application/json");
                multipartContent.Add(stringContent, propName);
                
            }
            return multipartContent;
        }
    }
}
