﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 年度计划-执行项目维护
    /// </summary>
    [Route("api/AnnualPlanIntervention")]
    [ApiController]
    [EnableCors("any")]
    public class AnnualPlanInterventionController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IAnnualPlanInterventionService _annualPlanInterventionService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="session"></param>
        /// <param name="annualPlanFormulateService"></param>
        public AnnualPlanInterventionController(ISessionService session, IAnnualPlanInterventionService annualPlanFormulateService)
        {
            _session = session;
            _annualPlanInterventionService = annualPlanFormulateService;
        }

        /// <summary>
        /// 获取按MainGoalID分组的项目明细集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetProjectDetailsGroupedByMainGoal")]
        public async Task<IActionResult> GetProjectDetailsGroupedByMainGoal(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanInterventionService.GetProjectDetailsGroupedByMainGoal(mainID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取某年度计划之执行项目列表
        /// </summary>
        /// <param name="mainID">业务主表ID</param>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualInterventions")]
        public async Task<IActionResult> GetAnnualInterventions(string mainID, string projectDetailID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanInterventionService.GetAnnualInterventions(mainID, projectDetailID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取当前计划已有的负责人选项情况
        /// </summary>
        /// <param name="planMainID">业务主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRecPrincipalOptions")]
        public async Task<IActionResult> GetRecPrincipalOptions(string planMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanInterventionService.GetRecPrincipalOptions(planMainID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存执行项目
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualInterventions")]
        public async Task<IActionResult> SaveAnnualInterventions([FromBody] AnnualInterventionSaveView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(saveView, session);
            result.Data = await _annualPlanInterventionService.SaveAnnualIntervention(saveView);
            return result.ToJson();
        }
        /// <summary>
        /// 删除执行项目主表数据
        /// </summary>
        /// <param name="interventionMainID">执行项目主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteAnnualIntervention")]
        public async Task<IActionResult> DeleteAnnualIntervention(string interventionMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanInterventionService.DeleteAnnualInterventionByID(interventionMainID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取年度计划计划总览数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanGeneralView")]
        public async Task<IActionResult> GetAnnualPlanGeneralView(int year, int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanInterventionService.GetAnnualPlanGeneralView(year, departmentID != 0 ? departmentID : session.DepartmentID);
            return result.ToJson();
        }
    }
}
