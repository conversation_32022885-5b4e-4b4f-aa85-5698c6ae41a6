﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("TrainingEvaluationDetail")]
    public class TrainingEvaluationDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 培训评价明细ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string TrainingEvaluationDetailID { get; set; }
        /// <summary>
        /// 培训评价主表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string TrainingEvaluationMainID { get; set; }
        /// <summary>
        /// 明细字典ID
        /// </summary>
        public int ItemID { get; set; }
        /// <summary>
        /// 结果
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string Value { get; set; }
        /// <summary>
        /// 分组ID
        /// </summary>
        public int? GroupID { get; set; }
        /// <summary>
        /// 父级ID
        /// </summary>
        public int? ParentID { get; set; }
    }
}
