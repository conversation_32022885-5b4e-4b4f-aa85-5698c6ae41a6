﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class PatientProfileDetailRepository : IPatientProfileDetailRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public PatientProfileDetailRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据主记录ID 获取明细记录
        /// </summary>
        /// <param name="patientProfileRecordID"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileDetailInfo>> GetDataByPatientProfileRecordID(string patientProfileRecordID)
        {
            return await _dbContext.PatientProfileDetailInfos.Where(m=>m.PatientProfileRecordID==patientProfileRecordID&&m.DeleteFlag!="*").ToListAsync();
        }
        /// <summary>
        /// 根据主记录ID集合 获取明细记录
        /// </summary>
        /// <param name="patientProfileRecordIDs"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileDetailInfo>> GetDataByPatientProfileRecordIDList(List<string> patientProfileRecordIDs)
        {
            return await _dbContext.PatientProfileDetailInfos.Where(m => patientProfileRecordIDs.Contains(m.PatientProfileRecordID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主记录ID获取明细记录
        /// </summary>
        /// <param name="patientProfileRecordID"></param>
        /// <returns></returns>
        public async Task<List<PatientProfileDetailInfo>> GetDataByPatientProfileRecordIDList(string patientProfileRecordID)
        {
            return await _dbContext.PatientProfileDetailInfos.Where(m => patientProfileRecordID == m.PatientProfileRecordID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}