﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class TrainingEvaluationDetailRepository : ITrainingEvaluationDetailRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public TrainingEvaluationDetailRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据培训评价主表ID获取明细数据
        /// </summary>
        /// <param name="trainingEvaluationMainID"></param>
        /// <returns></returns>
        public async Task<List<TrainingEvaluationDetailInfo>> GetDetailListByMainID(string trainingEvaluationMainID)
        {
            return await _dbContext.TrainingEvaluationDetailInfos.Where(m => m.TrainingEvaluationMainID == trainingEvaluationMainID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
