﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 年度计划-计划维护
    /// </summary>
    [Route("api/AnnualPlanMain")]
    [ApiController]
    [EnableCors("any")]
    public class AnnualPlanMaintainController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualPlanMaintainService _annualPlanMainService;
        private readonly IAnnualGenerateService _annualGenerateService;
        private readonly ISessionService _session;
        /// <summary>
        /// 年度计划
        /// </summary>
        /// <param name="mainGoalService"></param>
        /// <param name="session"></param>
        /// <param name="annualGenerateService"></param>
        public AnnualPlanMaintainController(
            ISessionService session
            , IAnnualPlanMaintainService mainGoalService
            , IAnnualGenerateService annualGenerateService)
        {
            _annualPlanMainService = mainGoalService;
            _session = session;
            _annualGenerateService = annualGenerateService;
        }
        #region 查询
        /// <summary>
        /// 查询本人及上下级已制定的年度计划
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBrowseAPViews")]
        public async Task<IActionResult> GetBrowseAPViews(int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetBrowseAPViews(year, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 查询本人部门及上下级部门
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBrowseAPDepartments")]
        public async Task<IActionResult> GetBrowseAPDepartments(int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetBrowseAPDepartments(year, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据年份、部门ID获取年度计划ID
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPlanMainViewsByYear")]
        public async Task<IActionResult> GetPlanMainViewsByYear(int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetCurrentYearAPViewsByEmployee(year, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取当前年度计划的分类
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanTypes")]
        public async Task<IActionResult> GetAnnualPlanTypes(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetAnnualPlanTypes(mainID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取年度计划详情
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanContent")]
        public async Task<IActionResult> GetAnnualPlanContent(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetAnnualPlanContent(mainID);
            return result.ToJson();
        }
        #endregion
        #region 分类&目标
        /// <summary>
        /// 分类排序（批量排序目标）
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SortAPType")]
        public async Task<IActionResult> SortAPType([FromBody] APTypeSortView sortView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.SortAPType(sortView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 目标排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SortAPGoal")]
        public async Task<IActionResult> SortAPGoal([FromBody] APGoalSortView sortView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.SortAPGoal(sortView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取今年的年度计划分类-目标
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanMainGoalList")]
        public async Task<IActionResult> GetAnnualPlanMainGoalList(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _annualPlanMainService.GetAnnualPlanMainGoalList(departmentID, DateTime.Now.Year);
            return result.ToJson();
        }
        /// <summary>
        /// 获取年度计划分类-目标的新序号
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAPMainGoalSortDict")]
        public async Task<IActionResult> GetAPMainGoalSortDict(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _annualPlanMainService.GetAPMainGoalSortDict(mainID);
            return result.ToJson();
        }
        #endregion
        #region 分组
        /// <summary>
        /// 获取负责部门候选项
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentOptions")]
        public async Task<IActionResult> GetDepartmentOptions(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetDepartmentOptions(mainID);
            return result.ToJson();
        }
        /// <summary>
        /// 分组保存
        /// </summary>
        /// <param name="saveView">分组更新View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualPlanGroup")]
        public async Task<IActionResult> SaveAnnualPlanGroup([FromBody] SaveGroupView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(saveView, session);
            result.Data = await _annualPlanMainService.SaveAnnualPlanGroup(saveView);
            return result.ToJson();
        }
        /// <summary>
        /// 分组删除
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteAnnualGoalGroup")]
        public async Task<IActionResult> DeleteAnnualGoalGroup(string groupID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.DeleteAnnualGoalGroup(groupID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 分类排序（批量排序目标）
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SortAPGroup")]
        public async Task<IActionResult> SortAPGroup([FromBody] APGroupSortView sortView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.SortAPGroup(sortView, session.EmployeeID);
            return result.ToJson();
        }
        #endregion
        #region 指标明细
        /// <summary>
        /// 获取指标明细
        /// </summary>
        /// <param name="view">查询view</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetIndicatorDetails")]
        public async Task<IActionResult> GetIndicatorDetails(APDetailsSearchView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetIndicatorDetails(view);
            return result.ToJson();
        }
        /// <summary>
        /// 获取指标字典及当前计划中未引用的指标ID集合
        /// </summary>
        /// <param name="mainID">业务主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRefIndicatorIDs")]
        public async Task<IActionResult> GetRefIndicatorIDs(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetRefIndicatorIDs(mainID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取指标明细历年情况
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">组织ID</param>
        /// <param name="indicatorID">指标ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPastYearIndicators")]
        public async Task<IActionResult> GetPastYearIndicators(int year, int departmentID, int indicatorID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetPastYearIndicators(year, departmentID, indicatorID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取某分组下的指标明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetIndicatorDetailsByGroupID")]
        public async Task<IActionResult> GetIndicatorDetailsByGroupID(string mainID, string groupID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetIndicatorDetailsByGroupID(mainID, groupID);
            return result.ToJson();
        }
        /// <summary>
        /// 新增指标明细
        /// </summary>
        /// <param name="addView">新的指标明细</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddIndicatorDetail")]
        public async Task<IActionResult> AddIndicatorDetail([FromBody] SaveIndicatorDetailView addView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(addView, session);
            result.Data = await _annualPlanMainService.AddIndicatorDetail(addView);
            return result.ToJson();
        }
        /// <summary>
        /// 更新指标明细
        /// </summary>
        /// <param name="updateView">新的指标明细</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateIndicatorDetail")]
        public async Task<IActionResult> UpdateIndicatorDetail([FromBody] UpdateIndicatorDetailView updateView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(updateView, session);
            result.Data = await _annualPlanMainService.UpdateIndicatorDetail(updateView);
            return result.ToJson();
        }
        /// <summary>
        /// 删除指标明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="detailID">指标明细ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteIndicatorDetail")]
        public async Task<IActionResult> DeleteIndicatorDetail(string mainID, string detailID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.DeleteIndicatorDetail(mainID, detailID, session.EmployeeID);
            return result.ToJson();
        }
        #endregion
        #region 项目明细
        /// <summary>
        /// 获取项目明细
        /// </summary>
        /// <param name="view">查询View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetProjectDetails")]
        public async Task<IActionResult> GetProjectDetails(APDetailsSearchView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetProjectDetails(view.MainID, view.MainGoalIDs);
            return result.ToJson();
        }
        /// <summary>
        /// 获取某分组下的项目明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetProjectDetailsByGroupID")]
        public async Task<IActionResult> GetProjectDetailsByGroupID(string mainID, string groupID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetProjectDetailsByGroupID(mainID, groupID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取上级部门的项目明细，用于建议
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="goalID">目标ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSuperiorProjectDetail")]
        public async Task<IActionResult> GetSuperiorProjectDetail(int departmentID, int goalID, int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _annualPlanMainService.GetSuperiorProjectDetail(departmentID, year, goalID);
            return result.ToJson();
        }
        /// <summary>
        /// 新增项目明细
        /// </summary>
        /// <param name="addView">新的指标明细</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddProjectDetail")]
        public async Task<IActionResult> AddProjectDetail([FromBody] SaveProjectDetailView addView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(addView, session);
            result.Data = await _annualPlanMainService.AddProjectDetail(addView);
            return result.ToJson();
        }
        /// <summary>
        /// 更新工作项目
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateProjectDetail")]
        public async Task<IActionResult> UpdateProjectDetail([FromBody] UpdateProjectDetailView updateView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(updateView, session);
            result.Data = await _annualPlanMainService.UpdateProjectDetail(updateView);
            return result.ToJson();
        }
        /// <summary>
        /// 删除项目明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="detailID">项目明细ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteProjectDetail")]
        public async Task<IActionResult> DeleteProjectDetail(string mainID, string detailID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.DeleteProjectDetail(mainID, detailID, session.EmployeeID);
            return result.ToJson();
        }
        #endregion
        /// <summary>
        /// 复制新一年数据
        /// </summary>
        /// <param name="departmentID">部门ID，传空则全部</param>
        /// <param name="year">新一年度</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GenAnnualPlanData")]
        public async Task<IActionResult> GenAnnualPlanData(int? departmentID, int year)
        {
            var result = new ResponseResult
            {
                Data = await _annualGenerateService.GenAnnualPlanData(departmentID, year)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 发布年度计划
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("PublishAnnualPlan")]
        public async Task<IActionResult> PublishAnnualPlan(string mainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.PublishAnnualPlan(mainID, session.EmployeeID);
            return result.ToJson();
        }
    }
}
