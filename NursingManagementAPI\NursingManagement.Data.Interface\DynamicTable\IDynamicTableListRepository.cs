﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDynamicTableListRepository
    {
        /// <summary>
        /// 根据表格分类和表格子类获取数据
        /// </summary>
        /// <param name="tableType"></param>
        /// <param name="tableSubType"></param>
        /// <returns></returns>
        Task<DynamicTableListInfo> GetDataByType(string tableType, string tableSubType);
        /// <summary>
        /// 根据表格分类和表格子类获取DynamicTableListID
        /// </summary>
        /// <param name="tableType"></param>
        /// <param name="tableSubType"></param>
        /// <returns></returns>
        Task<int> GetIDByType(string tableType, string tableSubType);
    }
}
