﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;

namespace NursingManagement.Data.Context
{
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 人员角色表
        /// </summary>
        public DbSet<EmployeeRoleInfo> EmployeeRoleInfos { get; set; }
        /// <summary>
        /// 权限列表
        /// </summary>
        public DbSet<AuthorityListInfo> AuthorityListInfos { get; set; }
        /// <summary>
        /// 角色列表
        /// </summary>
        public DbSet<AuthorityRoleInfo> AuthorityRoleInfos { get; set; }
        /// <summary>
        /// 角色权限列表
        /// </summary>
        public DbSet<AuthorityRoleListInfo> AuthorityRoleListInfos { get; set; }
        /// <summary>
        /// 菜单列表
        /// </summary>
        public DbSet<MenuListInfo> MenuListInfos { get; set; }
        /// <summary>
        /// 前端画面列表
        /// </summary>
        public DbSet<RouterListInfo> RouterListInfos { get; set; }       
        /// <summary>
        /// 前端画面使用组件权限
        /// </summary>
        public DbSet<AuthorityComponentListInfo> AuthorityComponentListInfos { get; set; }
        /// <summary>
        /// 人员多部门权限表
        /// </summary>
        public DbSet<EmployeeDepartmentSwitchInfo> EmployeeDepartmentSwitchInfos { get; set; }
        
    }
}
