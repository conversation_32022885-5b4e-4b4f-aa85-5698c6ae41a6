﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IRuleDetailListRepository : ICacheRepository
    {
        /// <summary>
        /// 根据主表ID获取数据
        /// </summary>
        /// <param name="ruleListID"></param>
        /// <returns></returns>
        Task<List<RuleDetailListInfo>> GetListByID(int ruleListID);
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        Task<List<RuleDetailListInfo>> GetAllCacheAsync();
        /// <summary>
        /// 根据主表ID获取数据
        /// </summary>
        /// <param name="ruleListIDs"></param>
        /// <returns></returns>
        Task<List<RuleDetailListInfo>> GetListByIDs(List<int> ruleListIDs);
        /// <summary>
        /// 获取最大值（主键）
        /// </summary>
        /// <returns></returns>
        Task<int> GetMaxID();
    }
}
