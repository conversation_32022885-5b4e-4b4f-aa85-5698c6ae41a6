﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class PostDescriptionRepository : IPostDescriptionRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;

        public PostDescriptionRepository(
            NursingManagementDbContext db
        )
        {
            _nursingManagementDbContext = db;
        }
        /// <summary>
        /// 获取岗位说明书
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="postID">部门岗位编码</param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<PostDescriptionInfo> GetData(int departmentID, int postID, string hospitalID, int language)
        {
            return await _nursingManagementDbContext.PostDescriptionInfos.Where(m => m.PostID == postID && m.DepartmentID == departmentID && m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据部门ID获取科室岗位说明书
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<List<PostDescriptionInfo>> GetListByDeparment(int departmentID, string hospitalID, int language)
        {
            return await _nursingManagementDbContext.PostDescriptionInfos.Where(m => m.DepartmentID == departmentID && m.HospitalID == hospitalID && m.Language == language  && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        ///  根据岗位编码，部门ID,岗位编号获取数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="postID"></param>
        /// <param name="postDescriptionCode"></param>
        /// <param name="version"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<PostDescriptionInfo> GetByCode(int departmentID, int postID, string postDescriptionCode,string version, string hospitalID, int language)
        {
            return await _nursingManagementDbContext.PostDescriptionInfos.Where(m => m.PostID == postID && m.DepartmentID == departmentID && m.PostDescriptionCode == postDescriptionCode && m.Version == version && m.HospitalID == hospitalID 
            && m.Language == language && m.DeleteFlag != "*" ).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取删除的数据，根据岗位编码，部门ID,岗位编号
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="postID"></param>
        /// <param name="postDescriptionCode"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<PostDescriptionInfo> GetDeleteByCode(int departmentID, int postID, string postDescriptionCode, string hospitalID, int language)
        {
            return await _nursingManagementDbContext.PostDescriptionInfos.Where(m => m.PostID == postID && m.DepartmentID == departmentID && m.PostDescriptionCode == postDescriptionCode && m.HospitalID == hospitalID
            && m.Language == language && m.DeleteFlag == "*").FirstOrDefaultAsync();
        }
    }
}
