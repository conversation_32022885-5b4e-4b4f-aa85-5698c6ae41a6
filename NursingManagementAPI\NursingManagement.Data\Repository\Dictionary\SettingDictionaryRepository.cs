﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class SettingDictionaryRepository : ISettingDictionaryRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="db"></param>
        /// <param name="redisService"></param>
        /// <param name="sessionCommonServer"></param>
        public SettingDictionaryRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<SettingDictionaryInfo>> GetAllCacheAsync()
        {
            return (List<SettingDictionaryInfo>)await GetCacheAsync();
        }

        public async Task<bool> GetSettingSwitch(SettingDictionaryParams settingDictionaryParams)
        {
            var datas = await GetAllCacheAsync();
            var data = datas.Where(m => m.DataType == "2")
                            .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingType), m => m.SettingType == settingDictionaryParams.SettingType)
                            .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingTypeCode), m => m.SettingTypeCode == settingDictionaryParams.SettingTypeCode)
                            .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingTypeValue), m => m.SettingTypeValue == settingDictionaryParams.SettingTypeValue)
                            .FirstOrDefault();

            if (data != null && bool.TryParse(data.SettingValue.Trim(), out bool ret))
            {
                return ret;
            }
            return false;
        }

        public async Task<List<SettingDictionaryInfo>> GetSettingSwitchList(SettingDictionaryParams settingDictionaryParams)
        {
            var datas = await GetAllCacheAsync();
            return datas.Where(m => m.DataType == "2")
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingType), m => m.SettingType == settingDictionaryParams.SettingType)
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingTypeCode), m => m.SettingTypeCode == settingDictionaryParams.SettingTypeCode)
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingTypeValue), m => m.SettingTypeValue == settingDictionaryParams.SettingTypeValue)
                        .ToList();
        }

        public async Task<List<SettingDictionaryInfo>> GetSettingDictionary(SettingDictionaryParams settingDictionaryParams)
        {
            var datas = await GetAllCacheAsync();
            if (settingDictionaryParams == null)
            {
                return datas;
            }
            return datas.Where(m => m.DataType == "1")
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingType), m => m.SettingType == settingDictionaryParams.SettingType)
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingTypeCode), m => m.SettingTypeCode == settingDictionaryParams.SettingTypeCode)
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingTypeValue), m => m.SettingTypeValue == settingDictionaryParams.SettingTypeValue)
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.SettingValue), m => m.SettingValue == settingDictionaryParams.SettingValue)
                        .IfWhere(!string.IsNullOrWhiteSpace(settingDictionaryParams.FilterSettingTypeValue), m => m.SettingTypeValue != settingDictionaryParams.FilterSettingTypeValue)
                        .OrderBy(m => m.Sort).ToList();
        }

        public async Task<string> GetSettingValue(SettingDictionaryParams settingDictionaryParams)
        {
            var datas = await GetSettingDictionary(settingDictionaryParams);
            if (datas.Count <= 0)
            {
                return string.Empty;
            }
            return datas[0].SettingValue;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.SettingDictionaryInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.SettingDictionaryInfo.GetKey(_sessionCommonServer);
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }
    }
}
