﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ITrainingEvaluationMainRepository
    {
        /// <summary>
        /// 根据学习人员记录ID和被评价人员ID获取数据
        /// </summary>
        /// <param name="trainingLearnerID">培训人员记录ID</param>
        /// <param name="beEvaluationEmployeeID">被评价人员ID</param>
        /// <returns></returns>
        Task<List<TrainingEvaluationMainInfo>> GetListByLearnerID(string trainingLearnerID, string beEvaluationEmployeeID);
    }
}
