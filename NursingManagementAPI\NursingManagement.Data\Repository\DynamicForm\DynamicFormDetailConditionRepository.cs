﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DynamicFormDetailConditionRepository : IDynamicFormDetailConditionRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public DynamicFormDetailConditionRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }


        public async Task<List<DynamicFormDetailConditionInfo>> GetDetailConditionsByDetailID(string dynamicFormDetailID)
        {
            var datas = await GetCacheAsync() as List<DynamicFormDetailConditionInfo>;
            return datas.Where(m => m.DynamicFormDetailID == dynamicFormDetailID).ToList();
        }

        public async Task<List<DynamicFormDetailConditionInfo>> GetDetailConditionsByDetailIDList(List<string> dynamicFormDetailIDs)
        {
            var datas = await GetCacheAsync() as List<DynamicFormDetailConditionInfo>;
            return datas.Where(m => dynamicFormDetailIDs.Contains(m.DynamicFormDetailID)).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DynamicFormDetailConditionInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.DynamicFormDetailCondition.GetKey(_sessionCommonServer);
        }
    }
}
