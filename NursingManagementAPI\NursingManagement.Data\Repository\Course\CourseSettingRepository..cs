﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data
{
    public class CourseSettingRepository : ICourseSettingRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public CourseSettingRepository(
            NursingManagementDbContext nursingManagementDbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.CourseSettingInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.CourseSetting.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        /// <summary>
        /// 根据课程分类获取对应课程配置
        /// </summary>
        /// <param name="courseTypeID">课程类别</param>
        /// <returns></returns>
        public async Task<List<CourseSettingInfo>> GetCourseSettingsAsync(string courseTypeID)
        {
            var list = await GetCacheAsync() as List<CourseSettingInfo>;
            if (string.IsNullOrEmpty(courseTypeID))
            {
                return list;
            }
            return list.Where(m => m.CourseTypeID == courseTypeID).ToList();
        }
        /// <summary>
        /// 根据主键获取培训课程记录（非缓存）
        /// </summary>
        /// <param name="courseSettingID"></param>
        /// <returns></returns>
        public async Task<CourseSettingInfo> GetCourseSettingAsNoCacheAsync(string courseSettingID)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            return await _nursingManagementDbContext.CourseSettingInfos.Where(m => m.HospitalID == hospitalID && m.CourseSettingID == courseSettingID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据课程ID集合获取数据
        /// </summary>
        /// <param name="courseSettingIDs"></param>
        /// <returns></returns>
        public async Task<List<CourseSettingInfo>> GetCourseListAsync(List<string> courseSettingIDs)
        {
            var list = await GetCacheAsync() as List<CourseSettingInfo>;
            if (courseSettingIDs == null || courseSettingIDs.Count <= 0)
            {
                return new List<CourseSettingInfo>();
            }
            return list.Where(m => courseSettingIDs.Any(n => n == m.CourseSettingID.ToString())).ToList();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<List<CourseSettingInfo>> GetAllCourseSetting()
        {
            return await GetCacheAsync() as List<CourseSettingInfo>;
        }
    }
}
