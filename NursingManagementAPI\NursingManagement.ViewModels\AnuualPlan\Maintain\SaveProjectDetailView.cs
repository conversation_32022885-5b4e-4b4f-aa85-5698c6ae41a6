﻿namespace NursingManagement.ViewModels
{
    public class SaveProjectDetailView
    {
        /// <summary>
        /// 项目明细
        /// </summary>
        public string DetailID { get; set; }
        /// <summary>
        /// 主表GUID
        /// </summary>
        public string MainID { get; set; }
        /// <summary>
        /// 分组GUID
        /// </summary>
        public string GroupID { get; set; }
        /// <summary>
        /// 目标GUID
        /// </summary>
        public string MainGoalID { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 特殊注记
        /// </summary>
        public string MarkID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// HR工号
        /// </summary>
        public string EmployeeID { get; set; }
    }
}
