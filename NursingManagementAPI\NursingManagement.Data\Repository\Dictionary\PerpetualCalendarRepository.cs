﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class PerpetualCalendarRepository : IPerpetualCalendarRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public PerpetualCalendarRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<PerpetualCalendarInfo>> GetCalendarByDate(DateTime startDate, DateTime endDate)
        {
            var data = await GetCacheAsync() as List<PerpetualCalendarInfo>;
            return data.Where(m=>m.Date.Date >= startDate.Date && m.Date.Date<=endDate.Date).ToList();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.PerpetualCalendarInfos.Where(m => m.HospitalID == hospitalID).ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.PerpetualCalendar.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 获取公假日期
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<PerpetualCalendarInfo>> GetPublicLeaveByDate(DateTime startDate, DateTime endDate)
        {
            var data = await GetCacheAsync() as List<PerpetualCalendarInfo>;
            return data.Where(m => m.Date.Date >= startDate.Date && m.Date.Date <= endDate.Date&&m.PublicLeaveFlag==true ).ToList();
        }
    }
}
