﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// CCC患者状况明细表
    /// </summary>
    [Serializable]
    [Table("PatientProfileDetail")]
    public class PatientProfileDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 患者状况明细唯一号
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string PatientProfileDetailID { get; set; }
        /// <summary>
        /// 患者状况主记录唯一号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string PatientProfileRecordID { get; set; }
        /// <summary>
        /// 状况ID（关联动态表格Prop属性）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string DataID { get; set; }
        /// <summary>
        /// 患者状况值
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string DataValue { get; set; }
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime OccurDateTime { get; set; }
    }
}
