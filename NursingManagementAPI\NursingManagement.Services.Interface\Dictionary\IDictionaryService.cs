﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IDictionaryService
    {
        /// <summary>
        /// 获取人事部门字典表
        /// </summary>
        /// <returns></returns>
        Task<List<DictItem>> GetHrmDepartmentDict(); 

        /// <summary>
        /// 获取当前部门、上级部门及从属部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="includeNursingDept">是否包含护理部</param>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetSuperAndSubDepartmentsByID(int departmentID, bool includeNursingDept = true);
        /// <summary>
        /// 获取直接与间接上级部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="func">要返回的数据格式</param>
        /// <returns></returns>
        Task<V> GetUpperDepts<T, V>(int departmentID, Func<DepartmentListInfo, T> func) where V : ICollection<T>, new();
        /// <summary>
        /// 获取国标字典表
        /// </summary>
        /// <param name="administrationParams"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetAdministrationDict(AdministrationParams administrationParams);

        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <param name="postTypeID"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetPostDict(string postTypeID = null);

        /// <summary>
        /// 获取部门岗位数据
        /// </summary>
        /// <param name="departmentID">部门序号</param>
        /// <param name="showAll">显示所有</param>
        /// <param name="postTypeID">岗位类型</param>
        /// <param name="currentDate">传入日期，取时间对应的岗位工作时间</param>
        /// <returns></returns>
        Task<List<PostSelectOptionsView>> GetDepartmentPostDict(int departmentID, bool showAll, string postTypeID = null, DateTime? currentDate = null);

        /// <summary>
        /// 根据部门岗位ID获取岗位字典数据
        /// </summary>
        /// <param name="departmentPostID"></param>
        /// <param name="departmentID"></param>
        /// <param name="currentDate"></param>
        /// <returns></returns>
        Task<PostSelectOptionsView> GetDepartmentPostDictByID(int departmentPostID, int departmentID, DateTime? currentDate = null);
        /// <summary>
        /// 获取能级数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetCapabilityLevelDict(string type = null);

        /// <summary>
        /// 获取人员数据
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="showAll">是否获取全部人员</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetEmployeeDict(int? departmentID, bool showAll);
        /// <summary>
        /// 获取部门级联选择器数据
        /// </summary>
        /// <param name="organizationType">组织架构</param>
        /// <param name="disableDepartmentIDs">禁用科室</param>
        /// <returns></returns>
        Task<List<CascaderView<int>>> GetDepartmentCascaderList(string organizationType, int[] disableDepartmentIDs);
        /// <summary>
        /// 获取注记图示
        /// </summary>
        /// <param name="moduleType"></param>
        /// <param name="groupIDs"></param>
        /// <returns></returns>
        Task<List<AdministrationIconInfo>> GetIconsByModuleType(string moduleType, string[] groupIDs = null);
        /// <summary>
        /// 获取医院列表
        /// </summary>
        /// <returns></returns>
        Task<List<Dictionary<string, string>>> GetHospitalList();
        /// <summary>
        /// 获取医院配置
        /// </summary>
        /// <param name="hospitalID">医院类别</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetHospitalByHospitalIDAsync(string hospitalID);
        /// <summary>
        /// 获取科室对职务数据
        /// </summary>
        /// <param name="departmentID">科室ID，非空时依据过滤后的EmployeeToJobs.JobCode对职务字典过滤</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetDepartmentToJobs(int? departmentID);
        /// <summary>
        /// 模糊查询人员信息
        /// </summary>
        /// <param name="employeeName">姓名</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetEmployeeDataByName(string employeeName);
        /// <summary>
        /// 获取员工的基本信息
        /// </summary>
        /// <param name="employeeIDs">工号</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetEmployeeDataByIDs(string[] employeeIDs);
        /// <summary>
        /// 获取时间段内应出勤天数
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<decimal> GetRequiredAttendanceDays(DateTime startDate, DateTime endDate);
        /// <summary>
        /// 获取部门名称
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<string> GetDepartmentName(int departmentID);
        /// <summary>
        /// 获取部门字典
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        Task<List<EmployeeDepartmentView>> GetDepartmentViewsByOrganizationType(string organizationType);
        /// <summary>
        /// 获取角色权限清单
        /// </summary>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetAuthorityRoles();
        /// <summary>
        /// 根据工号获取所拥有权限的科室
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="organizationType">组织类型</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetEmployeeDepartmentAsync(string employeeID, string organizationType = "1");
        /// <summary>
        /// 根据部门ID 获取部门岗位
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetPostDictByDepartmentID(int departmentID);
        /// <summary>
        /// 获取AppConfigSetting配置
        /// </summary>
        /// <param name="settingType"></param>
        /// <returns></returns>
        Task<List<AppConfigSettingInfo>> GetAppConfigsAsync(string settingType);
        /// <summary>
        /// 根据组件类型获取组件数据
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <returns></returns>
        Task<List<KeyValuePair<int, string>>> GetComponentListByType(string componentType);
        /// <summary>
        /// 获取上级部门选项 | 一般为片区
        /// </summary>
        /// <returns>返回空数组或者选项</returns>
        Task<List<SelectOptionsView>> GetUpperDeptOptions();
        /// <summary>
        /// 获取访视权限
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<CascaderView<int>>> GetVisitsDeptOptionsAsync(string employeeID);
    }
}
