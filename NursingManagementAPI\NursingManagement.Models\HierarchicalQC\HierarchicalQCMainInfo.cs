using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 质控维护记录
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCMain")]
    public class HierarchicalQCMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 质控维护记录主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCMainID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 质控主记录主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCRecordID { get; set; }
        /// <summary>
        /// 质控主题主表主键
        /// </summary>
        public int HierarchicalQCFormID { get; set; }
        /// <summary>
        /// 被质控部门，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 质控结果
        /// </summary>
        public decimal? Result { get; set; }
        /// <summary>
        /// 质控指导意见
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string Guidance { get; set; }
        /// <summary>
        /// 是否已阅读
        /// </summary>
        public bool? IsReadFlag { get; set; }
        /// <summary>
        /// 科室改进内容
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string Improvement { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 审核状态：0、待提交||审批未通过（当AuditDateTime为空的时候），1、待审批，2、审批通过
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string AuditStatus { get; set; }
        /// <summary>
        /// 审核人员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string VerifierEmployeeID { get; set; }
        /// <summary>
        /// 考核时间
        /// </summary>
        public DateTime? AssessDate { get; set; }
        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditDateTime { get; set; }
        /// <summary>
        /// 申诉原因(常态流程控制在用20240422)
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string Reason { get; set; }
    }
}
