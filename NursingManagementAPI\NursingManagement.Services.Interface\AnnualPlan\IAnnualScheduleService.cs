﻿using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Commands;

namespace NursingManagement.Services.Interface
{
    public interface IAnnualScheduleService
    {
        /// <summary>
        /// 删除年度计划排程
        /// </summary>
        /// <param name="scheduleMainID">白城主表ID</param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<(bool, string message)> DeleteAnnualScheduleAsync(string scheduleMainID, string employeeID);
        /// <summary>
        /// 获取年度计划排程对应明细
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        Task<List<AnnualScheduleDetailView>> GetAnnualScheduleDetailsAsync(string scheduleMainID);
        /// <summary>
        /// 获取年度计划主记录集合（根据执行人、年、月）
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="scheduleMonth">计划月份</param>
        /// <param name="scheduleYear">计划年度</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<AnnualScheduleMainView[]> GetAPSchedules(string employeeID, int? scheduleMonth, int scheduleYear, int departmentID);
        /// <summary>
        /// 获取排程统计数据
        /// </summary>
        /// <param name="schedulePerformer"></param>
        /// <param name="scheduleYear"></param>
        /// <returns></returns>
        Task<List<ScheduleStatisticsView>> GetAnnualScheduleStatisticsAsync(string schedulePerformer, int scheduleYear);
        /// <summary>
        /// 保存年度计划排程
        /// </summary>
        /// <param name="scheduleParamsView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> SaveAnnualScheduleAsync(AnnualScheduleParamsView scheduleParamsView, string employeeID);
        /// <summary>
        /// 某月未执行排程统计量
        /// </summary>
        /// <param name="scheduleYear"></param>
        /// <param name="scheduleMonth"></param>
        /// <param name="schedulePerformer"></param>
        /// <returns></returns>
        Task<int?> GetStatisticValueByMonthAsync(int scheduleYear, int scheduleMonth, string schedulePerformer);
        /// <summary>
        /// 发送年度计划消息提醒
        /// </summary>
        /// <returns></returns>
        Task<bool> SendAnnualPlanRemindAsync();
        /// <summary>
        /// 展出下月任务
        /// </summary>
        /// <param name="cmd">命令</param>
        /// <returns></returns>
        Task<bool> CreateOrUpdateTasks(CreateOrUpdateTasksCommand cmd);
        /// <summary>
        /// 优先获取当前部门计划对应的未执行排程数据）
        /// </summary>
        /// <param name="employeeID">计划执行人</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="preOrNextFlag">获取计划月份之前OR之后，True：当月和当月之前；False：之后一个月</param>
        /// <returns></returns>
        //Task<AnnualScheduleMainView[]> GetUnExecTasks(string employeeID, int departmentID, int? scheduleMonth, bool preOrNextFlag);
        /// <summary>
        /// 获取措施来源
        /// </summary>
        /// <param name="annualScheduleMainID">年度计划措施ID</param>
        /// <param name="interventionID">措施ID</param>
        /// <returns></returns>
        Task<List<string>> GetTaskSource(string annualScheduleMainID, int interventionID);
        /// <summary>
        /// 手动添加年度计划
        /// </summary>
        /// <param name="scheduleDate">预计执行日期</param>
        /// <param name="annualScheduleContent">措施内容</param>
        /// <param name="hospitalID">医院代码</param>
        /// <param name="employeID">用户ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<bool> AddAnnualScheduleManual(DateTime scheduleDate, string annualScheduleContent, string hospitalID, string employeID, int departmentID);
    }
}
