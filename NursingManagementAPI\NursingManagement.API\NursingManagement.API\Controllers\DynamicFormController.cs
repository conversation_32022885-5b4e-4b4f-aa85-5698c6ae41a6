﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 动态表单相关接口
    /// </summary>
    [Produces("application/json")]
    [Route("api/dynamicForm")]
    [EnableCors("any")]
    public class DynamicFormController : ControllerBase
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IDynamicFormService _dynamicFormService;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="serverSession"></param>
        /// <param name="dynamicFormService"></param>
        public DynamicFormController(
            ISessionService serverSession,
            IDynamicFormService dynamicFormService
        )
        {
            _session = serverSession;
            _dynamicFormService = dynamicFormService;
        }

        /// <summary>
        /// 根据表单ID获取表单模板
        /// </summary>
        /// <param name="dynamicFormRecordID"></param>
        /// <returns ></returns>
        [HttpGet]
        [Route("GetFormTemplateByRecordID")]
        public async Task<IActionResult> GetFormTemplateByRecordID(string dynamicFormRecordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _dynamicFormService.GetFormTemplateByRecordID(dynamicFormRecordID, null, null, null);
            return result.ToJson();
        }

        /// <summary>
        /// 保存表单模板
        /// </summary>
        /// <param name="formTemplateView"></param>
        /// <returns ></returns>
        [HttpPost]
        [Route("SaveFormTemplate")]
        public async Task<IActionResult> SaveFormTemplate([FromBody] FormTemplateView formTemplateView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var ret = await _dynamicFormService.SaveFormTemplate(formTemplateView, session.EmployeeID);
            if (string.IsNullOrWhiteSpace(ret))
            {
                result.Error("保存失败！");
            }
            else
            {
                result.Data = ret;
            }
            return result.ToJson();
        }
    }
}
