﻿ 
using NLog;
using NursingManagement.Data.Interface;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace NursingManagement.Data
{
    public class CacheFactory : ICacheFactory
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private static ConcurrentDictionary<string, ICacheRepository> _cacheRepositories
            = new();

        public CacheFactory(
            //  IAuthorityListRepository authorityListRepository             
            //, IHospitalListRepository hospitalListRepository            
            //, IDepartmentListRepository departmentListRepository            
            //, IAuthorityRoleListRepository authorityRoleListRepository           
            // IAppConfigSettingRepository appConfigSettingRepository            
            //, IMenuListRepository menuListRepository
            
            )
        {
            //Add(authorityListRepository);       
            //Add(hospitalListRepository);            
            //Add(departmentListRepository);            
            //Add(authorityRoleListRepository);             
          //  Add(appConfigSettingRepository);             
            //Add(menuListRepository);
            
        }

        public void Add(ICacheRepository _cacheRepository)
        {
            string type = _cacheRepository.GetCacheType();
            try
            {
                if (_cacheRepositories == null)
                {
                    _logger.Error("新增缓存失败，_cacheRepositories为空");
                    return;
                }
                if (_cacheRepositories.ContainsKey(type))
                {
                    _cacheRepositories[type] = _cacheRepository;
                }
                else
                {
                    _cacheRepositories.TryAdd(type, _cacheRepository);
                }
            }
            catch (Exception ex)
            {
                _logger.Error("增加缓存错误,type" + type + _cacheRepository.ToString() + "错误信息" + ex.ToString());
            }
        }

        public ICacheRepository Create(string type)
        {
            if (_cacheRepositories.ContainsKey(type))
            {
                return _cacheRepositories[type];
            }
            return null;
        }

        public string[] Get()
        {
            List<string> keys = new List<string>(_cacheRepositories.Keys);
            return keys.ToArray();
        }
    }
}