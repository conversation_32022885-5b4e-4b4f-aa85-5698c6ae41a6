﻿using NursingManagement.Common;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IDepartmentVSDepartmentService
    {
        /// <summary>
        /// 获取部门对照关系视图
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        Task<List<DepartmentMaintenanceView>> GetDepartmentVSDepartmentView(string organizationType);
        /// <summary>
        /// 批量保存部门及对照关系
        /// </summary>
        /// <param name="saveView">保存数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> SaveDepartmentVSDepartmentViews(List<DepartmentMaintenanceView> saveView , Session session);
        /// <summary>
        /// 启用或停用部门及部门对照记录
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="employeeID">护士工号</param>
        /// <param name="isActived">是否启用</param>
        /// <returns></returns>
        Task<bool> EnableOrDisableDepartment(int departmentID,string employeeID, bool isActived);
        /// <summary>
        /// 保存单个部门及对照关系
        /// </summary>
        /// <param name="saveView">保存数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> SaveDepartmentVSDepartmentView(DepartmentMaintenanceView saveView, Session session);
    }
}
