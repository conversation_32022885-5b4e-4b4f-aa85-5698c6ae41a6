﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班明细
    /// </summary>
    public class ShiftSchedulingDetailView
    {
        /// <summary>
        /// 排班主记录序号
        /// </summary>
        public string ShiftSchedulingRecordID { get; set; }

        /// <summary>
        /// 上午/下午 岗位
        /// </summary>
        public Dictionary<string, ShiftSchedulingPost> NoonPost { get; set; }

        /// <summary>
        /// 排班人ID
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 排班日期
        /// </summary>
        public DateTime? SchedulingDate { get; set; }

        /// <summary>
        /// 调班申请序号
        /// </summary>
        public string AdjustScheduleRecordID { get; set; }

        /// <summary>
        /// 排班标记集合
        /// </summary>
        public List<ShiftSchedulingMarkView> MarkList { get; set; }

        /// <summary>
        /// 鼠标悬浮提示内容
        /// </summary>
        public string TipContent { get; set; }

        /// <summary>
        /// 被借调标记，标记本部门的人被借调出去了
        /// </summary>
        public bool? SecondedFlag { get; set; }

        /// <summary>
        /// 借调标记，标记从其他部门借的人
        /// </summary>
        public bool? SecondmentFlag { get; set; }
    }
}
