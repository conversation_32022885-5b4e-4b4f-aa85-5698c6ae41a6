﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace NursingManagement.Data.Context
{
    public static class DbContextExtensions
    {
        /// <summary>
        ///  DbContext扩展方法，更新指定字段
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="context"></param>
        /// <param name="entities"></param>
        /// <param name="propertiesToModify"></param>
        public static void UpdateRange<T>(this NursingManagementDbContext context, IEnumerable<T> entities
            , params Expression<Func<T, object>>[] propertiesToModify) where T : class
        {
            // 将实体集合附加到上下文中
            context.Set<T>().AttachRange(entities);

            // 遍历每个实体
            foreach (var entity in entities)
            {
                var entry = context.Entry(entity);
                entry.State = EntityState.Unchanged; // 先将实体状态设置为未修改

                // 遍历需要修改的属性
                foreach (var property in propertiesToModify)
                {
                    var propertyName = GetPropertyName(property);
                    entry.Property(propertyName).IsModified = true; // 设置指定字段为已修改
                }
            }
        }
        /// <summary>
        /// 通过表达式树解析属性名称。允许使用 Lambda 表达式
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="expression"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        private static string GetPropertyName<T>(Expression<Func<T, object>> expression)
        {
            if (expression.Body is MemberExpression memberExpression)
            {
                return memberExpression.Member.Name;
            }
            else if (expression.Body is UnaryExpression unaryExpression)
            {
                return (unaryExpression.Operand as MemberExpression).Member.Name;
            }
            else
            {
                throw new ArgumentException("Invalid expression", nameof(expression));
            }
        }
    }
   
}
