﻿using NursingManagement.Common;
using System.Linq.Expressions;

namespace NursingManagement.UnitTest
{
    public class ExBuilderTest
    {

        /// <summary>
        /// 且表达式
        /// </summary>
        [Fact]
        public void AndTest()
        {
            Expression<Func<int, bool>> exp1 = (_) => true;
            Expression<Func<int, bool>> exp2 = (_) => false;
            var combined = exp1.And(exp2); // exp1 && exp2
            Assert.False(combined.Compile()(0));
        }

        /// <summary>
        /// 或表达式
        /// </summary>
        /// <example>
        /// exp1 || exp2
        /// </example>
        [Fact]
        public void OrTest()
        {
            Expression<Func<int, bool>> exp1 = (_) => true;
            Expression<Func<int, bool>> exp2 = (_) => false;
            var combined = exp1.Or(exp2); // exp1 || exp2
            Assert.True(combined.Compile()(0));
        }

        /// <summary>
        /// 条件且表达式
        /// </summary>
        /// <example>
        /// if (condition) exp1 && exp2
        /// </example>
        [Fact]
        public void IfAndTest()
        {
            Expression<Func<int, bool>> exp1 = (_) => true;
            Expression<Func<int, bool>> exp2 = (_) => false;

            var combined1 = exp1.IfAnd(true, exp2); // true, exp1 && exp2
            Assert.False(combined1.Compile()(0));

            var combined2 = exp1.IfAnd(false, exp2); // false, exp1
            Assert.True(combined2.Compile()(0));
        }

        /// <summary>
        /// 条件或表达式
        /// </summary>
        /// <example>
        /// if (condition) exp1 || exp2
        /// </example>
        [Fact]
        public void IfOrTest()
        {
            Expression<Func<int, bool>> exp1 = (_) => false;
            Expression<Func<int, bool>> exp2 = (_) => true;

            var combined1 = exp1.IfOr(true, exp2); // true, exp1 || exp2
            Assert.True(combined1.Compile()(0));

            var combined2 = exp1.IfOr(false, exp2); // false, exp1
            Assert.False(combined2.Compile()(0));
        }

        /// <summary>
        /// 且表达式与或表达式组合
        /// </summary>
        [Fact]
        public void AndCombineOr()
        {
            Expression<Func<int, bool>> exp1 = (_) => false;
            Expression<Func<int, bool>> exp2 = (_) => true;
            Expression<Func<int, bool>> exp3 = (_) => true;

            var combined1 = exp1.And(exp2).Or(exp3);
            Assert.True(combined1.Compile()(0)); // (exp1 && exp2) || exp3

            var combined2 = exp1.And(exp2.Or(exp3));
            Assert.False(combined2.Compile()(0)); // exp1 && (exp2 || exp3)
        }
    }
}
