﻿using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IBaseAPDetailRepository
    {
        /// <summary>
        /// 获取一定范围内的明细
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <param name="beginSort">开始序号</param>
        /// <param name="endSort">结束序号</param>
        /// <returns></returns>
        Task<IBaseAPDetail[]> GetRangeInfosByMainID(string annualPlanMainID, int beginSort, int endSort);
    }
}
