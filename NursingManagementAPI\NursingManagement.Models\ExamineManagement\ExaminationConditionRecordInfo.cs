﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 考核条件记录实体
    /// </summary>
    [Table("ExaminationConditionRecord")]
    public class ExaminationConditionRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考核条件表ID
        /// </summary>
        [Key]
        [Column(TypeName = "char(32)")]
        public string ExaminationConditionRecordID { get; set; }

        /// <summary>
        /// 条件名称
        /// </summary>
        [Column(TypeName = "nvarchar(100)")]
        public string ConditionName { get; set; }

        /// <summary>
        /// 条件明细内容
        /// </summary>
        [Column(TypeName = "nvarchar(400)")]
        public string ConditionContent { get; set; }

        /// <summary>
        /// 规则分数（依据明细计算出的总分）
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal Score { get; set; }
    }
}
