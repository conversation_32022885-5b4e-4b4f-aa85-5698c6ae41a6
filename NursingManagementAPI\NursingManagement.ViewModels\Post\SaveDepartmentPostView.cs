﻿namespace NursingManagement.ViewModels.Post
{
    public class SaveDepartmentPostView
    {
        /// <summary>
        /// 岗位ID
        /// </summary>
        public int PostID { get; set; }
        /// <summary>
        /// 岗位ID
        /// </summary>
        public int? ShiftValue { get; set; }
        /// <summary>
        /// 简写名称
        /// </summary>
        public string BriefName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 参与排班日统计
        /// </summary>
        public bool DailyStatisticalMark { get; set; }
        /// <summary>
        /// 参与排班月统计
        /// </summary>
        public bool MonthlyStatisticalMark { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 夏季时间上午开始
        /// </summary>
        public TimeSpan? SummerBeginTime1 { get; set; }
        /// <summary>
        /// 夏季时间上午结束
        /// </summary>
        public TimeSpan? SummerEndTime1 { get; set; }
        /// <summary>
        /// 夏季时间下午开始
        /// </summary>
        public TimeSpan? SummerBeginTime2 { get; set; }
        /// <summary>
        /// 夏季时间下午结束
        /// </summary>
        public TimeSpan? SummerEndTime2 { get; set; }
        /// <summary>
        /// 冬季时间上午开始
        /// </summary>
        public TimeSpan? WinterBeginTime1 { get; set; }
        /// <summary>
        /// 冬季时间上午结束
        /// </summary>
        public TimeSpan? WinterEndTime1 { get; set; }
        /// <summary>
        /// 冬季时间下午开始
        /// </summary>
        public TimeSpan? WinterBeginTime2 { get; set; }
        /// <summary>
        /// 冬季时间下午结束
        /// </summary>
        public TimeSpan? WinterEndTime2 { get; set; }
        /// <summary>
        /// 岗位说明主键
        /// </summary>
        public int? DepartmentPostID { get; set; }
        /// <summary>
        /// 考勤天数
        /// </summary>
        public string AttendanceDays { get; set; }
        /// <summary>
        /// 部门岗位班别
        /// </summary>
        public string PostShiftID { get; set; }
        /// <summary>
        /// 前景色
        /// </summary>
        public string Color { get; set; }
        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackGroundColor { get; set; }
        /// <summary>
        /// 半天考勤计算方式
        /// </summary>
        public string HalfDayAttendanceCalc { get; set; }
    }
}
