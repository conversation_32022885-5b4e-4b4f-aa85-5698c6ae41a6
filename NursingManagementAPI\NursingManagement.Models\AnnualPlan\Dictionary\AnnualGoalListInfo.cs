﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度目标字典
    /// </summary>
    [Serializable]
    [Table("AnnualGoalList")]
    public class AnnualGoalListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 年度计划目标序号，非自增
        /// </summary>
        public int AnnualGoalID { get; set; }
        /// <summary>
        /// 医院
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 目标名称
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string GoalContent { get; set; }
    }
}
