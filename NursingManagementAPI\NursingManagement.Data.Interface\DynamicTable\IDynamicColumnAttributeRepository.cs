﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDynamicColumnAttributeRepository
    {
        /// <summary>
        /// 根据表格ID获取数据
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <returns></returns>
        Task<List<DynamicColumnAttributeInfo>> GetListByID(int dynamicTableListID);
        /// <summary>
        ///  根据用户ID获取数据
        /// </summary>
        /// <param name="dynamicTableListID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        Task<List<DynamicColumnAttributeInfo>> GetListByUserID(int dynamicTableListID, string userID, string hospitalID);

        /// <summary>
        /// 根据类别获取Column中的字段名
        /// </summary>
        /// <param name="tableType"></param>
        /// <param name="tableSubType"></param>
        /// <returns></returns>
        Task<List<string>> GetTableFieldNamesByTableTypeAsync(string tableType, string tableSubType);
    }
}
