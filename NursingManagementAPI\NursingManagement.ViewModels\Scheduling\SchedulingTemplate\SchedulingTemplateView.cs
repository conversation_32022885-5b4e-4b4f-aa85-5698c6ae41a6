﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班模板记录
    /// </summary>
    public class SchedulingTemplateView
    {
        /// <summary>
        /// 排班模板主记录序号
        /// </summary>
        public string SchedulingTemplateRecordID { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板说明
        /// </summary>
        public string TemplateDescription { get; set; }

        /// <summary>
        /// 语言序号
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 部门序号
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        public int RowCount { get; set; }

        /// <summary>
        /// 列号
        /// </summary>
        public int ColumnCount { get; set; }

        /// <summary>
        /// 排班模板明细
        /// </summary>
        public List<SchedulingTemplateDetailView> SchedulingTemplateDetails { get; set; }
    }
}
