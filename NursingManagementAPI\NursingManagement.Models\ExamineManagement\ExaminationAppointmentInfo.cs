﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 考核预约信息实体类
    /// </summary>
    [Table("ExaminationAppointment")]
    public class ExaminationAppointmentInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考核预约ID（主键）
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminationAppointmentID { get; set; }

        /// <summary>
        /// 预约考核人员ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 考核计划ID（关联考核计划表）
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationRecordID { get; set; }

        /// <summary>
        /// 监考计划时间表ID（关联监考安排表）
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminerScheduleID { get; set; }

        /// <summary>
        /// 预约时间
        /// </summary>
        public DateTime AppointmentDate { get; set; }

        /// <summary>
        /// 预约状态（取值：已预约/已取消）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }

        /// <summary>
        /// 取消预约的原因（可为空）
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string CancelReason { get; set; }
        /// <summary>
        /// 取消预约日期时间
        /// </summary>
        public DateTime? CancelDateTime { get; set; }
    }
}