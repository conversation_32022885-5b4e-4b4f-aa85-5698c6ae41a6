﻿namespace NursingManagement.ViewModels.HierarchicalQC
{
    /// <summary>
    /// 定义 QCType 的可用值
    /// </summary>
    public enum QCTypeEnum
    {
        nodeQCFormType,
        normalWorkingFormType,
        visitsFormType,
        specialFormType
    }

    public class QCFormTypeView
    {
        // 使用字典来存储不同类型的表单
        public Dictionary<QCTypeEnum, HashSet<string>> FormTypes { get; private set; }

        public QCFormTypeView()
        {
            FormTypes = new Dictionary<QCTypeEnum, HashSet<string>>
             {
                 //节点式督导主题类型
                 { QCTypeEnum.nodeQCFormType, new HashSet<string> { "1", "2", "3", "4", "5" } },
                 //常态工作控制主题类型
                 { QCTypeEnum.normalWorkingFormType, new HashSet<string> { "6" } },
                 //访视主题类型
                 { QCTypeEnum.visitsFormType, new HashSet<string> { "7" } },
                 //小组主题类型
                 { QCTypeEnum.specialFormType, new HashSet<string> { "8" } }
            };
        }
    }
}
