﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 发送消息参数
    /// </summary>
    public class MessageView
    {
        /// <summary>
        /// 发送消息工具
        /// </summary>
        public List<MessageTool> MessageTools { get; set; }

        /// <summary>
        /// 消息接收人
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 接收者
        /// </summary>
        public string Receiver { get; set; }

        /// <summary>
        /// 客户端类别
        /// </summary>
        public int ClientType { get; set; }

        /// <summary>
        /// 不同MessageTool对应的消息条件
        /// </summary>
        public MessageConditionView MessageCondition { get; set; }
    }

    /// <summary>
    /// 消息工具
    /// </summary>
    public enum MessageTool
    {
        /// <summary>
        /// MQ消息
        /// </summary>
        MQ,

        /// <summary>
        /// 微信消息
        /// </summary>
        Wechat,

        /// <summary>
        /// 钉钉消息
        /// </summary>
        Dingtalk,

        /// <summary>
        /// 手机短信
        /// </summary>
        SMS,
    }
}
