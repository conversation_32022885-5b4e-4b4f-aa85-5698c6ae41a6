﻿using NursingManagement.ViewModels;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划指标明细
    /// </summary>
    [Serializable]
    [Table("AnnualPlanIndicatorDetail")]
    public class AnnualPlanIndicatorDetailInfo : MutiModifyInfo, IBaseAPDetail
    {
        /// <summary>
        /// 年度明细表GUID
        /// </summary>
        [Key]
        [Column("AnnualPlanIndicatorDetailID", TypeName = "varchar(32)")]
        public string DetailID { get; set; }
        /// <summary>
        /// 年度计划主表主键Guid，对应各部门年度计划主表
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 年度计划目标GuID,主键，对应年度计划目标表
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 年度计划目标分组主键ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanGoalGroupID { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 此计划制定年度
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 部门序号，便于查询上级指标使用，与主表中的科室序号等同，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 指标ID，来源指标字典
        /// </summary>
        public int AnnualIndicatorID { get; set; }
        /// <summary>
        /// 部门自定义呈现指标名称，但定义指标ID与字典保持一致
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string LocalShowName { get; set; }
        /// <summary>
        /// 操作符（≥、≤、＞、＜、＝）
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Operator { get; set; }
        /// <summary>
        /// 参考值
        /// </summary>
        [Column(TypeName = "decimal(8,2)")]
        public decimal? ReferenceValue { get; set; }
        /// <summary>
        /// 参考值单位（例如：%，‰，自定义：个、种、年、次）
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Unit { get; set; }
        /// <summary>
        /// 特别标记，来源Icon表
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string MarkID { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string Remark { get; set; }
        /// <summary>
        /// 呈现顺序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 导航属性，一个Indicator对应一个Group
        /// </summary>
        public AnnualPlanGoalGroupInfo AnnualPlanGoalGroup { get; set; }
    }
}
