﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class SerialNumberService : ISerialNumberService
    {
        private readonly ISerialNumberRecordsRepository _serialNumberRecordsRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;

        public SerialNumberService(ISerialNumberRecordsRepository serialNumberRecordsRepository,
            IUnitOfWork unitOfWork,
            ISettingDictionaryRepository settingDictionaryRepository
            )
        {
            _serialNumberRecordsRepository = serialNumberRecordsRepository;
            _unitOfWork = unitOfWork;
            _settingDictionaryRepository = settingDictionaryRepository;
        }

        /// <summary>
        /// 获取流水号
        /// </summary>
        /// <param name="subCode">业务细类码，流水号表存储此码</param>
        /// <returns>若存在流水号组装配置则返回组装后流水号，否则返回最新数值</returns>
        public async Task<string> GetSerialNumber(string subCode)
        {
            var settings = await _settingDictionaryRepository.GetSettingDictionary(new SettingDictionaryParams
            {
                SettingType = "ProveCategoryCounter",
                SettingTypeCode = subCode
            });
            if (settings.Count == 0)
            {
                // 纯数字，不清零
                var newNumber = await GetNewestNumber(subCode);
                return newNumber;
            }
            var resetRule = settings.FirstOrDefault(m => m.SettingTypeValue == "ResetRule")?.SettingValue;
            var seqNoLengthStr = settings.FirstOrDefault(m => m.SettingTypeValue == "SeqNoLength")?.SettingValue;
            _ = int.TryParse(seqNoLengthStr, out var seqNoLength);
            var dateFormat = settings.FirstOrDefault(m => m.SettingTypeValue == "DateFormat")?.SettingValue;

            var number = await GetNewestNumber(subCode, resetRule);
            var serialNumber = $"{subCode}{DateTime.Now.ToString(dateFormat)}{number.PadLeft(seqNoLength, '0')}";
            return serialNumber;
        }

        /// <summary>
        /// 获取最新号码
        /// </summary>
        /// <param name="subCode">业务细类码</param>
        /// <param name="resetRule">清零规则</param>
        /// <returns></returns>
        private async Task<string> GetNewestNumber(string subCode, string resetRule = null)
        {
            var serialNumberRecord = await _serialNumberRecordsRepository.GetSerialNumberByBizCategoryCode(subCode);
            // 流水号表不存在数据，则创建并返回最新号码为1
            if (serialNumberRecord == null)
            {
                var newSerialNumberRecord = new SerialNumberRecordsInfo
                {
                    BizCategoryCode = subCode,
                    SerialNumber = "1"
                };
                newSerialNumberRecord.Add("System").Modify("System");
                await _unitOfWork.GetRepository<SerialNumberRecordsInfo>().InsertAsync(newSerialNumberRecord);
                // 新创建数据，不存在归零情况
                return "1";
            }

            var newNumber = resetRule switch
            {
                "C" => (Convert.ToInt32(serialNumberRecord.SerialNumber) + 1).ToString(),
                "Y" => serialNumberRecord.ModifyDateTime.Year != DateTime.Now.Year ? "1" : (Convert.ToInt32(serialNumberRecord.SerialNumber) + 1).ToString(),
                "M" => serialNumberRecord.ModifyDateTime.Month != DateTime.Now.Month ? "1" : (Convert.ToInt32(serialNumberRecord.SerialNumber) + 1).ToString(),
                "D" => serialNumberRecord.ModifyDateTime.Day != DateTime.Now.Day ? "1" : (Convert.ToInt32(serialNumberRecord.SerialNumber) + 1).ToString(),
                _ => (Convert.ToInt32(serialNumberRecord.SerialNumber) + 1).ToString(),
            };
            serialNumberRecord.SerialNumber = newNumber;
            serialNumberRecord.Modify("System");
            return newNumber;
        }
    }
}
