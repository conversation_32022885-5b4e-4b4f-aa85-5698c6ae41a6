﻿

using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IAnnualInterventionMainPrincipalRepository
    {
        /// <summary>
        /// 计划执行项目主表
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualInterventionMainPrincipalInfo>> GetInfosByPlanMainIDAsNoTracking(string planMainID);
        /// <summary>
        /// 获取负责人ID列表
        /// </summary>
        /// <param name="interventionMainIDs">计划执行项目主表ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string, string[]>> GetPrincipalIDsByMainIDs(List<string> interventionMainIDs);
        /// <summary>
        /// 获取负责人ID列表
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        Task<Dictionary<string, string[]>> GetPrincipalIDsByPlanMainID(string planMainID);
        /// <summary>
        /// 计划执行项目主表
        /// </summary>
        /// <param name="interventionMainIDs">计划执行项目主表ID</param>
        /// <returns></returns>
        Task<List<AnnualInterventionMainPrincipalInfo>> GetInfosByInterventionMainIDs(params string[] interventionMainIDs);
        /// <summary>
        /// 依据年度计划ID查找措施对应负责人数据
        /// </summary>
        /// <param name="planMainID">年度计划ID</param>
        /// <returns></returns>
        Task<List<KeyValueString>> GetByPlanMainID(string planMainID);

        /// <summary>
        /// 获取执行项目负责人
        /// </summary>
        /// <param name="apInterventionMainIDs">年度计划执行项目表ID集合</param>
        /// <returns></returns>
        Task<Dictionary<string, string[]>> GetPrincipalIDsByAPInterventionMainIDs(string[] apInterventionMainIDs);
    }
}
