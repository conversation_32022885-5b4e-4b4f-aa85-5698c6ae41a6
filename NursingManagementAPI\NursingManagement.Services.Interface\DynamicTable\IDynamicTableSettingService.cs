﻿using NursingManagement.Common;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IDynamicTableSettingService
    {
        /// <summary>
        ///  获取动态表格表头配置
        /// </summary>
        /// <param name="queryView"></param>
        /// <returns></returns>
        Task<List<DynamicTableHeaderView>> GetDynamicTableHeader(DynamicTableHeaderQueryView queryView);
 
    }
}
