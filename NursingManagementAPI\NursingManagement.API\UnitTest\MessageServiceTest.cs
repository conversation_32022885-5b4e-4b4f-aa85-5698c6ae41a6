﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using Xunit.DependencyInjection;

namespace NursingManagement.UnitTest
{
    public class MessageServiceTest
    {
        private readonly IMessageService _messageService;
        private readonly ITestOutputHelperAccessor _testOutputHelperAccessor;

        public MessageServiceTest(
            IMessageService messageService,
            ITestOutputHelperAccessor testOutputHelperAccessor)
        {
            _messageService = messageService;
            _testOutputHelperAccessor = testOutputHelperAccessor;
        }
        /// <summary>
        /// 短信发送测试 超时6s
        /// </summary>
        /// <returns></returns>
        [Fact(Timeout = 6000000)]
        public async Task SendMessageTest()
        {
            var messageView = new MessageView()
            {
                //消息分类 -手机短信
                MessageTools = new List<MessageTool>() { MessageTool.SMS },
                //接收人工号 -当PhoneMessageView中的UserName和UserTelNumber没有设置的时候-必要
                EmployeeID = "111111",
                MessageCondition = new MessageConditionView()
                {
                    PhoneNumber = "17602970497",
                    // 通知内容 ：下边两个是模板
                    //您的申请已有结果；审批内容：${ approveContent }；审批结果：${ result}。详情请登录${operaSystem}查看。
                    //Content = "您的申请已有结果；审批内容：通知发起人；审批结果：同意。详情请登录护理管理系统查看。",
                    /**
                     * 您有新的审批，审批内容：${ approveContent}；请您及时登录护理管理系统进行审批。
                     */
                    Message = $"您有新的审批，审批内容：测试；请您及时登录护理管理系统进行审批。",
                },
            };
            var testResult = await _messageService.SendMessage(messageView);
            _testOutputHelperAccessor?.Output?.WriteLine($"测试参数：{ListToJson.ToJson(messageView)}\n测试结果：{testResult}");
            Assert.True(testResult);

        }
    }
}
