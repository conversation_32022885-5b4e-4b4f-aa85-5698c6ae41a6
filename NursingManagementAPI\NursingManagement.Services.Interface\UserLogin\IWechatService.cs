﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IWechatService
    {

        /// <summary>
        /// 检核微信小程序和系统账号是否绑定
        /// </summary>
        /// <param name="loginCode">前端从微信小程序接口获取到的登录Code</param>
        /// <returns>openID</returns>
        Task<Dictionary<string, object>> CheckMiniProgramBinding(string loginCode);

        /// <summary>
        /// 发送微信小程序测试消息
        /// </summary>
        /// <param name="openID">要发送用户的OpenID</param>
        /// <returns></returns>
        Task<bool> SendMiniProgramMessage(string openID);
        /// <summary>
        /// 检核微信浏览器登录账号是否绑定
        /// </summary>
        /// <param name="openID"></param>
        /// <returns></returns>
        Task<Dictionary<string, object>> CheckBrowserBinding(string openID);
        /// <summary>
        /// 获取微信配置
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetWxConfig(string url);
    }
}