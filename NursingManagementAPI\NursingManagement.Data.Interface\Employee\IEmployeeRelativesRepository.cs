﻿using NursingManagement.Models;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeRelativesRepository
    {
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeRelativesInfo>> GetListByEmployeeID(string employeeID);
        /// <summary>
        /// 获取本人关联信息（手机号、住址、学校等）
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<T> GetHerSelfFieldValueByEmployeeIDAsync<T>(string employeeID, [DisallowNull] Expression<Func<EmployeeRelativesInfo, T>> predicate);
    }
}
