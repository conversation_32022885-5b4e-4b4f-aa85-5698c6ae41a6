﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 待办审批View
    /// </summary>
    public class ApproveUnCompletedView
    {
        /// <summary>
        /// 流程名称
        /// </summary>
        public string ProcessName { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public string AddDateTime { get; set; }
        /// <summary>
        /// 审批内容
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 审批主记录ID
        /// </summary>
        public string ApproveRecordID { get; set; }
        /// <summary>
        /// 审批流程ID (添加注解，防止将参数传递给前端)
        /// </summary>
        [NotMapped]
        public string ApproveProcessID { get; set; }
    }
}
