﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划目标
    /// </summary>
    public class AnnualPlanMainGoalRepository : IAnnualPlanMainGoalRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;

        public AnnualPlanMainGoalRepository(
            NursingManagementDbContext nursingManagementDbContext
            )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 获取年度计划目标字典ID集合
        /// </summary>
        /// <param name="mainGoalIDs">目标表ID集合</param>
        /// <returns></returns>
        public async Task<List<int>> GetAnnualPlanGoalIDs(IEnumerable<string> mainGoalIDs)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.Where(m => mainGoalIDs.Contains(m.AnnualPlanMainGoalID) && m.DeleteFlag != "*")
                .Select(m => m.AnnualGoalID).Distinct().ToListAsync();
        }
        /// <summary>
        /// 获取年度计划分类目标表集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="isTracking">是否跟踪</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanMainGoalInfo>> GetInfosByPlanMainID(string mainID, bool isTracking = false)
        {
            var data = _nursingManagementDbContext.AnnualPlanMainGoalInfos
                .OrderBy(m => m.Sort)
                .Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*");
            return isTracking ? await data.ToListAsync() : await data.AsNoTracking().ToListAsync();
        }
        /// <summary>
        /// 获取关联的年度计划分类ID集合
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        public async Task<List<int>> GetAnnualPlanTypeIDs(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .Select(m => m.AnnualPlanTypeID).Distinct().ToListAsync();
       }
        /// <summary>
        /// 获取关联的年度计划分类ID集合
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        public async Task<List<APMainGoal>> GetAPGoalViews(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .Select(m => new APMainGoal
                {
                    MainGoalID = m.AnnualPlanMainGoalID,
                    GoalID = m.AnnualGoalID,
                    MainID = m.AnnualPlanMainID,
                    Sort = m.Sort,
                    TypeID = m.AnnualPlanTypeID
                }).OrderBy(m => m.Sort).ToListAsync();
        }
        /// <summary>
        /// 获取浏览视图
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanPreview.PlanType.PlanGoal>> GetBrowseView(string mainID)
        {
            var separator = new[] { "\r\n", "\r", "\n", "、" };
            var data = await _nursingManagementDbContext.AnnualPlanMainGoalInfos
                .Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .Include(m => m.PlanGroups).ThenInclude(m => m.PlanIndicators)
                .Include(m => m.PlanGroups).ThenInclude(m => m.PlanProjects)
                .AsSplitQuery()
                .OrderBy(m => m.Sort)
                .Select(m => new AnnualPlanPreview.PlanType.PlanGoal
                {
                    GoalId = m.AnnualGoalID,
                    TypeId = m.AnnualPlanTypeID,
                    Sort = m.Sort,
                    PlanGroups = m.PlanGroups.OrderBy(o => o.Sort).Select(n => new AnnualPlanPreview.PlanType.PlanGoal.PlanGroup
                    {
                        ResponsibleDepartments = n.ResponsibleDepartments == null ? Array.Empty<string>() : n.ResponsibleDepartments.Trim().Split(separator, StringSplitOptions.RemoveEmptyEntries),
                        PlanIndicators = n.PlanIndicators.OrderBy(m => m.Sort).Select(p => new AnnualPlanPreview.PlanType.PlanGoal.PlanGroup.PlanIndicator
                        {
                            LocalShowName = p.LocalShowName,
                            Operator = p.Operator,
                            ReferenceValue = p.ReferenceValue,
                            Unit = p.Unit,
                            MarkId = p.MarkID,
                            Remark = p.Remark,
                            Sort = p.Sort
                        }).ToList(),
                        PlanProjects = n.PlanProjects.OrderBy(m => m.Sort).Select(p => new AnnualPlanPreview.PlanType.PlanGoal.PlanGroup.PlanProject
                        {
                            LocalShowName = p.Content,
                            MarkId = p.MarkID,
                            Sort = p.Sort
                        }).ToList()
                    }).ToList()
                })
                .ToListAsync();
            return data;
        }
        /// <summary>
        /// 获取目标字典
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<List<KeyValueString>> GetAnnualPlanMainList(string mainID)
        {
            return await (from a in _nursingManagementDbContext.AnnualPlanMainGoalInfos
                          join b in _nursingManagementDbContext.AnnualGoalListInfos on a.AnnualGoalID equals b.AnnualGoalID
                          where a.AnnualPlanMainID == mainID
                          orderby a.Sort
                          select new KeyValueString
                          {
                              Key = a.AnnualPlanMainGoalID,
                              Value = b.GoalContent
                          }).ToListAsync();
        }
        /// <summary>
        /// 根据主键获取年度计划分类-目标
        /// </summary>
        /// <param name="mainGoalID">年度计划分类-目标主键ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanMainGoalInfo> GetAnnualPlanMainGoalByMainGoalID(string mainGoalID)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.FirstOrDefaultAsync(m => m.AnnualPlanMainGoalID == mainGoalID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 根据分类ID获取年度计划分类
        /// </summary>
        /// <param name="typeID">分类类别ID</param>
        /// <param name="hospitalID">医院ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanMainGoalInfo> GetAnnualPlanMainGoalByAnnualPlanTypeID(int typeID, string hospitalID)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.FirstOrDefaultAsync(m => m.AnnualPlanTypeID == typeID && m.HospitalID == hospitalID && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 获取主记录中目标记录ID
        /// </summary>
        /// <param name="annualMainID"></param>
        /// <param name="goalID"></param>
        /// <returns></returns>
        public async Task<string> GetAnnualPlanMainGoalIDByGoalIDAndMainIDAsync(string annualMainID, int goalID)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.Where(
                m => m.AnnualPlanMainID == annualMainID && m.AnnualGoalID == goalID && m.DeleteFlag != "*").Select(m=>m.AnnualPlanMainGoalID).FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取目标ID集合
        /// </summary>
        /// <param name="mainID">主表计划ID</param>
        /// <returns></returns>
        public async Task<string[]> GetMainGoalIDsByPlanMainID(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.Where(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*")
                .OrderBy(m => m.Sort).Select(m => m.AnnualPlanMainGoalID).ToArrayAsync();
        }
        /// <summary>
        /// 获取年度计划目标的序号
        /// </summary>
        /// <param name="predicate">筛选条件</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int>> GetMainGoalsSort(Expression<Func<AnnualPlanMainGoalInfo, bool>> predicate)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos
                .Where(m => m.DeleteFlag != "*")
                .Where(predicate)
                .Select(m => new { m.AnnualPlanMainGoalID, m.Sort })
                .ToDictionaryAsync(m => m.AnnualPlanMainGoalID, m => m.Sort);
        }
        /// <summary>
        /// 获取年度计划目标的序号
        /// </summary>
        /// <param name="mainGoalIDs">计划目标ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int>> GetMainGoalsSortByMainID(string mainGoalIDs)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.Where(m => mainGoalIDs.Contains(m.AnnualPlanMainGoalID) && m.DeleteFlag != "*")
                .Select(m => new { m.AnnualPlanMainGoalID, m.Sort })
                .ToDictionaryAsync(m => m.AnnualPlanMainGoalID, m => m.Sort);
        }
        /// <summary>
        /// 获取年度计划目标ID所属的分类ID
        /// </summary>
        /// <param name="mainGoalIDs">年度计划目标ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int>> GetMainGoalIDBelongType(List<string> mainGoalIDs)
        {
            return await _nursingManagementDbContext.AnnualPlanMainGoalInfos.Where(m => mainGoalIDs.Contains(m.AnnualPlanMainGoalID) && m.DeleteFlag != "*")
                .Select(m => new { m.AnnualPlanMainGoalID, m.AnnualPlanTypeID })
                .ToDictionaryAsync(m => m.AnnualPlanMainGoalID, m => m.AnnualPlanTypeID);
        }
    }
}
