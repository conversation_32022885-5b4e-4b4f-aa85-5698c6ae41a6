﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models.DynamicTable
{
    /// <summary>
    /// 动态表格列配置
    /// </summary>
    [Serializable]
    [Table("DynamicTableSetting")]
    public class DynamicTableSettingInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int DynamicTableSettingID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 表格ID
        /// </summary>
        public int DynamicTableListID { get; set; }
        /// <summary>
        /// 列ID,对应动态表格字段字典
        /// </summary>
        public int ColumnID { get; set; }
        /// <summary>
        /// 字段阶层
        /// </summary>
        [Column(TypeName = "TINYINT")]
        public byte Level { get; set; }
    }
}
