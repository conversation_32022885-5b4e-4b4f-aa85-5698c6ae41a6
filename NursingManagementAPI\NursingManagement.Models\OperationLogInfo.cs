using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 用户操作日志表
    /// </summary>
    [Serializable]
    [Table("OperationLog")]
    public class OperationLogInfo : BaseInfo
    {
        /// <summary>
        /// 用户操作日志序号，自增
        /// </summary>
        public int OperationLogID { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "verchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 登录类型，oa、his、wechat
        /// </summary>
        [Column(TypeName = "verchar(10)")]
        public string LoginType { get; set; }
        /// <summary>
        /// LoginType对应的UserID
        /// </summary>
        [Column(TypeName = "verchar(30)")]
        public string LoginUserID { get; set; }
        /// <summary>
        /// 客户端类型 （1:PC , 2:移动端H5, 3:微信）
        /// </summary>
        public int ClientType { get; set; }
        /// <summary>
        ///登录Token
        /// </summary>
        [Column(TypeName = "verchar(32)")]
        public string Token { get; set; }
        /// <summary>
        /// 日志标题
        /// </summary>
        [Column(TypeName = "nverchar(50)")]
        public string Title { get; set; }
        /// <summary>
        /// 日志内容
        /// </summary>
        [Column(TypeName = "verchar(4000)")]
        public string Content { get; set; }
        /// <summary>
        /// 日志发生页面
        /// </summary>
        [Column(TypeName = "verchar(200)")]
        public string Page { get; set; }
        /// <summary>
        /// 服务层接口地址
        /// </summary>
        [Column(TypeName = "verchar(300)")]
        public string Api { get; set; }
        /// <summary>
        /// 操作步骤序号
        /// </summary>
        public int Steps { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        [Column(TypeName = "verchar(20)")]
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增日期时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
    }
}
