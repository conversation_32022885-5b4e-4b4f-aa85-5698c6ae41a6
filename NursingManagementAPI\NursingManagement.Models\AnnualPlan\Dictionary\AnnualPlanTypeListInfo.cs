﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划类别字典
    /// </summary>
    [Serializable]
    [Table("AnnualPlanTypeList")]
    public class AnnualPlanTypeListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 年度计划类别序号，非自增
        /// </summary>
        public int AnnualPlanTypeID { get; set; }
        /// <summary>
        /// 年度计划类别名称
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string AnnualPlanTypeContent { get; set; }
        /// <summary>
        /// 年度计划类别描述
        /// </summary>
        [Column(TypeName = "nvarchar(1000)")]
        public string Description { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 医院
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
    }
}
