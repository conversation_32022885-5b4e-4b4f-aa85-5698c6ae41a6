﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 排班预约申请表
    /// </summary>
    [Serializable]
    [Table("SchedulingRequestRecord")]
    public class SchedulingRequestRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 排班申请记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SchedulingRequestRecordID { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门岗位编号，DepartmentPost表的主键，这里的岗一般指休假岗
        /// </summary>
        public int DepartmentPostID { get; set; }
        /// <summary>
        /// 预约排班开始日期，必须晚于当月时间
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 预约排班结束日期，必须晚于当月时间
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 预约排班开始午别
        /// </summary>
        public string StartNoon { get; set; }
        /// <summary>
        /// 预约排班结束午别
        /// </summary>
        public string EndNoon { get; set; }
        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 提交审批记录序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveRecordID { get; set; }

        /// <summary>
        /// 天数
        /// </summary>
        public decimal? Days { get; set; }
        /// <summary>
        /// 申请原因
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string Reason { get; set; }
    }
}
