﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class AnnualPlanInterventionService : IAnnualPlanInterventionService
    {
        private readonly IInterventionListRepository _annualInterventionListRepository;
        private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
        private readonly IAnnualPlanInterventionDetailRepository _annualPlanInterventionDetailRepository;
        private readonly IAnnualPlanMainGoalRepository _annualPlanMainGoalRepository;
        private readonly IAnnualGoalListRepository _goalListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IAnnualSettingService _annualSettingService;
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAnnualPlanMainRepository _annualPlanMainRepository;
        private readonly IAnnualPlanProjectDetailRepository _annualPlanProjectDetailRepository;
        private readonly IAnnualInterventionMainPrincipalRepository _annualInterventionMainPrincipalRepository;
        private readonly IAnnualScheduleService _annualScheduleService;

        public AnnualPlanInterventionService(
            IInterventionListRepository interventionListRepository,
            IAnnualPlanInterventionMainRepository mainRepository,
            IAnnualPlanInterventionDetailRepository detailRepository,
            IAnnualPlanMainGoalRepository annualPlanMainGoalRepository,
            IAnnualGoalListRepository goalListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IAnnualSettingService annualSettingService,
            IUnitOfWork unitOfWork,
            IAnnualPlanMainRepository annualPlanMainRepository,
            IAnnualPlanProjectDetailRepository annualPlanProjectDetailRepository,
            IAnnualInterventionMainPrincipalRepository annualInterventionMainPrincipalRepository,
            IAnnualScheduleService annualScheduleService
            )
        {
            _annualInterventionListRepository = interventionListRepository;
            _annualPlanInterventionMainRepository = mainRepository;
            _annualPlanInterventionDetailRepository = detailRepository;
            _annualPlanMainGoalRepository = annualPlanMainGoalRepository;
            _goalListRepository = goalListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _annualSettingService = annualSettingService;
            _unitOfWork = unitOfWork;
            _annualPlanMainRepository = annualPlanMainRepository;
            _annualPlanProjectDetailRepository = annualPlanProjectDetailRepository;
            _annualInterventionMainPrincipalRepository = annualInterventionMainPrincipalRepository;
            _annualScheduleService = annualScheduleService;
        }

        /// <summary>
        /// 获取按MainGoalID分组的项目明细集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, List<APProjectDetail>>> GetProjectDetailsGroupedByMainGoal(string mainID)
        {
            var projectDetails = await _annualPlanProjectDetailRepository.GetViewsByPlanMainID(mainID);
            var projectDetailKeyPairs = projectDetails.GroupBy(m => m.MainGoalID).ToDictionary(k => k.Key, v => v.OrderBy(m => m.Sort).ToList());
            return projectDetailKeyPairs;
        }
        /// <summary>
        /// 获取年度计划-计划制定列表
        /// </summary>
        /// <param name="planMainID">主表ID</param>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <returns></returns>
        public async Task<List<APInterventionsGroup>> GetAnnualInterventions(string planMainID, string projectDetailID)
        {
            #region 数据获取
            var employeeIDToNameViews = await _employeePersonalDataRepository.GetIDAndNameData();
            var mainGoalViews = await _annualPlanMainGoalRepository.GetAPGoalViews(planMainID);
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();
            mainGoalViews.ForEach(m => m.GoalContent = goalList.Find(n => n.AnnualGoalID == m.GoalID)?.GoalContent);
            var interventionViews = new List<APIntervention>();
            var interventionMonthsView = Array.Empty<APInterventionMonthsView>();
            var interventionMainAndPrincipalIDs = new Dictionary<string, string[]>();
            if (!(projectDetailID?.StartsWith("temp_") ?? false))
            {
                interventionViews = await _annualPlanInterventionMainRepository.GetViewsByPlanMainID(planMainID, projectDetailID);
                var interventionMainIDs = interventionViews.Select(m => m.AnnualPlanInterventionMainID).ToList();
                interventionMonthsView = projectDetailID == null
                    ? await _annualPlanInterventionDetailRepository.GetDetailsByPlanMainID(planMainID)
                    : await _annualPlanInterventionDetailRepository.GetDetailsByMainID(interventionMainIDs);
                interventionMainAndPrincipalIDs = projectDetailID == null
                    ? await _annualInterventionMainPrincipalRepository.GetPrincipalIDsByPlanMainID(planMainID)
                    : await _annualInterventionMainPrincipalRepository.GetPrincipalIDsByMainIDs(interventionMainIDs);
            }

            #endregion
            if (!interventionViews.Any())
            {
                return [];
            }
            var groups = await CreateGroups(projectDetailID, mainGoalViews, interventionViews, interventionMonthsView, interventionMainAndPrincipalIDs);
            return groups;
        }
        /// <summary>
        /// 创建按目标分组的执行项目主表View集合
        /// </summary>
        /// <param name="projectDetailID"></param>
        /// <param name="mainGoalViews"></param>
        /// <param name="interventionViews"></param>
        /// <param name="interventionMonthsView"></param>
        /// <param name="interventionMainAndPrincipalIDs"></param>
        /// <returns></returns>
        private async Task<List<APInterventionsGroup>> CreateGroups(string projectDetailID, List<APMainGoal> mainGoalViews, List<APIntervention> interventionViews, APInterventionMonthsView[] interventionMonthsView, Dictionary<string, string[]> interventionMainAndPrincipalIDs)
        {
            var groups = new List<APInterventionsGroup>(mainGoalViews.Count);
            var sort = 1;
            foreach (var mainGoalView in mainGoalViews)
            {
                var group = new APInterventionsGroup
                {
                    Interventions = [],
                    MainGoalID = mainGoalView.MainGoalID,
                    MainGoalContent = mainGoalView.GoalContent,
                    Sort = mainGoalView.Sort
                };
                var relateInterventionViews = interventionViews.FindAll(m => mainGoalView.MainGoalID == m.AnnualPlanMainGoalID);
                if (relateInterventionViews.Count == 0)
                {
                    if (string.IsNullOrEmpty(projectDetailID))
                    {
                        groups.Add(group);
                    }
                    continue;
                }
                // 同目标内按执行项目字典ID排序
                relateInterventionViews = relateInterventionViews.OrderBy(m => m.InterventionID).ToList();
                sort = await SupplyInterventionView(interventionMonthsView, interventionMainAndPrincipalIDs, sort, relateInterventionViews);
                group.Interventions = relateInterventionViews;

                groups.Add(group);
            }

            return groups;
        }

        /// <summary>
        /// 补充InterventionView
        /// </summary>
        /// <param name="interventionMonthsView">措施对应月份</param>
        /// <param name="interventionMainAndPrincipalIDs">措施主表对应负责人</param>
        /// <param name="sort">起始序号</param>
        /// <param name="relateInterventionViews">关联的措施View集合</param>
        /// <returns></returns>
        private async Task<int> SupplyInterventionView(APInterventionMonthsView[] interventionMonthsView, Dictionary<string, string[]> interventionMainAndPrincipalIDs, int sort, List<APIntervention> relateInterventionViews)
        {
            foreach (var interventionView in relateInterventionViews)
            {
                interventionMainAndPrincipalIDs.TryGetValue(interventionView.AnnualPlanInterventionMainID, out var principalIDs);
                interventionView.PrincipalIDs = principalIDs;
                // 当负责人为空、有负责人时，使用姓名拼接来呈现
                if (string.IsNullOrEmpty(interventionView.PrincipalGroupName) && principalIDs != null && principalIDs.Any())
                {
                    var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(principalIDs);
                    interventionView.PrincipalName = string.Join("，", principalIDs.Select(m => employees.FirstOrDefault(n => n.Key == m).Value));
                }
                interventionView.PlanMonths = interventionMonthsView.FirstOrDefault(n => n.AnnualPlanInterventionMainID == interventionView.AnnualPlanInterventionMainID)?.PlanMonths;
                interventionView.Sort = sort++;
            };
            return sort;
        }

        /// <summary>
        /// 保存执行项目
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        public async Task<bool> SaveAnnualIntervention(AnnualInterventionSaveView saveView)
        {
            var updateCache = false;
            var newID = await _annualInterventionListRepository.GetMaxID() + 1;
            foreach (var aPIntervention in saveView.APInterventions)
            {
                // 未传 InterventionID，还需新增执行项目
                if (!aPIntervention.InterventionID.HasValue)
                {
                    updateCache = true;
                    aPIntervention.InterventionID = await _annualSettingService.AddProjectList(saveView, aPIntervention.LocalShowName, newID);
                    newID += 1;
                }
                aPIntervention.AnnualPlanInterventionMainID = aPIntervention.AnnualPlanInterventionMainID.GetRealID(out var isSubstring);
                await (isSubstring ? AddAnnualIntervention(saveView, aPIntervention) : UpdateAnnualIntervention(saveView, aPIntervention));
            }
            if (updateCache)
            {
                await _annualInterventionListRepository.UpdateCache();
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 新增执行项目
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <param name="aPIntervention">执行项目View</param>
        /// <returns></returns>
        private async Task AddAnnualIntervention(AnnualInterventionSaveView saveView, APIntervention aPIntervention)
        {
            // 实例化计划制定主表
            var main = new AnnualInterventionMainInfo
            {
                AnnualPlanMainID = saveView.AnnualPlanMainID,
                AnnualPlanMainGoalID = aPIntervention.AnnualPlanMainGoalID,
                ProjectDetailID = aPIntervention.ProjectDetailID.GetRealID(out _),
                InterventionID = aPIntervention.InterventionID.Value,
                LocalShowName = aPIntervention.LocalShowName,
                PrincipalGroupName = aPIntervention.PrincipalGroupName
            };
            main.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
            main.AnnualPlanInterventionMainID = main.GetId();
            await _unitOfWork.GetRepository<AnnualInterventionMainInfo>().InsertAsync(main);
            if (aPIntervention.PlanMonths.Length == 0)
            {
                return;
            }
            // 实例化计划制定明细表
            var details = new List<AnnualInterventionDetailInfo>();
            foreach (var month in aPIntervention.PlanMonths)
            {
                var detail = new AnnualInterventionDetailInfo
                {
                    AnnualPlanMainID = saveView.AnnualPlanMainID,
                    AnnualPlanInterventionMainID = main.AnnualPlanInterventionMainID,
                    PlanMonth = month,
                    PlanDate = null
                };
                detail.AnnualPlanInterventionDetailID = detail.GetId();
                detail.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
                details.Add(detail);
            };
            await _unitOfWork.GetRepository<AnnualInterventionDetailInfo>().InsertAsync(details);
            // 实例化负责人表
            var principals = new List<AnnualInterventionMainPrincipalInfo>();
            foreach (var principalID in aPIntervention.PrincipalIDs)
            {
                var principal = new AnnualInterventionMainPrincipalInfo
                {
                    AnnualInterventionMainID = main.AnnualPlanInterventionMainID,
                    AnnualPlanMainID = saveView.AnnualPlanMainID,
                    EmployeeID = principalID
                };
                principal.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
                principals.Add(principal);
            };
            await _unitOfWork.GetRepository<AnnualInterventionMainPrincipalInfo>().InsertAsync(principals);
        }
        /// <summary>
        /// 更新执行项目
        /// </summary>
        /// <param name="saveView">保存view</param>
        /// <param name="aPIntervention">执行项目View</param>
        /// <returns></returns>
        private async Task UpdateAnnualIntervention(AnnualInterventionSaveView saveView, APIntervention aPIntervention)
        {
            var oldMainInfo = await _annualPlanInterventionMainRepository.GetInfoByID(aPIntervention.AnnualPlanInterventionMainID);
            if (oldMainInfo == null)
            {
                _logger.Error($"未找到年度计划执行项目信息：{aPIntervention.AnnualPlanInterventionMainID}");
                return;
            }
            var isUpdate = false;
            // 更新主表信息
            if (oldMainInfo.LocalShowName != aPIntervention.LocalShowName)
            {
                oldMainInfo.LocalShowName = aPIntervention.LocalShowName;
                isUpdate = true;
            }
            if (oldMainInfo.InterventionID != aPIntervention.InterventionID)
            {
                oldMainInfo.InterventionID = aPIntervention.InterventionID.Value;
                isUpdate = true;
            }
            if (oldMainInfo.PrincipalGroupName != aPIntervention.PrincipalGroupName)
            {
                oldMainInfo.PrincipalGroupName = aPIntervention.PrincipalGroupName;
                isUpdate = true;
            }
            if (isUpdate)
            {
                oldMainInfo.Modify(saveView.EmployeeID);
            }

            if (aPIntervention.PlanMonths == null)
            {
                return;
            }
            await UpdateAPInterventionDetails(saveView, aPIntervention);
            await UpdateAPInterventionPrincipals(saveView, aPIntervention);
            return;
        }
        /// <summary>
        /// 更新年度计划执行项目负责人
        /// </summary>
        /// <param name="saveView"></param>
        /// <param name="aPIntervention">执行项目View</param>
        /// <returns></returns>
        private async Task UpdateAPInterventionPrincipals(AnnualInterventionSaveView saveView, APIntervention aPIntervention)
        {
            // 更新负责人表信息
            var oldPrincipals = await _annualInterventionMainPrincipalRepository.GetInfosByInterventionMainIDs(aPIntervention.AnnualPlanInterventionMainID);
            var principalIDs = aPIntervention.PrincipalIDs;
            if (principalIDs == null)
            {
                return;
            }
            // principalIDs与oldPrincipals作差集， 得到新增的负责人
            var addPrincipals = principalIDs.Except(oldPrincipals.Select(m => m.EmployeeID)).ToList();
            foreach (var principalID in addPrincipals)
            {
                var principal = new AnnualInterventionMainPrincipalInfo
                {
                    AnnualInterventionMainID = aPIntervention.AnnualPlanInterventionMainID,
                    AnnualPlanMainID = saveView.AnnualPlanMainID,
                    EmployeeID = principalID
                };
                principal.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
                await _unitOfWork.GetRepository<AnnualInterventionMainPrincipalInfo>().InsertAsync(principal);
            }
            // principalIDs与oldPrincipals作差集， 得到删除的负责人
            oldPrincipals.ExceptBy(principalIDs, n => n.EmployeeID).ToList().ForEach(m => m.Delete(saveView.EmployeeID));
        }
        /// <summary>
        /// 更新年度计划执行项目明细
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <param name="aPIntervention">执行项目View</param>
        /// <returns></returns>
        private async Task UpdateAPInterventionDetails(AnnualInterventionSaveView saveView, APIntervention aPIntervention)
        {
            // 更新明细表信息
            var oldDetailInfos = await _annualPlanInterventionDetailRepository.GetDetailsByInterventionMainIDs(aPIntervention.AnnualPlanInterventionMainID);
            var months = aPIntervention.PlanMonths;
            // months与oldDetailInfos作差集， 得到新增的月份
            var addMonths = months.Except(oldDetailInfos.Select(m => m.PlanMonth)).ToList();
            foreach (var month in addMonths)
            {
                var detail = new AnnualInterventionDetailInfo
                {
                    AnnualPlanInterventionMainID = aPIntervention.AnnualPlanInterventionMainID,
                    AnnualPlanMainID = saveView.AnnualPlanMainID,
                    PlanMonth = month,
                    PlanDate = null
                };
                detail.AnnualPlanInterventionDetailID = detail.GetId();
                detail.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
                await _unitOfWork.GetRepository<AnnualInterventionDetailInfo>().InsertAsync(detail);
            }
        }

        /// <summary>
        /// 联动触发删除年度计划制定信息
        /// </summary>
        /// <param name="planMainID">主表ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <param name="projectDetailIDs">工作项目ID</param>
        /// <returns></returns>
        public async Task<bool> DeleteInterventionsByProjectDetailID(string planMainID, string employeeID, params string[] projectDetailIDs)
        {
            if (projectDetailIDs.Length == 0)
            {
                return true;
            }
            var interventionMains = await _annualPlanInterventionMainRepository.GetInfosByProjectDetailIDs(planMainID, projectDetailIDs);
            if (interventionMains.Count == 0)
            {
                return true;
            }
            interventionMains.ForEach(m => m.Delete(employeeID));

            var interventionMainIDs = interventionMains.Select(m => m.AnnualPlanInterventionMainID).ToArray();
            await DeleteInterventionsByMainIDs(employeeID, interventionMainIDs);

            return true;
        }
        /// <summary>
        /// 删除年度计划执行项目主表数据
        /// </summary>
        /// <param name="interventionMainID">执行项目主表ID</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteAnnualInterventionByID(string interventionMainID, string employeeID)
        {
            var oldMainInfo = await _annualPlanInterventionMainRepository.GetInfoByID(interventionMainID);
            if (oldMainInfo == null)
            {
                _logger.Error($"未找到年度计划执行项目信息：{interventionMainID}");
                return false;
            }
            oldMainInfo.Delete(employeeID);

            await DeleteInterventionsByMainIDs(employeeID, oldMainInfo.AnnualPlanInterventionMainID);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 获取年度计划计划总览数据
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanGeneralView>> GetAnnualPlanGeneralView(int year, int departmentID)
        {
            var generalViewList = new List<AnnualPlanGeneralView>();
            var mainGoalViews = await _annualPlanMainRepository.GetAPGeneralViewsByYear(departmentID, year);
            if (mainGoalViews.Length == 0)
            {
                return generalViewList;
            }
            var planMainID = mainGoalViews.First().AnnualPlanMainID;
            // 根据计划查执行项目
            var interventionViews = await _annualPlanInterventionMainRepository.GetViewsByPlanMainID(planMainID);
            if (interventionViews.Count <= 0)
            {
                return generalViewList;
            }
            var interventionMainIDs = interventionViews.Select(m => m.AnnualPlanInterventionMainID).ToList();
            // 配套执行项目明细、执行项目负责人
            var interventionMainAndPrincipalIDs = await _annualInterventionMainPrincipalRepository.GetPrincipalIDsByMainIDs(interventionMainIDs);
            var interventionMonthsView = await _annualPlanInterventionDetailRepository.GetDetailsByPlanMainID(planMainID);
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();
            foreach (var mainGoalView in mainGoalViews)
            {
                var currentGoalInterventionViews = interventionViews.FindAll(m => mainGoalView.AnnualPlanMainGoalID == m.AnnualPlanMainGoalID);
                foreach (var intervention in currentGoalInterventionViews)
                {
                    var view = new AnnualPlanGeneralView
                    {
                        MainGoalContent = goalList.Find(m => m.AnnualGoalID == mainGoalView.GoalID)?.GoalContent,
                        LocalShowName = intervention.LocalShowName,
                        PlanMonths = interventionMonthsView.FirstOrDefault(n => n.AnnualPlanInterventionMainID == intervention.AnnualPlanInterventionMainID)?.PlanMonths,
                        PrincipalName = intervention.PrincipalGroupName,
                        Sort = mainGoalView.Sort,
                        InterventionID = intervention.InterventionID ?? 0,
                    };
                    interventionMainAndPrincipalIDs.TryGetValue(intervention.AnnualPlanInterventionMainID, out var principalIDs);
                    if (principalIDs != null && principalIDs.Any() && string.IsNullOrEmpty(view.PrincipalName))
                    {
                        var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(principalIDs);
                        view.PrincipalName = string.Join("，", employees.Select(m => m.Value));
                    }
                    generalViewList.Add(view);
                }
            }

            return generalViewList.OrderBy(m => m.Sort).ThenBy(m => m.InterventionID).ToList();
        }
        /// <summary>
        /// 获取当前计划已有的负责人选项情况
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<APInterventionRecPrincipal>> GetRecPrincipalOptions(string planMainID)
        {
            var recPrincipalViews = await _annualPlanInterventionMainRepository.GetRecPrincipalViewByPlanMainID(planMainID);
            return recPrincipalViews;
        }

        /// <summary>
        /// 删除措施主表，联动删除其它有关表数据
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="interventionMainIDs">措施主表ID集合</param>
        /// <returns></returns>
        private async Task DeleteInterventionsByMainIDs(string employeeID, params string[] interventionMainIDs)
        {
            // 删除措施明细
            var oldDetails = await _annualPlanInterventionDetailRepository.GetDetailsByInterventionMainIDs(interventionMainIDs);
            oldDetails.ForEach(m => m.Delete(employeeID));
            // 删除措施负责人
            var oldPrincipals = await _annualInterventionMainPrincipalRepository.GetInfosByInterventionMainIDs(interventionMainIDs);
            oldPrincipals.ForEach(m => m.Delete(employeeID));
        }

        /// <summary>
        /// 获取执行项目转换视图
        /// </summary>
        /// <param name="planMainID">年度计划主表ID</param>
        /// <param name="months">月集合</param>
        /// <returns></returns>
        public async Task<List<APInterventionConvertView>> GetAPInterventionConvertViews(string planMainID, int[] months)
        {
            var views = await _annualPlanInterventionMainRepository.GetConvertView(planMainID, months);

            // 获取分类ID
            var mainGoalIDs = views.Select(m => m.APMainGoalID).ToList();
            var mainGoalIDAndTypeIDDict = await _annualPlanMainGoalRepository.GetMainGoalIDBelongType(mainGoalIDs);

            // 获取负责人集合
            var apInterventionMainIDs = views.Select(m => m.APInterventionMainID).ToArray();
            var interventionMainIDAndPrincipalIDsDict = await _annualInterventionMainPrincipalRepository.GetPrincipalIDsByAPInterventionMainIDs(apInterventionMainIDs);

            views.ForEach(view =>
            {
                mainGoalIDAndTypeIDDict.TryGetValue(view.APMainGoalID, out var typeID);
                view.TypeID = typeID;
                interventionMainIDAndPrincipalIDsDict.TryGetValue(view.APInterventionMainID, out var principalIDs);
                view.PrincipalIDs = principalIDs;
            });

            return views;
        }
    }
}
