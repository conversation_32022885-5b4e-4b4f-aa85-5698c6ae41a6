﻿using DocumentFormat.OpenXml.Bibliography;
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 消息确认记录的仓储实现类，提供对消息确认记录的查询操作。
    /// </summary>
    public class MessageToDepartmentRepository : IMessageToDepartmentRepository
    {
        private readonly NursingManagementDbContext _context;
        private readonly SessionCommonServer _sessionCommonServer;

        public MessageToDepartmentRepository(NursingManagementDbContext context, SessionCommonServer sessionCommonServer)
        {
            _context = context;
            _sessionCommonServer = sessionCommonServer;
        }
        public async Task<List<MessageToDepartmentInfo>> GetMessageToDepartmentByMessageID(string messageRecordID)
        {
            var session =  _sessionCommonServer.GetSessionByCache();
            return await _context.MessageToDepartmentInfos.Where(m=>m.MessageRecordID == messageRecordID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<MessageToDepartmentInfo>> GetMessageToDepartmentByDepartmentID(int departmentID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.MessageToDepartmentInfos.Where(m => m.DepartmentID == departmentID && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<string>> GetMessageRecordIDsByDepartmentIDs(List<int> departmentIDs)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _context.MessageToDepartmentInfos.Where(m => departmentIDs.Contains(m.DepartmentID) 
                                                && m.HospitalID == session.HospitalID && m.DeleteFlag != "*")
                                                .Select(m=>m.MessageRecordID)
                                                .ToListAsync();
        }
    }
}
