﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 动态表单明细表
    /// </summary>
    [Serializable]
    [Table("DynamicFormDetail")]
    public class DynamicFormDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 动态表单明细ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string DynamicFormDetailID { get; set; }
        /// <summary>
        /// 动态表单ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string DynamicFormRecordID { get; set; }
        /// <summary>
        /// 父ID
        /// </summary>
        public string ParentID { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ItemID { get; set; }
        /// <summary>
        /// 项目来源字典，如来源质控、培训、考核，这里放字典表名
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ItemSourceType { get; set; }
        /// <summary>
        /// 控件编号，ComponentList字典表主键
        /// </summary>
        public int ComponentListID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
