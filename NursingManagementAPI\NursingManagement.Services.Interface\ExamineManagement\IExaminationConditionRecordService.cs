﻿using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services.Interface
{
    public interface IExaminationConditionRecordService
    {
        /// <summary>
        /// 根据规则记录ID获取考核组卷规则
        /// </summary>
        /// <param name="conditionRecordID"></param>
        /// <returns></returns>
        Task<ExaminationConditionEditView> GetConditionDetailView(string conditionRecordID);
        /// <summary>
        /// 获取所有考核组卷规则记录
        /// </summary>
        /// <returns></returns>
        Task<List<ExaminationConditionRecordView>> GetAllRecords();
        /// <summary>
        /// 保存组卷规则记录
        /// </summary>
        /// <param name="examinationConditionSaveParamView">组卷规则记录保存参数</param>
        /// <param name="employeeID">当前会话人工号</param>
        /// <returns></returns>
        Task<string> SaveExaminationConditionRecord(ExaminationConditionSaveParamView examinationConditionSaveParamView,string employeeID);
        /// <summary>
        /// 删除组卷规则
        /// </summary>
        /// <param name="examinationConditionRecordID">组卷规则记录ID</param>
        /// <param name="employeeID">当前会话人工号</param>
        /// <returns></returns>
        Task<bool> DeleteExaminationConditionRecord(string examinationConditionRecordID, string employeeID);
        /// <summary>
        /// 获取组卷规则明细
        /// </summary>
        /// <param name="examinationConditionRecordID"></param>
        /// <returns></returns>
        Task<ExaminationConditionEditView> GetExaminationConditionEditView(string examinationConditionRecordID);
    }
}
