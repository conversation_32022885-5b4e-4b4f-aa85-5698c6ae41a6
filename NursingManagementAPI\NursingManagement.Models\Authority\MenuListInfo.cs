using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 菜单表
    /// </summary>
    [Serializable]
    [Table("MenuList")]
    public class MenuListInfo : MutiModifyInfo
    {

        /// <summary>
        /// 菜单序号
        /// </summary>
        public int MenuListID { get; set; }

        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "verchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "verchar(50)")]
        public string MenuType { get; set; }

        /// <summary>
        /// 空表示为1阶
        /// </summary>
        public int? ParentID { get; set; }

        /// <summary>
        /// 阶层
        /// </summary>
        public int MenuLevel { get; set; }

        /// <summary>
        /// 菜单名称
        /// </summary>
        [Column(TypeName = "nverchar(50)")]
        public string MenuName { get; set; }

        /// <summary>
        /// 路由清单序号
        /// </summary>
        public int? RouterListID { get; set; }

        /// <summary>
        /// 菜单图标（iconfont图标名称）
        /// </summary>
        [Column(TypeName = "verchar(50)")]
        public string IconName { get; set; }

        /// <summary>
        /// 移动端菜单标记
        /// </summary>
        public bool? MobileFlag { get; set; }

        /// <summary>
        /// 如果不为空，说明该菜单是指定路由的top菜单，为空则是正常菜单
        /// </summary>
        public int? TopParentRouterID { get; set; }

        /// <summary>
        /// 功能说明
        /// </summary>
        [Column(TypeName = "nverchar(200)")]
        public string Content { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
