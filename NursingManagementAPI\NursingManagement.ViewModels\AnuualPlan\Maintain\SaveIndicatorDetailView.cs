﻿namespace NursingManagement.ViewModels
{
    public class SaveIndicatorDetailView
    {
        /// <summary>
        /// 明细ID
        /// </summary>
        public string DetailID { get; set; }
        /// <summary>
        /// 主表ID
        /// </summary>
        public string MainID { get; set; }
        /// <summary>
        /// 目标业务ID
        /// </summary>
        public string MainGoalID { get; set; }
        /// <summary>
        /// 分组ID
        /// </summary>
        public string GroupID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 指标ID
        /// </summary>
        public int AnnualIndicatorID { get; set; }
        /// <summary>
        /// 指标名称
        /// </summary>
        public string LocalShowName { get; set; }
        /// <summary>
        /// 此计划制定年度
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 操作符
        /// </summary>
        public string Operator { get; set; }
        /// <summary>
        /// 参考值
        /// </summary>
        public decimal? ReferenceValue { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 特殊注记
        /// </summary>
        public string MarkID { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// HR工号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
