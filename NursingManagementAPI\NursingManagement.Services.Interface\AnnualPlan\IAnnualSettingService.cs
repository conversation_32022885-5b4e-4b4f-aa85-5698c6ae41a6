﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IAnnualSettingService
    {
        #region 指标字典
        /// <summary>
        /// 获取当前科室的年度指标字典
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="showUpperIndicator">是否呈现连续上级指标</param>
        /// <returns></returns>
        Task<List<AnnualIndicatorListView>> GetAnnualIndicatorList(int departmentID, bool showUpperIndicator);
        /// <summary>
        /// 修改年度计划指标字典
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        Task<(bool, int)> SaveAnnualIndicatorList(AnnualIndicatorListView saveView);
        /// <summary>
        /// 删除年度计划指标数据
        /// </summary>
        /// <param name="annualProjectListView"></param>
        /// <returns></returns>
        Task<bool> DeleteAnnualIndicatorList(AnnualIndicatorListView annualProjectListView);
        #endregion
        #region 执行项目字典
        /// <summary>
        /// 获取年度所有执行项目
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="showUpperIntervention">是否呈现上级执行项目</param>
        /// <returns></returns>
        Task<List<AnnualInterventionListView>> GetAnnualInterventionList(int departmentID, bool showUpperIntervention);
        /// <summary>
        /// 保存年度计划项目字典
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        Task<bool> SaveAnnualInterventionList(APInterventionListSaveView saveView);
        /// <summary>
        /// 新增执行项目字典
        /// </summary>
        /// <param name="interventionSaveView">保存View</param>
        /// <param name="projectContent">执行项目内容</param>
        /// <param name="newID">措施ID</param>
        /// <returns></returns>
        Task<int> AddProjectList(AnnualInterventionSaveView interventionSaveView, string projectContent, int newID);
        #endregion
        #region 目标字典
        /// <summary>
        /// 获取目标字典
        /// </summary>
        /// <param name="goalIds">目标Id集合</param>
        /// <returns></returns>
        Task<List<AnnualGoalListInfo>> GetGoalList(int[] goalIds);
        /// <summary>
        /// 更新目标字典名称
        /// </summary>
        /// <param name="goalID">目标ID</param>
        /// <param name="content">目标名称</param>
        /// <param name="userID">工号</param>
        /// <returns></returns>
        Task<bool> UpdateGoalContent(int goalID, string content, string userID);

        /// <summary>
        /// 保存年度计划分类-目标
        /// </summary>
        /// <param name="mainGoalView">保存数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> SaveAnnualPlanMainGoal(AnnualPlanMainGoalView mainGoalView, Session session);
        /// <summary>
        /// 删除年度计划分类-目标
        /// </summary>
        /// <param name="mainGoalID">主键ID</param>
        /// <param name="planMainID">计划主表ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> DeleteAnnualPlanMainGoal(string mainGoalID, string planMainID, Session session);
        /// <summary>
        /// 检核年度计划目标
        /// </summary>
        /// <param name="mainGoalView">待删除目标</param>
        /// <returns></returns>
        Task<Tuple<bool, string>> CheckAnnualPlanMainGoal(AnnualPlanMainGoalView mainGoalView);
        #endregion

        #region 年度计划分类
        /// <summary>
        /// 获取年度计划分类（本部门）
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanTypeListView>> GetAnnualPlanTypeList(int departmentID);
        /// <summary>
        /// 保存年度计划分类
        /// </summary>
        /// <param name="session">缓存</param>
        /// <param name="saveView">保存数据</param>
        /// <returns></returns>
        Task<bool> SaveAnnualPlanTypeList(Session session, AnnualPlanTypeListView saveView);
        /// <summary>
        /// 删除年度计划分类
        /// </summary>
        /// <param name="annualPlanType">类型ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> DeleteAnnualPlanType(AnnualPlanTypeListView annualPlanType, Session session);
        /// <summary>
        /// 获取年度计划分类（本部门及上级、间接上级部门分类）
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanTypeListInfo>> GetAnnualPlanTypeListByDepartment(int departmentID);

        /// <summary>
        /// 检核年度计划分类
        /// </summary>
        /// <param name="annualPlanTypeID">分类类别ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<Tuple<bool, string>> CheckAnnualPlanTypeList(int annualPlanTypeID, Session session);
        /// <summary>
        /// 获取年度计划分类
        /// </summary>
        /// <param name="typeIds">分类Id集合</param>
        /// <returns></returns>
        Task<List<AnnualPlanTypeListInfo>> GetTypeList(int[] typeIds);
        #endregion
    }
}
