﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDepartmentPostRepository : ICacheRepository
    {
        /// <summary>
        /// 获取部门岗位数据
        /// </summary>
        /// <param name="departmentID">科室代码</param>
        /// <returns></returns>
        Task<List<DepartmentPostInfo>> GetAsync(int departmentID);
        /// <summary>
        /// 获取最大的ID
        /// </summary>
        /// <returns></returns>
        Task<int> GetMaxID();
        /// <summary>
        /// 获取部门岗位数据
        /// </summary>
        /// <param name="departmentPostID">部门岗位ID</param>
        /// <returns></returns>
        Task<DepartmentPostInfo> GetDepartmentPostByID(int departmentPostID);
        /// <summary>
        /// 根据岗位序号集合获取部门岗位数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="postIDs"></param>
        /// <returns></returns>
        Task<List<DepartmentPostInfo>> GetByPostIDs(int departmentID, List<int> postIDs);
        /// <summary>
        /// 根据岗位ID获取数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        Task<bool> GetExistByPostID(int postID, string hospitalID);
    }
}
