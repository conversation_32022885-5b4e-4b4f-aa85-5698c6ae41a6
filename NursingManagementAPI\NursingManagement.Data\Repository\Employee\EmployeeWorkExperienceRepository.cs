﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeWorkExperienceRepository : IEmployeeWorkExperienceRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeWorkExperienceRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeWorkExperienceInfo>> GetListByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeWorkExperienceInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}