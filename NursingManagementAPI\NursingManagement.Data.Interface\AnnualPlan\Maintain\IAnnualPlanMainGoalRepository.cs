﻿using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划目标
    /// </summary>
    public interface IAnnualPlanMainGoalRepository
    {
        /// <summary>
        /// 获取年度计划目标字典ID集合
        /// </summary>
        /// <param name="mainGoalIDs">目标表ID集合</param>
        /// <returns></returns>
        Task<List<int>> GetAnnualPlanGoalIDs(IEnumerable<string> mainGoalIDs);
        /// <summary>
        /// 获取年度计划分类目标表集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="isTracking">是否跟踪</param>
        /// <returns></returns>
        Task<List<AnnualPlanMainGoalInfo>> GetInfosByPlanMainID(string mainID, bool isTracking = false);
        /// <summary>
        /// 获取关联的年度计划分类字典ID集合
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        Task<List<int>> GetAnnualPlanTypeIDs(string mainID);
        /// <summary>
        /// 获取分类关联的年度计划目标View
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        Task<List<APMainGoal>> GetAPGoalViews(string mainID);
        /// <summary>
        /// 获取浏览视图
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanPreview.PlanType.PlanGoal>> GetBrowseView(string mainID);
        /// <summary>
        /// 获取目标字典
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<List<KeyValueString>> GetAnnualPlanMainList(string mainID);
        /// <summary>
        /// 根据主键获取年度计划分类-目标
        /// </summary>
        /// <param name="mainGoalID">年度计划分类-目标主键ID</param>
        /// <returns></returns>
        Task<AnnualPlanMainGoalInfo> GetAnnualPlanMainGoalByMainGoalID(string mainGoalID);

        /// <summary>
        /// 根据分类ID获取年度计划分类
        /// </summary>
        /// <param name="typeID">分类类别ID</param>
        /// <param name="hospitalID">医院ID</param>
        /// <returns></returns>
        Task<AnnualPlanMainGoalInfo> GetAnnualPlanMainGoalByAnnualPlanTypeID(int typeID, string hospitalID);
        /// <summary>
        /// 根据目标和计划主记录ID获取计划主键ID
        /// </summary>
        /// <param name="annualMainID"></param>
        /// <param name="goalID"></param>
        /// <returns></returns>
        Task<string> GetAnnualPlanMainGoalIDByGoalIDAndMainIDAsync(string annualMainID, int goalID);
        /// <summary>
        /// 获取目标ID集合
        /// </summary>
        /// <param name="mainID">主表计划ID</param>
        /// <returns></returns>
        Task<string[]> GetMainGoalIDsByPlanMainID(string mainID);
        /// <summary>
        /// 获取年度计划目标的序号
        /// </summary>
        /// <param name="predicate">筛选条件</param>
        /// <returns></returns>
        Task<Dictionary<string, int>> GetMainGoalsSort(Expression<Func<AnnualPlanMainGoalInfo, bool>> predicate);

        /// <summary>
        /// 获取年度计划目标ID所属的分类ID
        /// </summary>
        /// <param name="mainGoalIDs">年度计划目标ID</param>
        /// <returns></returns>
        Task<Dictionary<string, int>> GetMainGoalIDBelongType(List<string> mainGoalIDs);
    }
}
