﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ShiftSchedulingDetailMarkRepository : IShiftSchedulingDetailMarkRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        public ShiftSchedulingDetailMarkRepository(
            NursingManagementDbContext db
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<ShiftSchedulingDetailMarkInfo>> GetMarkByRecordID(string shiftSchedulingRecordID)
        {
            return await _nursingManagementDbContext.ShiftSchedulingDetailMarkInfos.Where(m => m.ShiftSchedulingRecordID == shiftSchedulingRecordID && m.DeleteFlag != "*").ToListAsync();
        }
        public async Task<List<ShiftSchedulingDetailMarkInfo>> GetMarkByRecordID(string shiftSchedulingRecordID, DateTime? startDate, DateTime? endDate)
        {
            return await _nursingManagementDbContext.ShiftSchedulingDetailMarkInfos.Where(m => m.ShiftSchedulingRecordID == shiftSchedulingRecordID && m.DeleteFlag != "*")
                        .IfWhere(startDate != null, m => m.SchedulingDate.Date >= startDate.Value.Date)
                        .IfWhere(endDate != null, m => m.SchedulingDate.Date <= endDate.Value.Date)
                        .ToListAsync();
        }
        /// <summary>
        ///  根据employeeID集合获取数据
        /// </summary>
        /// <param name="shiftSchedulingRecordIDs">排班主记录ID集合</param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<ShiftSchedulingDetailMarkInfo>> GetMarkByEmployeeID(List<string> shiftSchedulingRecordIDs, string employeeID, DateTime? startDate, DateTime? endDate)
        {
            var datas = await _nursingManagementDbContext.ShiftSchedulingDetailMarkInfos.Where(m => shiftSchedulingRecordIDs.Any(n => n == m.ShiftSchedulingRecordID) && employeeID == m.EmployeeID && m.DeleteFlag != "*")
                        .IfWhere(startDate != null, m => m.SchedulingDate.Date >= startDate.Value.Date)
                        .IfWhere(endDate != null && startDate != null, m => m.SchedulingDate.Date <= endDate.Value.Date)
                         .IfWhere(endDate != null && startDate == null, m => m.SchedulingDate.Date >= endDate.Value.Date)
                        .ToListAsync();
            return datas;
        }
        public async Task<List<ShiftSchedulingDetailMarkInfo>> GetExcludeDepartmentMarksByEmployeeIDs(List<string> employeeIDs, DateTime startDate, DateTime endDate, int excludeDepartmentID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            var shiftSchedulingDetailMarks = await _nursingManagementDbContext.ShiftSchedulingDetailMarkInfos.Where(m => employeeIDs.Contains(m.EmployeeID) && m.SchedulingDate.Date >= startDate.Date
                                        && m.SchedulingDate.Date <= endDate.Date && m.HospitalID == session.HospitalID && m.DeleteFlag != "*").ToListAsync();
            var shiftSchedulingRecords = await _nursingManagementDbContext.ShiftSchedulingRecordInfos.Where(n => n.DepartmentID != excludeDepartmentID && n.StatusCode != "0" && n.DeleteFlag != "*")
                                        .OrderByDescending(j => j.ModifyDateTime).GroupBy(j => new { j.DepartmentID, j.StartDate, j.EndDate }).Select(j => j.FirstOrDefault()).ToListAsync();

            return (from m in shiftSchedulingDetailMarks
                    join n in shiftSchedulingRecords
                    on m.ShiftSchedulingRecordID equals n.ShiftSchedulingRecordID
                    select m).ToList();
        }
    }
}
