﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API
{
    /// <summary>
    /// 消息管理控制器
    /// </summary>
    [Route("api/messageManagement")]
    [Produces("application/json")]
    [EnableCors("any")]
    public class MessageRecordController : Controller
    {
        private readonly IMessageManagementService _messageRecordService;
        private readonly ISessionService _sessionService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="messageRecordService"></param>
        /// <param name="sessionService"></param>
        public MessageRecordController(IMessageManagementService messageRecordService, ISessionService sessionService)
        {
            _messageRecordService = messageRecordService;
            _sessionService = sessionService;
        }

        /// <summary>
        /// 获取消息列表
        /// </summary>
        /// <param name="messageType"></param>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        [HttpGet("GetMessageList")]
        public async Task<IActionResult> GetMessageList(string messageType, string departmentIDs)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var departmentIDList = ListToJson.ToList<List<int>>(departmentIDs);
            result.Data = await _messageRecordService.GetMessageListAsync(messageType, departmentIDList);
            return result.ToJson();
        }
        /// <summary>
        /// 获取消息详情
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <returns>消息详情</returns>
        [HttpGet("GetMessageDetail")]
        public async Task<IActionResult> GetMessageDetail(string messageRecordID)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageRecordService.GetMessageDetailAsync(messageRecordID);
            return result.ToJson();
        }
        /// <summary>
        /// 创建新消息
        /// </summary>
        /// <param name="messageView">消息信息</param>
        /// <returns>创建结果</returns>
        [HttpPost("AddMessage")]
        public async Task<IActionResult> AddMessage([FromBody] MessageRecordView messageView)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageRecordService.AddMessageAsync(messageView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 更新消息
        /// </summary>
        /// <param name="messageView">更新的消息信息</param>
        /// <returns>更新结果</returns>
        [HttpPost("UpdateMessage")]
        public async Task<IActionResult> UpdateMessage([FromBody] MessageRecordView messageView)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageRecordService.UpdateMessageAsync(messageView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除消息
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <returns>删除结果</returns>
        [HttpPost("DeleteMessage")]
        public async Task<IActionResult> DeleteMessage(string messageRecordID)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageRecordService.DeleteMessageAsync(messageRecordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 发布/取消发布消息
        /// </summary>
        /// <param name="messageRecordID">消息ID</param>
        /// <param name="messageStatus">消息状态</param>
        /// <returns>发布结果</returns>
        [HttpPost("PublishMessage")]
        public async Task<IActionResult> PublishMessage(string messageRecordID, string messageStatus)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageRecordService.PublishMessageAsync(messageRecordID, messageStatus, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 发送系统预更新通知
        /// </summary>
        /// <param name="messageRecordID"></param>
        /// <param name="customMessage"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SendSystemPreUpdateMessage")]
        public async Task<IActionResult> SendSystemPreUpdateMessage(string messageRecordID, string customMessage)
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageRecordService.SendSystemPreUpdateMessage(messageRecordID, customMessage);
            return result.ToJson();
        }
        /// <summary>
        /// 获取最后一条系统更新记录
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLastSystemUpdateRecord")]
        public async Task<IActionResult> GetLastSystemUpdateRecord()
        {
            var result = new ResponseResult();
            var session = await _sessionService.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _messageRecordService.GetLastSystemUpdateRecord();
            return result.ToJson();
        }
    }
}