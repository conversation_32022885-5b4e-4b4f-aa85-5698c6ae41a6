﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface.AnnualPlan
{
    public interface IAnnualPlanOverViewService
    {
        /// <summary>
        /// 生成合并后年度计划文档的文档
        /// </summary>
        /// <param name="mainID">年度计划主键ID</param>
        /// <returns></returns>
        Task<string> CombineWordDoc(string mainID);
        /// <summary>
        /// 获取年度计划导出信息
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanExportView>> GetAnnualPlanExportViews(string mainID);
        /// <summary>
        /// 生成季度计划Word文档
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <param name="showRoutineWorks">是否显示常规工作</param>
        /// <returns></returns>
        Task<string> GenerateQuarterPlanWordDocAsync(string quarterPlanMainID, bool showRoutineWorks);
        /// <summary>
        /// 获取年度计划预览视图
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<AnnualPlanPreview> GetAnnualPlanPreview(string mainID);
    }
}
