﻿namespace NursingManagement.ViewModels
{
    public class APIndicatorDetail : IBaseAPDetail
    {
        /// <summary>
        /// 主表ID
        /// </summary>
        public string MainID { get; set; }
        /// <summary>
        /// 指标明细ID
        /// </summary>
        public string DetailID { get; set; }
        /// <summary>
        /// 年度计划目标GuID,主键，对应年度计划目标表	
        /// </summary>
        public string MainGoalID { get; set; }
        /// <summary>
        /// 年度计划目标分组主键ID	
        /// </summary>
        public string GroupID { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 指标字典ID
        /// </summary>
        public int AnnualIndicatorID { get; set; }
        /// <summary>
        /// 参考后的自定义名称
        /// </summary>
        public string LocalShowName { get; set; }
        /// <summary>
        /// 是否参考的上级指标
        /// </summary>
        public bool IsUpperIndicator { get; set; }
        /// <summary>
        /// 条件操作符（>、<、≥、≤）
        /// </summary>
        public string Operator { get; set; }
        /// <summary>
        /// 参考值
        /// </summary>
        public decimal? ReferenceValue { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 特别标记
        /// </summary>
        public string MarkID { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
    }
}
