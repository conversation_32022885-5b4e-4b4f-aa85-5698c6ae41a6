﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划指标明细
    /// </summary>
    public class AnnualPlanIndicatorDetailRepository : IAnnualPlanIndicatorDetailRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public AnnualPlanIndicatorDetailRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }
        /// <summary>
        /// 根据主键获取指标明细
        /// </summary>
        /// <param name="indicatorDetailID">主键ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanIndicatorDetailInfo> GetDetailByID(string indicatorDetailID)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos.FirstOrDefaultAsync(m => m.DetailID == indicatorDetailID);
        }
        /// <summary>
        /// 根据主表ID获取指标明细集合，不跟踪
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanIndicatorDetailInfo>> GetInfosByPlanMainIDAsNoTracking(string annualPlanMainID)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos.AsNoTracking().Where(m => m.AnnualPlanMainID == annualPlanMainID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取简略的指标明细集合
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<APIndicatorDetail[]> GetIndicatorDetailsByPlanMainID(string planMainID, string[] mainGoalIDs = null)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos.Where(m => planMainID == m.AnnualPlanMainID && m.DeleteFlag != "*")
                .IfWhere(mainGoalIDs?.Length > 0, m => mainGoalIDs.Contains(m.AnnualPlanMainGoalID))
                .Select(m => new APIndicatorDetail
                {
                    MainID = m.AnnualPlanMainID,
                    MainGoalID = m.AnnualPlanMainGoalID,
                    GroupID = m.AnnualPlanGoalGroupID,
                    DetailID = m.DetailID,
                    Sort = m.Sort,
                }).OrderBy(m => m.Sort).ToArrayAsync();
        }
        /// <summary>
        /// 获取计划已参考的指标ID集合
        /// </summary>
        /// <param name="mainID">年度计划业务主表ID</param>
        /// <returns></returns>
        public async Task<int[]> GetRefIndicatorIDs(string mainID)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos.Where(m => m.AnnualPlanMainID == mainID &&
            m.DeleteFlag != "*").Select(m => m.AnnualIndicatorID).ToArrayAsync();
        }
        /// <summary>
        /// 获取当前分组的明细实体
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanIndicatorDetailInfo>> GetIndicatorInfosByGroupID(string groupID)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos.Where(m => m.AnnualPlanGoalGroupID == groupID &&
                                  m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取分组下的指标明细集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">计划分组ID</param>
        /// <returns></returns>
        public async Task<List<APIndicatorDetail>> GetViewsByGroupID(string mainID, string groupID)
        {
            var indicatorDetails = await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos
                .Where(m => mainID == m.AnnualPlanMainID && m.AnnualPlanGoalGroupID == groupID && m.DeleteFlag != "*")
                .Select(m => new APIndicatorDetail
                {
                    MainID = m.AnnualPlanMainID,
                    DetailID = m.DetailID,
                    MainGoalID = m.AnnualPlanMainGoalID,
                    GroupID = m.AnnualPlanGoalGroupID,
                    AnnualIndicatorID = m.AnnualIndicatorID,
                    LocalShowName = m.LocalShowName,
                    Operator = m.Operator,
                    ReferenceValue = m.ReferenceValue,
                    Unit = m.Unit,
                    MarkID = m.MarkID,
                    Remark = m.Remark,
                    Year = m.Year,
                    Sort = m.Sort
                }).ToListAsync();
            return indicatorDetails;
        }
        /// <summary>
        /// 获取序号之后的明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="sort">序号</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanIndicatorDetailInfo>> GetAfterSortDetail(string mainID, int sort)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos
                .Where(m => m.AnnualPlanMainID == mainID && m.Sort >= sort && m.DeleteFlag != "*")
                .OrderBy(m => m.Sort).ToListAsync();
        }
        /// <summary>
        /// 根据主表ID获取明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalIDs">计划目标IDs</param>
        /// <returns></returns>
        public async Task<List<APIndicatorDetail>> GetViewsByMainIDAndMainGoalIDs(string mainID, string[] mainGoalIDs)
        {
            if (string.IsNullOrEmpty(mainID))
            {
                return null;
            }
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos.Where(m => m.AnnualPlanMainID == mainID &&
            m.DeleteFlag != "*").IfWhere(mainGoalIDs?.Length > 0, m => mainGoalIDs.Contains(m.AnnualPlanMainGoalID)).Select(m =>
            new APIndicatorDetail
            {
                MainID = m.AnnualPlanMainID,
                DetailID = m.DetailID,
                MainGoalID = m.AnnualPlanMainGoalID,
                GroupID = m.AnnualPlanGoalGroupID,
                AnnualIndicatorID = m.AnnualIndicatorID,
                LocalShowName = m.LocalShowName,
                Operator = m.Operator,
                ReferenceValue = m.ReferenceValue,
                Unit = m.Unit,
                MarkID = m.MarkID,
                Remark = m.Remark,
                Year = m.Year,
                Sort = m.Sort
            }).OrderBy(m=> m.Sort).ToListAsync();
        }
        /// <summary>
        /// 根据主表ID获取指标明细集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanIndicatorDetailInfo>> GetInfosByMainID(string annualPlanMainID)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos
                .OrderBy(m => m.Sort)
                .Where(m => m.AnnualPlanMainID == annualPlanMainID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取一定范围内的明细
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <param name="beginSort">开始序号</param>
        /// <param name="endSort">结束序号</param>
        /// <returns></returns>
        public async Task<IBaseAPDetail[]> GetRangeInfosByMainID(string annualPlanMainID, int beginSort, int endSort)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos
                .Where(m => m.AnnualPlanMainID == annualPlanMainID && m.Sort >= beginSort && m.Sort <= endSort && m.DeleteFlag != "*")
                .ToArrayAsync();
        }
        /// <summary>
        /// 分组序号
        /// </summary>
        /// <param name="groupID">分组序号</param>
        /// <param name="groupSort">分组排序号</param>
        /// <returns></returns>
        public async Task<int> GetMaxSortByGroupID(string groupID, int groupSort)
        {
            var currentGroupSorts = await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos.Where(m => m.AnnualPlanGoalGroupID == groupID && m.DeleteFlag != "*").Select(m => m.Sort).ToListAsync();
            // 如果当前分组无明细，则查询此分组之前的分组最大序号
            if (currentGroupSorts.Count == 0)
            {
                var sorts = await (from a in _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos
                 from b in _nursingManagementDbContext.AnnualPlanGoalGroupInfos
                 where a.AnnualPlanGoalGroupID == b.AnnualPlanGoalGroupID && b.Sort < groupSort
                 select a.Sort).ToListAsync();
                // 如果此前的分组也没有明细，则返回最大序号为0
                if (sorts.Count == 0)
                {
                    return 0;
                }
                // 否则返回此前分组最大序号
                return sorts.Max();
            }
            var maxSort = currentGroupSorts.Max();
            return maxSort;
        }
        /// <summary>
        /// 根据主表ID获取指标明细集合
        /// </summary>
        /// <param name="mainGoalID">目标表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanIndicatorDetailInfo>> GetInfosByMainGoalID(string mainGoalID)
        {
            return await _nursingManagementDbContext.AnnualPlanIndicatorDetailInfos
                .OrderBy(m => m.Sort)
                .Where(m => m.AnnualPlanMainGoalID == mainGoalID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}
