﻿namespace NursingManagement.ViewModels.Employee
{
    public class EmployeeRelativesView
    {
        public string EmployeeRelativesID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 家庭关系(GB/T 4761-2008转换后)
        /// </summary>
        public string Relationship { get; set; }

        /// <summary>
        /// 亲属姓名
        /// </summary>
        public string RelativesName { get; set; }

        /// <summary>
        /// 亲属手机号码
        /// </summary>
        public string RelativesPhoneNumber { get; set; }

        /// <summary>
        /// 亲属身份证
        /// </summary>
        public string RelativesIDCardNo { get; set; }

        /// <summary>
        /// 亲属籍贯
        /// </summary>
        public string RelativesNativePlace { get; set; }

        /// <summary>
        /// 亲属学历
        /// </summary>
        public string RelativesEducation { get; set; }

        /// <summary>
        /// 亲属学校
        /// </summary>
        public string RelativesSchool { get; set; }

        /// <summary>
        /// 亲属任职单位
        /// </summary>
        public string RelativesCompany { get; set; }

        /// <summary>
        /// 亲属担任职务
        /// </summary>
        public string RelativesPost { get; set; }
    }
}
