﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("TrainingClassMain")]
    public class TrainingClassMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 培训群组主表ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string TrainingClassMainID { get; set; }

        /// <summary>
        /// 培训群组名称
        /// </summary>
        [Column(TypeName = "nvarchar(50)")]
        public string TrainingClassName { get; set; }

        /// <summary>
        /// 开班日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 闭班日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 培训时长
        /// </summary>
        public int TrainingDuration { get; set; }

        /// <summary>
        /// 结业日期
        /// </summary>
        public DateTime CompleteDate { get; set; }
    }
}