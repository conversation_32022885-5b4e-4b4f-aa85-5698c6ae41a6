using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    /// <summary>
    /// 监考人计划表service
    /// </summary>
    public class ExaminerScheduleService : IExaminerScheduleService
    {

        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMessageService _messageService;
        private readonly IExaminerScheduleRepository _examinerScheduleRepository;
        private readonly IExaminerScheduleItemRepository _examinerScheduleItemRepository;
        private readonly IExaminerScheduleEmployeeRepository _examinerScheduleEmployeeRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IExaminationRecordRepository _examinationRecordRepository;
        private readonly IExaminationAppointmentRepository _examinationAppointmentRepository;

        /// <summary>
        /// 监考计划的未被预约状态
        /// </summary>
        private static readonly string NOT_APPOINTMENT_STATUS = "0";
        /// <summary>
        /// 预约记录的已预约状态
        /// </summary>
        private static readonly string EXAMINATION_APPOINTMENT_STATUS = "1";

        public ExaminerScheduleService(
            IUnitOfWork unitOfWork,
            IMessageService messageService,
            IExaminerScheduleRepository examinerScheduleRepository,
            IExaminerScheduleItemRepository examinerScheduleItemRepository,
            IExaminerScheduleEmployeeRepository examinerScheduleEmployeeRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IExaminationRecordRepository examinationRecordRepository,
            IExaminationAppointmentRepository examinationAppointmentRepository
        )
        {
            _unitOfWork = unitOfWork;
            _messageService = messageService;
            _examinerScheduleRepository = examinerScheduleRepository;
            _examinerScheduleItemRepository = examinerScheduleItemRepository;
            _examinerScheduleEmployeeRepository = examinerScheduleEmployeeRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _examinationRecordRepository = examinationRecordRepository;
            _examinationAppointmentRepository = examinationAppointmentRepository;
        }


        public async Task<List<ExaminerScheduleView>> GetExaminerScheduleList(string employeeID, string examinationRecordID, DateTime startDate, DateTime endDate)
        {
            var examinerScheduleList = await _examinerScheduleRepository.GetListByDateRange(startDate, endDate);
            if (examinerScheduleList.Count <= 0)
            {
                return [];
            }
            var examinerScheduleIDs = examinerScheduleList.Select(m => m.ExaminerScheduleID).ToList();
            var examinerScheduleEmployees = await _examinerScheduleEmployeeRepository.GetByExaminerScheduleIDs(examinerScheduleIDs);
            var examinerScheduleItems = await _examinerScheduleItemRepository.GetByExaminerScheduleIDs(examinerScheduleIDs);
            var employeeIDs = examinerScheduleEmployees.Select(m => m.EmployeeID).ToList();
            employeeIDs = examinerScheduleList.SelectMany(m => new[] { m.AddEmployeeID, m.ModifyEmployeeID }.Concat(employeeIDs)).Distinct().ToList();
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            var examinationRecordIDs = examinerScheduleItems.Select(m => m.ExaminationRecordID).ToList();
            var examinationRecordDict = await _examinationRecordRepository.GetRecordIDAndNameDictByIDs(examinationRecordIDs);
            List<ExaminerScheduleView> resultList = [];
            foreach (var examinerSchedule in examinerScheduleList)
            {
                var examinerScheduleEmployeeIDs = examinerScheduleEmployees.Where(m => m.ExaminerScheduleID == examinerSchedule.ExaminerScheduleID).OrderBy(m => m.Sort).Select(m => m.EmployeeID).ToList();
                if (!string.IsNullOrWhiteSpace(employeeID) && !examinerScheduleEmployeeIDs.Contains(employeeID))
                {
                    continue;
                }
                var examinerScheduleItemIDs = examinerScheduleItems.Where(m => m.ExaminerScheduleID == examinerSchedule.ExaminerScheduleID).OrderBy(m => m.Sort).Select(m => m.ExaminationRecordID).ToList();
                if (!string.IsNullOrWhiteSpace(examinationRecordID) && !examinerScheduleItemIDs.Contains(examinationRecordID))
                {
                    continue;
                }
                resultList.Add(CreateExaminerScheduleView(examinerSchedule, examinerScheduleEmployeeIDs, examinerScheduleItemIDs, employeeList, examinationRecordDict));
            }
            return resultList.OrderBy(m => m.ScheduleDate).ToList();
        }

        /// <summary>
        /// 组装监考人计划视图
        /// </summary>
        /// <param name="examinerScheduleInfo"></param>
        /// <param name="examinerScheduleEmployeeIDs"></param>
        /// <param name="examinerScheduleItemIDs"></param>
        /// <param name="employeeList"></param>
        /// <param name="examinationRecordDict"></param>
        /// <returns></returns>
        private static ExaminerScheduleView CreateExaminerScheduleView(ExaminerScheduleInfo examinerScheduleInfo, List<string> examinerScheduleEmployeeIDs, List<string> examinerScheduleItemIDs, List<EmployeePersonalDataListView> employeeList, Dictionary<string, string> examinationRecordDict)
        {
            var scheduleView = new ExaminerScheduleView
            {
                ExaminerScheduleID = examinerScheduleInfo.ExaminerScheduleID,
                ExaminationRecordIDs = examinerScheduleItemIDs,
                Examiners = examinerScheduleEmployeeIDs,
                ScheduleDate = examinerScheduleInfo.ScheduleDate,
                ScheduleTimeRange = [examinerScheduleInfo.ScheduleStartTime.ToString(@"hh\:mm"), examinerScheduleInfo.ScheduleEndTime.ToString(@"hh\:mm")],
                StatusCode = examinerScheduleInfo.StatusCode,
                AddEmployeeID = examinerScheduleInfo.AddEmployeeID,
                AddEmployeeName = employeeList.Find(m => m.EmployeeID == examinerScheduleInfo.AddEmployeeID)?.EmployeeName,
                AddDateTime = examinerScheduleInfo.AddDateTime,
                Location = examinerScheduleInfo.Location
            };
            if (scheduleView.Examiners?.Count > 0)
            {
                var employeeNames = employeeList.Where(m => scheduleView.Examiners.Contains(m.EmployeeID)).Select(m => m.EmployeeName).ToList();
                if (employeeNames.Count > 0)
                {
                    scheduleView.ExaminerName = employeeNames.Aggregate((a, b) => $"{a}、{b}");
                }
            }
            if (scheduleView.ExaminationRecordIDs?.Count > 0)
            {
                List<string> examinationNames = [];
                foreach (var examinationRecordID in scheduleView.ExaminationRecordIDs)
                {
                    if (examinationRecordDict.TryGetValue(examinationRecordID, out string examinationName) && !string.IsNullOrWhiteSpace(examinationName))
                    {
                        examinationNames.Add(examinationName);
                    }
                }
                if (examinationNames.Count > 0)
                {
                    scheduleView.ExaminationName = examinationNames.Aggregate((a, b) => $"{a}、{b}");
                }
            }
            return scheduleView;
        }

        public async Task<bool> SaveExaminerSchedule(SaveExaminerScheduleView saveExaminerScheduleView, string employeeID)
        {
            if (saveExaminerScheduleView.ExaminerScheduleID != null)
            {
                var updateExaminerScheduleInfo = await _examinerScheduleRepository.GetByIdAsync(saveExaminerScheduleView.ExaminerScheduleID);
                if (updateExaminerScheduleInfo == null)
                {
                    return false;
                }
                var oldSchedule = CloneData.CloneObj(updateExaminerScheduleInfo);
                updateExaminerScheduleInfo.ScheduleDate = saveExaminerScheduleView.ScheduleDate;
                updateExaminerScheduleInfo.Location = saveExaminerScheduleView.Location;
                updateExaminerScheduleInfo.ScheduleStartTime = saveExaminerScheduleView.ScheduleTimeRange[0];
                updateExaminerScheduleInfo.ScheduleEndTime = saveExaminerScheduleView.ScheduleTimeRange[1];
                updateExaminerScheduleInfo.Modify(employeeID);
                (var deleteScheduleEmployeeFlag, var deleteScheduleItem) = await DeleteExaminerScheduleDetail(saveExaminerScheduleView, employeeID);
                await InsertExaminerScheduleDetail(saveExaminerScheduleView, saveExaminerScheduleView.ExaminerScheduleID, employeeID, deleteScheduleEmployeeFlag, deleteScheduleItem);
                var result = await _unitOfWork.SaveChangesAsync() >= 0;
                // 计划改变且已被预约 通知预约人员
                if (result && updateExaminerScheduleInfo.StatusCode != NOT_APPOINTMENT_STATUS)
                {
                    await SendModifyNotification(oldSchedule, updateExaminerScheduleInfo);
                }
                return result;
            }
            List<DateTime> scheduleDates = [];
            // 批量产生监考计划
            if (saveExaminerScheduleView.BatchFlag.HasValue && saveExaminerScheduleView.BatchFlag.Value
                && saveExaminerScheduleView.BatchInterval.HasValue && saveExaminerScheduleView.BatchEndDate.HasValue)
            {
                for (var date = saveExaminerScheduleView.ScheduleDate; date <= saveExaminerScheduleView.BatchEndDate.Value; date = date.AddDays(saveExaminerScheduleView.BatchInterval.Value))
                {
                    scheduleDates.Add(date);
                }
            }
            else
            {
                scheduleDates = [saveExaminerScheduleView.ScheduleDate];
            }
            foreach (var scheduleDate in scheduleDates)
            {
                var examinerScheduleInfo = new ExaminerScheduleInfo
                {
                    ScheduleDate = scheduleDate,
                    Location = saveExaminerScheduleView.Location,
                    ScheduleStartTime = saveExaminerScheduleView.ScheduleTimeRange[0],
                    ScheduleEndTime = saveExaminerScheduleView.ScheduleTimeRange[1],
                    StatusCode = NOT_APPOINTMENT_STATUS
                };
                examinerScheduleInfo.ExaminerScheduleID = examinerScheduleInfo.GetId();
                examinerScheduleInfo.Add(employeeID).Modify(employeeID);
                await _unitOfWork.GetRepository<ExaminerScheduleInfo>().InsertAsync(examinerScheduleInfo);
                await InsertExaminerScheduleDetail(saveExaminerScheduleView, examinerScheduleInfo.ExaminerScheduleID, employeeID, true, true);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        #region 监考计划发生改变，通知预约人员
        public async Task SendModifyNotification(ExaminerScheduleInfo oldSchedule, ExaminerScheduleInfo newExaminerScheduleInfo)
        {
            var message = "";
            if (oldSchedule.ScheduleDate != newExaminerScheduleInfo.ScheduleDate)
            {
                message += $"日期由{oldSchedule.ScheduleDate:yyyy-MM-dd}变更为{newExaminerScheduleInfo.ScheduleDate:yyyy-MM-dd}；";
            }
            if (oldSchedule.ScheduleStartTime != newExaminerScheduleInfo.ScheduleStartTime)
            {
                message += $"开始时间由{oldSchedule.ScheduleStartTime:hh\\:mm}变更为{newExaminerScheduleInfo.ScheduleStartTime:hh\\:dd}；";
            }
            if (oldSchedule.ScheduleEndTime != newExaminerScheduleInfo.ScheduleEndTime)
            {
                message += $"结束时间由{oldSchedule.ScheduleEndTime:hh\\:mm}变更为{newExaminerScheduleInfo.ScheduleEndTime:hh\\:dd}；";
            }
            if (oldSchedule.Location != newExaminerScheduleInfo.Location)
            {
                message += $"考核地点由{(string.IsNullOrWhiteSpace(oldSchedule.Location) ? "空" : oldSchedule.Location)}变更为{(string.IsNullOrWhiteSpace(newExaminerScheduleInfo.Location) ? "空" : newExaminerScheduleInfo.Location)}；";
            }
            if (string.IsNullOrWhiteSpace(message))
            {
                return;
            }
            var examinationAppointmentList = await _examinationAppointmentRepository.GetListByConditionAsync(m => m.ExaminerScheduleID == oldSchedule.ExaminerScheduleID && m.StatusCode == EXAMINATION_APPOINTMENT_STATUS);
            var examinationRecordIDs = examinationAppointmentList.Select(m => m.ExaminationRecordID).Distinct().ToList();
            var examinationRecordDict = await _examinationRecordRepository.GetRecordIDAndNameDictByIDs(examinationRecordIDs);
            Dictionary<string, string> notificationList = new();
            foreach (var examinationAppointment in examinationAppointmentList)
            {
                // 没有获取到考核信息 直接不进行通知
                if (!examinationRecordDict.TryGetValue(examinationAppointment.ExaminationRecordID, out var examinationName))
                {
                    continue;
                }
                message = $"您预约的操作考核【{examinationName}】有变动：{message}";
                notificationList.TryAdd(examinationAppointment.EmployeeID, message);
            }
            //发送通知至移动端
            var messageViewList = GenerateMessageView(notificationList, (int)ClientType.Mobile);
            foreach (var messageView in messageViewList)
            {
                await _messageService.SendMessage(messageView);
            }
        }
        /// <summary>
        /// 生成发送通知需要的参数对象
        /// </summary>
        /// <param name="examineEmployeeList"></param>
        /// <param name="clientType"></param>
        /// <returns></returns>
        private List<MessageView> GenerateMessageView(Dictionary<string, string> examineEmployeeList, int clientType)
        {
            var messageView = new List<MessageView>();
            foreach (var item in examineEmployeeList)
            {
                var view = new MessageView
                {
                    MessageTools = [MessageTool.Wechat],
                    EmployeeID = item.Key,
                    ClientType = clientType,
                    MessageCondition = new MessageConditionView
                    {
                        // 移动端与PC端使用不同的交换机
                        MQExchangeName = "MQNotification",
                        MQRoutingKey = item.Key,
                        Message = item.Value,
                        ClientType = clientType,
                    }
                };
                messageView.Add(view);
            }
            return messageView;
        }
        #endregion

        public async Task<bool> DeleteExaminerSchedule(string examinerScheduleID, string employeeID)
        {
            var invigolationSchedule = await _examinerScheduleRepository.GetByIdAsync(examinerScheduleID);
            if (invigolationSchedule == null)
            {
                _logger.Error($"找不到对应的监考计划记录examinerScheduleInfoID={examinerScheduleID}");
                return false;
            }
            invigolationSchedule.Delete(employeeID);
            await DeleteExaminerScheduleDetail(new SaveExaminerScheduleView() { ExaminerScheduleID = examinerScheduleID }, employeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 删除监考计划详细信息
        /// </summary>
        /// <param name="saveExaminerScheduleView"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<(bool, bool)> DeleteExaminerScheduleDetail(SaveExaminerScheduleView saveExaminerScheduleView, string employeeID)
        {
            var deleteScheduleEmployeeFlag = false;
            var deleteScheduleItem = false;
            var examinerScheduleEmployees = await _examinerScheduleEmployeeRepository.GetByExaminerScheduleID(saveExaminerScheduleView.ExaminerScheduleID);
            if (examinerScheduleEmployees?.Count > 0)
            {
                // 判断前端传的数据是否被修改，如修改，删除旧数据，否则只更新修改时间
                var oldExaminerScheduleEmployeeIDsStr = ListToJson.ToJson(examinerScheduleEmployees.Select(m => m.EmployeeID).ToList());
                if (saveExaminerScheduleView.Examiners?.Count <= 0 || oldExaminerScheduleEmployeeIDsStr != ListToJson.ToJson(saveExaminerScheduleView.Examiners))
                {
                    examinerScheduleEmployees.ForEach(m => m.Delete(employeeID));
                    deleteScheduleEmployeeFlag = true;
                }
                else
                {
                    examinerScheduleEmployees.ForEach(m => m.Modify(employeeID));
                }
            }
            var examinerScheduleItems = await _examinerScheduleItemRepository.GetByExaminerScheduleID(saveExaminerScheduleView.ExaminerScheduleID);
            if (examinerScheduleItems?.Count > 0)
            {
                // 判断前端传的数据是否被修改，如修改，删除旧数据，否则只更新修改时间
                var oldExaminationRecordIDsStr = ListToJson.ToJson(examinerScheduleItems.Select(m => m.ExaminationRecordID).ToList());
                if (saveExaminerScheduleView.ExaminationRecordIDs?.Count <= 0 || oldExaminationRecordIDsStr != ListToJson.ToJson(saveExaminerScheduleView.ExaminationRecordIDs))
                {
                    examinerScheduleItems.ForEach(m => m.Delete(employeeID));
                    deleteScheduleItem = true;
                }
                else
                {
                    examinerScheduleItems.ForEach(m => m.Modify(employeeID));
                }
            }
            return (deleteScheduleEmployeeFlag, deleteScheduleItem);
        }
        /// <summary>
        /// 删除(跟当前考核计划相关的所有)监考计划
        /// </summary>
        /// <param name="examinationRecordID">考核记录ID</param>
        /// <param name="employeeID">操作人工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteExaminerScheduleByRecordID(string examinationRecordID, string employeeID)
        {
            var scheduleIDs = await _examinerScheduleItemRepository.GetScheduleIDsByExaminationRecordID(examinationRecordID);
            if (scheduleIDs.Count == 0)
            {
                return true;
            }
            var schedules = await _examinerScheduleRepository.GetListByIdsAsync(scheduleIDs);
            if (schedules.Count == 0)
            {
                return true;
            }
            schedules.ForEach(m => m.Delete(employeeID));
            scheduleIDs = schedules.Select(m => m.ExaminerScheduleID).ToList();

            var scheduleEmployees = await _examinerScheduleEmployeeRepository.GetByExaminerScheduleIDs(scheduleIDs);
            scheduleEmployees.ForEach(m => m.Delete(employeeID));

            var scheduleItems = await _examinerScheduleItemRepository.GetByExaminerScheduleIDs(scheduleIDs);
            scheduleItems.ForEach(m => m.Delete(employeeID));

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        /// 新增监考计划详细信息
        /// </summary>
        /// <param name="saveExaminerScheduleView"></param>
        /// <param name="examinerScheduleID"></param>
        /// <param name="employeeID"></param>
        /// <param name="insertScheduleEmployeeFlag"></param>
        /// <param name="insertScheduleItem"></param>
        /// <returns></returns>
        private async Task InsertExaminerScheduleDetail(SaveExaminerScheduleView saveExaminerScheduleView, string examinerScheduleID, string employeeID, bool insertScheduleEmployeeFlag, bool insertScheduleItem)
        {
            if (insertScheduleEmployeeFlag && saveExaminerScheduleView.Examiners?.Count > 0)
            {
                var examinerSort = 1;
                foreach (var examiner in saveExaminerScheduleView.Examiners)
                {
                    var examinerScheduleEmployee = new ExaminerScheduleEmployeeInfo()
                    {
                        ExaminerScheduleID = examinerScheduleID,
                        EmployeeID = examiner,
                        Sort = examinerSort
                    };
                    examinerScheduleEmployee.ExaminerScheduleEmployeeID = examinerScheduleEmployee.GetId();
                    examinerScheduleEmployee.Add(employeeID).Modify(employeeID);
                    await _unitOfWork.GetRepository<ExaminerScheduleEmployeeInfo>().InsertAsync(examinerScheduleEmployee);
                    examinerSort++;
                }
            }
            var itemSort = 1;
            if (insertScheduleItem && saveExaminerScheduleView.ExaminationRecordIDs?.Count > 0)
            {
                foreach (var ExaminationRecordID in saveExaminerScheduleView.ExaminationRecordIDs)
                {
                    var examinerScheduleItem = new ExaminerScheduleItemInfo()
                    {
                        ExaminerScheduleID = examinerScheduleID,
                        ExaminationRecordID = ExaminationRecordID,
                        Sort = itemSort
                    };
                    examinerScheduleItem.ExaminerScheduleItemID = examinerScheduleItem.GetId();
                    examinerScheduleItem.Add(employeeID).Modify(employeeID);
                    await _unitOfWork.GetRepository<ExaminerScheduleItemInfo>().InsertAsync(examinerScheduleItem);
                    itemSort++;
                }
            }
        }
    }
}
