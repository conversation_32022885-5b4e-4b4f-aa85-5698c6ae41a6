﻿using DocumentFormat.OpenXml.Wordprocessing;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{

    /// <summary>
    /// 课程信息表
    /// </summary>
    [Serializable]
    [Table("CourseSetting")]
    public class CourseSettingInfo : MutiModifyInfo
    {
        /// <summary>
        /// 课程ID，主键
        /// </summary>
        [Key]
        [Column(TypeName="varchar(32)")]
        public string CourseSettingID { get; set; }

        /// <summary>
        /// 课程分类ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string CourseTypeID { get; set; }

        /// <summary>
        /// 课程名称
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string CourseName { get; set; }

        /// <summary>
        /// 课程简介
        /// </summary>
        [Column(TypeName = "narchar(500)")]
        public string CourseIntroduction { get; set; }

        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 父课程ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ParentID { get; set; }

        /// <summary>
        /// 课程级别
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 医院标识
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 添加部门
        /// </summary>
        public int AddDepartmentID { get; set; }
        /// <summary>
        /// 文件ID集合，存储与培训课程相关的文件ID，采用||分隔的字符串
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string FileID { get; set; }
    }
}

