using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 排班主表
    /// </summary>
    [Serializable]
    [Table("ShiftSchedulingRecord")]
    public class ShiftSchedulingRecordInfo : MutiModifyInfo
    {

        /// <summary>
        /// 排班记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ShiftSchedulingRecordID { get; set; }

        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 排班类型（1、月排班，2，周排班）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SchedulingType { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 自动排班标记
        /// </summary>
        public bool AutoFlag { get; set; }

        /// <summary>
        /// 状态 0：排班草稿、1：已发布、2：已归档（超过EndDate自动归档?）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 备注，备注本月工作重点
        /// </summary>
        [Column(TypeName = "nvarchar(500)")]
        public string Remark { get; set; }
        
    }
}
