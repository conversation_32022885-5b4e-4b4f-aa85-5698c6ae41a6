﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IAttendanceRecordRepository
    {
        /// <summary>
        ///  获取人员的考勤主表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="AttendanceYear"></param>
        /// <param name="AttendanceMonth"></param>
        /// <param name="employeeIDs"></param>
        /// <returns></returns>
        Task<List<AttendanceRecordInfo>> GetRecordByEmployeeIDs(int departmentID, int AttendanceYear, int AttendanceMonth, List<string> employeeIDs);

        /// <summary>
        ///  获取部门的考勤主表
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        Task<List<AttendanceRecordInfo>> GetRecordByDepartmentID(int departmentID, int attendanceYear, int attendanceMonth);
    }
}
