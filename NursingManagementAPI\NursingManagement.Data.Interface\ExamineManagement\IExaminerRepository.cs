﻿using NursingManagement.ViewModels.Examine;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    public interface IExaminerRepository
    {
        /// <summary>
        /// 根据来源集合获取主考人信息
        /// </summary>
        /// <param name="sourceType">来源类型：ExaminationRecord、ExaminationMain</param>
        /// <param name="sourceIDs">来源IDs</param>
        /// <returns></returns>
        Task<List<ExaminerInfo>> GetListBySourceIDsAsync(string sourceType, List<string> sourceIDs);
        /// <summary>
        /// 根据来源获取主考人信息
        /// </summary>
        /// <param name="sourceType">来源类型：ExaminationRecord、ExaminationMain</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        Task<List<ExaminerInfo>> GetListBySourceAsync(string sourceType, string sourceID);
        /// <summary>
        /// 根据来源获取主考人信息
        /// </summary>
        /// <param name="sourceType">来源类型：ExaminationRecord、ExaminationMain</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        Task<ExaminerInfo> GetBySourceAsync(string sourceType, string sourceID);
        /// <summary>
        /// 根据条件获取主考人列表
        /// </summary>
        /// <param name="predicate">>where 条件</param>
        /// <returns></returns>
        Task<List<ExaminerInfo>> GetListByConditionAsync(Expression<Func<ExaminerInfo, bool>> predicate);

        /// <summary>
        /// 根据条件获取主考人列表（不跟踪）
        /// </summary>
        /// <param name="predicate">where 条件</param>
        /// <returns></returns>
        Task<List<ExaminerInfo>> GetListByConditionAsNoTrackAsync(Expression<Func<ExaminerInfo, bool>> predicate);
    }
}
