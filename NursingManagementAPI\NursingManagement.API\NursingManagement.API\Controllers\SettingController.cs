﻿using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 字典配置控制器-获取字典配置 
    /// </summary>
    [ApiController]
    [Route("api/setting")]
    public class SettingController : ControllerBase
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IAPISettingService _apiSettingService;
        /// <summary>
        ///  字典配置控制器构造方法
        /// </summary>
        /// <param name="serverSession"></param>
        /// <param name="appConfigSettingRepository"></param>
        /// <param name="apiSettingService"></param>
        public SettingController(
            ISessionService serverSession
            , IAppConfigSettingRepository appConfigSettingRepository
            , IAPISettingService apiSettingService
        )
        {
            _session = serverSession;
            _appConfigSettingRepository = appConfigSettingRepository;
            _apiSettingService = apiSettingService;
        }
        /// <summary>
        /// 获取AppConfigSetting配置
        /// </summary>
        /// <param name="settingType"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAppConfigs")]
        public async Task<IActionResult> GetAppConfigs(string settingType)
        {
            var result = new ResponseResult();
            
                var session = await _session.GetSession();
                if (session == null)
                {
                    result.TimeOut();
                    return result.ToJson();
                }
                result.Data = await _appConfigSettingRepository.GetBySettingType(settingType);
                        return result.ToJson();
        }
        /// <summary>
        ///  根据传入的医院序号和语言序号获取AppConfigSetting配置
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAppConfigsByHospitalIDAndLanguage")]
        public async Task<IActionResult> GetAppConfigsByHospitalIDAndLanguage(string settingType,string hospitalID, int? language)
        {
            var result = new ResponseResult();
            
                if (string.IsNullOrWhiteSpace(hospitalID))
                {
                    hospitalID = "1";
                }
                if (language == null)
                {
                    language = 1;
                }
                //  将前端传入的医院和语言存入临时缓存，便于从缓存取HospitalID和Language
                await _session.SetUserLoginSession(hospitalID, language.Value);
                result.Data = await _appConfigSettingRepository.GetBySettingType(settingType);
                        return result.ToJson();
        }
        /// <summary>
        /// 根据SettingCode获取地址链接
        /// </summary>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApiUrlByCode")]
        public async Task<IActionResult> GetApiUrlByCode(string settingCode)
        {
            var result = new ResponseResult
            {
                Data = await _apiSettingService.GetAPIAddressByCode(settingCode)
            };
            return result.ToJson();
        }
    }
}
