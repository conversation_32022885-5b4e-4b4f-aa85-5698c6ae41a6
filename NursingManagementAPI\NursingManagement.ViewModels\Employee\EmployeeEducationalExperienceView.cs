﻿namespace NursingManagement.ViewModels.Employee
{
    /// <summary>
    /// 人员教育经历
    /// </summary>
    public class EmployeeEducationalExperienceView
    {
        public string EmployeeEducationalExperienceID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        public string EmployeeID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 学历(GB/T 4658-2006z转换后)
        /// </summary>
        public string EducationName { get; set; }

        /// <summary>
        /// 毕业院校
        /// </summary>
        public string GraduateSchool { get; set; }

        /// <summary>
        /// 毕业专业
        /// </summary>
        public string GraduationMajor { get; set; }

        /// <summary>
        /// 入学日期
        /// </summary>
        public string EntryDate { get; set; }

        /// <summary>
        /// 毕业日期
        /// </summary>
        public string GraduationDate { get; set; }

        /// <summary>
        /// 状态(毕业、在读)
        /// </summary>
        public string EducationStatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>

        public string Remark { get; set; }

        /// <summary>
        /// 特殊标记：第一学历First(F)、最高学历Highest(H)
        /// </summary>
        public string EducationType { get; set; }
    }
}
