﻿namespace NursingManagement.ViewModels
{
    public class QuarterPlanBrowseView
    {
        /// <summary>
        /// 季度计划主表ID
        /// </summary>
        public string MainID { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 季度
        /// </summary>
        public int Quarter { get; set; }
        /// <summary>
        /// 季度计划名称
        /// </summary>
        public string PlanName { get; set; }
        /// <summary>
        /// 是否是维护权限科室
        /// </summary>
        public bool IsMainDepartment { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 制定人名称
        /// </summary>
        public string PlannerName { get; set; }
        /// <summary>
        /// 制定人工号
        /// </summary>
        public string Planner { get; set; }
        /// <summary>
        /// 状态码
        /// </summary>
        public int StatusCode { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
    }
}
