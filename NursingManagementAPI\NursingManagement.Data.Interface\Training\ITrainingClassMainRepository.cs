﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface ITrainingClassMainRepository
    {
        /// <summary>
        /// 获取培训群组列表
        /// </summary>
        /// <returns></returns>
        Task<List<TrainingClassMainInfo>> GetTrainingClassViewList();

        /// <summary>
        /// 根据trainingClassMainID获取培训群组数据
        /// </summary>
        /// <param name="trainingClassMainID"></param>
        /// <returns></returns>
        Task<TrainingClassMainInfo> GetListByMainID(string trainingClassMainID);
        /// <summary>
        /// 获取培训群组下拉选项集合
        /// </summary>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetTrainClassOptions();
    }
}