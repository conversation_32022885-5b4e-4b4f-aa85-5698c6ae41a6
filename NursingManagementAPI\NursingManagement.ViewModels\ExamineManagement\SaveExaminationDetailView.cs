﻿using NursingManagement.Models;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 保存考核明细记录视图
    /// </summary>
    public class SaveExaminationDetailView
    {
        /// <summary>
        /// 考核作答记录明细
        /// </summary>
        public List<ExaminationDetailFormValueView> ExaminationDetailList { get; set; }
        /// <summary>
        /// 考核主记录MainID
        /// </summary>
        public string ExaminationMainID { get; set; }
        /// <summary>
        /// 考核主记录状态(来源于SettingDictonary)
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 异动人
        /// </summary>

        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 刷题暂存
        /// </summary>
        public bool? TempSave { get; set; }
        /// <summary>
        /// 刷题练习的进行到的题目ID
        /// </summary>
        public int? CurrentQuestionID { get; set; }
        /// <summary>
        /// 实际考核开始时间
        /// </summary>
        public DateTime StartDateTime { get; set; }
        /// <summary>
        /// 实际考核结束时间
        /// </summary>
        public DateTime? EndDateTime { get; set; }

    }
}
