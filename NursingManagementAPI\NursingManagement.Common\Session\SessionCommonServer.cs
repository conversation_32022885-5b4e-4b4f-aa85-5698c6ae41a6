﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.ViewModels;
using static NursingManagement.Common.WebRequestSugar;

namespace NursingManagement.Common
{
    /// <summary>
    /// ServerSession
    /// </summary>
    public class SessionCommonServer
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IRedisService _redis;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IOptions<SystemConfig> _config;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpContextAccessor"></param>
        /// <param name="redis"></param>
        /// <param name="options"></param>
        public SessionCommonServer(
            IHttpContextAccessor httpContextAccessor
            , IRedisService redis
            , IOptions<SystemConfig> options
            )
        {
            _httpContextAccessor = httpContextAccessor;
            _config = options;
            _redis = redis;
        }

        public Session GetSessionByCache()
        {
            var token = "";
            var session = new Session();
            try
            {
                token = _httpContextAccessor.HttpContext.GetCommonToken();
                if (!string.IsNullOrWhiteSpace(token))
                {
                    token = token.Trim();
                }
            }
            catch (Exception)
            {
                _logger.Warn("没有获取到Token,从本地配置文件获取");
            }

            if (!string.IsNullOrWhiteSpace(token))
            {
                session = GetSession(token);
            }
            //如果没有Token 获取默认的配置
            if (session == null || string.IsNullOrWhiteSpace(token))
            {
                var hospitalID = _config.Value.HospitalID;
                var language = _config.Value.Language;
                //获取登录前时候记录的HospitLID及Language
                //记录当先登录医院的用户信息，在登录前因Token不存在，获取缓存使用,在ServerSession有Set
                try
                {
                    var loginSession = GetSession("UserLoginHospitalLanguage");
                    if (loginSession != null)
                    {
                        if (!string.IsNullOrWhiteSpace(loginSession.HospitalID) && loginSession.HospitalID!="string")
                        {
                            hospitalID = loginSession.HospitalID;
                        }
                        if (loginSession.Language != 0)
                        {
                            language = loginSession.Language;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"GetSession(UserLoginHospitalLanguage)方法报错：{ex}");
                }
                session = new Session()
                {
                    HospitalID = hospitalID,
                    Language = language
                };
            }

            return session;
        }

        public Tuple<string, int> GetParamsByKey(string key)
        {
            var strs = key.Split("_");
            var hospitalID = "";
            int language = 1;
            if (strs.Length >= 3)
            {
                hospitalID = strs[1].Replace("H", "");
                int.TryParse(strs[2].Replace("L", ""), out language);
            }
            return Tuple.Create(hospitalID, language);
        }

        private Session GetSession(string token)
        {
            var session = _redis.GetAsync<Session>(token).Result;
            return session;
            // return _cache.Get<Session>(token);             
        }

        private async Task<Session> GetAsync(string token)
        {
            return await Task.Factory.StartNew(() =>
            {
                //return _cache.TryGetValue<Session>(token,out var session) ? session : null ;
                return new Session();
            });
        }
        /// <summary>
        /// 区分是否为内网客户端环境的请求
        /// </summary>
        /// <returns>内网环境返回true，外网返回false</returns>
        public bool GetServerEnvironment()
        {
            bool isInnerServer = false;
            var cacheSession = GetSessionByCache();
            if (cacheSession != null && cacheSession.ClientType != 1)
            {
                return isInnerServer;
            }
            try
            {
                isInnerServer = _httpContextAccessor.HttpContext.Request?.Headers?.ContainsKey(Constant.IS_INNER_SERVER) ?? false;
            }
            catch (Exception ex)
            {
                _logger.Error("判断医院环境失败", ex);
                return false;
            }
            return isInnerServer;
        }
    }
}