using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 用户登录表
    /// </summary>
    [Serializable]
    [Table("UserLogin")]
    public class UserLoginInfo : MutiModifyInfo
    {

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 人事系统中员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// OA员工账号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string OAUserID { get; set; }

        /// <summary>
        /// OA密码 此处会报错，但此字段没用，屏蔽
        /// </summary>
         public byte[] OAPassword { get; set; }

        /// <summary>
        /// His系统工号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HisUserID { get; set; }

        /// <summary>
        /// his工号密码
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string HisPassword { get; set; }

        /// <summary>
        /// 微信扫码登录的唯一ID
        /// </summary>
        [Column(TypeName = "varchar(28)")]
        public string WechatWebOpenID { get; set; }

        /// <summary>
        /// 微信笑程序唯一ID
        /// </summary>
        [Column(TypeName = "varchar(28)")]
        public string WechatMiniProgramOpenID { get; set; }

        /// <summary>
        /// 微信唯一ID
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string WechatUnionID { get; set; }
        /// <summary>
        /// 密码是否已修改
        /// </summary>
        public bool PasswordChanged { get; set; }
    }
}
