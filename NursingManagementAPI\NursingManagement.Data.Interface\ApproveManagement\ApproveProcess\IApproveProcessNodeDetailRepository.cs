﻿using NursingManagement.Models;
using NursingManagement.ViewModels;


namespace NursingManagement.Data.Interface
{
    public interface IApproveProcessNodeDetailRepository
    {
        /// <summary>
        /// 根据审批流程ID获取审批流程节点明细
        /// </summary>
        /// <param name="processID">流程ID</param>
        /// <returns></returns>
        Task<List<ApproveProcessNodeDetailInfo>> GetInfosByProcessID(string processID);
        /// <summary>
        /// 根据审批流程ID获取审批流程节点明细View
        /// </summary>
        /// <param name="processID">流程ID</param>
        /// <returns></returns>
        Task<List<ApproveProcessNodeDetail>> GetViewsByProcessID(string processID);
        /// <summary>
        /// 根据审批流程节点ID获取当前节点下的明细数据
        /// </summary>
        /// <param name="approveNodeID">审批节点ID</param>
        /// <returns></returns>
        Task<List<ApproveProcessNodeDetailInfo>> GetNodeDetailsAsync(string approveNodeID);
        /// <summary>
        /// 获取审批流程中指定到岗的的岗位集合
        /// </summary>
        /// <param name="processID"></param>
        /// <returns></returns>
        Task<List<string>> GetJobCodesByProcessIDAndNodeTypeAsync(string processID);
    }
}
