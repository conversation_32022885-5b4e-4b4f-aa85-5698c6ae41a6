﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels.EmployeeGroup;

namespace NursingManagement.Services;

public class EmployeeGroupMaintainService(
    IEmployeePersonalDataRepository employeePersonalDataRepository,
    IEmployeeStaffDataRepository employeeStaffDataRepository,
    IEmployeeGroupRepository employeeGroupRepository,
    IDepartmentListRepository departmentListRepository,
    IUnitOfWork unitOfWork
    ) : IEmployeeGroupMaintainService
{
    #region 查询
    public async Task<EmployeeGroupVo[]> GetEmployeeGroups(string employeeID)
    {
        var employeeGroups = await employeeGroupRepository.GetEmployeeGroups();
        if (employeeGroups.Length == 0)
        {
            return [];
        }
        var employeeIDs = employeeGroups.SelectMany(m => new HashSet<string>() { m.ModifyEmployeeID }.Concat(m.EmployeeIDs)).ToHashSet();
        var idAndNameDict = await employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
        var idAndDepartmentDict = await employeeStaffDataRepository.GetEmployeeDepartmentIDs(employeeIDs);
        var departmentList = await departmentListRepository.GetDepartmentIDAndNameByIDs(idAndDepartmentDict.Values);
        var employeeGroupVos = employeeGroups.OrderBy(m => m.ModifyEmployeeID == employeeID ? 0 : 1)
            .Select(m => new EmployeeGroupVo(
                m.EmployeeGroupID,
                m.GroupName,
                GetEmployeeInformation(m.EmployeeIDs, idAndNameDict, idAndDepartmentDict, departmentList),
                idAndNameDict.TryGetValue(m.ModifyEmployeeID, out var modifyEmployeeName) ? modifyEmployeeName : throw new CustomException($"找不到工号：{m.ModifyEmployeeID}的姓名，请联系管理员"),
                m.ModifyDateTime
                )).ToArray();
        return employeeGroupVos;
    }
    private static EmployeeGroupVo.MemberInformation[] GetEmployeeInformation(string[] employeeIDs, Dictionary<string, string> idAndNameDict, Dictionary<string, int?> idAndDepartmentDict, Dictionary<int, string> departmentList)
    {
        var employeesInformation = employeeIDs.Select(employeeID =>
        {
            var employeeName = idAndNameDict.TryGetValue(employeeID, out var name) ? name : throw new CustomException($"找不到工号：{employeeID}的姓名，请联系管理员");
            var departmentName = idAndDepartmentDict.TryGetValue(employeeID, out var depID) && depID.HasValue && departmentList.TryGetValue(depID.Value, out var deptName)
            ? deptName
            : "";
            return new EmployeeGroupVo.MemberInformation(
                employeeID,
                employeeName,
                departmentName
                );
        }).ToArray();
        return employeesInformation;
    }
    #endregion

    #region 命令
    public async Task<int> AddEmployeeGroup(AddEmployeeGroupDto dto, string employeeID)
    {
        if (string.IsNullOrWhiteSpace(dto.GroupName))
        {
            throw new CustomException("用户组名称不能为空");
        }
        if (dto.EmployeeIDs.Length == 0)
        {
            throw new CustomException("用户组成员不能为空");
        }
        var employeeGroup = new EmployeeGroupInfo(dto.GroupName, dto.EmployeeIDs, employeeID);
        await unitOfWork.GetRepository<EmployeeGroupInfo>().InsertAsync(employeeGroup);
        await unitOfWork.SaveChangesAsync();
        return employeeGroup.EmployeeGroupID;
    }

    public async Task<bool> UpdateEmployeeGroup(UpdateEmployeeGroupDto dto, string employeeID)
    {
        if (dto.GroupID == 0)
        {
            throw new CustomException("发生内部错误，请联系管理员");
        }
        if (string.IsNullOrWhiteSpace(dto.GroupName))
        {
            throw new CustomException("用户组名称不能为空");
        }
        if (dto.EmployeeIDs.Length == 0)
        {
            throw new CustomException("用户组成员不能为空");
        }
        var employeeGroup = await employeeGroupRepository.GetEmployeeGroupInfo(dto.GroupID);
        employeeGroup.UpdateEmployeeGroup(dto.GroupName, dto.EmployeeIDs, employeeID);
        return await unitOfWork.SaveChangesAsync() > 0;
    }

    public async Task<bool> DeleteEmployeeGroup(int groupID, string employeeID)
    {
        if (groupID == 0)
        {
            throw new CustomException("发生内部错误，请联系管理员");
        }
        var employeeGroup = await employeeGroupRepository.GetEmployeeGroupInfo(groupID) ?? throw new CustomException("用户组不存在，请刷新后重试");
        employeeGroup.DeleteEmployeeGroup(employeeID);
        return await unitOfWork.SaveChangesAsync() > 0;
    }
    #endregion
}
