﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 人员部门变动申请表
    /// </summary>
    [Serializable]
    [Table("EmployeeDepartmentChangeRequest")]
    public class EmployeeDepartmentChangeRequestInfo : MutiModifyInfo
    {
        /// <summary>
        /// 人员部门变动申请表主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ID { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 新部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 原部门ID
        /// </summary>
        public int OriginalDepartmentID { get; set; }
        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 提交审批记录序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveRecordID { get; set; }
    }
}