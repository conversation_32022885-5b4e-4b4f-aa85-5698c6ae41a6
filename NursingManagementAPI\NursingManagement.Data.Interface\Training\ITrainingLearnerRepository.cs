﻿using NursingManagement.Models;
using System.Linq.Expressions;

namespace NursingManagement.Data.Interface
{
    public interface ITrainingLearnerRepository
    {
        /// <summary>
        /// 根据ID获取培训学员信息
        /// </summary>
        /// <param name="trainingLearnerId">培训学员ID</param>
        /// <returns>培训学员信息</returns>
        Task<TrainingLearnerInfo> GetByIdAsync(string trainingLearnerId);

        /// <summary>
        /// 根据条件获取培训学员列表
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <param name="asNoTrackFlag">是否跟踪实体</param>
        /// <returns>培训学员列表</returns>
        Task<IEnumerable<TrainingLearnerInfo>> GetListByWherePredicateAsync(Expression<Func<TrainingLearnerInfo, bool>> predicate, bool asNoTrackFlag = false);

        /// <summary>
        /// 根据培训记录ID获取培训学员信息
        /// </summary>
        /// <param name="trainingRecordId">培训记录ID</param>
        /// <returns>培训学员信息</returns>
        Task<List<TrainingLearnerInfo>> GetByTrainingRecordIdAsync(string trainingRecordId);

        /// <summary>
        /// 根据员工ID获取培训学员列表
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>培训学员列表</returns>
        Task<List<TrainingLearnerView>> GetByEmployeeIdAsync(string employeeId);
        /// <summary>
        /// 根据培训主记录ID和员工ID获取培训学员信息
        /// </summary>
        /// <param name="trainingRecordId">培训主记录ID</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>培训学员信息</returns>
        Task<TrainingLearnerInfo> GetByRecordIdAndEmployeeId(string trainingRecordId, string employeeId);
        /// <summary>
        /// 根据TrainingRecordIDs获取对应的人员培训记录
        /// </summary>
        /// <param name="trainingRecordIDs">培训记录ID</param>
        /// <returns></returns>
        Task<List<TrainingLearnerView>> GetViewListByTrainingRecordIDsAsync(List<string> trainingRecordIDs);
    }
}
