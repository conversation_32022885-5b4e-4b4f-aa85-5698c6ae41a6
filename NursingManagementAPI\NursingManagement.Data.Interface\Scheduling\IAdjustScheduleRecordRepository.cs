﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IAdjustScheduleRecordRepository
    {
        /// <summary>
        /// 根据主键获取调班记录
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<AdjustScheduleRecordInfo> GetRecordByIDAsync(string id);
        /// <summary>
        /// 根据部门科室获取调班记录
        /// </summary>
        /// <param name="departmentID">部门科室ID</param>
        /// <param name="asNoTrack">是否跟踪数据 | bool</param>
        /// <returns></returns>
        Task<List<AdjustScheduleRecordInfo>> GetListByDepartmentIDAsync(int departmentID, bool asNoTrack);

        /// <summary>
        /// 根据员工ID获取调班记录
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="asNoTrack">是否跟踪数据 | bool</param>
        /// <returns></returns>
        Task<List<AdjustScheduleRecordInfo>> GetListByEmployeeIDAsync(string employeeID, bool asNoTrack);
        /// <summary>
        /// 根据主键集合获取调班记录
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<AdjustScheduleRecordInfo>> GetRecordsByIDsAsNoTrackAsync(List<string> recordIDs);

    }
}
