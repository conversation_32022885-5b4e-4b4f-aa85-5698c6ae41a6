﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 科室职务对照表
    /// </summary>
    [Table("DepartmentToJob")]
    public class DepartmentToJobInfo : MutiModifyInfo
    {
        /// <summary>
        /// 岗位编号，主键，来源EmployeeProfileList
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string JobCode { get; set; }
        /// <summary>
        /// 医院序号，主键
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言序号，主键
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 岗位名称，定义来源EmployeeProfileList，此处做本医院不同说法呈现
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string ShowJobName { get; set; }
        /// <summary>
        /// 护理管理部门编码,OrganizationType=1
        /// </summary>
        public int? DepartmentID { get; set; }
        /// <summary>
        /// HR岗位代码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HRJobCode { get; set; }
        /// <summary>
        /// HR部门编码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HRDepartmentCode { get; set; }
        /// <summary>
        /// HR部门名称
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string HRDepartmentName { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
