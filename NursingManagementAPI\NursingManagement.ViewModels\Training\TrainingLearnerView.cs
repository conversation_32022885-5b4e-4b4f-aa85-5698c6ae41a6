public class TrainingLearnerView
{
    /// <summary>
    /// 人员培训记录ID
    /// </summary>
    public string TrainingLearnerID { get; set; }
    /// <summary>
    /// 培训记录ID
    /// </summary>
    public string TrainingRecordID { get; set; }
    /// <summary>
    /// 培训人员员工编号
    /// </summary>
    public string EmployeeID { get; set; }
    /// <summary>
    /// 人员名称
    /// </summary>
    public string EmployeeName { get; set; }
    /// <summary>
    /// 部门ID
    /// </summary>
    public int DepartmentID { get; set; }
    /// <summary>
    /// 部门名称
    /// </summary>
    public object DepartmentName { get; set; }
    /// <summary>
    /// 最后培训时间
    /// </summary>
    public DateTime? LastTrainingTime { get; set; }

    /// <summary>
    /// 参与培训时长
    /// </summary>
    public decimal? TrainingDuration { get; set; }

    /// <summary>
    /// 培训进度，参与培训时长/总时长*100
    /// </summary>
    public decimal? Progress { get; set; }

    /// <summary>
    /// 培训总学习次数
    /// </summary>
    public int? LearningCount { get; set; }

    /// <summary>
    /// 班长标记
    /// </summary>
    public bool MonitorFlag { get; set; }
    /// <summary>
    /// 学生对老师培训点评建议
    /// </summary>
    public string LearnerEvaluation { get; set; }

    /// <summary>
    /// 老师对学生本次培训点评
    /// </summary>
    public string TeacherEvaluation { get; set; }

    /// <summary>
    /// 学生对老师培训点评建议ID
    /// </summary>
    public string LearnerEvaluationID { get; set; }

    /// <summary>
    /// 老师对学生本次培训点评ID
    /// </summary>
    public string TeacherEvaluationID { get; set; }

    /// <summary>
    /// 培训过程表现（积极、消极等）
    /// </summary>
    public string TrainingComment { get; set; }
    /// <summary>
    /// 护理能级
    /// </summary>
    public string CapabilityLevel { get; set; }
    /// <summary>
    /// 课程满意度（分）
    /// </summary>
    public int? CourseSatisfaction { get; set; }
    /// <summary>
    /// 课程建议
    /// </summary>
    public string CourseRecommendations { get; set; }
    /// <summary>
    /// 新增日期
    /// </summary>
    public DateTime AddDateTime { get; set; }
}