using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 质控内容备注字典表
    /// </summary>
    [Serializable]
    [Table("HierarchicalQCRemark")]
    public class HierarchicalQCRemarkInfo : MutiModifyInfo
    {

        /// <summary>
        /// 质控表单备注序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string HierarchicalQCRemarkID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 质控表单明细内容序号
        /// </summary>
        public int HierarchicalQCAssessListID { get; set; }
        /// <summary>
        /// 备注内容
        /// </summary>
        [Column(TypeName = "varchar(1000)")]
        public string Remark { get; set; }

    }
}
