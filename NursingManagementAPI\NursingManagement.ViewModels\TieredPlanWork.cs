﻿namespace NursingManagement.ViewModels
{
    public class TieredPlanWork
    {
        /// <summary>
        /// 主键
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 参考执行项目ID
        /// </summary>
        public int? APInterventionID { get; set; }

        /// <summary>
        /// 参考执行项目名称
        /// </summary>
        public string APInterventionLocalShowName { get; set; }

        /// <summary>
        /// 工作计划季度
        /// </summary>
        public int WorkQuarter { get; set; }

        /// <summary>
        /// 分类字典ID
        /// </summary>
        public int TypeID { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 季度工作内容
        /// </summary>
        public string WorkContent { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string Requirement { get; set; }

        /// <summary>
        /// 工作类型
        /// </summary>
        public byte WorkType { get; set; }

        /// <summary>
        /// 临时性工作标记
        /// </summary>
        public bool IsTemp { get; set; }

        /// <summary>
        /// 负责人工号
        /// </summary>
        public string[] PrincipalIDs { get; set; }

        /// <summary>
        /// 负责人分组名称
        /// </summary>
        public string PrincipalGroupName { get; set; }

        /// <summary>
        /// 负责人名称
        /// </summary>
        public string PrincipalName { get; set; }
    }
}
