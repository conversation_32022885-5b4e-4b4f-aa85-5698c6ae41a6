using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 排班人员顺序表
    /// </summary>
    [Serializable]
    [Table("ShiftSchedulingEmployeeSort")]
    public class ShiftSchedulingEmployeeSortInfo : MutiModifyInfo
    {

        /// <summary>
        /// 排班人员排序ID
        /// </summary>
        public int ShiftSchedulingEmployeeSortID { get; set; }

        /// <summary>
        /// 排班主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ShiftSchedulingRecordID { get; set; }

        /// <summary>
        /// 排班人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
