﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 提交控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/Condition")]
    [EnableCors("any")]
    public class ConditionController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IConditionService _conditionService;
        private readonly IExaminationConditionRecordService _examinationConditionRecordService;
        /// <summary>
        /// 构造注入
        /// </summary>
        /// <param name="session"></param>
        /// <param name="conditionService"></param>
        /// <param name="examinationConditionRecordService"></param>
        public ConditionController(
            ISessionService session
            , IConditionService conditionService
            , IExaminationConditionRecordService examinationConditionRecordService

            )
        {
            _session = session;
            _conditionService = conditionService;
            _examinationConditionRecordService = examinationConditionRecordService;
        }
        /// <summary>
        /// 获取选择人员条件配置数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeConditionSetting")]
        public async Task<IActionResult> GetEmployeeConditionSetting()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _conditionService.GetEmployeeConditionSetting();
            return result.ToJson();
        }
        /// <summary>
        /// 获取条件配置数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="filterIDs"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetConditionSelectComponent")]
        public async Task<IActionResult> GetConditionSelectComponent(string type,string filterIDs)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var filterIDArr = filterIDs?.Split(",").ToList();
            result.Data = await _conditionService.GetConditionSelectComponent(type, filterIDArr);
            return result.ToJson();
        }
        /// <summary>
        /// 保存条件内容
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveConditionData")]
        public async Task<IActionResult> SaveConditionData([FromBody] HandleConditionView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.ModifyEmployeeID = session.EmployeeID;
            result.Data = await _conditionService.SaveConditionData(view);
            return result.ToJson();
        }
        /// <summary>
        /// 获取规则数据集合
        /// </summary>
        /// <param name="systemType">规则所属系统类型</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRuleListByType")]
        public async Task<IActionResult> GetRuleListByType(string systemType)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _conditionService.GetRuleListByType(systemType);
            return result.ToJson();
        }
        /// <summary>
        /// 删除规则数据
        /// </summary>
        /// <param name="ruleListID">规则主键ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteRule")]
        public async Task<IActionResult> DeleteRule(int ruleListID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _conditionService.DeleteRule(ruleListID,session);
            return result.ToJson();
        }

        /// <summary>
        /// 保存规则及明细数据
        /// </summary>
        /// <param name="ruleView">规则及明细数据</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveRule")]
        public async Task<IActionResult> SaveRule([FromBody]RuleView ruleView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _conditionService.SaveRule(ruleView, session);
            return result.ToJson();
        }
        /// <summary>
        /// 获取题目筛选题型和题型分数的配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetConditionTableFormat")]
        public async Task<IActionResult> GetConditionTableFormat()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _conditionService.GetConditionTableFormat();
            return result.ToJson();
        }
        /// <summary>
        /// 获取所有规则记录
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExaminationConditionRecord")]
        public async Task<IActionResult> GetExaminationConditionRecord()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationConditionRecordService.GetAllRecords();
            return result.ToJson();
        }
        
        /// <summary>
        /// 保存组卷规则记录
        /// </summary>
        /// <param name="examinationConditionSaveParamView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveExaminationConditionRecord")]
        public async Task<IActionResult> SaveExaminationConditionRecord([FromBody] ExaminationConditionSaveParamView examinationConditionSaveParamView)
        {
            var result = new ResponseResult();
            result.Data = examinationConditionSaveParamView;
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationConditionRecordService.SaveExaminationConditionRecord(examinationConditionSaveParamView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除组卷规则记录
        /// </summary>
        /// <param name="examinationConditionRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteExaminationConditionRecord")]
        public async Task<IActionResult> DeleteExaminationConditionRecord(string examinationConditionRecordID )
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationConditionRecordService.DeleteExaminationConditionRecord(examinationConditionRecordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据规则记录ID获取规则明细数据回显
        /// </summary>
        /// <param name="examinationConditionRecordID">规则记录ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetConditionDetailView")]
        public async Task<IActionResult> GetConditionDetailView(string examinationConditionRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationConditionRecordService.GetConditionDetailView(examinationConditionRecordID);
            return result.ToJson();
        }

        /// <summary>
        /// 根据规则记录ID获取规则明细数据回显
        /// </summary>
        /// <param name="examinationConditionRecordID">规则记录ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExaminationConditionEditView")]
        public async Task<IActionResult> GetExaminationConditionEditView(string examinationConditionRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationConditionRecordService.GetExaminationConditionEditView(examinationConditionRecordID);
            return result.ToJson();
        }

    }

}
