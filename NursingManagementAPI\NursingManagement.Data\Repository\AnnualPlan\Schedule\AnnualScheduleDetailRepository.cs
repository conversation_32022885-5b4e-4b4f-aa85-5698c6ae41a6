﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class AnnualScheduleDetailRepository : IAnnualScheduleDetailRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public AnnualScheduleDetailRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 获取年度计划排程明细
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <param name="asNoTrack"></param>
        /// <returns>List<AnnualScheduleDetailInfo></returns>
        public async Task<List<AnnualScheduleDetailInfo>> GetAnnualScheduleDetailsAsync(string scheduleMainID, bool asNoTrack)
        {
            var query = _dbContext.AnnualScheduleDetailInfos.Where(m => m.AnnualScheduleMainID == scheduleMainID && m.DeleteFlag != "*");

            return asNoTrack ? await query.AsNoTracking().ToListAsync() : await query.ToListAsync();
        }

        /// <summary>
        /// 获取年度计划排程明细
        /// </summary>
        /// <param name="scheduleMainID"></param>
        /// <returns></returns>
        public async Task<List<AnnualScheduleDetailView>> GetAnnualScheduleDetailViewsAsync(string scheduleMainID)
        {
            return await _dbContext.AnnualScheduleDetailInfos.AsNoTracking().
                Where(m => m.AnnualScheduleMainID == scheduleMainID && m.DeleteFlag != "*")
                .Select(m=>new AnnualScheduleDetailView { 
                    InterventionID = m.InterventionID,
                    InterventionDetailID = m.InterventionDetailID,
                }).ToListAsync();
        }
    }
}
