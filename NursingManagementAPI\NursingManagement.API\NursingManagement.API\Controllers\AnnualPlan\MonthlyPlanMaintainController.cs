﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 月度计划维护控制器
    /// </summary>
    [Route("api/MonthlyPlanMaintain")]
    [ApiController]
    [EnableCors("any")]
    public class MonthlyPlanMaintainController : ControllerBase
    {
        private readonly ISessionService _session;
        private readonly IMonthlyPlanMaintainService _monthlyPlanMaintainService;

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="session">session</param>
        /// <param name="quarterPlanMaintainService">季度计划业务层</param>
        public MonthlyPlanMaintainController(ISessionService session, IMonthlyPlanMaintainService quarterPlanMaintainService)
        {
            _session = session;
            _monthlyPlanMaintainService = quarterPlanMaintainService;
        }

        #region 月度计划表增删改查
        /// <summary>
        /// 保存月度计划工作内容
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveMonthlyWorks")]
        public async Task<IActionResult> SaveMonthlyWorks(MpWorksSaveView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.Set(session);
            result.Data = await _monthlyPlanMaintainService.SaveMonthlyWorks(view);
            return result.ToJson();
        }

        /// <summary>
        /// 删除工作
        /// </summary>
        /// <param name="monthlyPlanDetailID">主键</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteMonthlyWork")]
        public async Task<IActionResult> DeleteMonthlyWork(string monthlyPlanDetailID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.DeleteMonthlyWork(monthlyPlanDetailID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 更新月度计划工作内容
        /// </summary>
        /// <param name="workView">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateMonthlyWork")]
        public async Task<IActionResult> UpdateMonthlyWork(TieredPlanWork workView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.UpdateMonthlyWork(workView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 发布月度计划
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("PublishMonthlyPlan")]
        public async Task<IActionResult> PublishMonthlyPlan(string monthlyPlanMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.PublishMonthlyPlan(monthlyPlanMainID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取月度计划状态
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetMonthlyPlanStatus")]
        public async Task<IActionResult> GetMonthlyPlanStatus(string monthlyPlanMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.GetMonthlyPlanStatus(monthlyPlanMainID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取月度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetMonthlyPlanMainID")]
        public async Task<IActionResult> GetMonthlyPlanMainID(string annualPlanMainID, int month)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.GetMonthlyPlanMainID(annualPlanMainID, month);
            return result.ToJson();
        }

        /// <summary>
        /// 查询某科室的某月度计划
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetMonthlyWorks")]
        public async Task<IActionResult> GetMonthlyWorks(string annualPlanMainID, string monthlyPlanMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.GetMonthlyWorks(annualPlanMainID, monthlyPlanMainID);
            return result.ToJson();
        }
        #endregion

        #region 参考、导入逻辑
        /// <summary>
        /// 获取可导入的工作
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="annual">年度</param>
        /// <param name="month">月度</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCanImportMpWorksGroupByPlanThenType")]
        public async Task<IActionResult> GetCanImportMpWorksGroupByPlanThenType(string annualPlanMainID, string monthlyPlanMainID, int departmentID, int annual, int month)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.GetCanImportMpWorksGroupByPlanThenType(annualPlanMainID, monthlyPlanMainID, departmentID, annual, month);
            return result.ToJson();
        }
        /// <summary>
        /// 获取上级部门月度计划可选工作
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="annual">年度</param>
        /// <param name="apInterventionID">指定执行项目字典ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUpperDeptMpWorksByPlanThenTypeList")]
        public async Task<IActionResult> GetUpperDeptMpWorksByPlanThenTypeList(int annual, int departmentID, int? apInterventionID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.GetUpperDeptMpWorksByPlanThenTypeList(annual, departmentID, apInterventionID);
            return result.ToJson();
        }
        /// <summary>
        /// 批量导入保存
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveImportWorks")]
        public async Task<IActionResult> SaveImportWorks(MpWorksSaveView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.Set(session);
            result.Data = await _monthlyPlanMaintainService.SaveImportWorks(view);
            return result.ToJson();
        }
        /// <summary>
        /// 查询本人及上下级已制定的月度计划
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBrowseMPViews")]
        public async Task<IActionResult> GetBrowseMPViews(int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _monthlyPlanMaintainService.GetBrowseMPViews(year, session.EmployeeID);
            return result.ToJson();
        }
        #endregion
    }
}
