﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Runtime.CompilerServices;

namespace NursingManagement.Data.Repository
{
    public class EmployeeSkillRepository : IEmployeeSkillRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public EmployeeSkillRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据employeeID获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeSkillInfo>> GetListByEmployeeID(string employeeID)
        {
            return await _dbContext.EmployeeSkillInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").ToListAsync();
        }
    }
}