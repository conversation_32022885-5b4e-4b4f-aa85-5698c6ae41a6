﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Models;
using NursingManagement.Models.DynamicTable;

namespace NursingManagement.Data.Context
{
    /// <summary>
    /// 动态表格相关的配置字典表
    /// </summary>
    public partial class NursingManagementDbContext
    {
        /// <summary>
        /// 动态表格清单（参考配置使用）
        /// </summary>
        public DbSet<DynamicTableListInfo> DynamicTableListInfos { get; set; }
        /// <summary>
        /// 动态表格列配置
        /// </summary>
        public DbSet<DynamicTableSettingInfo> DynamicTableSettingInfos { get; set; }
        /// <summary>
        /// 动态表格字段配置
        /// </summary>
        public DbSet<DynamicTableColumnInfo> DynamicTableColumnInfos { get; set; }
        /// <summary>
        /// 表格列属性配置
        /// </summary>
        public DbSet<DynamicColumnAttributeInfo> DynamicColumnAttributeInfos { get; set; }
        /// <summary>
        /// 表格列属性字典
        /// </summary>
        public DbSet<DynamicColumnAttributeListInfo> DynamicColumnAttributeListInfos { get; set; }
    }
}
