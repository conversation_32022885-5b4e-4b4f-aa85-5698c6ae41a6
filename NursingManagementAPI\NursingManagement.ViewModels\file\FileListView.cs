﻿using Microsoft.AspNetCore.Http;

namespace NursingManagement.ViewModels
{
    public class FileListView
    {
        /// <summary>
        /// 文件关联业务相关ID
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// 文件/图片关所属分组的组ID
        /// </summary>
        public int? GroupID { get; set; }

        /// <summary>
        /// 文件/图片关所属分组的组ID
        /// </summary>
        public int? ParentID { get; set; }

        /// <summary>
        /// 文件集合
        /// </summary>
        public List<IFormFile> Files { get; set; }

        /// <summary>
        /// 已存在的文件ID集合（修改逻辑，没有调整上传的文件，记录以及存储到服务器中的文件ID，便于更新明细的时候不会清除原本的文档信息）
        /// </summary>
        public List<string> FileIDs { get; set; }
    }
}
