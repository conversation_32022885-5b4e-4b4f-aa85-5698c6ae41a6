﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;

namespace NursingManagement.Data.Repository
{
    public class EmployeePersonalDataRepository : IEmployeePersonalDataRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public EmployeePersonalDataRepository(
            NursingManagementDbContext dbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _dbContext = dbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<T> GetFieldValueByEmployeeIDAsync<T>(string employeeID, [DisallowNull] Expression<Func<EmployeePersonalDataInfo, T>> predicate)
        {
            var whereCondition = _dbContext.EmployeePersonalDataInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*");
            if (predicate != null)
            {
                return await whereCondition.Select(predicate).FirstOrDefaultAsync();
            }

            return default;
        }

        /// <summary>
        /// 根据employeID获取数据
        /// </summary>
        /// <param name="employeID"></param>
        /// <returns></returns>
        public async Task<EmployeePersonalDataInfo> GetDataByEmployeeID(string employeID)
        {
            return await _dbContext.EmployeePersonalDataInfos.Where(m => m.EmployeeID == employeID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        public async Task<Dictionary<string, string>> GetDataByEmployeeIDs(IEnumerable<string> employeIDs)
        {
            var datas = await GetCacheAsync() as List<EmployeePersonalDataListView>;
            return datas.Where(m => employeIDs.Contains(m.EmployeeID)).OrderBy(m => m.EmployeeID)
                .ToDictionary(m => m.EmployeeID, m => m.EmployeeName);
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _dbContext.EmployeePersonalDataInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*")
                .Select(m => new EmployeePersonalDataListView
                {
                    EmployeeID = m.EmployeeID,
                    EmployeeName = m.EmployeeName,
                    NamePinyin = m.NamePinyin,
                    GenderCode = m.GenderCode,
                    NationalityCode = m.NationalityCode,
                    NationCode = m.NationCode,
                    IDCardNo = m.IDCardNo,
                    Birthdate = m.Birthdate,
                    LunarBirthdate = m.LunarBirthdate,
                    HomeAddress = m.HomeAddress,
                    ActualAddress = m.ActualAddress,
                    NativePlace = m.NativePlace,
                    MarriageCode = m.MarriageCode,
                    DeliverCode = m.DeliverCode,
                    PolityCode = m.PolityCode
                }).ToListAsync();
                return result;
            });
            return datas;
        }

        public async Task<List<EmployeePersonalDataListView>> GetEmployeePersonalDataView()
        {
            return await GetCacheAsync() as List<EmployeePersonalDataListView>;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeePersonalData.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        /// <summary>
        ///根据医院序号获取数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<EmployeePersonalDataInfo>> GetAllDataByHospital(string hospitalID)
        {
            return await _dbContext.EmployeePersonalDataInfos.Where(m => m.HospitalID == hospitalID).ToListAsync();
        }

        /// <summary>
        /// 获取所有人员（ID 名称）
        /// </summary>
        /// <returns></returns>
        public async Task<List<EmployeePersonalDataListView>> GetIDAndNameData()
        {
            var list = (List<EmployeePersonalDataListView>)await GetCacheAsync();
            return list.Select(m => new EmployeePersonalDataListView
            {
                EmployeeID = m.EmployeeID,
                EmployeeName = m.EmployeeName,
            }).ToList();
        }

        /// <summary>
        /// 根据EmployeeID获取员工基本信息List
        /// </summary>
        /// <param name="employeIDs"></param>
        /// <returns></returns>
        public async Task<List<EmployeePersonalDataListView>> GetListByEmployeeIDs(List<string> employeIDs)
        {
            var list = (List<EmployeePersonalDataListView>)await GetCacheAsync();
            return list.Where(m => employeIDs.Contains(m.EmployeeID)).ToList();
        }

        /// <summary>
        /// 根据名字集合获取数据
        /// </summary>
        /// <param name="employeeNameList"></param>
        /// <returns></returns>
        public async Task<List<EmployeePersonalDataListView>> GetListByNameList(List<string> employeeNameList)
        {
            var list = (List<EmployeePersonalDataListView>)await GetCacheAsync();
            return list.Where(m => employeeNameList.Any(name => name.Contains(m.EmployeeName))).Select(m => new EmployeePersonalDataListView
            {
                EmployeeID = m.EmployeeID,
                EmployeeName = m.EmployeeName,
            }).ToList();
        }

        /// <summary>
        /// 模糊查询人员信息
        /// </summary>
        /// <param name="employeeName">姓名</param>
        public async Task<Dictionary<string, string>> GetEmployeeIDByName(string employeeName)
        {
            var list = (List<EmployeePersonalDataListView>)await GetCacheAsync();
            var employeeDict = list.Where(m => m.EmployeeName.Contains(employeeName) || (!string.IsNullOrEmpty(m.NamePinyin) && m.NamePinyin.StartsWith(employeeName.ToLower())))?.ToDictionary(m => m.EmployeeID, n => n.EmployeeName);
            return employeeDict;
        }

        public async Task<string> GetEmployeeNameByID(string employeeID)
        {
            var list = (List<EmployeePersonalDataListView>)await GetCacheAsync();
            var employee = list.Find(m => m.EmployeeID== employeeID);
            if (employee != null)
            {
                return employee.EmployeeName;
            }
            return await _dbContext.EmployeePersonalDataInfos.Where(m => m.EmployeeID == employeeID && m.DeleteFlag != "*").Select(m => m.EmployeeName).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据ID 集合获取前端人员组件 optons
        /// </summary>
        /// <param name="employeIDs"></param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetEmployOptionViewByEmployeeIDs(List<string> employeIDs)
        {
            var datas = await GetCacheAsync() as List<EmployeePersonalDataListView>;
            return datas.Where(m => employeIDs.Contains(m.EmployeeID)).Select(m => new SelectOptionsView()
            {
                Value = m.EmployeeID,
                Label = m.EmployeeName,
            }).ToList();
        }

        /// <summary>
        /// 获取所有人员（ID 名称）
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetAllEmployees()
        {
            var datas = await GetCacheAsync() as List<EmployeePersonalDataListView>;
            return datas.ToDictionary(m => m.EmployeeID, m => m.EmployeeName);
        }

        /// <summary>
        /// 获取员工ID和姓名
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<string, (string, string)>> GetEmployeeIDAndNames()
        {
            var datas = await GetCacheAsync() as List<EmployeePersonalDataListView>;
            return datas.ToDictionary(m => m.EmployeeID, m => (m.EmployeeName, m.NamePinyin));
        }
    }
}
