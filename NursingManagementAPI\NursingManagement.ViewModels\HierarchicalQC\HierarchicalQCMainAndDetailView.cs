﻿using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.ViewModels
{
    public class HierarchicalQCMainAndDetailView
    {
        /// <summary>
        /// 维护记录
        /// </summary>
        public HierarchicalQCMainTableView QcMain { get; set; }
        /// <summary>
        /// 明细记录
        /// </summary>
        public List<HierarchicalQCDetailView> QcDetails { get; set; }
        /// <summary>
        ///  质控主题ID
        /// </summary>
        public string QcSubjectID { get; set; }
        /// <summary>
        ///  质控时传入的文件集合
        /// </summary>
        public List<FileListView> FileList { get; set; }
    }
}