﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCRecordRepository : IHierarchicalQCRecordRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public HierarchicalQCRecordRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        ///根据主题主记录ID判断该主题是否有指派
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        public async Task<bool> GetExistsReocrdBySubjectID(string subjectID)
        {
            var data = await _dbContext.HierarchicalQCRecordInfos.Where(m => m.HierarchicalQCSubjectID == subjectID && m.DeleteFlag != "*").FirstOrDefaultAsync();
            return data != null;
        }
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<HierarchicalQCRecordInfo> GetDataByRecordID(string recordID)
        {
            return await _dbContext.HierarchicalQCRecordInfos.Where(m => m.HierarchicalQCRecordID == recordID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
        /// <summary>
        /// 根据主题ID获取质控主记录
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectID(string subjectID)
        {
            return await _dbContext.HierarchicalQCRecordInfos.Where(m => m.HierarchicalQCSubjectID == subjectID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectIDAndDepart(string subjectID)
        {
            return await _dbContext.HierarchicalQCRecordInfos.Where(m => m.HierarchicalQCSubjectID == subjectID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据主题ID集合获取质控主记录
        /// </summary>
        /// <param name="subjectIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectIDs(List<string> subjectIDs)
        {
            return await _dbContext.HierarchicalQCRecordInfos.Where(m => subjectIDs.Contains(m.HierarchicalQCSubjectID) && m.DeleteFlag != "*").OrderBy(m => m.VerifierEmployeeID).ThenBy(m => m.AddDateTime).ToListAsync();
        }

        public async Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectIDsAndDepartment(List<string> subjectIDs, int departmentID)
        {
            return await _dbContext.HierarchicalQCRecordInfos.Where(m => subjectIDs.Contains(m.HierarchicalQCSubjectID) && m.DepartmentID == departmentID && m.DeleteFlag != "*").OrderBy(m => m.VerifierEmployeeID).ThenBy(m => m.AddDateTime).ToListAsync();
        }
        /// <summary>
        /// 根据主题ID获取质控主记录科室ID集合
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetQCReocrdDepartmentBySubjectID(string subjectID)
        {
            return await _dbContext.HierarchicalQCRecordInfos.Where(m => m.HierarchicalQCSubjectID == subjectID && m.DeleteFlag != "*").Select(m => m.QCObjectID).ToListAsync();
        }
        /// <summary>
        /// 获取质控数据
        /// </summary>
        /// <param name="subjectIDs"></param>
        /// <param name="hierarchicalQCEmployID"></param>
        /// <param name="switchDeptIDs"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCRecordInfo>> GetRecordData(List<string> subjectIDs, string hierarchicalQCEmployID,List<int> switchDeptIDs)
        {
            var list = _dbContext.HierarchicalQCRecordInfos.Where(m => subjectIDs.Contains(m.HierarchicalQCSubjectID) && m.DeleteFlag != "*");
            if (!string.IsNullOrEmpty(hierarchicalQCEmployID))
            {
                list = list.Where(m => m.HierarchicalQCEmployID.Contains(hierarchicalQCEmployID));
            }
            if (switchDeptIDs != null || switchDeptIDs.Count >0)
            {
                list = list.Where(m => switchDeptIDs.Contains(m.DepartmentID));
            }
            return await list.OrderBy(m => m.VerifierEmployeeID).ThenBy(m => m.AddDateTime).ToListAsync();
        }
        /// <summary>
        /// 根据主题ID获取被质控科室的数量
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        public async Task<int> GetQCReocrdDepartmentCountBySubjectID(string subjectID)
        {
            return await _dbContext.HierarchicalQCRecordInfos.Where(m => m.HierarchicalQCSubjectID == subjectID && m.DeleteFlag != "*").CountAsync();
        }
        /// <summary>
        /// 根据主记录获取对应的质控人
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns>{HierarchicalQCRecordID,AddEmployeeID,QCObjectID}</returns>
        public async Task<List<HierarchicalQCRecordInfo>> GetEmployeeInfoByRecordIDsAsNoTrackAsync(List<string> recordIDs)
        {
            return await _dbContext.HierarchicalQCRecordInfos.AsNoTracking()
                .Where(m => recordIDs.Contains(m.HierarchicalQCRecordID) && m.DeleteFlag != "*")
                .Select(m=>new HierarchicalQCRecordInfo { 
                HierarchicalQCRecordID = m.HierarchicalQCRecordID,
                AddEmployeeID = m.AddEmployeeID,
                QCObjectID = m.QCObjectID,
                }).ToListAsync();
        }
        /// <summary>
        /// 获取访视质控记录数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string,object>>> GetRecordDataByRecordIDs(List<string> recordIDs,string hospitalID)
        {
            return await(from a in _dbContext.HierarchicalQCRecordInfos.Where(m=>recordIDs.Contains(m.HierarchicalQCRecordID)&&m.DeleteFlag!="*")
                         join b in _dbContext.HierarchicalQCMainInfos.Where(m=>m.DeleteFlag!="*") on a.HierarchicalQCRecordID equals b.HierarchicalQCRecordID
                         join c in _dbContext.DepartmentListInfos.Where(m=>m.HospitalID==hospitalID&&m.DeleteFlag!="*") on b.DepartmentID equals c.DepartmentID
                         join d in _dbContext.EmployeePersonalDataInfos.Where(m=>m.HospitalID==hospitalID&&m.DeleteFlag!="*") on b.ModifyEmployeeID equals d.EmployeeID
                         select new Dictionary<string, object>()
                         {
                             {"hierarchicalQCRecordID",a.HierarchicalQCRecordID },
                             {"hierarchicalQCMainID",b.HierarchicalQCMainID },
                             {"result",b.Result },
                             {"guidance",b.Guidance },
                             {"qcDate",b.AssessDate },
                             {"qcDepartmentID",b.DepartmentID },
                             {"qcDepartmentName",c.LocalShowName },
                             {"qcEmployeeID",b.ModifyEmployeeID },
                             {"qcEmployeeName",d.EmployeeName },
                             {"modifyDate",b.ModifyDateTime }
                         }).ToListAsync();
        }
        ///<summary>
        /// 根据时间获取节点式督导考核人
        /// </summary>
        /// <param name="qcLevel"></param>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="formTypes"></param>
        /// <param name="hospitalID"></param>
        /// <param name="switchDeptIDs">switchDeptIDs</param>
        /// <returns></returns>
        public async Task<List<string>> GetQCReocrdHierarchicalQCEmployIDList(string qcLevel,int year,int month,List<string>formTypes,string hospitalID,List<int> switchDeptIDs)
        {
            return  await (from a in _dbContext.HierarchicalQCSubjectInfos
                                  join b in _dbContext.HierarchicalQCRecordInfos on a.HierarchicalQCSubjectID equals b.HierarchicalQCSubjectID
                                  where a.HierarchicalQCFormLevel == qcLevel && a.Year == year && a.Month == month&& formTypes.Contains(a.FormType) && a.HospitalID == hospitalID && a.DeleteFlag != "*" && switchDeptIDs.Contains(b.DepartmentID)
                                  && b.DeleteFlag != "*"
                                  select b.HierarchicalQCEmployID).ToListAsync();
        }

        /// <summary>
        /// 获取质控数据获取质控主记录数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCRecordInfo>> GetQCRecordInfos(GetQCRecordView view)
        {
            var list = _dbContext.HierarchicalQCRecordInfos.Where(m => view.QCSubjectIDs.Contains(m.HierarchicalQCSubjectID) && m.DeleteFlag != "*");
            if (!string.IsNullOrEmpty(view.EmployeeID))
            {
                list = list.Where(m => m.HierarchicalQCEmployID.Contains(view.EmployeeID));
            }
            if (view.QCDepartmentID.HasValue)
            {
                list = list.Where(m => view.QCDepartmentID == m.DepartmentID);
            }
            return await list.OrderBy(m => m.VerifierEmployeeID).ThenBy(m => m.AddDateTime).ToListAsync();
        }
    }
}