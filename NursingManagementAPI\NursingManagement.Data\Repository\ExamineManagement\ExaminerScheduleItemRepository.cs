using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 监考计划监考项目仓储实现类
    /// </summary>
    public class ExaminerScheduleItemRepository : IExaminerScheduleItemRepository
    {
        private readonly NursingManagementDbContext _context;

        public ExaminerScheduleItemRepository(NursingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<List<ExaminerScheduleItemInfo>> GetByExaminerScheduleID(string examinerScheduleID)
        {
            return await _context.ExaminerScheduleItemInfos.Where(x => x.ExaminerScheduleID == examinerScheduleID && x.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<ExaminerScheduleItemInfo>> GetByExaminerScheduleIDs(List<string> examinerScheduleIDs)
        {
            return await _context.ExaminerScheduleItemInfos.Where(x => examinerScheduleIDs.Contains(x.ExaminerScheduleID) && x.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<string>> GetScheduleIDsByExaminationRecordID(string examinationRecordID)
        {
            return await _context.ExaminerScheduleItemInfos.Where(x => x.ExaminationRecordID == examinationRecordID && x.DeleteFlag != "*")
                 .Select(x => x.ExaminerScheduleID).ToListAsync();
        }
    }
}