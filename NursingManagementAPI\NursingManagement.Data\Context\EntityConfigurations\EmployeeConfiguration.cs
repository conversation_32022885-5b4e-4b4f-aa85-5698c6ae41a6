﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NursingManagement.Common;
using NursingManagement.Models;

namespace NursingManagement.Data.Context.EntityConfigurations;

/// <summary>
/// 用户组聚合对应实体配置
/// </summary>
public class EmployeeGroupConfiguration : IEntityTypeConfiguration<EmployeeGroupInfo>
{
    public void Configure(EntityTypeBuilder<EmployeeGroupInfo> builder)
    {
        builder.ToTable("EmployeeGroup");
        builder.HasKey(m => m.EmployeeGroupID);
        builder.Property(m => m.EmployeeGroupID).ValueGeneratedOnAdd();
        builder.Property(m => m.GroupName).HasColumnType("nvarchar(20)").HasMaxLength(20);
        builder.Property(m => m.EmployeeIDs).HasColumnType("varchar(2000)").HasMaxLength(2000);
        builder.Property(m => m.EmployeeIDs).HasConversion(
            v => ListToJson.ToJson(v),
            v => ListToJson.ToList<string[]>(v)
        );
    }
}
