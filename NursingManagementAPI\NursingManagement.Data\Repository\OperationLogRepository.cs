﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Security.AccessControl;

namespace NursingManagement.Data.Repository
{
    public class OperationLogRepository : IOperationLogRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        public OperationLogRepository(
            NursingManagementDbContext db
            , SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<List<OperationLogInfo>> GetOperationLogByEmployeeID(string employeeID)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return await _nursingManagementDbContext.UserOperationLogInfos.Where(m => m.AddEmployeeID == employeeID
                    && m.HospitalID == session.HospitalID && m.Token == session.Token
                    && m.ClientType == session.ClientType).ToListAsync();
        }
    }
}
