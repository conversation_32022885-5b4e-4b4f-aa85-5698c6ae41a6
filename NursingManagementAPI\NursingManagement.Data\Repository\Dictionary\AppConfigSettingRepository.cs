﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class AppConfigSettingRepository : IAppConfigSettingRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public AppConfigSettingRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AppConfigSettingInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AppConfigSetting.GetKey(_sessionCommonServer);
        }

        /// <summary>
        /// 根据settingType获取配置
        /// </summary>
        /// <param name="settingType"></param>
        /// <returns></returns>
        public async Task<List<AppConfigSettingInfo>> GetBySettingType(string settingType)
        {
            var settings = (List<AppConfigSettingInfo>)await GetCacheAsync();
            return settings.Where(m => m.SettingType == settingType).ToList();
        }

        public async Task<AppConfigSettingInfo> GetConfigSetting(string settingType, string settingCode)
        {
            var settings = (List<AppConfigSettingInfo>)await GetCacheAsync();
            if (settings.Count == 0)
            {
                return null;
            }

            return settings.Find(m => m.SettingType == settingType && m.SettingCode == settingCode);
        }

        public async Task<string> GetConfigSettingValue(string settingType, string settingCode)
        {
            var setting = await GetConfigSetting(settingType, settingCode);
            if (setting == null)
            {
                return "";
            }
            return setting.SettingValue;
        }

    }
}
