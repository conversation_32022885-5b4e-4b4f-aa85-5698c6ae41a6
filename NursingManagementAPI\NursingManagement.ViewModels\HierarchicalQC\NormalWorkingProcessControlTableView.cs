﻿namespace NursingManagement.ViewModels.HierarchicalQC
{
    public class NormalWorkingProcessControlTableView
    {
        /// <summary>
        /// 质控维护记录ID
        /// </summary>
        public string HierarchicalQCMainID { get; set; }
        /// <summary>
        /// 质控主记录ID
        /// </summary>
        public string HierarchicalQCRecordID { get; set; }
        /// <summary>
        /// 质控主记录ID
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }
        /// <summary>
        /// 考核次数
        /// </summary>
        public string Number { get; set; }
        /// <summary>
        /// 考核日期
        /// </summary>
        public DateTime ExamineDate { get; set; }
        /// <summary>
        /// 考核人
        /// </summary>
        public string ExamineEmployee { get; set; }
        /// <summary>
        /// 考核人
        /// </summary>
        public string ExamineEmployeeID { get; set; }
        /// <summary>
        /// 被考核人
        /// </summary>
        public List<string> QcEmployeeID { get; set; }
        /// <summary>
        /// 被考核人
        /// </summary>
        public string QcEmployeeName { get; set; }
        /// <summary>
        /// 质控分数
        /// </summary>
        public decimal? Point { get; set; }
        /// <summary>
        /// 是否已阅读
        /// </summary>
        public string Reader { get; set; }
        /// <summary>
        /// 是否已阅读
        /// </summary>
        public bool? IsReadFlag { get; set; }
        /// <summary>
        /// 申诉状态
        /// </summary>
        public string SubmitStatus { get; set; }
        /// <summary>
        /// 被质控病区
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 质控指导意见
        /// </summary>
        public string Guidance { get; set; }
        /// <summary>
        /// 科室改进内容
        /// </summary>
        public string Improvement { get; set; }
        /// <summary>
        /// 未满分项
        /// </summary>
        public List<NormalWorkingDetail> Details { get; set; }
        /// <summary>
        /// 考核主题名称
        /// </summary>
        public string SubjectName { get; set; }
        /// <summary>
        /// 考核模板Code
        /// </summary>
        public string TemplateCode { get; set; }
        /// <summary>
        /// 审核状态：0、待提交||审批未通过（当AuditDateTime为空的时候），1、待审批，2、审批通过
        /// </summary>
        public string AuditStatus { get; set; }
        /// <summary>
        /// 分类说明
        /// </summary>
        public string Contents { get; set; }
        /// <summary>
        /// 问题整改数据
        /// </summary>
        public ProblemRectificationView ProblemRectificationView { get; set; }
        /// <summary>
        /// 审批标记
        /// </summary>
        public bool ApproveFlag { get; set; }
    }
    public class NormalWorkingDetail
    {
        /// <summary>
        /// 维护记录主键
        /// </summary>
        public string HierarchicalQCMainID { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// ID
        /// </summary>
        public int HierarchicalQCDetailID { get; set; }
    }
}
