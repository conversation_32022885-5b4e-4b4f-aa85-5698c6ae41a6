﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 员工带教关系表
    /// </summary>
    [Serializable]
    [Table("EmployeeTeachingRelation")]
    public class EmployeeTeachingRelationInfo : MutiModifyInfo
    {
        /// <summary>
        /// 员工带教关系唯一ID
        /// </summary>
        [Key]
        [Column(TypeName = "int")]
        public int EmployeeTeachingRelationID { get; set; }
        /// <summary>
        /// 员工带教关系中被带教员工ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 员工带教关系中带教员工ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string TeacherEmployeeID { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string Remark { get; set; }

    }
}
