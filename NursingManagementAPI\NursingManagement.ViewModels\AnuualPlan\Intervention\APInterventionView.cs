﻿namespace NursingManagement.ViewModels
{
    public class APInterventionView
    {
        /// <summary>
        /// 执行项目主表ID
        /// </summary>
        public string APInterventionMainID { get; set; }
        /// <summary>
        /// 执行项目明细ID
        /// </summary>
        public string APInterventionDetailID { get; set; }
        /// <summary>
        /// 执行项目负责人ID
        /// </summary>
        public int APInterventionPrincipalID { get; set; }
        /// <summary>
        /// 执行项目字典ID
        /// </summary>
        public int InterventionID { get; set; }
        /// <summary>
        /// 自定义执行项目名称
        /// </summary>
        public string LocalShowName { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int PlanMonth { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime? PlanDate { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public string PrincipalID { get; set; }
    }
}
