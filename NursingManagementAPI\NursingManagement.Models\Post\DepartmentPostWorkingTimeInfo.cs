using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 部门岗位工作时间表
    /// </summary>
    [Serializable]
    [Table("DepartmentPostWorkingTime")]
    public class DepartmentPostWorkingTimeInfo : MutiModifyInfo
    {
        /// <summary>
        /// 部门岗位工作时间表记录ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int DepartmentPostWorkingTimeID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门岗位序号
        /// </summary>
        public int DepartmentPostID { get; set; }
        /// <summary>
        /// 季节，winter冬季，summer夏季
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Season { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public TimeSpan StartTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public TimeSpan EndTime { get; set; }
        /// <summary>
        /// 是否跨天
        /// </summary>
        public bool CrossDayFlag { get; set; }
        
    }
}
