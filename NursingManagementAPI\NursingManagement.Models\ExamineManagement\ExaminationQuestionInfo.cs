using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 继承自 MutiModifyInfo 的考核题目表实体类
    /// </summary>
    [Table("ExaminationQuestion")]
    public class ExaminationQuestionInfo : MutiModifyInfo
    {
        /// <summary>
        /// 题目ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ExaminationQuestionID { get; set; } 
        /// <summary>
        /// 考核题目名字
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string QuestionContent { get; set; }
        /// <summary>
        /// 题目类型，来源SettingDictionary（判断题、多选题、单选题）
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ExaminationQuestionType { get; set; }
        /// <summary>
        /// 题目明细呈现方式（用来确认试题呈现方式，如单选组件），来源ComponentList
        /// </summary>
        public int ComponentListID { get; set; }
        /// <summary>
        /// 难度等级，来源SettingDictionary
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string DifficultyLevel { get; set; }
        /// <summary>
        /// 题目标签，来源SettingDictionary
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string QuestionTag { get; set; } 
        /// <summary>
        /// 题目说明
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Instructions { get; set; } 
        /// <summary>
        /// 题目权重(随机组题使用)
        /// </summary>
        public decimal FilterWeight { get; set; } 
        /// <summary>
        /// 解析
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string Analysis { get; set; }
        /// <summary>
        /// 考核题库ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string QuestionBankID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 分数
        /// </summary>
        public int? Score { get; set; }
        /// <summary>
        /// 题目明细 -导航
        /// </summary>
        public virtual ICollection<ExaminationQuestionDetailInfo> ExaminationQuestionDetails { get; set; } = [];
    }
}
