﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class FormExtend
    {
        /// <summary>
        /// 根据项目label获取项目ID
        /// </summary>
        /// <param name="label"></param>
        /// <returns></returns>
        public virtual Task<Tuple<string, Func<Task>>> GetItemIDByLabel(string label)
        {
            return Task.Run(() => Tuple.Create("", null as Func<Task>));
        }
        /// <summary>
        /// 获取评分组件备注选项集合
        /// </summary>
        /// <param name="itemIDs"></param>
        /// <returns></returns>
        public virtual Task<List<KeyValueString>> GetGradeRemarkOptions(List<string> itemIDs)
        {
            return Task.Run(() => new List<KeyValueString>());
        }
    }
}
