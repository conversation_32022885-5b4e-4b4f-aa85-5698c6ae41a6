﻿namespace NursingManagement.ViewModels
{
    public class AnnualIndicatorListView
    {
        /// <summary>
        /// 指标字典ID
        /// </summary>
        public int AnnualIndicatorID { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 指标名称
        /// </summary>
        public string IndicatorContent { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 启用年份
        /// </summary>
        public int EnableYear { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 异动时的人员ID
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 本级(current)、上级(upper)
        /// </summary>
        public string Level { get; set; }
        /// <summary>
        /// 是否是今年新指标
        /// </summary>
        public bool IsNewIndicator { get; set; }
    }
}
