﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IConditionService
    {
        /// <summary>
        /// 获取选择人员条件配置数据
        /// </summary>
        /// <returns></returns>
        Task<ConditionCommonView> GetEmployeeConditionSetting();
        /// <summary>
        /// 保存条件主数据和条件明细数据
        /// </summary>
        /// <param name="view"></param>
        /// <param name="saveChange">默认在方法中保存</param>
        /// <returns></returns>
        Task<string> HandleConditionData(HandleConditionView view, bool saveChange = true);
        /// <summary>
        /// 处理选择的人员条件数据
        /// </summary>
        /// <param name="conditionDetailList"></param>
        /// <param name="emloyeeList"></param>
        /// <param name="allConditionDetailList"></param>
        /// <returns></returns>
        Task<List<EmployeeStaffDataInfo>> FilterConditionDetailList(List<ConditionDetailInfo> conditionDetailList, List<EmployeeStaffDataInfo> emloyeeList, List<ConditionDetailInfo> allConditionDetailList);
        /// <summary>
        /// 获取条件配置数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="filterIDs"></param>
        /// <returns></returns>
        Task<ConditionCommonView> GetConditionSelectComponent(string type, List<string> filterIDs);
        /// <summary>
        /// 保存条件数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveConditionData(HandleConditionView view);
        /// <summary>
        /// 获取规则属性
        /// </summary>
        /// <param name="systemType">规则使用系统类型</param>
        /// <returns></returns>
        Task<List<RuleView>> GetRuleListByType(string systemType);
        /// <summary>
        /// 删除规则数据
        /// </summary>
        /// <param name="ruleListID">规则主记录ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> DeleteRule(int ruleListID, Session session);
        /// <summary>
        /// 保存规则及明细数据
        /// </summary>
        /// <param name="ruleView">规则及明细数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        Task<bool> SaveRule(RuleView ruleView, Session session);
        /// <summary>
        /// 根据来源ID删除条件
        /// </summary>
        /// <param name="sourceID">来源ID</param>
        /// <param name="sourceType">来源类别</param>
        /// <param name="userID">工号</param>
        /// <returns></returns>
        Task<bool> DeleteConditionMainAndDetails(string sourceID, string sourceType, string userID);
        /// <summary>
        /// 获取条件筛选题目类型和分数时使用的 表格呈现格式数据
        /// </summary>
        /// <returns></returns>
        Task<object> GetConditionTableFormat();
    }
}
