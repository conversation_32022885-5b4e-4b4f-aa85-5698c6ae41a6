﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 分层计划上下文
    /// </summary>
    public class APMainView
    {
        /// <summary>
        /// 	年度计划主键Guid	
        /// </summary>
        public string MainID { get; set; }
        /// <summary>
        /// 计划名称
        /// </summary>
        public string PlanName { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 是否是主管科室
        /// </summary>
        public bool IsMainDepartment { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 制定人ID
        /// </summary>
        public string Planner { get; set; }
        /// <summary>
        /// 制定人姓名
        /// </summary>
        public string PlannerName { get; set; }
        /// <summary>
        /// 制订时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<FileView> Attachments { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 季度对应季度主键集合
        /// </summary>
        public Dictionary<int, string> QuarterToIDs { get; set; }
        /// <summary>
        /// 月份对应月度主键集合
        /// </summary>
        public Dictionary<int, string> MonthToIDs { get; set; }
        /// <summary>
        /// 季度主键
        /// </summary>
        public string QuarterPlanMainID { get; set; }
        /// <summary>
        /// 季度
        /// </summary>
        public int Quarter { get; set; }
        /// <summary>
        /// 月度计划主键
        /// </summary>
        public string MonthlyPlanMainID { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }
    }
}
