﻿
using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DynamicFormRecordRepository : IDynamicFormRecordRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public DynamicFormRecordRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }


        public async Task<DynamicFormRecordInfo> GetFormByFormRecordID(string dynamicFormRecordID)
        {
            var datas = await GetCacheAsync() as List<DynamicFormRecordInfo>;
            return datas.Where(m => m.DynamicFormRecordID == dynamicFormRecordID).FirstOrDefault();
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, _) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DynamicFormRecordInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.DynamicFormRecord.GetKey(_sessionCommonServer);
        }
        /// <summary>
        /// 根据动态表单ID获取数据（列表）
        /// </summary>
        /// <param name="dynamicFormRecordIDs">动态表单ID集合</param>
        /// <returns></returns>
        public async Task<List<DynamicFormRecordInfo>> GetFormByFormRecordIDs(List<string> dynamicFormRecordIDs)
        {
            var datas = await GetCacheAsync() as List<DynamicFormRecordInfo>;
            return datas.Where(m => dynamicFormRecordIDs.Any(n => n == m.DynamicFormRecordID)).ToList();
        }
    }
}
