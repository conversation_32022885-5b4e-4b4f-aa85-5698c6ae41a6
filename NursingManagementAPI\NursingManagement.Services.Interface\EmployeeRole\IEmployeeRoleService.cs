﻿using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface IEmployeeRoleService
    {
        /// <summary>
        /// 根据科室ID获取对应的员工角色列表
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<List<EmployeeRoleView>> GetEmployeeRoleListAsync(int? departmentID);
        /// <summary>
        /// 保存人员角色
        /// </summary>
        /// <param name="employeeRoleView">前端传递参数</param>
        /// <param name="userID">用户工号</param>
        /// <param name="hospitalID">医院类别码</param>
        /// <returns></returns>
        Task<bool> SaveEmployeeRoleAsync(EmployeeRoleView employeeRoleView, string userID, string hospitalID);
        /// <summary>
        /// 删除人员角色
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        Task<bool> DeleteEmployeeRoleAsync(string employeeID, string userID);
        /// <summary>
        /// 获取片区权限
        /// </summary>
        /// <param name="employeeStrengthID"></param>
        /// <returns></returns>
        Task<bool> GetEmployeeRoleAsync(string employeeID);
    }
}
