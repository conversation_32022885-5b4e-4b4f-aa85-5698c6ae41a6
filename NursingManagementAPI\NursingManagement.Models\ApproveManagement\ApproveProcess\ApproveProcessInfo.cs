﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 审批流程设置表
    /// </summary>
    [Serializable]
    [Table("ApproveProcess")]
    public class ApproveProcessInfo : MutiModifyInfo
    {
        /// <summary>
        /// 审批流程唯一码，Guid
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ApproveProcessID { get; set; }
        /// <summary>
        /// 审批流程名称
        /// </summary>
        [Column(TypeName = "nvarchar(50)")]
        public string ProcessName { get; set; }
        /// <summary>
        /// 审批流程描述
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string ProcessDescription { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 流程当前状态
        /// </summary>
        public ApproveProcessStatusCode StatusCode { get; set; }
        /// <summary>
        /// 分类码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ProveCategory { get; set; }
        /// <summary>
        /// 申请内容模板
        /// </summary>
        [Column(TypeName = "nvarchar(500)")]
        public string ContentTemplate { get; set; }
    }
    public enum ApproveProcessStatusCode
    {
        /// <summary>
        /// 未启用
        /// </summary>
        UnEnable = 0,
        /// <summary>
        /// 启用
        /// </summary>
        Enable = 1,
        /// <summary>
        /// 停用
        /// </summary>
        Disable = 2,
    }
}
