﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NursingManagement.API.Extensions;
using NursingManagement.Data.Context;
using NursingManagement.ViewModels;

namespace NursingManagement.UnitTest
{
    public class Startup
    {
        public static void ConfigureHost(IHostBuilder hostBuilder) =>
            hostBuilder
            .UseServiceProviderFactory(new AutofacServiceProviderFactory())
            .ConfigureContainer<ContainerBuilder>((context, containerBuilder) =>
            {
                containerBuilder.RegisterModule<AutofacModuleRegister>();
            })
            .ConfigureHostConfiguration(builder =>
            {
                builder.AddConfiguration(
                    new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile(@"./appsettings.json")
                    .Build());
            });

        public void ConfigureServices(IServiceCollection services, HostBuilderContext context)
        {
            //配置数据库连接 UseMySql使用MySQL  UseSqlServer使用SQL Server
            services.AddDbContext<NursingManagementDbContext>(options => options.UseSqlServer(context.Configuration.GetConnectionString("Connection")));
            services.AddScoped<NursingManagementDbContext>();
            services.AddUnitOfWork<NursingManagementDbContext>();
            //消息数据库
            services.AddDbContext<MessageDBContext>(options => options.UseSqlServer(context.Configuration.GetConnectionString("MessageConnection")));
            services.AddScoped<MessageDBContext>();
            //连接Redis数据库
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = context.Configuration.GetConnectionString("RedisConnection");
                options.InstanceName = "key_";
            });
            services.AddHttpContextAccessor();
            services.Configure<SystemConfig>(context.Configuration.GetSection("Configs"));
            //增加服务端缓存
            services.AddMemoryCache();
        }

    }
}