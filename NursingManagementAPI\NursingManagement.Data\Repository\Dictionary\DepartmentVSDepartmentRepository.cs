﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class DepartmentVSDepartmentRepository : IDepartmentVSDepartmentRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public DepartmentVSDepartmentRepository(NursingManagementDbContext db,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _nursingManagementDbContext = db;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.DepartmentVSDepartmentInfos.Where(m =>m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.DepartmentVSDepartment.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        /// <summary>
        /// 获取所有缓存
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentVSDepartmentInfo>> GetByCacheAsync()
        {
            var caches = await GetCacheAsync() as List<DepartmentVSDepartmentInfo>;
            return caches;
        }
        
        /// <summary>
        /// 根据组织架构类型获取部门配置
        /// </summary>
        /// <param name="organizationType">组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门</param>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        public async Task<List<DepartmentVSDepartmentInfo>> GetByOrganizationType(string organizationType, int departmentID)
        {
            var list = (List<DepartmentVSDepartmentInfo>)await GetCacheAsync();
            return list.Where(m => m.OrganizationType2 == organizationType && m.DepartmentID2 == departmentID).ToList();
        }
        /// <summary>
        /// 根据双组织架构类型获取部门配置
        /// </summary>
        /// <param name="organizationType">组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门</param>
        /// <param name="departmentID"></param>
        /// <param name="organizationType2">组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门</param>
        /// <returns></returns>
        public async Task<List<DepartmentVSDepartmentInfo>> GetByOrganizationTypeAndDepartmentID(string organizationType, int departmentID, string organizationType2)
        {
            var list = (List<DepartmentVSDepartmentInfo>)await GetCacheAsync();
            return list.Where(m => m.OrganizationType1 == organizationType &&m.OrganizationType2 == organizationType2&& m.DepartmentID1 == departmentID).ToList();
        }
    }
}
