﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Services;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModel;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.file;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 岗位控制器
    /// </summary>
    /// <remarks>
    /// 岗位控制器
    /// </remarks>
    [Produces("application/json")]
    [Route("api/HierarchicalQC")]
    [EnableCors("any")]
    public class HierarchicalQCController(
          ISessionService session
            , IHierarchicalQCFormRepository hierarchicalQCFormRepository
            , IHierarchicalQCService hierarchicalQCService
            , ISettingDictionaryService settingDictionaryService
            , ICriticalPatientVisitsService criticalPatientVisitsService
            , IDeparmentToDataTypeRepository deparmentToDataTypeRepository
            , ISensitiveIndicatorSupervisionService sensitiveIndicatorSupervisionService
            ) : Controller
    {
        private readonly ISessionService _session = session;

        /// <summary>
        /// 获取质控表单清单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHierarchicalQCFormList")]
        public async Task<IActionResult> GetHierarchicalQCFormList()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetHierarchicalQCFormList();
            return result.ToJson();
        }
        /// <summary>
        /// 获取质控考核结果主记录
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHierarchicalQCRecordList")]
        public async Task<IActionResult> GetHierarchicalQCRecordList([FromQuery] GetQCRecordView searchView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            searchView.HospitalID ??= session.HospitalID;
            result.Data = await hierarchicalQCService.GetHierarchicalQCRecordList(searchView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除质控考核结果主记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteHierarchicalQCRecord")]
        public async Task<IActionResult> DeleteHierarchicalQCRecord(string recordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.DeleteHierarchicalQCRecord(recordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据主记录ID获取质控考核结果维护记录
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHierarchicalQCMainList")]
        public async Task<IActionResult> GetHierarchicalQCMainList(string recordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetHierarchicalQCMainList(recordID, session.HospitalID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除质控维护考核记录
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteHierarchicalQCMain")]
        public async Task<IActionResult> DeleteHierarchicalQCMain(string mainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.DeleteHierarchicalQCMain(mainID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据质控等级获取质控字典
        /// </summary>
        /// <param name="level"></param>
        /// <param name="formType"></param>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetQCFormByLevel")]
        public async Task<IActionResult> GetQCFormByLevel(string level, string formType, List<int> departmentIDs)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCFormRepository.GetQCFormByLevel(level, formType, departmentIDs);
            return result.ToJson();
        }
        /// <summary>
        /// 获取质控在主题表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSubjectTableView")]
        public async Task<IActionResult> GetSubjectTableView(GetQCSubjectView searchView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            searchView.HospitalID ??= session.HospitalID;
            result.Data = await hierarchicalQCService.GetSubjectTableView(searchView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 质控主题新增修改
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SubjectPlanSave")]
        public async Task<IActionResult> SubjectPlanSave([FromBody] SubjectView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            saveView.HospitalID = session.HospitalID;
            saveView.Language = session.Language;
            saveView.EmployeeID = session.EmployeeID;
            saveView.DepartmentID = saveView.DepartmentID == 0 ? session.DepartmentID : saveView.DepartmentID;
            //主题复制
            if (saveView.CopyFlag)
            {
                result.Data = await hierarchicalQCService.CopySubjectPlan(saveView);
            }
            //主题新增保存
            else
            {
                result.Data = await hierarchicalQCService.SubjectPlanSave(saveView);
            }
            return result.ToJson();
        }
        /// <summary>
        /// 获取被质控科室
        /// </summary>
        /// <param name="subjectID"></param>
        /// <param name="formLevel"></param>
        /// <param name="organizationType"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAssignDepartmentList")]
        public async Task<IActionResult> GetAssignDepartmentList(string subjectID, string formLevel, string organizationType = "1")
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetQCAssignDepartmentList(subjectID, session.EmployeeID, formLevel, organizationType);
            return result.ToJson();
        }
        /// <summary>
        /// 获取质控人员及审核人员
        /// </summary>        
        /// <param name="formLevel"></param>
        [HttpGet]
        [Route("GetAssignEmployeeList")]
        public async Task<IActionResult> GetAssignEmployeeList(string formLevel)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetQCAssignEmployeeList(formLevel, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 质控指派保存
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveSubjectAssign")]
        public async Task<IActionResult> SaveSubjectAssign([FromBody] SubjectAssignView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.SaveSubjectAssign(view, session.EmployeeID, session.HospitalID);
            return result.ToJson();
        }
        /// <summary>
        ///回显指派页面数据
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSubjectAssignView")]
        public async Task<IActionResult> GetSubjectAssignView(string subjectID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetSubjectAssignView(subjectID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除主题
        /// </summary>
        /// <param name="hierarchicalQCSubjectID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteSubject")]
        public async Task<IActionResult> DeleteSubject(string hierarchicalQCSubjectID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.DeleteSubject(hierarchicalQCSubjectID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据AssessListID获取对应的备注内容
        /// </summary>
        /// <param name="assessListID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHierarchicalQCRemark")]
        public async Task<IActionResult> GeHierarchicalQCRemark(int assessListID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GeHierarchicalQCRemarkAsync(assessListID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存备注内容
        /// </summary>
        /// <param name="remarkView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveHierarchicalQCRemark")]
        public async Task<IActionResult> SaveHierarchicalQCRemark([FromBody] HierarchicalQCRemarkView remarkView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.SaveHierarchicalQCRemarkAsync(remarkView, session.HospitalID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据质控维护记录获取对应的明细内容信息
        /// </summary>
        /// <param name="careMainID"></param>
        /// <param name="trackFlag">跟踪标记为true 满分禁止勾选</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHierarchicalQCDetails")]
        public async Task<IActionResult> GetHierarchicalQCDetails(string careMainID, bool? trackFlag)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetHierarchicalQCDetailsAsync(careMainID, trackFlag);
            return result.ToJson();
        }
        /// <summary>
        /// 保存质控结果内容
        /// </summary>
        /// <param name="mainAndDetailView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveHierarchicalQCMainAndDetails")]
        public async Task<IActionResult> SaveHierarchicalQCMainAndDetails([FromForm] HierarchicalQCMainAndDetailView mainAndDetailView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.SaveHierarchicalQCMainAndDetailsAsync(mainAndDetailView, session.HospitalID, session.EmployeeID,"");
            return result.ToJson();
        }
        /// <summary>
        /// 获取追踪考核表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTrackTableData")]
        public async Task<IActionResult> GetTrackTableData([FromQuery] GetQCRecordView searchView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            searchView.HospitalID ??= session.HospitalID;
            result.Data = await hierarchicalQCService.GetTrackTableView(searchView, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取 质控表单类型
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <param name="qcType"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQCFormType")]
        public async Task<IActionResult> GetQCFormType(string settingType, string settingTypeCode, QCTypeEnum? qcType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await settingDictionaryService.GetQCFormType(settingType, settingTypeCode, qcType);
            return result.ToJson();
        }
        /// <summary>
        /// 根据条件获取主题下拉框数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQCSubjectOption")]
        public async Task<IActionResult> GetQCSubjectOption([FromQuery] QCSubjectSearchView searchView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetSubjectOptions(searchView);
            return result.ToJson();
        }
        /// <summary>
        /// 获取人员质控表格数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNormalWorkingTableData")]
        public async Task<IActionResult> GetNormalWorkingTableData([FromQuery] GetQCSubjectView searchView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            searchView.HospitalID??=session.HospitalID;
            result.Data = await hierarchicalQCService.GetNormalWorkingTableData(searchView);
            return result.ToJson();
        }
        /// <summary>
        /// 停止质控审批
        /// </summary>
        /// <param name="hierarchicalMainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("StopHierarchicalQCApproval")]
        public async Task<IActionResult> StopHierarchicalQCApproval(string hierarchicalMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.StopHierarchicalQCApprovalAsync(hierarchicalMainID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 提交审批
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SubmitForApproval")]
        public async Task<IActionResult> SubmitForApproval([FromBody] ApproveMainAndDetailParamView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.SubmitForApprovalAsync(view);
            return result.ToJson();
        }
        /// <summary>
        /// 获取质控字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQCAssessList")]
        public async Task<IActionResult> GetQCAssessList()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetQCAssessList();
            return result.ToJson();
        }
        /// <summary>
        /// 保存质控模板
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveQCForm")]
        public async Task<IActionResult> SaveQCForm([FromBody] HierachicalQCFormView hierachicalQCFormView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.SaveQCForm(hierachicalQCFormView, session.EmployeeID, session.HospitalID, session.Language);
            return result.ToJson();
        }
        /// <summary>
        /// 删除质控模板
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteQCForm")]
        public async Task<IActionResult> DeleteQCForm(int hierarchicalQCFormID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.DeleteQCForm(hierarchicalQCFormID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存质控主题的质控模板
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveQCSubjectForm")]
        public async Task<IActionResult> SaveQCSubjectForm([FromBody] SubjectFormView subjectFormView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.SaveQCSubjectForm(subjectFormView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取三级质控评估模板数据
        /// </summary>
        /// <param name="careMainID"></param>
        /// <param name="templateCode"></param>
        /// <param name="trackFlag"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQCAssessView")]
        public async Task<Object> GetQCAssessView(string careMainID, string templateCode, bool? trackFlag)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetAssessContentView(careMainID, templateCode, trackFlag);
            return result.ToJson();
        }
        /// <summary>
        /// 获取危重患者访视主记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetCriticalPatientVisitsRecord")]
        public async Task<IActionResult> GetCriticalPatientVisitsRecord([FromBody] GetPatientProfileRecordView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.HospitalID = session.HospitalID;
            result.Data = await criticalPatientVisitsService.GetCriticalPatientVisitsRecord(view);
            return result.ToJson();
        }
        /// <summary>
        /// 获取访视质控内容
        /// </summary>
        /// <param name="profileID"></param>
        /// <param name="qcMainID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetVisitsQcAssessView")]
        public async Task<Object> GetVisitsQcAssessView(string profileID, string qcMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await criticalPatientVisitsService.GetVisitsQcAssessView(profileID, qcMainID);
            return result.ToJson();
        }
        /// <summary>
        /// 访视记录保存
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveVisitsRecord")]
        public async Task<IActionResult> SaveVisitsRecord([FromBody] VisitsRecordSaveView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.HospitalID = session.HospitalID;
            view.Language = session.Language;
            view.EmployID = session.EmployeeID;
            result.Data = await criticalPatientVisitsService.SaveVisitsRecord(view);
            return result.ToJson();
        }
        /// <summary>
        /// 获取访视质控记录
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <param name="profileID"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetVisitsQcRecord")]
        public async Task<IActionResult> GetVisitsQcRecord(string sourceID, string sourceType, string profileID, string templateCode)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            };
            result.Data = await criticalPatientVisitsService.GetVisitsQcRecord(sourceID, sourceType, profileID, templateCode, session.HospitalID);
            return result.ToJson();
        }
        /// <summary>
        /// 节点式督导获取质控人员
        /// </summary>
        /// <param name="qcLevel"></param>
        /// <param name="yearMonth"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQCEmployeeOptions")]
        public async Task<Object> GetQCEmployeeOptions(string qcLevel, string yearMonth)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetQCEmployeeOptions(qcLevel, yearMonth, session.HospitalID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除所有访视记录
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceID"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteAllVisitsRecord")]
        public async Task<Object> DeleteAllVisitsRecord(string sourceType, string sourceID, string relatedTableName)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await criticalPatientVisitsService.DeleteAllVisitsRecord(sourceType, sourceID, relatedTableName, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 根据访视记录主键集合删除访视记录
        /// </summary>
        /// <param name="patientProfileRecordIDs"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteVisitsRecord")]
        public async Task<Object> DeleteVisitsRecord(List<string> patientProfileRecordIDs)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await criticalPatientVisitsService.DeleteVisitsRecord(patientProfileRecordIDs, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存问题整改纪录
        /// </summary>
        /// <param name="saveView">保存参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveProblemRectificationData")]
        public async Task<Object> SaveProblemRectificationData([FromBody] ProblemRectificationView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if(await hierarchicalQCService.SaveProblemRectificationData(saveView, session))
            {
                result.Sucess();
            }
            return result.ToJson();
        }
        /// <summary>
        /// 确认问题整改
        /// </summary>
        /// <param name="hierarchicalQCMainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ConfirmRectification")]
        public async Task<Object> ConfirmRectification(string hierarchicalQCMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            if(await hierarchicalQCService.ConfirmRectification(hierarchicalQCMainID, session.EmployeeID))
            {
                result.Sucess();
            }
            return result.ToJson();
        }
        /// <summary>
        /// 上传质控主题维护节点式督导文件
        /// </summary>
        /// <param name="file">文件</param>
        /// <param name="view">除去文件的其它参数</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UploadHierarchicalReport")]
        public async Task<Object> UploadHierarchicalReport(IFormFile file, [FromForm] HierarchicalQCFileView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.UploadHierarchicalReportAsync(file, view);
            return result.ToJson();
        }
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteQCFile")]
        public async Task<Object> DeleteQCFile(string subjectID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.DeleteQCFileAsync(subjectID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取质控维护记录中的评价和指导内容
        /// </summary>
        /// <param name="careMainID">质控维护记录ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetGuidanceAndImprovement")]
        public async Task<IActionResult> GetGuidanceAndImprovement(string careMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetGuidanceAndImprovement(careMainID);
            result.Sucess();
            return result.ToJson();
        }
        /// <summary>
        /// 获取部门与质控类型关系
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDeparmentToQCFormType")]
        public async Task<IActionResult> GetDeparmentToQCFormType()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await deparmentToDataTypeRepository.GetDataByTableNameAndKey("HierarchicalQCForm", "FormType");
            result.Sucess();
            return result.ToJson();
        }

        /// <summary>
        /// 根据条件获取主题下拉框数据
        /// </summary>
        /// <param name="searchView"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQCSubjectSelectOptions")]
        public async Task<IActionResult> GetQCSubjectOption([FromQuery] GetQCSubjectView searchView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            searchView.HospitalID ??= session.HospitalID;
            result.Data = await hierarchicalQCService.GetSubjectSelectOptions(searchView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取评价标准
        /// </summary>
        /// <param name="templateCode">模板ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuestionTitles")]
        public async Task<IActionResult> GetQuestionTitles(string templateCode)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetQuestionTitles(templateCode);
            return result.ToJson();
        }

        /// <summary>
        /// 获取考核记录
        /// </summary>
        /// <param name="careMainID">维护记录ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQcMainViews")]
        public async Task<IActionResult> GetQcMainViews(string careMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetQcMainViews(careMainID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取质控预览图片
        /// </summary>
        /// <param name="careMainID"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPreviewImage")]
        public async Task<Object> GetPreviewImageAsync(string careMainID, string templateCode)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await hierarchicalQCService.GetPreviewImageAsync(careMainID, templateCode);
            return result.ToJson();
        }
        /// <summary>
        /// 获取督导记录
        /// </summary>
        /// <param name="supervisionQuery"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetSupervisionRecord")]
        public async Task<IActionResult> GetSupervisionRecord([FromBody] SupervisionQueryView supervisionQuery)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            supervisionQuery.HospitalID = session.HospitalID;
            supervisionQuery.Language = session.Language;
            result.Data = await sensitiveIndicatorSupervisionService.GetSensitiveIndicatorSupervisionRecord(supervisionQuery);
            return result.ToJson();
        }
        /// <summary>
        /// 获取督导记录模板
        /// </summary>
        /// <param name="profileID"></param>
        /// <param name="qcMainID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSensitiveQcAssessView")]
        public async Task<IActionResult> GetSensitiveQcAssessView(string profileID, string qcMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await sensitiveIndicatorSupervisionService.GetSensitiveQcAssessViewAsync(profileID, qcMainID);
            return result.ToJson();
        }
        /// <summary>
        /// 督导记录保存
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveSensitiveRecord")]
        public async Task<IActionResult> SaveSensitiveRecord([FromBody] SupervisionRecordSaveView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.HospitalID = session.HospitalID;
            view.Language = session.Language;
            view.EmployID = session.EmployeeID;
            result.Data = await sensitiveIndicatorSupervisionService.SaveSensitiveRecordAsync(view);
            return result.ToJson();
        }
        /// <summary>
        /// 获取督导记录
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="sourceType"></param>
        /// <param name="supervisionType"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSensitiveQcRecord")]
        public async Task<IActionResult> GetSensitiveQcRecord(string sourceID, string sourceType, string supervisionType, string templateCode)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            };
            result.Data = await sensitiveIndicatorSupervisionService.GetSensitiveQcRecordAsync(sourceID, sourceType, supervisionType, templateCode, session.HospitalID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除敏感指标记录
        /// </summary>
        /// <param name="patientProfileRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteSensitiveRecord")]
        public async Task<IActionResult> DeleteSensitiveRecord(string patientProfileRecordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await sensitiveIndicatorSupervisionService.DeleteSensitiveRecordAsync(patientProfileRecordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除所有访视记录
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceID"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteAllSupervisionRecord")]
        public async Task<IActionResult> DeleteAllSupervisionRecord(string sourceType, string sourceID, string relatedTableName)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await sensitiveIndicatorSupervisionService.DeleteAllSupervisionRecordAsync(sourceType, sourceID, relatedTableName, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存阅读记录
        /// </summary>
        /// <param name="hierarchicalQCMainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveReadRecord")]
        public async Task<IActionResult> SaveReadRecord(string hierarchicalQCMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await criticalPatientVisitsService.SaveReadRecordAsync(hierarchicalQCMainID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取危重患者访视数量
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetCriticalPatientVisitsCount")]
        public async Task<IActionResult> GetCriticalPatientVisitsCount([FromBody] GetPatientProfileRecordView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.HospitalID = session.HospitalID;
            result.Data = await criticalPatientVisitsService.GetCriticalPatientVisitsCount(view);
            return result.ToJson();
        }
    }
}