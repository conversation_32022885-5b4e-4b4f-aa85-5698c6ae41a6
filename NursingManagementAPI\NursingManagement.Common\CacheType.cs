﻿namespace NursingManagement.Common
{
    /// <summary>
    /// 缓存类型
    /// </summary>
    public enum CacheType
    {
        /// <summary>
        /// 远程调用API配置字典
        /// </summary>
        AppConfigSetting,
        /// <summary>
        /// API配置表
        /// </summary>
        APISetting,
        /// <summary>
        /// 医院列表
        /// </summary>
        HospitalList,
        /// <summary>
        /// 人员表
        /// </summary>
        EmployeeData,
        /// <summary>
        /// 用户登录表
        /// </summary>
        UserLogin,
        /// <summary>
        /// 角色表
        /// </summary>
        AuthorityRole,
        /// <summary>
        /// 用户角色表
        /// </summary>
        EmployeeRole,
        /// <summary>
        /// 功能权限表
        /// </summary>
        AuthorityList,
        /// <summary>
        /// 角色功能对照表
        /// </summary>
        AuthorityRoleList,
        /// <summary>
        /// 菜单表
        /// </summary>
        MenuList,
        /// <summary>
        /// 路由表
        /// </summary>
        RouterList,
        /// <summary>
        /// 路由组件表
        /// </summary>
        ComponentList,
        /// <summary>
        /// 路由组件权限表
        /// </summary>
        AuthorityComponentList,
        ///<summary>
        /// 岗位
        /// </summary>
        Post,
        /// <summary>
        /// 政府编码字典表
        /// </summary>
        AdministrationDictionary,
        /// <summary>
        /// 执行项目字典
        /// </summary>
        InterventionList,
        /// <summary>
        /// 年度计划类别
        /// </summary>
        AnnualPlanTypeList,
        /// <summary>
        /// 年度计划目标
        /// </summary>
        AnnualGoalList,
        /// <summary>
        /// 年度计划指标
        /// </summary>
        AnnualIndicatorList,
        /// <summary>
        /// 科室岗位
        /// </summary>
        DepartmentPost,
        /// <summary>
        /// 岗位说明书
        /// </summary>
        PostDescription,
        /// <summary>
        /// 岗位，能级对照表
        /// </summary>
        DepartmentPostToCapabilityLevel,
        /// <summary>
        /// 部门字典
        /// </summary>
        DepartmentList,
        /// <summary>
        /// 部门岗位设定字典
        /// </summary>
        DepartmentPostSetting,
        /// <summary>
        /// 部门岗位工作时间
        /// </summary>
        DepartmentPostWorkingTime,
        /// <summary>
        /// 质控表单字典表
        /// </summary>
        HierarchicalQCForm,
        /// <summary>
        /// 质控主题表
        /// </summary>
        HierarchicalQCSubject,
        /// <summary>
        /// 内容明细字典表
        /// </summary>
        HierarchicalQCAssessList,
        /// <summary>
        /// 质控内容备注字典表
        /// </summary>
        HierarchicalQCRemark,
        /// 能级配置
        /// </summary>
        CapabilityLevel,
        /// <summary>
        /// 个人信息
        /// </summary>
        EmployeePersonalData,
        /// <summary>
        /// 问卷模板表
        /// </summary>
        QuestionaireTemplate,
        /// <summary>
        /// 问卷模板项目属性表
        /// </summary>
        TemplateControlerAttribute,
        /// <summary>
        /// 注记图示表
        /// </summary>
        AdministrationIcon,
        /// <summary>
        /// 人事部门，护理管理部门对照表
        /// </summary>
        DepartmentVSDepartment,
        /// <summary>
        /// 业务模块对审批流程设置表
        /// </summary>
        ModuleToApproveProcess,
        /// <summary>
        /// 人员在职信息表
        /// </summary>
        EmployeeStaffData,
        /// 科室职务对照
        /// </summary>
        DepartmentToJob,
        /// <summary>
        /// 人员职务对照
        /// </summary>
        EmployeeToJob,
        /// <summary>
        /// 万年历
        /// </summary>
        PerpetualCalendar,
        /// <summary>
        /// 人员多组织架构部门对照表
        /// </summary>
        EmployeeToDepartment,
        /// <summary>
        /// 组件属性字典表
        /// </summary>
        ComponentAttribute,
        /// <summary>
        /// 动态表单主表
        /// </summary>
        DynamicFormRecord,
        /// <summary>
        /// 动态表单明细表
        /// </summary>
        DynamicFormDetail,
        /// <summary>
        /// 动态表单明细项目属性表
        /// </summary>
        DynamicFormDetailAttribute,
        /// <summary>
        /// 动态表单明细联动条件表
        /// </summary>
        DynamicFormDetailCondition,
        /// <summary>
        /// 人员多部门权限
        /// </summary>
        EmployeeDepartmentSwitch,
        /// <summary>
        /// 培训课程字典
        /// </summary>
        CourseSetting,
        /// <summary>
        /// 非国标字典配置表
        /// </summary>
        SettingDictionaryInfo,
        /// <summary>
        /// 规则属性主表
        /// </summary>
        RuleListInfo,
        /// <summary>
        /// 规则属性明细表
        /// </summary>
        RuleDetailListInfo
    }
    public static class CacheTypeExtensions
    {
        public static string GetKey(this CacheType cacheType, SessionCommonServer _sessionCommonServer)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return cacheType.ToString() + "_H" + session.HospitalID + "_L" + session.Language.ToString();
        }
        /// <summary>
        /// 医院ID及语言使用参数  前端模板配置页面使用 其它页面不允许使用
        /// </summary>
        /// <param name="cacheType"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static string GetKeyNotBySession(this CacheType cacheType, string hospitalID, int language)
        {
            return cacheType.ToString() + "_H" + hospitalID + "_L" + language.ToString();
        }
    }
}