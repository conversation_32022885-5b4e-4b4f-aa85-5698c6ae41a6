﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ShiftSchedulingRuleRepository : IShiftSchedulingRuleRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        public ShiftSchedulingRuleRepository(
            NursingManagementDbContext db
        )
        {
            _nursingManagementDbContext = db;
        }

        public async Task<List<ShiftSchedulingRuleInfo>> GetShiftSchedulingRule(int departmentID)
        {
            return await _nursingManagementDbContext.ShiftSchedulingRuleInfos.Where(m => m.DepartmentID == departmentID && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<ShiftSchedulingRuleInfo> GetShiftSchedulingRuleByID(string shiftSchedulingRuleID)
        {
            return await _nursingManagementDbContext.ShiftSchedulingRuleInfos.Where(m => m.ShiftSchedulingRuleID == shiftSchedulingRuleID && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }
    }
}
