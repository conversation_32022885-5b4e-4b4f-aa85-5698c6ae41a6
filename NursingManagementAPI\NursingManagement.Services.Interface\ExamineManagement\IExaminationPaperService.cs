﻿using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.Services.Interface
{
    public interface IExaminationPaperService
    {
        /// <summary>
        /// 获取试卷主表数据
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="paperType"></param>
        /// <param name="departmentID"></param>
        /// <param name="employeeID">当前会话登录人员</param>
        /// <returns></returns>
        Task<List<ExaminationPaperMainView>> GetExaminationPaperMainList(DateTime startDate, DateTime endDate, string paperType, int? departmentID, string employeeID);

        /// <summary>
        /// 删除试卷相关数据
        /// </summary>
        /// <param name="examinationPaperMainID"></param>
        /// <param name="employeeID"></param>
        Task<string> DeleteExaminationPaperMainData(string examinationPaperMainID, string employeeID);

        /// <summary>
        /// 删除题库相关试卷记录
        /// </summary>
        /// <param name="questionBankIDs"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<string>> DeletePaperByQuestionBank(List<string> questionBankIDs, string employeeID);

        /// <summary>
        /// 保存理论类试卷
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SaveTheoryPaper(ExaminationPaperMainView view);

        /// <summary>
        /// 保存实操类试卷
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<bool> SavePracticalPaper(ExaminationPaperMainView view);

        /// <summary>
        /// 获取试卷模版数据
        /// </summary>
        /// <param name="examinationPaperMainID"></param>
        /// <param name="isPreview"></param>
        /// <param name="employeeID"></param>
        /// <param name="examinationMainID"></param>
        /// <returns></returns>
        Task<ExamineFormTemplateView> GetPaperFormTemplate(string examinationPaperMainID, bool isPreview, string employeeID, string examinationMainID = null);

        /// <summary>
        /// 获取试卷下拉框数据
        /// </summary>
        /// <param name="paperType"></param>
        /// <param name="employeeID"></param>
        /// <param name="departmentID">当前用户登录查看的部门</param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetPaperMainSelectList(string paperType, string employeeID, int departmentID);

        /// <summary>
        /// 获取组卷规则下拉框数据
        /// </summary>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetPaperRuleSelectList();

        /// <summary>
        /// 根据考核记录MainID获取试卷模版内容
        /// </summary>
        /// <param name="examinationMainID">考核主记录ID</param>
        /// <param name="employeeID">人员工号</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns></returns>
        Task<ExamineFormTemplateView> GetPaperFormTemplateByMainID(string examinationMainID, string employeeID, string hospitalID);

        /// <summary>
        /// 根据试卷主记录ID复制一份新的试卷
        /// </summary>
        /// <param name="examinationPaperMainID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> CopyExamPaperMain(string examinationPaperMainID, string employeeID);
    }
}
