﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 发送消息参数
    /// </summary>
    public class MessageConditionView
    {
        /// <summary>
        /// MQ消息路由键
        /// </summary>
        public string MQExchangeName { get; set; }
        /// <summary>
        /// MQ路由键，仅在消息发送类型为广播时可用
        /// </summary>
        public string MQRoutingKey { get; set; }
        /// <summary>
        /// 消息类型 取自MessageType枚举
        /// </summary>
        public MessageType Type { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        public string PhoneNumber { get; set; }
        /// <summary>
        /// 消息发送时间
        /// </summary>
        public DateTime SendMessageDateTime { get; set; }        
        /// <summary>
        /// 消息内容 (实际只有在发送MQ的时候使用到了这个字段，其他时候都会将其最后赋值给MessageContent使用)
        /// </summary>
        public object Message { get; set; } = null!;
        /// <summary>
        /// 如果Message为对象，提供消息格式化模板供前端解析消息
        /// </summary>
        public string MessageFormatter { get; set; } = null!;
        /// <summary>
        /// 消息内容
        /// </summary>
        public string MessageContent { get; set; } = null;        
        /// <summary>
        /// MQ、微信、钉钉消息点击跳转的链接地址
        /// </summary>
        public string Url { get; set; } = null!;
        /// <summary>
        /// 消息客户端类别
        /// </summary>
        public int ClientType { get; set; }
        /// <summary>
        /// 消息客户端参数
        /// </summary>
        public object UrlParams { get; set; }
    }

    /// <summary>
    /// 消息类型
    /// </summary>
    public enum MessageType
    {
        /// <summary>
        /// 通知 用于系统级通知的被动提醒。
        /// </summary>
        Notification,
        /// <summary>
        /// 提示 用于页面中展示重要的提示信息
        /// </summary>
        Alert,
        /// <summary>
        /// 确认框 弹出一个简单的确认框，会打断用户操作
        /// </summary>
        Confirm,
        /// <summary>
        ///  消息 主动操作后的反馈提示
        /// </summary>
        Message
    }
}
