﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 试卷题目视图
    /// </summary>
    public class PaperQuestionView
    {
        /// <summary>
        /// 所属题库ID
        /// </summary>
        public string QuestionBankID { get; set; }

        /// <summary>
        /// 题目ID
        /// </summary>
        public string QuestionID { get; set; }

        /// <summary>
        /// 题目标题
        /// </summary>
        public string QuestionTitle { get; set; }

        /// <summary>
        /// 题目解析
        /// </summary>
        public string Analysis { get; set; }

        /// <summary>
        /// 题目类型
        /// </summary>
        public string QuestionType { get; set; }

        /// <summary>
        /// 组件ID
        /// </summary>
        public int ComponentListID { get; set; }

        /// <summary>
        /// 题目分数
        /// </summary>
        public decimal? Score { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 固定id，组装试卷时需要的参数
        /// </summary>
        public string FixedItemID { get; set; }

        /// <summary>
        /// 项目来源字典，这里放字典表名
        /// </summary>
        public string ItemSourceType { get; set; }

        /// <summary>
        /// 选项
        /// </summary>
        public List<PaperQuestionOptionsView> Options { get; set; }

        /// <summary>
        /// 正确选项ID集合
        /// </summary>
        public List<string> CorrectOptions { get; set; }
    }
}
