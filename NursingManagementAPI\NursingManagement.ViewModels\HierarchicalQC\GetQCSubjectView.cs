﻿namespace NursingManagement.ViewModels.HierarchicalQC
{
    public class GetQCSubjectView
    {
        /// <summary>
        /// 医院
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 质控级别
        /// </summary>
        public string QCLevel { get; set; }
        /// <summary>
        /// 开始月份
        /// </summary>
        public string StartYearMonth { get; set; }
        /// <summary>
        /// 结束月份
        /// </summary>
        public string EndYearMonth { get; set; }
        /// <summary>
        /// 维护科室
        /// </summary>
        public int? DepartmentID { get; set; }
        /// <summary>
        /// 主题类型
        /// </summary>
        public string FormType { get; set; }
        /// <summary>
        /// 主题ID
        /// </summary>
        public int? FormID { get; set; }
        /// <summary>
        /// 考核主题主键ID
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }
        /// <summary>
        /// 主题类型类别
        /// </summary>
        public QCTypeEnum? QCType { get; set; }
    }
}
