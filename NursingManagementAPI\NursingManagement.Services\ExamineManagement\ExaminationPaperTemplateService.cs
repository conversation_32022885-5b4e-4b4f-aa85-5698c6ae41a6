﻿using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Collections.Concurrent;

namespace NursingManagement.Services
{
    public class ExaminationPaperTemplateService : IExaminationPaperTemplateService
    {
        private readonly IExaminationQuestionRepository _examinationQuestionRepository;
        private readonly IExaminationQuestionDetailRepository _examinationQuestionDetailRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IQuestionBankRepository _questionBankRepository;
        private readonly IComponentListRepository _componentListRepository;

        /// <summary>
        /// 判断题的RuleCode
        /// </summary>
        private const string RULE_CODE_JUDGMENT_QUESTION = "Judgment";

        /// <summary>
        /// 刷题练习
        /// </summary>
        private static readonly string PAPER_TYPE_4 = "4";

        public ExaminationPaperTemplateService(
            IExaminationQuestionRepository examinationQuestionRepository
            , IExaminationQuestionDetailRepository examinationQuestionDetailRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , IQuestionBankRepository questionBankRepository
            , IComponentListRepository componentListRepository
        )
        {
            _examinationQuestionRepository = examinationQuestionRepository;
            _examinationQuestionDetailRepository = examinationQuestionDetailRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _questionBankRepository = questionBankRepository;
            _componentListRepository = componentListRepository;
        }

        #region 根据题库生成试卷模板（理论类）

        public async Task<(FormTemplateView, int)> CreateTemplateByBankID(string questionBankID, bool? isPractice = false, string paperTitle = null)
        {
            var questionBanks = await _questionBankRepository.GetListByBankID(questionBankID);
            if (questionBanks.Count <= 0)
            {
                return (new FormTemplateView(), 0);
            }
            var choiceBank = questionBanks.FirstOrDefault(m => m.QuestionBankID == questionBankID);
            var bankIDs = questionBanks.Select(m => m.QuestionBankID).ToList();
            var processedIDs = new HashSet<string>();
            var childrenBankList = await GetChildrenQuestionBankRecursive(bankIDs, processedIDs);
            if (childrenBankList.Count > 0)
            {
                var childrenBanks = childrenBankList.Select(m => m.QuestionBankID).ToList();
                bankIDs.AddRange(childrenBanks);
            }
            var questionList = await _examinationQuestionRepository.GetListByQuestionBankIDList(bankIDs);
            if (questionList.Count <= 0)
            {
                return (new FormTemplateView(), 0);
            }
            var questionIDs = questionList.Select(m => m.ExaminationQuestionID).Distinct().ToList();
            var questionDetailList = await _examinationQuestionDetailRepository.GetListByQuestionIDs(questionIDs);
            var paperQuestionViewList = CreateViewByQuestion(questionList, questionDetailList, isPractice.Value);
            // 根据题库生成试卷
            var paperGroup = await GetTheoryPaperPaperGroup(paperQuestionViewList, PAPER_TYPE_4);
            var returnView = await ConvertToFormTemplate(paperGroup, paperQuestionViewList, paperTitle ?? $"{choiceBank.Content}", false);
            return (returnView, paperQuestionViewList.Count);
        }

        /// <summary>
        /// 递归获取子题库
        /// </summary>
        /// <param name="questionBankIDs">题库ID集合</param>
        /// <param name="processedIDs">记录处理过的题库ID</param>
        /// <returns></returns>
        private async Task<List<QuestionBankInfo>> GetChildrenQuestionBankRecursive(List<string> questionBankIDs, HashSet<string> processedIDs)
        {
            var list = new List<QuestionBankInfo>();
            foreach (var bankID in questionBankIDs)
            {
                // 关键修复点：跳过已处理的ID
                if (processedIDs.Contains(bankID))
                {
                    continue;
                }
                // 标记为已处理
                processedIDs.Add(bankID);
                var questionBank = await _questionBankRepository.GetListByBankID(bankID);
                if (questionBank.Count == 0)
                {
                    continue;
                }
                list.AddRange(questionBank);
                // 提取子题库ID时排除已处理过的
                var childIDs = questionBank.Select(m => m.QuestionBankID).Except(processedIDs).ToList();
                // 递归处理子级（传递同一个已处理集合）
                var children = await GetChildrenQuestionBankRecursive(childIDs, processedIDs);
                list.AddRange(children);
            }
            return list;
        }

        /// <summary>
        /// 创建试卷题目视图
        /// </summary>
        /// <param name="questionList"></param>
        /// <param name="questionDetailList"></param>
        /// <param name="isPractice">是否为刷题练习</param>
        /// <returns></returns>
        private List<PaperQuestionView> CreateViewByQuestion(List<ExaminationQuestionInfo> questionList, List<ExaminationQuestionDetailInfo> questionDetailList, bool isPractice)
        {
            var returnView = new List<PaperQuestionView>();
            foreach (var question in questionList)
            {
                var typeCount = returnView.Where(m => m.QuestionType == question.ExaminationQuestionType).Count();
                var questionOptions = SortQuestionOption(question, questionDetailList).ToList();
                var view = new PaperQuestionView()
                {
                    QuestionBankID = question.QuestionBankID,
                    QuestionID = question.ExaminationQuestionID.ToString(),
                    QuestionTitle = question.QuestionContent,
                    Analysis = question.Analysis,
                    QuestionType = question.ExaminationQuestionType,
                    ComponentListID = question.ComponentListID,
                    Score = null,
                    Sort = typeCount + 1,
                    FixedItemID = question.ExaminationQuestionID.ToString(),
                    ItemSourceType = "ExaminationQuestion",
                    Options = questionOptions.Select(m => new PaperQuestionOptionsView
                    {
                        Value = m.ExaminationQuestionDetailID,
                        Label = m.Content,
                        FixedItemID = m.ExaminationQuestionDetailID,
                        ItemSourceType = "ExaminationQuestionDetail",
                    }).ToList()
                };
                // 刷题练习 添加特殊逻辑
                if (isPractice)
                {
                    view.CorrectOptions = questionOptions.Where(m => m.AnswerFlag).Select(m => m.ExaminationQuestionDetailID).ToList();
                    // 如果解析为空，将正确答案拼接为解析
                    if (string.IsNullOrWhiteSpace(view.Analysis))
                    {
                        var correctOptionContent = questionOptions.Where(m => m.AnswerFlag).Select(m => m.Content).Aggregate((a, b) => $"{a}、{b}");
                        view.Analysis = $"正确答案：{correctOptionContent}";
                    }
                }
                returnView.Add(view);
            }
            return returnView;
        }

        /// <summary>
        /// 获取理论试卷模版gruop内容
        /// </summary>
        /// <param name="questionList"></param>
        /// <returns></returns>
        public async Task<List<PaperGroupView>> GetTheoryPaperPaperGroup(List<PaperQuestionView> questionList, string paperType)
        {
            var groupList = new List<PaperGroupView>();
            var questionTypeSettingParams = new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationQuestion",
                SettingTypeValue = "ExaminationQuestionType"
            };
            var questionTypes = await _settingDictionaryRepository.GetSettingDictionary(questionTypeSettingParams);
            if (questionTypes.Count == 0)
            {
                return groupList;
            }
            var showScore = true;
            // 刷题练习 题目上不需要呈现分数
            if (paperType == PAPER_TYPE_4)
            {
                showScore = false;
            }
            var titles = new[] { "一", "二", "三", "四", "五" };
            string sortDesc;
            string scoreDesc;
            int sort = 1;
            foreach (var item in questionTypes)
            {
                var typeQuestions = questionList.Where(m => m.QuestionType == item.SettingValue).ToList();
                if (typeQuestions.Count <= 0)
                {
                    continue;
                }
                sortDesc = sort >= 1 && sort <= titles.Length ? titles[sort - 1] : sort.ToString();
                scoreDesc = showScore ? $"（{typeQuestions.Sum(m => m.Score)?.ToString("0.##")}分）" : "";
                var group = new PaperGroupView
                {
                    GroupID = item.SettingDictionaryID.ToString(),
                    GroupType = item.SettingValue,
                    GroupName = $"{sortDesc}、{item.Description}{scoreDesc}",
                    FixedItemID = item.SettingDictionaryID.ToString(),
                    ItemSourceType = "SettingDictionary"
                };
                groupList.Add(group);
                sort++;
            }
            return groupList;
        }

        #endregion

        #region 根据题库生成试卷模板（实操类）

        public async Task<(FormTemplateView, int)> GetPaperTemplateByBankID(string questionBankID, string paperTitle, bool isPractical)
        {
            var questionBanks = await _questionBankRepository.GetQuestionBankList(null, true);
            // 将题库集合转换为试卷分组（可嵌套），并返回所有题库ID集合
            var (paperGroups, questionBankIDs) = ConvertQuestionBankToGroups(questionBanks, questionBankID, true);
            // 根据题库ID集合获取试卷题目集合
            var paperQuestionList = await GetPaperQuestionListByQuestionBankIDs(questionBankIDs);
            if (paperQuestionList.Count <= 0)
            {
                throw new CustomException("保存失败：选择的题库中没有试题！");
            }
            var paperFormTemplate = await ConvertToFormTemplate(paperGroups, paperQuestionList, paperTitle, isPractical, false);
            return (paperFormTemplate, paperQuestionList.Count);
        }

        /// <summary>
        /// 将题库转换为试卷分组
        /// </summary>
        /// <param name="questionBanks"></param>
        /// <param name="parentQuestionBankID"></param>
        /// <param name="noParentFalg">是否有父项标记</param>
        /// <returns></returns>
        /// <exception cref="CustomException"></exception>
        private (List<PaperGroupView>, List<string>) ConvertQuestionBankToGroups(List<QuestionBankInfo> questionBanks, string parentQuestionBankID, bool noParentFalg)
        {
            var parentQuestionBank = questionBanks.Find(m => m.QuestionBankID == parentQuestionBankID);
            if (parentQuestionBank == null)
            {
                throw new CustomException("保存失败：选择的题库不存在！");
            }
            var questionBankIDs = new List<string>() { parentQuestionBankID };
            var paperGroups = new List<PaperGroupView>();
            var questionBankList = questionBanks.Where(m => m.ParentID == parentQuestionBankID).OrderBy(m => m.Sort).ToList();
            if (questionBankList.Count <= 0)
            {
                if (noParentFalg)
                {
                    paperGroups.Add(new()
                    {
                        GroupID = parentQuestionBank.QuestionBankID,
                        GroupName = parentQuestionBank.Content,
                        FixedItemID = parentQuestionBank.QuestionBankID,
                        ItemSourceType = "QuestionBank"
                    });
                }
                return (paperGroups, questionBankIDs);
            }
            foreach (var questionBank in questionBankList)
            {
                var paperGroup = new PaperGroupView()
                {
                    GroupID = questionBank.QuestionBankID,
                    GroupName = questionBank.Content,
                    FixedItemID = questionBank.QuestionBankID,
                    ItemSourceType = "QuestionBank"
                };
                // 递归转换子题库
                (paperGroup.Children, var childQuestionBankIDs) = ConvertQuestionBankToGroups(questionBanks, questionBank.QuestionBankID, false);
                paperGroups.Add(paperGroup);
                questionBankIDs.AddRange(childQuestionBankIDs);
            }
            return (paperGroups, questionBankIDs);
        }

        /// <summary>
        /// 根据题库ID集合获取试卷题目集合
        /// </summary>
        /// <param name="questionBankIDs"></param>
        /// <returns></returns>
        private async Task<List<PaperQuestionView>> GetPaperQuestionListByQuestionBankIDs(List<string> questionBankIDs)
        {
            var paperQuestionList = new List<PaperQuestionView>();
            var questionList = await _examinationQuestionRepository.GetListByQuestionBankIDList(questionBankIDs);
            if (questionList.Count <= 0)
            {
                return paperQuestionList;
            }
            var questionIDs = questionList.Select(m => m.ExaminationQuestionID).ToList();
            var questionDetailList = await _examinationQuestionDetailRepository.GetListByQuestionIDs(questionIDs);
            foreach (var question in questionList)
            {
                var options = SortQuestionOption(question, questionDetailList).Select(
                    m => new PaperQuestionOptionsView()
                    {
                        Value = m.ExaminationQuestionDetailID,
                        Label = m.Content,
                        FixedItemID = m.ExaminationQuestionDetailID,
                        ItemSourceType = "ExaminationQuestionDetail"
                    }).ToList();
                paperQuestionList.Add(new PaperQuestionView()
                {
                    QuestionBankID = question.QuestionBankID,
                    QuestionID = question.ExaminationQuestionID.ToString(),
                    QuestionTitle = question.QuestionContent,
                    Analysis = question.Analysis,
                    QuestionType = question.ExaminationQuestionType,
                    ComponentListID = question.ComponentListID,
                    Sort = question.Sort,
                    Score = question.Score,
                    FixedItemID = question.ExaminationQuestionID.ToString(),
                    ItemSourceType = "ExaminationQuestion",
                    Options = options
                });
            }
            return paperQuestionList;
        }

        #endregion

        #region 将试卷数据转换为试卷模板

        /// <summary>
        /// 转换为试卷模板
        /// </summary>
        /// <param name="paperGroups">试卷分组</param>
        /// <param name="paperQuestionList">试卷考题集合</param>
        /// <param name="paperTitle">试卷标题</param>
        /// <param name="isPractical">是否为实操类型</param>
        /// <param name="oneGroupIsDisplay">只有一个分组且没有子分组时是否显示分组，默认显示</param>
        /// <param name="paperQuestionMode">试卷题目组卷模式</param>
        /// <returns></returns>
        public async Task<FormTemplateView> ConvertToFormTemplate(List<PaperGroupView> paperGroups, List<PaperQuestionView> paperQuestionList, string paperTitle, bool isPractical, bool oneGroupIsDisplay = true, string paperQuestionMode = "1")
        {
            var componentList = await _componentListRepository.GetComponentListByType("DynamicForm");
            var formTemplate = new FormTemplateView()
            {
                Props = new FormRecordView(paperTitle, "2"),
                Datas = [],
                Components = [],
                AllowCreateDictionary = isPractical
            };
            // 如果只有一个分组且没有子分组 并且标记此种情况不显示分组，特殊处理
            if (paperGroups.Count == 1 && (paperGroups[0].Children == null || paperGroups[0].Children.Count <= 0) && !oneGroupIsDisplay)
            {
                formTemplate.Components = GetFormComponents(isPractical, paperQuestionList, paperGroups[0], componentList, paperQuestionMode);
                return formTemplate;
            }
            foreach (var group in paperGroups)
            {
                var groupComponent = new FormComponentView()
                {
                    Type = "groupLayout",
                    IsLayout = true,
                    ID = group.GroupID,
                    FixedItemID = group.FixedItemID,
                    ItemSourceType = group.ItemSourceType,
                    Props = CreateComponentProps(isPractical, true, group.GroupName, true),
                    Children = GetFormComponents(isPractical, paperQuestionList, group, componentList, paperQuestionMode)
                };
                formTemplate.Components.Add(groupComponent);
            }
            return formTemplate;
        }

        /// <summary>
        /// 递归组装分组内的的项目
        /// </summary>
        /// <param name="isPractical">是否为实操类</param>
        /// <param name="paperQuestionList"></param>
        /// <param name="group"></param>
        /// <param name="componentList"></param>
        /// <param name="paperQuestionMode"></param>
        /// <returns></returns>
        private List<FormComponentView> GetFormComponents(bool isPractical, List<PaperQuestionView> paperQuestionList, PaperGroupView group, List<ComponentListInfo> componentList, string paperQuestionMode)
        {
            var components = new List<FormComponentView>();
            var questionList = new List<PaperQuestionView>();
            if (isPractical)
            {
                questionList = paperQuestionList.Where(m => m.QuestionBankID == group.GroupID).OrderBy(m => m.Sort).ToList();
            }
            else
            {
                questionList = paperQuestionList.Where(m => m.QuestionType == group.GroupType).ToList();
                // 按照不定题规则重排序
                questionList = ReSortQuestionOrderOrOptionOrder(paperQuestionMode, questionList, group.GroupType);
            }
            // 组装本组的项目
            if (questionList.Count > 0)
            {
                foreach (var question in questionList)
                {
                    var component = new FormComponentView()
                    {
                        Type = componentList.Find(m => m.ComponentListID == question.ComponentListID)?.ControlerType ?? "",
                        IsLayout = false,
                        ID = question.QuestionID,
                        FixedItemID = question.FixedItemID,
                        ItemSourceType = question.ItemSourceType,
                    };
                    if (isPractical)
                    {
                        component.Props = CreateComponentProps(isPractical, false, question.QuestionTitle, false, null, question.Score, question.Analysis);
                    }
                    else
                    {
                        var label = $"{question.Sort}、{question.QuestionTitle}{(question.Score.HasValue ? $"（{question.Score}分）" : "")}";
                        component.Props = CreateComponentProps(isPractical, false, label, false, question.Options, question.Score, question.Analysis, question.CorrectOptions);
                    }

                    components.Add(component);
                }
            }
            if (group.Children == null || group.Children.Count <= 0)
            {
                return components;
            }
            // 组装子分组项目
            foreach (var childGroup in group.Children)
            {
                var groupComponent = new FormComponentView()
                {
                    Type = "groupLayout",
                    IsLayout = true,
                    ID = childGroup.GroupID,
                    FixedItemID = childGroup.FixedItemID,
                    ItemSourceType = childGroup.ItemSourceType,
                    Props = CreateComponentProps(isPractical, true, childGroup.GroupName, true),
                    Children = GetFormComponents(isPractical, paperQuestionList, childGroup, componentList, paperQuestionMode)
                };
                components.Add(groupComponent);
            }
            return components;
        }

        /// <summary>
        /// 组装表单模板项目的属性集合
        /// </summary>
        /// <param name="isPractical"></param>
        /// <param name="isLayout"></param>
        /// <param name="label"></param>
        /// <param name="isTitle"></param>
        /// <param name="options"></param>
        /// <param name="score"></param>
        /// <param name="sort"></param>
        /// <param name="score"></param>
        /// <returns></returns>
        private Dictionary<string, object> CreateComponentProps(bool isPractical, bool isLayout, string label, bool isTitle = false, List<PaperQuestionOptionsView> options = null, Decimal? score = null, string analysis = null, List<string> correctOptions = null)
        {
            var props = new Dictionary<string, object> {
                { "width", 12 },
                { "showLabel", true },
                { "fontSize", 14 },
                { "isBold", isTitle },
                { "showFlag", true },
                { "label", label },
                { "labelNewLine", true },
                { "description", analysis }
            };
            if (isLayout)
            {
                props["showBorder"] = true;
                props["borderStyle"] = "solid";
                props["borderColor"] = "#cccccc";
                props["borderWidth"] = 1;
                props["paddingLeft"] = 10;
                return props;
            }
            if (isPractical)
            {
                props["maxScore"] = (int)score;
                props["showZero"] = true;
                props["showRemark"] = true;
                props["scoreReverseFlag"] = true;
                if (score.HasValue)
                {
                    props["defaultValue"] = (int)score;
                }
            }
            else
            {
                props["options"] = options;
                props["points"] = score;
                props["newLine"] = true;
                if (correctOptions != null && correctOptions.Count > 0)
                {
                    props["correctOptions"] = correctOptions;
                }
            }

            return props;
        }

        #endregion

        #region 题目排序逻辑

        /// <summary>
        /// 解决试卷中判断题选项顺序不一致的问题
        /// </summary>
        /// <param name="question"></param>
        /// <param name="questionDetailList"></param>
        /// <returns></returns>
        private IEnumerable<ExaminationQuestionDetailInfo> SortQuestionOption(ExaminationQuestionInfo question, List<ExaminationQuestionDetailInfo> questionDetailList)
        {
            IEnumerable<ExaminationQuestionDetailInfo> detailEnumerable = questionDetailList.Where(detail => question.ExaminationQuestionID == detail.ExaminationQuestionID);

            if (question.ExaminationQuestionType == RULE_CODE_JUDGMENT_QUESTION)
            {
                // 后续看是否删除还是走配置
                detailEnumerable = detailEnumerable.OrderBy(m => m.Content == "正确" ? 0 : 1).ThenBy(m => m.Content == "对" ? 0 : 1);
            }
            else
            {
                detailEnumerable = detailEnumerable.OrderBy(m => m.Sort);
            }
            return detailEnumerable;
        }

        /// <summary>
        /// 根据条件设置题目选项顺序以及题目顺序
        /// </summary>
        /// <param name="unFixedQuestion">不定题条件值</param>
        /// <param name="questionList">试卷某一个类别的题目</param>
        /// <param name="questionType">题目类型</param>
        /// <returns></returns>
        private static List<PaperQuestionView> ReSortQuestionOrderOrOptionOrder(string unFixedQuestion, List<PaperQuestionView> questionList, string questionType)
        {
            // 题目顺序打乱
            if (!PaperDynamicGeneratorUtils.CheckQuestionHasOrder(unFixedQuestion))
            {
                // 顺序打乱之前，先按照ID规整，以免重排序效果不佳
                questionList = [.. questionList.OrderBy(m => m.QuestionID)];
                questionList = PaperDynamicGeneratorUtils.CreateShuffledList(questionList);
            }
            // 选项有序返回
            var unOrderFlag = true;
            if (PaperDynamicGeneratorUtils.CheckQuestionOptionsHasOrder(unFixedQuestion))
            {
                unOrderFlag = false;
            }
            // 判断题不打乱选项顺序、设置前缀
            var setPrefixFlag = true;
            if (questionType == RULE_CODE_JUDGMENT_QUESTION)
            {
                setPrefixFlag = false;
            }
            // 选项顺序打乱
            ReSetOption(questionList, questionType, unOrderFlag, setPrefixFlag);
            return ReSetSort(questionList);
        }

        /// <summary>
        /// 选项重排序、设置选项前缀
        /// </summary>
        /// <param name="questionList">题目集合</param>
        /// <param name="questionType">题目类型</param>
        /// <param name="unOrderFlag">无序标志</param>
        /// <param name="setPrefixFlag">设置前缀标志</param>
        /// <returns></returns>
        private static List<PaperQuestionView> ReSetOption(List<PaperQuestionView> questionList, string questionType, bool unOrderFlag, bool setPrefixFlag)
        {
            if (questionList.Count < 1000)
            {
                for (int i = 0; i < questionList.Count; i++)
                {
                    // 非判断题、且无序
                    if (unOrderFlag && questionType != RULE_CODE_JUDGMENT_QUESTION)
                    {
                        PaperDynamicGeneratorUtils.CreateShuffledList(questionList[i].Options);
                    }
                    if (setPrefixFlag)
                    {
                        questionList[i].Options = SetQuestionOptionPrefix(questionList[i].Options).ToList();
                    }
                }
            }
            else
            {
                var parallelOptions = new ParallelOptions
                {
                    MaxDegreeOfParallelism = Environment.ProcessorCount
                };
                Parallel.ForEach(Partitioner.Create(0, questionList.Count), parallelOptions, range =>
                {
                    for (int i = range.Item1; i < range.Item2; i++)
                    {
                        if (unOrderFlag)
                        {
                            PaperDynamicGeneratorUtils.CreateShuffledList(questionList[i].Options);
                        }
                        if (setPrefixFlag)
                        {
                            questionList[i].Options = SetQuestionOptionPrefix(questionList[i].Options).ToList();
                        }
                    }
                });
            }
            return questionList;
        }

        /// <summary>
        /// 题目顺序打乱之后，重新调整题目序号
        /// </summary>
        /// <param name="questionList"></param>
        /// <returns></returns>
        private static List<PaperQuestionView> ReSetSort(List<PaperQuestionView> questionList)
        {
            for (int i = 0; i < questionList.Count; i++)
            {
                questionList[i].Sort = i + 1;
            }
            return questionList;
        }

        /// <summary>
        /// 设置选项字母前缀
        /// </summary>
        /// <param name="questionOptions"></param>
        /// <returns></returns>
        private static IEnumerable<PaperQuestionOptionsView> SetQuestionOptionPrefix(List<PaperQuestionOptionsView> questionOptions)
        {
            return questionOptions.Select((option, index) =>
            {
                return new PaperQuestionOptionsView
                {
                    Label = $"{PaperDynamicGeneratorUtils.GetMemoryCachedPrefix(index)}、{option.Label}",
                    Value = option.Value,
                    FixedItemID = option.FixedItemID,
                    ItemSourceType = option.ItemSourceType
                }
                ;
            });
        }

        #endregion
    }
}
