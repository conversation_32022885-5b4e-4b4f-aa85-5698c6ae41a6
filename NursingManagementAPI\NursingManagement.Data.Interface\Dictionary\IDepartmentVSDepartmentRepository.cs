﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDepartmentVSDepartmentRepository : ICacheRepository
    {
        /// <summary>
        /// 获取所有缓存
        /// </summary>
        /// <returns></returns>
        Task<List<DepartmentVSDepartmentInfo>> GetByCacheAsync();
        /// <summary>
        /// 根据组织架构类型获取部门配置
        /// </summary>
        /// <param name="organizationType">组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门</param>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        Task<List<DepartmentVSDepartmentInfo>> GetByOrganizationType(string organizationType , int departmentID);

        /// <summary>
        /// 根据双组织架构类型获取部门配置
        /// </summary>
        /// <param name="organizationType">组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门</param>
        /// <param name="departmentID"></param>
        /// <param name="organizationType2">组织架构类别 1：护理组织架构、2:委员会小组、3:HIS部门,6:医院HR部门</param>
        /// <returns></returns>
        Task<List<DepartmentVSDepartmentInfo>> GetByOrganizationTypeAndDepartmentID(string organizationType, int departmentID, string organizationType2);
    }
}
