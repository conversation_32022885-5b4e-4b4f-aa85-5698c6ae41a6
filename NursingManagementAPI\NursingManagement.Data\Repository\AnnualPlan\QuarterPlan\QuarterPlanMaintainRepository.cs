﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data
{
    public class QuarterPlanMaintainRepository(NursingManagementDbContext dbContext) : IQuarterPlanMaintainRepository
    {

        /// <summary>
        /// 获取季度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">部门ID</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        public async Task<string> GetQuarterPlanMainID(string annualPlanMainID, int quarter)
        {
            return await dbContext.QuarterPlanMainInfos.Where(m => m.AnnualPlanMainID == annualPlanMainID && m.Quarter == quarter && m.DeleteFlag != "*")
                .Select(m => m.QuarterPlanMainID)
                .FirstOrDefaultAsync();
        }
        /// <summary>
        /// 获取季度计划
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        public async Task<QuarterPlanMainInfo> GetQuarterPlanMain(string quarterPlanMainID)
        {
            return await dbContext.QuarterPlanMainInfos
                .FirstOrDefaultAsync(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 获取季度计划状态
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        public async Task<int> GetQuarterPlanStatus(string quarterPlanMainID)
        {
            return await dbContext.QuarterPlanMainInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
                .Select(m => m.StatusCode)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取季度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, Dictionary<int, string>>> GetQuarterToID(string[] annualPlanMainIDs)
        {
            return await dbContext.QuarterPlanMainInfos.Where(m => annualPlanMainIDs.Contains(m.AnnualPlanMainID) && m.DeleteFlag != "*")
                .GroupBy(m => m.AnnualPlanMainID)
                .ToDictionaryAsync(m => m.Key, m => m.ToDictionary(n => n.Quarter, n => n.QuarterPlanMainID));
        }
        /// <summary>
        /// 获取季度计划View
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        public async Task<TieredPlanWorksByType[]> GetQuarterWorks(string quarterPlanMainID)
        {
            var views = await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
                .GroupBy(m => m.TypeID)
                .Select(m => new TieredPlanWorksByType
                {
                    TypeID = m.Key,
                    Children = m.Select(n => new TieredPlanWork
                    {
                        Key = n.QuarterPlanDetailID,
                        APInterventionID = n.APInterventionID,
                        TypeID = n.TypeID,
                        Sort = n.Sort,
                        WorkContent = n.WorkContent,
                        WorkType = (byte)n.WorkType,
                        IsTemp = n.IsTemp,
                        Requirement = n.Requirement,
                        PrincipalIDs = n.PrincipalIDs,
                        PrincipalGroupName = n.PrincipalGroupName,
                    }).OrderBy(m => m.Sort.HasValue ? 0 : 1).ThenBy(m => m.Sort).ToArray()
                }).ToArrayAsync();
            return views;
        }

        /// <summary>
        /// 获取工作ID与工作内容
        /// </summary>
        /// <param name="workIDs">工作ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs)
        {
            return await dbContext.QuarterPlanDetailInfos.Where(m => m.DeleteFlag != "*" && workIDs.Contains(m.QuarterPlanDetailID))
                .ToDictionaryAsync(m => m.QuarterPlanDetailID, m => m.WorkContent);
        }

        /// <summary>
        /// 获取多个部门的季度计划工作
        /// </summary>
        /// <param name="apMainIDs">年度计划主表ID</param>
        /// <param name="refInterventionIDs">已参考的执行项目字典ID</param>
        /// <param name="onlyPriority">是否仅获取重点工作</param>
        /// <returns></returns>
        public async Task<List<TieredPlanWorksByPlanThenType>> GetWorkViews(string[] apMainIDs, int? includeApInterventionID = null, int[] excludeApInterventionIDs = null, bool onlyPriority = false, bool includeTempWork = true)
        {
            // 表达式组装器，业务解释：
            // 零、以 true作为表达式开始，组装完结构大致为：(true && condition1 && condition2) || condition3
            // 一、若有 excludeApInterventionIDs，说明要依据 interventionID排除部分工作
            // 二、若有 includeApInterventionID，说明要依据 interventionID筛选指定字典工作
            // 三、前两个筛选完，临时标记（IsTemp）为 true的也要加进来
            var apInterventionPredicate = ExpBuilder.True<QuarterPlanDetailInfo>()
                .IfAnd((excludeApInterventionIDs?.Length ?? 0) > 0, m => m.APInterventionID.HasValue && !excludeApInterventionIDs.Contains(m.APInterventionID.Value))
                .IfAnd(includeApInterventionID.HasValue, m => m.APInterventionID == includeApInterventionID.Value);
            var workPredicate = apInterventionPredicate.IfOr(includeTempWork, m => m.IsTemp);

            var workViews = await dbContext.QuarterPlanDetailInfos.Where(m => m.DeleteFlag != "*").Where(workPredicate)
                .Join(dbContext.QuarterPlanMainInfos.Where(m => m.DeleteFlag != "*" && apMainIDs.Contains(m.AnnualPlanMainID) && m.StatusCode == 1), m => m.QuarterPlanMainID, m => m.QuarterPlanMainID, (detail, main) => new
                {
                    main.AnnualPlanMainID,
                    detail,
                })
                .GroupBy(m => m.AnnualPlanMainID)
                .ToDictionaryAsync(m => m.Key, m => m.ToList().Select(o => new TieredPlanWork
                {
                    APInterventionID = o.detail.APInterventionID,
                    WorkContent = o.detail.WorkContent,
                    TypeID = o.detail.TypeID,
                    Requirement = o.detail.Requirement,
                    WorkType = (byte)o.detail.WorkType,
                    IsTemp = o.detail.IsTemp
                }));
            return workViews.Select(pair =>
            {
                var typeGroup = pair.Value.GroupBy(m => m.TypeID);
                return new TieredPlanWorksByPlanThenType
                {
                    PlanMainID = pair.Key,
                    Children = typeGroup.Select(m => new TieredPlanWorksByType
                    {
                        TypeID = m.Key,
                        Children = [.. m]
                    }).ToArray()
                };
            }).ToList();
        }
        /// <summary>
        /// 获取季度计划工作（供月度计划使用）
        /// </summary>
        /// <param name="annualPlanMainID">年度计划</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        public async Task<TieredPlanWorksByPlanThenType> GetQuarterPlanWorksForMonthlyPlan(string annualPlanMainID, int quarter)
        {
            var main = await dbContext.QuarterPlanMainInfos.Where(m => m.DeleteFlag != "*" && m.AnnualPlanMainID == annualPlanMainID && m.StatusCode == 1 && m.Quarter == quarter)
                .Select(m => new TieredPlanWorksByPlanThenType
                {
                    PlanMainID = m.AnnualPlanMainID,
                    QuarterMainID = m.QuarterPlanMainID
                }).FirstOrDefaultAsync() ?? throw new CustomException("无法导入，请先维护或发布季度计划！");
            var detailsGroupByType = await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == main.QuarterMainID && m.DeleteFlag != "*")
                .GroupBy(m => m.TypeID)
                .Select(m => new TieredPlanWorksByType
                {
                    TypeID = m.Key,
                    Children = m.Select(n => new TieredPlanWork
                    {
                        APInterventionID = n.APInterventionID,
                        WorkContent = n.WorkContent,
                        TypeID = n.TypeID,
                        Requirement = n.Requirement,
                        WorkType = (byte)n.WorkType,
                        IsTemp = n.IsTemp
                    }).ToArray()
                }).ToArrayAsync();
            main.Children = detailsGroupByType;
            return main;
        }
        /// <summary>
        /// 获取季度计划工作
        /// </summary>
        /// <param name="workIDs">工作ID集合</param>
        /// <returns></returns>
        public async Task<List<QuarterPlanDetailInfo>> GetQuarterPlanWorks(string[] workIDs)
        {
            return await dbContext.QuarterPlanDetailInfos.Where(m => m.DeleteFlag != "*" && workIDs.Contains(m.QuarterPlanDetailID)).ToListAsync();
        }
        /// <summary>
        /// 获取季度计划工作
        /// </summary>
        /// <param name="qpDetailID">主键</param>
        /// <returns></returns>
        public async Task<QuarterPlanDetailInfo> GetQuarterWork(string qpDetailID)
        {
            return await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanDetailID == qpDetailID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取某季度计划工作关联字典ID集合
        /// </summary>
        /// <param name="qpMainID">季度计划主键</param>
        /// <param name="quarter">季度</param>
        /// <returns></returns>
        public async Task<int[]> GetQpWorkInterventionIDs(string qpMainID, int quarter)
        {
            if (string.IsNullOrEmpty(qpMainID))
            {
                return [];
            }
            return await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == qpMainID && m.APInterventionID.HasValue && m.DeleteFlag != "*")
                .Select(m => m.APInterventionID.Value).Distinct().ToArrayAsync();
        }
        /// <summary>
        /// 获取季度计划工作关联字典ID集合
        /// </summary>
        /// <param name="year">年</param>
        /// <param name="departmentIDs">病区集合</param>
        /// <returns></returns>
        public async Task<List<QuarterPlanBrowseView>> GetQuarterPlanMainViewsByYearAndDepartmentIDs(int year, IEnumerable<int> departmentIDs)
        {
            return await (from a in dbContext.QuarterPlanMainInfos
                          where a.Year == year && departmentIDs.Contains(a.DepartmentID) && a.DeleteFlag != "*"
                          select new QuarterPlanBrowseView
                          {
                              MainID = a.QuarterPlanMainID,
                              StatusCode = a.StatusCode,
                              Year = a.Year,
                              Quarter = a.Quarter,
                              DepartmentID = a.DepartmentID,
                              Planner = a.AddEmployeeID,
                              AddDateTime = a.AddDateTime,
                          }).ToListAsync();
        }
        /// <summary>
        /// 获取导出视图
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <returns></returns>
        public async Task<QuarterPlanExportView> GetExportView(string quarterPlanMainID)
        {
            var data = await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
                .GroupBy(m => m.TypeID)
                .Select(m => new QuarterPlanExportView.PlanType
                {
                    TypeID = m.Key,
                    PlanWorks = m.Select(n => new QuarterPlanExportView.PlanType.PlanWork
                    {
                        WorkType = n.WorkType,
                        WorkContent = n.WorkContent,
                        Requirement = n.Requirement,
                        PrincipalIDs = n.PrincipalIDs
                    }).ToArray()
                }).ToArrayAsync();
            return new QuarterPlanExportView
            {
                QuarterPlanMainID = quarterPlanMainID,
                PlanTypes = data
            };
        }
        /// <summary>
        /// 获取季度计划预览视图
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划ID</param>
        /// <returns></returns>
        public async Task<QuarterPlanPreview> GetQuarterPlanPreview(string quarterPlanMainID)
        {
            var data = await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
                .GroupBy(m => m.TypeID)
                .Select(m => new QuarterPlanPreview.PlanType
                {
                    TypeId = m.Key,
                    PlanWorks = m.Select(n => new QuarterPlanPreview.PlanType.PlanWork
                    {
                        WorkContent = n.WorkContent,
                        Requirement = n.Requirement,
                        PrincipalIDs = n.PrincipalIDs,
                        PrincipalName = n.PrincipalGroupName,
                        WorkType = n.WorkType
                    }).ToArray()
                }).ToArrayAsync();
            return new QuarterPlanPreview
            {
                QuarterPlanMainID = quarterPlanMainID,
                PlanTypes = data
            };
        }
    }
}
