﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 审批记录控制器
    /// </summary>
    [Route("api/ApproveRecord")]
    [EnableCors("any")]
    public class ApproveRecordController : Controller
    {
        private readonly IApproveRecordService _approveRecordService;
        private readonly IApproveProcessService _approveProcessService;
        private readonly ISessionService _session;

        /// <summary>
        /// 审批流程控制器
        /// </summary>
        /// <param name="approveRecordService"></param>
        /// <param name="session"></param>
        /// <param name="approveProcessService"></param>
        public ApproveRecordController(
            IApproveRecordService approveRecordService
            , ISessionService session
            , IApproveProcessService approveProcessService
            )
        {
            _approveRecordService = approveRecordService;
            _session = session;
            _approveProcessService = approveProcessService;
        }
        /// <summary>
        /// 获取当前用户待审批的审批记录
        /// </summary>
        /// <param name="proveCategory">审批类别码</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApproveRecordView")]
        public async Task<IActionResult> GetApproveRecordView(string proveCategory, DateTime? startDate, DateTime? endDate)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.GetApproveRecordViewAsync(session.EmployeeID, session.DepartmentID, proveCategory, startDate, endDate);
            return result.ToJson();
        }
        /// <summary>
        /// 获取历史审批记录（已经审批结束的）
        /// </summary>
        /// <param name="proveCategory">审批类别码</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetHistoryApproveRecordView")]
        public async Task<IActionResult> GetHistoryApproveRecordView(string proveCategory, DateTime? startDate, DateTime? endDate)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.GetHistoryApproveRecordByEmployeeIDAsync(session.EmployeeID, proveCategory, session.DepartmentID, startDate, endDate);
            return result.ToJson();
        }
        /// <summary>
        /// 根据审批主记录获取呈现的审批明细内容
        /// </summary>
        /// <param name="approveMainRecordID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApproveDetailView")]
        public async Task<IActionResult> GetApproveDetailView(string approveMainRecordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.GetApproveDetailView(approveMainRecordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存审批内容
        /// </summary>
        /// <param name="approveParams"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveApproval")]
        public async Task<IActionResult> SaveApproval([FromBody] ApproveDetailInfo approveParams)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.SaveApprovalAsync(approveParams, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存审批内容 2023-12-10暂无调用
        /// </summary>
        /// <param name="sourceID">发起停止审批的记录主键ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("StopApproval")]
        public async Task<IActionResult> StopApproval(string sourceID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.StopApprovalAsync(sourceID, session.EmployeeID, true);
            return result.ToJson();
        }
        /// <summary>
        /// 撤销审批
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("RevokeApproval")]
        public async Task<IActionResult> RevokeApprovalAsync([FromBody] ApprovalRevokeParamsView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.RevokeApprovalAsync(view, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取未审批的记录（主页呈现待办内容使用）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUnApprovalRecord")]
        public async Task<IActionResult> GetUnApprovalRecord()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.GetUnApprovalRecordAsync(session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取审批类型及相关审批记录数量
        /// </summary>
        /// <param name="isCompleteFlag">是否已审批</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApproveCategoryAndCount")]
        public async Task<IActionResult> GetApproveCategoryAndCount(bool isCompleteFlag, DateTime? startDate, DateTime? endDate)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveRecordService.GetApproveCategoryAndCount(session.EmployeeID, session.DepartmentID, isCompleteFlag, startDate, endDate);
            return result.ToJson();
        }
        /// <summary>
        /// 手工提交审批
        /// </summary>
        /// <param name="approve"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ManualSubmissionApprove")]
        public async Task<IActionResult> ManualSubmissionApprove([FromBody] ApproveRequestView approve)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _approveProcessService.ManualSubmissionApproveAsync(approve.ApproveType, approve.RecordID);
            return result.ToJson();
        }
    }
}

