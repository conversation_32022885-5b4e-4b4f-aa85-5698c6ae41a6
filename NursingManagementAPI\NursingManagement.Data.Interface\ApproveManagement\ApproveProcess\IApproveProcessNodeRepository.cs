﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 审批流程节点仓储接口
    /// </summary>
    public interface IApproveProcessNodeRepository
    {
        /// <summary>
        /// 查询审批流程节点列表
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        Task<List<ApproveProcessNodeInfo>> GetApproveProcessNodeInfos(string approveProcessID);
        /// <summary>
        /// 查询审批流程节点列表
        /// </summary>
        /// <param name="approveProcessID">审批流程ID</param>
        /// <returns></returns>
        Task<List<ApproveProcessNode>> GetApproveProcessNodeViews(string approveProcessID);
    }
}
