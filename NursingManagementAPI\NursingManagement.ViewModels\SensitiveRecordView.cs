﻿namespace NursingManagement.ViewModels
{
    public class SensitiveRecordView
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string PatientProfileRecordID { get; set; }
        /// <summary>
        /// 质控主记录ID
        /// </summary>
        public string HierarchicalQCRecordID { get; set; }
        /// <summary>
        /// 质控主表ID
        /// </summary>
        public string HierarchicalQCMainID { get; set; }
        /// <summary>
        /// 质控评估ID
        /// </summary>
        public int HierarchicalQCAssessListID { get; set; }
        /// <summary>
        /// 评估日期
        /// </summary>
        public DateTime AssessDate { get; set; }
        /// <summary>
        /// 科室ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 组号
        /// </summary>
        public int GroupID { get; set; }
        /// <summary>
        /// 父ID
        /// </summary>
        public int ParentID { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string GuiDance { get; set; }
        /// <summary>
        /// 评估人
        /// </summary>
        public string AssessEmployID { get; set; }
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 床号
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 责任护士
        /// </summary>
        public string NurseEmployeeID { get; set; }
        /// <summary>
        /// 敏感记录时间
        /// </summary>
        public DateTime SupervisionRecordTime { get; set; }
        /// <summary>
        /// 来源ID
        /// </summary>
        public string SourceID { get; set; }
    }
}
