﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class AnnualSettingService : IAnnualSettingService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualIndicatorListRepository _annualIndicatorListRepository;
        private readonly IInterventionListRepository _annualInterventionListRepository;
        private readonly IAnnualGoalListRepository _goalListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDictionaryService _dictionaryService;
        private readonly IAnnualPlanMainRepository _annualPlanMainRepository;
        private readonly IAnnualPlanMainGoalRepository _mainGoalRepository;
        private readonly IAnnualPlanGoalGroupRepository _annualPlanGoalGroupRepository;
        private readonly IAnnualPlanTypeListRepository _typeListRepository;
        private readonly IDepartmentListRepository _departmentListRepository;

        #region 常量
        /// <summary>
        /// 护理部部门ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;
        #endregion

        public AnnualSettingService(
            IAnnualIndicatorListRepository annualIndicatorListRepository
            , IUnitOfWork unitOfWork
            , IInterventionListRepository projectListRepository
            , IAnnualGoalListRepository goalListRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IDictionaryService dictionaryService
            , IAnnualPlanMainRepository annualPlanMainRepository
            , IAnnualPlanMainGoalRepository mainGoalRepository
            , IAnnualPlanGoalGroupRepository annualPlanGoalGroupRepository
            , IAnnualPlanTypeListRepository typeListRepository
            , IDepartmentListRepository departmentListRepository
            )
        {
            _annualIndicatorListRepository = annualIndicatorListRepository;
            _unitOfWork = unitOfWork;
            _annualInterventionListRepository = projectListRepository;
            _goalListRepository = goalListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _dictionaryService = dictionaryService;
            _annualPlanMainRepository = annualPlanMainRepository;
            _mainGoalRepository = mainGoalRepository;
            _annualPlanGoalGroupRepository = annualPlanGoalGroupRepository;
            _typeListRepository = typeListRepository;
            _departmentListRepository = departmentListRepository;
        }
        #region 指标字典
        /// <summary>
        /// 获取当前科室的指标字典
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="showUpperIndicator">是否呈现连续上级指标</param>
        /// <returns></returns>
        public async Task<List<AnnualIndicatorListView>> GetAnnualIndicatorList(int departmentID, bool showUpperIndicator)
        {
            var annualIndicatorList = await _annualIndicatorListRepository.GetAll<AnnualIndicatorListInfo>();
            var listViews = annualIndicatorList.Where(m => m.DepartmentID == departmentID).Select(m => ConvertToView(m, level: "current"));

            if (showUpperIndicator)
            {
                // 获取上级科室ID
                var upperDepartmentIDs = await _dictionaryService.GetUpperDepts<int, HashSet<int>>(departmentID, m => m.DepartmentID);
                if (departmentID != DEPARTMENT_ID_405)
                {
                    upperDepartmentIDs.Add(DEPARTMENT_ID_405);
                }
                if (upperDepartmentIDs.Count != 0)
                {
                    var upperDeptIndicators = annualIndicatorList.Where(m => upperDepartmentIDs.Contains(m.DepartmentID)).Select(m => ConvertToView(m, "upper"));
                    listViews = listViews.Concat(upperDeptIndicators);
                }
            }
            var data = listViews.ToList();
            var employeeIDs = data.Select(m => m.AddEmployeeID).Concat(data.Select(m => m.ModifyEmployeeID)).Distinct().ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            foreach (var view in data)
            {
                view.AddEmployeeName = employees.TryGetValue(view.AddEmployeeID, out var addEmployeeName) ? addEmployeeName : string.Empty;
                view.ModifyEmployeeName = employees.TryGetValue(view.ModifyEmployeeID, out var modifyEmployeeName) ? modifyEmployeeName : string.Empty;
            }
            return data;
        }
        /// <summary>
        /// 转换为View
        /// </summary>
        /// <param name="annualIndicatorListInfo">指标字典Model</param>
        /// <param name="level">上级还是本级</param>
        /// <returns></returns>
        private static AnnualIndicatorListView ConvertToView(AnnualIndicatorListInfo annualIndicatorListInfo, string level)
        {
            return new AnnualIndicatorListView
            {
                AnnualIndicatorID = annualIndicatorListInfo.AnnualIndicatorID,
                IndicatorContent = annualIndicatorListInfo.IndicatorContent,
                DepartmentID = annualIndicatorListInfo.DepartmentID,
                EnableYear = annualIndicatorListInfo.EnableYear,
                AddEmployeeID = annualIndicatorListInfo.AddEmployeeID,
                ModifyEmployeeID = annualIndicatorListInfo.ModifyEmployeeID,
                AddDateTime = annualIndicatorListInfo.AddDateTime,
                ModifyDateTime = annualIndicatorListInfo.ModifyDateTime,
                Level = level,
                IsNewIndicator = annualIndicatorListInfo.EnableYear == DateTime.Now.Year,
            };
        }
        /// <summary>
        /// 保存指标字典
        /// </summary>
        /// <param name="listView"></param>
        /// <returns></returns>
        public async Task<(bool, int)> SaveAnnualIndicatorList(AnnualIndicatorListView listView)
        {
            bool resultFlag;
            int indicatorID = 0;
            if (listView.AnnualIndicatorID == 0)
            {
                (resultFlag, indicatorID) = await InsertAnnualIndicatorList(listView);
            }
            else
            {
                resultFlag = await UpdateAnnualIndicatorList(listView);
            }

            if (!resultFlag)
            {
                return (true, 0);
            }
            await _unitOfWork.SaveChangesAsync();
            await _annualIndicatorListRepository.UpdateCache();
            return (true, indicatorID);
        }

        /// <summary>
        /// 新增指标字典
        /// </summary>
        /// <param name="listView"></param>
        /// <returns></returns>
        private async Task<(bool, int)> InsertAnnualIndicatorList(AnnualIndicatorListView listView)
        {
            var annualIndicatorListInfo = new AnnualIndicatorListInfo
            {
                IndicatorContent = listView.IndicatorContent,
                DepartmentID = listView.DepartmentID,
                EnableYear = listView.EnableYear,
                HospitalID = listView.HospitalID,
                Language = listView.Language,
                AnnualIndicatorID = await _annualIndicatorListRepository.GetMaxID() + 1
            };
            annualIndicatorListInfo.Add(listView.EmployeeID).Modify(listView.EmployeeID);
            await _unitOfWork.GetRepository<AnnualIndicatorListInfo>().InsertAsync(annualIndicatorListInfo);
            return (true, annualIndicatorListInfo.AnnualIndicatorID);
        }
        /// <summary>
        /// 更新指标字典
        /// </summary>
        /// <param name="annualIndicatorListView"></param>
        /// <returns></returns>
        private async Task<bool> UpdateAnnualIndicatorList(AnnualIndicatorListView annualIndicatorListView)
        {
            var annualIndicatorListInfo = await _annualIndicatorListRepository.GetInfoByID(annualIndicatorListView.AnnualIndicatorID, true);
            if (annualIndicatorListInfo == null)
            {
                return false;
            }
            if (annualIndicatorListInfo.IndicatorContent == annualIndicatorListView.IndicatorContent)
            {
                return false;
            }
            annualIndicatorListInfo.IndicatorContent = annualIndicatorListView.IndicatorContent;
            annualIndicatorListInfo.Modify(annualIndicatorListView.EmployeeID);
            return true;
        }
        /// <summary>
        /// 删除指标字典
        /// </summary>
        /// <param name="annualIndicatorListView"></param>
        /// <returns></returns>
        public async Task<bool> DeleteAnnualIndicatorList(AnnualIndicatorListView annualIndicatorListView)
        {
            var annualIndicatorListInfo = await _annualIndicatorListRepository.GetInfoByID(annualIndicatorListView.AnnualIndicatorID, true);
            if (annualIndicatorListInfo == null)
            {
                return false;
            }
            annualIndicatorListInfo.Delete(annualIndicatorListView.EmployeeID);
            await _unitOfWork.SaveChangesAsync();
            await _annualIndicatorListRepository.UpdateCache();
            return true;
        }
        #endregion
        #region 项目字典
        /// <summary>
        /// 获取执行项目字典
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="showUpperIntervention">是否呈现上级执行项目</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionListView>> GetAnnualInterventionList(int departmentID, bool showUpperIntervention)
        {
            var annualInterventionList = await _annualInterventionListRepository.GetAll<InterventionListInfo>();
            var employeeIDToNameViews = await _employeePersonalDataRepository.GetIDAndNameData();
            var interventionListViews = annualInterventionList.Where(m => m.DepartmentID == departmentID).Select(m => new AnnualInterventionListView
            {
                AnnualInterventionID = m.InterventionID,
                InterventionContent = m.InterventionContent,
                AddEmployeeName = employeeIDToNameViews.Find(n => n.EmployeeID == m.AddEmployeeID)?.EmployeeName,
                ModifyEmployeeName = employeeIDToNameViews.Find(n => n.EmployeeID == m.ModifyEmployeeID)?.EmployeeName,
                AddDateTime = m.AddDateTime,
                ModifyDateTime = m.ModifyDateTime,
            }).ToList();
            if (showUpperIntervention)
            {
                var departmentIDs = await _dictionaryService.GetUpperDepts<int, HashSet<int>>(departmentID, m => m.DepartmentID);
                //获取本部门和上级或间接上级部门的年度计划分类，还有固定护理部的分类
                departmentIDs.Add(DEPARTMENT_ID_405);
                var upperInterventionListViews = annualInterventionList.Where(m => departmentIDs.Contains(m.DepartmentID)).Select(m => new AnnualInterventionListView
                {
                    AnnualInterventionID = m.InterventionID,
                    InterventionContent = m.InterventionContent,
                    AddEmployeeName = employeeIDToNameViews.Find(n => n.EmployeeID == m.AddEmployeeID)?.EmployeeName,
                    ModifyEmployeeName = employeeIDToNameViews.Find(n => n.EmployeeID == m.ModifyEmployeeID)?.EmployeeName,
                    AddDateTime = m.AddDateTime,
                    ModifyDateTime = m.ModifyDateTime,
                }).ToList();
                interventionListViews = interventionListViews.Concat(upperInterventionListViews).ToList();
            }
            return interventionListViews;
        }
        /// <summary>
        /// 保存年度计划执行项目字典数据
        /// </summary>
        /// <param name="saveView">保存View</param>
        /// <returns></returns>
        public async Task<bool> SaveAnnualInterventionList(APInterventionListSaveView saveView)
        {
            if (saveView.Interventions.Count == 0 && saveView.DeletedInterventionIDs.Count == 0)
            {
                return true;
            }
            foreach (var intervention in saveView.Interventions)
            {
                _ = intervention.AnnualInterventionID == 0 ? await InsertAnnualInterventionList(saveView, intervention) : await UpdateAnnualProjectList(saveView, intervention);
            }

            var projects = await _annualInterventionListRepository.GetInfosByIDsNoCache(saveView.DeletedInterventionIDs);
            projects.ForEach(m => m.Delete(saveView.EmployeeID));

            await _unitOfWork.SaveChangesAsync();
            await _annualInterventionListRepository.UpdateCache();
            return true;
        }
        /// <summary>
        /// 新增执行项目字典
        /// </summary>
        /// <param name="interventionSaveView">保存View</param>
        /// <param name="projectContent">执行项目内容</param>
        /// <param name="newID">措施ID</param>
        /// <returns></returns>
        public async Task<int> AddProjectList(AnnualInterventionSaveView interventionSaveView, string projectContent, int newID)
        {
            var annualInterventionListInfo = new InterventionListInfo
            {
                InterventionID = newID,
                InterventionContent = projectContent,
                DepartmentID = interventionSaveView.DepartmentID,
                Language = interventionSaveView.Language,
                HospitalID = interventionSaveView.HospitalID,
            };
            annualInterventionListInfo.Add(interventionSaveView.EmployeeID).Modify(interventionSaveView.EmployeeID);
            await _unitOfWork.GetRepository<InterventionListInfo>().InsertAsync(annualInterventionListInfo);
            await _unitOfWork.SaveChangesAsync();
            return newID;
        }
        /// <summary>
        /// 新增项目字典
        /// </summary>
        /// <param name="saveView">保存VIew</param>
        /// <param name="interventionListView">执行项目View</param>
        /// <returns></returns>
        private async Task<bool> InsertAnnualInterventionList(APInterventionListSaveView saveView, AnnualInterventionListView interventionListView)
        {
            var annualProjectListInfo = new InterventionListInfo
            {
                InterventionContent = interventionListView.InterventionContent,
                DepartmentID = saveView.DepartmentID,
                HospitalID = saveView.HospitalID,
                Language = saveView.Language,
                InterventionID = (await _annualInterventionListRepository.GetMaxID() + 1)
            };
            annualProjectListInfo.Add(saveView.EmployeeID).Modify(saveView.EmployeeID);
            await _unitOfWork.GetRepository<InterventionListInfo>().InsertAsync(annualProjectListInfo);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }
        /// <summary>
        /// 更新项目字典
        /// </summary>
        /// <param name="saveView"></param>
        /// <returns></returns>
        private async Task<bool> UpdateAnnualProjectList(APInterventionListSaveView saveView, AnnualInterventionListView interventionListView)
        {
            var oldIntervention = await _annualInterventionListRepository.GetInfoByIDNoCache(interventionListView.AnnualInterventionID);
            if (oldIntervention == null)
            {
                return false;
            }
            var isUpdate = false;
            if (oldIntervention.InterventionContent != interventionListView.InterventionContent)
            {
                oldIntervention.InterventionContent = interventionListView.InterventionContent;
                isUpdate = true;
            }
            if (isUpdate)
            {
                oldIntervention.Modify(saveView.EmployeeID);
            }
            return true;
        }
        #endregion
        #region 目标字典
        /// <summary>
        /// 获取目标字典
        /// </summary>
        /// <param name="goalIds">目标Id集合</param>
        /// <returns></returns>
        public async Task<List<AnnualGoalListInfo>> GetGoalList(int[] goalIds)
        {
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();
            return goalList.FindAll(m => goalIds.Contains(m.AnnualGoalID));
        }
        /// <summary>
        /// 更新目标字典名称
        /// </summary>
        /// <param name="goalID">目标字典ID</param>
        /// <param name="content">目标名称</param>
        /// <param name="userID">工号</param>
        /// <returns></returns>
        public async Task<bool> UpdateGoalContent(int goalID, string content, string userID)
        {
            var goalListInfo = await _goalListRepository.GetGoalListInfoNoCache(goalID);
            if (goalListInfo == null)
            {
                return false;
            }
            goalListInfo.GoalContent = content;
            goalListInfo.Modify(userID);

            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await _goalListRepository.UpdateCache();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 保存年度计划分类-目标
        /// </summary>
        /// <param name="mainGoalView">保存参数</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> SaveAnnualPlanMainGoal(AnnualPlanMainGoalView mainGoalView, Session session)
        {
            if (mainGoalView == null)
            {
                _logger.Error("年度计划目标分类维护失败，前端传递参数为空！");
                return false;
            }
            var annualPlanMainID = await _annualPlanMainRepository.GetMainIDByDeptIDAndYear(mainGoalView.DepartmentID, mainGoalView.Year);
            if (string.IsNullOrEmpty(annualPlanMainID))
            {
                _logger.Error($"未获取到该部门年度计划，DepartmentID：{mainGoalView.DepartmentID}，Year：{mainGoalView.Year}");
                return false;
            }
            var annualPlanMainGoalList = await _mainGoalRepository.GetInfosByPlanMainID(annualPlanMainID, true);
            if (string.IsNullOrEmpty(mainGoalView.AnnualPlanMainGoalID))
            {
                //新增
                await AddAnnualPlanMainGoal(mainGoalView, session, annualPlanMainGoalList, annualPlanMainID);
            }
            else
            {
                //修改
                if (!await ModifyAnnualPlanMainGoal(mainGoalView, session))
                {
                    return false;
                }
            }
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                //更新缓存
                await _goalListRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 新增年度计划分类目标
        /// </summary>
        /// <param name="mainGoalView">保存参数</param>
        /// <param name="session">缓存</param>
        /// <param name="annualPlanMainGoalList">年度计划分类-目标集合</param>
        /// <param name="annualPlanMainID">年度计划ID</param>
        /// <returns></returns>
        private async Task AddAnnualPlanMainGoal(AnnualPlanMainGoalView mainGoalView, Session session, List<AnnualPlanMainGoalInfo> annualPlanMainGoalList, string annualPlanMainID)
        {
            // 添加新目标
            var goalID = await _goalListRepository.GetMaxGoalID();
            var insertGoal = new AnnualGoalListInfo()
            {
                AnnualGoalID = goalID,
                HospitalID = session.HospitalID,
                Language = session.Language,
                DepartmentID = mainGoalView.DepartmentID,
                GoalContent = mainGoalView.AnnualPlanGoalContent,
            };
            insertGoal.Add(session.EmployeeID);
            insertGoal.Modify(session.EmployeeID);
            await _unitOfWork.GetRepository<AnnualGoalListInfo>().InsertAsync(insertGoal);
            var newSort = 1;
            var annualPlanMainGoals = annualPlanMainGoalList.Where(m => m.AnnualPlanTypeID == mainGoalView.AnnualPlanTypeID).ToList();
            if (annualPlanMainGoals.Count > 0)
            {
                newSort = annualPlanMainGoals.Max(m => m.Sort) + 1;
            }
            // 后续目标排序依次+1
            annualPlanMainGoalList.Where(m => m.Sort >= newSort).OrderBy(m => m.Sort).ToList().ForEach(m => m.Sort++);
            //添加年度计划分类-目标
            var insertAnnualPlanMainGoal = new AnnualPlanMainGoalInfo()
            {
                AnnualPlanMainID = annualPlanMainID,
                HospitalID = session.HospitalID,
                AnnualPlanTypeID = mainGoalView.AnnualPlanTypeID,
                AnnualGoalID = goalID,
                Sort = newSort,
                DeleteFlag = ""
            };
            insertAnnualPlanMainGoal.AnnualPlanMainGoalID = insertAnnualPlanMainGoal.GetId();
            insertAnnualPlanMainGoal.Add(session.EmployeeID);
            insertAnnualPlanMainGoal.Modify(session.EmployeeID);
            await _unitOfWork.GetRepository<AnnualPlanMainGoalInfo>().InsertAsync(insertAnnualPlanMainGoal);

        }
        /// <summary>
        /// 修改年度计划分类目标
        /// </summary>
        /// <param name="mainGoalView">保存参数</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        private async Task<bool> ModifyAnnualPlanMainGoal(AnnualPlanMainGoalView mainGoalView, Session session)
        {
            var goalInfo = await _goalListRepository.GetGoalListInfoNoCache(mainGoalView.AnnualPlanGoalID);
            if (goalInfo == null)
            {
                _logger.Error($"未找到年度计划对应目标，AnnualPlanGoalID：{mainGoalView.AnnualPlanGoalID}");
                return false;
            }
            //目标名称修改，调整对应字典
            if (goalInfo.GoalContent.Trim() != mainGoalView.AnnualPlanGoalContent.Trim())
            {
                goalInfo.GoalContent = mainGoalView.AnnualPlanGoalContent.Trim();
                goalInfo.Modify(session.EmployeeID);
            }
            var planMainGoals = await _mainGoalRepository.GetInfosByPlanMainID(mainGoalView.AnnualPlanMainID, true);
            var updateMainGoal = planMainGoals.Find(m => m.AnnualPlanMainGoalID == mainGoalView.AnnualPlanMainGoalID);
            if (updateMainGoal != null && updateMainGoal.AnnualPlanTypeID != mainGoalView.AnnualPlanTypeID)
            {
                updateMainGoal.AnnualPlanTypeID = mainGoalView.AnnualPlanTypeID;
                updateMainGoal.Modify(session.EmployeeID);
            }
            return true;
        }
        /// <summary>
        /// 删除年度计划分类-目标
        /// </summary>
        /// <param name="mainGoalID">主键ID</param>
        /// <param name="planMainID">计划主表ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> DeleteAnnualPlanMainGoal(string mainGoalID, string planMainID, Session session)
        {
            if (string.IsNullOrEmpty(mainGoalID))
            {
                _logger.Error("前端传递主键ID为空，删除年度计划分类-目标失败");
                return false;
            }
            var planMainGoals = await _mainGoalRepository.GetInfosByPlanMainID(planMainID, true);
            var delMainGoal = planMainGoals.Find(m => m.AnnualPlanMainGoalID == mainGoalID);
            delMainGoal?.Delete(session.EmployeeID);
            planMainGoals.Where(m => m.Sort > delMainGoal.Sort).OrderBy(m => m.Sort).ToList().ForEach(m => m.Sort--);

            var goalSetting = await _goalListRepository.GetGoalListInfoNoCache(delMainGoal.AnnualGoalID);
            goalSetting?.Delete(session.EmployeeID);
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                //更新缓存
                await _goalListRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 检核年度计划目标
        /// </summary>
        /// <param name="mainGoalView">待删除目标</param>
        /// <returns></returns>
        public async Task<Tuple<bool, string>> CheckAnnualPlanMainGoal(AnnualPlanMainGoalView mainGoalView)
        {
            var annualPlanMainGoal = await _mainGoalRepository.GetAnnualPlanMainGoalByMainGoalID(mainGoalView.AnnualPlanMainGoalID);
            if (annualPlanMainGoal == null)
            {
                _logger.Error($"未获取到记录，年度计划分类-目标获取失败，AnnualPlanMainGoalID：{mainGoalView.AnnualPlanMainGoalID}");
                return new Tuple<bool, string>(false, "未获取到记录！");
            }
            var goalSetting = await _goalListRepository.GetGoalListInfoNoCache(annualPlanMainGoal.AnnualGoalID);
            if (goalSetting != null && goalSetting.DepartmentID != mainGoalView.DepartmentID)
            {
                return new Tuple<bool, string>(false, "非本部门制定的目标，不可删除！");
            }
            var goalGroup = await _annualPlanGoalGroupRepository.GetAnnualPlanGroups(annualPlanMainGoal.AnnualPlanMainID, mainGoalView.AnnualPlanMainGoalID);
            if (goalGroup.Count > 0)
            {
                return new Tuple<bool, string>(false, "该目标下已存在指标与项目明细，不可删除！");
            }
            return new Tuple<bool, string>(true, "");
        }

        #endregion

        #region 年度计划分类维护
        /// <summary>
        /// 获取年度计划分类（本部门）
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanTypeListView>> GetAnnualPlanTypeList(int departmentID)
        {
            var listViews = new List<AnnualPlanTypeListView>();
            if (departmentID == 0)
            {
                _logger.Error("部门ID传递参数有误");
                return listViews;
            }
            listViews = await _typeListRepository.GetAnnualPlanTypeListView(departmentID);
            if (listViews.Count() <= 0)
            {
                _logger.Warn("未获取到年度计划分类字典数据");
                return listViews;
            }
            var employeeIDs = listViews.Select(m => m.AddEmployeeID).Concat(listViews.Select(m => m.ModifyEmployeeID)).Distinct().ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var departmentList = await _departmentListRepository.GetByOrganizationType("1");
            foreach (var listView in listViews)
            {
                listView.AddEmployeeName = employees.TryGetValue(listView.AddEmployeeID, out var addEmployeeName) ? addEmployeeName : string.Empty;
                listView.ModifyEmployeeName = employees.TryGetValue(listView.ModifyEmployeeID, out var modifyEmployeeName) ? modifyEmployeeName : string.Empty;
                listView.AddDepartmentName = departmentList.FirstOrDefault(m => m.DepartmentID == listView.DepartmentID)?.DepartmentContent ?? "";
            }
            return listViews;
        }

        /// <summary>
        /// 保存年度计划分类
        /// </summary>
        /// <param name="session">缓存</param>
        /// <param name="saveView">保存数据</param>
        /// <returns></returns>
        public async Task<bool> SaveAnnualPlanTypeList(Session session, AnnualPlanTypeListView saveView)
        {
            if (saveView.AnnualPlanTypeID == 0)
            {
                var insertTypeID = await _typeListRepository.GetAnnualPlanTypeMaxID();
                var typeInfo = new AnnualPlanTypeListInfo()
                {
                    AnnualPlanTypeID = insertTypeID,
                    HospitalID = session.HospitalID,
                    Language = session.Language,
                    DepartmentID = saveView.DepartmentID,
                    AnnualPlanTypeContent = saveView.AnnualPlanTypeContent.Trim(),
                    Description = "",
                };
                typeInfo.Add(session.EmployeeID);
                typeInfo.Modify(session.EmployeeID);
                await _unitOfWork.GetRepository<AnnualPlanTypeListInfo>().InsertAsync(typeInfo);
            }
            else
            {
                var annualPlanTypeInfo = await _typeListRepository.GetAnnualPlanTypeByID(saveView.AnnualPlanTypeID);
                annualPlanTypeInfo.AnnualPlanTypeContent = saveView.AnnualPlanTypeContent;
                annualPlanTypeInfo.Modify(session.EmployeeID);
            }
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                //更新缓存
                await _typeListRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 删除年度计划分类
        /// </summary>
        /// <param name="annualPlanType">分类信息</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> DeleteAnnualPlanType(AnnualPlanTypeListView annualPlanType, Session session)
        {
            if (annualPlanType == null)
            {
                _logger.Error("前端传递参数为空，删除年度计划分类失败");
                return false;
            }
            var annualPlanTypeInfo = await _typeListRepository.GetAnnualPlanTypeByID(annualPlanType.AnnualPlanTypeID);
            if (annualPlanTypeInfo == null)
            {
                _logger.Error($"未获取到相关年度计划分类，删除年度计划分类失败，AnnualPlanTypeID：{annualPlanType.AnnualPlanTypeID}");
                return false;
            }
            annualPlanTypeInfo.DeleteFlag = "*";
            annualPlanTypeInfo.Modify(session.EmployeeID);
            if (await _unitOfWork.SaveChangesAsync() >= 0)
            {
                //更新缓存
                await _typeListRepository.UpdateCache();
                return true;
            }
            return false;
        }
        /// <summary>
        /// 获取年度计划分类（本部门及上级、间接上级部门分类）
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanTypeListInfo>> GetAnnualPlanTypeListByDepartment(int departmentID)
        {
            var listViews = new List<AnnualPlanTypeListInfo>();
            if (departmentID == 0)
            {
                _logger.Error("部门ID传递参数有误");
                return listViews;
            }
            //获取年度计划字典
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            // 获取上级科室ID
            var upperDeptIDs = await _dictionaryService.GetUpperDepts<int, List<int>>(departmentID, m => m.DepartmentID);
            // 无上级科室，只返回本级科室指标即可
            if (!upperDeptIDs.Any())
            {
                return typeList.Where(m => m.DepartmentID == departmentID).ToList();
            }
            //获取本部门和上级或间接上级部门的年度计划分类，还有固定护理部的分类
            upperDeptIDs = upperDeptIDs.Prepend(departmentID).Prepend(DEPARTMENT_ID_405).ToList();
            return typeList.Where(m => upperDeptIDs.Contains(m.DepartmentID)).ToList();
        }
        /// <summary>
        /// 检核年度计划分类
        /// </summary>
        /// <param name="annualPlanTypeID">分类类别ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<Tuple<bool, string>> CheckAnnualPlanTypeList(int annualPlanTypeID, Session session)
        {
            var annualPlanTypeInfo = await _typeListRepository.GetAnnualPlanTypeByID(annualPlanTypeID);
            if (annualPlanTypeInfo == null)
            {
                _logger.Error($"未获取到记录，年度计划分类获取失败，AnnualPlanTypeID：{annualPlanTypeID}");
                return new Tuple<bool, string>(false, "未获取到记录！");
            }
            var goalGroup = await _mainGoalRepository.GetAnnualPlanMainGoalByAnnualPlanTypeID(annualPlanTypeInfo.AnnualPlanTypeID, annualPlanTypeInfo.HospitalID);
            if (goalGroup != null)
            {
                return new Tuple<bool, string>(false, "该分类下已存在目标，不可删除！");
            }
            return new Tuple<bool, string>(true, "");
        }
        /// <summary>
        /// 获取年度计划分类
        /// </summary>
        /// <param name="typeIds">分类Id集合</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanTypeListInfo>> GetTypeList(int[] typeIds)
        {
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            return typeList.FindAll(m => typeIds.Contains(m.AnnualPlanTypeID));
        }
        #endregion
    }
}
