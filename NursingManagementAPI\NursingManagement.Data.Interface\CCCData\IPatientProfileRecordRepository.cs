﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IPatientProfileRecordRepository
    {
        /// <summary>
        /// 根据sourceType和sourceID集合获取relatedTableRecordID集合
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceIDList"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        Task<List<Dictionary<string, string>>> GetRelatedTableRecordIDList(string sourceType, List<string> sourceIDList, string relatedTableName);

        /// <summary>
        /// 根据relatedTableName和relatedTableID获取数据
        /// </summary>
        /// <param name="relatedTableName"></param>
        /// <param name="relatedTableID"></param>
        /// <returns></returns>
        Task<PatientProfileRecordInfo> GetRecordByRelatedTableID(string relatedTableName, string relatedTableID);

        /// <summary>
        /// 根据sourceID获取患者状况ID
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="souceType"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        Task<List<PatientProfileRecordInfo>> GetRecordDataBySourceID(string sourceID, string souceType, string relatedTableName);

        /// <summary>
        /// 根据主键集合获取数据
        /// </summary>
        /// <param name="recordIDList"></param>
        /// <returns></returns>
        Task<List<PatientProfileRecordInfo>> GetRecordDataByRecordIDList(List<string> recordIDList);

        /// <summary>
        /// 根据sourceID获取患者访视记录主键集合
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="souceType"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        Task<List<string>> GetRecordIDBySourceID(string sourceID, string souceType, string relatedTableName);

        /// <summary>
        /// 根据sourceID获取患者状况
        /// </summary>
        /// <param name="sourceID"></param>
        /// <param name="souceType"></param>
        /// <param name="relatedTableName"></param>
        /// <returns></returns>
        Task<List<SensitiveRecordView>> GetRecordDataBySourceIDAndType(List<string> sourceIDs, string souceType, string relatedTableName);

        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        Task<PatientProfileRecordInfo> GetRecordDataByRecordIDAsync(string recordID);

        /// <summary>
        /// 根据时间范围和ProfileID获取记录数据
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="profileID"></param>
        /// <returns></returns>
        Task<List<string>> GetRecordDataByTimeRangeAsync(DateTime startTime, DateTime endTime, int profileID);
        /// <summary>
        /// 获取忽略记录数据
        /// </summary>
        /// <param name="chartNoList"></param>
        /// <returns></returns>
        Task<List<string>> GetIgnoreRecordDataByChartNo(List<string> chartNoList);
    }
}
