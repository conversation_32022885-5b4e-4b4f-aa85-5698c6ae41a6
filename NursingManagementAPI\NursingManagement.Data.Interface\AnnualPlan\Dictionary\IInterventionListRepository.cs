﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划项目字典
    /// </summary>
    public interface IInterventionListRepository : ICacheRepository
    {
        /// <summary>
        /// 获取最大项目ID
        /// </summary>
        /// <returns></returns>
        Task<int> GetMaxID();

        /// <summary>
        /// 根据ID获取一条数据，更新使用
        /// </summary>
        /// <param name="interventionID">项目字典序号</param>
        /// <returns></returns>
        Task<InterventionListInfo> GetInfoByIDNoCache(int interventionID);
        /// <summary>
        /// 获取多条数据
        /// </summary>
        /// <param name="interventionIDs">字典ID集合</param>
        /// <returns></returns>
        Task<List<InterventionListInfo>> GetInfosByIDsNoCache(List<int> interventionIDs);
    }
}
