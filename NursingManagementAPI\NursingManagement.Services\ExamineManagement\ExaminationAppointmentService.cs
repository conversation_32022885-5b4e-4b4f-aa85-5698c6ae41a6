﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;
using static NursingManagement.Common.Enums;

namespace NursingManagement.Services
{
    public class ExaminationAppointmentService : IExaminationAppointmentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IExaminationAppointmentRepository _examinationAppointmentRepository;
        private readonly IExaminerScheduleRepository _examinerScheduleRepository;
        private readonly IExaminerScheduleItemRepository _examinerScheduleItemRepository;
        private readonly IExaminerScheduleEmployeeRepository _examinerScheduleEmployeeRepository;
        private readonly IExaminationMainRepository _examinationMainRepository;
        private readonly IExaminationRecordRepository _examinationRecordRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IRouterListRepository _routerListRepository;
        private readonly IMessageService _messageService;
        private readonly IExaminerRepository _examinerRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;

        /// <summary>
        /// 完成预约
        /// </summary>
        private const string COMPLETE_APPOINTMENT = "1";
        /// <summary>
        /// 取消预约
        /// </summary>
        private const string CANCEL_APPOINTMENT = "2";
        /// <summary>
        /// 预约状态
        /// </summary>
        private static readonly string STATUSCODE_APPOINTMENT = "7";
        /// <summary>
        /// 待考核状态
        /// </summary>
        private static readonly string STATUSCODE_EVALUATION_PENDING = "1";
        /// <summary>
        /// 监考计划未被预约
        /// </summary>
        private static readonly string EXAMINER_SCHEDULE_STATUS_0 = "0";
        /// <summary>
        /// 监考计划已被预约
        /// </summary>
        private static readonly string EXAMINER_SCHEDULE_STATUS_1 = "1";

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="examinationAppointmentRepository"></param>
        /// <param name="examinerScheduleRepository"></param>
        /// <param name="examinerScheduleItemRepository"></param>
        /// <param name="examinerScheduleEmployeeRepository"></param>
        /// <param name="examinationMainRepository"></param>
        /// <param name="examinationRecordRepository"></param>
        /// <param name="employeePersonalDataRepository"></param>
        /// <param name="messageService"></param>
        /// <param name="routerListRepository"></param>
        /// <param name="examinerRepository"></param>
        /// <param name="employeeStaffDataRepository"></param>
        /// <param name="departmentListRepository"></param>
        /// <param name="settingDictionaryRepository"></param>
        public ExaminationAppointmentService(
            IUnitOfWork unitOfWork,
            IExaminationAppointmentRepository examinationAppointmentRepository,
            IExaminerScheduleRepository examinerScheduleRepository,
            IExaminerScheduleItemRepository examinerScheduleItemRepository,
            IExaminerScheduleEmployeeRepository examinerScheduleEmployeeRepository,
            IExaminationMainRepository examinationMainRepository,
            IExaminationRecordRepository examinationRecordRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IMessageService messageService,
            IRouterListRepository routerListRepository,
            IExaminerRepository examinerRepository,
            IEmployeeStaffDataRepository employeeStaffDataRepository,
            IDepartmentListRepository departmentListRepository,
            ISettingDictionaryRepository settingDictionaryRepository
        )
        {
            _unitOfWork = unitOfWork;
            _examinationAppointmentRepository = examinationAppointmentRepository;
            _examinerScheduleRepository = examinerScheduleRepository;
            _examinerScheduleItemRepository = examinerScheduleItemRepository;
            _examinerScheduleEmployeeRepository = examinerScheduleEmployeeRepository;
            _examinationMainRepository = examinationMainRepository;
            _examinationRecordRepository = examinationRecordRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _routerListRepository = routerListRepository;
            _messageService = messageService;
            _examinerRepository = examinerRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _departmentListRepository = departmentListRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
        }

        /// <summary>
        /// 获取可以预约的监考日信息
        /// </summary>
        /// <param name="examinationRecordID"></param>
        /// <returns></returns>
        public async Task<List<AppointmentExaminerScheduleView>> GetAvailableAppointmentList(string examinationRecordID)
        {
            var examinerScheduleList = await _examinerScheduleRepository.GetListAsync(examinationRecordID, DateTime.Now.Date);
            var examinerScheduleIDs = examinerScheduleList.Select(m => m.ExaminerScheduleID).ToList();
            var appointmentList = await _examinationAppointmentRepository.GetListByConditionAsync(m => examinerScheduleIDs.Contains(m.ExaminerScheduleID) && m.StatusCode == COMPLETE_APPOINTMENT);
            var examinationRecordIDs = appointmentList.Select(m => m.ExaminationRecordID).ToList();
            examinationRecordIDs.Add(examinationRecordID);
            var examinationRecordList = await _examinationRecordRepository.GetListByIDs(examinationRecordIDs.Distinct().ToList());
            var itemMinutes = examinationRecordList.Find(m => m.ExaminationRecordID == examinationRecordID)?.Duration ?? 0;
            var appointmentViews = new List<AppointmentExaminerScheduleView>();
            foreach (var examinerSchedule in examinerScheduleList)
            {
                var appointments = appointmentList.Where(m => m.ExaminerScheduleID == examinerSchedule.ExaminerScheduleID).ToList();
                var examinationRecords = examinationRecordList.Where(m => appointments.Any(n => n.ExaminationRecordID == m.ExaminationRecordID)).ToList();
                var number = ComputeCanAppointmentNumber(examinerSchedule, itemMinutes, appointments, examinationRecords);
                if (number == 0)
                {
                    continue;
                }
                var appointmentView = new AppointmentExaminerScheduleView
                {
                    ExaminerScheduleID = examinerSchedule.ExaminerScheduleID,
                    ScheduleDate = examinerSchedule.ScheduleDate,
                    ScheduleStartTime = examinerSchedule.ScheduleStartTime,
                    ScheduleTime = $"{examinerSchedule.ScheduleStartTime:hh\\:mm}-{examinerSchedule.ScheduleEndTime:hh\\:mm}",
                    Number = number,
                    Location = examinerSchedule.Location
                };
                appointmentViews.Add(appointmentView);
            }
            return appointmentViews;
        }

        /// <summary>
        /// 计算可以预约人数
        /// </summary>
        /// <param name="examinerSchedule"></param>
        /// <param name="itemMinutes"></param>
        /// <param name="appointments"></param>
        /// <param name="examinationRecordList"></param>
        /// <returns></returns>
        private int ComputeCanAppointmentNumber(ExaminerScheduleInfo examinerSchedule, decimal itemMinutes, List<ExaminationAppointmentInfo> appointments, List<ExaminationRecordInfo> examinationRecordList)
        {
            var allMinutes = (decimal)(examinerSchedule.ScheduleDate.Add(examinerSchedule.ScheduleEndTime) - examinerSchedule.ScheduleDate.Add(examinerSchedule.ScheduleStartTime)).TotalMinutes;
            var number = (int)Math.Floor(allMinutes / itemMinutes);
            // 如果监考计划未被预约，直接返回
            if (examinerSchedule.StatusCode == EXAMINER_SCHEDULE_STATUS_0)
            {
                return number;
            }
            var appointmentMinutes = 0m;
            // 如果已有预约记录，计算预约项目分钟数，判断是否可以约当前项目
            if (appointments.Count > 0)
            {
                foreach (var appointment in appointments)
                {
                    var appointmentMinute = examinationRecordList.Find(m => m.ExaminationRecordID == appointment.ExaminationRecordID)?.Duration ?? 0;
                    appointmentMinutes += appointmentMinute;
                }
                // 如果已预约项目分钟数总和 + 当前项目分钟数 > 监考计划总分钟数 跳过
                if (appointmentMinutes + itemMinutes > allMinutes)
                {
                    number = 0;
                }
                else
                {
                    number = (int)Math.Floor((allMinutes - appointmentMinutes) / itemMinutes);
                }
            }
            return number;
        }

        /// <summary>
        /// 保存考核预约记录
        /// </summary>
        /// <param name="appointmentView">包含完整预约信息的实体对象</param>
        /// <param name="employeeID">会话人工号</param>
        /// <returns></returns>
        public async Task<bool> SaveExaminationAppointment(ExaminationAppointmentView appointmentView, string employeeID)
        {
            if (appointmentView == null || string.IsNullOrWhiteSpace(appointmentView.ExaminationRecordID)
                || string.IsNullOrWhiteSpace(appointmentView.EmployeeID)
                || string.IsNullOrWhiteSpace(appointmentView.ExaminerScheduleID))
            {
                throw new CustomException("获取不到考核计划信息！");
            }
            #region 处理多人同时操作时，前端看的时候还能预约，但提交时预约满的情况
            var examinerSchedule = await _examinerScheduleRepository.GetByIdAsync(appointmentView.ExaminerScheduleID);
            var itemMinutes = (await _examinationRecordRepository.GetDataByID(appointmentView.ExaminationRecordID))?.Duration ?? 0;
            var appointments = await _examinationAppointmentRepository.GetListByConditionAsync(m => m.ExaminerScheduleID == appointmentView.ExaminerScheduleID && m.StatusCode == COMPLETE_APPOINTMENT);
            var examinationRecords = await _examinationRecordRepository.GetListByIDs(appointments.Select(m => m.ExaminationRecordID).ToList());
            var number = ComputeCanAppointmentNumber(examinerSchedule, itemMinutes, appointments, examinationRecords);
            if (number == 0)
            {
                throw new CustomException("选择的日期已约满，请更换日期！");
            }
            #endregion
            ExaminationAppointmentInfo appointmentInfo = null;
            if (string.IsNullOrWhiteSpace(appointmentView.ExaminationAppointmentID))
            {
                appointmentInfo = new ExaminationAppointmentInfo()
                {
                    EmployeeID = appointmentView.EmployeeID,
                    ExaminationRecordID = appointmentView.ExaminationRecordID,
                    StatusCode = COMPLETE_APPOINTMENT,
                };
                appointmentInfo.ExaminationAppointmentID = appointmentInfo.GetId();
                appointmentInfo.Add(employeeID).Modify(employeeID);
                await _unitOfWork.GetRepository<ExaminationAppointmentInfo>().InsertAsync(appointmentInfo);
            }
            else
            {
                appointmentInfo = await _examinationAppointmentRepository.GetByIDAsync(appointmentView.ExaminationAppointmentID);
                // 删除旧的主考人信息，新增新的主考人信息           
                var examinerInfos = await _examinerRepository.GetListBySourceAsync("ExaminationMain", appointmentView.ExaminationMainID);
                examinerInfos.ForEach(m => m.Delete(employeeID));
            }
            if (appointmentInfo != null)
            {
                appointmentInfo.AppointmentDate = appointmentView.AppointmentDate;
                appointmentInfo.ExaminerScheduleID = appointmentView.ExaminerScheduleID;
            }
            var examinationMain = await _examinationMainRepository.GetDataByMainID(appointmentView.ExaminationMainID);
            if (examinationMain == null)
            {
                throw new CustomException("获取考核记录失败。");
            }
            // 预约完成，修改状态为 待考核
            examinationMain.StatusCode = STATUSCODE_EVALUATION_PENDING;
            examinationMain.StartDateTime = appointmentView.AppointmentDate.Date.Add(appointmentView.ScheduleStartTime);
            await InsertExaminerInfo(appointmentView, employeeID);
            // 修改监考计划状态
            if (examinerSchedule.StatusCode == EXAMINER_SCHEDULE_STATUS_0)
            {
                examinerSchedule.StatusCode = EXAMINER_SCHEDULE_STATUS_1;
                examinerSchedule.Modify(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }
        /// <summary>
        ///  插入监考人
        /// </summary>
        /// <param name="appointmentView"></param>
        /// <param name="sessionEmployeeID"></param>
        /// <returns></returns>
        private async Task InsertExaminerInfo(ExaminationAppointmentView appointmentView, string sessionEmployeeID)
        {
            var examinerScheduleEmployeeList = await _examinerScheduleEmployeeRepository.GetByExaminerScheduleID(appointmentView.ExaminerScheduleID);
            if (examinerScheduleEmployeeList == null)
            {
                return;
            }
            var addExaminerInfos = examinerScheduleEmployeeList.Select(examinerScheduleEmployee =>
            {
                var examiner = new ExaminerInfo
                {
                    SourceType = "ExaminationMain",
                    SourceID = appointmentView.ExaminationMainID,
                    EmployeeID = examinerScheduleEmployee.EmployeeID,
                };
                examiner.Add(sessionEmployeeID).Modify(sessionEmployeeID);
                return examiner;
            }).ToList();
            await _unitOfWork.GetRepository<ExaminerInfo>().InsertAsync(addExaminerInfos);
        }

        public async Task<AppointmentExaminerScheduleView> GetAppointmentByExaminationRecordID(string examinationRecordID, string employeeID)
        {
            var appointmentRecords = await _examinationAppointmentRepository.GetListByConditionAsync(m => m.ExaminationRecordID == examinationRecordID
                                                                            && m.EmployeeID == employeeID && m.StatusCode == COMPLETE_APPOINTMENT);
            if (appointmentRecords?.Count <= 0)
            {
                throw new CustomException("获取考核预约信息失败");
            }
            var appointmentRecord = appointmentRecords[0];
            var examinerSchedule = await _examinerScheduleRepository.GetByIdAsync(appointmentRecord.ExaminerScheduleID);
            if (examinerSchedule == null)
            {
                throw new CustomException("获取监考信息失败", true);
            }
            var appointmentView = new AppointmentExaminerScheduleView
            {
                ExaminationAppointmentID = appointmentRecord.ExaminationAppointmentID,
                ExaminerScheduleID = examinerSchedule.ExaminerScheduleID,
                ScheduleDate = examinerSchedule.ScheduleDate,
                ScheduleStartTime = examinerSchedule.ScheduleStartTime,
                ScheduleTime = $"{examinerSchedule.ScheduleStartTime:hh\\:mm}-{examinerSchedule.ScheduleEndTime:hh\\:mm}",
            };
            return appointmentView;
        }

        public async Task<bool> CancelAppointment(CancelAppointmentParamView cancelView)
        {
            if (cancelView == null || string.IsNullOrEmpty(cancelView.ExaminationAppointmentID) || string.IsNullOrEmpty(cancelView.CancelReason))
            {
                return false;
            }
            var appointmentInfo = await _examinationAppointmentRepository.GetByIDAsync(cancelView.ExaminationAppointmentID);
            if (appointmentInfo == null)
            {
                return false;
            }
            appointmentInfo.CancelReason = cancelView.CancelReason;
            appointmentInfo.CancelDateTime = DateTime.Now;
            appointmentInfo.Modify(cancelView.EmployeeID);
            appointmentInfo.StatusCode = CANCEL_APPOINTMENT;

            var examinationMainInfo = await _examinationMainRepository.GetDataByMainID(cancelView.ExaminationMainID);
            if (examinationMainInfo != null)
            {
                // 恢复回预约状态
                examinationMainInfo.StatusCode = STATUSCODE_APPOINTMENT;
                examinationMainInfo.StartDateTime = null;
            }
            // 删除-考核对应的监考人
            var examinerInfos = await _examinerRepository.GetListBySourceAsync("ExaminationMain", cancelView.ExaminationMainID);
            examinerInfos.ForEach(m => m.Delete(cancelView.EmployeeID));
            var appointments = await _examinationAppointmentRepository.GetListByConditionAsync(m => m.ExaminerScheduleID == appointmentInfo.ExaminerScheduleID
                       && m.ExaminationAppointmentID != appointmentInfo.ExaminationAppointmentID && m.StatusCode == COMPLETE_APPOINTMENT);
            // 如果没有预约记录了，改变监考计划的状态为 未预约状态
            if (appointments.Count == 0)
            {
                var examinerSchedule = await _examinerScheduleRepository.GetByIdAsync(appointmentInfo.ExaminerScheduleID);
                if (examinerSchedule != null)
                {
                    examinerSchedule.StatusCode = EXAMINER_SCHEDULE_STATUS_0;
                    examinerSchedule.Modify(cancelView.EmployeeID);
                }
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        public async Task<List<ExaminationAppointmentData>> GetAppointmentList(DateTime startDate, DateTime endDate)
        {
            var examinerScheduleList = await _examinerScheduleRepository.GetListByDateRange(startDate, endDate);
            if (examinerScheduleList.Count <= 0)
            {
                return [];
            }
            var examinerScheduleIDs = examinerScheduleList.Select(m => m.ExaminerScheduleID).ToList();
            var examinerScheduleEmployees = await _examinerScheduleEmployeeRepository.GetByExaminerScheduleIDs(examinerScheduleIDs);
            var appointmentList = await _examinationAppointmentRepository.GetListByConditionAsync(m => examinerScheduleIDs.Contains(m.ExaminerScheduleID));
            var employeeIDs = examinerScheduleEmployees.Select(m => m.EmployeeID).ToList();
            List<string> examinationRecordIDs = [];
            appointmentList.ForEach(m =>
            {
                if (!employeeIDs.Contains(m.EmployeeID))
                {
                    employeeIDs.Add(m.EmployeeID);
                }
                examinationRecordIDs.Add(m.ExaminationRecordID);
            });
            var employeeList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            var employeeStaffList = await _employeeStaffDataRepository.GetEmployeeStaffDataView();
            employeeStaffList = employeeStaffList.Where(m => employeeIDs.Contains(m.EmployeeID)).ToList();
            var departmentList = await _departmentListRepository.GetByOrganizationType("1");
            var examinationRecordDict = await _examinationRecordRepository.GetRecordIDAndNameDictByIDs(examinationRecordIDs);
            var appointmentStatusSettings = await _settingDictionaryRepository.GetSettingDictionary(new SettingDictionaryParams
            {
                SettingType = "ExaminationManagement",
                SettingTypeCode = "ExaminationMain",
                SettingTypeValue = "AppointmentStatus"
            });
            List<ExaminationAppointmentData> resultList = [];
            foreach (var examinerSchedule in examinerScheduleList)
            {
                var scheduleAppointmentList = appointmentList.Where(m => m.ExaminerScheduleID == examinerSchedule.ExaminerScheduleID).ToList();
                if (scheduleAppointmentList.Count <= 0)
                {
                    continue;
                }
                var scheduleTimeRange = $"{examinerSchedule.ScheduleStartTime:hh\\:mm}-{examinerSchedule.ScheduleEndTime:hh\\:mm}";
                var examinerScheduleEmployeeIDs = examinerScheduleEmployees.Where(m => m.ExaminerScheduleID == examinerSchedule.ExaminerScheduleID).OrderBy(m => m.Sort).Select(m => m.EmployeeID).ToList();
                var examinerList = employeeList.Where(m => examinerScheduleEmployeeIDs.Contains(m.EmployeeID));
                var examiners = examinerList.Select(
                    m => new SelectOptionsView
                    {
                        Label = m.EmployeeName,
                        Value = m.EmployeeID
                    }).ToList();
                var examinerName = examinerList.Select(m => m.EmployeeName).Aggregate((a, b) => $"{a}、{b}");

                foreach (var scheduleAppointment in scheduleAppointmentList)
                {
                    var employeeName = employeeList.Find(m => m.EmployeeID == scheduleAppointment.EmployeeID)?.EmployeeName ?? "";
                    var departmentID = employeeStaffList.Find(n => n.EmployeeID == scheduleAppointment.EmployeeID)?.DepartmentID ?? 0;
                    var departmentName = departmentList.Find(m => m.DepartmentID == departmentID)?.DepartmentContent ?? "";
                    examinationRecordDict.TryGetValue(scheduleAppointment.ExaminationRecordID, out var examinationName);
                    var statusName = appointmentStatusSettings.Find(m => m.SettingValue == scheduleAppointment.StatusCode)?.Description ?? "";
                    var examinationAppointmentData = new ExaminationAppointmentData()
                    {
                        ExaminerScheduleID = scheduleAppointment.ExaminerScheduleID,
                        ScheduleDate = examinerSchedule.ScheduleDate,
                        ExaminerName = examinerName,
                        Examiners = examiners,
                        ScheduleTimeRange = scheduleTimeRange,
                        DepartmentName = departmentName,
                        EmployeeID = scheduleAppointment.EmployeeID,
                        EmployeeName = employeeName,
                        ExaminationRecordID = scheduleAppointment.ExaminationRecordID,
                        ExaminationName = examinationName ?? "",
                        AppointmentDate = scheduleAppointment.AddDateTime,
                        StatusCode = scheduleAppointment.StatusCode,
                        StatusName = statusName,
                        CancelReason = scheduleAppointment.CancelReason,
                        CancelDateTime = scheduleAppointment.CancelDateTime
                    };
                    resultList.Add(examinationAppointmentData);
                }
            }
            return resultList.OrderBy(m => m.ScheduleDate).ThenBy(m => m.ScheduleTimeRange).ThenBy(m => m.ExaminerName).ThenBy(m => m.AppointmentDate).ToList();
        }

        #region 考试通知 定时任务调用
        /// <summary>
        ///  发送考试通知
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SendPracticalExamNotification()
        {
            var lastDate = DateTime.Now.Date;
            var appointemntList = await _examinationAppointmentRepository.GetListByConditionAsync(
                m => m.AppointmentDate < DateTime.Now.Date && m.AppointmentDate >= lastDate,
                n => new ExaminationAppointmentInfo
                {
                    AppointmentDate = n.AppointmentDate,
                    ExaminationRecordID = n.ExaminationRecordID
                });
            var examinationRecordIDs = appointemntList.Select(m => m.ExaminationRecordID).Distinct().ToList();
            var examinationRecordDict = await _examinationRecordRepository.GetRecordIDAndNameDictByIDs(examinationRecordIDs);
            Dictionary<string, string> notificationList = new Dictionary<string, string>();
            foreach (var appointItem in appointemntList)
            {
                // 没有获取到考核信息 直接不进行通知
                if (!examinationRecordDict.TryGetValue(appointItem.ExaminationRecordID, out var examinationName))
                {
                    continue;
                }
                var message = $"您预约的操作考核【{examinationName}】将在{appointItem.AppointmentDate:yyyy-MM-dd}开始，注意按时参加。";
                notificationList.TryAdd(appointItem.EmployeeID, message);
            }
            //发送通知至移动端
            var messageViewList = GenerateMessageView(notificationList, (int)ClientType.Mobile);
            foreach (var messageView in messageViewList)
            {
                await _messageService.SendMessage(messageView);
            }
            return true;
        }
        /// <summary>
        /// 生成发送通知需要的参数对象
        /// </summary>
        /// <param name="examineEmployeeList"></param>
        /// <param name="clientType"></param>
        /// <returns></returns>
        private List<MessageView> GenerateMessageView(Dictionary<string, string> examineEmployeeList, int clientType)
        {
            var messageView = new List<MessageView>();
            foreach (var item in examineEmployeeList)
            {
                var view = new MessageView
                {
                    MessageTools = [MessageTool.Wechat],
                    EmployeeID = item.Key,
                    ClientType = clientType,
                    MessageCondition = new MessageConditionView
                    {
                        // 移动端与PC端使用不同的交换机
                        MQExchangeName = "MQNotification",
                        MQRoutingKey = item.Key,
                        Message = item.Value,
                        ClientType = clientType,
                    }
                };
                messageView.Add(view);
            }
            return messageView;
        }
        #endregion
    }
}
