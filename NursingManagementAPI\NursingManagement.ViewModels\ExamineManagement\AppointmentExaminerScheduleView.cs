﻿namespace NursingManagement.ViewModels
{
    public class AppointmentExaminerScheduleView
    {
        /// <summary>
        /// 监考计划ID
        /// </summary>
        public string ExaminerScheduleID { get; set; }
        /// <summary>
        /// 计划日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }
        /// <summary>
        /// 计划开始时间
        /// </summary>
        public TimeSpan ScheduleStartTime { get; set; }
        /// <summary>
        /// 计划时间段
        /// </summary>
        public string ScheduleTime { get; set; }
        /// <summary>
        /// 剩余可约人数
        /// </summary>
        public int Number { get; set; }
        /// <summary>
        /// 预约记录ID
        /// </summary>
        public string ExaminationAppointmentID { get; set; }
        /// <summary>
        /// 考核地点
        /// </summary>
        public string Location { get; set; }
    }
}
