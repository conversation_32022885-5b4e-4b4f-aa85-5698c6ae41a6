﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IRouterListRepository : ICacheRepository
    {

        /// <summary>
        /// 根据客户端类型获取所有路由
        /// </summary>
        /// <returns></returns>
        Task<List<RouterListInfo>> GetRouterListByClientType(int clientType);
        /// <summary>
        /// 根据路由键获取路由信息
        /// </summary>
        /// <param name="routerListID">路由键</param>
        /// <returns></returns>
        Task<RouterListInfo[]> GetInfosByRouterListID(int routerListID);
        /// <summary>
        ///  根据ID获取数据
        /// </summary>
        /// <param name="routerListID">路由ID</param>
        /// <returns></returns>
        Task<RouterListInfo> GetDataByRouterListID(int routerListID);
    }
}
