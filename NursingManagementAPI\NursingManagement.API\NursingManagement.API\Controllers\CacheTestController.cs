﻿
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using System.Text;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 缓存控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/testCache")]
    [EnableCors("any")]
    public class TestCacheController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IRedisService _redisService;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDistributedCache _cache;
        private readonly  IDynamicFormDetailRepository _dynamicFormDetailRepository;
        /// <summary>
        /// 注入
        /// </summary>
        /// <param name="redisService"></param>
        /// <param name="employeePersonalDataRepository"></param>
        public TestCacheController(
             IRedisService redisService,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IDistributedCache distributedCache,
            IDynamicFormDetailRepository dynamicFormDetailRepository
            )
        {
            _redisService = redisService;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _cache = distributedCache;
            _dynamicFormDetailRepository = dynamicFormDetailRepository;
        }

        /// <summary>
        /// 根据Key获取缓存
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetCacheByKeyTest")]
        public async Task<IActionResult> GetCacheByKeyTest(string key)
        {

            var result = new ResponseResult();
            try
            {
                await _cache.GetAsync(key);
                result.Data = "获取结束";

            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }

        /// <summary>
        /// 缓存拆分测试EmployeePersona
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SetTestEmployeePersonaCache")]
        public async Task<IActionResult> TestEmployeePersonaCache()
        {

            var result = new ResponseResult();
            try
            {
                var data = await _employeePersonalDataRepository.GetAllDataByHospital("1");
                var count = 0;
                foreach (var item in data)
                {
                    var key = "EmployeeData1_" + count++.ToString();
                    await AddCache(key, item);
                }

                result.Data = true;

            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }
        /// <summary>
        /// 根据Key循环获取缓存(GetEmployeeCacheTest)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetEmployeeCacheTest")]
        public async Task<IActionResult> GetEmployeeCacheTest()
        {

            var result = new ResponseResult();
            try
            {
                for (int i = 0; i < 5000; i++)
                {
                    var key = "EmployeeData1_" + i.ToString();
                    var d = await _cache.GetAsync(key);
                }
                result.Data = "获取结束";

            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }


        /// <summary>
        /// 缓存测试EmployeePersona
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("TestEmployeePersonaAll")]
        public async Task<IActionResult> TestEmployeePersonaCache1()
        {

            var result = new ResponseResult();
            try
            {
                var data = await _employeePersonalDataRepository.GetAllDataByHospital("1");

                var key = "EmployeeData1All";
                await AddCache(key, data);
                result.Data = true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }
        /// <summary>
        /// 获取缓存(GetEmployeeCacheTestAll)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetEmployeeCacheTestAll")]
        public async Task<IActionResult> GetEmployeeCacheTestAll()
        {

            var result = new ResponseResult();
            try
            {
                var key = "EmployeeData1All";
                await GetAsync<Object>(key);
                result.Data = "获取成功";
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }


        /// <summary>
        /// 缓存测试DynamicFormDetail
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SetDynamicFormDetailTest")]
        public async Task<IActionResult> SetDynamicFormDetailTest()
        {

            var result = new ResponseResult();
            try
            {
                var data = await _dynamicFormDetailRepository.GetAllDynamicFormDetailByTest();

                var key = "DynamicFormDetailAll";
                await AddCache(key, data);
                result.Data = true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }
        /// <summary>
        /// 获取缓存(DynamicFormDetailAll)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetDynamicFormDetailTest")]
        public async Task<IActionResult> GetDynamicFormDetailTest()
        {

            var result = new ResponseResult();
            try
            {
                var key = "DynamicFormDetailAll";
                await GetAsync<Object>(key);
                result.Data = "获取成功";
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }


        private async Task<bool> AddCache<T>(string key, T value)
        {
            byte[] val = null;
            if (value.ToString() != "")
            {
                string data = JsonConvert.SerializeObject(value);

                val = Encoding.UTF8.GetBytes(data);
            }
            if (val == null)
            {
                return false;
            }
            //添加缓存
            await _cache.SetAsync(key, val);
            //刷新缓存
            await _cache.RefreshAsync(key);
            return true;
        }
        private async Task<T> GetAsync<T>(string key)
        {
            T datas = default(T);
            if (string.IsNullOrEmpty(key))
            {
                return datas;
            }
            var value = await _cache.GetAsync(key);
            if (value != null)
            {
                string str = Encoding.UTF8.GetString(value);
                datas = JsonConvert.DeserializeObject<T>(str);
            }
            return datas;
        }
    }
}