﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 调班申请 服务层
    /// </summary>
    public interface IAdjustScheduleService
    {
        /// <summary>
        /// 获取调班申请数据
        /// </summary>
        /// <param name="employeeID">员工工号</param>
        /// <param name="departmentID">部门科室ID</param>
        /// <param name="showDeptDataSwitch">是否显示全科数据</param>
        /// <returns></returns>
        Task<List<AdjustScheduleRecordView>> GetAdjustScheduleRecordsAsync(string employeeID, int? departmentID, bool showDeptDataSwitch);
        /// <summary>
        /// 保存调班申请数据
        /// </summary>
        /// <param name="paramView"></param>
        /// <param name="hospitalID">医院类别</param>
        /// <param name="employeeID">员工工号</param>
        /// <param name="departmentID">部门科室ID</param>
        /// <returns></returns>
        Task<SaveReponseView> SaveAdjustScheduleRecordAsync(AdjustScheduleParamView paramView, string hospitalID, string employeeID, int departmentID);
        /// <summary>
        /// 删除调班申请
        /// </summary>
        /// <param name="adjustScheduleRecordID">删除调班申请主键</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        Task<bool> DeleteAdjustScheduleRecordAsync(string adjustScheduleRecordID, string employeeID);
        /// <summary>
        /// 创建或更新审批记录
        /// </summary>
        /// <param name="adjustScheduleRecordInfo"></param>
        /// <returns></returns>
        Task<bool> CreateOrUpdateApprove(AdjustScheduleRecordInfo adjustScheduleRecordInfo);
    }
}
