﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCAssessListRepository : IHierarchicalQCAssessListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public HierarchicalQCAssessListRepository(
            NursingManagementDbContext nursingManagementDbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }

        public async Task<int?> GetIDByContentName(string contentName)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            // 在SaveChange前取查找
            var data = _nursingManagementDbContext.ChangeTracker.Entries<HierarchicalQCAssessListInfo>()
                        .FirstOrDefault(m => m.Entity.ContentName == contentName && m.Entity.HospitalID == hospitalID && m.Entity.Language == language && m.Entity.DeleteFlag != "*")?.Entity;
            // 第一次_nursingManagementDbContext.ChangeTracker可能取不到，从缓存中取
            if (data == null)
            {
                var datas = await GetCacheAsync() as List<HierarchicalQCAssessListInfo>;
                data = datas.Find(m => m.ContentName == contentName);
            }
            return data?.HierarchicalQCAssessListID;
        }

        public async Task<int> GetNewID()
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            // 在SaveChange前取最大值
            var last = _nursingManagementDbContext.ChangeTracker.Entries<HierarchicalQCAssessListInfo>().OrderByDescending(m => m.Entity.HierarchicalQCAssessListID)
                        .FirstOrDefault(m => m.Entity.HospitalID == hospitalID && m.Entity.Language == language && m.Entity.DeleteFlag != "*")?.Entity;
            // 第一次_nursingManagementDbContext.ChangeTracker可能取不到，从缓存中取
            if (last == null)
            {
                var datas = await GetCacheAsync() as List<HierarchicalQCAssessListInfo>;
                last = datas.OrderBy(m => m.HierarchicalQCAssessListID).LastOrDefault();
            }
            return last != null ? last.HierarchicalQCAssessListID + 1 : 1;
        }
        /// <summary>
        /// 获取评估字典键值对
        /// </summary>
        /// <returns></returns>
        public async Task<Dictionary<int,string>> GetAssessDictionaries()
        {
            var datas = await GetCacheAsync() as List<HierarchicalQCAssessListInfo>;
            return datas.ToDictionary(m => m.HierarchicalQCAssessListID, m => m.ContentName);
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 3600, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.HierarchicalQCAssessListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;

            });
            return datas;
        }
        public string GetCacheType()
        {
            return CacheType.HierarchicalQCAssessList.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
    }
}