﻿namespace NursingManagement.ViewModel
{
    /// <summary>
    /// CCC患者状况View
    /// </summary>
    public class PatientProfileRecordView
    {
        /// <summary>
        /// 患者状况类型（病危患者事件AssessListID）
        /// </summary>
        public string ProfileID { get; set; }
        /// <summary>
        /// 患者住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病历号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public string Age { get; set; }
        /// <summary>
        /// 入院日期
        /// </summary>
        public DateTime AdmissionDateTime { get; set; }
        /// <summary>
        /// 出院日期
        /// </summary>
        public DateTime? DischargeDateTime { get; set; }
        /// <summary>
        /// CCC病区ID
        /// </summary>
        public string StationID { get; set; }
        /// <summary>
        /// CCC病区名称
        /// </summary>
        public string StationName { get; set; }
        /// <summary>
        /// CCC床位
        /// </summary>
        public string BedNumber { get; set; }
        /// <summary>
        /// 来源类型（PatientEvent）
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 状况来源ID（PatientEventID）
        /// </summary>
        public string SourceID { get; set; }
        /// <summary>
        /// 状况开始时间（病危开始患者事件发生时间……）
        /// </summary>
        public DateTime StartDateTime { get; set; }
        /// <summary>
        /// 状况结束时间（病危结束患者事件发生时间……）
        /// </summary>
        public DateTime? EndDateTime { get; set; }
        /// <summary>
        /// 主责护士
        /// </summary>
        public string AttendanceEmployName { get; set; }
        /// <summary>
        /// 患者状况明细
        /// </summary>
        public List<PatientProfileDetailView> ProfileDetails { get; set; }
        /// <summary>
        /// 事件发生病区
        /// </summary>
        public string OccurStationID { get; set; }
    }
}
