﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface ITieredTaskRepository
    {
        /// <summary>
        /// 获取单条任务
        /// </summary>
        /// <param name="scheduleMainID">任务主键</param>
        /// <returns></returns>
        Task<AnnualScheduleMainInfo> GetTieredTaskByID(string scheduleMainID);
        /// <summary>
        /// 获取多条任务
        /// </summary>
        /// <param name="scheduleIDs">任务ID集合</param>
        /// <returns></returns>
        Task<List<AnnualScheduleMainInfo>> GetTieredTasksByIDs(IEnumerable<string> scheduleIDs);
        /// <summary>
        /// 获取{schedulePerformer}在{scheduleMonth}月的任务
        /// </summary>
        /// <param name="schedulePerformer">计划执行人</param>
        /// <param name="scheduleMonth">计划月份</param>
        /// <param name="scheduleYear">计划年度</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        Task<AnnualScheduleMainView[]> GetTieredTaskViews(string schedulePerformer, int scheduleMonth, int scheduleYear, int departmentID);

        /// <summary>
        /// 获取每月任务的数量
        /// </summary>
        /// <param name="schedulePerformer">预计执行人</param>
        /// <param name="scheduleYear">年份</param>
        /// <returns></returns>
        Task<List<ScheduleStatisticsView>> GetTasksMonthlyCountViews(string schedulePerformer, int scheduleYear);

        /// <summary>
        /// 获取某月未执行任务数量
        /// </summary>
        /// <param name="scheduleYear">年份</param>
        /// <param name="scheduleMonth">月份</param>
        /// <param name="schedulePerformer">预计执行人</param>
        /// <returns></returns>
        Task<int?> GetMonthlyUnPerformTasksCount(int scheduleYear, int scheduleMonth, string schedulePerformer);
        /// <summary>
        /// 根据日期获取未执行的计划任务ID
        /// </summary>
        /// <param name="scheduleDate">计划执行日期</param>
        /// <returns></returns>
        Task<List<AnnualScheduleMainInfo>> GetDailyUnPerformTaskRelationWorks(DateTime scheduleDate);
        /// <summary>
        /// 获取关联的任务ID
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="planMonth">月份</param>
        /// <param name="interventionID">措施ID</param>
        /// <param name="principalID">执行人</param>
        /// <returns></returns>
        Task<string> GetRelationTaskID(int year, int planMonth, int interventionID, string principalID);
        /// <summary>
        /// 优先获取当前部门计划对应的未执行排程数据
        /// </summary>
        /// <param name="schedulePerformer">计划执行人</param>
        /// <param name="scheduleYear">计划年份</param>
        /// <param name="startMonth">月份</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="preOrNextFlag">获取计划月份之前OR之后，True：之前；False：当月</param>
        /// <returns></returns>
        Task<AnnualScheduleMainView[]> GetUnPerformTaskViews(string schedulePerformer, int scheduleYear, int? startMonth, int departmentID, bool preOrNextFlag);
        /// <summary>
        /// 获取某人员某月的任务集合
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <param name="schedulePerformer">预计执行人</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns></returns>
        Task<List<AnnualScheduleMainInfo>> GetMonthlyTasks(int year, int month, string hospitalID);
    }
}
