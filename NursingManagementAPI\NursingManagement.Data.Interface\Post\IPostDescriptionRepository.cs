﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IPostDescriptionRepository
    {
        /// <summary>
        /// 获取岗位说明书
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="postID">部门岗位编号</param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<PostDescriptionInfo> GetData(int departmentID, int postID, string hospitalID, int language);
        /// <summary>
        /// 根据部门ID获取科室岗位说明书
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<List<PostDescriptionInfo>> GetListByDeparment(int departmentID,string hospitalID, int language);
        /// <summary>
        /// 根据岗位编码，部门ID,岗位编号获取数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="postID"></param>
        /// <param name="postDescriptionCode"></param>
        /// <param name="version"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<PostDescriptionInfo> GetByCode(int departmentID, int postID, string postDescriptionCode, string version, string hospitalID, int language);
        /// <summary>
        /// 获取删除的数据，根据岗位编码，部门ID,岗位编号
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="postID"></param>
        /// <param name="postDescriptionCode"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        Task<PostDescriptionInfo> GetDeleteByCode(int departmentID, int postID, string postDescriptionCode, string hospitalID, int language);
    }
}
