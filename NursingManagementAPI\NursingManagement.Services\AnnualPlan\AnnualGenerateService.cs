﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;

namespace NursingManagement.Services
{
    public class AnnualGenerateService : IAnnualGenerateService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualPlanMainRepository _mainRepository;
        private readonly IAnnualPlanMainGoalRepository _mainGoalRepository;
        private readonly IAnnualPlanGoalGroupRepository _annualPlanGoalGroupRepository;
        private readonly IAnnualPlanIndicatorDetailRepository _annualPlanIndicatorDetailRepository;
        private readonly IAnnualPlanProjectDetailRepository _annualPlanProjectDetailRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
        private readonly IAnnualPlanInterventionDetailRepository _interventionDetailRepository;
        private readonly IAnnualInterventionMainPrincipalRepository _annualInterventionMainPrincipalRepository;
        public AnnualGenerateService(
            IAnnualPlanMainRepository mainRepository,
            IAnnualPlanMainGoalRepository mainGoalRepository,
            IAnnualPlanGoalGroupRepository annualPlanGoalGroupRepository,
            IAnnualPlanIndicatorDetailRepository annualPlanIndicatorDetailRepository,
            IAnnualPlanProjectDetailRepository annualPlanProjectDetailRepository,
            IUnitOfWork unitOfWork,
            IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
            IAnnualPlanInterventionDetailRepository interventionDetailRepository,
            IAnnualInterventionMainPrincipalRepository annualInterventionMainPrincipalRepository
            )
        {
            _mainRepository = mainRepository;
            _mainGoalRepository = mainGoalRepository;
            _annualPlanGoalGroupRepository = annualPlanGoalGroupRepository;
            _annualPlanIndicatorDetailRepository = annualPlanIndicatorDetailRepository;
            _annualPlanProjectDetailRepository = annualPlanProjectDetailRepository;
            _unitOfWork = unitOfWork;
            _annualPlanInterventionMainRepository = annualPlanInterventionMainRepository;
            _interventionDetailRepository = interventionDetailRepository;
            _annualInterventionMainPrincipalRepository = annualInterventionMainPrincipalRepository;
        }

        /// <summary>
        /// 生成年度计划数据
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年度</param>
        /// <returns></returns>
        public async Task<string> GenAnnualPlanData(int? departmentID, int year)
        {
            // 查询上一年数据
            var lastYear = year - 1;
            // 尝试获取当前年份的年度计划主表ID，若获取到需要先走一遍删除逻辑
            var departmentIDs = await _mainRepository.GetPlanMainExistedDepartmentIDs(year);
            // 获取旧主表
            var oldAnnualPlans = await _mainRepository.GetInfoByMainIDAndYearAsNoTracking(departmentID, lastYear);
            oldAnnualPlans = oldAnnualPlans.ExceptBy(departmentIDs, m => m.DepartmentID).ToList();
            if (oldAnnualPlans.Count == 0)
            {
                return "复制失败，没有可复制的部门";
            }
            var annualPlanSaveStatus = oldAnnualPlans.ToDictionary(m => m.DepartmentID, _ => false);
            // 事务大小控制在部门为单位
            foreach (var oldAnnualPlan in oldAnnualPlans)
            {
                await GenAnnualPlanDataByLastYear(oldAnnualPlan);
                annualPlanSaveStatus[oldAnnualPlan.DepartmentID] = await _unitOfWork.SaveChangesAsync() > 0;
            }

            return $"复制成功的部门：{string.Join("，", annualPlanSaveStatus.Where(m => m.Value).Select(m => m.Key))}\n" +
                $"复制失败的部门：{string.Join("，", annualPlanSaveStatus.Where(m => !m.Value).Select(m => m.Key))}";
        }
        /// <summary>
        /// 根据去年数据生成今年数据
        /// </summary>
        /// <param name="oldPlanMain">旧计划主表</param>
        /// <returns></returns>
        private async Task GenAnnualPlanDataByLastYear(AnnualPlanMainInfo oldPlanMain)
        {
            var year = oldPlanMain.Year + 1;
            // 分类目标表
            var mainGoalInfos = await _mainGoalRepository.GetInfosByPlanMainID(oldPlanMain.AnnualPlanMainID);
            // 分组表
            var groupInfos = await _annualPlanGoalGroupRepository.GetInfosByPlanMainIDAsNoTracking(oldPlanMain.AnnualPlanMainID);
            // 指标明细表
            var indicatorDetailInfos = await _annualPlanIndicatorDetailRepository.GetInfosByPlanMainIDAsNoTracking(oldPlanMain.AnnualPlanMainID);
            // 项目明细表
            var projectDetailInfos = await _annualPlanProjectDetailRepository.GetInfosByPlanMainIDAsNoTracking(oldPlanMain.AnnualPlanMainID);
            // 执行项目主表
            var interventionMainInfos = await _annualPlanInterventionMainRepository.GetInfosByPlanMainAsNoTracking(oldPlanMain.AnnualPlanMainID);
            // 执行项目明细表
            var interventionDetailInfos = await _interventionDetailRepository.GetInfosByPlanMainIDAsNoTracking(oldPlanMain.AnnualPlanMainID);
            // 执行项目明细表
            var interventionMainPrincipalInfos = await _annualInterventionMainPrincipalRepository.GetInfosByPlanMainIDAsNoTracking(oldPlanMain.AnnualPlanMainID);

            // 生成新的年度计划主表
            var newPlanMain = await InsertNewPlanMain(year, oldPlanMain);

            // 生成新的年度计划分类目标表
            // 记录新旧GUID映射关系，便于后续其它表更新
            Dictionary<string, string> mainGoalIDMapping = await InsertNewMainGoals(newPlanMain, mainGoalInfos);

            // 生成新的年度计划分组表
            // 记录新旧GUID映射关系，便于后续其它表更新
            Dictionary<string, string> groupIDMapping = await InsertNewGroups(newPlanMain, groupInfos, mainGoalIDMapping);

            // 生成新的年度计划指标明细表
            await GenIndicatorDetail(year, newPlanMain, indicatorDetailInfos, mainGoalIDMapping, groupIDMapping);

            // 生成新的年度计划项目明细表
            // 记录新旧GUID映射关系，便于后续其它表更新
            Dictionary<string, string> projectDetailIDMapping = await InsertNewProjectDetails(newPlanMain, projectDetailInfos, mainGoalIDMapping, groupIDMapping);

            // 生成新的年度计划执行项目主表
            // 记录新旧GUID映射关系，便于后续其它表更新
            Dictionary<string, string> interventionMainIDMapping = await InsertNewInterventionMains(newPlanMain, interventionMainInfos, mainGoalIDMapping, projectDetailIDMapping);

            // 生成新的年度计划执行项目负责人表
            await InsertNewInterventionMainPrincipals(newPlanMain, interventionMainPrincipalInfos, interventionMainIDMapping);

            // 生成新的年度计划执行项目明细表
            await InsertNewInterventionDetails(newPlanMain, interventionDetailInfos, interventionMainIDMapping);
        }

        /// <summary>
        /// 生成计划主表
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="oldPlanMain">旧主表</param>
        /// <returns></returns>
        private async Task<AnnualPlanMainInfo> InsertNewPlanMain(int year, AnnualPlanMainInfo oldPlanMain)
        {
            oldPlanMain.Year = year;
            oldPlanMain.AnnualPlanMainID = oldPlanMain.GetId();
            oldPlanMain.Add("System");
            oldPlanMain.Modify("System");
            await _unitOfWork.GetRepository<AnnualPlanMainInfo>().InsertAsync(oldPlanMain);
            return oldPlanMain;
        }
        /// <summary>
        /// 生成目标表
        /// </summary>
        /// <param name="mainInfo">主表Model</param>
        /// <param name="mainGoalInfos">旧目标</param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> InsertNewMainGoals(AnnualPlanMainInfo mainInfo, List<AnnualPlanMainGoalInfo> mainGoalInfos)
        {
            var mainGoalIDMapping = new Dictionary<string, string>();
            mainGoalInfos.ForEach(m =>
            {
                // 使用新的主表ID
                m.AnnualPlanMainID = mainInfo.AnnualPlanMainID;
                var newMainGoalID = m.GetId();
                mainGoalIDMapping.Add(m.AnnualPlanMainGoalID, newMainGoalID);
                m.AnnualPlanMainGoalID = newMainGoalID;
                m.Add("System");
                m.Modify("System");
            });
            await _unitOfWork.GetRepository<AnnualPlanMainGoalInfo>().InsertAsync(mainGoalInfos);
            return mainGoalIDMapping;
        }
        /// <summary>
        /// 生成分组表
        /// </summary>
        /// <param name="mainInfo">计划主表Model</param>
        /// <param name="groupInfos">旧分组表</param>
        /// <param name="mainGoalIDMapping">目标ID映射</param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> InsertNewGroups(AnnualPlanMainInfo mainInfo, List<AnnualPlanGoalGroupInfo> groupInfos, Dictionary<string, string> mainGoalIDMapping)
        {
            var groupIDMapping = new Dictionary<string, string>();
            groupInfos.ForEach(m =>
            {
                var newGroupID = m.GetId();
                groupIDMapping.Add(m.AnnualPlanGoalGroupID, newGroupID);
                m.AnnualPlanGoalGroupID = newGroupID;
                // 使用新的主表、分类目标ID
                m.AnnualPlanMainID = mainInfo.AnnualPlanMainID;
                m.AnnualPlanMainGoalID = mainGoalIDMapping[m.AnnualPlanMainGoalID];
                m.Add("System");
                m.Modify("System");
            });
            await _unitOfWork.GetRepository<AnnualPlanGoalGroupInfo>().InsertAsync(groupInfos);
            return groupIDMapping;
        }
        /// <summary>
        /// 生成指标明细表
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="mainInfo">计划主表Model</param>
        /// <param name="indicatorDetailInfos">旧指标明细</param>
        /// <param name="mainGoalIDMapping">目标ID映射</param>
        /// <param name="groupIDMapping">分组ID映射</param>
        /// <returns></returns>
        private async Task GenIndicatorDetail(int year, AnnualPlanMainInfo mainInfo, List<AnnualPlanIndicatorDetailInfo> indicatorDetailInfos, Dictionary<string, string> mainGoalIDMapping, Dictionary<string, string> groupIDMapping)
        {
            indicatorDetailInfos.ForEach(m =>
            {
                m.DetailID = m.GetId();
                // 使用新的主表、目标、分组ID
                m.AnnualPlanMainID = mainInfo.AnnualPlanMainID;
                m.AnnualPlanMainGoalID = mainGoalIDMapping[m.AnnualPlanMainGoalID];
                m.AnnualPlanGoalGroupID = groupIDMapping[m.AnnualPlanGoalGroupID];
                m.Year = year;
                m.Add("System");
                m.Modify("System");
            });
            await _unitOfWork.GetRepository<AnnualPlanIndicatorDetailInfo>().InsertAsync(indicatorDetailInfos);
        }
        /// <summary>
        /// 生成项目明细表
        /// </summary>
        /// <param name="mainInfo">主表Model</param>
        /// <param name="projectDetailInfos">旧项目明细</param>
        /// <param name="mainGoalIDMapping">目标ID映射</param>
        /// <param name="groupIDMapping">分组ID映射</param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> InsertNewProjectDetails(AnnualPlanMainInfo mainInfo, List<AnnualPlanProjectDetailInfo> projectDetailInfos, Dictionary<string, string> mainGoalIDMapping, Dictionary<string, string> groupIDMapping)
        {
            var projectDetailIDMapping = new Dictionary<string, string>();
            projectDetailInfos.ForEach(m =>
            {
                var newProjectDetailID = m.GetId();
                projectDetailIDMapping.Add(m.DetailID, newProjectDetailID);
                m.DetailID = newProjectDetailID;
                // 使用新的主表、目标、分组ID
                m.AnnualPlanMainID = mainInfo.AnnualPlanMainID;
                m.AnnualPlanMainGoalID = mainGoalIDMapping[m.AnnualPlanMainGoalID];
                m.AnnualPlanGoalGroupID = groupIDMapping[m.AnnualPlanGoalGroupID];
                m.Add("System");
                m.Modify("System");
            });
            await _unitOfWork.GetRepository<AnnualPlanProjectDetailInfo>().InsertAsync(projectDetailInfos);
            return projectDetailIDMapping;
        }
        /// <summary>
        /// 生成计划执行项目主表
        /// </summary>
        /// <param name="mainInfo">主表Model</param>
        /// <param name="interventionMainInfos">旧主表Model</param>
        /// <param name="mainGoalIDMapping">目标ID映射</param>
        /// <param name="projectDetailIDMapping">项目ID映射</param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> InsertNewInterventionMains(AnnualPlanMainInfo mainInfo, List<AnnualInterventionMainInfo> interventionMainInfos, Dictionary<string, string> mainGoalIDMapping, Dictionary<string, string> projectDetailIDMapping)
        {
            var interventionMainIDMapping = new Dictionary<string, string>();
            interventionMainInfos.ForEach(m =>
            {
                var newInterventionMainID = m.GetId();
                interventionMainIDMapping.Add(m.AnnualPlanInterventionMainID, newInterventionMainID);
                m.AnnualPlanInterventionMainID = newInterventionMainID;
                // 使用新的主表、目标、项目明细ID
                m.AnnualPlanMainID = mainInfo.AnnualPlanMainID;
                mainGoalIDMapping.TryGetValue(m.AnnualPlanMainGoalID, out var newMainGoalID);
                m.AnnualPlanMainGoalID = newMainGoalID;
                projectDetailIDMapping.TryGetValue(m.ProjectDetailID, out var newProjectDetailID);
                m.ProjectDetailID = newProjectDetailID;
                m.Add("System");
                m.Modify("System");
            });
            // 缺少外键的，不复制
            interventionMainInfos = interventionMainInfos.Where(m => !string.IsNullOrEmpty(m.AnnualPlanMainGoalID) && !string.IsNullOrEmpty(m.ProjectDetailID)).ToList();
            await _unitOfWork.GetRepository<AnnualInterventionMainInfo>().InsertAsync(interventionMainInfos);
            return interventionMainIDMapping;
        }

        /// <summary>
        /// 生成年度计划执行项目负责人表
        /// </summary>
        /// <param name="newPlanMain">新计划</param>
        /// <param name="interventionMainPrincipalInfos">旧负责人表</param>
        /// <param name="interventionMainIDMapping">新旧执行项目ID对照关系</param>
        /// <returns></returns>
        private async Task InsertNewInterventionMainPrincipals(AnnualPlanMainInfo newPlanMain, List<AnnualInterventionMainPrincipalInfo> interventionMainPrincipalInfos, Dictionary<string, string> interventionMainIDMapping)
        {
            interventionMainPrincipalInfos.ForEach(m =>
            {
                m.ID = 0;
                m.AnnualPlanMainID = newPlanMain.AnnualPlanMainID;
                interventionMainIDMapping.TryGetValue(m.AnnualInterventionMainID, out var newInterventionMainID);
                m.AnnualInterventionMainID = newInterventionMainID;
                m.Add("System").Modify("System");
            });
            interventionMainPrincipalInfos = interventionMainPrincipalInfos.Where(m => !string.IsNullOrEmpty(m.AnnualInterventionMainID)).ToList();
            await _unitOfWork.GetRepository<AnnualInterventionMainPrincipalInfo>().InsertAsync(interventionMainPrincipalInfos);
        }

        /// <summary>
        /// 生成计划执行项目明细
        /// </summary>
        /// <param name="mainInfo">主表Model</param>
        /// <param name="interventionDetailInfos">旧明细表Model</param>
        /// <param name="interventionMainIDMapping">主表ID映射</param>
        /// <returns></returns>
        private async Task InsertNewInterventionDetails(AnnualPlanMainInfo mainInfo, List<AnnualInterventionDetailInfo> interventionDetailInfos, Dictionary<string, string> interventionMainIDMapping)
        {
            interventionDetailInfos.ForEach(m =>
            {
                m.AnnualPlanInterventionDetailID = m.GetId();
                // 使用新的主表、目标、项目明细ID
                m.AnnualPlanMainID = mainInfo.AnnualPlanMainID;
                interventionMainIDMapping.TryGetValue(m.AnnualPlanInterventionMainID, out var newInterventionMainID);
                m.AnnualPlanInterventionMainID = newInterventionMainID;
                m.Add("System");
                m.Modify("System");
            });
            interventionDetailInfos = interventionDetailInfos.Where(m => !string.IsNullOrEmpty(m.AnnualPlanInterventionMainID)).ToList();
            await _unitOfWork.GetRepository<AnnualInterventionDetailInfo>().InsertAsync(interventionDetailInfos);
        }
    }
}
