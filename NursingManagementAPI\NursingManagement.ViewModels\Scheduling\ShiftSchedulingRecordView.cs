﻿using NursingManagement.Models;

namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 排班记录
    /// </summary>
    public class ShiftSchedulingRecordView
    {
        /// <summary>
        /// 排班主记录序号
        /// </summary>
        public string ShiftSchedulingRecordID { get; set; }
        /// <summary>
        /// 语言序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 人员编号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 部门序号
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 部门序号
        /// </summary>
        public List<int> DepartmentID2s { get; set; }
        /// <summary>
        /// 排班类型：1：月排班；2：周排班
        /// </summary>
        public string SchedulingType { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        /// 自动排班标记
        /// </summary>
        public bool AutoFlag { get; set; }
        /// <summary>
        /// 状态 0：排班草稿、1：已发布、2：已归档
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 备注，备注本月工作重点
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 排班明细
        /// </summary>
        public List<ShiftSchedulingDetail> ShiftSchedulingDetails { get; set; }
    }
}
