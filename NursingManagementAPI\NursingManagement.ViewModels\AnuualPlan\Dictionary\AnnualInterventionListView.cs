﻿namespace NursingManagement.ViewModels
{
    public class AnnualInterventionListView
    {
        /// <summary>
        /// 项目字典ID
        /// </summary>
        public int AnnualInterventionID { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string InterventionContent { get; set; }
        /// <summary>
        /// 新增人员ID
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增人员姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 修改人员ID
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改人员姓名
        /// </summary>
        public string ModifyEmployeeName { get; set; }
        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 修改日期
        /// </summary>
        public DateTime ModifyDateTime { get; set; }

    }
}
