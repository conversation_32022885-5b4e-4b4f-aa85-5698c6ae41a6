﻿using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Services
{
    /// <summary>
    /// 质控审批动态列
    /// </summary>
    public class HierarchicalQcDynamicColumn
    {
        private readonly IHierarchicalQCRecordRepository _hierarchicalQCRecordRepository;
        private readonly IHierarchicalQCMainRepository _hierarchicalQCMainRepository;
        private readonly IHierarchicalQCFormRepository _hierarchicalQCFormRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IHierarchicalQCObjectRepository _hierarchicalQCObjectRepository;

        public HierarchicalQcDynamicColumn(
            IHierarchicalQCRecordRepository hierarchicalQCRecordRepository,
            IHierarchicalQCMainRepository hierarchicalQCMainRepository,
            IHierarchicalQCFormRepository hierarchicalQCFormRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IHierarchicalQCObjectRepository hierarchicalQCObjectRepository)
        {
            _hierarchicalQCRecordRepository = hierarchicalQCRecordRepository;
            _hierarchicalQCMainRepository = hierarchicalQCMainRepository;
            _hierarchicalQCFormRepository = hierarchicalQCFormRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _hierarchicalQCObjectRepository = hierarchicalQCObjectRepository;
        }



        /// <summary>
        /// 根据主键来源表动态列(维护记录)
        /// </summary>
        /// <param name="mainIDs"></param>
        /// <param name="proveCategory"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string,object>>> GetDynamicColumnListByRecordIDAsync(List<string> mainIDs, string proveCategory)
        {
            var mainInfos = await _hierarchicalQCMainRepository.GetDataByMainIDsAsync(mainIDs);
            if (mainInfos.Count <= 0)
            {
                return new List<Dictionary<string, object>>();
            }
            var recordIDs = mainInfos.Select(m => m.HierarchicalQCRecordID).ToList();
            var qcObjectList = await _hierarchicalQCObjectRepository.GetHierarchicalQCObjectViewByRecordID(recordIDs, 1);
            var formInfos = await _hierarchicalQCFormRepository.GetAll<HierarchicalQCFormInfo>();
            var employeeList = await _employeePersonalDataRepository.GetIDAndNameData();
            var resultList = new List<Dictionary<string, object>>();
            foreach (var item in mainInfos)
            {
                var dynamicView = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase)
                {
                    ["sourceID"] = item.HierarchicalQCMainID,
                    ["point"] = item.Result,
                    ["reason"] = item.Reason,
                    ["assessDate"] = item.AssessDate,
                    ["proveCategory"] = proveCategory,
                    ["formName"] = formInfos.Find(m => m.HierarchicalQCFormID == item.HierarchicalQCFormID)?.FormName ?? "",
                    ["examineEmployee"] = employeeList.Find(m => m.EmployeeID == item.AddEmployeeID)?.EmployeeName ?? item.AddEmployeeID
                };
                resultList.Add(dynamicView);
                // 补充质控对象
                var employeeIDs = qcObjectList.Where(m => m.HierarchicalQCRecordID == item.HierarchicalQCRecordID).Select(m=>m.ObjectValue).ToList();
                if (employeeIDs.Count <=0)
                {
                    continue;
                }
                var qcEmployeeName = employeeList.Where(m => employeeIDs.Contains(m.EmployeeID)).Select(m => m.EmployeeName).ToList();
                if (qcEmployeeName.Count > 0)
                {
                    dynamicView["qcEmployeeName"] = string.Join("、", qcEmployeeName);
                }
            }
            return resultList;
        }
    }
}
