﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 剩余休假天数日志表
    /// </summary>
    [Serializable]
    [Table("RemainingRestDaysLog")]
    public class RemainingRestDaysLogInfo
    {
        /// <summary>
        /// 剩余休假天数表主键ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int RemainingRestDaysLogID { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 员工编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; }
        /// <summary>
        /// 休假可用天数
        /// </summary>
        public decimal? Days { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
    }
}
