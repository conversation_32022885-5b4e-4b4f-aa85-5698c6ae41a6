﻿using Microsoft.AspNetCore.Mvc.Filters;
using NLog;
using NursingManagement.Services.Interface;
using System.Diagnostics;

namespace NursingManagement.API
{
    /// <summary>
    /// 公共拦截器
    /// </summary>
    public class CommonFilterAttribute : ActionFilterAttribute
    {
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        /// <summary>
        /// 时效监听器
        /// </summary>
        private Stopwatch Watch { get; set; }
        /// <summary>
        /// 请求体中的所有值
        /// </summary>
        private string RequestBody { get; set; }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="session"></param>
        public CommonFilterAttribute(ISessionService session)
        {
            _session = session;
        }

        /// <summary>
        /// OnActionExecuting
        /// </summary>
        /// <param name="context"></param>
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            context.HttpContext.Response.Headers.Add("ServerTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm"));
            base.OnActionExecuting(context);
            Watch = new Stopwatch();
            Watch.Start();
        }

        /// <summary>
        /// OnActionExecuted
        /// </summary>
        /// <param name="context"></param>
        public override void OnActionExecuted(ActionExecutedContext context)
        {
            int seconds = 60000;
            base.OnActionExecuted(context);
            Watch.Stop();
            string url = context.HttpContext.Request.Host + context.HttpContext.Request.Path + context.HttpContext.Request.QueryString;
            string method = context.HttpContext.Request.Method;

            string res = "";
            bool success = true;
            if (context.Result == null)
            {
                success = false;
                res = "未获取到结果，返回的数据无法序列化";
                return;
            }
            try
            {
                var result = (context.Result as dynamic).Value;
                if (result != null)
                {
                    success = (result.Code ?? 1) == 1;
                    res = result.Message;
                }
                if (!success || Watch.Elapsed.TotalMilliseconds > seconds)
                {
                    long contentLen = context.HttpContext.Request.ContentLength == null ? 0 : context.HttpContext.Request.ContentLength.Value;
                    if (contentLen > 0)
                    {
                        if (context.HttpContext.Request.Form.Count == 0)
                        {
                            // 读取请求体中所有内容
                            System.IO.Stream stream = context.HttpContext.Request.Body;
                            if (context.HttpContext.Request.Method == "POST")
                            {
                                stream.Position = 0;
                            }
                            byte[] buffer = new byte[contentLen];
                            stream.Read(buffer, 0, buffer.Length);
                            // 转化为字符串
                            RequestBody = System.Text.Encoding.UTF8.GetString(buffer);
                        }
                        else
                        {
                            RequestBody = "";
                            foreach (var form in context.HttpContext.Request.Form)
                            {
                                if (RequestBody.Length > 0)
                                {
                                    RequestBody += ";";
                                }
                                RequestBody += form.Key + ":" + form.Value;
                            }
                        }
                    }
                    string info = $"\r\n地址：{url}\r\n" +
                    $"方式：{method}\r\n" +
                    $"Web页面：{context.HttpContext.Request.Headers["Referer"].ToString()}\r\n" +
                    $"请求体：{RequestBody}\r\n" +
                    $"结果：{res}\r\n" +
                    $"客户端IP：{context.HttpContext.Connection.RemoteIpAddress.ToString()}\r\n" +
                    //$"用户：{userID}\r\n" +                
                    $"浏览器：{context.HttpContext.Request.Headers["User-Agent"].ToString()}\r\n" +
                    $"耗时：{Watch.Elapsed.TotalMilliseconds} 毫秒";

                    if (success && Watch.Elapsed.TotalMilliseconds > seconds)
                    {
                        _logger.Warn("警告，执行成功，但请求时间大于" + seconds + "毫秒" + info);
                    }
                    else
                    {
                        _logger.Warn("执行错误," + info);
                    }
                }
            }
            catch (Exception)
            {
                success = false;
                res = "未获取到结果，返回的数据无法序列化";
            }

        }
    }
}