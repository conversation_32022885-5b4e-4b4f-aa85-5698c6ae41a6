﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Employee;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 人事档案控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/Employee")]
    [EnableCors("any")]
    public class EmployeeController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IEmployeeService _employeeService;
        private readonly IEmployeeRoleService _employeeRoleService;

        /// <summary>
        /// 人事档案构造器
        /// </summary>
        public EmployeeController(
            ISessionService session
            , IEmployeeService employeeService
            , IEmployeeRoleService employeeRoleService
        )
        {
            _session = session;
            _employeeService = employeeService;
            _employeeRoleService = employeeRoleService;
        }

        /// <summary>
        /// 获取人事档案的人员清单
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetEmployeeList")]
        public async Task<IActionResult> GetEmployeeList([FromBody] EmployeeQueryView employeeQueryView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            employeeQueryView.HospitalID = session.HospitalID;
            result.Data = await _employeeService.GetEmployeeList(employeeQueryView);
            return result.ToJson();
        }

        /// <summary>
        /// 获取单个人事档案的人员清单
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOnJobInfo")]
        public async Task<IActionResult> GetOnJobInfo(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetOnJobInfoAsync(employeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取人事档案的人员清单
        /// </summary>
        /// <param name="onJobView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveOnJobInfo")]
        public async Task<IActionResult> SaveOnJobInfo([FromBody] EmployeeOnJobView onJobView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.SaveOnJobInfoAsync(onJobView, session?.EmployeeID ?? onJobView?.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取员工档案头信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPersonalFileHeaderInfo")]
        public async Task<IActionResult> GetPersonalFileHeaderInfo(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetPersonalFileHeaderInfoAsync(employeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取员工个人信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeePersonalData")]
        public async Task<IActionResult> GetEmployeePersonalData(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmployeePersonalData(employeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取人员借调数据
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="effectiveRecordSwitch">有效记录开关， True:显示有效记录，结束时间在当前时间之后的数据并且没有撤销审批的数据 False:显示历史记录，结束时间在当前时间之前的数据或者已经撤销审批的数据</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeSecondmenRecordList")]
        public async Task<IActionResult> GetEmployeeSecondmenRecordList(string employeeID, bool effectiveRecordSwitch)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmployeeSecondmenRecordList(employeeID, effectiveRecordSwitch, session);
            return result.ToJson();
        }

        /// <summary>
        /// 删除人员借调数据
        /// </summary>
        /// <param name="employeeSecondmentRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteEmployeeSecondmenRecord")]
        public async Task<IActionResult> DeleteEmployeeSecondmenRecord(string employeeSecondmentRecordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.DeleteEmployeeSecondmenRecord(employeeSecondmentRecordID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存人员借调数据
        /// </summary>
        /// <param name="recordView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEmployeeSecondmenRecord")]
        public async Task<IActionResult> SaveEmployeeSecondmenRecord([FromBody] EmployeeSecondmentRecordView recordView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            recordView.HospitalID = session.HospitalID;
            recordView.UserID = session.EmployeeID;
            result.Data = await _employeeService.SaveEmployeeSecondmenRecord(recordView);
            return result.ToJson();
        }

        /// <summary>
        /// 获取带教模块初始化数据
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns>返回的data中包含三个列表（带教关系信息、被带教员工、带教老师）</returns>
        [HttpGet]
        [Route("GetEmployeeTeachingRelationView")]
        public async Task<IActionResult> GetEmployeeTeachingRelationView(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmployeeTeachingRelationView(departmentID);
            return result.ToJson();
        }

        /// <summary>
        /// 新增带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationView">保存数据</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveTeachRelation")]
        public async Task<IActionResult> SaveTeachRelation([FromBody] EmployeeTeachingRelationView employeeTeachingRelationView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.SaveTeachRelation(employeeTeachingRelationView, session.EmployeeID);

            return result.ToJson();
        }

        /// <summary>
        /// 停止带教
        /// </summary>
        /// <param name="employeeTeachingRelationID">带教关系主键ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("StopTeachRelation")]
        public async Task<IActionResult> StopTeachRelation(int employeeTeachingRelationID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.StopTeachRelation(employeeTeachingRelationID, session.EmployeeID);

            return result.ToJson();
        }

        /// <summary>
        /// 删除带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationID">带教关系主键ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteTeachRelation")]
        public async Task<IActionResult> DeleteTeachRelation(int employeeTeachingRelationID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.DeleteTeachRelation(employeeTeachingRelationID, session.EmployeeID);

            return result.ToJson();
        }

        /// <summary>
        /// 批量保存数据
        /// </summary>
        /// <param name="employeeTeachingRelationView">保存的数据</param>
        /// <returns></returns>
        [HttpPost]
        [Route("BachSaveTeachRelation")]
        public async Task<IActionResult> BachSaveTeachRelation([FromBody] List<EmployeeTeachingRelationView> employeeTeachingRelationView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.BachSaveTeachRelation(employeeTeachingRelationView, session.EmployeeID);

            return result.ToJson();
        }

        /// <summary>
        /// 获取部门可以变更的人员
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentChangeRequestEmployeeByDepartmentID")]
        public async Task<IActionResult> GetDepartmentChangeRequestEmployeeByDepartmentID(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetDepartmentChangeRequestEmployeeByDepartmentID(departmentID, session.HospitalID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存人员部门变更申请
        /// </summary>
        /// <param name="saveViews"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEmployeeDepartmentChangeRequest")]
        public async Task<IActionResult> SaveEmployeeDepartmentChangeRequest([FromBody] EmployeeDepartmentChangeSaveView saveViews)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.SaveEmployeeDepartmentChangeRequest(saveViews.EmployeeListViews, saveViews.DepartmentID, session);
            return result.ToJson();
        }

        /// <summary>
        /// 获取员工部门调动记录
        /// </summary>
        /// <param name="requestStartDate">申请开始时间</param>
        /// <param name="requestEndDate">申请开始时间</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeDepartmentChangeRequestView")]
        public async Task<IActionResult> GetEmployeeDepartmentChangeRequestView(DateTime requestStartDate, DateTime requestEndDate)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmployeeDepartmentChangeRequestView(requestStartDate, requestEndDate, session);
            return result.ToJson();
        }

        /// <summary>
        /// 删除员工部门调动记录
        /// </summary>
        /// <param name="requestData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteEmployeeDepartmentChangeRequest")]
        public async Task<IActionResult> DeleteEmployeeDepartmentChangeRequest([FromBody] EmployeeDepartmentChangeRequestInfo requestData)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.DeleteEmployeeDepartmentChangeRequest(requestData.ID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存员工服饰尺码信息
        /// </summary>
        /// <param name="employeeClothingSize">员工服饰尺码信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEmployeeClothingSizes")]
        public async Task<IActionResult> SaveEmployeeClothingSizes([FromBody] EmployeeClothingSizesView employeeClothingSize)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            employeeClothingSize.EmployeeID ??= session.EmployeeID;
            result.Data = await _employeeService.SaveEmployeeClothingSizes(employeeClothingSize, session.HospitalID);
            return result.ToJson();
        }

        #region 人员多组织架构部门

        /// <summary>
        /// 获取员工多组织架构部门列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeToDepartments")]
        public async Task<IActionResult> GetEmployeeToDepartments()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmpToDeptViews();
            return result.ToJson();
        }

        /// <summary>
        /// 获取某人员所属部门
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeDepartmentsByEmployeeID")]
        public async Task<IActionResult> GetEmployeeDepartmentsByEmployeeID(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmpDepartmentsByEmployeeID(employeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存某成员的部门信息
        /// </summary>
        /// <param name="view">待保存View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveEmployeeToDepartments")]
        public async Task<IActionResult> SaveEmployeeToDepartments([FromBody] EmployeeToDepartmentView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(view, session);
            if(await _employeeService.SaveEmployeeToDepartments(view, session.EmployeeID))
            {
                result.Sucess();
            }
            return result.ToJson();
        }

        /// <summary>
        /// 删除某成员的所有所属部门
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteEmployeeToDepartmentByEmployeeID")]
        public async Task<IActionResult> DeleteEmployeeToDepartmentByEmployeeID(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.DeleteEmployeeToDepartmentByEmployeeID(employeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 根据员工ID和组织类型获取员工所属部门的信息。
        /// </summary>
        /// <param name="employeeID">员工的ID。</param>
        /// <param name="organizationType">组织类型。</param>
        /// <returns>包含员工组织部门信息</returns>
        [HttpGet]
        [Route("GetEmployeeToDeptByEmployeeIDAndOrganizationType")]
        public async Task<IActionResult> GetEmployeeToDeptByEmployeeIDAndOrganizationType(string employeeID, string organizationType)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmployeeToDeptByEmployeeIDAndOrganizationTypeAsync(employeeID, organizationType);
            return result.ToJson();
        }

        #endregion

        /// <summary>
        /// 保存个人特长数据
        /// </summary>
        /// <param name="employeeStrengthViews"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("saveEmployeeStrengths")]
        public async Task<IActionResult> SaveEmployeeStrengths([FromBody] List<EmployeeStrengthView> employeeStrengthViews)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            employeeStrengthViews.FirstOrDefault().EmployeeID ??= session.EmployeeID;
            result.Data = await _employeeService.SaveEmployeeStrengthsAsync(employeeStrengthViews);
            return result.ToJson();
        }

        /// <summary>
        /// 获取员工体检信息列表
        /// </summary>
        /// <param name="employeeID">员工明细</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPhyExamList")]
        public async Task<IActionResult> GetPhyExamList(string employeeID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetPhyExamList(employeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 根据检验号和体检次数获取检验明细数据
        /// </summary>
        /// <param name="phyExamID">体检号</param>
        /// <param name="visitID">体检次数</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPhyExamRecord")]
        public async Task<IActionResult> GetPhyExamRecord(string phyExamID, int visitID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetPhyExamRecord(phyExamID, visitID);
            return result.ToJson();
        }

        /// <summary>
        /// 删除个人特长数据
        /// </summary>
        /// <param name="employeeStrengthID">特长主键</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteEmployeeStrengths")]
        public async Task<IActionResult> DeleteEmployeeStrengths(string employeeStrengthID)
        {
            var result = new ResponseResult();
            try
            {
                var session = await _session.GetSession();
                if (session == null)
                {
                    result.TimeOut();
                    return result.ToJson();
                }
                result.Data = await _employeeService.DeleteEmployeeStrengthsAsync(employeeStrengthID, session.EmployeeID);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }

        /// <summary>
        /// 删除个人任职记录
        /// </summary>
        /// <param name="employeeEmploymentRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteEmployeePosition")]
        public async Task<IActionResult> DeleteEmployeePosition(string employeeEmploymentRecordID)
        {
            var result = new ResponseResult();
            try
            {
                var session = await _session.GetSession();
                if (session == null)
                {
                    result.TimeOut();
                    return result.ToJson();
                }
                result.Data = await _employeeService.DeleteEmployeePositionAsync(employeeEmploymentRecordID, session.EmployeeID);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }

        /// <summary>
        /// 获取人员对应的部门
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeToRole")]
        public async Task<IActionResult> GetPatientEmployeeToDepartment(string employeeID)
        {
            var result = new ResponseResult();
            try
            {
                var session = await _session.GetSession();
                if (session == null)
                {
                    result.TimeOut();
                    return result.ToJson();
                }
                result.Data = await _employeeRoleService.GetEmployeeRoleAsync(employeeID);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                result.Message = ex.Message;
            }
            return result.ToJson();
        }

        /// <summary>
        /// 获取用户权限科室
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeDepartmentIDAndNames")]
        public async Task<IActionResult> GetEmployeeDepartmentIDAndNames(string employeeID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _employeeService.GetEmployeeDepartmentIDAndNames(employeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取员工信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeesInformation")]
        public async Task<IActionResult> GetEmployeesInformation()
        {
            var result = new ResponseResult
            {
                Data = await _employeeService.GetEmployeesInformation()
            };
            return result.ToJson();
        }

        /// <summary>
        /// 获取员工离职列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("GetEmployeeResignationList")]
        public async Task<IActionResult> GetEmployeeResignationList([FromBody] EmployeeQueryView queryView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            queryView.HospitalID = session.HospitalID;
            result.Data = await _employeeService.GetEmployeeResignationList(queryView);
            return result.ToJson();
        }
    }
}
