﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划项目明细
    /// </summary>
    public interface IAnnualPlanProjectDetailRepository : IBaseAPDetailRepository
    {
        /// <summary>
        /// 获取项目明细
        /// </summary>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <returns></returns>
        Task<AnnualPlanProjectDetailInfo> GetDetailByID(string projectDetailID);
        /// <summary>
        /// 根据分组ID获取明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        Task<List<APProjectDetail>> GetViewsByGroupID(string mainID, string groupID);
        /// <summary>
        /// 根据分组ID获取项目明细ID集合
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        Task<string[]> GetProjectDetailIDsByGroupID(string groupID);
        /// <summary>
        /// 获取当前分组的明细实体
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanProjectDetailInfo>> GetProjectInfosByGroupID(string groupID);
        /// <summary>
        /// 更新组内序号之后的明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="sort">序号</param>
        /// <returns></returns>
        Task<List<AnnualPlanProjectDetailInfo>> GetAfterSortDetail(string mainID, int sort);
        /// <summary>
        /// 根据主表ID获取工作项目
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalIDs">目标业务ID</param>
        /// <returns></returns>
        Task<List<APProjectDetail>> GetViewsByPlanMainID(string mainID, string[] mainGoalIDs = null);
        /// <summary>
        /// 根据主表ID获取工作项目集合，不跟踪
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanProjectDetailInfo>> GetInfosByPlanMainIDAsNoTracking(string annualPlanMainID);
        /// <summary>
        /// 根据主表ID获取工作项目集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanProjectDetailInfo>> GetInfosByMainID(string annualPlanMainID);
        /// <summary>
        /// 获取简略的项目明细集合
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <param name="mainGoalIDs">目标ID集合</param>
        /// <returns></returns>
        Task<APProjectDetail[]> GetProjectDetailsByPlanMainID(string planMainID, string[] mainGoalIDs = null);
        /// <summary>
        /// 根据主表ID和MarkID获取工作项目内容集合，不跟踪
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <param name="markIDs">标识ID集合</param>
        /// <param name="mainGoalID">主记录目标内容主键ID</param>
        /// <returns></returns>
        Task<List<string>> GetProjectDetailContentAsNoTracking(string annualPlanMainID, List<string> markIDs, string mainGoalID);
        /// <summary>
        /// 分组序号
        /// </summary>
        /// <param name="groupID">分组序号</param>
        /// <param name="groupSort">当前分组序号</param>
        /// <returns></returns>
        Task<int> GetMaxSortByGroupID(string groupID, int groupSort);
        /// <summary>
        /// 根据主表ID获取工作项目集合
        /// </summary>
        /// <param name="annualPlanMainID">目标表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanProjectDetailInfo>> GetInfosByMainGoalID(string mainGoalID);
    }
}
