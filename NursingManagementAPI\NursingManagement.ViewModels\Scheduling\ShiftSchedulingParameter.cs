﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 智能排班相关参数
    /// </summary>
    public class ShiftSchedulingParameter
    {
        /// <summary>
        /// 底部实时日统计条件
        /// </summary>
        public List<ShiftSchedulingCondition> DailyStatisticsPostCondition { get; set; }

        /// <summary>
        /// 浮动实时月统计条件
        /// </summary>
        public List<ShiftSchedulingCondition> MonthlyStatisticsPostCondition { get; set; }

        /// <summary>
        /// 排班特殊标记集合
        /// </summary>
        public List<ShiftSchedulingMarkView> ShiftSchedulingMarkList { get; set; }

        /// <summary>
        /// 人员可休假天数集合
        /// </summary>
        public Dictionary<string, decimal?> EmployeeRemainingRestDaysDict { get; set; }

        /// <summary>
        /// 排班规则
        /// </summary>
        public Dictionary<string, string> ShiftSchedulingRuleDict { get; set; }

        /// <summary>
        /// 排班模板
        /// </summary>
        public List<KeyValueString> SchedulingTemplateList { get; set; }
    }
}
