﻿namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 表格
    /// </summary>
    public class TableView
    {
        /// <summary>
        /// 构造器
        /// </summary>
        public TableView()
        {
            this.Columns = new List<TableColumn>();
            this.Rows = new List<Dictionary<string, object>>();
        }
        /// <summary>
        /// 表格列
        /// </summary>
        public List<TableColumn> Columns { get; set; }
        /// <summary>
        /// 表格行数据
        /// </summary>
        public List<Dictionary<string, object>> Rows { get; set; }
    }

    /// <summary>
    /// 表格列
    /// </summary>
    public class TableColumn
    {
        /// <summary>
        /// 构造器
        /// </summary>
        public TableColumn() { }
        /// <summary>
        ///  构造器
        /// </summary>
        /// <param name="index"></param>
        /// <param name="name"></param>
        /// <param name="key"></param>
        /// <param name="width"></param>
        /// <param name="sort"></param>
        /// <param name="align"></param>
        /// <param name="childColumns"></param>
        public TableColumn(int index, string name, string key, int? width, int? sort, string align, List<TableColumn> childColumns = null)
        {
            this.Index = index;
            this.Name = name;
            this.Key = key;
            this.Width = width ?? 60;
            this.Sort = sort ?? index;
            this.Align = align ?? "center";
            this.ChildColumns = childColumns;
        }
        /// <summary>
        /// 设置特殊内容
        /// </summary>
        /// <param name="specialContent"></param>
        public void SetSpecialContent(string specialContent)
        {
            SpecialContent = specialContent;
        }
        /// <summary>
        /// 设置周日标记
        /// </summary>
        /// <param name="isSunday"></param>
        public void SetSunday(bool isSunday = true)
        {
            IsSunday = isSunday;
        }
        /// <summary>
        /// 设置冻结
        /// </summary>
        /// <param name="fixedStr"></param>
        public void SetFixed(string fixedStr = "left")
        {
            Fixed = fixedStr;
        }
        /// <summary>
        /// 设置合并
        /// </summary>
        /// <param name="mergeFlag"></param>
        public void SetMerge(bool mergeFlag = true)
        {
            MergeFlag = mergeFlag;
        }
        /// <summary>
        /// 列序号
        /// </summary>
        public int Index { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public object Value { get; set; }
        /// <summary>
        /// key, 用于关联行数据
        /// </summary>
        public string Key { get; set; }
        /// <summary>
        /// 宽度
        /// </summary>
        public int? Width { get; set; }
        /// <summary>
        /// 特殊提提示内容
        /// </summary>
        public string SpecialContent { get; set; }
        /// <summary>
        /// 周日标记
        /// </summary>
        public bool? IsSunday { get; set; }
        /// <summary>
        /// 列排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 对齐方式
        /// </summary>
        public string Align { get; set; }
        /// <summary>
        /// 列冻结  'left' 、'right'
        /// </summary>
        public string Fixed { get; set; }
        /// <summary>
        /// 合并列标记
        /// </summary>
        public bool? MergeFlag { get; set; }
        /// <summary>
        ///  前端呈现组件类型：T：输入框、TN：数字输入框
        /// </summary>
        public string ComponentType { get; set; }
        /// <summary>
        /// 子列
        /// </summary>
        public List<TableColumn> ChildColumns { get; set; }
    }
}
