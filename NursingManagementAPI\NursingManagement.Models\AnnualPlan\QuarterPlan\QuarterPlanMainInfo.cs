﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    [Serializable]
    [Table("QuarterPlanMain")]
    public class QuarterPlanMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 季度计划主表ID，主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string QuarterPlanMainID { get; set; }
        /// <summary>
        /// 年度计划主表ID，外键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        public int Year { get; set; }
        /// <summary>
        /// 季度
        /// </summary>
        public int Quarter { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 状态，1：已发布；0：未发布
        /// </summary>
        public int StatusCode { get; set; }
    }
}
