﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 审批流程节点明细表
    /// </summary>
    [Serializable]
    [Table("ApproveProcessNodeDetail")]
    public class ApproveProcessNodeDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 审批流程节点明细唯一码，Guid
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ApproveNodeDetailID { get; set; }
        /// <summary>
        /// 审批流程节点唯一码
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveNodeID { get; set; }
        /// <summary>
        /// 审批流程设置表ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveProcessID { get; set; }
        /// <summary>
        /// 审批流程节点明细类型
        /// </summary>
        public ApproveProcessNodeDetailType NodeDetailType { get; set; }
        /// <summary>
        /// 明细类型对应ID（工号/职务ID）
        /// </summary>
        public string DataValue { get; set; }
        /// <summary>
        /// 如果此节点审批需要指定部门做审批，则填写DepartmentID，如果为空，则默认发起人的部门
        /// </summary>
        public int? DepartmentID { get; set; }
    }
    public enum ApproveProcessNodeDetailType
    {
        /// <summary>
        /// 人员
        /// </summary>
        Employee = 1,
        /// <summary>
        /// 职务
        /// </summary>
        Position = 2,
        /// <summary>
        /// 业务审批人
        /// </summary>
        SelfSelected = 3,
    }
}
