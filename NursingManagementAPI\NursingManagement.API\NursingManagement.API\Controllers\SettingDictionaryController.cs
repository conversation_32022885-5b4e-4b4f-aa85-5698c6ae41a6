﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Produces("application/json")]
    [Route("api/SettingDictionary")]
    [EnableCors("any")]
    public class SettingDictionaryController : ControllerBase
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly ISessionService _session;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="settingDictionaryService"></param>
        /// <param name="session"></param>
        public SettingDictionaryController(
            ISettingDictionaryService settingDictionaryService
            , ISessionService session

            )
        {
            _settingDictionaryService = settingDictionaryService;
            _session = session;
        }
        /// <summary>
        ///  获取非国标字典
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSettingDictionaryDict")]
        public async Task<IActionResult> GetSettingDictionaryDict([FromQuery] SettingDictionaryParams settingDictionaryParams)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _settingDictionaryService.GetSettingDictionaryDict(settingDictionaryParams);
            return result.ToJson();
        }

        /// <summary>
        /// 获取语言列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetLanguageList")]
        public async Task<IActionResult> GetLanguageList()
        {
            var result = new ResponseResult();

            result.Data = await _settingDictionaryService.GetLanguageList();
            return result.ToJson();
        }

        /// <summary>
        /// 获取审批页面跳转按钮配置（审批类别->决定是否先手跳转功能）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetApprovalJumpSetting")]
        public async Task<IActionResult> GetApprovalJumpSetting()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _settingDictionaryService.GetApprovalJumpSettingAsync();
            return result.ToJson();
        }
        /// <summary>
        ///  开关，判断是否按照要求显示某项内容
        /// </summary>
        /// <param name="settingDictionaryParams"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSettingSwitch")]
        public async Task<IActionResult> GetSettingSwitch([FromQuery] SettingDictionaryParams settingDictionaryParams)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _settingDictionaryService.GetSettingSwitchAsync(settingDictionaryParams);
            return result.ToJson();
        }

        /// <summary>
        /// 获取分类级联选择器数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetProveCategoryCascaderList")]
        public async Task<IActionResult> GetProveCategoryCascaderList()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _settingDictionaryService.GetProveCategoryCascaderList();
            return result.ToJson();
        }
        /// <summary>
        /// 获取组织架构类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetOrganizationTypes")]
        public async Task<IActionResult> GetOrganizationTypes()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _settingDictionaryService.GetOrganizationTypes();
            return result.ToJson();
        }
        /// <summary>
        /// 获取考核/培训分类数据
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSettingDictionaryMaintain")]
        public async Task<IActionResult> GetSettingDictionaryMaintain(string settingType, string settingTypeCode)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _settingDictionaryService.GetSettingDictionaryMaintain(settingType, settingTypeCode);
            return result.ToJson();
        }
        /// <summary>
        ///  删除分类数据
        /// </summary>
        /// <param name="settingValue"></param>
        /// <param name="settingType"></param>
        /// <param name="settingTypeCode"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteSettingDictionaryMaintain")]
        public async Task<IActionResult> DeleteSettingDictionaryMaintain(string settingValue, string settingType, string settingTypeCode)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _settingDictionaryService.DeleteSettingDictionaryMaintain(settingValue, settingType, settingTypeCode, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存分类数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveSettingDictionaryMaintain")]
        public async Task<IActionResult> SaveSettingDictionaryMaintain([FromBody] TrainingExamineClassifiedView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.EmployeeID = session.EmployeeID;
            view.HospitalID = session.HospitalID;
            view.Language = session.Language;
            result.Data = await _settingDictionaryService.SaveSettingDictionaryMaintain(view);
            return result.ToJson();
        }
        /// <summary>
        /// 根据SettingTypeCode和SettingTypeValue获取配置键值对配置
        /// </summary>
        /// <param name="settingTypeCode"></param>
        /// <param name="settingTypeValues"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSettingDictionaryByCodeValue")]
        public async Task<IActionResult> GetSettingDictionaryByCodeValue(string settingTypeCode, string settingTypeValues)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var settingTypeValueList = ListToJson.ToList<List<string>>(settingTypeValues);
            result.Data = await _settingDictionaryService.GetSettingDictionaryByCodeValue(settingTypeCode, settingTypeValueList);
            return result.ToJson();
        }

        /// <summary>
        /// 根据SettingTypeCode和SettingTypeValue获取配置级联数据
        /// </summary>
        /// <param name="settingTypeCode"></param>
        /// <param name="settingTypeValues"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCascaderSettingDictionary")]
        public async Task<IActionResult> GetCascaderSettingDictionary(string settingTypeCode, string settingTypeValues)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var settingTypeValueList = ListToJson.ToList<List<string>>(settingTypeValues);
            result.Data = await _settingDictionaryService.GetCascaderSettingDictionary(settingTypeCode, settingTypeValueList);
            return result.ToJson();
        }
    }
}
