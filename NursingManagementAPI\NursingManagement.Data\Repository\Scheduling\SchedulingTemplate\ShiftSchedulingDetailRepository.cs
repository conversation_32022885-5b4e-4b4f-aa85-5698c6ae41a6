﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class SchedulingTemplateDetailRepository : ISchedulingTemplateDetailRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="nursingManagementDbContext"></param>
        public SchedulingTemplateDetailRepository(
            NursingManagementDbContext nursingManagementDbContext
        )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }
        /// <summary>
        /// 获取排班明细记录
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<SchedulingTemplateDetailInfo>> GetDetailByRecordID(string schedulingTemplateRecordID)
        {
            return await _nursingManagementDbContext.SchedulingTemplateDetailInfos
                   .Where(m => m.SchedulingTemplateRecordID == schedulingTemplateRecordID && m.DeleteFlag != "*")
                   .ToListAsync();
        }
    }
}
