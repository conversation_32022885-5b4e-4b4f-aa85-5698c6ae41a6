﻿namespace NursingManagement.ViewModels
{
    public class EmployeeDepartmentChangeView
    {
        /// <summary>
        /// 部门调整申请记录主键ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 调整部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 原部门ID
        /// </summary>
        public int OriginalDepartmentID { get; set; }
        /// <summary>
        /// 调整部门名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 原部门名称
        /// </summary>
        public string OriginalDepartmentName { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 状态名称
        /// </summary>
        public string Status { get; set; }
    }
}
