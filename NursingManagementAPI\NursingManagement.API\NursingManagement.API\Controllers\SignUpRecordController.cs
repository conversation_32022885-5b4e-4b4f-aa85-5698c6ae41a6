﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 报名记录控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/SignUpRecord")]
    [EnableCors("any")]
    public class SignUpRecordController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly ISignUpRecordService _signUpRecordService;

        /// <summary>
        /// 报名记录控制器构造函数
        /// </summary>
        /// <param name="session"></param>
        /// <param name="signUpRecordService"></param>
        public SignUpRecordController(
            ISessionService session,
            ISignUpRecordService signUpRecordService)
        {
            _session = session;
            _signUpRecordService = signUpRecordService;
        }

        /// <summary>
        /// 获取报名记录列表
        /// </summary>
        /// <param name="sourceType">来源类别</param>
        /// <param name="sourceID">来源ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSignUpRecordList")]
        public async Task<IActionResult> GetSignUpRecordList(string sourceType, string sourceID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _signUpRecordService.GetSignUpRecordListAsync(sourceType, sourceID);
            result.Sucess();
            return result.ToJson();
        }

        /// <summary>
        /// 保存报名记录
        /// </summary>
        /// <param name="signUpRecord">报名记录信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveSignUpRecord")]
        public async Task<IActionResult> SaveSignUpRecord([FromBody] SignUpRecordInfo signUpRecord)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _signUpRecordService.SaveSignUpRecordAsync(signUpRecord, session.EmployeeID);
            result.Sucess();
            return result.ToJson();
        }

        /// <summary>
        /// 删除报名记录
        /// </summary>
        /// <param name="signUpRecordID">报名记录ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteSignUpRecord")]
        public async Task<IActionResult> DeleteSignUpRecord(string signUpRecordID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _signUpRecordService.DeleteSignUpRcordAsync(signUpRecordID, session.EmployeeID);
            result.Sucess();
            return result.ToJson();
        }
    }
}