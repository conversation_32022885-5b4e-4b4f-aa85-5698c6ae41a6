﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Post;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 岗位控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/Post")]
    [EnableCors("any")]
    public class PostController : Controller
    {
        private readonly ISessionService _session;
        private readonly IPostService _postService;

        /// <summary>
        /// 岗位控制器
        /// </summary>
        public PostController(
            ISessionService session
            , IPostService postService
        )
        {
            _session = session;
            _postService = postService;
        }

        /// <summary>
        /// 获取科室岗位字典
        /// </summary>
        /// <param name="departmentID">科室代码</param>
        [HttpGet]
        [Route("GetDepartmentPostList")]
        public async Task<IActionResult> GetDepartmentPostList(int departmentID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.GetDepartmentPostsAsync(departmentID); 
            return result.ToJson();
        }
        /// <summary>
        /// 新增部门和修改部门岗位
        /// </summary>
        /// <param name="saveDepartmentPostView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveDepartmentPost")]
        public async Task<IActionResult> SaveDepartmentPost([FromBody] SaveDepartmentPostView saveDepartmentPostView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var resultBool = await _postService.SaveDepartmentPost(saveDepartmentPostView, session.HospitalID, session.Language);
            result.Data = resultBool;
            if (string.IsNullOrEmpty(resultBool))
            {
                result.Error("保存失败！");
            }
            return result.ToJson();
        }
        /// <summary>
        /// 批量新增部门岗位
        /// </summary>
        /// <param name="saveDepartmentPostViews"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("BatchSaveDepartmentPost")]
        public async Task<IActionResult> BatchSaveDepartmentPost([FromBody] List<SaveDepartmentPostView> saveDepartmentPostViews)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var resultBool = await _postService.BatchSaveDepartmentPost(saveDepartmentPostViews, session.HospitalID, session.Language);
            result.Data = resultBool;
            if (!resultBool)
            {
                result.Code = 0;
                result.Message = "新增失败！";
            }
            return result.ToJson();
        }
        /// <summary>
        /// 临时导数据使用
        /// </summary>
        /// <param name="postDescriptionList"></param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SavePostDescriptionList")]
        public async Task<IActionResult> SavePostDescriptionList([FromBody] List<PostDescriptionInfo> postDescriptionList)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.SavePostDescriptionList(postDescriptionList);
            return result.ToJson();
        }

        /// <summary>
        /// 获取科室岗位说明书
        /// </summary>
        /// <param name="departmentID">科室代码</param>
        [HttpGet]
        [Route("GetPostDescriptionList")]
        public async Task<IActionResult> GetPostDescriptionList(int departmentID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.GetPostDescriptionInfosAsync(departmentID, session.HospitalID, session.Language);
            return result.ToJson();
        }
        /// <summary>
        /// 获取岗位说明书
        /// </summary>
        /// <param name="departmentID">科室代码</param>
        /// <param name="postID">岗位序号</param>
        [HttpGet]
        [Route("GetPostDescription")]
        public async Task<IActionResult> GetPostDescription(int departmentID, int postID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.GetPostDescriptionInfoAsync(departmentID, postID, session.HospitalID, session.Language);
            return result.ToJson();
        }
        /// <summary>
        /// 更新部门岗位信息
        /// </summary>
        /// <param name="updateDepartmentPostView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateDepartmentPost")]
        public async Task<IActionResult> UpdateDepartmentPost([FromBody] UpdateDepartmentPostView updateDepartmentPostView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            updateDepartmentPostView.ModifyEmployeeID = session.EmployeeID;
            result.Data = await _postService.UpdatePostStatus(updateDepartmentPostView);
            return result.ToJson();
        }
        /// <summary>
        /// 根据科室ID获取科室岗位设定信息
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentPostSetting")]
        public async Task<IActionResult> GetDepartmentPostSetting(int? departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _postService.GetDepartmentPostSettingAsync(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存科室岗位设定信息
        /// </summary>
        /// <param name="departmentPostSetting">保存的部门岗位能级设定信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveDepartmentPostSetting")]
        public async Task<IActionResult> SaveDepartmentPostSetting([FromBody] DepartmentPostSettingInfo departmentPostSetting)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            departmentPostSetting.HospitalID = session.HospitalID;
            var resultBool = await _postService.SaveDepartmentPostSettingAsync(departmentPostSetting, session.HospitalID, session.EmployeeID);
            result.Data = resultBool;
            if (!resultBool)
            {
                result.Code = 0;
            }
            return result.ToJson();
        }
        /// <summary>
        /// 删除科室岗位设定信息
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <param name="departmentID">科室ID</param>
        /// <param name="type">类型</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteDepartmentPostSetting")]
        public async Task<IActionResult> DeleteDepartmentPostSetting(int postID, int departmentID, string type)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            var resultBool = await _postService.DeleteDepartmentPostSettingAsync(postID, departmentID, session.EmployeeID, type);
            result.Data = resultBool;
            if (!resultBool)
            {
                result.Code = 0;
                result.Message = "删除失败";
            }
            return result.ToJson();
        }
        /// <summary>
        /// 获取科室岗位能级对照数据
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="postID?">岗位ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentPostToCapabilityLevels")]
        public async Task<IActionResult> GetDepartmentPostToCapabilityLevels(int? departmentID, int? postID)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var resultData = await _postService.GetDepartmentPostToCapabilityLevelsAsync(departmentID, postID);
            result.Data = resultData;
            return result.ToJson();
        }

        /// <summary>
        /// 更新科室岗位能级对照信息
        /// </summary>
        /// <param name="bodyView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveDepartmentPostToCapabilityLevel")]
        public async Task<IActionResult> SaveDepartmentPostToCapabilityLevel([FromBody] DepartmentPostToCapabilityLevelInfo bodyView)
        {
            var result = new ResponseResult();


            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var resultData = await _postService.SaveDepartmentPostToCapabilityLevelAsync(bodyView, session.HospitalID, session.EmployeeID);
            result.Data = resultData;
            return result.ToJson();
        }
        /// <summary>
        /// 删除岗位能级关系配置
        /// </summary>
        /// <param name="deleteDepartmentPostToCapabilityLevelView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteDepartmentPostToCapabilityLevel")]
        public async Task<IActionResult> DeleteDepartmentPostToCapabilityLevel([FromBody] DeleteDepartmentPostToCapabilityLevelView deleteDepartmentPostToCapabilityLevelView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            var resultBool = await _postService.DeleteDepartmentPostToCapabilityLevelAsync(deleteDepartmentPostToCapabilityLevelView, session.EmployeeID);
            result.Data = resultBool;
            if (!resultBool)
            {
                result.Code = 0;
                result.Message = "删除失败！";
            }
            return result.ToJson();
        }
        
        /// <summary>
        /// 批量新增部门岗位设定
        /// </summary>
        /// <param name="departmentPostSettingInfos"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("BatchSaveDepartmentPostSetting")]
        public async Task<IActionResult> BatchSaveDepartmentPostSetting([FromBody] List<DepartmentPostSettingInfo> departmentPostSettingInfos)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            var resultBool = await _postService.BatchSaveDepartmentPostSettingAsync(departmentPostSettingInfos, session.HospitalID, session.EmployeeID);
            result.Data = resultBool;
            if (!resultBool)
            {
                result.Code = 0;
                result.Message = "新增失败！";
            }
            return result.ToJson();
        }
        /// <summary>
        /// 批量更新科室岗位能级对照信息
        /// </summary>
        /// <param name="bodyView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveDepartmentPostToCapabilityLevelList")]
        public async Task<IActionResult> SaveDepartmentPostToCapabilityLevelList([FromBody] List<DepartmentPostToCapabilityLevelInfo> bodyView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result = await _postService.SaveDepartmentPostToCapabilityLevelListAsync(bodyView, session.HospitalID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除岗位说明书数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeletePostDescription")]
        public async Task<IActionResult> DeletePostDescription([FromBody] DeletePostDescriptionView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.ModifyEmployeeID = session?.EmployeeID;
            view.HospitalID = session?.HospitalID;
            view.Language = session.Language != 0 ? session.Language : 0;
            result.Data = await _postService.DeletePostDescription(view);
            return result.ToJson();
        }
        /// <summary>
        /// 保存岗位说明书明细内容
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SavePostDescription")]
        public async Task<IActionResult> SavePostDescription([FromBody] SavePostDescriptionView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.HospitalID = session.HospitalID;
            view.Language = session.Language;
            if (!await _postService.CheckPostDescriptionData(view))
            {
                result.Message = "填写版本号已存在说明书，处于停用状态，请更改版本号或状态！";
                return result.ToJson();
            }
            result.Data = await _postService.SavePostDescription(view);
            return result.ToJson();
        }
        /// <summary>
        /// 批量保存岗位说明书明细内容
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("BatchSavePostDescription")]
        public async Task<IActionResult> BatchSavePostDescription([FromBody] List<SavePostDescriptionView> list)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.BatchSavePostDescription(list, session.HospitalID, session.Language);
            return result?.ToJson();
        }
        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPostList")]
        public async Task<IActionResult> GetPostList()
        {
            var result = new ResponseResult
            {
                Data = await _postService.GetPostListAsync()
            };
            return result.ToJson();
        }
        /// <summary>
        /// 保存岗位数据
        /// </summary>
        /// <param name="postView">岗位视图</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SavePostData")]
        public async Task<IActionResult> SavePostData([FromBody] PostView postView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.SavePostDataAsync(postView,session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除岗位数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeletePostData")]
        public async Task<IActionResult> DeletePostData(int postID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.DeletePostDataAsync(postID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 激活岗位数据
        /// </summary>
        /// <param name="postID">岗位ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ActivatePostData")]
        public async Task<IActionResult> ActivatePostData(int postID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.ActivatePostDataAsync(postID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取岗位数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPostWhetherData")]
        public async Task<IActionResult> GetPostWhetherData(int postID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _postService.GetPostWhetherDataAsync(postID, session.HospitalID);
            return result.ToJson();
        }
    }
}
