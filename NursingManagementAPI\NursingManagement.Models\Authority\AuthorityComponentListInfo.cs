using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 页面组件权限表
    /// </summary>
    [Serializable]
    [Table("AuthorityComponentList")]
    public class AuthorityComponentListInfo : MutiModifyInfo
    {

        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 权限角色ID
        /// </summary>
        public int AuthorityRoleID { get; set; }

        /// <summary>
        /// 路由使用组件ID，对应ComponentList表的主键
        /// </summary>
        public int ComponentListID { get; set; }

        /// <summary>
        /// 路由ID，对应RouterList表的主键
        /// </summary>
        public int RouterListID { get; set; }

        /// <summary>
        /// 客户端类型 （1:PC , 2:移动端）
        /// </summary>
        public int ClientType { get; set; }

        /// <summary>
        /// 状态，0不显示，1显示
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        [Column(TypeName = "nverchar(200)")]
        public string Description { get; set; }
    }
}
