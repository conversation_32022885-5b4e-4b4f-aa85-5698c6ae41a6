﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  考勤表   
    /// </summary>
    [Table("AttendanceDetail")]
    public class AttendanceDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考勤明细表记录号，主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string AttendanceDetailID { get; set; }
        /// <summary>
        /// 考勤主表记录号，主键
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AttendanceRecordID { get; set; }
        /// <summary>
        /// 午别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string NoonTypeID { get; set; }
        /// <summary>
        /// 1号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day1 { get; set; }
        /// <summary>
        /// 2号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day2 { get; set; }
        /// <summary>
        /// 3号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day3 { get; set; }
        /// <summary>
        /// 4号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day4 { get; set; }
        /// <summary>
        /// 5号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day5 { get; set; }
        /// <summary>
        /// 6号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day6 { get; set; }
        /// <summary>
        /// 7号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day7 { get; set; }
        /// <summary>
        /// 8号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day8 { get; set; }
        /// <summary>
        /// 9号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day9 { get; set; }
        /// <summary>
        /// 10号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day10 { get; set; }
        /// <summary>
        /// 11号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day11 { get; set; }
        /// <summary>
        /// 12号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day12 { get; set; }
        /// <summary>
        /// 13号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day13 { get; set; }
        /// <summary>
        /// 14号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day14 { get; set; }
        /// <summary>
        /// 15号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day15 { get; set; }
        /// <summary>
        /// 16号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day16 { get; set; }
        /// <summary>
        /// 17号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day17 { get; set; }
        /// <summary>
        /// 18号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day18 { get; set; }
        /// <summary>
        /// 19号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day19 { get; set; }
        /// <summary>
        /// 20号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day20 { get; set; }
        /// <summary>
        /// 21号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day21 { get; set; }
        /// <summary>
        /// 22号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day22 { get; set; }
        /// <summary>
        /// 23号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day23 { get; set; }
        /// <summary>
        /// 24号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day24 { get; set; }
        /// <summary>
        /// 25号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day25 { get; set; }
        /// <summary>
        /// 26号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day26 { get; set; }
        /// <summary>
        /// 27号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day27 { get; set; }
        /// <summary>
        /// 28号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day28 { get; set; }
        /// <summary>
        /// 29号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day29 { get; set; }
        /// <summary>
        /// 30号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day30 { get; set; }
        /// <summary>
        /// 31号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string Day31 { get; set; }
    }
}