﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划分组
    /// </summary>
    public interface IAnnualPlanGoalGroupRepository
    {
        /// <summary>
        /// 获取目标对应分组集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalID">计划目标ID</param>
        /// <returns></returns>
        Task<List<APGroup>> GetAnnualPlanGroups(string mainID, string mainGoalID = null);
        /// <summary>
        /// 获取不跟踪的年度计划目标分组集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanGoalGroupInfo>> GetInfosByPlanMainIDAsNoTracking(string mainID);
        /// <summary>
        /// 获取年度计划目标分组集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanGoalGroupInfo>> GetInfosByMainID(string mainID);
        /// <summary>
        /// 获取年度计划目标分组集合
        /// </summary>
        /// <param name="mainGoalID">目标主表ID</param>
        /// <returns></returns>
        Task<List<AnnualPlanGoalGroupInfo>> GetInfosByMainGoalID(string mainGoalID);
        /// <summary>
        /// 获取目标对应分组集合
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<string[]> GetAPGroupDepartmentsByMainID(string mainID);
        /// <summary>
        /// 获取年度计划目标分组集合
        /// </summary>
        /// <param name="groupIDs">分组ID集合</param>
        /// <returns></returns>
        Task<List<AnnualPlanGoalGroupInfo>> GetGoalGroups(IEnumerable<string> groupIDs);
        /// <summary>
        /// 获取目标分组最大序号
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<int> GetMaxSort(string mainID);
        /// <summary>
        /// 获取年度计划分组数据
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        Task<AnnualPlanGoalGroupInfo> GetGoalGroup(string groupID);
        /// <summary>
        /// 获取年度计划分组对应的序号
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        Task<string[]> GetAPGroupIDsBySort(string planMainID);
        /// <summary>
        /// 获取其后的分组
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="sort">序号</param>
        /// <returns></returns>
        Task<AnnualPlanGoalGroupInfo[]> GetAfterSortGroup(string mainID, int sort);
        /// <summary>
        /// 获取排序后的分组ID集合
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <returns></returns>
        Task<string[]> GetGroupIDsByMainID(string mainID);
    }
}
