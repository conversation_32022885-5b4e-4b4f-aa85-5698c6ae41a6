﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IQuestionBankRepository
    {
        /// <summary>
        /// 根据部门获取数据
        /// </summary>
        /// <param name="departmentIDs"></param>
        /// <param name="isPractical">空获取所有，true获取实操类题库，false获取理论类题库</param>
        /// <returns></returns>
        Task<List<QuestionBankInfo>> GetQuestionBankList(List<int> departmentIDs = null, bool? isPractical = null);
        /// <summary>
        /// 根据主键获取数据
        /// </summary>
        /// <param name="testQuestionBankID"></param>
        /// <returns></returns>
        Task<QuestionBankInfo> GetDataByID(string testQuestionBankID);
        /// <summary>
        /// 获取题库名称和主键的键值对关系
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<SelectOptionsView>> GetQuestionBankDictAsync();
        /// <summary>
        /// 获取实操类题库ID与名称的键值对
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetPracticalQuestionBank();
        /// <summary>
        /// 根据题库ID获取数据，包含子级
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <returns></returns>
        Task<List<QuestionBankInfo>> GetListByBankID(string questionBankID);
        /// <summary>
        /// 获取题库父子关系映射表
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<string, List<string>>> GetQuestionBankParentChildMapping();
        /// <summary>
        /// 根据题库ID获取数据
        /// </summary>
        /// <param name="questionBankIDs">题库ID集</param>
        /// <returns></returns>
        Task<List<QuestionBankInfo>> GetListByBankIDs(List<string> questionBankIDs);
    }
}
