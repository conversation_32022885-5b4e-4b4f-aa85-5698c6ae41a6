﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IDynamicFormDetailConditionRepository : ICacheRepository
    {
        /// <summary>
        /// 根据表单明细ID获取表单明细联动条件集合
        /// </summary>
        /// <param name="dynamicFormDetailID"></param>
        /// <returns></returns>
        Task<List<DynamicFormDetailConditionInfo>> GetDetailConditionsByDetailID(string dynamicFormDetailID);

        /// <summary>
        /// 根据表单明细ID集合获取表单明细联动条件集合
        /// </summary>
        /// <param name="dynamicFormDetailIDs"></param>
        /// <returns></returns>
        Task<List<DynamicFormDetailConditionInfo>> GetDetailConditionsByDetailIDList(List<string> dynamicFormDetailIDs);
    }
}
