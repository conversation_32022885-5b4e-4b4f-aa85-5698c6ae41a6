﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 临时出勤记录表
    /// </summary>
    [Serializable]
    [Table("TemporaryAttendanceRecord")]
    public class TemporaryAttendanceRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 临时出勤记录ID(主键ID)
        /// </summary>
        [Key]
        public int TemporaryAttendanceRecordID { get; set; }
        /// <summary>
        /// 出勤人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AttendanceEmployeeID { get; set; }
        /// <summary>
        /// 出勤日期
        /// </summary>
        public DateTime AttendanceDate { get; set; }
        /// <summary>
        /// 出勤小时数
        /// </summary>
        public decimal AttendanceHours { get; set; }
        /// <summary>
        /// 备班注记图示ID
        /// </summary>
        public int? AdministrationIconID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Remark { get; set; }
    }
}
