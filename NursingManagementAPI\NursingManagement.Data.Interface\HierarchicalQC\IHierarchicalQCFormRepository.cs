﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCFormRepository:ICacheRepository
    {
        /// <summary>
        /// 根据质控等级获取质控字典
        /// </summary>
        /// <param name="level"></param>
        /// <param name="formType"></param>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        Task<List<HierachicalQCFormOptionView>> GetQCFormByLevel(string level, string formType, List<int> departmentIDs);
        /// <summary>
        /// 获取所有配置
        /// </summary>
        /// <returns></returns>
        Task<List<HierarchicalQCFormInfo>> GetAll();
        /// <summary>
        /// 根据主键获取缓存的质控登记信息
        /// </summary>
        /// <param name="hierarchicalQCFormID"></param>
        /// <returns></returns>
        Task<string> GetCachedQCLevelByFormIDAsync(int hierarchicalQCFormID);
        /// <summary>
        /// 获取一个新的ID（现有最大ID+1）
        /// </summary>
        /// <returns></returns>
        Task<int> GetNewID();
        /// <summary>
        /// 根据ID获取表单
        /// </summary>
        /// <param name="hierarchicalQCFormID"></param>
        /// <returns></returns>
        Task<HierarchicalQCFormInfo> GetFormByID(int hierarchicalQCFormID);
    }
}
