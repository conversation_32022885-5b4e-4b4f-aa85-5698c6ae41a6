﻿using NLog;
using System.Text.RegularExpressions;

namespace NursingManagement.Common
{
    public class ImageHelper
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        private static char[] base64CodeArray = new char[]
         {
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            '0', '1', '2', '3', '4',  '5', '6', '7', '8', '9', '+', '/', '='
         };

      
        public static bool IsBase64(string base64Str)
        {
            string strRegex = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";

            if (string.IsNullOrEmpty(base64Str))
                return false;
            if (DateTime.TryParse(base64Str, out _))
                return false;
            if (base64Str.Length % 4 != 0)
                return false;

            return Regex.IsMatch(base64Str, strRegex);
        }
    }
}