﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeEmploymentRecordRepository
    {
        /// <summary>
        /// 根据工号和岗位类型获取任职记录
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="postType">岗位类型</param>
        /// <returns></returns>
        Task<List<EmployeeEmploymentRecordInfo>> GetRecordListAsync(string employeeID, int postType);
        /// <summary>
        /// 根据工号获取任职记录
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeEmploymentRecordInfo>> GetAllRecordListAsync(string employeeID);
        /// <summary>
        /// 根据ID获取数据
        /// </summary>
        /// <param name="employeeEmploymentRecordID"></param>
        /// <returns></returns>
        Task<EmployeeEmploymentRecordInfo> GetDataByID(string employeeEmploymentRecordID);
        /// <summary>
        /// 根据工号集合获取最后一次任职记录
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeEmploymentRecordInfo>> GetLastRecordByEmployeeIDs(List<string> employeeIDs);
    }
}
