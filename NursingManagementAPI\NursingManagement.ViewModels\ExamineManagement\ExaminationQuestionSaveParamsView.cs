﻿namespace NursingManagement.ViewModels
{

    public class ExaminationQuestionSaveParamsView
    {
        /// <summary>
        /// 题目内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 题目类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 来源ID
        /// </summary>
        public int SourceID { get; set; }

        /// <summary>
        /// 难度等级
        /// </summary>
        public int DifficultyLevel { get; set; }

        /// <summary>
        /// 题目标签
        /// </summary>
        public int QuestionTag { get; set; }

        /// <summary>
        /// 题目说明
        /// </summary>
        public string Instructions { get; set; }

        /// <summary>
        /// 题目权重（随机组题使用）
        /// </summary>
        public string FilterWeight { get; set; }

        /// <summary>
        /// 解析
        /// </summary>
        public string Analysis { get; set; }

        /// <summary>
        /// 考核题库ID
        /// </summary>
        public int ExaminationQuestionBankID { get; set; }
    }

}
