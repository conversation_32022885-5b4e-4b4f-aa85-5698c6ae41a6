﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface ISchedulingRequestService
    {
        /// <summary>
        /// 获取单人排班预约数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<SchedulingRequestRecordView>> GetSingleSchedulingData(string employeeID);
        /// <summary>
        /// 获取科室排班预约数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<SchedulingRequestRecordView>> GetDepartmentSchedulingData(int departmentID);
        /// <summary>
        /// 保存记录数据
        /// </summary>
        /// <param name="saveData"></param>
        /// <returns></returns>
        Task<SaveReponseView> SaveSchedulingRequestRecord(SchedulingRequestRecordView saveData);
        /// <summary>
        /// 删除预约记录
        /// </summary>
        /// <param name="schedulingRequestRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> DeleteScheduleRequestRecord(string schedulingRequestRecordID, string employeeID);
        /// <summary>
        /// 创建对应的审批记录
        /// </summary>
        /// <param name="requestRecordInfo">排班预约Model</param>
        /// <returns>是否创建成功</returns>
        Task<bool> CreateOrUpdateApprove(SchedulingRequestRecordInfo requestRecordInfo);
    }
}
