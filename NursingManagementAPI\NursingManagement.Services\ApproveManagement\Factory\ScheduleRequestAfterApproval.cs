﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Data.Interface;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 派班申请之后的结果
    /// </summary>
    public class ScheduleRequestAfterApproval(
        ISchedulingRequestRepository _schedulingRequestRepository,
        IUnitOfWork _unitOfWork,
        IRouterListRepository _routerListRepository,
        ISettingDictionaryRepository _settingDictionaryRepository
        ) : ICommonProcessingAfterApproval
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();

        public override async Task<(string, int)[]> ProcessAfterApprovalAsync(ProcessAfterApprovalView view)
        {
            var schedulingRecordInfo = await _schedulingRequestRepository.GetDataByID(view.SourceID);
            if (schedulingRecordInfo == null)
            {
                _logger.Error("未找到审批记录来源，回写审批结果失败");
                return null;
            }
            //当审批状态改变的时候 回写状态
            if (schedulingRecordInfo.StatusCode != view.ApprovalResult)
            {
                schedulingRecordInfo.StatusCode = view.ApprovalResult;

                if (await _unitOfWork.SaveChangesAsync() <= 0)
                {
                    throw new Exception("更新派班申请信息失败，请联系管理员。");
                }
                var settingParams = new SettingDictionaryParams
                {
                    SettingType = "ApprovalManagement",
                    SettingTypeCode = "ProveCategoryToRoute",
                    SettingTypeValue = "AA-021",

                };
                var settingList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
                var setting = settingList.FirstOrDefault();
                if (setting == null || !int.TryParse(setting.SettingValue, out var routerListID))
                {
                    _logger.Error($"发送完成审批通知失败，找不到SettingType=ProveCategoryToRoute, SettingTypeCode=AA-021的配置");
                    return null;
                }
                var routers = await _routerListRepository.GetInfosByRouterListID(routerListID);
                // 获取跳转 path，PC端、移动端
                var paths = routers.Select(r => (r.Path, r.ClientType)).ToArray();
                return paths;
            }
            
            return [];
            
        }
    }
}
