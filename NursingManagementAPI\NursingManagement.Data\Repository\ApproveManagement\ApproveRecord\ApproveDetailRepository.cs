﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using System.Collections.Immutable;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 审批明细表仓储接口
    /// </summary>
    public class ApproveDetailRepository : IApproveDetailRepository
    {

        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ApproveDetailRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 根据审批主记录获取明细记录
        /// </summary>
        /// <param name="approveMainIDs"></param>
        /// <returns></returns>
        public async Task<List<ApproveDetailInfo>> GetApproveDetailsByMainIDAsync(params string[] approveMainIDs)
        {
            return await _nursingManagementDbContext.ApproveDetailInfos
                 .Where(m => m.DeleteFlag != "*" && approveMainIDs.Contains(m.ApproveMainID)).ToListAsync();
        }
        /// <summary>
        /// 根据主键获取明细数据
        /// </summary>
        /// <param name="detailID"></param>
        /// <returns></returns>
        public async Task<ApproveDetailInfo> GetApproveDetailByDetailIDAsync(string detailID)
        {
            return await _nursingManagementDbContext.ApproveDetailInfos
                .Where(m => m.DeleteFlag != "*" && m.ApproveDetailID == detailID).FirstOrDefaultAsync();
        }

        public async Task<bool> CheckUnApproveDetailsExistOrNotByMainIDAsync(string approveMainID)
        {
            var counts = await _nursingManagementDbContext.ApproveDetailInfos
                .Where(m => m.DeleteFlag != "*" && m.ApproveMainID == approveMainID && string.IsNullOrEmpty(m.StatusCode)).CountAsync();
            return counts > 0;
        }
        /// <summary>
        /// 获取多个节点中 完成的审批明细
        /// </summary>
        /// <param name="approveMainIDs"></param>
        /// <returns></returns>
        public async Task<List<ApproveDetailInfo>> GetCompletedDetailViewsByMainIDAsync(params string[] approveMainIDs)
        {
            return await _nursingManagementDbContext.ApproveDetailInfos
                .Where(m => m.DeleteFlag != "*" && approveMainIDs.Contains(m.ApproveMainID) && m.StatusCode != null)
                .Select(m => new ApproveDetailInfo
                {
                    ApproveDetailID = m.ApproveDetailID,
                    ApproveMainID = m.ApproveMainID,
                })
                .ToListAsync();
        }

        #region NoTrack
        /// <summary>
        /// 获取下一个节点的所有可能审批人
        /// NoTrack
        /// </summary>
        /// <param name="approveMainID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetPreApproveEmployeeIDAsNoTrackAsync(string approveMainID)
        {
            return await _nursingManagementDbContext.ApproveDetailInfos
                .Where(m=>m.ApproveMainID == approveMainID && m.DeleteFlag !="*").Select(m=>m.PreApproveEmployeeID).ToListAsync();
        }

        /// <summary>
        /// 获取整个审批流程中已经完成节点审批的所有审批人员工号
        /// NoTrack
        /// </summary>
        /// <param name="approveMainIDs"></param>
        /// <returns></returns>
        public async Task<List<string>> GetApproveEmployeeIDAsync(List<string> approveMainIDs)
        {
            return await _nursingManagementDbContext.ApproveDetailInfos
                .Where(m => approveMainIDs.Contains( m.ApproveMainID ) && m.DeleteFlag != "*" && !string.IsNullOrEmpty(m.ApproveEmployeeID) ).Select(m => m.ApproveEmployeeID).ToListAsync();
        }
        /// <summary>
        /// 获取整个审批流程中人员未完成审批的记录ID
        /// </summary>
        /// <param name="approveMainIDs">审批维护记录ID</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        public async Task<List<string>> GetNotApproveMainIDsAsync(List<string> approveMainIDs, string employeeID)
        {
            return await _nursingManagementDbContext.ApproveDetailInfos
                .Where(m => approveMainIDs.Contains(m.ApproveMainID) && m.DeleteFlag != "*" && string.IsNullOrEmpty(m.StatusCode) && m.PreApproveEmployeeID.Contains(employeeID))
                .Select(m => m.ApproveMainID).ToListAsync();
        }
        public async Task<Dictionary<string, string>> GetApproveEmployeeIDByRecordIDsAsync(List<string> approveRecordIDList)
        {
            var employeeIDs = new Dictionary<string, string>();
            employeeIDs = await (from approveDetail in _nursingManagementDbContext.ApproveDetailInfos
                                 join approveMain in _nursingManagementDbContext.ApproveMainInfos
                                     on approveDetail.ApproveMainID equals approveMain.ApproveMainID
                                 where approveRecordIDList.Contains(approveMain.ApproveRecordID)
                                    && approveDetail.DeleteFlag != "*"
                                    && approveDetail.ApproveEmployeeID != ""
                                    && approveDetail.ApproveDateTime != null
                                 group approveDetail by approveMain.ApproveRecordID into grouped
                                 select new
                                 {
                                     RecordID = grouped.Key,
                                     EmployeeID = grouped.OrderByDescending(x => x.ApproveDateTime)
                                                         .Select(x => x.ApproveEmployeeID)
                                                         .FirstOrDefault() // 选择最新的记录
                                 })
                    .ToDictionaryAsync(x => x.RecordID, x => x.EmployeeID ?? "");
            return employeeIDs;
        }
        /// <summary>
        /// 根据业务主表ID集合获取多个业务的审批明细记录(部分字段)
        /// </summary>
        /// <param name="approveMainIDs">审批节点记录ID集合</param>
        /// <returns>ApproveMainID、StatusCode、PreApproveEmployeeID</returns>
        public async Task<List<ApproveDetailInfo>> GetPartDetailsByMainIDAsync(params string[] approveMainIDs)
        {
            return await _nursingManagementDbContext.ApproveDetailInfos
                .Where(m => m.DeleteFlag != "*" && approveMainIDs.Contains(m.ApproveMainID))
                .Select(m=>new ApproveDetailInfo
                {
                    ApproveMainID = m.ApproveMainID,
                    StatusCode = m.StatusCode,
                    PreApproveEmployeeID = m.PreApproveEmployeeID,
                }).ToListAsync();
        }
        #endregion
    }
}
;