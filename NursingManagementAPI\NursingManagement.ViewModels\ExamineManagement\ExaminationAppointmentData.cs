﻿namespace NursingManagement.ViewModels
{
    public class ExaminationAppointmentData
    {

        /// <summary>
        /// 预约考核时间
        /// </summary>
        public DateTime ScheduleDate { get; set; }
        /// <summary>
        /// 预约考核时间
        /// </summary>
        public string ScheduleTimeRange { get; set; }
        /// <summary>
        /// 预约监考人姓名
        /// </summary>
        public string ExaminerName { get; set; }
        /// <summary>
        /// 预约监考计划ID
        /// </summary>
        public string ExaminerScheduleID { get; set; }
        /// <summary>
        /// 预约监考人
        /// </summary>
        public List<SelectOptionsView> Examiners { get; set; }
        /// <summary>
        /// 预约人所属部门
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 预约人ID
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 预约人姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 考核计划ID
        /// </summary>
        public string ExaminationRecordID { get; set; }
        /// <summary>
        /// 预约考核计划
        /// </summary>
        public string ExaminationName { get; set; }
        /// <summary>
        /// 实际预约时间
        /// </summary>
        public DateTime AppointmentDate { get; set; }
        /// <summary>
        /// 预约状态（1预约，2撤销）
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 预约状态显示名称
        /// </summary>
        public string StatusName { get; set; }
        /// <summary>
        /// 取消预约的原因（可为空）
        /// </summary>
        public string CancelReason { get; set; }
        /// <summary>
        /// 取消预约的原因（可为空）
        /// </summary>
        public DateTime? CancelDateTime { get; set; }
    }
}
