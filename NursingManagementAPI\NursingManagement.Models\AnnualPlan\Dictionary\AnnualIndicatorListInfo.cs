﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划指标字典
    /// </summary>
    [Serializable]
    [Table("AnnualIndicatorList")]
    public class AnnualIndicatorListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 年度计划指标序号，非自增
        /// </summary>
        public int AnnualIndicatorID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 医院
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 指标启用年份
        /// </summary>
        public int EnableYear { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 指标内容
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string IndicatorContent { get; set; }

    }
}
