﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class HierarchicalQCObjectRepository : IHierarchicalQCObjectRepository
    {
        private readonly NursingManagementDbContext _dbContext = null;

        public HierarchicalQCObjectRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        /// <summary>
        /// 根据主记录集合和质控对象类型获取View
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <param name="objectType"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCObjectInfo>> GetHierarchicalQCObjectViewByRecordID(List<string> recordIDs, int objectType)
        {
            return await _dbContext.HierarchicalQCObjectInfos.Where(m => recordIDs.Contains(m.HierarchicalQCRecordID) && m.ObjectType == objectType && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCObjectInfo()
                {
                    HierarchicalQCRecordID = m.HierarchicalQCRecordID,
                    ObjectValue = m.ObjectValue,
                })
                .ToListAsync();
        }
        /// <summary>
        /// 根据主记录ID和质控对象类型获取View
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="objectType"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCObjectInfo>> GetHierarchicalQCObjectViewByRecordID(string recordID, int objectType)
        {
            return await _dbContext.HierarchicalQCObjectInfos.Where(m => m.HierarchicalQCRecordID == recordID && m.ObjectType == objectType && m.DeleteFlag != "*")
                .Select(m => new HierarchicalQCObjectInfo()
                {
                    HierarchicalQCRecordID = m.HierarchicalQCRecordID,
                    ObjectValue = m.ObjectValue,
                    DeleteFlag = m.DeleteFlag,
                    ModifyDateTime = m.ModifyDateTime,
                    ModifyEmployeeID = m.ModifyEmployeeID,
                })
                .ToListAsync();
        }
        /// <summary>
        /// 根据主记录ID和质控对象类型获取Model 删除使用
        /// </summary>
        /// <param name="recordID"></param>
        /// <param name="objectType"></param>
        /// <returns></returns>
        public async Task<List<HierarchicalQCObjectInfo>> GetHierarchicalQCObjectInfoByRecordID(string recordID, int objectType)
        {
            return await _dbContext.HierarchicalQCObjectInfos.Where(m => m.HierarchicalQCRecordID == recordID && m.ObjectType == objectType && m.DeleteFlag != "*").ToListAsync();
        }
    }

}