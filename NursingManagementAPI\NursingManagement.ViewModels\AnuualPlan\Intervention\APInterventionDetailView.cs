﻿namespace NursingManagement.ViewModels
{
    public class APInterventionDetailView
    {
        /// <summary>
        /// 主键
        /// </summary>
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 明细内容
        /// </summary>
        public string MainGoalContent { get; set; }
        /// <summary>
        /// 明细ID
        /// </summary>
        public string ProjectDetailID { get; set; }
        /// <summary>
        /// 明细内容
        /// </summary>
        public string ItemContent { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public Dictionary<int, bool> PlanMonthDictionary { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public int PlanMonth { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public string PrincipalName { get; set; }
        /// <summary>
        /// 分组负责人名称
        /// </summary>
        public string PrincipalGroupName { get; set; }
        /// <summary>
        /// 年度计划措施主ID
        /// </summary>
        public string AnnualPlanInterventionMainID { get; set; }
        /// <summary>
        /// 目标内容
        /// </summary>
        public string GoalContent { get; set; }
    }
}
