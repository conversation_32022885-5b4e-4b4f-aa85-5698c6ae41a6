﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ShiftSchedulingEmployeeSortRepository : IShiftSchedulingEmployeeSortRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        public ShiftSchedulingEmployeeSortRepository(
            NursingManagementDbContext db
        )
        {
            _nursingManagementDbContext = db;
        }

        public async Task<List<ShiftSchedulingEmployeeSortInfo>> GetEmployeeListByScheduling(string shiftSchedulingRecordID)
        {
            return await _nursingManagementDbContext.ShiftSchedulingEmployeeSortInfos.Where(m => m.ShiftSchedulingRecordID == shiftSchedulingRecordID && m.DeleteFlag != "*").OrderBy(m => m.Sort).ToListAsync();
        }
    }
}
