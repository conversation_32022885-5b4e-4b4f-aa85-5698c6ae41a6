﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ExaminationQuestionRepository : IExaminationQuestionRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ExaminationQuestionRepository(NursingManagementDbContext nursingManagementDbContext)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 根据问题ID获取问题记录
        /// </summary>
        /// <param name="examinationQuestionListID"></param>
        /// <returns></returns>
        public async Task<ExaminationQuestionInfo> GetDataByID(int examinationQuestionListID)
        {
            return await _nursingManagementDbContext.ExaminationQuestionInfos
                .FirstOrDefaultAsync(m => m.ExaminationQuestionID == examinationQuestionListID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据题库ID获取题目列表
        /// </summary>
        /// <param name="questionBankID">问题的ID</param>
        /// <param name="asNoTrack">是否跟踪实体</param>
        /// <returns>考试问题集合。</returns>
        public async Task<List<ExaminationQuestionInfo>> GetListByQuestionBankID(string questionBankID, bool asNoTrack = false)
        {
            if (asNoTrack)
            {
                return await _nursingManagementDbContext.ExaminationQuestionInfos.AsNoTracking()
                    .Where(m => m.QuestionBankID == questionBankID && m.DeleteFlag != "*")
                    .ToListAsync();
            }
            return await _nursingManagementDbContext.ExaminationQuestionInfos
                .Where(m => m.QuestionBankID == questionBankID && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据题库ID集合获取题目数据
        /// </summary>
        /// <param name="questionBankIDs"></param>
        /// <param name="asNoTrack">是否跟踪实体</param>
        /// <returns></returns>
        public async Task<List<ExaminationQuestionInfo>> GetListByQuestionBankIDList(List<string> questionBankIDs, bool asNoTrack = false)
        {
            if (asNoTrack)
            {
                return await _nursingManagementDbContext.ExaminationQuestionInfos.AsNoTracking()
                .Where(m => questionBankIDs.Any(n => n == m.QuestionBankID) && m.DeleteFlag != "*").ToListAsync();
            }
            return await _nursingManagementDbContext.ExaminationQuestionInfos
                .Where(m => questionBankIDs.Any(n => n == m.QuestionBankID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据题库ID，按组获取问题ID与描述的键值对
        /// </summary>
        /// <param name="questionBankIDs">题库ID集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, (int questionID, string questionContent)[]>> GetQuestionIDAndContent(IEnumerable<string> questionBankIDs)
        {
            return await _nursingManagementDbContext.ExaminationQuestionInfos
                .Where(m => questionBankIDs.Any(n => n == m.QuestionBankID) && m.DeleteFlag != "*")
                .GroupBy(m => m.QuestionBankID)
                .ToDictionaryAsync(m => m.Key, m => m.Select(n => (n.ExaminationQuestionID, n.QuestionContent)).ToArray());
        }
        /// <summary>
        /// 根据题目ID集合获取记录
        /// </summary>
        /// <param name="examinationQuestionListIDs"></param>
        /// <returns></returns>
        public async Task<List<ExaminationQuestionInfo>> GetListByIDList(List<int> examinationQuestionListIDs)
        {
            return await _nursingManagementDbContext.ExaminationQuestionInfos.Where(m => examinationQuestionListIDs.Any(n => n == m.ExaminationQuestionID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据问题ID获取所属题库
        /// </summary>
        /// <param name="examinationQuestionIDs">考试问题列表的ID。</param>
        /// <returns></returns>
        public async Task<List<(int, string)>> GetQuestionBanksByQuestionIDs(List<int> examinationQuestionIDs)
        {
            return await _nursingManagementDbContext.ExaminationQuestionInfos.Where(m => examinationQuestionIDs.Contains(m.ExaminationQuestionID) && m.DeleteFlag != "*")
                .Select(m => ValueTuple.Create(m.ExaminationQuestionID, m.QuestionBankID))
                .ToListAsync();
        }
        /// <summary>
        /// 获取问题类型和问题ID集合
        /// </summary>
        /// <param name="questionBankIDs">题库主键ID集合</param>
        /// <returns></returns>
        public async Task<List<ExaminationQuestionInfo>> GetQuestionTypeAndIDByBankIDs(List<string> questionBankIDs)
        {
            return await _nursingManagementDbContext.ExaminationQuestionInfos.Where(m => questionBankIDs.Contains(m.QuestionBankID) && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取主键最大值
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetMaxID()
        {
            var maxData = await _nursingManagementDbContext.ExaminationQuestionInfos.OrderByDescending(m => m.ExaminationQuestionID).FirstOrDefaultAsync();
            if (maxData == null)
            {
                return 0;
            }
            return maxData.ExaminationQuestionID;
        }
    }
}
