﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    /// <summary>
    /// 排班相关业务服务层
    /// </summary>
    public interface IShiftSchedulingService
    {
        /// <summary>
        /// 获取智能排班相关参数
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<ShiftSchedulingParameter> GetShiftSchedulingParameter(int departmentID, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 保存排班
        /// </summary>
        /// <param name="shiftSchedulingView">保存的排班数据</param>
        /// <returns></returns>
        Task<bool> SaveShiftSchedulingData(ShiftSchedulingView shiftSchedulingView);

        /// <summary>
        /// 保存排班人员顺序
        /// </summary>
        /// <param name="shiftSchedulingEmployeeSortInfos"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<bool> UpdateSchedulingEmployeeSort(List<ShiftSchedulingEmployeeSortInfo> shiftSchedulingEmployeeSortInfos, string employeeID);

        /// <summary>
        ///  获取部门排班数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="schedulingType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="statusCode"></param>
        /// <returns></returns>
        Task<ShiftSchedulingView> GetShiftSchedulingData(int departmentID, string schedulingType, DateTime startDate, DateTime endDate, string statusCode);

        /// <summary>
        /// 获取个人排班信息
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<Dictionary<string, object>> GetShiftSchedulingByEmployeeID(int departmentID, DateTime startDate, DateTime endDate, string employeeID);
        /// <summary>
        /// 根据员工编号和部门、时间获取排班明细
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="datetime"></param>
        /// <param name="employeeID"></param>
        /// <param name="noonType">午别</param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetailInfo>> GetShiftSchedulingDetailsByEmployeeID(int departmentID, DateTime datetime, string employeeID, string noonType = null);
        /// <summary>
        /// 根据员工编号和部门、时间获取排班岗位
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="datetime"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<string> GetSchedulingPostByEmployeeID(int departmentID, DateTime datetime, string employeeID, string noonType);

        /// <summary>
        /// 根据排班主记录获取排班数据
        /// </summary>
        /// <param name="schedulingRecordID"></param>
        /// <returns></returns>
        Task<ShiftSchedulingRecordView> GetShiftSchedulingDataByRecordID(string schedulingRecordID);
        /// <summary>
        /// 复制上个月/周排班
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="schedulingType"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        Task<string> CopyScheduling(int departmentID, string schedulingType, DateTime startDate, DateTime endDate);
        /// <summary>
        /// 获取排班标记设定数据
        /// </summary>
        /// <param name="departmentID">病区主键</param>
        /// <returns></returns>
        Task<List<SchedulingMarkSettingsView>> GetSchedulingMarkSettings(int departmentID);
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="schedulingMarks">标记集合</param>
        /// <returns></returns>
        Task<bool> SaveSchedulingMarkSettings(SchedulingMarkSettingsView schedulingMarks);
        /// <summary>
        /// 删除排班标记
        /// </summary>
        /// <param name="administrationIconID">标记主键</param>
        /// <param name="userID">用户ID</param>
        /// <returns></returns>
        Task<bool> DeleteSchedulingMarkSettings(DeleteSchedulingMarkSettingsView deleteScheduling);
        /// <summary>
        /// 获取排班规则记录
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingRuleView>> GetShiftSchedulingRules(int departmentID);
        /// <summary>
        /// 保存排班规则
        /// </summary>
        /// <param name="shiftSchedulingRule"></param>
        /// <returns></returns>
        Task<bool> SaveShiftSchedulingRule(ShiftSchedulingRuleView shiftSchedulingRule);
    }
}
