﻿namespace NursingManagement.ViewModels
{
    public class HandleConditionView
    {
        /// <summary>
        /// 条件选择内容
        /// </summary>
        public List<FormDetailConditionView> Conditions { get; set; }
        /// <summary>
        /// 条件表达式
        /// </summary>
        public string ConditionExpression { get; set; }
        /// <summary>
        /// 条件内容
        /// </summary>
        public string ConditionContent { get; set; }
        /// <summary>
        /// 来源ID
        /// </summary>
        public string SourceID { get; set; }
        /// <summary>
        /// 来源类别
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 修改人员工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 新增标记
        /// </summary>
        public bool AddFlag { get; set; }
        /// <summary>
        /// 条件名称
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 数据类型 存储题目的题型ExaminationQuestionType
        /// </summary>
        public string DataType { get; set; }
        /// <summary>
        /// 数据类型值 具体的规则RuleListID（每一种题型对应的规则，数量、分数、困难程度）
        /// </summary>
        public string DataTypeValue { get; set; }
        /// <summary>
        /// 分组类型
        /// </summary>
        public string GroupType { get; set; }
        /// <summary>
        /// 分类下的明细值
        /// </summary>
        public string GroupTypeValue { get; set; }
    }
}
