﻿
namespace NursingManagement.ViewModels
{
    public class DynamicTableHeaderView
    {
        public DynamicTableHeaderView()
        {
            Children = new List<DynamicTableHeaderView>();
        }
        /// <summary>
        /// 表格列绑定值
        /// </summary>
        public string Prop { get; set; }
        /// <summary>
        /// 表格列宽
        /// </summary>
        public string TableColumnWidth { get; set; }
        /// <summary>
        /// 是否为最小宽度
        /// </summary>
        public bool? MinWidthFlag { get; set; }
        /// <summary>
        /// 表格固定位置
        /// </summary>
        public string FixedPosition { get; set; }
        /// <summary>
        /// 表格内容位置
        /// </summary>
        public string Align { get; set; }
        /// <summary>
        /// 表格内容位置
        /// </summary>
        public string HeaderAlign { get; set; }
        /// <summary>
        /// 插槽名称
        /// </summary>
        public string SlotName { get; set; }
        /// <summary>
        /// 列样式Class名
        /// </summary>
        public string ColumnClassName { get; set; }
        /// <summary>
        /// 列内容类型
        /// </summary>
        public string ColumnStyle { get; set; }
        /// <summary>
        /// 表头名称
        /// </summary>
        public string Label { get; set; }
        /// <summary>
        /// 子表头
        /// </summary>
        public List<DynamicTableHeaderView> Children { get; set; }
        /// <summary>
        /// 放置位置
        /// </summary>
        public PlacementLocationType PlacementLocation { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public short? Sort { get; set; }
        /// <summary>
        /// 是否支持排序功能
        /// </summary>
        public bool SortFlag { get; set; }
        /// <summary>
        /// 当前列是否进行合并
        /// </summary>
        public bool ColumnMergeFlag { get; set; }
        /// <summary>
        /// 占据列数
        /// </summary>
        public int? ColumnSpan { get; set; }
    }

    /// <summary>
    /// 动态表格中表格头和表格文本放在单元格中的位置
    /// </summary>
    public enum PlacementLocationType
    {
        /// <summary>
        /// 全部放到单个表格中，不进行拆分
        /// </summary>
        None = 0,
        /// <summary>
        /// 左侧
        /// </summary>
        Left = 1,
        /// <summary>
        /// 右侧
        /// </summary>
        Right = 2,
    }
}
