﻿using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 年度计划执行项目字典
    /// </summary>
    [Serializable]
    [Table("InterventionList")]
    public class InterventionListInfo : MutiModifyInfo
    {
        /// <summary>
        /// 非自增主键
        /// </summary>
        public int InterventionID { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string InterventionContent { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 医院
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }
    }
}
