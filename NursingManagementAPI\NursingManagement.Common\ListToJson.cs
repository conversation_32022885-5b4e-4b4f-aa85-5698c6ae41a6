﻿using Newtonsoft.Json;
using System.Diagnostics;

namespace NursingManagement.Common
{
    [DebuggerStepThrough]
    public static class ListToJson
    {
        public static string ToJson<T>(T data)
        {
            return JsonConvert.SerializeObject(data);
        }

        public static T ToList<T>(string dataString)
        {
            return JsonConvert.DeserializeObject<T>(dataString);
        }
    }
}
