using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 权限表
    /// </summary>
    [Serializable]
    [Table("AuthorityList")]
    public class AuthorityListInfo : MutiModifyInfo
    {

        /// <summary>
        /// 权限序号
        /// </summary>
        public int AuthorityID { get; set; }

        /// <summary>
        /// 功能菜单序号
        /// </summary>
        public int MenuListID { get; set; }

        /// <summary>
        /// 语言序号
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 功能名称
        /// </summary>
        [Column(TypeName = "nverchar(100)")]
        public string FunctionName { get; set; }

        /// <summary>
        /// 程序功能名称
        /// </summary>
        [Column(TypeName = "nverchar(100)")]
        public string Function { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }
}
