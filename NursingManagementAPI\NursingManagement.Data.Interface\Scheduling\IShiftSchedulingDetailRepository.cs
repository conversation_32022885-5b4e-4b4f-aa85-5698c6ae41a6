﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IShiftSchedulingDetailRepository
    {
        /// <summary>
        /// 获取排班明细记录
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetailInfo>> GetDetailByRecordID(string shiftSchedulingRecordID, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 根据人员编号获取排班明细数据
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <param name="startNoon">开始午别</param>
        /// <param name="endNoon">结束午别</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetailInfo>> GetDetailByEmployeeID(string employeeID, int departmentID, string startNoon, string endNoon, DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 获取排班明细记录
        /// </summary>
        /// <param name="shiftSchedulingRecordID"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetail>> GetDetailByRecordID(string shiftSchedulingRecordID);
        /// <summary>
        /// 获取人员除departmentID之外的排班明细
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="excludeDepartmentID"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingDetailInfo>> GetExcludeDepartmentDetailsByEmployeeIDs(List<string> employeeIDs, DateTime startDate, DateTime endDate, int excludeDepartmentID);
        /// <summary>
        /// 获取人员排班置顶岗位的天数
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="departmentPostID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<Dictionary<string, int>> GetEmployeePostDays(List<string> employeeIDs, int departmentPostID, DateTime startDate, DateTime endDate);
    }
}
