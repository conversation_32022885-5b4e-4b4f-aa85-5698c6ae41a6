﻿using NursingManagement.Models;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.ViewModels
{
    public class SubjectView
    {
        /// <summary>
        /// 质控主题主键
        /// </summary>
        public string HierarchicalQCSubjectID { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 主题类型
        /// </summary>
        public string FormType { get; set; }
        /// <summary>
        /// 主题类型Code
        /// </summary>
        public string FormTypeCode { get; set; }
        /// <summary>
        /// 获取QualityControlFormContent明细数据
        /// </summary>
        public string TemplateCode { get; set; }
        /// <summary>
        /// 质控表单序号
        /// </summary>
        public int HierarchicalQCFormID { get; set; }
        /// <summary>
        /// 表单名称
        /// </summary>
        public string FormName { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public string YearMonth { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        public string HierarchicalQCFormLevel { get; set; }
        /// <summary>
        /// 质控字典级别 1：一级质控 2：二级质控 3：三级质控
        /// </summary>
        public string HierarchicalQCFormName { get; set; }
        /// <summary>
        /// 开始时间（主题使用）
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 结束时间（主题使用）
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 质控报告文件
        /// </summary>
        public string ReportFileID { get; set; }
        /// <summary>
        /// 添加部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 添加部门
        /// </summary>
        public string AddDepartmentName { get; set; }
        /// <summary>
        /// 异动人
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 异动人ID
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 异动时间
        /// </summary>
        public DateTime ModifyDate { get; set; }
        /// <summary>
        /// 主题是否指派
        /// </summary>
        public bool AssignFlag { get; set; }
        /// <summary>
        /// 主题复制标记
        /// </summary>
        public bool CopyFlag { get; set; }
        /// <summary>
        /// 质控病区的数量
        /// </summary>
        public int qcDepartmentCount { get; set; }
        /// <summary>
        /// 开始实施时间
        /// </summary>
        public DateTime? ImplementationStartDate { get; set; }
        /// <summary>
        /// 报告名称
        /// </summary>
        public string ReportFileName { get; set; }
        /// <summary>
        /// 报告链接
        /// </summary>
        public string ReportFileUrl { get; set; }
        /// <summary>
        /// 文件类型
        /// </summary>
        public string ReportFileType { get; set; }
        /// <summary>
        /// 审核人集合
        /// </summary>
        public List<string> VerifierEmployeeIDs { get; set; }
        /// <summary>
        /// 达标分数
        /// </summary>
        public int? MinPassingScore { get; set; }
    }
}
