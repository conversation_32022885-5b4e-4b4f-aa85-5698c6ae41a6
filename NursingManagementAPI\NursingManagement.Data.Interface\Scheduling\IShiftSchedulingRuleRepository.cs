﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IShiftSchedulingRuleRepository
    {
        /// <summary>
        /// 获取排班规则
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<ShiftSchedulingRuleInfo>> GetShiftSchedulingRule(int departmentID);
        /// <summary>
        /// 根据主键获取排班规则记录
        /// </summary>
        /// <param name="shiftSchedulingRuleID"></param>
        /// <returns></returns>
        Task<ShiftSchedulingRuleInfo> GetShiftSchedulingRuleByID(string shiftSchedulingRuleID);
    }
}
