﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/Test")]
    [ApiController]
    [EnableCors("any")]
    public class ExternalController : Controller
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        /// <summary>
        /// 构造器注入
        /// </summary>
        /// <param name="appConfigSettingRepository"></param>
        public ExternalController(
            IAppConfigSettingRepository appConfigSettingRepository
        )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
        }


        /// <summary>
        /// 测试Post
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("TestHttpPost")]
        public async Task<IActionResult> TestHttpPost()
        {

            var result = new ResponseResult();

            result.Data = 0;
            return result.ToJson();
        }


        /// <summary>
        /// 测试Get
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("TestHttpGet")]
        public async Task<IActionResult> TestHttpGet()
        {
            var result = new ResponseResult();

            result.Data = 0;
            return result.ToJson();
        }
        /// <summary>
        /// 测试GetBySettingCode
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBySettingCode")]
        public async Task<IActionResult> GetBySettingCode(string settingType, string settingCode)
        {
            var result = new ResponseResult();

            result.Data = await _appConfigSettingRepository.GetConfigSettingValue(settingType, settingCode);
            return result.ToJson();
        }

    }
}