﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 员工多组织架构部门表
    /// </summary>
    [Serializable]
    [Table("EmployeeToDepartment")]
    public class EmployeeToDepartmentInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key]
        public int EmployeeToDepartmentID { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 组织架构类型
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string OrganizationType { get; set; }
        /// <summary>
        /// 是否是主职部门
        /// </summary>
        public bool? IsMainDepartment { get; set; }
        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
    }
}
