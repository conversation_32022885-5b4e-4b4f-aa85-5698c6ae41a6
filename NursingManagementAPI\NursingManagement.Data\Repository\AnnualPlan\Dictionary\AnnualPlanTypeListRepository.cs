﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划类别字典
    /// </summary>
    public class AnnualPlanTypeListRepository : IAnnualPlanTypeListRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public AnnualPlanTypeListRepository(
            NursingManagementDbContext nursingManagementDbContext,
            SessionCommonServer sessionCommonServer,
            IRedisService redisService)
        {
            _nursingManagementDbContext = nursingManagementDbContext;
            _sessionCommonServer = sessionCommonServer;
            _redisService = redisService;
        }
        
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.AnnualPlanTypeListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public string GetCacheType()
        {
            return CacheType.AnnualPlanTypeList.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        /// <summary>
        /// 获取部门制定的分类
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanTypeListView>> GetAnnualPlanTypeListView(int departmentID)
        {
            var datas = (await GetCacheAsync()) as List<AnnualPlanTypeListInfo>;
            return datas.Where(m => m.DepartmentID == departmentID).Select(m => new AnnualPlanTypeListView
            {
                AnnualPlanTypeID = m.AnnualPlanTypeID,
                AnnualPlanTypeContent = m.AnnualPlanTypeContent,
                DepartmentID = m.DepartmentID,
                AddEmployeeID = m.AddEmployeeID,
                AddDateTime = m.AddDateTime,
                ModifyEmployeeID = m.ModifyEmployeeID,
                ModifyDateTime = m.ModifyDateTime
            }).ToList();
        }
        /// <summary>
        /// 获取分类
        /// </summary>
        /// <param name="typeID">主键ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanTypeListInfo> GetAnnualPlanTypeByID(int typeID)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            return await _nursingManagementDbContext.AnnualPlanTypeListInfos.FirstOrDefaultAsync(m => m.HospitalID == hospitalID && m.Language == language && m.DeleteFlag != "*" && m.AnnualPlanTypeID == typeID);
        }
        /// <summary>
        /// 获取最大主键ID
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetAnnualPlanTypeMaxID()
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var query = await _nursingManagementDbContext.AnnualPlanTypeListInfos.Where(m => m.HospitalID == hospitalID && m.Language == language).Select(m => m.AnnualPlanTypeID).ToListAsync();
            if (query.Count<=0)
            {
                return 1;
            }
            return query.Max() + 1;
        }

    }
}
