﻿using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Services.Interface
{
    public interface ITrainingLearnerService
    {
        /// <summary>
        /// 获取学员列表
        /// </summary>
        /// <param name="trainingRecordID">查询参数</param>
        /// <param name="session">缓存</param>
        /// <returns>学员列表</returns>
        Task<List<TrainingLearnerView>> GetTraineeList(string trainingRecordID, Session session);

        /// <summary>
        /// 更新学员记录
        /// </summary>
        /// <param name="trainingLearnerInfo">学员信息</param>
        /// <param name="employeeID">操作员工ID</param>
        /// <returns>更新是否成功</returns>
        Task<bool> UpdateTraineeRecordAsync(TrainingLearnerInfo trainingLearnerInfo, string employeeID);

        /// <summary>
        /// 删除学员记录
        /// </summary>
        /// <param name="trainingLearnerID">学员记录ID</param>
        /// <param name="employeeID">操作员工ID</param>
        /// <returns>删除是否成功</returns>
        Task<bool> DeleteTraineeAsync(string trainingLearnerID, string employeeID);

        /// <summary>
        /// 设置学员为班长
        /// </summary>
        /// <param name="setMonitorParamsView">设置班长参数</param>
        /// <param name="employeeID">操作员工ID</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetTraineeMonitorAsync(TrainingLearnerParamView setMonitorParamsView, string employeeID);

        /// <summary>
        /// 获取培训时间线
        /// </summary>
        /// <param name="timelineSearchParamsView">时间线搜索参数</param>
        /// <returns>培训时间线数据</returns>
        Task<List<TimelineItemView>> GetTrainTimelineAsync(TrainingLearnerParamView timelineSearchParamsView);
        /// <summary>
        /// 保存课程评价及课程建议
        /// </summary>
        /// <param name="recommendationsView">培训记录建议及满意度信息</param>
        /// <returns></returns>
        Task<bool> SaveCourseRecommendations(TrainingLearnerRecommendationsParamsView recommendationsView, string employeeID);
        /// <summary>
        /// 培训扫码签到
        /// </summary>
        /// <param name="trainingRecordID">培训主表记录</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <param name="employeeID">员工ID</param>
        /// <returns></returns>
        Task<(bool, string)> TrainingLearnerSignIn(string trainingRecordID, long timeStamp, string employeeID);
    }
}