﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IAnnualPlanInterventionDetailRepository
    {
        /// <summary>
        /// 获取年度计划-计划制定明细列表
        /// </summary>
        /// <param name="interventionMainIDs">执行项目主表ID集合</param>
        /// <returns></returns>
        Task<APInterventionMonthsView[]> GetDetailsByMainID(List<string> interventionMainIDs);
        /// <summary>
        /// 获取年度计划-计划制定明细列表
        /// </summary>
        /// <param name="planMainID">计划主表ID</param>
        /// <returns></returns>
        Task<APInterventionMonthsView[]> GetDetailsByPlanMainID(string planMainID);
        /// <summary>
        /// 获取年度计划-计划制定明细列表
        /// </summary>
        /// <param name="interventionMainIDs">计划制定主表ID</param>
        /// <returns></returns>
        Task<List<AnnualInterventionDetailInfo>> GetDetailsByInterventionMainIDs(params string[] interventionMainIDs);
        /// <summary>
        /// 依据年度计划ID获取部分字段
        /// </summary>
        /// <param name="annualPlanMainID">年度计划ID</param>
        /// <returns></returns>
        Task<List<AnnualInterventionDetailInfo>> GetPartByAnnualPlanMainID(string annualPlanMainID);
        /// <summary>
        /// 获取不跟踪的执行项目明细集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualInterventionDetailInfo>> GetInfosByPlanMainIDAsNoTracking(string annualPlanMainID);
    }
}
