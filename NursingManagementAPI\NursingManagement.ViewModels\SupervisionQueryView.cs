﻿namespace NursingManagement.ViewModels
{
    public class SupervisionQueryView
    {
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 病区集合
        /// </summary>
        public List<int> StationIDs { get; set; }

        /// <summary>
        /// 患者状况类型
        /// </summary>
        public string SupervisionType { get; set; }

        /// <summary>
        /// 是否已督导
        /// </summary>
        public bool SupervisionFlag { get; set; }

        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }
    }
}
