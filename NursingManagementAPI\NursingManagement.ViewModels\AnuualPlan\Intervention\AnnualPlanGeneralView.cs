﻿namespace NursingManagement.ViewModels
{
    public class AnnualPlanGeneralView
    {
        /// <summary>
        /// 所属目标
        /// </summary>
        public string MainGoalContent { get; set; }
        /// <summary>
        /// 工作项目
        /// </summary>
        public string LocalShowName { get; set; }
        /// <summary>
        /// 计划月份
        /// </summary>
        public int[] PlanMonths { get; set; }
        /// <summary>
        /// 负责人分组名称
        /// </summary>
        public string PrincipalName { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        public string AnnualPlanMainID { get; set; }
        /// <summary>
        /// 目标ID
        /// </summary>
        public string AnnualPlanMainGoalID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }
        /// <summary>
        /// 目标字典ID
        /// </summary>
        public int GoalID { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 执行项目字典ID
        /// </summary>
        public int InterventionID { get; set; }
    }
}
