﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class MonthlyPlanMaintainService : IMonthlyPlanMaintainService
    {
        private readonly IAnnualPlanInterventionService _annualPlanInterventionService;
        private readonly IMonthlyPlanMaintainRepository _monthlyPlanMaintainRepository;
        private readonly IQuarterPlanMaintainRepository _quarterPlanMaintainRepository;
        private readonly IAnnualPlanMainRepository _annualPlanMainRepository;
        private readonly IAnnualPlanTypeListRepository _typeListRepository;
        private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;
        private readonly IDictionaryService _dictionaryService;

        public MonthlyPlanMaintainService(
            IAnnualPlanInterventionService annualPlanInterventionService,
            IMonthlyPlanMaintainRepository monthlyPlanMaintainRepository,
            IQuarterPlanMaintainRepository quarterPlanMaintainRepository,
            IAnnualPlanMainRepository annualPlanMainRepository,
            IAnnualPlanTypeListRepository typeListRepository,
            IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
            IDepartmentListRepository departmentListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IUnitOfWork unitOfWork,
            IEmployeeToDepartmentRepository employeeToDepartmentRepository,
            IDictionaryService dictionaryService
            )
        {
            _annualPlanInterventionService = annualPlanInterventionService;
            _monthlyPlanMaintainRepository = monthlyPlanMaintainRepository;
            _quarterPlanMaintainRepository = quarterPlanMaintainRepository;
            _annualPlanMainRepository = annualPlanMainRepository;
            _typeListRepository = typeListRepository;
            _annualPlanInterventionMainRepository = annualPlanInterventionMainRepository;
            _departmentListRepository = departmentListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _unitOfWork = unitOfWork;
            _employeeToDepartmentRepository = employeeToDepartmentRepository;
            _dictionaryService = dictionaryService;
        }

        
        #region 常量
        /// <summary>
        /// 护理部ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;
        /// <summary>
        /// 护理委员会
        /// </summary>
        private const int DEPARTMENT_ID_53 = 53;
        private readonly Dictionary<int, int> MonthToQuarter = new Dictionary<int, int>
        {
            { 1, 1 }, { 2, 1 }, { 3, 1 },
            { 4, 2 }, { 5, 2 }, { 6, 2 },
            { 7, 3 }, { 8, 3 }, { 9, 3 },
            { 10, 4 }, { 11, 4 }, { 12, 4 }
        };
        #endregion

        #region 月度计划表增删改查
        /// <summary>
        /// 保存月度计划工作内容
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        public async Task<bool> SaveMonthlyWorks(MpWorksSaveView view)
        {
            var lookup = view.WorkViews.ToLookup(m => string.IsNullOrEmpty(m.Key));
            var newWorks = lookup[true];
            await AddNewWorks(view.MonthlyPlanMainID, newWorks, view.EmployeeID);

            var modifyWorks = lookup[false];
            await UpdateWorks(modifyWorks, view.EmployeeID);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #region 保存方法私有方法
        /// <summary>
        /// 新增月度计划工作
        /// </summary>
        /// <param name="MpMainID">月度计划主表ID</param>
        /// <param name="workViews">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task AddNewWorks(string MpMainID, IEnumerable<TieredPlanWork> workViews, string employeeID)
        {
            var newEntities = workViews.Select(m => new MonthlyPlanDetailInfo
            {
                MonthlyPlanDetailID = "".NewGuid(),
                MonthlyPlanMainID = MpMainID,
                TypeID = m.TypeID,
                APInterventionID = m.APInterventionID,
                Sort = m.Sort,
                WorkContent = m.WorkContent,
                Requirement = m.Requirement,
                WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(m.WorkType.ToString()),
                IsTemp = m.IsTemp,
                PrincipalIDs = m.PrincipalIDs ?? [],
                PrincipalGroupName = m.PrincipalGroupName
            }).Select(m => m.Add(employeeID).Modify(employeeID) as MonthlyPlanDetailInfo).ToList();
            await _unitOfWork.GetRepository<MonthlyPlanDetailInfo>().InsertAsync(newEntities);
        }

        /// <summary>
        /// 更新月度计划工作
        /// </summary>
        /// <param name="workViews">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task UpdateWorks(IEnumerable<TieredPlanWork> workViews, string employeeID)
        {
            
            var workIDs = workViews.Select(m => m.Key).ToArray();
            var workInfos = await _monthlyPlanMaintainRepository.GetMonthlyPlanWorks(workIDs);
            foreach (var workInfo in workInfos)
            {
                var modifyView = workViews.FirstOrDefault(m => m.Key == workInfo.MonthlyPlanDetailID);
                if (modifyView is null)
                {
                    continue;
                }
                workInfo.WorkContent = modifyView.WorkContent;
                workInfo.Requirement = modifyView.Requirement;
                workInfo.WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(modifyView.WorkType.ToString());
                workInfo.PrincipalIDs = modifyView.PrincipalIDs;
                workInfo.PrincipalGroupName = modifyView.PrincipalGroupName;
                workInfo.Sort = modifyView.Sort;
                workInfo.Modify(employeeID);
            }
        }
        #endregion

        /// <summary>
        /// 删除月度计划工作
        /// </summary>
        /// <param name="monthlyPlanDetailInfo">主键</param>
        /// <returns></returns>
        public async Task<bool> DeleteMonthlyWork(string monthlyPlanDetailInfo, string employeeID)
        {
            if (string.IsNullOrEmpty(monthlyPlanDetailInfo))
            {
                return false;
            }
            var monthlyWorkInfo = await _monthlyPlanMaintainRepository.GetMonthlyWork(monthlyPlanDetailInfo);
            if (monthlyWorkInfo == null)
            {
                return false;
            }
            monthlyWorkInfo.Delete(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 更新月度计划工作
        /// </summary>
        /// <param name="workView">工作集合</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> UpdateMonthlyWork(TieredPlanWork workView, string employeeID)
        {
            var workInfos = await _monthlyPlanMaintainRepository.GetMonthlyPlanWorks([workView.Key]);
            if (workInfos.FirstOrDefault() is MonthlyPlanDetailInfo workInfo && workInfo != null)
            {
                workInfo.WorkContent = workView.WorkContent;
                workInfo.Requirement = workView.Requirement;
                workInfo.WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(workView.WorkType.ToString());
                workInfo.PrincipalIDs = workView.PrincipalIDs;
                workInfo.PrincipalGroupName = workView.PrincipalGroupName;
                workInfo.Sort = workView.Sort;
                workInfo.Modify(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 发布月度计划
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        public async Task<bool> PublishMonthlyPlan(string monthlyPlanMainID, string employeeID)
        {
            var monthlyPlanMainInfo = await _monthlyPlanMaintainRepository.GetMonthlyPlanMain(monthlyPlanMainID);
            monthlyPlanMainInfo.StatusCode = 1;
            monthlyPlanMainInfo.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取月度计划主表ID
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="month">月份</param>
        /// <returns></returns>
        public async Task<string> GetMonthlyPlanMainID(string annualPlanMainID, int month)
        {
            return await _monthlyPlanMaintainRepository.GetMonthlyPlanMainID(annualPlanMainID, month);
        }

        /// <summary>
        /// 获取月度计划状态
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主键</param>
        /// <returns></returns>
        public async Task<bool> GetMonthlyPlanStatus(string monthlyPlanMainID)
        {
            var statusCode = await _monthlyPlanMaintainRepository.GetMonthlyPlanStatus(monthlyPlanMainID);
            return statusCode == 1;
        }

        /// <summary>
        /// 查询某科室的月度计划
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <returns></returns>
        public async Task<TieredPlanWorksByType[]> GetMonthlyWorks(string annualPlanMainID, string monthlyPlanMainID)
        {
            // 获取当前月度工作
            var monthlyWorks = await _monthlyPlanMaintainRepository.GetMonthlyWorks(monthlyPlanMainID);
            
            if (monthlyWorks.Length == 0)
            {
                return [];
            }
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var interventionIDs = monthlyWorks.SelectMany(m => m.Children).Where(m => !m.IsTemp && m.APInterventionID.HasValue).Select(m => m.APInterventionID.Value).ToArray();
            var interventionIDAndLocalShowName = await _annualPlanInterventionMainRepository.GetInterventionIDAndLocalShowName(interventionIDs, annualPlanMainID);
            foreach (var monthlyWork in monthlyWorks)
            {
                // 补充分类名称
                monthlyWork.TypeName = typeList.Find(m => m.AnnualPlanTypeID == monthlyWork.TypeID)?.AnnualPlanTypeContent;
                foreach (var work in monthlyWork.Children)
                {
                    // 补充所引用执行项目的自定义名称
                    if (!work.IsTemp && work.APInterventionID.HasValue)
                    {
                        interventionIDAndLocalShowName.TryGetValue(work.APInterventionID.Value, out var localShowName);
                        work.APInterventionLocalShowName = localShowName;
                    }
                    // 若没有自定义分组名称，则拼接人员姓名作前端呈现
                    if (string.IsNullOrEmpty(work.PrincipalGroupName) && work.PrincipalIDs.Length > 0)
                    {
                        var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(work.PrincipalIDs);
                        work.PrincipalName = string.Join("，", employees.Select(m => m.Value));
                    }
                }
            }
            return monthlyWorks;
        }
        #endregion

        #region 参考导入逻辑
        /// <summary>
        /// 获取可导入的工作
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <param name="departmentID">当前部门</param>
        /// <param name="annual">年度</param>
        /// <param name="month">月度</param>
        /// <returns></returns>
        public async Task<List<TieredPlanWorksByPlanThenType>> GetCanImportMpWorksGroupByPlanThenType(string annualPlanMainID, string monthlyPlanMainID, int departmentID, int annual, int month)
        {
            // 当前部门月度计划工作已参考过的执行项目ID，重复进行导入时，需要根据此集合排除掉已参考的工作
            var refInterventionIDs = await _monthlyPlanMaintainRepository.GetMpWorkInterventionIDs(monthlyPlanMainID, month);
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            // 来源一：本部门季度计划，仅首次有，因首次导入后所有季度计划执行项目数据均已转换为月度计划工作
            var currentDeptMpWorksByPlan = refInterventionIDs.Length == 0
                ? await GetCurrentDeptMpWorksByPlanThenType(annualPlanMainID, departmentID, month, typeList)
                : null;

            // 来源二：上级部门月度计划
            var upperDeptMpWorksByPlanThenTypeList = await GetUpperDeptMpWorksByPlanThenTypeList(annual, departmentID, null, refInterventionIDs, typeList);

            if (currentDeptMpWorksByPlan is null)
            {
                return upperDeptMpWorksByPlanThenTypeList;
            }
            return [.. upperDeptMpWorksByPlanThenTypeList, currentDeptMpWorksByPlan];
        }

        /// <summary>
        /// 获取当前部门季度计划可选工作，非首次时 children为空集合
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="month">月度</param>
        /// <param name="typeList">分类字典</param>
        /// <returns></returns>
        private async Task<TieredPlanWorksByPlanThenType> GetCurrentDeptMpWorksByPlanThenType(string annualPlanMainID, int departmentID, int month, List<AnnualPlanTypeListInfo> typeList)
        {
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var quarter = MonthToQuarter[month];
            var currentDeptWorksGroup = await _quarterPlanMaintainRepository.GetQuarterPlanWorksForMonthlyPlan(annualPlanMainID, quarter);
            currentDeptWorksGroup.PlanName = $"{departmentList.Find(m => m.DepartmentID == departmentID)?.LocalShowName}季度计划";
            currentDeptWorksGroup.DepartmentID = departmentID;
            foreach (var typeGroup in currentDeptWorksGroup.Children)
            {
                typeGroup.TypeName = typeList.Find(m => m.AnnualPlanTypeID == typeGroup.TypeID)?.AnnualPlanTypeContent;
            }
            return currentDeptWorksGroup;
        }

        /// <summary>
        /// 获取上级部门月度计划可选工作
        /// </summary>
        /// <param name="annual">年度</param>
        /// <param name="departmentID">部门</param>
        /// <param name="includeApInterventionID">需包含的执行项目字典ID</param>
        /// <param name="excludeApInterventionIDs">需排除的执行项目字典ID集合</param>
        /// <param name="typeList">分类字典</param>
        /// <returns></returns>
        /// <exception cref="Exception">未找到上级部门月度计划所属的年度计划</exception>
        public async Task<List<TieredPlanWorksByPlanThenType>> GetUpperDeptMpWorksByPlanThenTypeList(int annual, int departmentID, int? includeApInterventionID, int[] excludeApInterventionIDs = null, List<AnnualPlanTypeListInfo> typeList = null)
        {
            typeList ??= await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var nursingDepartmentName = (await _departmentListRepository.GetByIDAsync(DEPARTMENT_ID_405))?.LocalShowName;
            var committeeDepartments = await _departmentListRepository.GetLowerDepartments(DEPARTMENT_ID_53);
            var upperDeptIDs = committeeDepartments.Select(m => m.DepartmentID)
                .Append(DEPARTMENT_ID_405)
                // 排除当前部门
                .Where(m => m != departmentID);
            var planMainViews = await _annualPlanMainRepository.GetPlanMainViewsByYearAndDepartmentIDs(annual, upperDeptIDs);
            var planMainIDs = planMainViews.Select(m => m.MainID).ToArray();
            // 若指定了执行项目ID，说明是快捷参考，按业务只包含对应字典的上级计划工作，故查询数据时不再包含临时性工作
            var upperDeptQpWorksByPlanThenTypeList = await _monthlyPlanMaintainRepository.GetWorkViews(planMainIDs, includeApInterventionID, excludeApInterventionIDs, true, !includeApInterventionID.HasValue);
            upperDeptQpWorksByPlanThenTypeList.ForEach(planGroup =>
            {
                if (planMainViews.FirstOrDefault(m => m.MainID == planGroup.PlanMainID)?.DepartmentID is not int currentPlanDeptID)
                {
                    throw new Exception($"未找到当前月度计划所属年度计划！planMainID = {planGroup.PlanMainID}");
                }
                planGroup.IsCommittee = currentPlanDeptID != DEPARTMENT_ID_405;
                planGroup.DepartmentID = currentPlanDeptID;
                planGroup.PlanName = currentPlanDeptID == DEPARTMENT_ID_405
                ? $"{nursingDepartmentName}月度计划"
                : committeeDepartments.Find(m => m.DepartmentID == currentPlanDeptID)?.LocalShowName + "月度计划";
                foreach (var typeGroup in planGroup.Children)
                {
                    typeGroup.TypeName = typeList.Find(m => m.AnnualPlanTypeID == typeGroup.TypeID)?.AnnualPlanTypeContent;
                }
            });
            return upperDeptQpWorksByPlanThenTypeList;
        }

        /// <summary>
        /// 批量导入保存
        /// </summary>
        /// <param name="view">保存参数</param>
        /// <returns></returns>
        public async Task<bool> SaveImportWorks(MpWorksSaveView view)
        {
            // 规则一：数据来源是导入弹窗，而导入弹窗中的数据是过滤后的，不会和月度计划现有数据重叠。所以前端传递的始终认为是新数据
            // 规则二：本部门年度计划执行项目中有但未传的，若此时是首次（即月度计划表中无数据）导入，则新增为常规工作。
            if (view.WorkViews.Length == 0)
            {
                return false;
            }
            var mainID = view.IsFirstImport ? "".NewGuid() : view.MonthlyPlanMainID;
            var priorityWorks = view.WorkViews.Select(workView => new MonthlyPlanDetailInfo
            {
                MonthlyPlanDetailID = "".NewGuid(),
                MonthlyPlanMainID = mainID,
                TypeID = workView.TypeID,
                APInterventionID = workView.APInterventionID,
                Sort = workView.Sort,
                WorkType = AnnualPlanEnums.WorkType.Key,
                WorkContent = workView.WorkContent,
                Requirement = workView.Requirement,
                IsTemp = workView.IsTemp,
                PrincipalIDs = []
            }).Select(m => m.Add(view.EmployeeID).Modify(view.EmployeeID) as MonthlyPlanDetailInfo).ToList();
            if (view.IsFirstImport)
            {
                var quarterMainInfo = CreateMainInfo(view, mainID);
                await _unitOfWork.GetRepository<MonthlyPlanMainInfo>().InsertAsync(quarterMainInfo);
                var normalWorks = await CreateNormalWorks(view, mainID);
                await _unitOfWork.GetRepository<MonthlyPlanDetailInfo>().InsertAsync([.. priorityWorks, .. normalWorks]);
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            await _unitOfWork.GetRepository<MonthlyPlanDetailInfo>().InsertAsync(priorityWorks);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 创建月度计划主表
        /// </summary>
        /// <param name="view">导入参数</param>
        /// <returns></returns>
        private static MonthlyPlanMainInfo CreateMainInfo(MpWorksSaveView view, string mainID)
        {
            var quarterMainInfo = new MonthlyPlanMainInfo
            {
                MonthlyPlanMainID = mainID,
                AnnualPlanMainID = view.AnnualPlanMainID,
                Month = view.Month,
                StatusCode = 0,
                DeleteFlag = ""
            };
            quarterMainInfo.Add(view.EmployeeID).Modify(view.EmployeeID);
            return quarterMainInfo;
        }

        /// <summary>
        /// 创建常规工作
        /// </summary>
        /// <param name="view">导入参数</param>
        /// <returns></returns>
        private async Task<List<MonthlyPlanDetailInfo>> CreateNormalWorks(MpWorksSaveView view, string mainID)
        {
            // 未经前端传递过来的本部门年度计划执行项目，视为常规工作
            // 查询年度计划执行项目表
            var convertViews = await _annualPlanInterventionService.GetAPInterventionConvertViews(view.AnnualPlanMainID, [view.Month]);
            var filteredAPInterventions = convertViews.ExceptBy(view.WorkViews.Select(m => m.APInterventionID), m => m.InterventionID);
            var normalWorks = filteredAPInterventions.Select(m => new MonthlyPlanDetailInfo
            {
                MonthlyPlanDetailID = "".NewGuid(),
                MonthlyPlanMainID = mainID,
                TypeID = m.TypeID,
                APInterventionID = m.InterventionID,
                Sort = null,
                WorkType = AnnualPlanEnums.WorkType.Routine,
                WorkContent = m.LocalShowName,
                Requirement = "",
                PrincipalIDs = [],
                PrincipalGroupName = ""
            }).Select(m => m.Add(view.EmployeeID).Modify(view.EmployeeID) as MonthlyPlanDetailInfo).ToList();
            return normalWorks;
        }
        #endregion

        /// <summary>
        /// 查询本人及上下级已制定的月度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<List<APMainView>> GetBrowseMPViews(int year, string employeeID)
        {
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var mainDepartment = employeeToDepartments.FirstOrDefault(m => m.IsMainDepartment ?? false)?.DepartmentID;
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var departIDs = await GetDepartmentIDs(employeeToDepartments, departmentList);

            var plans = await _monthlyPlanMaintainRepository.GetMonthlyPlanMainViewsByYearAndDepartmentIDs(year, departIDs);
            foreach (var monthlyPlanMain in plans)
            {
                monthlyPlanMain.IsMainDepartment = monthlyPlanMain.DepartmentID == mainDepartment;
                var departmentName = departmentList.Find(m => m.DepartmentID == monthlyPlanMain.DepartmentID)?.LocalShowName;
                monthlyPlanMain.PlanName = $"{monthlyPlanMain.Year}年{departmentName}第{monthlyPlanMain.Month}月度计划";
                monthlyPlanMain.PlannerName = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(monthlyPlanMain.Planner, m => m.EmployeeName);
            }
            return [.. plans.OrderBy(m => departmentList.Find(n => n.DepartmentID == m.DepartmentID)?.Level)];
        }
        /// <summary>
        /// 获取科室集合
        /// </summary>
        /// <param name="employeeToDepartments">当前用户的权限科室</param>
        /// <param name="departmentList">科室字典</param>
        /// <returns></returns>
        private async Task<IEnumerable<int>> GetDepartmentIDs(EmployeeToDepartmentInfo[] employeeToDepartments, List<DepartmentListInfo> departmentList)
        {
            if (employeeToDepartments.Any(m => (m.IsMainDepartment ?? false) && m.DepartmentID == DEPARTMENT_ID_405))
            {
                return departmentList.Where(m => m.OrganizationType == "1").Select(m => m.DepartmentID);
            }

            var departIDs = new HashSet<int>() { DEPARTMENT_ID_405 };
            foreach (var empToDepart in employeeToDepartments)
            {
                // 获取此部门的连续上下级部门ID集合
                var departments = await _dictionaryService.GetSuperAndSubDepartmentsByID(empToDepart.DepartmentID, false);
                departments.ForEach(m => departIDs.Add(m.DepartmentID));
            }
            return departIDs;
        }
    }
}
