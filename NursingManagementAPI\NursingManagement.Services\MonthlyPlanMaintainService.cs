﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.MonthlyPlan;

namespace NursingManagement.Services;

public class MonthlyPlanMaintainService : IMonthlyPlanMaintainService
{
    private readonly IAnnualPlanInterventionService _annualPlanInterventionService;
    private readonly IMonthlyPlanMaintainRepository _monthlyPlanMaintainRepository;
    private readonly IQuarterPlanMaintainRepository _quarterPlanMaintainRepository;
    private readonly IAnnualPlanMainRepository _annualPlanMainRepository;
    private readonly IAnnualPlanTypeListRepository _typeListRepository;
    private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
    private readonly IDepartmentListRepository _departmentListRepository;
    private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;
    private readonly IDictionaryService _dictionaryService;

    public MonthlyPlanMaintainService(
        IAnnualPlanInterventionService annualPlanInterventionService,
        IMonthlyPlanMaintainRepository monthlyPlanMaintainRepository,
        IQuarterPlanMaintainRepository quarterPlanMaintainRepository,
        IAnnualPlanMainRepository annualPlanMainRepository,
        IAnnualPlanTypeListRepository typeListRepository,
        IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
        IDepartmentListRepository departmentListRepository,
        IEmployeePersonalDataRepository employeePersonalDataRepository,
        IUnitOfWork unitOfWork,
        IEmployeeToDepartmentRepository employeeToDepartmentRepository,
        IDictionaryService dictionaryService
        )
    {
        _annualPlanInterventionService = annualPlanInterventionService;
        _monthlyPlanMaintainRepository = monthlyPlanMaintainRepository;
        _quarterPlanMaintainRepository = quarterPlanMaintainRepository;
        _annualPlanMainRepository = annualPlanMainRepository;
        _typeListRepository = typeListRepository;
        _annualPlanInterventionMainRepository = annualPlanInterventionMainRepository;
        _departmentListRepository = departmentListRepository;
        _employeePersonalDataRepository = employeePersonalDataRepository;
        _unitOfWork = unitOfWork;
        _employeeToDepartmentRepository = employeeToDepartmentRepository;
        _dictionaryService = dictionaryService;
    }


    #region 常量
    /// <summary>
    /// 护理部ID
    /// </summary>
    private const int DEPARTMENT_ID_405 = 405;
    /// <summary>
    /// 护理委员会
    /// </summary>
    private const int DEPARTMENT_ID_53 = 53;
    #endregion

    #region 月度计划表增删改查
    /// <summary>
    /// 保存月度计划工作内容
    /// </summary>
    /// <param name="view">保存参数</param>
    /// <param name="employeeID">工号</param>
    /// <returns></returns>
    public async Task<bool> SaveMonthlyWorks(SaveMonthlyWorksView view, string employeeID)
    {
        var lookup = view.WorkViews.ToLookup(m => string.IsNullOrEmpty(m.Key));
        var newWorks = lookup[true];
        await AddNewWorks(view.MonthlyPlanMainID, newWorks, employeeID);

        var modifyWorks = lookup[false];
        await UpdateWorks(modifyWorks, employeeID);

        return await _unitOfWork.SaveChangesAsync() > 0;
    }

    #region 保存方法私有方法
    /// <summary>
    /// 新增月度计划工作
    /// </summary>
    /// <param name="MpMainID">月度计划主表ID</param>
    /// <param name="workViews">工作集合</param>
    /// <param name="employeeID">工号</param>
    /// <returns></returns>
    private async Task AddNewWorks(string MpMainID, IEnumerable<SaveMonthlyWorksView.MonthlyWork> workViews, string employeeID)
    {
        var newEntities = workViews.Select(m => new MonthlyPlanDetailInfo
        {
            MonthlyPlanDetailID = "".NewGuid(),
            MonthlyPlanMainID = MpMainID,
            TypeID = m.TypeID,
            APInterventionID = m.ApInterventionID,
            Sort = m.Sort,
            WorkContent = m.WorkContent,
            Requirement = m.Requirement,
            WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(m.WorkType.ToString()),
            IsTemp = m.IsTemp,
            PrincipalIDs = m.PrincipalIDs ?? [],
            PrincipalGroupName = m.PrincipalGroupName
        }).Select(m => m.Add(employeeID).Modify(employeeID) as MonthlyPlanDetailInfo).ToList();
        await _unitOfWork.GetRepository<MonthlyPlanDetailInfo>().InsertAsync(newEntities);
    }

    /// <summary>
    /// 更新月度计划工作
    /// </summary>
    /// <param name="workViews">工作集合</param>
    /// <param name="employeeID">工号</param>
    /// <returns></returns>
    private async Task UpdateWorks(IEnumerable<SaveMonthlyWorksView.MonthlyWork> workViews, string employeeID)
    {

        var workIDs = workViews.Select(m => m.Key).ToArray();
        var workInfos = await _monthlyPlanMaintainRepository.GetMonthlyPlanWorks(workIDs);
        foreach (var workInfo in workInfos)
        {
            var modifyView = workViews.FirstOrDefault(m => m.Key == workInfo.MonthlyPlanDetailID);
            if (modifyView is null)
            {
                continue;
            }
            workInfo.WorkContent = modifyView.WorkContent;
            workInfo.Requirement = modifyView.Requirement;
            workInfo.WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(modifyView.WorkType.ToString());
            workInfo.PrincipalIDs = modifyView.PrincipalIDs;
            workInfo.PrincipalGroupName = modifyView.PrincipalGroupName;
            workInfo.Sort = modifyView.Sort;
            workInfo.Modify(employeeID);
        }
    }
    #endregion

    /// <summary>
    /// 删除月度计划工作
    /// </summary>
    /// <param name="monthlyPlanDetailInfo">主键</param>
    /// <returns></returns>
    public async Task<bool> DeleteMonthlyWork(string monthlyPlanDetailInfo, string employeeID)
    {
        if (string.IsNullOrEmpty(monthlyPlanDetailInfo))
        {
            return false;
        }
        var monthlyWorkInfo = await _monthlyPlanMaintainRepository.GetMonthlyWork(monthlyPlanDetailInfo);
        if (monthlyWorkInfo == null)
        {
            return false;
        }
        monthlyWorkInfo.Delete(employeeID);
        var works = await _monthlyPlanMaintainRepository.GetGtSortWorks(monthlyWorkInfo.MonthlyPlanMainID, monthlyWorkInfo.TypeID, monthlyWorkInfo.Sort.Value);
        works.ForEach(work => (work.Modify(employeeID) as QuarterPlanDetailInfo).Sort -= 1);
        return await _unitOfWork.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 更新月度计划工作
    /// </summary>
    /// <param name="workView">工作集合</param>
    /// <param name="employeeID">工号</param>
    /// <returns></returns>
    public async Task<bool> UpdateMonthlyWork(TieredPlanWork workView, string employeeID)
    {
        var workInfos = await _monthlyPlanMaintainRepository.GetMonthlyPlanWorks([workView.Key]);
        var oldWork = workInfos.FirstOrDefault();
        if (oldWork != null)
        {
            oldWork.WorkContent = workView.WorkContent;
            oldWork.Requirement = workView.Requirement;
            oldWork.WorkType = Enum.Parse<AnnualPlanEnums.WorkType>(workView.WorkType.ToString());
            oldWork.PrincipalIDs = workView.PrincipalIDs;
            oldWork.PrincipalGroupName = workView.PrincipalGroupName;
            if (oldWork.Sort.HasValue && !workView.Sort.HasValue)
            {
                var works = await _monthlyPlanMaintainRepository.GetGtSortWorks(oldWork.MonthlyPlanMainID, oldWork.TypeID, oldWork.Sort.Value);
                works.ForEach(work => (work.Modify(employeeID) as MonthlyPlanDetailInfo).Sort -= 1);
            }
            oldWork.Sort = workView.Sort;
            oldWork.Modify(employeeID);
        }
        return await _unitOfWork.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 发布月度计划
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    public async Task<bool> PublishMonthlyPlan(string monthlyPlanMainID, string employeeID)
    {
        var monthlyPlanMainInfo = await _monthlyPlanMaintainRepository.GetMonthlyPlanMain(monthlyPlanMainID);
        monthlyPlanMainInfo.StatusCode = AnnualPlanEnums.PlanStatus.Published;
        monthlyPlanMainInfo.Modify(employeeID);
        return await _unitOfWork.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 获取月度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表ID</param>
    /// <param name="month">月份</param>
    /// <returns></returns>
    public async Task<string> GetMonthlyPlanMainID(string annualPlanMainID, int month)
    {
        return await _monthlyPlanMaintainRepository.GetMonthlyPlanMainID(annualPlanMainID, month);
    }

    /// <summary>
    /// 获取月度计划状态
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主键</param>
    /// <returns></returns>
    public async Task<bool> GetMonthlyPlanStatus(string monthlyPlanMainID)
    {
        var statusCode = await _monthlyPlanMaintainRepository.GetMonthlyPlanStatus(monthlyPlanMainID);
        return statusCode == AnnualPlanEnums.PlanStatus.Published;
    }

    /// <summary>
    /// 查询某科室的月度计划
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表ID</param>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    public async Task<TieredPlanWorksByType[]> GetMonthlyWorks(string annualPlanMainID, string monthlyPlanMainID)
    {
        // 获取当前月度工作
        var monthlyWorks = await _monthlyPlanMaintainRepository.GetMonthlyWorks(monthlyPlanMainID);

        if (monthlyWorks.Length == 0)
        {
            return [];
        }
        var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
        var interventionIDs = monthlyWorks.SelectMany(m => m.Children).Where(m => !m.IsTemp && m.APInterventionID.HasValue).Select(m => m.APInterventionID.Value).ToArray();
        // TODO：此处存在跨域。最佳做法是在MonthlyPlanDetail冗余一个InterventionLocalShowName字段，当年度计划制定修改LocalShowName时，发出事件进行同步
        var interventionIDAndLocalShowName = await _annualPlanInterventionMainRepository.GetInterventionIDAndLocalShowName(interventionIDs, annualPlanMainID);
        foreach (var monthlyWork in monthlyWorks)
        {
            // 补充分类名称
            monthlyWork.TypeName = typeList.Find(m => m.AnnualPlanTypeID == monthlyWork.TypeID)?.AnnualPlanTypeContent;
            foreach (var work in monthlyWork.Children)
            {
                // 补充所引用执行项目的自定义名称
                if (!work.IsTemp && work.APInterventionID.HasValue)
                {
                    interventionIDAndLocalShowName.TryGetValue(work.APInterventionID.Value, out var localShowName);
                    work.APInterventionLocalShowName = localShowName;
                }
                // 若没有自定义分组名称，则拼接人员姓名作前端呈现
                if (string.IsNullOrEmpty(work.PrincipalGroupName) && work.PrincipalIDs.Length > 0)
                {
                    var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(work.PrincipalIDs);
                    work.PrincipalName = string.Join("，", employees.Select(m => m.Value));
                }
            }
        }
        return monthlyWorks;
    }
    #endregion

    #region 参考导入逻辑
    /// <summary>
    /// 获取可导入的工作
    /// </summary>
    /// <param name="quarterPlanMainID">年度计划主表ID</param>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <param name="departmentID">当前部门</param>
    /// <param name="annual">年度</param>
    /// <param name="month">月度</param>
    /// <returns></returns>
    public async Task<List<TieredPlanWorksByPlanThenType>> GetCanImportMpWorksGroupByPlanThenType(string quarterPlanMainID, string monthlyPlanMainID, int departmentID, int annual, int month)
    {
        if (string.IsNullOrEmpty(quarterPlanMainID))
        {
            throw new CustomException("未制定季度计划，无法进行月度计划制定！");
        }
        // 获取当前月份所处季度的、已参考过的分解目标任务字典ID
        var refInterventionIDs = await _monthlyPlanMaintainRepository.GetMonthlyPlanWorkInterventionIDs(monthlyPlanMainID);
        var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
        // 来源一：本部门季度计划，排除掉已参考过的分解目标任务
        var currentDeptQpWorks = await GetCurrentDeptQpWorksByPlanThenType(quarterPlanMainID, departmentID, month, typeList, refInterventionIDs);
        // 来源二：上级部门月度计划
        var upperDeptMpWorksByPlanThenTypeList = await GetUpperDeptMpWorksByPlanThenTypeList(annual, departmentID, null, refInterventionIDs, typeList);

        if (currentDeptQpWorks is null)
        {
            return upperDeptMpWorksByPlanThenTypeList;
        }
        return [.. upperDeptMpWorksByPlanThenTypeList, currentDeptQpWorks];
    }

    /// <summary>
    /// 获取当前部门季度计划可选工作
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <param name="departmentID">部门ID</param>
    /// <param name="month">月度</param>
    /// <param name="typeList">分类字典</param>
    /// <returns></returns>
    private async Task<TieredPlanWorksByPlanThenType> GetCurrentDeptQpWorksByPlanThenType(string quarterPlanMainID, int departmentID, int month, List<AnnualPlanTypeListInfo> typeList, int[] refInterventionIDs)
    {
        var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
        var currentDeptWorksGroup = await _quarterPlanMaintainRepository.GetQuarterPlanWorksForMonthlyPlan(quarterPlanMainID, month, refInterventionIDs);
        currentDeptWorksGroup.PlanName = $"{departmentList.Find(m => m.DepartmentID == departmentID)?.LocalShowName}季度计划";
        currentDeptWorksGroup.DepartmentID = departmentID;
        foreach (var typeGroup in currentDeptWorksGroup.Children)
        {
            typeGroup.TypeName = typeList.Find(m => m.AnnualPlanTypeID == typeGroup.TypeID)?.AnnualPlanTypeContent;
        }
        return currentDeptWorksGroup;
    }

    /// <summary>
    /// 获取上级部门月度计划可选工作
    /// </summary>
    /// <param name="annual">年度</param>
    /// <param name="departmentID">部门</param>
    /// <param name="includeApInterventionID">需包含的执行项目字典ID</param>
    /// <param name="excludeApInterventionIDs">需排除的执行项目字典ID集合</param>
    /// <param name="typeList">分类字典</param>
    /// <returns></returns>
    /// <exception cref="Exception">未找到上级部门月度计划所属的年度计划</exception>
    public async Task<List<TieredPlanWorksByPlanThenType>> GetUpperDeptMpWorksByPlanThenTypeList(int annual, int departmentID, int? includeApInterventionID, int[] excludeApInterventionIDs = null, List<AnnualPlanTypeListInfo> typeList = null)
    {
        typeList ??= await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
        var nursingDepartmentName = (await _departmentListRepository.GetByIDAsync(DEPARTMENT_ID_405))?.LocalShowName;
        var committeeDepartments = await _departmentListRepository.GetLowerDepartments(DEPARTMENT_ID_53);
        var upperDeptIDs = committeeDepartments.Select(m => m.DepartmentID)
            .Append(DEPARTMENT_ID_405)
            // 排除当前部门
            .Where(m => m != departmentID);
        var planMainViews = await _annualPlanMainRepository.GetPlanMainViewsByYearAndDepartmentIDs(annual, upperDeptIDs);
        var planMainIDs = planMainViews.Select(m => m.MainID).ToArray();
        // 若指定了执行项目ID，说明是快捷参考，按业务只包含对应字典的上级计划工作，故查询数据时不再包含临时性工作
        var upperDeptQpWorksByPlanThenTypeList = await _monthlyPlanMaintainRepository.GetWorkViews(planMainIDs, includeApInterventionID, excludeApInterventionIDs, true, !includeApInterventionID.HasValue);
        upperDeptQpWorksByPlanThenTypeList.ForEach(planGroup =>
        {
            if (planMainViews.FirstOrDefault(m => m.MainID == planGroup.PlanMainID)?.DepartmentID is not int currentPlanDeptID)
            {
                throw new Exception($"未找到当前月度计划所属年度计划！planMainID = {planGroup.PlanMainID}");
            }
            planGroup.IsCommittee = currentPlanDeptID != DEPARTMENT_ID_405;
            planGroup.DepartmentID = currentPlanDeptID;
            planGroup.PlanName = currentPlanDeptID == DEPARTMENT_ID_405
            ? $"{nursingDepartmentName}月度计划"
            : committeeDepartments.Find(m => m.DepartmentID == currentPlanDeptID)?.LocalShowName + "月度计划";
            foreach (var typeGroup in planGroup.Children)
            {
                typeGroup.TypeName = typeList.Find(m => m.AnnualPlanTypeID == typeGroup.TypeID)?.AnnualPlanTypeContent;
            }
        });
        return upperDeptQpWorksByPlanThenTypeList;
    }

    /// <summary>
    /// 批量导入保存
    /// </summary>
    /// <param name="view">保存参数</param>
    /// <returns></returns>
    public async Task<string> SaveImportWorks(MpWorksSaveView view)
    {
        // 规则：数据来源是导入弹窗，而导入弹窗中的数据是过滤后的，不会和月度计划现有数据重叠。所以前端传递的始终认为是新数据
        if (view.WorkViews.Length == 0)
        {
            throw new CustomException("没有可导入的工作内容！");
        }
        var mainID = view.IsFirstImport ? "".NewGuid() : view.MonthlyPlanMainID;
        var priorityWorks = view.WorkViews.Select(workView => new MonthlyPlanDetailInfo
        {
            MonthlyPlanDetailID = "".NewGuid(),
            MonthlyPlanMainID = mainID,
            TypeID = workView.TypeID,
            APInterventionID = workView.APInterventionID,
            Sort = workView.Sort,
            WorkType = AnnualPlanEnums.WorkType.Key,
            WorkContent = workView.WorkContent,
            Requirement = workView.Requirement,
            IsTemp = workView.IsTemp,
            PrincipalIDs = [],
            PrincipalGroupName = ""
        }).Select(m => m.Add(view.EmployeeID).Modify(view.EmployeeID) as MonthlyPlanDetailInfo).ToList();
        if (view.IsFirstImport)
        {
            var quarterMainInfo = CreateMainInfo(view, mainID);
            await _unitOfWork.GetRepository<MonthlyPlanMainInfo>().InsertAsync(quarterMainInfo);
        }
        await _unitOfWork.GetRepository<MonthlyPlanDetailInfo>().InsertAsync(priorityWorks);
        await _unitOfWork.SaveChangesAsync();
        return mainID;
    }

    /// <summary>
    /// 创建月度计划主表
    /// </summary>
    /// <param name="view">导入参数</param>
    /// <param name="mainID">主键</param>
    /// <returns></returns>
    private static MonthlyPlanMainInfo CreateMainInfo(MpWorksSaveView view, string mainID)
    {
        var quarterMainInfo = new MonthlyPlanMainInfo
        {
            MonthlyPlanMainID = mainID,
            AnnualPlanMainID = view.AnnualPlanMainID,
            DepartmentID = view.DepartmentID,
            Month = view.Month,
            Year = view.Annual,
            StatusCode = 0,
            DeleteFlag = ""
        };
        quarterMainInfo.Add(view.EmployeeID).Modify(view.EmployeeID);
        return quarterMainInfo;
    }
    #endregion

    /// <summary>
    /// 查询本人及上下级已制定的月度计划
    /// </summary>
    /// <param name="year">年份</param>
    /// <param name="employeeID">工号</param>
    /// <returns></returns>
    public async Task<MonthlyPlanQueryView[]> GetBrowseMPViews(int year, string employeeID)
    {
        var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
        var mainDepartment = employeeToDepartments.FirstOrDefault(m => m.IsMainDepartment ?? false)?.DepartmentID;
        var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
        var departIDs = await GetDepartmentIDs(employeeToDepartments, departmentList);

        var queryViews = await _monthlyPlanMaintainRepository.GetMonthlyPlanQueryViews(year, departIDs);
        foreach (var queryView in queryViews)
        {
            queryView.IsMainDepartment = queryView.DepartmentID == mainDepartment;
            queryView.DepartmentName = departmentList.Find(m => m.DepartmentID == queryView.DepartmentID)?.LocalShowName;
            queryView.PlannerName = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(queryView.Planner, m => m.EmployeeName);
        }
        return [.. queryViews.OrderBy(m => departmentList.Find(n => n.DepartmentID == m.DepartmentID)?.Level)];
    }
    /// <summary>
    /// 获取科室集合
    /// </summary>
    /// <param name="employeeToDepartments">当前用户的权限科室</param>
    /// <param name="departmentList">科室字典</param>
    /// <returns></returns>
    private async Task<IEnumerable<int>> GetDepartmentIDs(EmployeeToDepartmentInfo[] employeeToDepartments, List<DepartmentListInfo> departmentList)
    {
        if (employeeToDepartments.Any(m => (m.IsMainDepartment ?? false) && m.DepartmentID == DEPARTMENT_ID_405))
        {
            return departmentList.Where(m => m.OrganizationType == "1").Select(m => m.DepartmentID);
        }

        var departIDs = new HashSet<int>() { DEPARTMENT_ID_405 };
        foreach (var empToDepart in employeeToDepartments)
        {
            // 获取此部门的连续上下级部门ID集合
            var departments = await _dictionaryService.GetSuperAndSubDepartmentsByID(empToDepart.DepartmentID, false);
            departments.ForEach(m => departIDs.Add(m.DepartmentID));
        }
        return departIDs;
    }
    /// <summary>
    /// 月度计划工作重排序
    /// </summary>
    /// <param name="resetSortView">参数</param>
    /// <param name="employeeID">工号</param>
    /// <returns></returns>
    public async Task<bool> ResetMonthlyPlanWorksSort(ResetMonthlyPlanWorksSortDto resetSortView, string employeeID)
    {
        var works = await _quarterPlanMaintainRepository.GetWorksByQuarterPlanMainIDAndTypeID(resetSortView.MonthlyPlanMainID, resetSortView.TypeID);
        foreach (var work in works)
        {
            var newSort = resetSortView.PlanWorkIDAndSort[work.QuarterPlanMainID];
            if (work.Sort != newSort)
            {
                work.Sort = newSort;
                work.Modify(employeeID);
            }
        }
        return await _unitOfWork.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 获取月度计划预览数据
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划ID</param>
    /// <returns></returns>
    public async Task<MonthlyPlanPreview> GetMonthlyPlanPreview(string monthlyPlanMainID)
    {
        var monthlyPlan = await _monthlyPlanMaintainRepository.GetMonthlyPlanPreview(monthlyPlanMainID);
        foreach (var planType in monthlyPlan.PlanTypes)
        {
            foreach (var planWork in planType.PlanWorks)
            {
                if (string.IsNullOrEmpty(planWork.PrincipalName))
                {
                    var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(planWork.PrincipalIDs);
                    planWork.PrincipalName = string.Join("，", employees.Select(m => m.Value));
                }
            }
        }
        return monthlyPlan;
    }
}
