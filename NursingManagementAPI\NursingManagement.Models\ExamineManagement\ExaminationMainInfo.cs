﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 考核主表
    /// </summary>
    [Table("ExaminationMain")]
    public class ExaminationMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考核主表ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminationMainID { get; set; }

        /// <summary>
        /// 试卷主记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationPaperMainID { get; set; }

        /// <summary>
        /// 考核记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationRecordID { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 员工编号（被考核人）
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }

        /// <summary>
        /// 考试状态（7 待预约、1待考核，2 签到，3 考核中、4 已交卷、5 作弊交卷、6 强制交卷）(配置在SettingDictionary)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string StatusCode { get; set; }

        /// <summary>
        /// 分数
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal? Score { get; set; }

        /// <summary>
        /// 是否是补考
        /// </summary>
        public bool RetakeFlag { get; set; }

        /// <summary>
        /// 作弊次数
        /// </summary>
        public int? CheatedCount { get; set; }

        /// <summary>
        /// 考试开始时间（进入考试时间）
        /// </summary>
        public DateTime? StartDateTime { get; set; }

        /// <summary>
        /// 考试结束时间（交卷时间）
        /// </summary>
        public DateTime? EndDateTime { get; set; }

        /// <summary>
        /// 补考分数
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal? RetakeScore { get; set; }

        /// <summary>
        /// 记录当前刷题进度
        /// </summary>
        public int? CurrentQuestionID { get; set; }

        /// <summary>
        /// 试卷ID（说明来源DynamicFormRecord表，定卷时和ExaminationPaperMain表的paperID保持一致，动态组卷时，人员开始考核时写入）
        /// </summary>
        public string PaperID { get; set; }
    }
}
