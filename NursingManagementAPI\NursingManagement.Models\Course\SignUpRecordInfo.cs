using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 报名表
    /// </summary>
    [Serializable]
    [Table("SignUpRecord")]
    public class SignUpRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string SignUpRecordID { get; set; } = null!;
        /// <summary>
        /// 来源ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string SourceID { get; set; }
        /// <summary>
        /// 来源类别(1：培训清单，2：培训群组，3：考核签到，4、培训签到)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SourceType { get; set; }
        /// <summary>
        /// 状态(0:不同意/未签，1：同意/签到，2：报名取消)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 报名类型(1：自主报名，2：系统选择)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SignUpType { get; set; }
        /// <summary>
        /// 人员工号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string EmployeeID { get; set; }
    }
}

