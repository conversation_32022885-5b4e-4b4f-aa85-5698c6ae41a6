﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface ICapabilityLevelRepository : ICacheRepository
    {
        /// <summary>
        /// 根据主键获取能级配置记录
        /// </summary>
        /// <param name="capabilityLevelID">能级ID</param>
        /// <returns></returns>
        Task<CapabilityLevelInfo> GetRecordByKeysAsync(int capabilityLevelID);

        #region Cache Partition
        /// <summary>
        /// 获取缓存中所有的能级数据
        /// </summary>
        /// <returns></returns>
        Task<List<CapabilityLevelInfo>> GetByCacheAsync();

        #endregion
    }
}
