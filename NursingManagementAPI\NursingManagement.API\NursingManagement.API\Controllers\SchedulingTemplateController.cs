﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 排班模板控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/schedulingTemplate")]
    [EnableCors("any")]
    public class SchedulingTemplateController : Controller
    {
        /// <summary>
        /// 引用
        /// </summary>
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly ISchedulingTemplateService _schedulingTemplateService;
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="session"></param>
        /// <param name="schedulingTemplateService"></param>
        /// <returns></returns>
        public SchedulingTemplateController(
            ISessionService session
            , ISchedulingTemplateService schedulingTemplateService
        )
        {
            _session = session;
            _schedulingTemplateService = schedulingTemplateService;
        }

        /// <summary>
        /// 获取部门所有模板记录
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTemplateRecords")]
        public async Task<IActionResult> GetTemplateRecords(int departmentID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingTemplateService.GetTemplateRecords(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取排班标记集合
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetShiftSchedulingMarks")]
        public async Task<IActionResult> GetShiftSchedulingMarks(int departmentID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingTemplateService.GetShiftSchedulingMarks(departmentID);
            return result.ToJson();
        }
        /// <summary>
        /// 获取排班模板数据
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTemplateData")]
        public async Task<IActionResult> GetTemplateData(string schedulingTemplateRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingTemplateService.GetTemplateData(schedulingTemplateRecordID);
            return result.ToJson();
        }
        /// <summary>
        /// 保存排班模板
        /// </summary>
        /// <param name="schedulingTemplateView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveTemplateRecord")]
        public async Task<IActionResult> SaveTemplateRecord([FromBody] SchedulingTemplateView schedulingTemplateView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            schedulingTemplateView.HospitalID = session.HospitalID;
            schedulingTemplateView.EmployeeID = session.EmployeeID;
            result.Data= await _schedulingTemplateService.SaveTemplateRecord(schedulingTemplateView);
            return result.ToJson();
        }
        /// <summary>
        /// 复制排班模板
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CopyTemplate")]
        public async Task<IActionResult> CopyTemplate(string schedulingTemplateRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingTemplateService.CopyTemplate(schedulingTemplateRecordID, session.EmployeeID);
            return result.ToJson();
        }
        /// <summary>
        /// 删除排班模板
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteTemplate")]
        public async Task<IActionResult> DeleteTemplate(string schedulingTemplateRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingTemplateService.DeleteTemplate(schedulingTemplateRecordID, session.EmployeeID);           
            return result.ToJson();
        }
        /// <summary>
        /// 更新排班模板状态
        /// </summary>
        /// <param name="schedulingTemplateRecordID"></param>
        /// <param name="statusCode"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateTemplateStatus")]
        public async Task<IActionResult> UpdateTemplateStatus(string schedulingTemplateRecordID, string statusCode)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _schedulingTemplateService.UpdateTemplateStatus(schedulingTemplateRecordID, statusCode, session.EmployeeID);
            return result.ToJson();
        }
    }
}
