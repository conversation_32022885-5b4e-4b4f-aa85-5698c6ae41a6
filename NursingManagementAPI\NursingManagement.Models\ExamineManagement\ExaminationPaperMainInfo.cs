﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 考核试卷主表
    /// </summary>
    [Table("ExaminationPaperMain")]
    public class ExaminationPaperMainInfo : MutiModifyInfo
    {
        /// <summary>
        /// 试卷主记录ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminationPaperMainID { get; set; }

        /// <summary>
        /// 试卷名称
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string PaperTitle { get; set; }

        /// <summary>
        /// 试卷难度（配置在SettingDictionary）
        /// </summary>
        public int DifficultyLevel { get; set; }

        /// <summary>
        /// 总分
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal TotalScore { get; set; }

        /// <summary>
        /// 题目数量
        /// </summary>
        [Column(TypeName = "TINYINT")]
        public int QuestionCount { get; set; }

        /// <summary>
        /// 及格分数
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal? PassingScore { get; set; }

        /// <summary>
        /// 试卷表单ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string PaperID { get; set; }

        /// <summary>
        /// 试卷类型，来源于SettingDictionary
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string PaperType { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 组卷条件记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationConditionRecordID { get; set; }

        /// <summary>
        /// 实操类考试对应的题库ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string QuestionBankID { get; set; }

        /// <summary>
        /// 试卷题目组织模式
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string PaperQuestionMode { get; set; } = "1";

        /// <summary>
        /// 固定题目列表
        /// </summary>
        public List<string> FixedQuestionList { get; set; }
    }
}
