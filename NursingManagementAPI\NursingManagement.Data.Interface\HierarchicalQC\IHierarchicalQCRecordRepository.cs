﻿using NursingManagement.Models;
using NursingManagement.ViewModels.HierarchicalQC;

namespace NursingManagement.Data.Interface
{
    public interface IHierarchicalQCRecordRepository
    {
        /// <summary>
        ///根据主题主记录ID判断该主题是否有指派
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        Task<bool> GetExistsReocrdBySubjectID(string subjectID);
        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        Task<HierarchicalQCRecordInfo> GetDataByRecordID(string recordID);
        /// <summary>
        /// 根据主题ID获取质控主记录
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectID(string subjectID);
        /// <summary>
        /// 根据主题ID获取按部门质控主记录
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectIDAndDepart(string subjectID);
        /// <summary>
        /// 根据主题ID集合获取质控主记录
        /// </summary>
        /// <param name="subjectIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectIDs(List<string> subjectIDs);
        /// <summary>
        /// 根据主题ID集合和科室获取质控主记录
        /// </summary>
        /// <param name="subjectIDs"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordInfo>> GetQCReocrdBySubjectIDsAndDepartment(List<string> subjectIDs, int departmentID);
        /// <summary>
        /// 根据主题ID获取质控主记录科室ID集合
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        Task<List<int>> GetQCReocrdDepartmentBySubjectID(string subjectID);
        /// <summary>
        /// 获取质控数据
        /// </summary>
        /// <param name="subjectIDs"></param>
        /// <param name="hierarchicalQCEmployID"></param>
        /// <param name="switchDpartmentIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordInfo>> GetRecordData(List<string> subjectIDs, string hierarchicalQCEmployID, List<int> switchDpartmentIDs);
        /// <summary>
        /// 根据主题ID获取被质控科室的数量
        /// </summary>
        /// <param name="subjectID"></param>
        /// <returns></returns>
        Task<int> GetQCReocrdDepartmentCountBySubjectID(string subjectID);
        /// <summary>
        /// 根据主记录获取对应的质控人
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordInfo>> GetEmployeeInfoByRecordIDsAsNoTrackAsync(List<string> recordIDs);
        /// <summary>
        /// 获取访视质控记录数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<Dictionary<string, object>>> GetRecordDataByRecordIDs(List<string> recordIDs, string hospitalID);
        /// <summary>
        /// 根据时间获取节点式督导考核人
        /// </summary>
        /// <param name="qcLevel"></param>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="formTypes"></param>
        /// <param name="hospitalID"></param>
        /// <param name="switchDeptIDs">当前登陆人拥有的权限科室</param>
        /// <returns></returns>
        Task<List<string>> GetQCReocrdHierarchicalQCEmployIDList(string qcLevel, int year, int month, List<string> formTypes, string hospitalID, List<int> switchDeptIDs);
        /// <summary>
        /// 获取质控数据获取质控主记录数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        Task<List<HierarchicalQCRecordInfo>> GetQCRecordInfos(GetQCRecordView view);
    }
}