﻿namespace NursingManagement.ViewModels.Examine
{
    public class ExaminationConditionEditView
    {
        /// <summary>
        /// 困难程度组件回显数据
        /// </summary>
        public List<FormDetailConditionView> DifficultyLevelConditions { get; set; }
        /// <summary>
        /// 必选题组件回显数据
        /// </summary>
        public List<FormDetailConditionView> RequiredChoiceConditions { get; set; }
        /// <summary>
        /// 分数和题型数量过滤组件回显的数据
        /// </summary>
        public List<object> CountAndScoreConditions { get; set; }
        /// <summary>
        /// 规则名称
        /// </summary>
        public string ConditionName { get; set; }

    }
}
