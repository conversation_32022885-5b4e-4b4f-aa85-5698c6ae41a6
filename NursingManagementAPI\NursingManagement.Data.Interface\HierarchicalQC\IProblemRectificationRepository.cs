﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IProblemRectificationRepository
    {
        /// <summary>
        /// 根据质控问题MainID获取对应最近一条的整改记录
        /// </summary>
        /// <param name="hierarchicalQCMainIDs">质控Main表主键集合</param>
        /// <returns></returns>
        Task<List<ProblemRectificationView>> GetProblemRectificationByHierarchicalQCMainID(List<string> hierarchicalQCMainIDs);
        /// <summary>
        /// 根据质控问题MainID和员工编号获取对应的整改记录
        /// </summary>
        /// <param name="hierarchicalQCMainID">质控Main表主键</param>
        /// <param name="employeeID">员工编号</param>
        /// <returns></returns>
        Task<ProblemRectificationInfo> GetRecordByHierarchicalQCMainIDAndEmployeeID(string hierarchicalQCMainID, string employeeID);
    }
}
