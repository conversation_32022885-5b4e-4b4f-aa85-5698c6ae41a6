﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="NursingManagement\**" />
    <EmbeddedResource Remove="NursingManagement\**" />
    <None Remove="NursingManagement\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.0.2" />
    <PackageReference Include="Hangfire.Core" Version="1.8.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.UnitOfWork" Version="3.1.0" />
    <PackageReference Include="NLog" Version="5.3.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NursingManagement.Data\NursingManagement.Data.csproj" />
    <ProjectReference Include="..\NursingManagement.Data.Interface\NursingManagement.Data.Interface.csproj" />
    <ProjectReference Include="..\NursingManagement.Services.Interface\NursingManagement.Services.Interface.csproj" />
  </ItemGroup>

</Project>
