﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeToJobRepository : IEmployeeToJobRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;
        public EmployeeToJobRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer
        )
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        /// <summary>
        /// 根据职务编号获取工号
        /// </summary>
        /// <param name="jobCode">职务编号</param>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        public async Task<List<string>> GetEmployeeIDByJobCode(string jobCode, int departmentID)
        {
            var data = await this.GetAll<EmployeeToJobInfo>();
            var result = data.Where(m => m.JobCode == jobCode && m.DepartmentID == departmentID).Select(m => m.EmployeeID).ToList();
            return result;
        }
        /// <summary>
        /// 根据科室ID获取职务编号
        /// </summary>
        /// <param name="departmentID">科室</param>
        /// <returns></returns>
        public async Task<HashSet<string>> GetJobCodesByDepartmentID(int departmentID)
        {
            var data = await this.GetAll<EmployeeToJobInfo>();
            var result = data.Where(m => m.DepartmentID == departmentID).Select(m => m.JobCode).ToHashSet();
            return result;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            var (hospitalID, language) = _sessionCommonServer.GetParamsByKey(key);
            var data = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.EmployeeToJobInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return data;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeToJob.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }
        /// <summary>
        /// 获取同一部门相同岗位的人员工号
        /// </summary>
        /// <param name="jobCodes">岗位集合</param>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<HashSet<string>> GetEmployeeIDsByDepartmentIDAndJobCodesAsync(List<string> jobCodes, int departmentID)
        {
            var data = await this.GetAll<EmployeeToJobInfo>();
            var result = data.Where(m => m.DepartmentID == departmentID && jobCodes.Contains(m.JobCode)).Select(m => m.EmployeeID).ToHashSet();
            return result;
        }
    }
}
