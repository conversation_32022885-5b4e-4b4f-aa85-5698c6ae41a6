﻿using System.ComponentModel.DataAnnotations.Schema;
namespace NursingManagement.Models
{
    /// <summary>
    ///  调班申请表   
    /// </summary>
    [Table("AdjustScheduleRecord")]
    public class AdjustScheduleRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 调班记录ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string AdjustScheduleRecordID { get; set; }

        /// <summary>
        /// 部门编码，护理管理组织架构的DepartmentID
        /// </summary>
        public int DepartmentID { get; set; }

        /// <summary>
        /// 医院序号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 申请调班日期
        /// </summary>
        public DateTime AdjustDate { get; set; }

        /// <summary>
        /// 申请调整岗位，根据申请人和申请日期自动提取已经排好的值班岗
        /// </summary>
        public string AdjustDepartmentPost { get; set; }

        /// <summary>
        /// 调班目标人员ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string TargetEmployeeID { get; set; }

        /// <summary>
        /// 调班目标日期
        /// </summary>
        public DateTime TargetDate { get; set; }

        /// <summary>
        /// 目标岗位，根据目标人和目标日期自动提取已经排好的值班岗
        /// </summary>
        public string TargetDepartmentPost { get; set; }

        /// <summary>
        /// 状态 0：申请提交、1：审核中、2：审批通过、3：审批未通过
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// 提交审批记录序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ApproveRecordID { get; set; }
        /// <summary>
        /// 申请原因
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string Reason { get; set; }
        /// <summary>
        /// 调整午别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string AdjustNoonType { get; set; }
        /// <summary>
        /// 换班午别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string TargetNoonType { get; set; }
        /// <summary>
        /// 自动排班开关（true打开）
        /// </summary>
        public bool? AutoScheduleFlag { get; set; }
    }
}