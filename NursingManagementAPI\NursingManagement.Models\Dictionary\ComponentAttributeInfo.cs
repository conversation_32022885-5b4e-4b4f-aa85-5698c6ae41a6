using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 组件属性字典表
    /// </summary>
    [Serializable]
    [Table("ComponentAttribute")]
    public class ComponentAttributeInfo : MutiModifyInfo
    {
        /// <summary>
        /// 组件明细属性ID
        /// </summary>
        public int ComponentAttributeID { get; set; }
        /// <summary>
        /// 项目属性，比如字体，字号、宽度、下拉选项，只读等
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string Attribute { get; set; }
        /// <summary>
        /// 属性值的数据类型，int、string，bool、list、object，json等
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string DataType { get; set; }
        /// <summary>
        /// 属性说明
        /// </summary>
        [Column(TypeName = "nvarchar(200)")]
        public string Description { get; set; }
    }
}
