﻿namespace NursingManagement.ViewModels
{
    public class FormDetailConditionView
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        public string ItemID { get; set; }
        /// <summary>
        /// 条件：
        ///     EQUALS:等于; NOT_EQUALS:不等于; GREATER_THAN:大于; LESS_THAN:小于;
        ///     GREATER_THAN_OR_EQUALS:大于等于; LESS_THAN_OR_EQUALS:小于等于;
        ///     EMPTY:为空; INCLUDES:包含; EXCLUDE:不包含; RANGE:范围
        /// </summary>
        public string Condition { get; set; }
        /// <summary>
        /// 条件值
        /// </summary>
        public string Value { get; set; }
        /// <summary>
        /// 条件类型：and、or
        /// </summary>
        public string ConditionType { get; set; }
        /// <summary>
        /// 子数据
        /// </summary>
        public List<FormDetailConditionView> Children { get; set; }
    }
}
