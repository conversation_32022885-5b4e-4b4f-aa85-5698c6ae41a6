﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.ViewModel
{
    /// <summary>
    /// 患者访视表格组装
    /// </summary>
    public class VisitsRecordSaveView
    {
        /// <summary>
        /// 质控数据
        /// </summary>
        public HierarchicalQCMainAndDetailView QCMainAndDetail {  get; set; }
        /// <summary>
        /// 访视内容模板ID
        /// </summary>
        public int VisitsFormID {  get; set; }
        /// <summary>
        /// 片区ID
        /// </summary>
        public int UpNMDepartmentID { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public int NmDepartmentID { get; set; }
        /// <summary>
        /// CCC患者主记录数据
        /// </summary>
        public PatientProfileRecordInfo VisitsRecord {  get; set; }
        /// <summary>
        /// CCC患者明细记录数据
        /// </summary>
        public List<PatientProfileDetailInfo> VisitsDetails {  get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language {  get; set; }
        /// <summary>
        /// 登陆人
        /// </summary>
        public string EmployID { get; set; }
    }
}
