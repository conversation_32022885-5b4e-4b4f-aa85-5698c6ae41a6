﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IRuleListRepository : ICacheRepository
    {
        /// <summary>
        ///  根据主记录ID集合获取数据
        /// </summary>
        /// <param name="ruleLisIDs"></param>
        /// <returns></returns>
        Task<List<RuleListInfo>> GetListyID(List<int> ruleLisIDs);
        /// <summary>
        /// 根据类别获取数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<List<RuleListInfo>> GetListByType(string type);
        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        Task<List<RuleListInfo>> GetAllCacheAsync();
        /// <summary>
        ///  根据主记录ID获取数据
        /// </summary>
        /// <param name="ruleLisID"></param>
        /// <returns></returns>
        Task<RuleListInfo> GetRuleDataByID(int ruleLisID);
        /// <summary>
        /// 获取最大值（主键）
        /// </summary>
        /// <returns></returns>
        Task<int> GetMaxID();
        /// <summary>
        /// 根据类别列表获取数据
        /// </summary>
        /// <param name="typeList">使用系统TypeList</param>
        /// <returns></returns>
        Task<List<RuleListInfo>> GetListByTypeList(List<string> typeList);
        /// <summary>
        /// 根据类别获取数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<List<RuleListInfo>> GetListByContainsType(string type);
    }
}
